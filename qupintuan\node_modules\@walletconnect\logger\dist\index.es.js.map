{"version": 3, "file": "index.es.js", "sources": ["../src/constants.ts", "../src/linkedList.ts", "../src/baseChunkLogger.ts", "../src/clientChunkLogger.ts", "../src/serverChunkLogger.ts", "../src/utils.ts"], "sourcesContent": ["export const PINO_LOGGER_DEFAULTS = {\n  level: \"info\",\n};\n\nexport const PINO_CUSTOM_CONTEXT_KEY = \"custom_context\";\n\nexport const MAX_LOG_SIZE_IN_BYTES_DEFAULT = 1000 * 1024; // 1MB\n", "class LogListNode {\n  private nodeValue: string;\n  private sizeInBytes: number;\n  public next: LogListNode | null;\n\n  constructor(value: string) {\n    this.nodeValue = value;\n    this.sizeInBytes = new TextEncoder().encode(this.nodeValue).length;\n    this.next = null;\n  }\n\n  public get value() {\n    return this.nodeValue;\n  }\n\n  public get size() {\n    return this.sizeInBytes;\n  }\n}\n\nexport default class LogLinkedList {\n  private lengthInNodes: number;\n  private sizeInBytes: number;\n  private head: LogListNode | null;\n  private tail: LogListNode | null;\n  private maxSizeInBytes: number;\n\n  constructor(maxSizeInBytes: number) {\n    this.head = null;\n    this.tail = null;\n    this.lengthInNodes = 0;\n    this.maxSizeInBytes = maxSizeInBytes;\n    this.sizeInBytes = 0;\n  }\n\n  public append(value: string): void {\n    const newNode = new LogListNode(value);\n\n    if (newNode.size > this.maxSizeInBytes) {\n      throw new Error(\n        `[LinkedList] Value too big to insert into list: ${value} with size ${newNode.size}`,\n      );\n    }\n\n    while (this.size + newNode.size > this.maxSizeInBytes) {\n      this.shift();\n    }\n\n    if (!this.head) {\n      this.head = newNode;\n      this.tail = newNode;\n    } else {\n      if (this.tail) {\n        this.tail.next = newNode;\n      }\n      this.tail = newNode;\n    }\n    this.lengthInNodes++;\n    this.sizeInBytes += newNode.size;\n  }\n\n  public shift(): void {\n    if (!this.head) {\n      return;\n    }\n\n    const removedNode = this.head;\n    this.head = this.head.next;\n\n    if (!this.head) {\n      this.tail = null;\n    }\n\n    this.lengthInNodes--;\n    this.sizeInBytes -= removedNode.size;\n  }\n\n  public toArray(): string[] {\n    const array: string[] = [];\n    let currentNode = this.head;\n    while (currentNode !== null) {\n      array.push(currentNode.value);\n      currentNode = currentNode.next;\n    }\n    return array;\n  }\n\n  public get length() {\n    return this.lengthInNodes;\n  }\n\n  public get size() {\n    return this.sizeInBytes;\n  }\n\n  public toOrderedArray() {\n    return Array.from(this);\n  }\n\n  [Symbol.iterator](): Iterator<string> {\n    let node = this.head;\n\n    const next = (): IteratorResult<string> => {\n      if (!node) {\n        return { done: true, value: null };\n      }\n\n      const value = node.value;\n      node = node.next;\n\n      return { done: false, value };\n    };\n\n    return { next };\n  }\n}\n", "import { MAX_LOG_SIZE_IN_BYTES_DEFAULT } from \"./constants\";\nimport type { LoggerOptions } from \"pino\";\nimport { levels } from \"pino\";\nimport LinkedList from \"./linkedList\";\nimport { safeJsonStringify } from \"@walletconnect/safe-json\";\n\nexport default class BaseChunkLogger {\n  private logs: LinkedList;\n  private level: LoggerOptions[\"level\"];\n  private levelValue: number;\n\n  private MAX_LOG_SIZE_IN_BYTES: number;\n\n  public constructor(\n    level: LoggerOptions[\"level\"],\n    MAX_LOG_SIZE_IN_BYTES: number = MAX_LOG_SIZE_IN_BYTES_DEFAULT,\n  ) {\n    this.level = level ?? \"error\";\n    this.levelValue = levels.values[this.level];\n\n    this.MAX_LOG_SIZE_IN_BYTES = MAX_LOG_SIZE_IN_BYTES;\n    this.logs = new LinkedList(this.MAX_LOG_SIZE_IN_BYTES);\n  }\n\n  public forwardToConsole(chunk: any, level: number) {\n    if (level === levels.values.error) {\n      // eslint-disable-next-line no-console\n      console.error(chunk);\n    } else if (level === levels.values.warn) {\n      // eslint-disable-next-line no-console\n      console.warn(chunk);\n    } else if (level === levels.values.debug) {\n      // eslint-disable-next-line no-console\n      console.debug(chunk);\n    } else if (level === levels.values.trace) {\n      // eslint-disable-next-line no-console\n      console.trace(chunk);\n    } else {\n      // eslint-disable-next-line no-console\n      console.log(chunk);\n    }\n  }\n\n  public appendToLogs(chunk: any) {\n    this.logs.append(\n      safeJsonStringify({\n        timestamp: new Date().toISOString(),\n        log: chunk,\n      }),\n    );\n\n    // Based on https://github.com/pinojs/pino/blob/master/lib/constants.js\n    const level = typeof chunk === \"string\" ? JSON.parse(chunk).level : chunk.level;\n    if (level >= this.levelValue) {\n      this.forwardToConsole(chunk, level);\n    }\n  }\n\n  public getLogs() {\n    return this.logs;\n  }\n\n  public clearLogs() {\n    this.logs = new LinkedList(this.MAX_LOG_SIZE_IN_BYTES);\n  }\n\n  public getLogArray() {\n    return Array.from(this.logs);\n  }\n\n  public logsToBlob(extraMetadata: Record<string, string>) {\n    const logArray = this.getLogArray();\n    logArray.push(safeJsonStringify({ extraMetadata }));\n    const blob = new Blob(logArray, { type: \"application/json\" });\n    return blob;\n  }\n}\n", "import { MAX_LOG_SIZE_IN_BYTES_DEFAULT } from \"./constants\";\nimport type { LoggerOptions } from \"pino\";\nimport BaseChunkLogger from \"./baseChunkLogger\";\n\nexport default class ClientChunkLogger {\n  private baseChunkLogger: BaseChunkLogger;\n\n  public constructor(\n    level: LoggerOptions[\"level\"],\n    MAX_LOG_SIZE_IN_BYTES: number = MAX_LOG_SIZE_IN_BYTES_DEFAULT,\n  ) {\n    this.baseChunkLogger = new BaseChunkLogger(level, MAX_LOG_SIZE_IN_BYTES);\n  }\n\n  public write(chunk: any): void {\n    this.baseChunkLogger.appendToLogs(chunk);\n  }\n\n  public getLogs() {\n    return this.baseChunkLogger.getLogs();\n  }\n\n  public clearLogs() {\n    this.baseChunkLogger.clearLogs();\n  }\n\n  public getLogArray() {\n    return this.baseChunkLogger.getLogArray();\n  }\n\n  public logsToBlob(extraMetadata: Record<string, string>) {\n    return this.baseChunkLogger.logsToBlob(extraMetadata);\n  }\n\n  public downloadLogsBlobInBrowser(extraMetadata: Record<string, string>) {\n    const url = URL.createObjectURL(this.logsToBlob(extraMetadata));\n    const anchor = document.createElement(\"a\");\n    anchor.href = url;\n    anchor.download = `walletconnect-logs-${new Date().toISOString()}.txt`;\n    document.body.appendChild(anchor);\n    anchor.click();\n    document.body.removeChild(anchor);\n    URL.revokeObjectURL(url);\n  }\n}\n", "import { MAX_LOG_SIZE_IN_BYTES_DEFAULT } from \"./constants\";\nimport type { DestinationStream, LoggerOptions } from \"pino\";\nimport BaseChunkLogger from \"./baseChunkLogger\";\n\nexport default class ServerChunkLogger implements DestinationStream {\n  private baseChunkLogger: BaseChunkLogger;\n\n  public constructor(\n    level: LoggerOptions[\"level\"],\n    MAX_LOG_SIZE_IN_BYTES: number = MAX_LOG_SIZE_IN_BYTES_DEFAULT,\n  ) {\n    this.baseChunkLogger = new BaseChunkLogger(level, MAX_LOG_SIZE_IN_BYTES);\n  }\n\n  public write(chunk: any): void {\n    this.baseChunkLogger.appendToLogs(chunk);\n  }\n\n  public getLogs() {\n    return this.baseChunkLogger.getLogs();\n  }\n\n  public clearLogs() {\n    this.baseChunkLogger.clearLogs();\n  }\n\n  public getLogArray() {\n    return this.baseChunkLogger.getLogArray();\n  }\n\n  public logsToBlob(extraMetadata: Record<string, string>) {\n    return this.baseChunkLogger.logsToBlob(extraMetadata);\n  }\n}\n", "import pino, { Logger, LoggerOptions } from \"pino\";\nimport { PINO_CUSTOM_CONTEXT_KEY, PINO_LOGGER_DEFAULTS } from \"./constants\";\nimport ClientChunkLogger from \"./clientChunkLogger\";\nimport ServerChunkLogger from \"./serverChunkLogger\";\nimport BaseChunkLogger from \"./baseChunkLogger\";\n\nexport interface ChunkLoggerController {\n  logsToBlob: BaseChunkLogger[\"logsToBlob\"];\n  getLogArray: () => string[];\n  clearLogs: () => void;\n  downloadLogsBlobInBrowser?: ClientChunkLogger[\"downloadLogsBlobInBrowser\"];\n}\n\nexport function getDefaultLoggerOptions(opts?: LoggerOptions): LoggerOptions {\n  return {\n    ...opts,\n    level: opts?.level || PINO_LOGGER_DEFAULTS.level,\n  };\n}\n\nexport function getBrowserLoggerContext(\n  logger: Logger,\n  customContextKey: string = PINO_CUSTOM_CONTEXT_KEY,\n): string {\n  return (logger as any)[customContextKey] || \"\";\n}\n\nexport function setBrowserLoggerContext(\n  logger: Logger,\n  context: string,\n  customContextKey: string = PINO_CUSTOM_CONTEXT_KEY,\n): Logger {\n  (logger as any)[customContextKey] = context;\n  return logger;\n}\n\nexport function getLoggerContext(\n  logger: Logger,\n  customContextKey: string = PINO_CUSTOM_CONTEXT_KEY,\n): string {\n  let context = \"\";\n  // logger.bindings is undefined in browser\n  if (typeof logger.bindings === \"undefined\") {\n    context = getBrowserLoggerContext(logger, customContextKey);\n  } else {\n    context = logger.bindings().context || \"\";\n  }\n  return context;\n}\n\nexport function formatChildLoggerContext(\n  logger: Logger,\n  childContext: string,\n  customContextKey: string = PINO_CUSTOM_CONTEXT_KEY,\n): string {\n  const parentContext = getLoggerContext(logger, customContextKey);\n  const context = parentContext.trim() ? `${parentContext}/${childContext}` : childContext;\n  return context;\n}\n\nexport function generateChildLogger(\n  logger: Logger,\n  childContext: string,\n  customContextKey: string = PINO_CUSTOM_CONTEXT_KEY,\n): Logger {\n  const context = formatChildLoggerContext(logger, childContext, customContextKey);\n  const child = logger.child({ context });\n  return setBrowserLoggerContext(child, context, customContextKey);\n}\n\nexport function generateClientLogger(params: { opts?: LoggerOptions; maxSizeInBytes?: number }): {\n  logger: Logger<any>;\n  chunkLoggerController: ClientChunkLogger;\n} {\n  const clientLogger = new ClientChunkLogger(params.opts?.level, params.maxSizeInBytes);\n  const logger = pino({\n    ...params.opts,\n    level: \"trace\",\n    browser: {\n      ...params.opts?.browser,\n      write: (obj) => clientLogger.write(obj),\n    },\n  });\n\n  return { logger, chunkLoggerController: clientLogger };\n}\n\nexport function generateServerLogger(params: { maxSizeInBytes?: number; opts?: LoggerOptions }): {\n  logger: Logger<any>;\n  chunkLoggerController: ServerChunkLogger;\n} {\n  const serverLogger = new ServerChunkLogger(params.opts?.level, params.maxSizeInBytes);\n  const logger = pino(\n    {\n      ...params.opts,\n      level: \"trace\",\n    },\n    serverLogger,\n  );\n\n  return { logger, chunkLoggerController: serverLogger };\n}\n\nexport function generatePlatformLogger(params: {\n  maxSizeInBytes?: number;\n  opts?: LoggerOptions;\n  loggerOverride?: string | Logger<any>;\n}): {\n  logger: Logger<any>;\n  chunkLoggerController: ChunkLoggerController | null;\n} {\n  if (typeof params.loggerOverride !== \"undefined\" && typeof params.loggerOverride !== \"string\") {\n    return {\n      logger: params.loggerOverride,\n      chunkLoggerController: null,\n    };\n  }\n\n  if (typeof window !== \"undefined\") {\n    return generateClientLogger(params);\n  } else {\n    return generateServerLogger(params);\n  }\n}\n"], "names": ["LogListNode", "value", "LogLinkedList", "maxSizeInBytes", "newNode", "removedNode", "array", "currentNode", "node", "BaseChunkLogger", "level", "MAX_LOG_SIZE_IN_BYTES", "MAX_LOG_SIZE_IN_BYTES_DEFAULT", "levels", "LinkedList", "chunk", "safeJsonStringify", "extraMetadata", "logArray", "ClientChunkLogger", "url", "anchor", "ServerChunkLogger", "opts", "__spreadProps", "__spreadValues", "PINO_LOGGER_DEFAULTS", "logger", "customContextKey", "PINO_CUSTOM_CONTEXT_KEY", "context", "childContext", "parentContext", "child", "params", "_a", "_b", "clientLogger", "pino", "obj", "serverLogger"], "mappings": ";;;;AAAa,MAAA,oBAAA,CAAuB,CAClC,KAAO,CAAA,MACT,EAEa,uBAA0B,CAAA,gBAAA,CAE1B,8BAAgC,GAAO,CAAA;;ACNpD,MAAMA,GAAY,CAKhB,WAAA,CAAYC,CAAe,CAAA,CACzB,KAAK,SAAYA,CAAAA,CAAAA,CACjB,IAAK,CAAA,WAAA,CAAc,IAAI,WAAA,GAAc,MAAO,CAAA,IAAA,CAAK,SAAS,CAAA,CAAE,MAC5D,CAAA,IAAA,CAAK,IAAO,CAAA,KACd,CAEA,IAAW,KAAQ,EAAA,CACjB,OAAO,IAAA,CAAK,SACd,CAEA,IAAW,IAAO,EAAA,CAChB,OAAO,IAAA,CAAK,WACd,CACF,CAEqBC,MAAAA,CAAc,CAOjC,WAAA,CAAYC,EAAwB,CAClC,IAAA,CAAK,IAAO,CAAA,IAAA,CACZ,IAAK,CAAA,IAAA,CAAO,IACZ,CAAA,IAAA,CAAK,aAAgB,CAAA,CAAA,CACrB,IAAK,CAAA,cAAA,CAAiBA,CACtB,CAAA,IAAA,CAAK,YAAc,EACrB,CAEO,MAAOF,CAAAA,CAAAA,CAAqB,CACjC,MAAMG,EAAU,IAAIJ,GAAAA,CAAYC,CAAK,CAAA,CAErC,GAAIG,CAAAA,CAAQ,KAAO,IAAK,CAAA,cAAA,CACtB,MAAM,IAAI,KACR,CAAA,CAAA,gDAAA,EAAmDH,CAAmBG,CAAAA,WAAAA,EAAAA,CAAAA,CAAQ,IAChF,CAAA,CAAA,CAAA,CAGF,KAAO,IAAA,CAAK,IAAOA,CAAAA,CAAAA,CAAQ,KAAO,IAAK,CAAA,cAAA,EACrC,IAAK,CAAA,KAAA,EAGF,CAAA,IAAA,CAAK,MAIJ,IAAK,CAAA,IAAA,GACP,IAAK,CAAA,IAAA,CAAK,IAAOA,CAAAA,CAAAA,CAAAA,CAEnB,KAAK,IAAOA,CAAAA,CAAAA,GANZ,IAAK,CAAA,IAAA,CAAOA,CACZ,CAAA,IAAA,CAAK,IAAOA,CAAAA,CAAAA,CAAAA,CAOd,IAAK,CAAA,aAAA,EAAA,CACL,IAAK,CAAA,WAAA,EAAeA,CAAQ,CAAA,KAC9B,CAEO,KAAc,EAAA,CACnB,GAAI,CAAC,IAAK,CAAA,IAAA,CACR,OAGF,MAAMC,CAAAA,CAAc,IAAK,CAAA,IAAA,CACzB,IAAK,CAAA,IAAA,CAAO,KAAK,IAAK,CAAA,IAAA,CAEjB,IAAK,CAAA,IAAA,GACR,IAAK,CAAA,IAAA,CAAO,IAGd,CAAA,CAAA,IAAA,CAAK,aACL,EAAA,CAAA,IAAA,CAAK,WAAeA,EAAAA,CAAAA,CAAY,KAClC,CAEO,SAAoB,CACzB,MAAMC,CAAkB,CAAA,EACxB,CAAA,IAAIC,EAAc,IAAK,CAAA,IAAA,CACvB,KAAOA,CAAAA,GAAgB,IACrBD,EAAAA,CAAAA,CAAM,KAAKC,CAAY,CAAA,KAAK,CAC5BA,CAAAA,CAAAA,CAAcA,CAAY,CAAA,IAAA,CAE5B,OAAOD,CACT,CAEA,IAAW,MAAS,EAAA,CAClB,OAAO,IAAA,CAAK,aACd,CAEA,IAAW,IAAO,EAAA,CAChB,OAAO,IAAA,CAAK,WACd,CAEO,cAAA,EAAiB,CACtB,OAAO,KAAM,CAAA,IAAA,CAAK,IAAI,CACxB,CAEA,CAAC,MAAA,CAAO,QAAQ,CAAA,EAAsB,CACpC,IAAIE,CAAO,CAAA,IAAA,CAAK,IAahB,CAAA,OAAO,CAAE,IAAA,CAXI,IAA8B,CACzC,GAAI,CAACA,CAAAA,CACH,OAAO,CAAE,IAAM,CAAA,CAAA,CAAA,CAAM,KAAO,CAAA,IAAK,CAGnC,CAAA,MAAMP,CAAQO,CAAAA,CAAAA,CAAK,MACnB,OAAAA,CAAAA,CAAOA,CAAK,CAAA,IAAA,CAEL,CAAE,IAAA,CAAM,CAAO,CAAA,CAAA,KAAA,CAAAP,CAAM,CAC9B,CAEc,CAChB,CACF;;MC7GqBQ,GAAgB,CAO5B,YACLC,CACAC,CAAAA,CAAAA,CAAgCC,6BAChC,CAAA,CACA,KAAK,KAAQF,CAAAA,CAAAA,EAAA,KAAAA,CAAS,CAAA,OAAA,CACtB,KAAK,UAAaG,CAAAA,MAAAA,CAAO,OAAO,IAAK,CAAA,KAAK,EAE1C,IAAK,CAAA,qBAAA,CAAwBF,EAC7B,IAAK,CAAA,IAAA,CAAO,IAAIG,CAAW,CAAA,IAAA,CAAK,qBAAqB,EACvD,CAEO,gBAAiBC,CAAAA,CAAAA,CAAYL,EAAe,CAC7CA,CAAAA,GAAUG,OAAO,MAAO,CAAA,KAAA,CAE1B,QAAQ,KAAME,CAAAA,CAAK,EACVL,CAAUG,GAAAA,MAAAA,CAAO,OAAO,IAEjC,CAAA,OAAA,CAAQ,KAAKE,CAAK,CAAA,CACTL,CAAUG,GAAAA,MAAAA,CAAO,OAAO,KAEjC,CAAA,OAAA,CAAQ,MAAME,CAAK,CAAA,CACVL,IAAUG,MAAO,CAAA,MAAA,CAAO,MAEjC,OAAQ,CAAA,KAAA,CAAME,CAAK,CAGnB,CAAA,OAAA,CAAQ,IAAIA,CAAK,EAErB,CAEO,YAAaA,CAAAA,CAAAA,CAAY,CAC9B,IAAA,CAAK,KAAK,MACRC,CAAAA,iBAAAA,CAAkB,CAChB,SAAW,CAAA,IAAI,MAAO,CAAA,WAAA,EACtB,CAAA,GAAA,CAAKD,CACP,CAAC,CACH,EAGA,MAAML,CAAAA,CAAQ,OAAOK,CAAU,EAAA,QAAA,CAAW,IAAK,CAAA,KAAA,CAAMA,CAAK,CAAE,CAAA,KAAA,CAAQA,EAAM,KACtEL,CAAAA,CAAAA,EAAS,KAAK,UAChB,EAAA,IAAA,CAAK,iBAAiBK,CAAOL,CAAAA,CAAK,EAEtC,CAEO,OAAA,EAAU,CACf,OAAO,IAAA,CAAK,IACd,CAEO,SAAA,EAAY,CACjB,IAAA,CAAK,KAAO,IAAII,CAAAA,CAAW,KAAK,qBAAqB,EACvD,CAEO,WAAc,EAAA,CACnB,OAAO,KAAM,CAAA,IAAA,CAAK,KAAK,IAAI,CAC7B,CAEO,UAAWG,CAAAA,CAAAA,CAAuC,CACvD,MAAMC,CAAAA,CAAW,IAAK,CAAA,WAAA,GACtB,OAAAA,CAAAA,CAAS,KAAKF,iBAAkB,CAAA,CAAE,cAAAC,CAAc,CAAC,CAAC,CACrC,CAAA,IAAI,KAAKC,CAAU,CAAA,CAAE,KAAM,kBAAmB,CAAC,CAE9D,CACF;;ACxEqBC,MAAAA,CAAkB,CAG9B,WAAA,CACLT,CACAC,CAAAA,CAAAA,CAAgCC,8BAChC,CACA,IAAA,CAAK,eAAkB,CAAA,IAAIH,GAAgBC,CAAAA,CAAAA,CAAOC,CAAqB,EACzE,CAEO,KAAMI,CAAAA,CAAAA,CAAkB,CAC7B,IAAA,CAAK,gBAAgB,YAAaA,CAAAA,CAAK,EACzC,CAEO,OAAU,EAAA,CACf,OAAO,IAAK,CAAA,eAAA,CAAgB,OAAQ,EACtC,CAEO,SAAA,EAAY,CACjB,IAAK,CAAA,eAAA,CAAgB,SAAU,GACjC,CAEO,WAAA,EAAc,CACnB,OAAO,IAAA,CAAK,eAAgB,CAAA,WAAA,EAC9B,CAEO,WAAWE,CAAuC,CAAA,CACvD,OAAO,IAAA,CAAK,eAAgB,CAAA,UAAA,CAAWA,CAAa,CACtD,CAEO,yBAA0BA,CAAAA,CAAAA,CAAuC,CACtE,MAAMG,EAAM,GAAI,CAAA,eAAA,CAAgB,IAAK,CAAA,UAAA,CAAWH,CAAa,CAAC,EACxDI,CAAS,CAAA,QAAA,CAAS,aAAc,CAAA,GAAG,CACzCA,CAAAA,CAAAA,CAAO,KAAOD,CACdC,CAAAA,CAAAA,CAAO,QAAW,CAAA,CAAA,mBAAA,EAAsB,IAAI,IAAA,GAAO,WAAY,EAAA,CAAA,IAAA,CAAA,CAC/D,QAAS,CAAA,IAAA,CAAK,WAAYA,CAAAA,CAAM,EAChCA,CAAO,CAAA,KAAA,EACP,CAAA,QAAA,CAAS,IAAK,CAAA,WAAA,CAAYA,CAAM,CAAA,CAChC,GAAI,CAAA,eAAA,CAAgBD,CAAG,EACzB,CACF;;ACxCqBE,MAAAA,GAA+C,CAG3D,WAAA,CACLZ,CACAC,CAAAA,CAAAA,CAAgCC,6BAChC,CAAA,CACA,IAAK,CAAA,eAAA,CAAkB,IAAIH,GAAAA,CAAgBC,CAAOC,CAAAA,CAAqB,EACzE,CAEO,KAAMI,CAAAA,CAAAA,CAAkB,CAC7B,IAAA,CAAK,eAAgB,CAAA,YAAA,CAAaA,CAAK,EACzC,CAEO,OAAU,EAAA,CACf,OAAO,IAAA,CAAK,eAAgB,CAAA,OAAA,EAC9B,CAEO,SAAY,EAAA,CACjB,IAAK,CAAA,eAAA,CAAgB,SAAU,GACjC,CAEO,WAAc,EAAA,CACnB,OAAO,IAAA,CAAK,eAAgB,CAAA,WAAA,EAC9B,CAEO,UAAWE,CAAAA,CAAAA,CAAuC,CACvD,OAAO,IAAK,CAAA,eAAA,CAAgB,UAAWA,CAAAA,CAAa,CACtD,CACF;;ACjCA,IAAA,CAAA,CAAA,MAAA,CAAA,cAAA,CAAA,CAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,yBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAagB,SAAA,uBAAA,CAAwBM,CAAqC,CAAA,CAC3E,OAAOC,CAAAA,CAAAC,CAAA,CAAA,EAAA,CACFF,CADE,CAAA,CAAA,CAEL,KAAOA,CAAAA,CAAAA,CAAAA,EAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,KAASG,GAAAA,oBAAAA,CAAqB,KAC7C,CAAA,CACF,CAEO,SAAS,uBACdC,CAAAA,CAAAA,CACAC,CAA2BC,CAAAA,uBAAAA,CACnB,CACR,OAAQF,CAAeC,CAAAA,CAAgB,CAAK,EAAA,EAC9C,CAEO,SAAS,uBACdD,CAAAA,CAAAA,CACAG,CACAF,CAAAA,CAAAA,CAA2BC,uBACnB,CAAA,CACR,OAACF,CAAAA,CAAeC,CAAgB,CAAA,CAAIE,CAC7BH,CAAAA,CACT,CAEO,SAAS,iBACdA,CACAC,CAAAA,CAAAA,CAA2BC,uBACnB,CAAA,CACR,IAAIC,CAAAA,CAAU,EAEd,CAAA,OAAI,OAAOH,CAAAA,CAAO,QAAa,EAAA,WAAA,CAC7BG,CAAU,CAAA,uBAAA,CAAwBH,CAAQC,CAAAA,CAAgB,CAE1DE,CAAAA,CAAAA,CAAUH,CAAO,CAAA,QAAA,EAAW,CAAA,OAAA,EAAW,EAElCG,CAAAA,CACT,CAEO,SAAS,wBACdH,CAAAA,CAAAA,CACAI,CACAH,CAAAA,CAAAA,CAA2BC,uBACnB,CAAA,CACR,MAAMG,CAAgB,CAAA,gBAAA,CAAiBL,CAAQC,CAAAA,CAAgB,CAE/D,CAAA,OADgBI,CAAc,CAAA,IAAA,EAAS,CAAA,CAAA,EAAGA,CAAiBD,CAAAA,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAiBA,CAE9E,CAEgB,SAAA,mBAAA,CACdJ,CACAI,CAAAA,CAAAA,CACAH,CAA2BC,CAAAA,uBAAAA,CACnB,CACR,MAAMC,CAAU,CAAA,wBAAA,CAAyBH,CAAQI,CAAAA,CAAAA,CAAcH,CAAgB,CAAA,CACzEK,CAAQN,CAAAA,CAAAA,CAAO,KAAM,CAAA,CAAE,QAAAG,CAAQ,CAAC,CACtC,CAAA,OAAO,uBAAwBG,CAAAA,CAAAA,CAAOH,CAASF,CAAAA,CAAgB,CACjE,CAEgB,SAAA,oBAAA,CAAqBM,CAGnC,CAAA,CAzEF,IAAAC,CAAAA,CAAAC,GA0EE,CAAA,MAAMC,CAAe,CAAA,IAAIlB,CAAkBgB,CAAAA,CAAAA,CAAAA,CAAAD,CAAO,CAAA,IAAA,GAAP,IAAAC,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAa,KAAOD,CAAAA,CAAAA,CAAO,cAAc,CAAA,CAUpF,OAAO,CAAE,OATMI,CAAKd,CAAAA,CAAAA,CAAAC,CAAA,CAAA,EAAA,CACfS,CAAO,CAAA,IAAA,CAAA,CADQ,CAElB,KAAA,CAAO,OACP,CAAA,OAAA,CAASV,CAAAC,CAAAA,CAAAA,CAAA,EACJW,CAAAA,CAAAA,GAAAA,CAAAF,CAAO,CAAA,IAAA,GAAP,IAAAE,CAAAA,KAAAA,CAAAA,CAAAA,GAAAA,CAAa,OADT,CAAA,CAAA,CAEP,KAAQG,CAAAA,CAAAA,EAAQF,CAAa,CAAA,KAAA,CAAME,CAAG,CACxC,CACF,CAAA,CAAA,CAAC,CAEgB,CAAA,qBAAA,CAAuBF,CAAa,CACvD,CAEgB,SAAA,oBAAA,CAAqBH,CAGnC,CAAA,CA1FF,IAAAC,CAAAA,CA2FE,MAAMK,CAAAA,CAAe,IAAIlB,GAAAA,CAAAA,CAAkBa,CAAAD,CAAAA,CAAAA,CAAO,IAAP,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAC,CAAa,CAAA,KAAA,CAAOD,CAAO,CAAA,cAAc,CASpF,CAAA,OAAO,CAAE,MAAA,CARMI,CACbd,CAAAA,CAAAA,CAAAC,CAAA,CAAA,EAAA,CACKS,CAAO,CAAA,IAAA,CAAA,CADZ,CAEE,KAAA,CAAO,OACT,CAAA,CAAA,CACAM,CACF,CAEiB,CAAA,qBAAA,CAAuBA,CAAa,CACvD,CAEO,SAAS,sBAAuBN,CAAAA,CAAAA,CAOrC,CACA,OAAI,OAAOA,CAAAA,CAAO,cAAmB,EAAA,WAAA,EAAe,OAAOA,CAAAA,CAAO,cAAmB,EAAA,QAAA,CAC5E,CACL,MAAA,CAAQA,CAAO,CAAA,cAAA,CACf,qBAAuB,CAAA,IACzB,CAGE,CAAA,OAAO,MAAW,EAAA,WAAA,CACb,oBAAqBA,CAAAA,CAAM,CAE3B,CAAA,oBAAA,CAAqBA,CAAM,CAEtC;;;;"}