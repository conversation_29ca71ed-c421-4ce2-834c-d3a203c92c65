"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.solidityPanicSelector = exports.solidityPanic = exports.solidityErrorSelector = exports.solidityError = exports.panicReasons = void 0;
exports.decode = decode;
exports.encode = encode;
exports.format = format;
exports.from = from;
exports.fromAbi = fromAbi;
exports.getSelector = getSelector;
const abitype = require("abitype");
const AbiItem = require("./AbiItem.js");
const AbiParameters = require("./AbiParameters.js");
const Hex = require("./Hex.js");
function decode(abiError, data, options = {}) {
    if (Hex.size(data) < 4)
        throw new AbiItem.InvalidSelectorSizeError({ data });
    if (abiError.inputs.length === 0)
        return undefined;
    const values = AbiParameters.decode(abiError.inputs, Hex.slice(data, 4), options);
    if (values && Object.keys(values).length === 1) {
        if (Array.isArray(values))
            return values[0];
        return Object.values(values)[0];
    }
    return values;
}
function encode(abiError, ...args) {
    const selector = getSelector(abiError);
    const data = args.length > 0
        ? AbiParameters.encode(abiError.inputs, args[0])
        : undefined;
    return data ? Hex.concat(selector, data) : selector;
}
function format(abiError) {
    return abitype.formatAbiItem(abiError);
}
function from(abiError, options = {}) {
    return AbiItem.from(abiError, options);
}
function fromAbi(abi, name, options) {
    if (name === 'Error')
        return exports.solidityError;
    if (name === 'Panic')
        return exports.solidityPanic;
    if (Hex.validate(name, { strict: false })) {
        const selector = Hex.slice(name, 0, 4);
        if (selector === exports.solidityErrorSelector)
            return exports.solidityError;
        if (selector === exports.solidityPanicSelector)
            return exports.solidityPanic;
    }
    const item = AbiItem.fromAbi(abi, name, options);
    if (item.type !== 'error')
        throw new AbiItem.NotFoundError({ name, type: 'error' });
    return item;
}
function getSelector(abiItem) {
    return AbiItem.getSelector(abiItem);
}
exports.panicReasons = {
    1: 'An `assert` condition failed.',
    17: 'Arithmetic operation resulted in underflow or overflow.',
    18: 'Division or modulo by zero (e.g. `5 / 0` or `23 % 0`).',
    33: 'Attempted to convert to an invalid type.',
    34: 'Attempted to access a storage byte array that is incorrectly encoded.',
    49: 'Performed `.pop()` on an empty array',
    50: 'Array index is out of bounds.',
    65: 'Allocated too much memory or created an array which is too large.',
    81: 'Attempted to call a zero-initialized variable of internal function type.',
};
exports.solidityError = from({
    inputs: [
        {
            name: 'message',
            type: 'string',
        },
    ],
    name: 'Error',
    type: 'error',
});
exports.solidityErrorSelector = '0x08c379a0';
exports.solidityPanic = from({
    inputs: [
        {
            name: 'reason',
            type: 'uint8',
        },
    ],
    name: 'Panic',
    type: 'error',
});
exports.solidityPanicSelector = '0x4e487b71';
//# sourceMappingURL=AbiError.js.map