const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-C8294aMB-*************-wb8rvug16.js","assets/web3-uglCpOhK-*************-7ns772q62.js","assets/vendor-CgHzTxSQ-*************-7ns772q62.js","assets/ui-CbWGv3YI-*************-7ns772q62.js","assets/index-QZjJZq-p-*************-mniiclbh2.css","assets/enhancedGroupBuyService-z2wicMML-*************-7ns772q62.js","assets/claimRewardStable-DT5KxDeg-*************-7ns772q62.js"])))=>i.map(i=>d[i]);
import{_ as a}from"./web3-uglCpOhK-*************-7ns772q62.js";async function P({chainId:o,roomId:s,signer:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);if(!e)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await a(async()=>{const{getContractAddress:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.n);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3,4])),{ABIS:_}=await a(async()=>{const{ABIS:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.o);return{ABIS:t}},__vite__mapDeps([0,1,2,3,4])),m=n(o,"GroupBuyRoom"),w=_.GroupBuyRoom,u=await e.writeContract({address:m,abi:w,functionName:"claimCreatorCommission",args:[BigInt(s)]}),{createPublicClient:l,http:h}=await a(async()=>{const{createPublicClient:t,http:r}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(E=>E.aL);return{createPublicClient:t,http:r}},__vite__mapDeps([1,2,3])),{bscTestnet:c}=await a(async()=>{const{bscTestnet:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4]));return{receipt:await l({chain:c,transport:h()}).waitForTransactionReceipt({hash:u,timeout:6e4}),txHash:u}}catch(n){throw console.error("领取创建者佣金失败:",n),n}}async function T({chainId:o,roomId:s,signer:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);if(!e)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await a(async()=>{const{getContractAddress:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.n);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3,4])),{ABIS:_}=await a(async()=>{const{ABIS:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.o);return{ABIS:t}},__vite__mapDeps([0,1,2,3,4])),m=n(o,"GroupBuyRoom"),w=_.GroupBuyRoom,u=await e.writeContract({address:m,abi:w,functionName:"claimParticipantRefund",args:[BigInt(s)]}),{createPublicClient:l,http:h}=await a(async()=>{const{createPublicClient:t,http:r}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(E=>E.aL);return{createPublicClient:t,http:r}},__vite__mapDeps([1,2,3])),{bscTestnet:c}=await a(async()=>{const{bscTestnet:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4]));return{receipt:await l({chain:c,transport:h()}).waitForTransactionReceipt({hash:u,timeout:6e4}),txHash:u}}catch(n){throw console.error("领取参与者退款失败:",n),n}}async function I({chainId:o,signer:s,roomId:e,tierAmount:n}){if(n){const{claimWithUSDTValidation:w}=await a(async()=>{const{claimWithUSDTValidation:l}=await import("./enhancedGroupBuyService-z2wicMML-*************-7ns772q62.js");return{claimWithUSDTValidation:l}},__vite__mapDeps([5,1,2,3,0,4]));return await w(e,n,s.account.address,s)}const{claimRewardStable:_}=await a(async()=>{const{claimRewardStable:w}=await import("./claimRewardStable-DT5KxDeg-*************-7ns772q62.js");return{claimRewardStable:w}},__vite__mapDeps([6,1,2,3,0,4]));return await _({chainId:o,roomId:e,signer:s})}async function f({chainId:o,roomId:s}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);const{getContractAddress:e}=await a(async()=>{const{getContractAddress:c}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(i=>i.n);return{getContractAddress:c}},__vite__mapDeps([0,1,2,3,4])),{ABIS:n}=await a(async()=>{const{ABIS:c}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(i=>i.o);return{ABIS:c}},__vite__mapDeps([0,1,2,3,4])),_=e(o,"GroupBuyRoom"),m=n.GroupBuyRoom,{createPublicClient:w,http:u}=await a(async()=>{const{createPublicClient:c,http:i}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(d=>d.aL);return{createPublicClient:c,http:i}},__vite__mapDeps([1,2,3])),{bscTestnet:l}=await a(async()=>{const{bscTestnet:c}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(i=>i.m);return{bscTestnet:c}},__vite__mapDeps([0,1,2,3,4])),h=w({chain:l,transport:u()});try{const i=(await h.readContract({address:_,abi:m,functionName:"getRoomRewardInfo",args:[BigInt(s)]}))[0];return Number(i)/1e6}catch{try{const d=(await h.readContract({address:_,abi:m,functionName:"rooms",args:[BigInt(s)]}))[7];return Number(d)/1e6}catch(i){return console.warn("无法查询房间佣金信息:",i),0}}}catch(e){return console.error("获取创建者佣金金额失败:",e),0}}async function B({chainId:o,roomId:s,userAddress:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);const{getContractAddress:n}=await a(async()=>{const{getContractAddress:p}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(A=>A.n);return{getContractAddress:p}},__vite__mapDeps([0,1,2,3,4])),{ABIS:_}=await a(async()=>{const{ABIS:p}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(A=>A.o);return{ABIS:p}},__vite__mapDeps([0,1,2,3,4])),m=n(o,"GroupBuyRoom"),w=n(o,"QPTBuyback"),u=_.GroupBuyRoom,l=_.QPTBuyback,{createPublicClient:h,http:c}=await a(async()=>{const{createPublicClient:p,http:A}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(b=>b.aL);return{createPublicClient:p,http:A}},__vite__mapDeps([1,2,3])),{bscTestnet:i}=await a(async()=>{const{bscTestnet:p}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(A=>A.m);return{bscTestnet:p}},__vite__mapDeps([0,1,2,3,4])),d=h({chain:i,transport:c()}),t=await d.readContract({address:m,abi:u,functionName:"rooms",args:[BigInt(s)]}),r=t[1],E=Number(r)/1e6;let y=0;try{const p=await d.readContract({address:m,abi:u,functionName:"getRoomRewardInfo",args:[BigInt(s)]});y=Number(p[1])/1e6}catch{const A=t[6];y=Number(A)/1e6}if(e&&y>0)try{const p=await d.readContract({address:w,abi:l,functionName:"getRefundStatus",args:[BigInt(s),e]}),[A,b,R]=p;(Number(b)===1||Number(b)===3)&&(y=0)}catch(p){console.warn("查询退款状态失败，保持原有补贴金额:",p)}return{refundAmount:E,subsidyAmount:y}}catch(n){return console.error("获取参与者退款金额失败:",n),{refundAmount:0,subsidyAmount:0}}}async function D({chainId:o,roomId:s,signer:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);if(!e)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await a(async()=>{const{getContractAddress:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.n);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3,4])),{ABIS:_}=await a(async()=>{const{ABIS:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.o);return{ABIS:t}},__vite__mapDeps([0,1,2,3,4])),m=n(o,"GroupBuyRoom"),w=_.GroupBuyRoom,u=await e.writeContract({address:m,abi:w,functionName:"claimWinnerQPT",args:[BigInt(s)]}),{createPublicClient:l,http:h}=await a(async()=>{const{createPublicClient:t,http:r}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(E=>E.aL);return{createPublicClient:t,http:r}},__vite__mapDeps([1,2,3])),{bscTestnet:c}=await a(async()=>{const{bscTestnet:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4])),d=await l({chain:c,transport:h()}).waitForTransactionReceipt({hash:u,timeout:6e4});if(d.status==="reverted")throw new Error("交易被回滚，QPT奖励领取失败");return{receipt:d,txHash:u}}catch(n){throw console.error("领取QPT奖励失败:",n),n}}async function g({chainId:o,roomId:s,signer:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);if(!e)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await a(async()=>{const{getContractAddress:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.n);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3,4])),{ABIS:_}=await a(async()=>{const{ABIS:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.o);return{ABIS:t}},__vite__mapDeps([0,1,2,3,4])),m=n(o,"GroupBuyRoom"),w=_.GroupBuyRoom,u=await e.writeContract({address:m,abi:w,functionName:"claimWinnerPoints",args:[BigInt(s)]}),{createPublicClient:l,http:h}=await a(async()=>{const{createPublicClient:t,http:r}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(E=>E.aL);return{createPublicClient:t,http:r}},__vite__mapDeps([1,2,3])),{bscTestnet:c}=await a(async()=>{const{bscTestnet:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(r=>r.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4])),d=await l({chain:c,transport:h()}).waitForTransactionReceipt({hash:u,timeout:6e4});if(d.status==="reverted")throw new Error("交易被回滚，积分奖励领取失败");return{receipt:d,txHash:u}}catch(n){throw console.error("领取积分奖励失败:",n),n}}export{D as a,g as b,P as c,T as d,I as e,B as f,f as g};
