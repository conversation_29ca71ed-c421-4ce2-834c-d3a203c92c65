{"version": 3, "file": "types.esm.js", "sources": ["../src/index.ts"], "sourcesContent": ["export interface AbstractConnectorArguments {\n  supportedChainIds?: number[]\n}\n\nexport interface ConnectorUpdate<T = number | string> {\n  provider?: any\n  chainId?: T\n  account?: null | string\n}\n\nexport enum ConnectorEvent {\n  Update = 'Web3ReactUpdate',\n  Error = 'Web3ReactError',\n  Deactivate = 'Web3ReactDeactivate'\n}\n"], "names": ["ConnectorEvent"], "mappings": "IAUYA,cAAZ;;AAAA,WAAYA;EACVA,wBAAA,oBAAA;EACAA,uBAAA,mBAAA;EACAA,4BAAA,wBAAA;CAHF,EAAYA,cAAc,KAAdA,cAAc,KAAA,CAA1B;;;;"}