// vite.config.js
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    include: [
      "wagmi",
      "viem/chains",
      "@wagmi/core",
      "use-sync-external-store",
      "ethers",
      "react-hot-toast",
      "@tanstack/react-query",
    ],
    exclude: ["@openzeppelin/contracts"], // 排除不需要预构建的包
    esbuildOptions: {
      target: "es2020",
      define: {
        global: "globalThis", // 更标准的全局定义
      },
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
    extensions: [".jsx", ".js", ".ts", ".tsx", ".json"],
    dedupe: ["react", "react-dom", "ethers"], // 避免重复打包
  },
  define: {
    global: "window",
  },
  server: {
    open: "http://localhost:5174/", // 指定完整URL提升兼容性
    port: 5174, // 更改端口避免冲突
    host: true, // 允许外部访问
    cors: true, // 启用CORS
    strictPort: false, // 如果端口被占用，自动尝试下一个端口
    hmr: {
      overlay: false, // 禁用错误覆盖层，提升开发体验
    },
  },
  build: {
    target: "es2020",
    rollupOptions: {
      output: {
        // 强制生成新的文件名
        entryFileNames: `assets/[name]-[hash]-${Date.now()}.js`,
        chunkFileNames: `assets/[name]-[hash]-${Date.now()}.js`,
        assetFileNames: `assets/[name]-[hash]-${Date.now()}.[ext]`,
        manualChunks: {
          // 代码分割优化
          vendor: ["react", "react-dom"],
          web3: ["wagmi", "viem", "ethers"],
          ui: ["react-hot-toast", "@tanstack/react-query"],
        },
      },
    },
    commonjsOptions: {
      transformMixedEsModules: true, // 兼容CJS/ESM混合模块
    },
    chunkSizeWarningLimit: 1000, // 提高chunk大小警告阈值
  },
});
