{"version": 3, "sources": ["../src/createScaffoldMiddleware.ts"], "names": [], "mappings": ";AAkBO,SAAS,yBAAyB,UAEE;AACzC,SAAO,CAAC,KAAK,KAAK,MAAM,QAAQ;AAC9B,UAAM,UAAU,SAAS,IAAI,MAAM;AAEnC,QAAI,YAAY,QAAW;AACzB,aAAO,KAAK;AAAA,IACd;AAGA,QAAI,OAAO,YAAY,YAAY;AACjC,aAAO,QAAQ,KAAK,KAAK,MAAM,GAAG;AAAA,IACpC;AAEA,IAAC,IAA6B,SAAS;AACvC,WAAO,IAAI;AAAA,EACb;AACF", "sourcesContent": ["import type { <PERSON><PERSON>, <PERSON>sonRpcPara<PERSON>, <PERSON>sonRpcSuc<PERSON> } from '@metamask/utils';\n\nimport type { JsonRpcMiddleware } from './JsonRpcEngine';\n\ntype ScaffoldMiddlewareHandler<\n  Params extends JsonRpcParams,\n  Result extends J<PERSON>,\n> = JsonRpcMiddleware<Params, Result> | Json;\n\n/**\n * Creates a middleware function from an object of RPC method handler functions,\n * keyed to particular method names. If a method corresponding to a key of this\n * object is requested, this middleware will pass it to the corresponding\n * handler and return the result.\n *\n * @param handlers - The RPC method handler functions.\n * @returns The scaffold middleware function.\n */\nexport function createScaffoldMiddleware(handlers: {\n  [methodName: string]: ScaffoldMiddlewareHandler<JsonRpcParams, Json>;\n}): JsonRpcMiddleware<JsonRpcParams, Json> {\n  return (req, res, next, end) => {\n    const handler = handlers[req.method];\n    // if no handler, return\n    if (handler === undefined) {\n      return next();\n    }\n\n    // if handler is fn, call as middleware\n    if (typeof handler === 'function') {\n      return handler(req, res, next, end);\n    }\n    // if handler is some other value, use as result\n    (res as JsonRpcSuccess<Json>).result = handler;\n    return end();\n  };\n}\n"]}