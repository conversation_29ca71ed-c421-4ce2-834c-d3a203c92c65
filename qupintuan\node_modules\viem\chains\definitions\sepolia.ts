import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const sepolia = /*#__PURE__*/ define<PERSON>hain({
  id: 11_155_111,
  name: '<PERSON><PERSON>',
  nativeCurrency: { name: '<PERSON><PERSON> Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://sepolia.drpc.org'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Etherscan',
      url: 'https://sepolia.etherscan.io',
      apiUrl: 'https://api-sepolia.etherscan.io/api',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 751532,
    },
    ensRegistry: { address: '******************************************' },
    ensUniversalResolver: {
      address: '******************************************',
      blockCreated: 5_317_080,
    },
  },
  testnet: true,
})
