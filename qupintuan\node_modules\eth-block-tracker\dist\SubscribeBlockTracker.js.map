{"version": 3, "file": "SubscribeBlockTracker.js", "sourceRoot": "", "sources": ["../src/SubscribeBlockTracker.ts"], "names": [], "mappings": ";;;;;;AAAA,4EAAmD;AAGnD,yDAAsD;AAEtD,MAAM,cAAc,GAAG,IAAA,4BAAiB,GAAE,CAAC;AAa3C,MAAa,qBAAsB,SAAQ,mCAAgB;IAKzD,YAAY,OAAqC,EAAE;QACjD,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACnE;QAED,+BAA+B;QAC/B,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,SAAS;QACT,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,OAAO,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;IACrC,CAAC;IAES,KAAK,CAAC,MAAM;QACpB,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;YACvE,IAAI;gBACF,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAW,CAAC;gBACpE,IAAI,CAAC,eAAe,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CACtC,eAAe,EACf,UAAU,CACX,CAAW,CAAC;gBACb,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;aACvC;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;aACvB;SACF;IACH,CAAC;IAES,KAAK,CAAC,IAAI;QAClB,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;YACvE,IAAI;gBACF,MAAM,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC1D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;aAC7B;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;aACvB;SACF;IACH,CAAC;IAEO,KAAK,CAAC,MAAc,EAAE,GAAG,MAAiB;QAChD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,SAAS,CAAC,SAAS,CACtB;gBACE,EAAE,EAAE,cAAc,EAAE;gBACpB,MAAM;gBACN,MAAM;gBACN,OAAO,EAAE,KAAK;aACf,EACD,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACX,IAAI,GAAG,EAAE;oBACP,MAAM,CAAC,GAAG,CAAC,CAAC;iBACb;qBAAM;oBACL,OAAO,CAAE,GAA+B,CAAC,MAAM,CAAC,CAAC;iBAClD;YACH,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CACpB,CAAU,EACV,QAA6D;;QAE7D,IACE,QAAQ,CAAC,MAAM,KAAK,kBAAkB;YACtC,CAAA,MAAA,QAAQ,CAAC,MAAM,0CAAE,YAAY,MAAK,IAAI,CAAC,eAAe,EACtD;YACA,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACzD;IACH,CAAC;CACF;AAhFD,sDAgFC", "sourcesContent": ["import getCreateRandomId from 'json-rpc-random-id';\nimport { JsonRpcNotification, JsonRpcSuccess } from 'json-rpc-engine';\nimport type { SafeEventEmitterProvider } from '@metamask/eth-json-rpc-provider';\nimport { BaseBlockTracker } from './BaseBlockTracker';\n\nconst createRandomId = getCreateRandomId();\n\nexport interface SubscribeBlockTrackerOptions {\n  provider?: SafeEventEmitterProvider;\n  blockResetDuration?: number;\n  usePastBlocks?: boolean;\n}\n\ninterface SubscriptionNotificationParams {\n  subscription: string;\n  result: { number: string };\n}\n\nexport class SubscribeBlockTracker extends BaseBlockTracker {\n  private _provider: SafeEventEmitterProvider;\n\n  private _subscriptionId: string | null;\n\n  constructor(opts: SubscribeBlockTrackerOptions = {}) {\n    // parse + validate args\n    if (!opts.provider) {\n      throw new Error('SubscribeBlockTracker - no provider specified.');\n    }\n\n    // BaseBlockTracker constructor\n    super(opts);\n    // config\n    this._provider = opts.provider;\n    this._subscriptionId = null;\n  }\n\n  async checkForLatestBlock(): Promise<string> {\n    return await this.getLatestBlock();\n  }\n\n  protected async _start(): Promise<void> {\n    if (this._subscriptionId === undefined || this._subscriptionId === null) {\n      try {\n        const blockNumber = (await this._call('eth_blockNumber')) as string;\n        this._subscriptionId = (await this._call(\n          'eth_subscribe',\n          'newHeads',\n        )) as string;\n        this._provider.on('data', this._handleSubData.bind(this));\n        this._newPotentialLatest(blockNumber);\n      } catch (e) {\n        this.emit('error', e);\n      }\n    }\n  }\n\n  protected async _end() {\n    if (this._subscriptionId !== null && this._subscriptionId !== undefined) {\n      try {\n        await this._call('eth_unsubscribe', this._subscriptionId);\n        this._subscriptionId = null;\n      } catch (e) {\n        this.emit('error', e);\n      }\n    }\n  }\n\n  private _call(method: string, ...params: unknown[]): Promise<unknown> {\n    return new Promise((resolve, reject) => {\n      this._provider.sendAsync(\n        {\n          id: createRandomId(),\n          method,\n          params,\n          jsonrpc: '2.0',\n        },\n        (err, res) => {\n          if (err) {\n            reject(err);\n          } else {\n            resolve((res as JsonRpcSuccess<unknown>).result);\n          }\n        },\n      );\n    });\n  }\n\n  private _handleSubData(\n    _: unknown,\n    response: JsonRpcNotification<SubscriptionNotificationParams>,\n  ): void {\n    if (\n      response.method === 'eth_subscription' &&\n      response.params?.subscription === this._subscriptionId\n    ) {\n      this._newPotentialLatest(response.params.result.number);\n    }\n  }\n}\n"]}