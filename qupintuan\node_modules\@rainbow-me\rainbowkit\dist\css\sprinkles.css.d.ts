import { type RequiredConditionalValue } from '@vanilla-extract/sprinkles';
import './reset.css';
declare const themeContractValues: {
    colors: {
        accentColor: string;
        accentColorForeground: string;
        actionButtonBorder: string;
        actionButtonBorderMobile: string;
        actionButtonSecondaryBackground: string;
        closeButton: string;
        closeButtonBackground: string;
        connectButtonBackground: string;
        connectButtonBackgroundError: string;
        connectButtonInnerBackground: string;
        connectButtonText: string;
        connectButtonTextError: string;
        connectionIndicator: string;
        downloadBottomCardBackground: string;
        downloadTopCardBackground: string;
        error: string;
        generalBorder: string;
        generalBorderDim: string;
        menuItemBackground: string;
        modalBackdrop: string;
        modalBackground: string;
        modalBorder: string;
        modalText: string;
        modalTextDim: string;
        modalTextSecondary: string;
        profileAction: string;
        profileActionHover: string;
        profileForeground: string;
        selectedOptionBorder: string;
        standby: string;
    };
    fonts: {
        body: string;
    };
    radii: {
        actionButton: string;
        connectButton: string;
        menuButton: string;
        modal: string;
        modalMobile: string;
    };
    shadows: {
        connectButton: string;
        dialog: string;
        profileDetailsAction: string;
        selectedOption: string;
        selectedWallet: string;
        walletLogo: string;
    };
    blurs: {
        modalOverlay: string;
    };
};
export type ThemeVars = typeof themeContractValues;
export declare const themeVars: {
    colors: {
        accentColor: `var(--${string})`;
        accentColorForeground: `var(--${string})`;
        actionButtonBorder: `var(--${string})`;
        actionButtonBorderMobile: `var(--${string})`;
        actionButtonSecondaryBackground: `var(--${string})`;
        closeButton: `var(--${string})`;
        closeButtonBackground: `var(--${string})`;
        connectButtonBackground: `var(--${string})`;
        connectButtonBackgroundError: `var(--${string})`;
        connectButtonInnerBackground: `var(--${string})`;
        connectButtonText: `var(--${string})`;
        connectButtonTextError: `var(--${string})`;
        connectionIndicator: `var(--${string})`;
        downloadBottomCardBackground: `var(--${string})`;
        downloadTopCardBackground: `var(--${string})`;
        error: `var(--${string})`;
        generalBorder: `var(--${string})`;
        generalBorderDim: `var(--${string})`;
        menuItemBackground: `var(--${string})`;
        modalBackdrop: `var(--${string})`;
        modalBackground: `var(--${string})`;
        modalBorder: `var(--${string})`;
        modalText: `var(--${string})`;
        modalTextDim: `var(--${string})`;
        modalTextSecondary: `var(--${string})`;
        profileAction: `var(--${string})`;
        profileActionHover: `var(--${string})`;
        profileForeground: `var(--${string})`;
        selectedOptionBorder: `var(--${string})`;
        standby: `var(--${string})`;
    };
    fonts: {
        body: `var(--${string})`;
    };
    radii: {
        actionButton: `var(--${string})`;
        connectButton: `var(--${string})`;
        menuButton: `var(--${string})`;
        modal: `var(--${string})`;
        modalMobile: `var(--${string})`;
    };
    shadows: {
        connectButton: `var(--${string})`;
        dialog: `var(--${string})`;
        profileDetailsAction: `var(--${string})`;
        selectedOption: `var(--${string})`;
        selectedWallet: `var(--${string})`;
        walletLogo: `var(--${string})`;
    };
    blurs: {
        modalOverlay: `var(--${string})`;
    };
};
export declare const largeScreenMinWidth = 768;
declare const responsiveProperties: {
    conditions: {
        defaultCondition: "smallScreen";
        conditionNames: ("smallScreen" | "largeScreen")[];
    };
    styles: {
        alignItems: {
            values: {
                center: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                "flex-end": {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                "flex-start": {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
            };
        };
        display: {
            values: {
                none: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                flex: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                block: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                inline: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
            };
        };
    };
};
export type ResponsiveValue<Value extends string | number | boolean> = RequiredConditionalValue<typeof responsiveProperties, Value>;
export declare const mapResponsiveValue: <OutputValue extends string | number | boolean | null | undefined, Value extends import("@vanilla-extract/sprinkles").ConditionalValue<{
    conditions: {
        defaultCondition: "smallScreen";
        conditionNames: ("smallScreen" | "largeScreen")[];
    };
    styles: {
        alignItems: {
            values: {
                center: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                "flex-end": {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                "flex-start": {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
            };
        };
        display: {
            values: {
                none: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                flex: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                block: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                inline: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
            };
        };
    };
}, string | number | boolean>>(value: Value, fn: (inputValue: Value extends import("@vanilla-extract/sprinkles").ResponsiveArray<1, string | number | boolean | null> | import("@vanilla-extract/sprinkles").ResponsiveArray<1 | 2, string | number | boolean | null> | import("@vanilla-extract/sprinkles").ResponsiveArray<1 | 3 | 2, string | number | boolean | null> | import("@vanilla-extract/sprinkles").ResponsiveArray<1 | 3 | 2 | 4, string | number | boolean | null> | import("@vanilla-extract/sprinkles").ResponsiveArray<1 | 3 | 2 | 5 | 4, string | number | boolean | null> | import("@vanilla-extract/sprinkles").ResponsiveArray<1 | 3 | 2 | 5 | 4 | 6, string | number | boolean | null> | import("@vanilla-extract/sprinkles").ResponsiveArray<1 | 3 | 2 | 5 | 4 | 6 | 7, string | number | boolean | null> | import("@vanilla-extract/sprinkles").ResponsiveArray<1 | 3 | 2 | 5 | 4 | 6 | 7 | 8, string | number | boolean | null> ? NonNullable<Value[number]> : Value extends Partial<Record<string, string | number | boolean>> ? NonNullable<Value[keyof Value]> : Value, key: "smallScreen" | "largeScreen") => OutputValue) => Value extends string | number | boolean ? OutputValue : Partial<Record<"smallScreen" | "largeScreen", OutputValue>>;
export declare const normalizeResponsiveValue: <Value extends string | number | boolean>(value: import("@vanilla-extract/sprinkles").ConditionalValue<{
    conditions: {
        defaultCondition: "smallScreen";
        conditionNames: ("smallScreen" | "largeScreen")[];
    };
    styles: {
        alignItems: {
            values: {
                center: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                "flex-end": {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                "flex-start": {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
            };
        };
        display: {
            values: {
                none: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                flex: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                block: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
                inline: {
                    defaultClass: string;
                    conditions: {
                        smallScreen: string;
                        largeScreen: string;
                    };
                };
            };
        };
    };
}, Value>) => Partial<Record<"smallScreen" | "largeScreen", Value>>;
export declare const sprinkles: ((props: {
    background?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | {
        base?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
        hover?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
        active?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
    } | undefined;
    borderColor?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | {
        base?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
        hover?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
        active?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
    } | undefined;
    boxShadow?: "connectButton" | "dialog" | "profileDetailsAction" | "selectedOption" | "selectedWallet" | "walletLogo" | {
        base?: "connectButton" | "dialog" | "profileDetailsAction" | "selectedOption" | "selectedWallet" | "walletLogo" | undefined;
        hover?: "connectButton" | "dialog" | "profileDetailsAction" | "selectedOption" | "selectedWallet" | "walletLogo" | undefined;
        active?: "connectButton" | "dialog" | "profileDetailsAction" | "selectedOption" | "selectedWallet" | "walletLogo" | undefined;
    } | undefined;
    color?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | {
        base?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
        hover?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
        active?: "accentColor" | "accentColorForeground" | "actionButtonBorder" | "actionButtonBorderMobile" | "actionButtonSecondaryBackground" | "closeButton" | "closeButtonBackground" | "connectButtonBackground" | "connectButtonBackgroundError" | "connectButtonInnerBackground" | "connectButtonText" | "connectButtonTextError" | "connectionIndicator" | "downloadBottomCardBackground" | "downloadTopCardBackground" | "error" | "generalBorder" | "generalBorderDim" | "menuItemBackground" | "modalBackdrop" | "modalBackground" | "modalBorder" | "modalText" | "modalTextDim" | "modalTextSecondary" | "profileAction" | "profileActionHover" | "profileForeground" | "selectedOptionBorder" | "standby" | undefined;
    } | undefined;
} & {
    alignItems?: "center" | "flex-end" | "flex-start" | {
        smallScreen?: "center" | "flex-end" | "flex-start" | undefined;
        largeScreen?: "center" | "flex-end" | "flex-start" | undefined;
    } | undefined;
    display?: "none" | "flex" | "block" | "inline" | {
        smallScreen?: "none" | "flex" | "block" | "inline" | undefined;
        largeScreen?: "none" | "flex" | "block" | "inline" | undefined;
    } | undefined;
} & {
    readonly alignSelf?: "center" | "flex-end" | "flex-start" | undefined;
    readonly backgroundSize?: "cover" | undefined;
    readonly borderRadius?: "1" | "actionButton" | "connectButton" | "menuButton" | "modal" | "modalMobile" | "6" | "10" | "13" | "25%" | "full" | undefined;
    readonly borderStyle?: "solid" | undefined;
    readonly borderWidth?: "0" | "1" | "2" | "4" | undefined;
    readonly cursor?: "none" | "pointer" | undefined;
    readonly pointerEvents?: "none" | "all" | undefined;
    readonly minHeight?: "8" | "44" | undefined;
    readonly flexDirection?: "column" | "row" | undefined;
    readonly fontFamily?: "body" | undefined;
    readonly fontSize?: "13" | "12" | "14" | "16" | "18" | "20" | "23" | undefined;
    readonly fontWeight?: "medium" | "bold" | "regular" | "semibold" | "heavy" | undefined;
    readonly gap?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly height?: "1" | "2" | "full" | "4" | "8" | "44" | "12" | "20" | "24" | "28" | "30" | "32" | "34" | "36" | "40" | "48" | "54" | "60" | "200" | "max" | undefined;
    readonly justifyContent?: "center" | "space-around" | "space-between" | "flex-end" | "flex-start" | undefined;
    readonly textAlign?: "inherit" | "left" | "center" | undefined;
    readonly marginBottom?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly marginLeft?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly marginRight?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly marginTop?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly maxWidth?: "1" | "2" | "full" | "4" | "8" | "44" | "12" | "20" | "24" | "28" | "30" | "32" | "34" | "36" | "40" | "48" | "54" | "60" | "200" | "max" | undefined;
    readonly minWidth?: "1" | "2" | "full" | "4" | "8" | "44" | "12" | "20" | "24" | "28" | "30" | "32" | "34" | "36" | "40" | "48" | "54" | "60" | "200" | "max" | undefined;
    readonly overflow?: "hidden" | undefined;
    readonly paddingBottom?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly paddingLeft?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly paddingRight?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly paddingTop?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    readonly position?: "fixed" | "absolute" | "relative" | undefined;
    readonly WebkitUserSelect?: "none" | undefined;
    readonly right?: "0" | undefined;
    readonly transition?: "transform" | "default" | undefined;
    readonly userSelect?: "none" | undefined;
    readonly width?: "1" | "2" | "full" | "4" | "8" | "44" | "12" | "20" | "24" | "28" | "30" | "32" | "34" | "36" | "40" | "48" | "54" | "60" | "200" | "max" | undefined;
    readonly backdropFilter?: "modalOverlay" | undefined;
    margin?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    marginX?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    marginY?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    padding?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    paddingX?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
    paddingY?: "0" | "1" | "2" | "3" | "6" | "10" | "4" | "8" | "44" | "12" | "14" | "16" | "18" | "20" | "24" | "28" | "32" | "36" | "-1" | "5" | "64" | undefined;
}) => string) & {
    properties: Set<"alignItems" | "alignSelf" | "backdropFilter" | "backgroundSize" | "boxShadow" | "color" | "cursor" | "display" | "flexDirection" | "fontFamily" | "fontSize" | "fontWeight" | "height" | "justifyContent" | "marginBottom" | "marginLeft" | "marginRight" | "marginTop" | "maxWidth" | "minHeight" | "minWidth" | "paddingBottom" | "paddingLeft" | "paddingRight" | "paddingTop" | "pointerEvents" | "position" | "right" | "textAlign" | "userSelect" | "width" | "background" | "borderColor" | "borderRadius" | "borderStyle" | "borderWidth" | "gap" | "margin" | "overflow" | "padding" | "transition" | "WebkitUserSelect" | "marginX" | "marginY" | "paddingX" | "paddingY">;
};
export type Sprinkles = Parameters<typeof sprinkles>[0];
export {};
