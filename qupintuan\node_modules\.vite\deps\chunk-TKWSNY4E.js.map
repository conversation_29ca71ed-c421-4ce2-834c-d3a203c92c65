{"version": 3, "sources": ["../../@wagmi/core/src/connectors/createConnector.ts", "../../@wagmi/core/src/connectors/injected.ts", "../../@wagmi/core/src/connectors/mock.ts", "../../mipd/src/utils.ts", "../../mipd/src/store.ts", "../../zustand/esm/middleware.mjs", "../../zustand/esm/vanilla.mjs", "../../@wagmi/core/src/createEmitter.ts", "../../@wagmi/core/src/utils/deserialize.ts", "../../@wagmi/core/src/utils/serialize.ts", "../../@wagmi/core/src/createStorage.ts", "../../@wagmi/core/src/utils/uid.ts", "../../@wagmi/core/src/createConfig.ts", "../../@wagmi/core/src/hydrate.ts", "../../@wagmi/core/src/transports/connector.ts", "../../@wagmi/core/src/transports/fallback.ts", "../../@wagmi/core/src/utils/cookie.ts", "../../@wagmi/core/src/utils/extractRpcUrls.ts", "../../@wagmi/core/src/utils/normalizeChainId.ts"], "sourcesContent": ["import type {\n  AddEthereumChainParameter,\n  Address,\n  Chain,\n  Client,\n  ProviderConnectInfo,\n  ProviderMessage,\n} from 'viem'\n\nimport type { Transport } from '../createConfig.js'\nimport type { Emitter } from '../createEmitter.js'\nimport type { Storage } from '../createStorage.js'\nimport type { Compute, ExactPartial, StrictOmit } from '../types/utils.js'\n\nexport type ConnectorEventMap = {\n  change: {\n    accounts?: readonly Address[] | undefined\n    chainId?: number | undefined\n  }\n  connect: { accounts: readonly Address[]; chainId: number }\n  disconnect: never\n  error: { error: Error }\n  message: { type: string; data?: unknown | undefined }\n}\n\nexport type CreateConnectorFn<\n  provider = unknown,\n  properties extends Record<string, unknown> = Record<string, unknown>,\n  storageItem extends Record<string, unknown> = Record<string, unknown>,\n> = (config: {\n  chains: readonly [Chain, ...Chain[]]\n  emitter: Emitter<ConnectorEventMap>\n  storage?: Compute<Storage<storageItem>> | null | undefined\n  transports?: Record<number, Transport> | undefined\n}) => Compute<\n  {\n    readonly icon?: string | undefined\n    readonly id: string\n    readonly name: string\n    readonly rdns?: string | readonly string[] | undefined\n    /** @deprecated */\n    readonly supportsSimulation?: boolean | undefined\n    readonly type: string\n\n    setup?(): Promise<void>\n    connect(\n      parameters?:\n        | { chainId?: number | undefined; isReconnecting?: boolean | undefined }\n        | undefined,\n    ): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n    disconnect(): Promise<void>\n    getAccounts(): Promise<readonly Address[]>\n    getChainId(): Promise<number>\n    getProvider(\n      parameters?: { chainId?: number | undefined } | undefined,\n    ): Promise<provider>\n    getClient?(\n      parameters?: { chainId?: number | undefined } | undefined,\n    ): Promise<Client>\n    isAuthorized(): Promise<boolean>\n    switchChain?(\n      parameters: Compute<{\n        addEthereumChainParameter?:\n          | ExactPartial<StrictOmit<AddEthereumChainParameter, 'chainId'>>\n          | undefined\n        chainId: number\n      }>,\n    ): Promise<Chain>\n\n    onAccountsChanged(accounts: string[]): void\n    onChainChanged(chainId: string): void\n    onConnect?(connectInfo: ProviderConnectInfo): void\n    onDisconnect(error?: Error | undefined): void\n    onMessage?(message: ProviderMessage): void\n  } & properties\n>\n\nexport function createConnector<\n  provider,\n  properties extends Record<string, unknown> = Record<string, unknown>,\n  storageItem extends Record<string, unknown> = Record<string, unknown>,\n  ///\n  createConnectorFn extends CreateConnectorFn<\n    provider,\n    properties,\n    storageItem\n  > = CreateConnectorFn<provider, properties, storageItem>,\n>(createConnectorFn: createConnectorFn) {\n  return createConnectorFn\n}\n", "import {\n  type AddEthereumChainParameter,\n  type Address,\n  type EIP1193<PERSON><PERSON><PERSON>,\n  getAddress,\n  numberToHex,\n  type ProviderConnectInfo,\n  type ProviderRpcError,\n  ResourceUnavailableRpcError,\n  type RpcError,\n  SwitchChainError,\n  UserRejectedRequestError,\n  withRetry,\n  withTimeout,\n} from 'viem'\n\nimport type { Connector } from '../createConfig.js'\nimport { ChainNotConfiguredError } from '../errors/config.js'\nimport { ProviderNotFoundError } from '../errors/connector.js'\nimport type { Compute } from '../types/utils.js'\nimport { createConnector } from './createConnector.js'\n\nexport type InjectedParameters = {\n  /**\n   * Some injected providers do not support programmatic disconnect.\n   * This flag simulates the disconnect behavior by keeping track of connection status in storage.\n   * @default true\n   */\n  shimDisconnect?: boolean | undefined\n  /**\n   * [EIP-1193](https://eips.ethereum.org/EIPS/eip-1193) Ethereum Provider to target\n   */\n  target?: TargetId | Target | (() => Target | undefined) | undefined\n  unstable_shimAsyncInject?: boolean | number | undefined\n}\n\ninjected.type = 'injected' as const\nexport function injected(parameters: InjectedParameters = {}) {\n  const { shimDisconnect = true, unstable_shimAsyncInject } = parameters\n\n  function getTarget(): Compute<Target & { id: string }> {\n    const target = parameters.target\n    if (typeof target === 'function') {\n      const result = target()\n      if (result) return result\n    }\n\n    if (typeof target === 'object') return target\n\n    if (typeof target === 'string')\n      return {\n        ...(targetMap[target as keyof typeof targetMap] ?? {\n          id: target,\n          name: `${target[0]!.toUpperCase()}${target.slice(1)}`,\n          provider: `is${target[0]!.toUpperCase()}${target.slice(1)}`,\n        }),\n      }\n\n    return {\n      id: 'injected',\n      name: 'Injected',\n      provider(window) {\n        return window?.ethereum\n      },\n    }\n  }\n\n  type Provider = WalletProvider | undefined\n  type Properties = {\n    onConnect(connectInfo: ProviderConnectInfo): void\n  }\n  type StorageItem = {\n    [_ in 'injected.connected' | `${string}.disconnected`]: true\n  }\n\n  let accountsChanged: Connector['onAccountsChanged'] | undefined\n  let chainChanged: Connector['onChainChanged'] | undefined\n  let connect: Connector['onConnect'] | undefined\n  let disconnect: Connector['onDisconnect'] | undefined\n\n  return createConnector<Provider, Properties, StorageItem>((config) => ({\n    get icon() {\n      return getTarget().icon\n    },\n    get id() {\n      return getTarget().id\n    },\n    get name() {\n      return getTarget().name\n    },\n    /** @deprecated */\n    get supportsSimulation() {\n      return true\n    },\n    type: injected.type,\n    async setup() {\n      const provider = await this.getProvider()\n      // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n      if (provider?.on && parameters.target) {\n        if (!connect) {\n          connect = this.onConnect.bind(this)\n          provider.on('connect', connect)\n        }\n\n        // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n        // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n      }\n    },\n    async connect({ chainId, isReconnecting } = {}) {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      let accounts: readonly Address[] = []\n      if (isReconnecting) accounts = await this.getAccounts().catch(() => [])\n      else if (shimDisconnect) {\n        // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n        try {\n          const permissions = await provider.request({\n            method: 'wallet_requestPermissions',\n            params: [{ eth_accounts: {} }],\n          })\n          accounts = (permissions[0]?.caveats?.[0]?.value as string[])?.map(\n            (x) => getAddress(x),\n          )\n          // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n          // switch to `'eth_accounts'` ordering if more than one account is connected\n          // https://github.com/wevm/wagmi/issues/4140\n          if (accounts.length > 0) {\n            const sortedAccounts = await this.getAccounts()\n            accounts = sortedAccounts\n          }\n        } catch (err) {\n          const error = err as RpcError\n          // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n          // Only bubble up error if user rejects request\n          if (error.code === UserRejectedRequestError.code)\n            throw new UserRejectedRequestError(error)\n          // Or prompt is already open\n          if (error.code === ResourceUnavailableRpcError.code) throw error\n        }\n      }\n\n      try {\n        if (!accounts?.length && !isReconnecting) {\n          const requestedAccounts = await provider.request({\n            method: 'eth_requestAccounts',\n          })\n          accounts = requestedAccounts.map((x) => getAddress(x))\n        }\n\n        // Manage EIP-1193 event listeners\n        // https://eips.ethereum.org/EIPS/eip-1193#events\n        if (connect) {\n          provider.removeListener('connect', connect)\n          connect = undefined\n        }\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n\n        // Switch to chain if provided\n        let currentChainId = await this.getChainId()\n        if (chainId && currentChainId !== chainId) {\n          const chain = await this.switchChain!({ chainId }).catch((error) => {\n            if (error.code === UserRejectedRequestError.code) throw error\n            return { id: currentChainId }\n          })\n          currentChainId = chain?.id ?? currentChainId\n        }\n\n        // Remove disconnected shim if it exists\n        if (shimDisconnect)\n          await config.storage?.removeItem(`${this.id}.disconnected`)\n\n        // Add connected shim if no target exists\n        if (!parameters.target)\n          await config.storage?.setItem('injected.connected', true)\n\n        return { accounts, chainId: currentChainId }\n      } catch (err) {\n        const error = err as RpcError\n        if (error.code === UserRejectedRequestError.code)\n          throw new UserRejectedRequestError(error)\n        if (error.code === ResourceUnavailableRpcError.code)\n          throw new ResourceUnavailableRpcError(error)\n        throw error\n      }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      // Manage EIP-1193 event listeners\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n      if (!connect) {\n        connect = this.onConnect.bind(this)\n        provider.on('connect', connect)\n      }\n\n      // Experimental support for MetaMask disconnect\n      // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n      try {\n        // Adding timeout as not all wallets support this method and can hang\n        // https://github.com/wevm/wagmi/issues/4064\n        await withTimeout(\n          () =>\n            // TODO: Remove explicit type for viem@3\n            provider.request<{\n              Method: 'wallet_revokePermissions'\n              Parameters: [permissions: { eth_accounts: Record<string, any> }]\n              ReturnType: null\n            }>({\n              // `'wallet_revokePermissions'` added in `viem@2.10.3`\n              method: 'wallet_revokePermissions',\n              params: [{ eth_accounts: {} }],\n            }),\n          { timeout: 100 },\n        )\n      } catch {}\n\n      // Add shim signalling connector is disconnected\n      if (shimDisconnect) {\n        await config.storage?.setItem(`${this.id}.disconnected`, true)\n      }\n\n      if (!parameters.target)\n        await config.storage?.removeItem('injected.connected')\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n      const accounts = await provider.request({ method: 'eth_accounts' })\n      return accounts.map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n      const hexChainId = await provider.request({ method: 'eth_chainId' })\n      return Number(hexChainId)\n    },\n    async getProvider() {\n      if (typeof window === 'undefined') return undefined\n\n      let provider: Provider\n      const target = getTarget()\n      if (typeof target.provider === 'function')\n        provider = target.provider(window as Window | undefined)\n      else if (typeof target.provider === 'string')\n        provider = findProvider(window, target.provider)\n      else provider = target.provider\n\n      // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n      // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n      if (provider && !provider.removeListener) {\n        // Try using `off` handler if it exists, otherwise noop\n        if ('off' in provider && typeof provider.off === 'function')\n          provider.removeListener =\n            provider.off as typeof provider.removeListener\n        else provider.removeListener = () => {}\n      }\n\n      return provider\n    },\n    async isAuthorized() {\n      try {\n        const isDisconnected =\n          shimDisconnect &&\n          // If shim exists in storage, connector is disconnected\n          (await config.storage?.getItem(`${this.id}.disconnected`))\n        if (isDisconnected) return false\n\n        // Don't allow injected connector to connect if no target is set and it hasn't already connected\n        // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n        // automatically whenever there is a targeted connector configured.\n        if (!parameters.target) {\n          const connected = await config.storage?.getItem('injected.connected')\n          if (!connected) return false\n        }\n\n        const provider = await this.getProvider()\n        if (!provider) {\n          if (\n            unstable_shimAsyncInject !== undefined &&\n            unstable_shimAsyncInject !== false\n          ) {\n            // If no provider is found, check for async injection\n            // https://github.com/wevm/references/issues/167\n            // https://github.com/MetaMask/detect-provider\n            const handleEthereum = async () => {\n              if (typeof window !== 'undefined')\n                window.removeEventListener(\n                  'ethereum#initialized',\n                  handleEthereum,\n                )\n              const provider = await this.getProvider()\n              return !!provider\n            }\n            const timeout =\n              typeof unstable_shimAsyncInject === 'number'\n                ? unstable_shimAsyncInject\n                : 1_000\n            const res = await Promise.race([\n              ...(typeof window !== 'undefined'\n                ? [\n                    new Promise<boolean>((resolve) =>\n                      window.addEventListener(\n                        'ethereum#initialized',\n                        () => resolve(handleEthereum()),\n                        { once: true },\n                      ),\n                    ),\n                  ]\n                : []),\n              new Promise<boolean>((resolve) =>\n                setTimeout(() => resolve(handleEthereum()), timeout),\n              ),\n            ])\n            if (res) return true\n          }\n\n          throw new ProviderNotFoundError()\n        }\n\n        // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n        // immediately resolve JSON-RPC requests on page load.\n        const accounts = await withRetry(() => this.getAccounts())\n        return !!accounts.length\n      } catch {\n        return false\n      }\n    },\n    async switchChain({ addEthereumChainParameter, chainId }) {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      const chain = config.chains.find((x) => x.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      const promise = new Promise<void>((resolve) => {\n        const listener = ((data) => {\n          if ('chainId' in data && data.chainId === chainId) {\n            config.emitter.off('change', listener)\n            resolve()\n          }\n        }) satisfies Parameters<typeof config.emitter.on>[1]\n        config.emitter.on('change', listener)\n      })\n\n      try {\n        await Promise.all([\n          provider\n            .request({\n              method: 'wallet_switchEthereumChain',\n              params: [{ chainId: numberToHex(chainId) }],\n            })\n            // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n            // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n            // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n            // this callback or an externally emitted `'chainChanged'` event.\n            // https://github.com/MetaMask/metamask-extension/issues/24247\n            .then(async () => {\n              const currentChainId = await this.getChainId()\n              if (currentChainId === chainId)\n                config.emitter.emit('change', { chainId })\n            }),\n          promise,\n        ])\n        return chain\n      } catch (err) {\n        const error = err as RpcError\n\n        // Indicates chain is not added to provider\n        if (\n          error.code === 4902 ||\n          // Unwrapping for MetaMask Mobile\n          // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n          (error as ProviderRpcError<{ originalError?: { code: number } }>)\n            ?.data?.originalError?.code === 4902\n        ) {\n          try {\n            const { default: blockExplorer, ...blockExplorers } =\n              chain.blockExplorers ?? {}\n            let blockExplorerUrls: string[] | undefined\n            if (addEthereumChainParameter?.blockExplorerUrls)\n              blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls\n            else if (blockExplorer)\n              blockExplorerUrls = [\n                blockExplorer.url,\n                ...Object.values(blockExplorers).map((x) => x.url),\n              ]\n\n            let rpcUrls: readonly string[]\n            if (addEthereumChainParameter?.rpcUrls?.length)\n              rpcUrls = addEthereumChainParameter.rpcUrls\n            else rpcUrls = [chain.rpcUrls.default?.http[0] ?? '']\n\n            const addEthereumChain = {\n              blockExplorerUrls,\n              chainId: numberToHex(chainId),\n              chainName: addEthereumChainParameter?.chainName ?? chain.name,\n              iconUrls: addEthereumChainParameter?.iconUrls,\n              nativeCurrency:\n                addEthereumChainParameter?.nativeCurrency ??\n                chain.nativeCurrency,\n              rpcUrls,\n            } satisfies AddEthereumChainParameter\n\n            await Promise.all([\n              provider\n                .request({\n                  method: 'wallet_addEthereumChain',\n                  params: [addEthereumChain],\n                })\n                .then(async () => {\n                  const currentChainId = await this.getChainId()\n                  if (currentChainId === chainId)\n                    config.emitter.emit('change', { chainId })\n                  else\n                    throw new UserRejectedRequestError(\n                      new Error('User rejected switch after adding network.'),\n                    )\n                }),\n              promise,\n            ])\n\n            return chain\n          } catch (error) {\n            throw new UserRejectedRequestError(error as Error)\n          }\n        }\n\n        if (error.code === UserRejectedRequestError.code)\n          throw new UserRejectedRequestError(error)\n        throw new SwitchChainError(error)\n      }\n    },\n    async onAccountsChanged(accounts) {\n      // Disconnect if there are no accounts\n      if (accounts.length === 0) this.onDisconnect()\n      // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n      else if (config.emitter.listenerCount('connect')) {\n        const chainId = (await this.getChainId()).toString()\n        this.onConnect({ chainId })\n        // Remove disconnected shim if it exists\n        if (shimDisconnect)\n          await config.storage?.removeItem(`${this.id}.disconnected`)\n      }\n      // Regular change event\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onConnect(connectInfo) {\n      const accounts = await this.getAccounts()\n      if (accounts.length === 0) return\n\n      const chainId = Number(connectInfo.chainId)\n      config.emitter.emit('connect', { accounts, chainId })\n\n      // Manage EIP-1193 event listeners\n      const provider = await this.getProvider()\n      if (provider) {\n        if (connect) {\n          provider.removeListener('connect', connect)\n          connect = undefined\n        }\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n      }\n    },\n    async onDisconnect(error) {\n      const provider = await this.getProvider()\n\n      // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n      // https://github.com/MetaMask/providers/pull/120\n      if (error && (error as RpcError<1013>).code === 1013) {\n        if (provider && !!(await this.getAccounts()).length) return\n      }\n\n      // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n      // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n      // actually disconnected and we don't need to simulate it.\n      config.emitter.emit('disconnect')\n\n      // Manage EIP-1193 event listeners\n      if (provider) {\n        if (chainChanged) {\n          provider.removeListener('chainChanged', chainChanged)\n          chainChanged = undefined\n        }\n        if (disconnect) {\n          provider.removeListener('disconnect', disconnect)\n          disconnect = undefined\n        }\n        if (!connect) {\n          connect = this.onConnect.bind(this)\n          provider.on('connect', connect)\n        }\n      }\n    },\n  }))\n}\n\nconst targetMap = {\n  coinbaseWallet: {\n    id: 'coinbaseWallet',\n    name: 'Coinbase Wallet',\n    provider(window) {\n      if (window?.coinbaseWalletExtension) return window.coinbaseWalletExtension\n      return findProvider(window, 'isCoinbaseWallet')\n    },\n  },\n  metaMask: {\n    id: 'metaMask',\n    name: 'MetaMask',\n    provider(window) {\n      return findProvider(window, (provider) => {\n        if (!provider.isMetaMask) return false\n        // Brave tries to make itself look like MetaMask\n        // Could also try RPC `web3_clientVersion` if following is unreliable\n        if (provider.isBraveWallet && !provider._events && !provider._state)\n          return false\n        // Other wallets that try to look like MetaMask\n        const flags = [\n          'isApexWallet',\n          'isAvalanche',\n          'isBitKeep',\n          'isBlockWallet',\n          'isKuCoinWallet',\n          'isMathWallet',\n          'isOkxWallet',\n          'isOKExWallet',\n          'isOneInchIOSWallet',\n          'isOneInchAndroidWallet',\n          'isOpera',\n          'isPhantom',\n          'isPortal',\n          'isRabby',\n          'isTokenPocket',\n          'isTokenary',\n          'isUniswapWallet',\n          'isZerion',\n        ] satisfies WalletProviderFlags[]\n        for (const flag of flags) if (provider[flag]) return false\n        return true\n      })\n    },\n  },\n  phantom: {\n    id: 'phantom',\n    name: 'Phantom',\n    provider(window) {\n      if (window?.phantom?.ethereum) return window.phantom?.ethereum\n      return findProvider(window, 'isPhantom')\n    },\n  },\n} as const satisfies TargetMap\n\ntype TargetMap = { [_ in TargetId]?: Target | undefined }\n\ntype Target = {\n  icon?: string | undefined\n  id: string\n  name: string\n  provider:\n    | WalletProviderFlags\n    | WalletProvider\n    | ((window?: Window | undefined) => WalletProvider | undefined)\n}\n\n/** @deprecated */\ntype TargetId = Compute<WalletProviderFlags> extends `is${infer name}`\n  ? name extends `${infer char}${infer rest}`\n    ? `${Lowercase<char>}${rest}`\n    : never\n  : never\n\n/**\n * @deprecated As of 2024/10/16, we are no longer accepting new provider flags as EIP-6963 should be used instead.\n */\ntype WalletProviderFlags =\n  | 'isApexWallet'\n  | 'isAvalanche'\n  | 'isBackpack'\n  | 'isBifrost'\n  | 'isBitKeep'\n  | 'isBitski'\n  | 'isBlockWallet'\n  | 'isBraveWallet'\n  | 'isCoinbaseWallet'\n  | 'isDawn'\n  | 'isEnkrypt'\n  | 'isExodus'\n  | 'isFrame'\n  | 'isFrontier'\n  | 'isGamestop'\n  | 'isHyperPay'\n  | 'isImToken'\n  | 'isKuCoinWallet'\n  | 'isMathWallet'\n  | 'isMetaMask'\n  | 'isOkxWallet'\n  | 'isOKExWallet'\n  | 'isOneInchAndroidWallet'\n  | 'isOneInchIOSWallet'\n  | 'isOpera'\n  | 'isPhantom'\n  | 'isPortal'\n  | 'isRabby'\n  | 'isRainbow'\n  | 'isStatus'\n  | 'isTally'\n  | 'isTokenPocket'\n  | 'isTokenary'\n  | 'isTrust'\n  | 'isTrustWallet'\n  | 'isUniswapWallet'\n  | 'isXDEFI'\n  | 'isZerion'\n\ntype WalletProvider = Compute<\n  EIP1193Provider & {\n    [key in WalletProviderFlags]?: true | undefined\n  } & {\n    providers?: WalletProvider[] | undefined\n    /** Only exists in MetaMask as of 2022/04/03 */\n    _events?: { connect?: (() => void) | undefined } | undefined\n    /** Only exists in MetaMask as of 2022/04/03 */\n    _state?:\n      | {\n          accounts?: string[]\n          initialized?: boolean\n          isConnected?: boolean\n          isPermanentlyDisconnected?: boolean\n          isUnlocked?: boolean\n        }\n      | undefined\n  }\n>\n\ntype Window = {\n  coinbaseWalletExtension?: WalletProvider | undefined\n  ethereum?: WalletProvider | undefined\n  phantom?: { ethereum: WalletProvider } | undefined\n}\n\nfunction findProvider(\n  window: globalThis.Window | Window | undefined,\n  select?: WalletProviderFlags | ((provider: WalletProvider) => boolean),\n) {\n  function isProvider(provider: WalletProvider) {\n    if (typeof select === 'function') return select(provider)\n    if (typeof select === 'string') return provider[select]\n    return true\n  }\n\n  const ethereum = (window as Window).ethereum\n  if (ethereum?.providers)\n    return ethereum.providers.find((provider) => isProvider(provider))\n  if (ethereum && isProvider(ethereum)) return ethereum\n  return undefined\n}\n", "import {\n  type Address,\n  custom,\n  type EIP1193RequestFn,\n  fromH<PERSON>,\n  getAddress,\n  type Hex,\n  keccak256,\n  numberToHex,\n  RpcRequestError,\n  SwitchChainError,\n  stringToHex,\n  type Transport,\n  UserRejectedRequestError,\n  type WalletCallReceipt,\n  type WalletGetCallsStatusReturnType,\n  type WalletRpcSchema,\n} from 'viem'\nimport { rpc } from 'viem/utils'\n\nimport {\n  ChainNotConfiguredError,\n  ConnectorNotConnectedError,\n} from '../errors/config.js'\nimport { createConnector } from './createConnector.js'\n\nexport type MockParameters = {\n  accounts: readonly [Address, ...Address[]]\n  features?:\n    | {\n        defaultConnected?: boolean | undefined\n        connectError?: boolean | Error | undefined\n        switchChainError?: boolean | Error | undefined\n        signMessageError?: boolean | Error | undefined\n        signTypedDataError?: boolean | Error | undefined\n        reconnect?: boolean | undefined\n        watchAssetError?: boolean | Error | undefined\n      }\n    | undefined\n}\n\nmock.type = 'mock' as const\nexport function mock(parameters: MockParameters) {\n  const transactionCache = new Map<Hex, Hex[]>()\n  const features =\n    parameters.features ??\n    ({ defaultConnected: false } satisfies MockParameters['features'])\n\n  type Provider = ReturnType<\n    Transport<'custom', unknown, EIP1193RequestFn<WalletRpcSchema>>\n  >\n  type Properties = {\n    connect(parameters?: {\n      chainId?: number | undefined\n      isReconnecting?: boolean | undefined\n      foo?: string | undefined\n    }): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n  }\n  let connected = features.defaultConnected\n  let connectedChainId: number\n\n  return createConnector<Provider, Properties>((config) => ({\n    id: 'mock',\n    name: 'Mock Connector',\n    type: mock.type,\n    async setup() {\n      connectedChainId = config.chains[0].id\n    },\n    async connect({ chainId } = {}) {\n      if (features.connectError) {\n        if (typeof features.connectError === 'boolean')\n          throw new UserRejectedRequestError(new Error('Failed to connect.'))\n        throw features.connectError\n      }\n\n      const provider = await this.getProvider()\n      const accounts = await provider.request({\n        method: 'eth_requestAccounts',\n      })\n\n      let currentChainId = await this.getChainId()\n      if (chainId && currentChainId !== chainId) {\n        const chain = await this.switchChain!({ chainId })\n        currentChainId = chain.id\n      }\n\n      connected = true\n\n      return {\n        accounts: accounts.map((x) => getAddress(x)),\n        chainId: currentChainId,\n      }\n    },\n    async disconnect() {\n      connected = false\n    },\n    async getAccounts() {\n      if (!connected) throw new ConnectorNotConnectedError()\n      const provider = await this.getProvider()\n      const accounts = await provider.request({ method: 'eth_accounts' })\n      return accounts.map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      const hexChainId = await provider.request({ method: 'eth_chainId' })\n      return fromHex(hexChainId, 'number')\n    },\n    async isAuthorized() {\n      if (!features.reconnect) return false\n      if (!connected) return false\n      const accounts = await this.getAccounts()\n      return !!accounts.length\n    },\n    async switchChain({ chainId }) {\n      const provider = await this.getProvider()\n      const chain = config.chains.find((x) => x.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      await provider.request({\n        method: 'wallet_switchEthereumChain',\n        params: [{ chainId: numberToHex(chainId) }],\n      })\n      return chain\n    },\n    onAccountsChanged(accounts) {\n      if (accounts.length === 0) this.onDisconnect()\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onDisconnect(_error) {\n      config.emitter.emit('disconnect')\n      connected = false\n    },\n    async getProvider({ chainId } = {}) {\n      const chain =\n        config.chains.find((x) => x.id === chainId) ?? config.chains[0]\n      const url = chain.rpcUrls.default.http[0]!\n\n      const request: EIP1193RequestFn = async ({ method, params }) => {\n        // eth methods\n        if (method === 'eth_chainId') return numberToHex(connectedChainId)\n        if (method === 'eth_requestAccounts') return parameters.accounts\n        if (method === 'eth_signTypedData_v4')\n          if (features.signTypedDataError) {\n            if (typeof features.signTypedDataError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to sign typed data.'),\n              )\n            throw features.signTypedDataError\n          }\n\n        // wallet methods\n        if (method === 'wallet_switchEthereumChain') {\n          if (features.switchChainError) {\n            if (typeof features.switchChainError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to switch chain.'),\n              )\n            throw features.switchChainError\n          }\n          type Params = [{ chainId: Hex }]\n          connectedChainId = fromHex((params as Params)[0].chainId, 'number')\n          this.onChainChanged(connectedChainId.toString())\n          return\n        }\n\n        if (method === 'wallet_watchAsset') {\n          if (features.watchAssetError) {\n            if (typeof features.watchAssetError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to switch chain.'),\n              )\n            throw features.watchAssetError\n          }\n          return connected\n        }\n\n        if (method === 'wallet_getCapabilities')\n          return {\n            '0x2105': {\n              paymasterService: {\n                supported:\n                  (params as [Hex])[0] ===\n                  '******************************************',\n              },\n              sessionKeys: {\n                supported: true,\n              },\n            },\n            '0x14A34': {\n              paymasterService: {\n                supported:\n                  (params as [Hex])[0] ===\n                  '******************************************',\n              },\n            },\n          }\n\n        if (method === 'wallet_sendCalls') {\n          const hashes = []\n          const calls = (params as any)[0].calls\n          const from = (params as any)[0].from\n          for (const call of calls) {\n            const { result, error } = await rpc.http(url, {\n              body: {\n                method: 'eth_sendTransaction',\n                params: [\n                  {\n                    ...call,\n                    ...(typeof from !== 'undefined' ? { from } : {}),\n                  },\n                ],\n              },\n            })\n            if (error)\n              throw new RpcRequestError({\n                body: { method, params },\n                error,\n                url,\n              })\n            hashes.push(result)\n          }\n          const id = keccak256(stringToHex(JSON.stringify(calls)))\n          transactionCache.set(id, hashes)\n          return { id }\n        }\n\n        if (method === 'wallet_getCallsStatus') {\n          const hashes = transactionCache.get((params as any)[0])\n          if (!hashes)\n            return {\n              atomic: false,\n              chainId: '0x1',\n              id: (params as any)[0],\n              status: 100,\n              receipts: [],\n              version: '2.0.0',\n            } satisfies WalletGetCallsStatusReturnType\n\n          const receipts = await Promise.all(\n            hashes.map(async (hash) => {\n              const { result, error } = await rpc.http(url, {\n                body: {\n                  method: 'eth_getTransactionReceipt',\n                  params: [hash],\n                  id: 0,\n                },\n              })\n              if (error)\n                throw new RpcRequestError({\n                  body: { method, params },\n                  error,\n                  url,\n                })\n              if (!result) return null\n              return {\n                blockHash: result.blockHash,\n                blockNumber: result.blockNumber,\n                gasUsed: result.gasUsed,\n                logs: result.logs,\n                status: result.status,\n                transactionHash: result.transactionHash,\n              } satisfies WalletCallReceipt\n            }),\n          )\n          const receipts_ = receipts.filter((x) => x !== null)\n          if (receipts_.length === 0)\n            return {\n              atomic: false,\n              chainId: '0x1',\n              id: (params as any)[0],\n              status: 100,\n              receipts: [],\n              version: '2.0.0',\n            } satisfies WalletGetCallsStatusReturnType\n          return {\n            atomic: false,\n            chainId: '0x1',\n            id: (params as any)[0],\n            status: 200,\n            receipts: receipts_,\n            version: '2.0.0',\n          } satisfies WalletGetCallsStatusReturnType\n        }\n\n        if (method === 'wallet_showCallsStatus') return\n\n        // other methods\n        if (method === 'personal_sign') {\n          if (features.signMessageError) {\n            if (typeof features.signMessageError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to sign message.'),\n              )\n            throw features.signMessageError\n          }\n          // Change `personal_sign` to `eth_sign` and swap params\n          method = 'eth_sign'\n          type Params = [data: Hex, address: Address]\n          params = [(params as Params)[1], (params as Params)[0]]\n        }\n\n        const body = { method, params }\n        const { error, result } = await rpc.http(url, { body })\n        if (error) throw new RpcRequestError({ body, error, url })\n\n        return result\n      }\n      return custom({ request })({ retryCount: 0 })\n    },\n  }))\n}\n", "import type { EIP1193Provider } from './register.js'\nimport type {\n  EIP6963AnnounceProviderEvent,\n  EIP6963ProviderDetail,\n} from './types.js'\n\n////////////////////////////////////////////////////////////////////////////\n// Announce Provider\n\nexport type AnnounceProviderParameters = EIP6963ProviderDetail<\n  EIP1193Provider,\n  string\n>\nexport type AnnounceProviderReturnType = () => void\n\n/**\n * Announces an EIP-1193 Provider.\n */\nexport function announceProvider(\n  detail: AnnounceProviderParameters,\n): AnnounceProviderReturnType {\n  const event: CustomEvent<EIP6963ProviderDetail> = new CustomEvent(\n    'eip6963:announceProvider',\n    { detail: Object.freeze(detail) },\n  )\n\n  window.dispatchEvent(event)\n\n  const handler = () => window.dispatchEvent(event)\n  window.addEventListener('eip6963:requestProvider', handler)\n  return () => window.removeEventListener('eip6963:requestProvider', handler)\n}\n\n////////////////////////////////////////////////////////////////////////////\n// Request Providers\n\nexport type RequestProvidersParameters = (\n  providerDetail: EIP6963ProviderDetail,\n) => void\nexport type RequestProvidersReturnType = (() => void) | undefined\n\n/**\n * Watches for EIP-1193 Providers to be announced.\n */\nexport function requestProviders(\n  listener: RequestProvidersParameters,\n): RequestProvidersReturnType {\n  if (typeof window === 'undefined') return\n  const handler = (event: EIP6963AnnounceProviderEvent) =>\n    listener(event.detail)\n\n  window.addEventListener('eip6963:announceProvider', handler)\n\n  window.dispatchEvent(new CustomEvent('eip6963:requestProvider'))\n\n  return () => window.removeEventListener('eip6963:announceProvider', handler)\n}\n", "import type { Rdns } from './register.js'\nimport type { EIP6963ProviderDetail } from './types.js'\nimport { requestProviders } from './utils.js'\n\nexport type Listener = (\n  providerDetails: readonly EIP6963ProviderDetail[],\n  meta?:\n    | {\n        added?: readonly EIP6963ProviderDetail[] | undefined\n        removed?: readonly EIP6963ProviderDetail[] | undefined\n      }\n    | undefined,\n) => void\n\nexport type Store = {\n  /**\n   * Clears the store, including all provider details.\n   */\n  clear(): void\n  /**\n   * Destroys the store, including all provider details and listeners.\n   */\n  destroy(): void\n  /**\n   * Finds a provider detail by its RDNS (Reverse Domain Name Identifier).\n   */\n  findProvider(args: { rdns: Rdns }): EIP6963ProviderDetail | undefined\n  /**\n   * Returns all provider details that have been emitted.\n   */\n  getProviders(): readonly EIP6963ProviderDetail[]\n  /**\n   * Resets the store, and emits an event to request provider details.\n   */\n  reset(): void\n  /**\n   * Subscribes to emitted provider details.\n   */\n  subscribe(\n    listener: Listener,\n    args?: { emitImmediately?: boolean | undefined } | undefined,\n  ): () => void\n\n  /**\n   * @internal\n   * Current state of listening listeners.\n   */\n  _listeners(): Set<Listener>\n}\n\nexport function createStore(): Store {\n  const listeners: Set<Listener> = new Set()\n  let providerDetails: readonly EIP6963ProviderDetail[] = []\n\n  const request = () =>\n    requestProviders((providerDetail) => {\n      if (\n        providerDetails.some(\n          ({ info }) => info.uuid === providerDetail.info.uuid,\n        )\n      )\n        return\n\n      providerDetails = [...providerDetails, providerDetail]\n      listeners.forEach((listener) =>\n        listener(providerDetails, { added: [providerDetail] }),\n      )\n    })\n  let unwatch = request()\n\n  return {\n    _listeners() {\n      return listeners\n    },\n    clear() {\n      listeners.forEach((listener) =>\n        listener([], { removed: [...providerDetails] }),\n      )\n      providerDetails = []\n    },\n    destroy() {\n      this.clear()\n      listeners.clear()\n      unwatch?.()\n    },\n    findProvider({ rdns }) {\n      return providerDetails.find(\n        (providerDetail) => providerDetail.info.rdns === rdns,\n      )\n    },\n    getProviders() {\n      return providerDetails\n    },\n    reset() {\n      this.clear()\n      unwatch?.()\n      unwatch = request()\n    },\n    subscribe(listener, { emitImmediately } = {}) {\n      listeners.add(listener)\n      if (emitImmediately) listener(providerDetails, { added: providerDetails })\n      return () => listeners.delete(listener)\n    },\n  }\n}\n", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return [\n              true,\n              options.migrate(\n                deserializedStorageValue.state,\n                deserializedStorageValue.version\n              )\n            ];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import { EventEmitter } from 'eventemitter3'\n\ntype EventMap = Record<string, object | never>\ntype EventKey<eventMap extends EventMap> = string & keyof eventMap\ntype EventFn<parameters extends unknown[] = any[]> = (\n  ...parameters: parameters\n) => void\nexport type EventData<\n  eventMap extends EventMap,\n  eventName extends keyof eventMap,\n> = (eventMap[eventName] extends [never] ? unknown : eventMap[eventName]) & {\n  uid: string\n}\n\nexport class Emitter<eventMap extends EventMap> {\n  _emitter = new EventEmitter()\n\n  constructor(public uid: string) {}\n\n  on<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.on(eventName, fn as EventFn)\n  }\n\n  once<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.once(eventName, fn as EventFn)\n  }\n\n  off<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.off(eventName, fn as EventFn)\n  }\n\n  emit<key extends EventKey<eventMap>>(\n    eventName: key,\n    ...params: eventMap[key] extends [never] ? [] : [data: eventMap[key]]\n  ) {\n    const data = params[0]\n    this._emitter.emit(eventName, { uid: this.uid, ...data })\n  }\n\n  listenerCount<key extends EventKey<eventMap>>(eventName: key) {\n    return this._emitter.listenerCount(eventName)\n  }\n}\n\nexport function createEmitter<eventMap extends EventMap>(uid: string) {\n  return new Emitter<eventMap>(uid)\n}\n", "type Reviver = (key: string, value: any) => any\n\nexport function deserialize<type>(value: string, reviver?: Reviver): type {\n  return JSON.parse(value, (key, value_) => {\n    let value = value_\n    if (value?.__type === 'bigint') value = BigInt(value.value)\n    if (value?.__type === 'Map') value = new Map(value.value)\n    return reviver?.(key, value) ?? value\n  })\n}\n", "/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys: string[], cutoff: number) {\n  return keys.slice(0, cutoff).join('.') || '.'\n}\n\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array: any[], value: any) {\n  const { length } = array\n\n  for (let index = 0; index < length; ++index) {\n    if (array[index] === value) {\n      return index + 1\n    }\n  }\n\n  return 0\n}\n\ntype StandardReplacer = (key: string, value: any) => any\ntype CircularReplacer = (key: string, value: any, referenceKey: string) => any\n\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(\n  replacer?: StandardReplacer | null | undefined,\n  circularReplacer?: CircularReplacer | null | undefined,\n): StandardReplacer {\n  const hasReplacer = typeof replacer === 'function'\n  const hasCircularReplacer = typeof circularReplacer === 'function'\n\n  const cache: any[] = []\n  const keys: string[] = []\n\n  return function replace(this: any, key: string, value: any) {\n    if (typeof value === 'object') {\n      if (cache.length) {\n        const thisCutoff = getCutoff(cache, this)\n\n        if (thisCutoff === 0) {\n          cache[cache.length] = this\n        } else {\n          cache.splice(thisCutoff)\n          keys.splice(thisCutoff)\n        }\n\n        keys[keys.length] = key\n\n        const valueCutoff = getCutoff(cache, value)\n\n        if (valueCutoff !== 0) {\n          return hasCircularReplacer\n            ? circularReplacer.call(\n                this,\n                key,\n                value,\n                getReferenceKey(keys, valueCutoff),\n              )\n            : `[ref=${getReferenceKey(keys, valueCutoff)}]`\n        }\n      } else {\n        cache[0] = value\n        keys[0] = key\n      }\n    }\n\n    return hasReplacer ? replacer.call(this, key, value) : value\n  }\n}\n\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nexport function serialize(\n  value: any,\n  replacer?: StandardReplacer | null | undefined,\n  indent?: number | null | undefined,\n  circularReplacer?: CircularReplacer | null | undefined,\n) {\n  return JSON.stringify(\n    value,\n    createReplacer((key, value_) => {\n      let value = value_\n      if (typeof value === 'bigint')\n        value = { __type: 'bigint', value: value_.toString() }\n      if (value instanceof Map)\n        value = { __type: 'Map', value: Array.from(value_.entries()) }\n      return replacer?.(key, value) ?? value\n    }, circularReplacer),\n    indent ?? undefined,\n  )\n}\n", "import type { PartializedState } from './createConfig.js'\nimport type { Compute } from './types/utils.js'\nimport { deserialize as deserialize_ } from './utils/deserialize.js'\nimport { serialize as serialize_ } from './utils/serialize.js'\n\n// key-values for loose autocomplete and typing\nexport type StorageItemMap = {\n  recentConnectorId: string\n  state: PartializedState\n}\n\nexport type Storage<\n  itemMap extends Record<string, unknown> = Record<string, unknown>,\n  ///\n  storageItemMap extends StorageItemMap = StorageItemMap & itemMap,\n> = {\n  key: string\n  getItem<\n    key extends keyof storageItemMap,\n    value extends storageItemMap[key],\n    defaultValue extends value | null | undefined,\n  >(\n    key: key,\n    defaultValue?: defaultValue | undefined,\n  ):\n    | (defaultValue extends null ? value | null : value)\n    | Promise<defaultValue extends null ? value | null : value>\n  setItem<\n    key extends keyof storageItemMap,\n    value extends storageItemMap[key] | null,\n  >(key: key, value: value): void | Promise<void>\n  removeItem(key: keyof storageItemMap): void | Promise<void>\n}\n\nexport type BaseStorage = {\n  getItem(\n    key: string,\n  ): string | null | undefined | Promise<string | null | undefined>\n  setItem(key: string, value: string): void | Promise<void>\n  removeItem(key: string): void | Promise<void>\n}\n\nexport type CreateStorageParameters = {\n  deserialize?: (<type>(value: string) => type | unknown) | undefined\n  key?: string | undefined\n  serialize?: (<type>(value: type | any) => string) | undefined\n  storage?: Compute<BaseStorage> | undefined\n}\n\nexport function createStorage<\n  itemMap extends Record<string, unknown> = Record<string, unknown>,\n  storageItemMap extends StorageItemMap = StorageItemMap & itemMap,\n>(parameters: CreateStorageParameters): Compute<Storage<storageItemMap>> {\n  const {\n    deserialize = deserialize_,\n    key: prefix = 'wagmi',\n    serialize = serialize_,\n    storage = noopStorage,\n  } = parameters\n\n  function unwrap<type>(value: type): type | Promise<type> {\n    if (value instanceof Promise) return value.then((x) => x).catch(() => null)\n    return value\n  }\n\n  return {\n    ...storage,\n    key: prefix,\n    async getItem(key, defaultValue) {\n      const value = storage.getItem(`${prefix}.${key as string}`)\n      const unwrapped = await unwrap(value)\n      if (unwrapped) return deserialize(unwrapped) ?? null\n      return (defaultValue ?? null) as any\n    },\n    async setItem(key, value) {\n      const storageKey = `${prefix}.${key as string}`\n      if (value === null) await unwrap(storage.removeItem(storageKey))\n      else await unwrap(storage.setItem(storageKey, serialize(value)))\n    },\n    async removeItem(key) {\n      await unwrap(storage.removeItem(`${prefix}.${key as string}`))\n    },\n  }\n}\n\nexport const noopStorage = {\n  getItem: () => null,\n  setItem: () => {},\n  removeItem: () => {},\n} satisfies BaseStorage\n\nexport function getDefaultStorage() {\n  const storage = (() => {\n    if (typeof window !== 'undefined' && window.localStorage)\n      return window.localStorage\n    return noopStorage\n  })()\n  return {\n    getItem(key) {\n      return storage.getItem(key)\n    },\n    removeItem(key) {\n      storage.removeItem(key)\n    },\n    setItem(key, value) {\n      try {\n        storage.setItem(key, value)\n        // silence errors by default (QuotaExceededError, SecurityError, etc.)\n      } catch {}\n    },\n  } satisfies BaseStorage\n}\n", "const size = 256\nlet index = size\nlet buffer: string\n\nexport function uid(length = 11) {\n  if (!buffer || index + length > size * 2) {\n    buffer = ''\n    index = 0\n    for (let i = 0; i < size; i++) {\n      buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1)\n    }\n  }\n  return buffer.substring(index, index++ + length)\n}\n", "import {\n  createStore as createMipd,\n  type EIP6963ProviderDetail,\n  type Store as MipdStore,\n} from 'mipd'\nimport {\n  type Address,\n  type Chain,\n  type Client,\n  createClient,\n  type EIP1193RequestFn,\n  type ClientConfig as viem_ClientConfig,\n  type Transport as viem_Transport,\n} from 'viem'\nimport { persist, subscribeWithSelector } from 'zustand/middleware'\nimport { createStore, type Mutate, type StoreApi } from 'zustand/vanilla'\n\nimport type {\n  ConnectorEventMap,\n  CreateConnectorFn,\n} from './connectors/createConnector.js'\nimport { injected } from './connectors/injected.js'\nimport { createEmitter, type Emitter, type EventData } from './createEmitter.js'\nimport {\n  createStorage,\n  getDefaultStorage,\n  type Storage,\n} from './createStorage.js'\nimport { ChainNotConfiguredError } from './errors/config.js'\nimport type {\n  Compute,\n  ExactPartial,\n  LooseOmit,\n  OneOf,\n  RemoveUndefined,\n} from './types/utils.js'\nimport { uid } from './utils/uid.js'\nimport { version } from './version.js'\n\nexport function createConfig<\n  const chains extends readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport>,\n  const connectorFns extends readonly CreateConnectorFn[],\n>(\n  parameters: CreateConfigParameters<chains, transports, connectorFns>,\n): Config<chains, transports, connectorFns> {\n  const {\n    multiInjectedProviderDiscovery = true,\n    storage = createStorage({\n      storage: getDefaultStorage(),\n    }),\n    syncConnectedChain = true,\n    ssr = false,\n    ...rest\n  } = parameters\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Set up connectors, clients, etc.\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  const mipd =\n    typeof window !== 'undefined' && multiInjectedProviderDiscovery\n      ? createMipd()\n      : undefined\n\n  const chains = createStore(() => rest.chains)\n  const connectors = createStore(() => {\n    const collection = []\n    const rdnsSet = new Set<string>()\n    for (const connectorFns of rest.connectors ?? []) {\n      const connector = setup(connectorFns)\n      collection.push(connector)\n      if (!ssr && connector.rdns) {\n        const rdnsValues =\n          typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns\n        for (const rdns of rdnsValues) {\n          rdnsSet.add(rdns)\n        }\n      }\n    }\n    if (!ssr && mipd) {\n      const providers = mipd.getProviders()\n      for (const provider of providers) {\n        if (rdnsSet.has(provider.info.rdns)) continue\n        collection.push(setup(providerDetailToConnector(provider)))\n      }\n    }\n    return collection\n  })\n  function setup(connectorFn: CreateConnectorFn): Connector {\n    // Set up emitter with uid and add to connector so they are \"linked\" together.\n    const emitter = createEmitter<ConnectorEventMap>(uid())\n    const connector = {\n      ...connectorFn({\n        emitter,\n        chains: chains.getState(),\n        storage,\n        transports: rest.transports,\n      }),\n      emitter,\n      uid: emitter.uid,\n    }\n\n    // Start listening for `connect` events on connector setup\n    // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n    emitter.on('connect', connect)\n    connector.setup?.()\n\n    return connector\n  }\n  function providerDetailToConnector(providerDetail: EIP6963ProviderDetail) {\n    const { info } = providerDetail\n    const provider = providerDetail.provider as any\n    return injected({ target: { ...info, id: info.rdns, provider } })\n  }\n\n  const clients = new Map<number, Client<Transport, chains[number]>>()\n  function getClient<chainId extends chains[number]['id']>(\n    config: { chainId?: chainId | chains[number]['id'] | undefined } = {},\n  ): Client<Transport, Extract<chains[number], { id: chainId }>> {\n    const chainId = config.chainId ?? store.getState().chainId\n    const chain = chains.getState().find((x) => x.id === chainId)\n\n    // chainId specified and not configured\n    if (config.chainId && !chain) throw new ChainNotConfiguredError()\n\n    // If the target chain is not configured, use the client of the current chain.\n    type Return = Client<Transport, Extract<chains[number], { id: chainId }>>\n    {\n      const client = clients.get(store.getState().chainId)\n      if (client && !chain) return client as Return\n      if (!chain) throw new ChainNotConfiguredError()\n    }\n\n    // If a memoized client exists for a chain id, use that.\n    {\n      const client = clients.get(chainId)\n      if (client) return client as Return\n    }\n\n    let client: Client<Transport, chains[number]>\n    if (rest.client) client = rest.client({ chain })\n    else {\n      const chainId = chain.id as chains[number]['id']\n      const chainIds = chains.getState().map((x) => x.id)\n      // Grab all properties off `rest` and resolve for use in `createClient`\n      const properties: Partial<viem_ClientConfig> = {}\n      const entries = Object.entries(rest) as [keyof typeof rest, any][]\n\n      for (const [key, value] of entries) {\n        if (\n          key === 'chains' ||\n          key === 'client' ||\n          key === 'connectors' ||\n          key === 'transports'\n        )\n          continue\n\n        if (typeof value === 'object') {\n          // check if value is chainId-specific since some values can be objects\n          // e.g. { batch: { multicall: { batchSize: 1024 } } }\n          if (chainId in value) properties[key] = value[chainId]\n          else {\n            // check if value is chainId-specific, but does not have value for current chainId\n            const hasChainSpecificValue = chainIds.some((x) => x in value)\n            if (hasChainSpecificValue) continue\n            properties[key] = value\n          }\n        } else properties[key] = value\n      }\n\n      client = createClient({\n        ...properties,\n        chain,\n        batch: properties.batch ?? { multicall: true },\n        transport: (parameters) =>\n          rest.transports[chainId]({ ...parameters, connectors }),\n      })\n    }\n\n    clients.set(chainId, client)\n    return client as Return\n  }\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Create store\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  function getInitialState(): State {\n    return {\n      chainId: chains.getState()[0].id,\n      connections: new Map<string, Connection>(),\n      current: null,\n      status: 'disconnected',\n    }\n  }\n\n  let currentVersion: number\n  const prefix = '0.0.0-canary-'\n  if (version.startsWith(prefix))\n    currentVersion = Number.parseInt(version.replace(prefix, ''))\n  // use package major version to version store\n  else currentVersion = Number.parseInt(version.split('.')[0] ?? '0')\n\n  const store = createStore(\n    subscribeWithSelector(\n      // only use persist middleware if storage exists\n      storage\n        ? persist(getInitialState, {\n            migrate(persistedState, version) {\n              if (version === currentVersion) return persistedState as State\n\n              const initialState = getInitialState()\n              const chainId = validatePersistedChainId(\n                persistedState,\n                initialState.chainId,\n              )\n              return { ...initialState, chainId }\n            },\n            name: 'store',\n            partialize(state) {\n              // Only persist \"critical\" store properties to preserve storage size.\n              return {\n                connections: {\n                  __type: 'Map',\n                  value: Array.from(state.connections.entries()).map(\n                    ([key, connection]) => {\n                      const { id, name, type, uid } = connection.connector\n                      const connector = { id, name, type, uid }\n                      return [key, { ...connection, connector }]\n                    },\n                  ),\n                } as unknown as PartializedState['connections'],\n                chainId: state.chainId,\n                current: state.current,\n              } satisfies PartializedState\n            },\n            merge(persistedState, currentState) {\n              // `status` should not be persisted as it messes with reconnection\n              if (\n                typeof persistedState === 'object' &&\n                persistedState &&\n                'status' in persistedState\n              )\n                delete persistedState.status\n              // Make sure persisted `chainId` is valid\n              const chainId = validatePersistedChainId(\n                persistedState,\n                currentState.chainId,\n              )\n              return {\n                ...currentState,\n                ...(persistedState as object),\n                chainId,\n              }\n            },\n            skipHydration: ssr,\n            storage: storage as Storage<Record<string, unknown>>,\n            version: currentVersion,\n          })\n        : getInitialState,\n    ),\n  )\n  store.setState(getInitialState())\n\n  function validatePersistedChainId(\n    persistedState: unknown,\n    defaultChainId: number,\n  ) {\n    return persistedState &&\n      typeof persistedState === 'object' &&\n      'chainId' in persistedState &&\n      typeof persistedState.chainId === 'number' &&\n      chains.getState().some((x) => x.id === persistedState.chainId)\n      ? persistedState.chainId\n      : defaultChainId\n  }\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Subscribe to changes\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  // Update default chain when connector chain changes\n  if (syncConnectedChain)\n    store.subscribe(\n      ({ connections, current }) =>\n        current ? connections.get(current)?.chainId : undefined,\n      (chainId) => {\n        // If chain is not configured, then don't switch over to it.\n        const isChainConfigured = chains\n          .getState()\n          .some((x) => x.id === chainId)\n        if (!isChainConfigured) return\n\n        return store.setState((x) => ({\n          ...x,\n          chainId: chainId ?? x.chainId,\n        }))\n      },\n    )\n\n  // EIP-6963 subscribe for new wallet providers\n  mipd?.subscribe((providerDetails) => {\n    const connectorIdSet = new Set<string>()\n    const connectorRdnsSet = new Set<string>()\n    for (const connector of connectors.getState()) {\n      connectorIdSet.add(connector.id)\n      if (connector.rdns) {\n        const rdnsValues =\n          typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns\n        for (const rdns of rdnsValues) {\n          connectorRdnsSet.add(rdns)\n        }\n      }\n    }\n\n    const newConnectors: Connector[] = []\n    for (const providerDetail of providerDetails) {\n      if (connectorRdnsSet.has(providerDetail.info.rdns)) continue\n      const connector = setup(providerDetailToConnector(providerDetail))\n      if (connectorIdSet.has(connector.id)) continue\n      newConnectors.push(connector)\n    }\n\n    if (storage && !store.persist.hasHydrated()) return\n    connectors.setState((x) => [...x, ...newConnectors], true)\n  })\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Emitter listeners\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  function change(data: EventData<ConnectorEventMap, 'change'>) {\n    store.setState((x) => {\n      const connection = x.connections.get(data.uid)\n      if (!connection) return x\n      return {\n        ...x,\n        connections: new Map(x.connections).set(data.uid, {\n          accounts:\n            (data.accounts as readonly [Address, ...Address[]]) ??\n            connection.accounts,\n          chainId: data.chainId ?? connection.chainId,\n          connector: connection.connector,\n        }),\n      }\n    })\n  }\n  function connect(data: EventData<ConnectorEventMap, 'connect'>) {\n    // Disable handling if reconnecting/connecting\n    if (\n      store.getState().status === 'connecting' ||\n      store.getState().status === 'reconnecting'\n    )\n      return\n\n    store.setState((x) => {\n      const connector = connectors.getState().find((x) => x.uid === data.uid)\n      if (!connector) return x\n\n      if (connector.emitter.listenerCount('connect'))\n        connector.emitter.off('connect', change)\n      if (!connector.emitter.listenerCount('change'))\n        connector.emitter.on('change', change)\n      if (!connector.emitter.listenerCount('disconnect'))\n        connector.emitter.on('disconnect', disconnect)\n\n      return {\n        ...x,\n        connections: new Map(x.connections).set(data.uid, {\n          accounts: data.accounts as readonly [Address, ...Address[]],\n          chainId: data.chainId,\n          connector: connector,\n        }),\n        current: data.uid,\n        status: 'connected',\n      }\n    })\n  }\n  function disconnect(data: EventData<ConnectorEventMap, 'disconnect'>) {\n    store.setState((x) => {\n      const connection = x.connections.get(data.uid)\n      if (connection) {\n        const connector = connection.connector\n        if (connector.emitter.listenerCount('change'))\n          connection.connector.emitter.off('change', change)\n        if (connector.emitter.listenerCount('disconnect'))\n          connection.connector.emitter.off('disconnect', disconnect)\n        if (!connector.emitter.listenerCount('connect'))\n          connection.connector.emitter.on('connect', connect)\n      }\n\n      x.connections.delete(data.uid)\n\n      if (x.connections.size === 0)\n        return {\n          ...x,\n          connections: new Map(),\n          current: null,\n          status: 'disconnected',\n        }\n\n      const nextConnection = x.connections.values().next().value as Connection\n      return {\n        ...x,\n        connections: new Map(x.connections),\n        current: nextConnection.connector.uid,\n      }\n    })\n  }\n\n  return {\n    get chains() {\n      return chains.getState() as chains\n    },\n    get connectors() {\n      return connectors.getState() as Connector<connectorFns[number]>[]\n    },\n    storage,\n\n    getClient,\n    get state() {\n      return store.getState() as unknown as State<chains>\n    },\n    setState(value) {\n      let newState: State\n      if (typeof value === 'function') newState = value(store.getState() as any)\n      else newState = value\n\n      // Reset state if it got set to something not matching the base state\n      const initialState = getInitialState()\n      if (typeof newState !== 'object') newState = initialState\n      const isCorrupt = Object.keys(initialState).some((x) => !(x in newState))\n      if (isCorrupt) newState = initialState\n\n      store.setState(newState, true)\n    },\n    subscribe(selector, listener, options) {\n      return store.subscribe(\n        selector as unknown as (state: State) => any,\n        listener,\n        options\n          ? ({\n              ...options,\n              fireImmediately: options.emitImmediately,\n              // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n            } as RemoveUndefined<typeof options>)\n          : undefined,\n      )\n    },\n\n    _internal: {\n      mipd,\n      store,\n      ssr: Boolean(ssr),\n      syncConnectedChain,\n      transports: rest.transports as transports,\n      chains: {\n        setState(value) {\n          const nextChains = (\n            typeof value === 'function' ? value(chains.getState()) : value\n          ) as chains\n          if (nextChains.length === 0) return\n          return chains.setState(nextChains, true)\n        },\n        subscribe(listener) {\n          return chains.subscribe(listener)\n        },\n      },\n      connectors: {\n        providerDetailToConnector,\n        setup: setup as <connectorFn extends CreateConnectorFn>(\n          connectorFn: connectorFn,\n        ) => Connector<connectorFn>,\n        setState(value) {\n          return connectors.setState(\n            typeof value === 'function' ? value(connectors.getState()) : value,\n            true,\n          )\n        },\n        subscribe(listener) {\n          return connectors.subscribe(listener)\n        },\n      },\n      events: { change, connect, disconnect },\n    },\n  }\n}\n\n/////////////////////////////////////////////////////////////////////////////////////////////////\n// Types\n/////////////////////////////////////////////////////////////////////////////////////////////////\n\nexport type CreateConfigParameters<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n  connectorFns extends\n    readonly CreateConnectorFn[] = readonly CreateConnectorFn[],\n> = Compute<\n  {\n    chains: chains\n    connectors?: connectorFns | undefined\n    multiInjectedProviderDiscovery?: boolean | undefined\n    storage?: Storage | null | undefined\n    ssr?: boolean | undefined\n    syncConnectedChain?: boolean | undefined\n  } & OneOf<\n    | ({ transports: transports } & {\n        [key in keyof ClientConfig]?:\n          | ClientConfig[key]\n          | { [_ in chains[number]['id']]?: ClientConfig[key] | undefined }\n          | undefined\n      })\n    | {\n        client(parameters: {\n          chain: chains[number]\n        }): Client<transports[chains[number]['id']], chains[number]>\n      }\n  >\n>\n\nexport type Config<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n  connectorFns extends\n    readonly CreateConnectorFn[] = readonly CreateConnectorFn[],\n> = {\n  readonly chains: chains\n  readonly connectors: readonly Connector<connectorFns[number]>[]\n  readonly storage: Storage | null\n\n  readonly state: State<chains>\n  setState<tchains extends readonly [Chain, ...Chain[]] = chains>(\n    value: State<tchains> | ((state: State<tchains>) => State<tchains>),\n  ): void\n  subscribe<state>(\n    selector: (state: State<chains>) => state,\n    listener: (state: state, previousState: state) => void,\n    options?:\n      | {\n          emitImmediately?: boolean | undefined\n          equalityFn?: ((a: state, b: state) => boolean) | undefined\n        }\n      | undefined,\n  ): () => void\n\n  getClient<chainId extends chains[number]['id']>(parameters?: {\n    chainId?: chainId | chains[number]['id'] | undefined\n  }): Client<transports[chainId], Extract<chains[number], { id: chainId }>>\n\n  /**\n   * Not part of versioned API, proceed with caution.\n   * @internal\n   */\n  _internal: Internal<chains, transports>\n}\n\ntype Internal<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n> = {\n  readonly mipd: MipdStore | undefined\n  readonly store: Mutate<StoreApi<any>, [['zustand/persist', any]]>\n  readonly ssr: boolean\n  readonly syncConnectedChain: boolean\n  readonly transports: transports\n\n  chains: {\n    setState(\n      value:\n        | readonly [Chain, ...Chain[]]\n        | ((\n            state: readonly [Chain, ...Chain[]],\n          ) => readonly [Chain, ...Chain[]]),\n    ): void\n    subscribe(\n      listener: (\n        state: readonly [Chain, ...Chain[]],\n        prevState: readonly [Chain, ...Chain[]],\n      ) => void,\n    ): () => void\n  }\n  connectors: {\n    providerDetailToConnector(\n      providerDetail: EIP6963ProviderDetail,\n    ): CreateConnectorFn\n    setup<connectorFn extends CreateConnectorFn>(\n      connectorFn: connectorFn,\n    ): Connector<connectorFn>\n    setState(value: Connector[] | ((state: Connector[]) => Connector[])): void\n    subscribe(\n      listener: (state: Connector[], prevState: Connector[]) => void,\n    ): () => void\n  }\n  events: {\n    change(data: EventData<ConnectorEventMap, 'change'>): void\n    connect(data: EventData<ConnectorEventMap, 'connect'>): void\n    disconnect(data: EventData<ConnectorEventMap, 'disconnect'>): void\n  }\n}\n\nexport type State<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n> = {\n  chainId: chains[number]['id']\n  connections: Map<string, Connection>\n  current: string | null\n  status: 'connected' | 'connecting' | 'disconnected' | 'reconnecting'\n}\n\nexport type PartializedState = Compute<\n  ExactPartial<Pick<State, 'chainId' | 'connections' | 'current' | 'status'>>\n>\n\nexport type Connection = {\n  accounts: readonly [Address, ...Address[]]\n  chainId: number\n  connector: Connector\n}\n\nexport type Connector<\n  createConnectorFn extends CreateConnectorFn = CreateConnectorFn,\n> = ReturnType<createConnectorFn> & {\n  emitter: Emitter<ConnectorEventMap>\n  uid: string\n}\n\nexport type Transport<\n  type extends string = string,\n  rpcAttributes = Record<string, any>,\n  eip1193RequestFn extends EIP1193RequestFn = EIP1193RequestFn,\n> = (\n  params: Parameters<\n    viem_Transport<type, rpcAttributes, eip1193RequestFn>\n  >[0] & {\n    connectors?: StoreApi<Connector[]> | undefined\n  },\n) => ReturnType<viem_Transport<type, rpcAttributes, eip1193RequestFn>>\n\ntype ClientConfig = LooseOmit<\n  viem_ClientConfig,\n  'account' | 'chain' | 'key' | 'name' | 'transport' | 'type'\n>\n", "import { reconnect } from './actions/reconnect.js'\nimport type { Config, State } from './createConfig.js'\n\ntype HydrateParameters = {\n  initialState?: State | undefined\n  reconnectOnMount?: boolean | undefined\n}\n\nexport function hydrate(config: Config, parameters: HydrateParameters) {\n  const { initialState, reconnectOnMount } = parameters\n\n  if (initialState && !config._internal.store.persist.hasHydrated())\n    config.setState({\n      ...initialState,\n      chainId: config.chains.some((x) => x.id === initialState.chainId)\n        ? initialState.chainId\n        : config.chains[0].id,\n      connections: reconnectOnMount ? initialState.connections : new Map(),\n      status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n    })\n\n  return {\n    async onMount() {\n      if (config._internal.ssr) {\n        await config._internal.store.persist.rehydrate()\n        if (config._internal.mipd) {\n          config._internal.connectors.setState((connectors) => {\n            const rdnsSet = new Set<string>()\n            for (const connector of connectors ?? []) {\n              if (connector.rdns) {\n                const rdnsValues = Array.isArray(connector.rdns)\n                  ? connector.rdns\n                  : [connector.rdns]\n                for (const rdns of rdnsValues) {\n                  rdnsSet.add(rdns)\n                }\n              }\n            }\n            const mipdConnectors = []\n            const providers = config._internal.mipd?.getProviders() ?? []\n            for (const provider of providers) {\n              if (rdnsSet.has(provider.info.rdns)) continue\n              const connectorFn =\n                config._internal.connectors.providerDetailToConnector(provider)\n              const connector = config._internal.connectors.setup(connectorFn)\n              mipdConnectors.push(connector)\n            }\n            return [...connectors, ...mipdConnectors]\n          })\n        }\n      }\n\n      if (reconnectOnMount) reconnect(config)\n      else if (config.storage)\n        // Reset connections that may have been hydrated from storage.\n        config.setState((x) => ({\n          ...x,\n          connections: new Map(),\n        }))\n    },\n  }\n}\n", "import {\n  ChainDisconnectedError,\n  createTransport,\n  type EIP1193Parameters,\n  type EIP1193Provider,\n  type EIP1193RequestFn,\n  hexToNumber,\n  ProviderDisconnectedError,\n  type TransportConfig,\n  type WalletRpcSchema,\n  withRetry,\n  withTimeout,\n} from 'viem'\n\nimport type { Connector, Transport } from '../createConfig.js'\n\nexport type ConnectorTransportConfig = {\n  /** The key of the transport. */\n  key?: TransportConfig['key'] | undefined\n  /** The name of the transport. */\n  name?: TransportConfig['name'] | undefined\n  /** The max number of times to retry. */\n  retryCount?: TransportConfig['retryCount'] | undefined\n  /** The base delay (in ms) between retries. */\n  retryDelay?: TransportConfig['retryDelay'] | undefined\n}\n\nexport type ConnectorTransport = Transport\n\nexport function unstable_connector(\n  connector: Pick<Connector, 'type'>,\n  config: ConnectorTransportConfig = {},\n): Transport<'connector'> {\n  const { type } = connector\n  const { key = 'connector', name = 'Connector', retryDelay } = config\n\n  return (parameters) => {\n    const { chain, connectors } = parameters\n    const retryCount = config.retryCount ?? parameters.retryCount\n\n    const request: EIP1193RequestFn = async ({ method, params }) => {\n      const connector = connectors?.getState().find((c) => c.type === type)\n      if (!connector)\n        throw new ProviderDisconnectedError(\n          new Error(\n            `Could not find connector of type \"${type}\" in \\`connectors\\` passed to \\`createConfig\\`.`,\n          ),\n        )\n\n      const provider = (await connector.getProvider({\n        chainId: chain?.id,\n      })) as EIP1193Provider | undefined\n      if (!provider)\n        throw new ProviderDisconnectedError(\n          new Error('Provider is disconnected.'),\n        )\n\n      // We are applying a retry & timeout strategy here as some injected wallets (e.g. MetaMask) fail to\n      // immediately resolve a JSON-RPC request on page load.\n      const chainId = hexToNumber(\n        await withRetry(() =>\n          withTimeout(() => provider.request({ method: 'eth_chainId' }), {\n            timeout: 100,\n          }),\n        ),\n      )\n      if (chain && chainId !== chain.id)\n        throw new ChainDisconnectedError(\n          new Error(\n            `The current chain of the connector (id: ${chainId}) does not match the target chain for the request (id: ${chain.id} – ${chain.name}).`,\n          ),\n        )\n\n      const body = { method, params } as EIP1193Parameters<WalletRpcSchema>\n      return provider.request(body)\n    }\n\n    return createTransport({\n      key,\n      name,\n      request,\n      retryCount,\n      retryDelay,\n      type: 'connector',\n    })\n  }\n}\n", "import { fallback as viem_fallback } from 'viem'\n\nimport type { Transport } from '../createConfig.js'\n\nexport function fallback(\n  transports: Transport[],\n  config?: Parameters<typeof viem_fallback>[1],\n) {\n  return viem_fallback(transports, config)\n}\n", "import type { Config, State } from '../createConfig.js'\nimport type { BaseStorage } from '../createStorage.js'\nimport { deserialize } from './deserialize.js'\n\nexport const cookieStorage = {\n  getItem(key) {\n    if (typeof window === 'undefined') return null\n    const value = parseCookie(document.cookie, key)\n    return value ?? null\n  },\n  setItem(key, value) {\n    if (typeof window === 'undefined') return\n    // biome-ignore lint/suspicious/noDocumentCookie: using\n    document.cookie = `${key}=${value};path=/;samesite=Lax`\n  },\n  removeItem(key) {\n    if (typeof window === 'undefined') return\n    // biome-ignore lint/suspicious/noDocumentCookie: using\n    document.cookie = `${key}=;max-age=-1;path=/`\n  },\n} satisfies BaseStorage\n\nexport function cookieToInitialState(config: Config, cookie?: string | null) {\n  if (!cookie) return undefined\n  const key = `${config.storage?.key}.store`\n  const parsed = parseCookie(cookie, key)\n  if (!parsed) return undefined\n  return deserialize<{ state: State }>(parsed).state\n}\n\nexport function parseCookie(cookie: string, key: string) {\n  const keyValue = cookie.split('; ').find((x) => x.startsWith(`${key}=`))\n  if (!keyValue) return undefined\n  return keyValue.substring(key.length + 1)\n}\n", "import type { Chain, Transport } from 'viem'\n\ntype ExtractRpcUrlsParameters = {\n  transports?: Record<string, Transport> | undefined\n  chain: Chain\n}\n\nexport function extractRpcUrls(parameters: ExtractRpcUrlsParameters) {\n  const { chain } = parameters\n  const fallbackUrl = chain.rpcUrls.default.http[0]\n\n  if (!parameters.transports) return [fallbackUrl]\n\n  const transport = parameters.transports?.[chain.id]?.({ chain })\n  const transports = (transport?.value?.transports as NonNullable<\n    typeof transport\n  >[]) || [transport]\n  return transports.map(({ value }) => value?.url || fallbackUrl)\n}\n", "/** @deprecated use `Number` instead */\nexport function normalizeChainId(chainId: bigint | number | string | unknown) {\n  if (typeof chainId === 'string')\n    return Number.parseInt(\n      chainId,\n      chainId.trim().substring(0, 2) === '0x' ? 16 : 10,\n    )\n  if (typeof chainId === 'bigint') return Number(chainId)\n  if (typeof chainId === 'number') return chainId\n  throw new Error(\n    `Cannot normalize chainId \"${chainId}\" of type \"${typeof chainId}\"`,\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFM,SAAU,gBAUd,mBAAoC;AACpC,SAAO;AACT;;;ACxDA,SAAS,OAAO;AACV,SAAU,SAAS,aAAiC,CAAA,GAAE;AAC1D,QAAM,EAAE,iBAAiB,MAAM,yBAAwB,IAAK;AAE5D,WAAS,YAAS;AAChB,UAAM,SAAS,WAAW;AAC1B,QAAI,OAAO,WAAW,YAAY;AAChC,YAAM,SAAS,OAAM;AACrB,UAAI;AAAQ,eAAO;IACrB;AAEA,QAAI,OAAO,WAAW;AAAU,aAAO;AAEvC,QAAI,OAAO,WAAW;AACpB,aAAO;QACL,GAAI,UAAU,MAAgC,KAAK;UACjD,IAAI;UACJ,MAAM,GAAG,OAAO,CAAC,EAAG,YAAW,CAAE,GAAG,OAAO,MAAM,CAAC,CAAC;UACnD,UAAU,KAAK,OAAO,CAAC,EAAG,YAAW,CAAE,GAAG,OAAO,MAAM,CAAC,CAAC;;;AAI/D,WAAO;MACL,IAAI;MACJ,MAAM;MACN,SAASA,SAAM;AACb,eAAOA,SAAQ;MACjB;;EAEJ;AAUA,MAAI;AACJ,MAAI;AACJ,MAAIC;AACJ,MAAIC;AAEJ,SAAO,gBAAmD,CAAC,YAAY;IACrE,IAAI,OAAI;AACN,aAAO,UAAS,EAAG;IACrB;IACA,IAAI,KAAE;AACJ,aAAO,UAAS,EAAG;IACrB;IACA,IAAI,OAAI;AACN,aAAO,UAAS,EAAG;IACrB;;IAEA,IAAI,qBAAkB;AACpB,aAAO;IACT;IACA,MAAM,SAAS;IACf,MAAM,QAAK;AACT,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,UAAI,UAAU,MAAM,WAAW,QAAQ;AACrC,YAAI,CAACD,UAAS;AACZ,UAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,mBAAS,GAAG,WAAWA,QAAO;QAChC;AAIA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;MACF;IACF;IACA,MAAM,QAAQ,EAAE,SAAS,eAAc,IAAK,CAAA,GAAE;AAC5C,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,UAAI,WAA+B,CAAA;AACnC,UAAI;AAAgB,mBAAW,MAAM,KAAK,YAAW,EAAG,MAAM,MAAM,CAAA,CAAE;eAC7D,gBAAgB;AAEvB,YAAI;AACF,gBAAM,cAAc,MAAM,SAAS,QAAQ;YACzC,QAAQ;YACR,QAAQ,CAAC,EAAE,cAAc,CAAA,EAAE,CAAE;WAC9B;AACD,qBAAY,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,OAAoB,IAC5D,CAAC,MAAM,WAAW,CAAC,CAAC;AAKtB,cAAI,SAAS,SAAS,GAAG;AACvB,kBAAM,iBAAiB,MAAM,KAAK,YAAW;AAC7C,uBAAW;UACb;QACF,SAAS,KAAK;AACZ,gBAAM,QAAQ;AAGd,cAAI,MAAM,SAAS,yBAAyB;AAC1C,kBAAM,IAAI,yBAAyB,KAAK;AAE1C,cAAI,MAAM,SAAS,4BAA4B;AAAM,kBAAM;QAC7D;MACF;AAEA,UAAI;AACF,YAAI,CAAC,UAAU,UAAU,CAAC,gBAAgB;AACxC,gBAAM,oBAAoB,MAAM,SAAS,QAAQ;YAC/C,QAAQ;WACT;AACD,qBAAW,kBAAkB,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;QACvD;AAIA,YAAIA,UAAS;AACX,mBAAS,eAAe,WAAWA,QAAO;AAC1C,UAAAA,WAAU;QACZ;AACA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAACC,aAAY;AACf,UAAAA,cAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAcA,WAAU;QACtC;AAGA,YAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,YAAI,WAAW,mBAAmB,SAAS;AACzC,gBAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE,EAAE,MAAM,CAAC,UAAS;AACjE,gBAAI,MAAM,SAAS,yBAAyB;AAAM,oBAAM;AACxD,mBAAO,EAAE,IAAI,eAAc;UAC7B,CAAC;AACD,2BAAiB,OAAO,MAAM;QAChC;AAGA,YAAI;AACF,gBAAM,OAAO,SAAS,WAAW,GAAG,KAAK,EAAE,eAAe;AAG5D,YAAI,CAAC,WAAW;AACd,gBAAM,OAAO,SAAS,QAAQ,sBAAsB,IAAI;AAE1D,eAAO,EAAE,UAAU,SAAS,eAAc;MAC5C,SAAS,KAAK;AACZ,cAAM,QAAQ;AACd,YAAI,MAAM,SAAS,yBAAyB;AAC1C,gBAAM,IAAI,yBAAyB,KAAK;AAC1C,YAAI,MAAM,SAAS,4BAA4B;AAC7C,gBAAM,IAAI,4BAA4B,KAAK;AAC7C,cAAM;MACR;IACF;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAG9C,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAIA,aAAY;AACd,iBAAS,eAAe,cAAcA,WAAU;AAChD,QAAAA,cAAa;MACf;AACA,UAAI,CAACD,UAAS;AACZ,QAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,iBAAS,GAAG,WAAWA,QAAO;MAChC;AAIA,UAAI;AAGF,cAAM,YACJ;;UAEE,SAAS,QAIN;;YAED,QAAQ;YACR,QAAQ,CAAC,EAAE,cAAc,CAAA,EAAE,CAAE;WAC9B;WACH,EAAE,SAAS,IAAG,CAAE;MAEpB,QAAQ;MAAC;AAGT,UAAI,gBAAgB;AAClB,cAAM,OAAO,SAAS,QAAQ,GAAG,KAAK,EAAE,iBAAiB,IAAI;MAC/D;AAEA,UAAI,CAAC,WAAW;AACd,cAAM,OAAO,SAAS,WAAW,oBAAoB;IACzD;IACA,MAAM,cAAW;AACf,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAC9C,YAAM,WAAW,MAAM,SAAS,QAAQ,EAAE,QAAQ,eAAc,CAAE;AAClE,aAAO,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC1C;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAC9C,YAAM,aAAa,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE;AACnE,aAAO,OAAO,UAAU;IAC1B;IACA,MAAM,cAAW;AACf,UAAI,OAAO,WAAW;AAAa,eAAO;AAE1C,UAAI;AACJ,YAAM,SAAS,UAAS;AACxB,UAAI,OAAO,OAAO,aAAa;AAC7B,mBAAW,OAAO,SAAS,MAA4B;eAChD,OAAO,OAAO,aAAa;AAClC,mBAAW,aAAa,QAAQ,OAAO,QAAQ;;AAC5C,mBAAW,OAAO;AAIvB,UAAI,YAAY,CAAC,SAAS,gBAAgB;AAExC,YAAI,SAAS,YAAY,OAAO,SAAS,QAAQ;AAC/C,mBAAS,iBACP,SAAS;;AACR,mBAAS,iBAAiB,MAAK;UAAE;MACxC;AAEA,aAAO;IACT;IACA,MAAM,eAAY;AAChB,UAAI;AACF,cAAM,iBACJ;QAEC,MAAM,OAAO,SAAS,QAAQ,GAAG,KAAK,EAAE,eAAe;AAC1D,YAAI;AAAgB,iBAAO;AAK3B,YAAI,CAAC,WAAW,QAAQ;AACtB,gBAAM,YAAY,MAAM,OAAO,SAAS,QAAQ,oBAAoB;AACpE,cAAI,CAAC;AAAW,mBAAO;QACzB;AAEA,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAI,CAAC,UAAU;AACb,cACE,6BAA6B,UAC7B,6BAA6B,OAC7B;AAIA,kBAAM,iBAAiB,YAAW;AAChC,kBAAI,OAAO,WAAW;AACpB,uBAAO,oBACL,wBACA,cAAc;AAElB,oBAAME,YAAW,MAAM,KAAK,YAAW;AACvC,qBAAO,CAAC,CAACA;YACX;AACA,kBAAM,UACJ,OAAO,6BAA6B,WAChC,2BACA;AACN,kBAAM,MAAM,MAAM,QAAQ,KAAK;cAC7B,GAAI,OAAO,WAAW,cAClB;gBACE,IAAI,QAAiB,CAAC,YACpB,OAAO,iBACL,wBACA,MAAM,QAAQ,eAAc,CAAE,GAC9B,EAAE,MAAM,KAAI,CAAE,CACf;kBAGL,CAAA;cACJ,IAAI,QAAiB,CAAC,YACpB,WAAW,MAAM,QAAQ,eAAc,CAAE,GAAG,OAAO,CAAC;aAEvD;AACD,gBAAI;AAAK,qBAAO;UAClB;AAEA,gBAAM,IAAI,sBAAqB;QACjC;AAIA,cAAM,WAAW,MAAM,UAAU,MAAM,KAAK,YAAW,CAAE;AACzD,eAAO,CAAC,CAAC,SAAS;MACpB,QAAQ;AACN,eAAO;MACT;IACF;IACA,MAAM,YAAY,EAAE,2BAA2B,QAAO,GAAE;AACtD,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,UAAU,IAAI,QAAc,CAAC,YAAW;AAC5C,cAAM,WAAY,CAAC,SAAQ;AACzB,cAAI,aAAa,QAAQ,KAAK,YAAY,SAAS;AACjD,mBAAO,QAAQ,IAAI,UAAU,QAAQ;AACrC,oBAAO;UACT;QACF;AACA,eAAO,QAAQ,GAAG,UAAU,QAAQ;MACtC,CAAC;AAED,UAAI;AACF,cAAM,QAAQ,IAAI;UAChB,SACG,QAAQ;YACP,QAAQ;YACR,QAAQ,CAAC,EAAE,SAAS,YAAY,OAAO,EAAC,CAAE;WAC3C,EAMA,KAAK,YAAW;AACf,kBAAM,iBAAiB,MAAM,KAAK,WAAU;AAC5C,gBAAI,mBAAmB;AACrB,qBAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;UAC7C,CAAC;UACH;SACD;AACD,eAAO;MACT,SAAS,KAAK;AACZ,cAAM,QAAQ;AAGd,YACE,MAAM,SAAS;;QAGd,OACG,MAAM,eAAe,SAAS,MAClC;AACA,cAAI;AACF,kBAAM,EAAE,SAAS,eAAe,GAAG,eAAc,IAC/C,MAAM,kBAAkB,CAAA;AAC1B,gBAAI;AACJ,gBAAI,2BAA2B;AAC7B,kCAAoB,0BAA0B;qBACvC;AACP,kCAAoB;gBAClB,cAAc;gBACd,GAAG,OAAO,OAAO,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG;;AAGrD,gBAAI;AACJ,gBAAI,2BAA2B,SAAS;AACtC,wBAAU,0BAA0B;;AACjC,wBAAU,CAAC,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,EAAE;AAEpD,kBAAM,mBAAmB;cACvB;cACA,SAAS,YAAY,OAAO;cAC5B,WAAW,2BAA2B,aAAa,MAAM;cACzD,UAAU,2BAA2B;cACrC,gBACE,2BAA2B,kBAC3B,MAAM;cACR;;AAGF,kBAAM,QAAQ,IAAI;cAChB,SACG,QAAQ;gBACP,QAAQ;gBACR,QAAQ,CAAC,gBAAgB;eAC1B,EACA,KAAK,YAAW;AACf,sBAAM,iBAAiB,MAAM,KAAK,WAAU;AAC5C,oBAAI,mBAAmB;AACrB,yBAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;;AAEzC,wBAAM,IAAI,yBACR,IAAI,MAAM,4CAA4C,CAAC;cAE7D,CAAC;cACH;aACD;AAED,mBAAO;UACT,SAASC,QAAO;AACd,kBAAM,IAAI,yBAAyBA,MAAc;UACnD;QACF;AAEA,YAAI,MAAM,SAAS,yBAAyB;AAC1C,gBAAM,IAAI,yBAAyB,KAAK;AAC1C,cAAM,IAAI,iBAAiB,KAAK;MAClC;IACF;IACA,MAAM,kBAAkB,UAAQ;AAE9B,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;eAEnC,OAAO,QAAQ,cAAc,SAAS,GAAG;AAChD,cAAM,WAAW,MAAM,KAAK,WAAU,GAAI,SAAQ;AAClD,aAAK,UAAU,EAAE,QAAO,CAAE;AAE1B,YAAI;AACF,gBAAM,OAAO,SAAS,WAAW,GAAG,KAAK,EAAE,eAAe;MAC9D;AAGE,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,UAAU,aAAW;AACzB,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,SAAS,WAAW;AAAG;AAE3B,YAAM,UAAU,OAAO,YAAY,OAAO;AAC1C,aAAO,QAAQ,KAAK,WAAW,EAAE,UAAU,QAAO,CAAE;AAGpD,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,UAAU;AACZ,YAAIH,UAAS;AACX,mBAAS,eAAe,WAAWA,QAAO;AAC1C,UAAAA,WAAU;QACZ;AACA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAACC,aAAY;AACf,UAAAA,cAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAcA,WAAU;QACtC;MACF;IACF;IACA,MAAM,aAAa,OAAK;AACtB,YAAM,WAAW,MAAM,KAAK,YAAW;AAIvC,UAAI,SAAU,MAAyB,SAAS,MAAM;AACpD,YAAI,YAAY,CAAC,EAAE,MAAM,KAAK,YAAW,GAAI;AAAQ;MACvD;AAKA,aAAO,QAAQ,KAAK,YAAY;AAGhC,UAAI,UAAU;AACZ,YAAI,cAAc;AAChB,mBAAS,eAAe,gBAAgB,YAAY;AACpD,yBAAe;QACjB;AACA,YAAIA,aAAY;AACd,mBAAS,eAAe,cAAcA,WAAU;AAChD,UAAAA,cAAa;QACf;AACA,YAAI,CAACD,UAAS;AACZ,UAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,mBAAS,GAAG,WAAWA,QAAO;QAChC;MACF;IACF;IACA;AACJ;AAEA,IAAM,YAAY;EAChB,gBAAgB;IACd,IAAI;IACJ,MAAM;IACN,SAASD,SAAM;AACb,UAAIA,SAAQ;AAAyB,eAAOA,QAAO;AACnD,aAAO,aAAaA,SAAQ,kBAAkB;IAChD;;EAEF,UAAU;IACR,IAAI;IACJ,MAAM;IACN,SAASA,SAAM;AACb,aAAO,aAAaA,SAAQ,CAAC,aAAY;AACvC,YAAI,CAAC,SAAS;AAAY,iBAAO;AAGjC,YAAI,SAAS,iBAAiB,CAAC,SAAS,WAAW,CAAC,SAAS;AAC3D,iBAAO;AAET,cAAM,QAAQ;UACZ;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;AAEF,mBAAW,QAAQ;AAAO,cAAI,SAAS,IAAI;AAAG,mBAAO;AACrD,eAAO;MACT,CAAC;IACH;;EAEF,SAAS;IACP,IAAI;IACJ,MAAM;IACN,SAASA,SAAM;AACb,UAAIA,SAAQ,SAAS;AAAU,eAAOA,QAAO,SAAS;AACtD,aAAO,aAAaA,SAAQ,WAAW;IACzC;;;AA4FJ,SAAS,aACPA,SACA,QAAsE;AAEtE,WAAS,WAAW,UAAwB;AAC1C,QAAI,OAAO,WAAW;AAAY,aAAO,OAAO,QAAQ;AACxD,QAAI,OAAO,WAAW;AAAU,aAAO,SAAS,MAAM;AACtD,WAAO;EACT;AAEA,QAAM,WAAYA,QAAkB;AACpC,MAAI,UAAU;AACZ,WAAO,SAAS,UAAU,KAAK,CAAC,aAAa,WAAW,QAAQ,CAAC;AACnE,MAAI,YAAY,WAAW,QAAQ;AAAG,WAAO;AAC7C,SAAO;AACT;;;AC/oBA,KAAK,OAAO;AACN,SAAU,KAAK,YAA0B;AAC7C,QAAM,mBAAmB,oBAAI,IAAG;AAChC,QAAM,WACJ,WAAW,YACV,EAAE,kBAAkB,MAAK;AAe5B,MAAI,YAAY,SAAS;AACzB,MAAI;AAEJ,SAAO,gBAAsC,CAAC,YAAY;IACxD,IAAI;IACJ,MAAM;IACN,MAAM,KAAK;IACX,MAAM,QAAK;AACT,yBAAmB,OAAO,OAAO,CAAC,EAAE;IACtC;IACA,MAAM,QAAQ,EAAE,QAAO,IAAK,CAAA,GAAE;AAC5B,UAAI,SAAS,cAAc;AACzB,YAAI,OAAO,SAAS,iBAAiB;AACnC,gBAAM,IAAI,yBAAyB,IAAI,MAAM,oBAAoB,CAAC;AACpE,cAAM,SAAS;MACjB;AAEA,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,WAAW,MAAM,SAAS,QAAQ;QACtC,QAAQ;OACT;AAED,UAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,UAAI,WAAW,mBAAmB,SAAS;AACzC,cAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE;AACjD,yBAAiB,MAAM;MACzB;AAEA,kBAAY;AAEZ,aAAO;QACL,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;QAC3C,SAAS;;IAEb;IACA,MAAM,aAAU;AACd,kBAAY;IACd;IACA,MAAM,cAAW;AACf,UAAI,CAAC;AAAW,cAAM,IAAI,2BAA0B;AACpD,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,WAAW,MAAM,SAAS,QAAQ,EAAE,QAAQ,eAAc,CAAE;AAClE,aAAO,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC1C;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,aAAa,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE;AACnE,aAAO,QAAQ,YAAY,QAAQ;IACrC;IACA,MAAM,eAAY;AAChB,UAAI,CAAC,SAAS;AAAW,eAAO;AAChC,UAAI,CAAC;AAAW,eAAO;AACvB,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,aAAO,CAAC,CAAC,SAAS;IACpB;IACA,MAAM,YAAY,EAAE,QAAO,GAAE;AAC3B,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,SAAS,QAAQ;QACrB,QAAQ;QACR,QAAQ,CAAC,EAAE,SAAS,YAAY,OAAO,EAAC,CAAE;OAC3C;AACD,aAAO;IACT;IACA,kBAAkB,UAAQ;AACxB,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;;AAE1C,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,aAAa,QAAM;AACvB,aAAO,QAAQ,KAAK,YAAY;AAChC,kBAAY;IACd;IACA,MAAM,YAAY,EAAE,QAAO,IAAK,CAAA,GAAE;AAChC,YAAM,QACJ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK,OAAO,OAAO,CAAC;AAChE,YAAM,MAAM,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAExC,YAAM,UAA4B,OAAO,EAAE,QAAQ,OAAM,MAAM;AAE7D,YAAI,WAAW;AAAe,iBAAO,YAAY,gBAAgB;AACjE,YAAI,WAAW;AAAuB,iBAAO,WAAW;AACxD,YAAI,WAAW;AACb,cAAI,SAAS,oBAAoB;AAC/B,gBAAI,OAAO,SAAS,uBAAuB;AACzC,oBAAM,IAAI,yBACR,IAAI,MAAM,4BAA4B,CAAC;AAE3C,kBAAM,SAAS;UACjB;;AAGF,YAAI,WAAW,8BAA8B;AAC3C,cAAI,SAAS,kBAAkB;AAC7B,gBAAI,OAAO,SAAS,qBAAqB;AACvC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AAEA,6BAAmB,QAAS,OAAkB,CAAC,EAAE,SAAS,QAAQ;AAClE,eAAK,eAAe,iBAAiB,SAAQ,CAAE;AAC/C;QACF;AAEA,YAAI,WAAW,qBAAqB;AAClC,cAAI,SAAS,iBAAiB;AAC5B,gBAAI,OAAO,SAAS,oBAAoB;AACtC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AACA,iBAAO;QACT;AAEA,YAAI,WAAW;AACb,iBAAO;YACL,UAAU;cACR,kBAAkB;gBAChB,WACG,OAAiB,CAAC,MACnB;;cAEJ,aAAa;gBACX,WAAW;;;YAGf,WAAW;cACT,kBAAkB;gBAChB,WACG,OAAiB,CAAC,MACnB;;;;AAKV,YAAI,WAAW,oBAAoB;AACjC,gBAAM,SAAS,CAAA;AACf,gBAAM,QAAS,OAAe,CAAC,EAAE;AACjC,gBAAM,OAAQ,OAAe,CAAC,EAAE;AAChC,qBAAWK,SAAQ,OAAO;AACxB,kBAAM,EAAE,QAAAC,SAAQ,OAAAC,OAAK,IAAK,MAAM,IAAI,KAAK,KAAK;cAC5C,MAAM;gBACJ,QAAQ;gBACR,QAAQ;kBACN;oBACE,GAAGF;oBACH,GAAI,OAAO,SAAS,cAAc,EAAE,KAAI,IAAK,CAAA;;;;aAIpD;AACD,gBAAIE;AACF,oBAAM,IAAI,gBAAgB;gBACxB,MAAM,EAAE,QAAQ,OAAM;gBACtB,OAAAA;gBACA;eACD;AACH,mBAAO,KAAKD,OAAM;UACpB;AACA,gBAAM,KAAK,UAAU,YAAY,KAAK,UAAU,KAAK,CAAC,CAAC;AACvD,2BAAiB,IAAI,IAAI,MAAM;AAC/B,iBAAO,EAAE,GAAE;QACb;AAEA,YAAI,WAAW,yBAAyB;AACtC,gBAAM,SAAS,iBAAiB,IAAK,OAAe,CAAC,CAAC;AACtD,cAAI,CAAC;AACH,mBAAO;cACL,QAAQ;cACR,SAAS;cACT,IAAK,OAAe,CAAC;cACrB,QAAQ;cACR,UAAU,CAAA;cACV,SAAS;;AAGb,gBAAM,WAAW,MAAM,QAAQ,IAC7B,OAAO,IAAI,OAAO,SAAQ;AACxB,kBAAM,EAAE,QAAAA,SAAQ,OAAAC,OAAK,IAAK,MAAM,IAAI,KAAK,KAAK;cAC5C,MAAM;gBACJ,QAAQ;gBACR,QAAQ,CAAC,IAAI;gBACb,IAAI;;aAEP;AACD,gBAAIA;AACF,oBAAM,IAAI,gBAAgB;gBACxB,MAAM,EAAE,QAAQ,OAAM;gBACtB,OAAAA;gBACA;eACD;AACH,gBAAI,CAACD;AAAQ,qBAAO;AACpB,mBAAO;cACL,WAAWA,QAAO;cAClB,aAAaA,QAAO;cACpB,SAASA,QAAO;cAChB,MAAMA,QAAO;cACb,QAAQA,QAAO;cACf,iBAAiBA,QAAO;;UAE5B,CAAC,CAAC;AAEJ,gBAAM,YAAY,SAAS,OAAO,CAAC,MAAM,MAAM,IAAI;AACnD,cAAI,UAAU,WAAW;AACvB,mBAAO;cACL,QAAQ;cACR,SAAS;cACT,IAAK,OAAe,CAAC;cACrB,QAAQ;cACR,UAAU,CAAA;cACV,SAAS;;AAEb,iBAAO;YACL,QAAQ;YACR,SAAS;YACT,IAAK,OAAe,CAAC;YACrB,QAAQ;YACR,UAAU;YACV,SAAS;;QAEb;AAEA,YAAI,WAAW;AAA0B;AAGzC,YAAI,WAAW,iBAAiB;AAC9B,cAAI,SAAS,kBAAkB;AAC7B,gBAAI,OAAO,SAAS,qBAAqB;AACvC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AAEA,mBAAS;AAET,mBAAS,CAAE,OAAkB,CAAC,GAAI,OAAkB,CAAC,CAAC;QACxD;AAEA,cAAM,OAAO,EAAE,QAAQ,OAAM;AAC7B,cAAM,EAAE,OAAO,OAAM,IAAK,MAAM,IAAI,KAAK,KAAK,EAAE,KAAI,CAAE;AACtD,YAAI;AAAO,gBAAM,IAAI,gBAAgB,EAAE,MAAM,OAAO,IAAG,CAAE;AAEzD,eAAO;MACT;AACA,aAAO,OAAO,EAAE,QAAO,CAAE,EAAE,EAAE,YAAY,EAAC,CAAE;IAC9C;IACA;AACJ;;;ACpRM,SAAU,iBACd,UAAoC;AAEpC,MAAI,OAAO,WAAW;AAAa;AACnC,QAAM,UAAU,CAAC,UACf,SAAS,MAAM,MAAM;AAEvB,SAAO,iBAAiB,4BAA4B,OAAO;AAE3D,SAAO,cAAc,IAAI,YAAY,yBAAyB,CAAC;AAE/D,SAAO,MAAM,OAAO,oBAAoB,4BAA4B,OAAO;AAC7E;;;ACNM,SAAU,cAAW;AACzB,QAAM,YAA2B,oBAAI,IAAG;AACxC,MAAI,kBAAoD,CAAA;AAExD,QAAM,UAAU,MACd,iBAAiB,CAAC,mBAAkB;AAClC,QACE,gBAAgB,KACd,CAAC,EAAE,KAAI,MAAO,KAAK,SAAS,eAAe,KAAK,IAAI;AAGtD;AAEF,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AACrD,cAAU,QAAQ,CAAC,aACjB,SAAS,iBAAiB,EAAE,OAAO,CAAC,cAAc,EAAC,CAAE,CAAC;EAE1D,CAAC;AACH,MAAI,UAAU,QAAO;AAErB,SAAO;IACL,aAAU;AACR,aAAO;IACT;IACA,QAAK;AACH,gBAAU,QAAQ,CAAC,aACjB,SAAS,CAAA,GAAI,EAAE,SAAS,CAAC,GAAG,eAAe,EAAC,CAAE,CAAC;AAEjD,wBAAkB,CAAA;IACpB;IACA,UAAO;AACL,WAAK,MAAK;AACV,gBAAU,MAAK;AACf,gBAAS;IACX;IACA,aAAa,EAAE,KAAI,GAAE;AACnB,aAAO,gBAAgB,KACrB,CAAC,mBAAmB,eAAe,KAAK,SAAS,IAAI;IAEzD;IACA,eAAY;AACV,aAAO;IACT;IACA,QAAK;AACH,WAAK,MAAK;AACV,gBAAS;AACT,gBAAU,QAAO;IACnB;IACA,UAAU,UAAU,EAAE,gBAAe,IAAK,CAAA,GAAE;AAC1C,gBAAU,IAAI,QAAQ;AACtB,UAAI;AAAiB,iBAAS,iBAAiB,EAAE,OAAO,gBAAe,CAAE;AACzE,aAAO,MAAM,UAAU,OAAO,QAAQ;IACxC;;AAEJ;;;AC+GA,IAAM,4BAA4B,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;AAC3D,QAAM,gBAAgB,IAAI;AAC1B,MAAI,YAAY,CAAC,UAAU,aAAa,YAAY;AAClD,QAAI,WAAW;AACf,QAAI,aAAa;AACf,YAAM,cAAc,WAAW,OAAO,SAAS,QAAQ,eAAe,OAAO;AAC7E,UAAI,eAAe,SAAS,IAAI,SAAS,CAAC;AAC1C,iBAAW,CAAC,UAAU;AACpB,cAAM,YAAY,SAAS,KAAK;AAChC,YAAI,CAAC,WAAW,cAAc,SAAS,GAAG;AACxC,gBAAM,gBAAgB;AACtB,sBAAY,eAAe,WAAW,aAAa;AAAA,QACrD;AAAA,MACF;AACA,UAAI,WAAW,OAAO,SAAS,QAAQ,iBAAiB;AACtD,oBAAY,cAAc,YAAY;AAAA,MACxC;AAAA,IACF;AACA,WAAO,cAAc,QAAQ;AAAA,EAC/B;AACA,QAAM,eAAe,GAAG,KAAK,KAAK,GAAG;AACrC,SAAO;AACT;AACA,IAAM,wBAAwB;AAI9B,SAAS,kBAAkB,YAAY,SAAS;AAC9C,MAAI;AACJ,MAAI;AACF,cAAU,WAAW;AAAA,EACvB,SAAS,GAAG;AACV;AAAA,EACF;AACA,QAAM,iBAAiB;AAAA,IACrB,SAAS,CAAC,SAAS;AACjB,UAAI;AACJ,YAAM,QAAQ,CAAC,SAAS;AACtB,YAAI,SAAS,MAAM;AACjB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,MAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO;AAAA,MACpE;AACA,YAAM,OAAO,KAAK,QAAQ,QAAQ,IAAI,MAAM,OAAO,KAAK;AACxD,UAAI,eAAe,SAAS;AAC1B,eAAO,IAAI,KAAK,KAAK;AAAA,MACvB;AACA,aAAO,MAAM,GAAG;AAAA,IAClB;AAAA,IACA,SAAS,CAAC,MAAM,aAAa,QAAQ;AAAA,MACnC;AAAA,MACA,KAAK,UAAU,UAAU,WAAW,OAAO,SAAS,QAAQ,QAAQ;AAAA,IACtE;AAAA,IACA,YAAY,CAAC,SAAS,QAAQ,WAAW,IAAI;AAAA,EAC/C;AACA,SAAO;AACT;AACA,IAAM,aAAa,CAAC,OAAO,CAAC,UAAU;AACpC,MAAI;AACF,UAAM,SAAS,GAAG,KAAK;AACvB,QAAI,kBAAkB,SAAS;AAC7B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,KAAK,aAAa;AAChB,eAAO,WAAW,WAAW,EAAE,MAAM;AAAA,MACvC;AAAA,MACA,MAAM,aAAa;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,WAAO;AAAA,MACL,KAAK,cAAc;AACjB,eAAO;AAAA,MACT;AAAA,MACA,MAAM,YAAY;AAChB,eAAO,WAAW,UAAU,EAAE,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,cAAc,CAAC,QAAQ,gBAAgB,CAAC,KAAK,KAAK,QAAQ;AAC9D,MAAI,UAAU;AAAA,IACZ,SAAS,kBAAkB,MAAM,YAAY;AAAA,IAC7C,YAAY,CAAC,UAAU;AAAA,IACvB,SAAS;AAAA,IACT,OAAO,CAAC,gBAAgB,kBAAkB;AAAA,MACxC,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,GAAG;AAAA,EACL;AACA,MAAI,cAAc;AAClB,QAAM,qBAAqC,oBAAI,IAAI;AACnD,QAAM,2BAA2C,oBAAI,IAAI;AACzD,MAAI,UAAU,QAAQ;AACtB,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,MACL,IAAI,SAAS;AACX,gBAAQ;AAAA,UACN,uDAAuD,QAAQ,IAAI;AAAA,QACrE;AACA,YAAI,GAAG,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,QAAQ,WAAW,EAAE,GAAG,IAAI,EAAE,CAAC;AAC7C,WAAO,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACnC;AAAA,MACA,SAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,IAAI;AAC1B,MAAI,WAAW,CAAC,OAAO,YAAY;AACjC,kBAAc,OAAO,OAAO;AAC5B,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,eAAe;AAAA,IACnB,IAAI,SAAS;AACX,UAAI,GAAG,IAAI;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,kBAAkB,MAAM;AAC5B,MAAI;AACJ,QAAME,WAAU,MAAM;AACpB,QAAI,IAAI;AACR,QAAI,CAAC,QAAS;AACd,kBAAc;AACd,uBAAmB,QAAQ,CAAC,OAAO;AACjC,UAAI;AACJ,aAAO,IAAI,MAAM,IAAI,MAAM,OAAO,MAAM,YAAY;AAAA,IACtD,CAAC;AACD,UAAM,4BAA4B,KAAK,QAAQ,uBAAuB,OAAO,SAAS,GAAG,KAAK,UAAU,KAAK,IAAI,MAAM,OAAO,KAAK,YAAY,MAAM;AACrJ,WAAO,WAAW,QAAQ,QAAQ,KAAK,OAAO,CAAC,EAAE,QAAQ,IAAI,EAAE,KAAK,CAAC,6BAA6B;AAChG,UAAI,0BAA0B;AAC5B,YAAI,OAAO,yBAAyB,YAAY,YAAY,yBAAyB,YAAY,QAAQ,SAAS;AAChH,cAAI,QAAQ,SAAS;AACnB,mBAAO;AAAA,cACL;AAAA,cACA,QAAQ;AAAA,gBACN,yBAAyB;AAAA,gBACzB,yBAAyB;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA,kBAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO,CAAC,OAAO,yBAAyB,KAAK;AAAA,QAC/C;AAAA,MACF;AACA,aAAO,CAAC,OAAO,MAAM;AAAA,IACvB,CAAC,EAAE,KAAK,CAAC,oBAAoB;AAC3B,UAAI;AACJ,YAAM,CAAC,UAAU,aAAa,IAAI;AAClC,yBAAmB,QAAQ;AAAA,QACzB;AAAA,SACC,MAAM,IAAI,MAAM,OAAO,MAAM;AAAA,MAChC;AACA,UAAI,kBAAkB,IAAI;AAC1B,UAAI,UAAU;AACZ,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC,EAAE,KAAK,MAAM;AACZ,iCAA2B,OAAO,SAAS,wBAAwB,kBAAkB,MAAM;AAC3F,yBAAmB,IAAI;AACvB,oBAAc;AACd,+BAAyB,QAAQ,CAAC,OAAO,GAAG,gBAAgB,CAAC;AAAA,IAC/D,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,iCAA2B,OAAO,SAAS,wBAAwB,QAAQ,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AACA,MAAI,UAAU;AAAA,IACZ,YAAY,CAAC,eAAe;AAC1B,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,UAAI,WAAW,SAAS;AACtB,kBAAU,WAAW;AAAA,MACvB;AAAA,IACF;AAAA,IACA,cAAc,MAAM;AAClB,iBAAW,OAAO,SAAS,QAAQ,WAAW,QAAQ,IAAI;AAAA,IAC5D;AAAA,IACA,YAAY,MAAM;AAAA,IAClB,WAAW,MAAMA,SAAQ;AAAA,IACzB,aAAa,MAAM;AAAA,IACnB,WAAW,CAAC,OAAO;AACjB,yBAAmB,IAAI,EAAE;AACzB,aAAO,MAAM;AACX,2BAAmB,OAAO,EAAE;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,OAAO;AACzB,+BAAyB,IAAI,EAAE;AAC/B,aAAO,MAAM;AACX,iCAAyB,OAAO,EAAE;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,eAAe;AAC1B,IAAAA,SAAQ;AAAA,EACV;AACA,SAAO,oBAAoB;AAC7B;AACA,IAAM,UAAU;;;AC7ahB,IAAM,kBAAkB,CAAC,gBAAgB;AACvC,MAAI;AACJ,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,WAAW,CAAC,SAAS,YAAY;AACrC,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,CAAC,OAAO,GAAG,WAAW,KAAK,GAAG;AAChC,YAAM,gBAAgB;AACtB,eAAS,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,SAAS;AAC1I,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,kBAAkB,MAAM;AAC9B,QAAM,YAAY,CAAC,aAAa;AAC9B,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,UAAU,OAAO,QAAQ;AAAA,EACxC;AACA,QAAM,MAAM,EAAE,UAAU,UAAU,iBAAiB,UAAU;AAC7D,QAAM,eAAe,QAAQ,YAAY,UAAU,UAAU,GAAG;AAChE,SAAO;AACT;AACA,IAAMC,eAAc,CAAC,gBAAgB,cAAc,gBAAgB,WAAW,IAAI;;;ACP5E,IAAO,UAAP,MAAc;EAGlB,YAAmBC,MAAW;AAAlB,WAAA,eAAA,MAAA,OAAA;;;;aAAOA;;AAFnB,WAAA,eAAA,MAAA,YAAA;;;;aAAW,IAAI,aAAAC,QAAY;;EAEM;EAEjC,GACE,WACA,IAIC;AAED,SAAK,SAAS,GAAG,WAAW,EAAa;EAC3C;EAEA,KACE,WACA,IAIC;AAED,SAAK,SAAS,KAAK,WAAW,EAAa;EAC7C;EAEA,IACE,WACA,IAIC;AAED,SAAK,SAAS,IAAI,WAAW,EAAa;EAC5C;EAEA,KACE,cACG,QAAkE;AAErE,UAAM,OAAO,OAAO,CAAC;AACrB,SAAK,SAAS,KAAK,WAAW,EAAE,KAAK,KAAK,KAAK,GAAG,KAAI,CAAE;EAC1D;EAEA,cAA8C,WAAc;AAC1D,WAAO,KAAK,SAAS,cAAc,SAAS;EAC9C;;AAGI,SAAU,cAAyCD,MAAW;AAClE,SAAO,IAAI,QAAkBA,IAAG;AAClC;;;ACjEM,SAAU,YAAkB,OAAe,SAAiB;AAChE,SAAO,KAAK,MAAM,OAAO,CAAC,KAAK,WAAU;AACvC,QAAIE,SAAQ;AACZ,QAAIA,QAAO,WAAW;AAAU,MAAAA,SAAQ,OAAOA,OAAM,KAAK;AAC1D,QAAIA,QAAO,WAAW;AAAO,MAAAA,SAAQ,IAAI,IAAIA,OAAM,KAAK;AACxD,WAAO,UAAU,KAAKA,MAAK,KAAKA;EAClC,CAAC;AACH;;;ACFA,SAAS,gBAAgB,MAAgB,QAAc;AACrD,SAAO,KAAK,MAAM,GAAG,MAAM,EAAE,KAAK,GAAG,KAAK;AAC5C;AASA,SAAS,UAAU,OAAc,OAAU;AACzC,QAAM,EAAE,OAAM,IAAK;AAEnB,WAASC,SAAQ,GAAGA,SAAQ,QAAQ,EAAEA,QAAO;AAC3C,QAAI,MAAMA,MAAK,MAAM,OAAO;AAC1B,aAAOA,SAAQ;IACjB;EACF;AAEA,SAAO;AACT;AAYA,SAAS,eACP,UACA,kBAAsD;AAEtD,QAAM,cAAc,OAAO,aAAa;AACxC,QAAM,sBAAsB,OAAO,qBAAqB;AAExD,QAAM,QAAe,CAAA;AACrB,QAAM,OAAiB,CAAA;AAEvB,SAAO,SAAS,QAAmB,KAAa,OAAU;AACxD,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,MAAM,QAAQ;AAChB,cAAM,aAAa,UAAU,OAAO,IAAI;AAExC,YAAI,eAAe,GAAG;AACpB,gBAAM,MAAM,MAAM,IAAI;QACxB,OAAO;AACL,gBAAM,OAAO,UAAU;AACvB,eAAK,OAAO,UAAU;QACxB;AAEA,aAAK,KAAK,MAAM,IAAI;AAEpB,cAAM,cAAc,UAAU,OAAO,KAAK;AAE1C,YAAI,gBAAgB,GAAG;AACrB,iBAAO,sBACH,iBAAiB,KACf,MACA,KACA,OACA,gBAAgB,MAAM,WAAW,CAAC,IAEpC,QAAQ,gBAAgB,MAAM,WAAW,CAAC;QAChD;MACF,OAAO;AACL,cAAM,CAAC,IAAI;AACX,aAAK,CAAC,IAAI;MACZ;IACF;AAEA,WAAO,cAAc,SAAS,KAAK,MAAM,KAAK,KAAK,IAAI;EACzD;AACF;AAaM,SAAU,UACd,OACA,UACA,QACA,kBAAsD;AAEtD,SAAO,KAAK,UACV,OACA,eAAe,CAAC,KAAK,WAAU;AAC7B,QAAIC,SAAQ;AACZ,QAAI,OAAOA,WAAU;AACnB,MAAAA,SAAQ,EAAE,QAAQ,UAAU,OAAO,OAAO,SAAQ,EAAE;AACtD,QAAIA,kBAAiB;AACnB,MAAAA,SAAQ,EAAE,QAAQ,OAAO,OAAO,MAAM,KAAK,OAAO,QAAO,CAAE,EAAC;AAC9D,WAAO,WAAW,KAAKA,MAAK,KAAKA;EACnC,GAAG,gBAAgB,GACnB,UAAU,MAAS;AAEvB;;;AClEM,SAAU,cAGd,YAAmC;AACnC,QAAM,EACJ,aAAAC,eAAc,aACd,KAAK,SAAS,SACd,WAAAC,aAAY,WACZ,UAAU,YAAW,IACnB;AAEJ,WAAS,OAAa,OAAW;AAC/B,QAAI,iBAAiB;AAAS,aAAO,MAAM,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,MAAM,IAAI;AAC1E,WAAO;EACT;AAEA,SAAO;IACL,GAAG;IACH,KAAK;IACL,MAAM,QAAQ,KAAK,cAAY;AAC7B,YAAM,QAAQ,QAAQ,QAAQ,GAAG,MAAM,IAAI,GAAa,EAAE;AAC1D,YAAM,YAAY,MAAM,OAAO,KAAK;AACpC,UAAI;AAAW,eAAOD,aAAY,SAAS,KAAK;AAChD,aAAQ,gBAAgB;IAC1B;IACA,MAAM,QAAQ,KAAK,OAAK;AACtB,YAAM,aAAa,GAAG,MAAM,IAAI,GAAa;AAC7C,UAAI,UAAU;AAAM,cAAM,OAAO,QAAQ,WAAW,UAAU,CAAC;;AAC1D,cAAM,OAAO,QAAQ,QAAQ,YAAYC,WAAU,KAAK,CAAC,CAAC;IACjE;IACA,MAAM,WAAW,KAAG;AAClB,YAAM,OAAO,QAAQ,WAAW,GAAG,MAAM,IAAI,GAAa,EAAE,CAAC;IAC/D;;AAEJ;AAEO,IAAM,cAAc;EACzB,SAAS,MAAM;EACf,SAAS,MAAK;EAAE;EAChB,YAAY,MAAK;EAAE;;AAGf,SAAU,oBAAiB;AAC/B,QAAM,WAAW,MAAK;AACpB,QAAI,OAAO,WAAW,eAAe,OAAO;AAC1C,aAAO,OAAO;AAChB,WAAO;EACT,GAAE;AACF,SAAO;IACL,QAAQ,KAAG;AACT,aAAO,QAAQ,QAAQ,GAAG;IAC5B;IACA,WAAW,KAAG;AACZ,cAAQ,WAAW,GAAG;IACxB;IACA,QAAQ,KAAK,OAAK;AAChB,UAAI;AACF,gBAAQ,QAAQ,KAAK,KAAK;MAE5B,QAAQ;MAAC;IACX;;AAEJ;;;AC/GA,IAAM,OAAO;AACb,IAAI,QAAQ;AACZ,IAAI;AAEE,SAAU,IAAI,SAAS,IAAE;AAC7B,MAAI,CAAC,UAAU,QAAQ,SAAS,OAAO,GAAG;AACxC,aAAS;AACT,YAAQ;AACR,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,iBAAY,MAAM,KAAK,OAAM,IAAK,MAAO,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;IACtE;EACF;AACA,SAAO,OAAO,UAAU,OAAO,UAAU,MAAM;AACjD;;;AC0BM,SAAU,aAKd,YAAoE;AAEpE,QAAM,EACJ,iCAAiC,MACjC,UAAU,cAAc;IACtB,SAAS,kBAAiB;GAC3B,GACD,qBAAqB,MACrB,MAAM,OACN,GAAG,KAAI,IACL;AAMJ,QAAM,OACJ,OAAO,WAAW,eAAe,iCAC7B,YAAU,IACV;AAEN,QAAM,SAASC,aAAY,MAAM,KAAK,MAAM;AAC5C,QAAM,aAAaA,aAAY,MAAK;AAClC,UAAM,aAAa,CAAA;AACnB,UAAM,UAAU,oBAAI,IAAG;AACvB,eAAW,gBAAgB,KAAK,cAAc,CAAA,GAAI;AAChD,YAAM,YAAY,MAAM,YAAY;AACpC,iBAAW,KAAK,SAAS;AACzB,UAAI,CAAC,OAAO,UAAU,MAAM;AAC1B,cAAM,aACJ,OAAO,UAAU,SAAS,WAAW,CAAC,UAAU,IAAI,IAAI,UAAU;AACpE,mBAAW,QAAQ,YAAY;AAC7B,kBAAQ,IAAI,IAAI;QAClB;MACF;IACF;AACA,QAAI,CAAC,OAAO,MAAM;AAChB,YAAM,YAAY,KAAK,aAAY;AACnC,iBAAW,YAAY,WAAW;AAChC,YAAI,QAAQ,IAAI,SAAS,KAAK,IAAI;AAAG;AACrC,mBAAW,KAAK,MAAM,0BAA0B,QAAQ,CAAC,CAAC;MAC5D;IACF;AACA,WAAO;EACT,CAAC;AACD,WAAS,MAAM,aAA8B;AAE3C,UAAM,UAAU,cAAiC,IAAG,CAAE;AACtD,UAAM,YAAY;MAChB,GAAG,YAAY;QACb;QACA,QAAQ,OAAO,SAAQ;QACvB;QACA,YAAY,KAAK;OAClB;MACD;MACA,KAAK,QAAQ;;AAKf,YAAQ,GAAG,WAAWC,QAAO;AAC7B,cAAU,QAAO;AAEjB,WAAO;EACT;AACA,WAAS,0BAA0B,gBAAqC;AACtE,UAAM,EAAE,KAAI,IAAK;AACjB,UAAM,WAAW,eAAe;AAChC,WAAO,SAAS,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,KAAK,MAAM,SAAQ,EAAE,CAAE;EAClE;AAEA,QAAM,UAAU,oBAAI,IAAG;AACvB,WAASC,WACP,SAAmE,CAAA,GAAE;AAErE,UAAM,UAAU,OAAO,WAAW,MAAM,SAAQ,EAAG;AACnD,UAAM,QAAQ,OAAO,SAAQ,EAAG,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAG5D,QAAI,OAAO,WAAW,CAAC;AAAO,YAAM,IAAI,wBAAuB;AAI/D;AACE,YAAMC,UAAS,QAAQ,IAAI,MAAM,SAAQ,EAAG,OAAO;AACnD,UAAIA,WAAU,CAAC;AAAO,eAAOA;AAC7B,UAAI,CAAC;AAAO,cAAM,IAAI,wBAAuB;IAC/C;AAGA;AACE,YAAMA,UAAS,QAAQ,IAAI,OAAO;AAClC,UAAIA;AAAQ,eAAOA;IACrB;AAEA,QAAI;AACJ,QAAI,KAAK;AAAQ,eAAS,KAAK,OAAO,EAAE,MAAK,CAAE;SAC1C;AACH,YAAMC,WAAU,MAAM;AACtB,YAAM,WAAW,OAAO,SAAQ,EAAG,IAAI,CAAC,MAAM,EAAE,EAAE;AAElD,YAAM,aAAyC,CAAA;AAC/C,YAAM,UAAU,OAAO,QAAQ,IAAI;AAEnC,iBAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,YACE,QAAQ,YACR,QAAQ,YACR,QAAQ,gBACR,QAAQ;AAER;AAEF,YAAI,OAAO,UAAU,UAAU;AAG7B,cAAIA,YAAW;AAAO,uBAAW,GAAG,IAAI,MAAMA,QAAO;eAChD;AAEH,kBAAM,wBAAwB,SAAS,KAAK,CAAC,MAAM,KAAK,KAAK;AAC7D,gBAAI;AAAuB;AAC3B,uBAAW,GAAG,IAAI;UACpB;QACF;AAAO,qBAAW,GAAG,IAAI;MAC3B;AAEA,eAAS,aAAa;QACpB,GAAG;QACH;QACA,OAAO,WAAW,SAAS,EAAE,WAAW,KAAI;QAC5C,WAAW,CAACC,gBACV,KAAK,WAAWD,QAAO,EAAE,EAAE,GAAGC,aAAY,WAAU,CAAE;OACzD;IACH;AAEA,YAAQ,IAAI,SAAS,MAAM;AAC3B,WAAO;EACT;AAMA,WAAS,kBAAe;AACtB,WAAO;MACL,SAAS,OAAO,SAAQ,EAAG,CAAC,EAAE;MAC9B,aAAa,oBAAI,IAAG;MACpB,SAAS;MACT,QAAQ;;EAEZ;AAEA,MAAI;AACJ,QAAM,SAAS;AACf,MAAI,QAAQ,WAAW,MAAM;AAC3B,qBAAiB,OAAO,SAAS,QAAQ,QAAQ,QAAQ,EAAE,CAAC;;AAEzD,qBAAiB,OAAO,SAAS,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG;AAElE,QAAM,QAAQL,aACZ;;IAEE,UACI,QAAQ,iBAAiB;MACvB,QAAQ,gBAAgBM,UAAO;AAC7B,YAAIA,aAAY;AAAgB,iBAAO;AAEvC,cAAM,eAAe,gBAAe;AACpC,cAAM,UAAU,yBACd,gBACA,aAAa,OAAO;AAEtB,eAAO,EAAE,GAAG,cAAc,QAAO;MACnC;MACA,MAAM;MACN,WAAW,OAAK;AAEd,eAAO;UACL,aAAa;YACX,QAAQ;YACR,OAAO,MAAM,KAAK,MAAM,YAAY,QAAO,CAAE,EAAE,IAC7C,CAAC,CAAC,KAAK,UAAU,MAAK;AACpB,oBAAM,EAAE,IAAI,MAAM,MAAM,KAAAC,KAAG,IAAK,WAAW;AAC3C,oBAAM,YAAY,EAAE,IAAI,MAAM,MAAM,KAAAA,KAAG;AACvC,qBAAO,CAAC,KAAK,EAAE,GAAG,YAAY,UAAS,CAAE;YAC3C,CAAC;;UAGL,SAAS,MAAM;UACf,SAAS,MAAM;;MAEnB;MACA,MAAM,gBAAgB,cAAY;AAEhC,YACE,OAAO,mBAAmB,YAC1B,kBACA,YAAY;AAEZ,iBAAO,eAAe;AAExB,cAAM,UAAU,yBACd,gBACA,aAAa,OAAO;AAEtB,eAAO;UACL,GAAG;UACH,GAAI;UACJ;;MAEJ;MACA,eAAe;MACf;MACA,SAAS;KACV,IACD;EAAe,CACpB;AAEH,QAAM,SAAS,gBAAe,CAAE;AAEhC,WAAS,yBACP,gBACA,gBAAsB;AAEtB,WAAO,kBACL,OAAO,mBAAmB,YAC1B,aAAa,kBACb,OAAO,eAAe,YAAY,YAClC,OAAO,SAAQ,EAAG,KAAK,CAAC,MAAM,EAAE,OAAO,eAAe,OAAO,IAC3D,eAAe,UACf;EACN;AAOA,MAAI;AACF,UAAM,UACJ,CAAC,EAAE,aAAa,QAAO,MACrB,UAAU,YAAY,IAAI,OAAO,GAAG,UAAU,QAChD,CAAC,YAAW;AAEV,YAAM,oBAAoB,OACvB,SAAQ,EACR,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAC/B,UAAI,CAAC;AAAmB;AAExB,aAAO,MAAM,SAAS,CAAC,OAAO;QAC5B,GAAG;QACH,SAAS,WAAW,EAAE;QACtB;IACJ,CAAC;AAIL,QAAM,UAAU,CAAC,oBAAmB;AAClC,UAAM,iBAAiB,oBAAI,IAAG;AAC9B,UAAM,mBAAmB,oBAAI,IAAG;AAChC,eAAW,aAAa,WAAW,SAAQ,GAAI;AAC7C,qBAAe,IAAI,UAAU,EAAE;AAC/B,UAAI,UAAU,MAAM;AAClB,cAAM,aACJ,OAAO,UAAU,SAAS,WAAW,CAAC,UAAU,IAAI,IAAI,UAAU;AACpE,mBAAW,QAAQ,YAAY;AAC7B,2BAAiB,IAAI,IAAI;QAC3B;MACF;IACF;AAEA,UAAM,gBAA6B,CAAA;AACnC,eAAW,kBAAkB,iBAAiB;AAC5C,UAAI,iBAAiB,IAAI,eAAe,KAAK,IAAI;AAAG;AACpD,YAAM,YAAY,MAAM,0BAA0B,cAAc,CAAC;AACjE,UAAI,eAAe,IAAI,UAAU,EAAE;AAAG;AACtC,oBAAc,KAAK,SAAS;IAC9B;AAEA,QAAI,WAAW,CAAC,MAAM,QAAQ,YAAW;AAAI;AAC7C,eAAW,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,aAAa,GAAG,IAAI;EAC3D,CAAC;AAMD,WAAS,OAAO,MAA4C;AAC1D,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,aAAa,EAAE,YAAY,IAAI,KAAK,GAAG;AAC7C,UAAI,CAAC;AAAY,eAAO;AACxB,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,KAAK,KAAK;UAChD,UACG,KAAK,YACN,WAAW;UACb,SAAS,KAAK,WAAW,WAAW;UACpC,WAAW,WAAW;SACvB;;IAEL,CAAC;EACH;AACA,WAASN,SAAQ,MAA6C;AAE5D,QACE,MAAM,SAAQ,EAAG,WAAW,gBAC5B,MAAM,SAAQ,EAAG,WAAW;AAE5B;AAEF,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,YAAY,WAAW,SAAQ,EAAG,KAAK,CAACO,OAAMA,GAAE,QAAQ,KAAK,GAAG;AACtE,UAAI,CAAC;AAAW,eAAO;AAEvB,UAAI,UAAU,QAAQ,cAAc,SAAS;AAC3C,kBAAU,QAAQ,IAAI,WAAW,MAAM;AACzC,UAAI,CAAC,UAAU,QAAQ,cAAc,QAAQ;AAC3C,kBAAU,QAAQ,GAAG,UAAU,MAAM;AACvC,UAAI,CAAC,UAAU,QAAQ,cAAc,YAAY;AAC/C,kBAAU,QAAQ,GAAG,cAAcC,WAAU;AAE/C,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,KAAK,KAAK;UAChD,UAAU,KAAK;UACf,SAAS,KAAK;UACd;SACD;QACD,SAAS,KAAK;QACd,QAAQ;;IAEZ,CAAC;EACH;AACA,WAASA,YAAW,MAAgD;AAClE,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,aAAa,EAAE,YAAY,IAAI,KAAK,GAAG;AAC7C,UAAI,YAAY;AACd,cAAM,YAAY,WAAW;AAC7B,YAAI,UAAU,QAAQ,cAAc,QAAQ;AAC1C,qBAAW,UAAU,QAAQ,IAAI,UAAU,MAAM;AACnD,YAAI,UAAU,QAAQ,cAAc,YAAY;AAC9C,qBAAW,UAAU,QAAQ,IAAI,cAAcA,WAAU;AAC3D,YAAI,CAAC,UAAU,QAAQ,cAAc,SAAS;AAC5C,qBAAW,UAAU,QAAQ,GAAG,WAAWR,QAAO;MACtD;AAEA,QAAE,YAAY,OAAO,KAAK,GAAG;AAE7B,UAAI,EAAE,YAAY,SAAS;AACzB,eAAO;UACL,GAAG;UACH,aAAa,oBAAI,IAAG;UACpB,SAAS;UACT,QAAQ;;AAGZ,YAAM,iBAAiB,EAAE,YAAY,OAAM,EAAG,KAAI,EAAG;AACrD,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW;QAClC,SAAS,eAAe,UAAU;;IAEtC,CAAC;EACH;AAEA,SAAO;IACL,IAAI,SAAM;AACR,aAAO,OAAO,SAAQ;IACxB;IACA,IAAI,aAAU;AACZ,aAAO,WAAW,SAAQ;IAC5B;IACA;IAEA,WAAAC;IACA,IAAI,QAAK;AACP,aAAO,MAAM,SAAQ;IACvB;IACA,SAAS,OAAK;AACZ,UAAI;AACJ,UAAI,OAAO,UAAU;AAAY,mBAAW,MAAM,MAAM,SAAQ,CAAS;;AACpE,mBAAW;AAGhB,YAAM,eAAe,gBAAe;AACpC,UAAI,OAAO,aAAa;AAAU,mBAAW;AAC7C,YAAM,YAAY,OAAO,KAAK,YAAY,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,SAAS;AACxE,UAAI;AAAW,mBAAW;AAE1B,YAAM,SAAS,UAAU,IAAI;IAC/B;IACA,UAAU,UAAU,UAAU,SAAO;AACnC,aAAO,MAAM,UACX,UACA,UACA,UACK;QACC,GAAG;QACH,iBAAiB,QAAQ;;UAG3B,MAAS;IAEjB;IAEA,WAAW;MACT;MACA;MACA,KAAK,QAAQ,GAAG;MAChB;MACA,YAAY,KAAK;MACjB,QAAQ;QACN,SAAS,OAAK;AACZ,gBAAM,aACJ,OAAO,UAAU,aAAa,MAAM,OAAO,SAAQ,CAAE,IAAI;AAE3D,cAAI,WAAW,WAAW;AAAG;AAC7B,iBAAO,OAAO,SAAS,YAAY,IAAI;QACzC;QACA,UAAU,UAAQ;AAChB,iBAAO,OAAO,UAAU,QAAQ;QAClC;;MAEF,YAAY;QACV;QACA;QAGA,SAAS,OAAK;AACZ,iBAAO,WAAW,SAChB,OAAO,UAAU,aAAa,MAAM,WAAW,SAAQ,CAAE,IAAI,OAC7D,IAAI;QAER;QACA,UAAU,UAAQ;AAChB,iBAAO,WAAW,UAAU,QAAQ;QACtC;;MAEF,QAAQ,EAAE,QAAQ,SAAAD,UAAS,YAAAQ,YAAU;;;AAG3C;;;AC/dM,SAAU,QAAQ,QAAgB,YAA6B;AACnE,QAAM,EAAE,cAAc,iBAAgB,IAAK;AAE3C,MAAI,gBAAgB,CAAC,OAAO,UAAU,MAAM,QAAQ,YAAW;AAC7D,WAAO,SAAS;MACd,GAAG;MACH,SAAS,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,aAAa,OAAO,IAC5D,aAAa,UACb,OAAO,OAAO,CAAC,EAAE;MACrB,aAAa,mBAAmB,aAAa,cAAc,oBAAI,IAAG;MAClE,QAAQ,mBAAmB,iBAAiB;KAC7C;AAEH,SAAO;IACL,MAAM,UAAO;AACX,UAAI,OAAO,UAAU,KAAK;AACxB,cAAM,OAAO,UAAU,MAAM,QAAQ,UAAS;AAC9C,YAAI,OAAO,UAAU,MAAM;AACzB,iBAAO,UAAU,WAAW,SAAS,CAAC,eAAc;AAClD,kBAAM,UAAU,oBAAI,IAAG;AACvB,uBAAW,aAAa,cAAc,CAAA,GAAI;AACxC,kBAAI,UAAU,MAAM;AAClB,sBAAM,aAAa,MAAM,QAAQ,UAAU,IAAI,IAC3C,UAAU,OACV,CAAC,UAAU,IAAI;AACnB,2BAAW,QAAQ,YAAY;AAC7B,0BAAQ,IAAI,IAAI;gBAClB;cACF;YACF;AACA,kBAAM,iBAAiB,CAAA;AACvB,kBAAM,YAAY,OAAO,UAAU,MAAM,aAAY,KAAM,CAAA;AAC3D,uBAAW,YAAY,WAAW;AAChC,kBAAI,QAAQ,IAAI,SAAS,KAAK,IAAI;AAAG;AACrC,oBAAM,cACJ,OAAO,UAAU,WAAW,0BAA0B,QAAQ;AAChE,oBAAM,YAAY,OAAO,UAAU,WAAW,MAAM,WAAW;AAC/D,6BAAe,KAAK,SAAS;YAC/B;AACA,mBAAO,CAAC,GAAG,YAAY,GAAG,cAAc;UAC1C,CAAC;QACH;MACF;AAEA,UAAI;AAAkB,kBAAU,MAAM;eAC7B,OAAO;AAEd,eAAO,SAAS,CAAC,OAAO;UACtB,GAAG;UACH,aAAa,oBAAI,IAAG;UACpB;IACN;;AAEJ;;;AChCM,SAAU,mBACd,WACA,SAAmC,CAAA,GAAE;AAErC,QAAM,EAAE,KAAI,IAAK;AACjB,QAAM,EAAE,MAAM,aAAa,OAAO,aAAa,WAAU,IAAK;AAE9D,SAAO,CAAC,eAAc;AACpB,UAAM,EAAE,OAAO,WAAU,IAAK;AAC9B,UAAM,aAAa,OAAO,cAAc,WAAW;AAEnD,UAAM,UAA4B,OAAO,EAAE,QAAQ,OAAM,MAAM;AAC7D,YAAMC,aAAY,YAAY,SAAQ,EAAG,KAAK,CAAC,MAAM,EAAE,SAAS,IAAI;AACpE,UAAI,CAACA;AACH,cAAM,IAAI,0BACR,IAAI,MACF,qCAAqC,IAAI,iDAAiD,CAC3F;AAGL,YAAM,WAAY,MAAMA,WAAU,YAAY;QAC5C,SAAS,OAAO;OACjB;AACD,UAAI,CAAC;AACH,cAAM,IAAI,0BACR,IAAI,MAAM,2BAA2B,CAAC;AAK1C,YAAM,UAAU,YACd,MAAM,UAAU,MACd,YAAY,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE,GAAG;QAC7D,SAAS;OACV,CAAC,CACH;AAEH,UAAI,SAAS,YAAY,MAAM;AAC7B,cAAM,IAAI,uBACR,IAAI,MACF,2CAA2C,OAAO,0DAA0D,MAAM,EAAE,MAAM,MAAM,IAAI,IAAI,CACzI;AAGL,YAAM,OAAO,EAAE,QAAQ,OAAM;AAC7B,aAAO,SAAS,QAAQ,IAAI;IAC9B;AAEA,WAAO,gBAAgB;MACrB;MACA;MACA;MACA;MACA;MACA,MAAM;KACP;EACH;AACF;;;AClFM,SAAUC,UACd,YACA,QAA4C;AAE5C,SAAO,SAAc,YAAY,MAAM;AACzC;;;ACLO,IAAM,gBAAgB;EAC3B,QAAQ,KAAG;AACT,QAAI,OAAO,WAAW;AAAa,aAAO;AAC1C,UAAM,QAAQ,YAAY,SAAS,QAAQ,GAAG;AAC9C,WAAO,SAAS;EAClB;EACA,QAAQ,KAAK,OAAK;AAChB,QAAI,OAAO,WAAW;AAAa;AAEnC,aAAS,SAAS,GAAG,GAAG,IAAI,KAAK;EACnC;EACA,WAAW,KAAG;AACZ,QAAI,OAAO,WAAW;AAAa;AAEnC,aAAS,SAAS,GAAG,GAAG;EAC1B;;AAGI,SAAU,qBAAqB,QAAgB,QAAsB;AACzE,MAAI,CAAC;AAAQ,WAAO;AACpB,QAAM,MAAM,GAAG,OAAO,SAAS,GAAG;AAClC,QAAM,SAAS,YAAY,QAAQ,GAAG;AACtC,MAAI,CAAC;AAAQ,WAAO;AACpB,SAAO,YAA8B,MAAM,EAAE;AAC/C;AAEM,SAAU,YAAY,QAAgB,KAAW;AACrD,QAAM,WAAW,OAAO,MAAM,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,WAAW,GAAG,GAAG,GAAG,CAAC;AACvE,MAAI,CAAC;AAAU,WAAO;AACtB,SAAO,SAAS,UAAU,IAAI,SAAS,CAAC;AAC1C;;;AC3BM,SAAU,eAAe,YAAoC;AACjE,QAAM,EAAE,MAAK,IAAK;AAClB,QAAM,cAAc,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAEhD,MAAI,CAAC,WAAW;AAAY,WAAO,CAAC,WAAW;AAE/C,QAAM,YAAY,WAAW,aAAa,MAAM,EAAE,IAAI,EAAE,MAAK,CAAE;AAC/D,QAAM,aAAc,WAAW,OAAO,cAE9B,CAAC,SAAS;AAClB,SAAO,WAAW,IAAI,CAAC,EAAE,MAAK,MAAO,OAAO,OAAO,WAAW;AAChE;;;ACjBM,SAAU,iBAAiB,SAA2C;AAC1E,MAAI,OAAO,YAAY;AACrB,WAAO,OAAO,SACZ,SACA,QAAQ,KAAI,EAAG,UAAU,GAAG,CAAC,MAAM,OAAO,KAAK,EAAE;AAErD,MAAI,OAAO,YAAY;AAAU,WAAO,OAAO,OAAO;AACtD,MAAI,OAAO,YAAY;AAAU,WAAO;AACxC,QAAM,IAAI,MACR,6BAA6B,OAAO,cAAc,OAAO,OAAO,GAAG;AAEvE;", "names": ["window", "connect", "disconnect", "provider", "error", "call", "result", "error", "hydrate", "createStore", "uid", "EventEmitter", "value", "index", "value", "deserialize", "serialize", "createStore", "connect", "getClient", "client", "chainId", "parameters", "version", "uid", "x", "disconnect", "connector", "fallback"]}