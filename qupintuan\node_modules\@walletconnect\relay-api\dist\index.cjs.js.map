{"version": 3, "file": "index.cjs.js", "sources": ["../src/misc.ts", "../src/validators.ts", "../src/parsers.ts", "../src/jsonrpc.ts"], "sourcesContent": ["export function assertType(obj: any, key: string, type = \"string\") {\n  if (!obj[key] || typeof obj[key] !== type) {\n    throw new Error(`Missing or invalid \"${key}\" param`);\n  }\n}\n\nexport function hasRequiredParams(params: any, required: string[]) {\n  let matches = true;\n  required.forEach((key) => {\n    const exists = key in params;\n    if (!exists) {\n      matches = false;\n    }\n  });\n  return matches;\n}\n\nexport function hasExactParamsLength(params: any, length: number): boolean {\n  return Array.isArray(params) ? params.length === length : Object.keys(params).length === length;\n}\n\nexport function hasRequiredParamsLength(params: any, minLength: number): boolean {\n  return Array.isArray(params)\n    ? params.length >= minLength\n    : Object.keys(params).length >= minLength;\n}\n\nexport function checkParams(params: any, required: string[], optional: string[]) {\n  const exact = !optional.length;\n  const matchesLength = exact\n    ? hasExactParamsLength(params, required.length)\n    : hasRequiredParamsLength(params, required.length);\n  if (!matchesLength) return false;\n  return hasRequiredParams(params, required);\n}\n\nexport function methodEndsWith(method: string, expected: string, separator = \"_\") {\n  const split = method.split(separator);\n  return split[split.length - 1].trim().toLowerCase() === expected.trim().toLowerCase();\n}\n", "import { JsonRpcRequest } from \"@walletconnect/jsonrpc-types\";\n\nimport { checkParams, methodEndsWith } from \"./misc\";\nimport { RelayJsonRpc } from \"./types\";\n\n// ---------- Subscribe ----------------------------------------------- //\n\nexport function isSubscribeRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.SubscribeParams> {\n  return isSubscribeMethod(request.method) && isSubscribeParams(request.params);\n}\n\nexport function isSubscribeMethod(method: string): boolean {\n  return methodEndsWith(method, \"subscribe\");\n}\n\nexport function isSubscribeParams(params: any): params is RelayJsonRpc.SubscribeParams {\n  const required = [\"topic\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Publish ----------------------------------------------- //\n\nexport function isPublishRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.PublishParams> {\n  return isPublishMethod(request.method) && isPublishParams(request.params);\n}\n\nexport function isPublishMethod(method: string): boolean {\n  return methodEndsWith(method, \"publish\");\n}\n\nexport function isPublishParams(params: any): params is RelayJsonRpc.PublishParams {\n  const required = [\"message\", \"topic\", \"ttl\"];\n  const optional = [\"prompt\", \"tag\"];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Unsubscribe ----------------------------------------------- //\n\nexport function isUnsubscribeRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.UnsubscribeParams> {\n  return isUnsubscribeMethod(request.method) && isUnsubscribeParams(request.params);\n}\n\nexport function isUnsubscribeMethod(method: string): boolean {\n  return methodEndsWith(method, \"unsubscribe\");\n}\n\nexport function isUnsubscribeParams(params: any): params is RelayJsonRpc.UnsubscribeParams {\n  const required = [\"id\", \"topic\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Subscription ----------------------------------------------- //\n\nexport function isSubscriptionRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.SubscriptionParams> {\n  return isSubscriptionMethod(request.method) && isSubscriptionParams(request.params);\n}\n\nexport function isSubscriptionMethod(method: string): boolean {\n  return methodEndsWith(method, \"subscription\");\n}\n\nexport function isSubscriptionParams(params: any): params is RelayJsonRpc.SubscriptionParams {\n  const required = [\"id\", \"data\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n", "import { JsonRpcRequest } from \"@walletconnect/jsonrpc-types\";\n\nimport { RelayJsonRpc } from \"./types\";\nimport { assertType } from \"./misc\";\nimport {\n  isPublishMethod,\n  isPublishParams,\n  isSubscribeMethod,\n  isSubscribeParams,\n  isSubscriptionMethod,\n  isSubscriptionParams,\n  isUnsubscribeMethod,\n  isUnsubscribeParams,\n} from \"./validators\";\n\nexport function parseSubscribeRequest(request: JsonRpcRequest): RelayJsonRpc.SubscribeParams {\n  if (!isSubscribeMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid subscribe method\");\n  }\n  if (!isSubscribeParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid subscribe params\");\n  }\n  const params = request.params as RelayJsonRpc.SubscribeParams;\n\n  assertType(params, \"topic\");\n\n  return params;\n}\n\nexport function parsePublishRequest(request: JsonRpcRequest): RelayJsonRpc.PublishParams {\n  if (!isPublishMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid publish method\");\n  }\n  if (!isPublishParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid publish params\");\n  }\n  const params = request.params as RelayJsonRpc.PublishParams;\n\n  assertType(params, \"topic\");\n  assertType(params, \"message\");\n  assertType(params, \"ttl\", \"number\");\n\n  return params;\n}\n\nexport function parseUnsubscribeRequest(request: JsonRpcRequest): RelayJsonRpc.UnsubscribeParams {\n  if (!isUnsubscribeMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid unsubscribe method\");\n  }\n  if (!isUnsubscribeParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid unsubscribe params\");\n  }\n  const params = request.params as RelayJsonRpc.UnsubscribeParams;\n\n  assertType(params, \"id\");\n\n  return params;\n}\n\nexport function parseSubscriptionRequest(request: JsonRpcRequest): RelayJsonRpc.SubscriptionParams {\n  if (!isSubscriptionMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid subscription method\");\n  }\n  if (!isSubscriptionParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid subscription params\");\n  }\n  const params = request.params as RelayJsonRpc.SubscriptionParams;\n\n  assertType(params, \"id\");\n  assertType(params, \"data\");\n\n  return params;\n}\n", "import { RelayJsonRpc } from \"./types\";\n\nexport const RELAY_JSONRPC: { [protocol: string]: RelayJsonRpc.Methods } = {\n  waku: {\n    publish: \"waku_publish\",\n    batchPublish: \"waku_batchPublish\",\n    subscribe: \"waku_subscribe\",\n    batchSubscribe: \"waku_batchSubscribe\",\n    subscription: \"waku_subscription\",\n    unsubscribe: \"waku_unsubscribe\",\n    batchUnsubscribe: \"waku_batchUnsubscribe\",\n    batchFetchMessages: \"waku_batchFetchMessages\",\n  },\n  irn: {\n    publish: \"irn_publish\",\n    batchPublish: \"irn_batchPublish\",\n    subscribe: \"irn_subscribe\",\n    batchSubscribe: \"irn_batchSubscribe\",\n    subscription: \"irn_subscription\",\n    unsubscribe: \"irn_unsubscribe\",\n    batchUnsubscribe: \"irn_batchUnsubscribe\",\n    batchFetchMessages: \"irn_batchFetchMessages\",\n  },\n  iridium: {\n    publish: \"iridium_publish\",\n    batchPublish: \"iridium_batchPublish\",\n    subscribe: \"iridium_subscribe\",\n    batchSubscribe: \"iridium_batchSubscribe\",\n    subscription: \"iridium_subscription\",\n    unsubscribe: \"iridium_unsubscribe\",\n    batchUnsubscribe: \"iridium_batchUnsubscribe\",\n    batchFetchMessages: \"iridium_batchFetchMessages\",\n  },\n};\n"], "names": ["obj", "key", "type", "params", "required", "matches", "length", "<PERSON><PERSON><PERSON><PERSON>", "optional", "method", "expected", "separator", "split", "request", "methodEndsWith", "checkParams", "isSubscribeMethod", "isSubscribeParams", "assertType", "isPublishMethod", "isPublishParams", "isUnsubscribeMethod", "isUnsubscribeParams", "isSubscriptionMethod", "isSubscriptionParams"], "mappings": ";;;;AAAO,SAAS,UAAWA,CAAAA,CAAAA,CAAUC,CAAaC,CAAAA,CAAAA,CAAO,QAAU,CAAA,CACjE,GAAI,CAACF,CAAIC,CAAAA,CAAG,CAAK,EAAA,OAAOD,EAAIC,CAAG,CAAA,GAAMC,CACnC,CAAA,MAAM,IAAI,KAAA,CAAM,CAAuBD,oBAAAA,EAAAA,CAAAA,CAAAA,OAAAA,CAAY,CAEvD,CAEO,SAAS,iBAAkBE,CAAAA,CAAAA,CAAaC,CAAoB,CAAA,CACjE,IAAIC,CAAAA,CAAU,GACd,OAAAD,CAAAA,CAAS,OAASH,CAAAA,CAAAA,EAAQ,CACTA,CAAAA,IAAOE,CAEpBE,GAAAA,CAAAA,CAAU,CAEd,CAAA,EAAA,CAAC,CACMA,CAAAA,CACT,CAEO,SAAS,oBAAqBF,CAAAA,CAAAA,CAAaG,EAAyB,CACzE,OAAO,KAAM,CAAA,OAAA,CAAQH,CAAM,CAAA,CAAIA,CAAO,CAAA,MAAA,GAAWG,EAAS,MAAO,CAAA,IAAA,CAAKH,CAAM,CAAA,CAAE,MAAWG,GAAAA,CAC3F,CAEO,SAAS,wBAAwBH,CAAaI,CAAAA,CAAAA,CAA4B,CAC/E,OAAO,KAAM,CAAA,OAAA,CAAQJ,CAAM,CAAA,CACvBA,CAAO,CAAA,MAAA,EAAUI,CACjB,CAAA,MAAA,CAAO,IAAKJ,CAAAA,CAAM,CAAE,CAAA,MAAA,EAAUI,CACpC,CAEO,SAAS,WAAYJ,CAAAA,CAAAA,CAAaC,CAAoBI,CAAAA,CAAAA,CAAoB,CAK/E,OAAA,CAJc,CAACA,CAAS,CAAA,MAAA,CAEpB,oBAAqBL,CAAAA,CAAAA,CAAQC,CAAS,CAAA,MAAM,CAC5C,CAAA,uBAAA,CAAwBD,EAAQC,CAAS,CAAA,MAAM,CAE5C,EAAA,iBAAA,CAAkBD,CAAQC,CAAAA,CAAQ,CADd,CAAA,CAAA,CAE7B,CAEgB,SAAA,cAAA,CAAeK,CAAgBC,CAAAA,CAAAA,CAAkBC,CAAY,CAAA,GAAA,CAAK,CAChF,MAAMC,EAAQH,CAAO,CAAA,KAAA,CAAME,CAAS,CAAA,CACpC,OAAOC,CAAAA,CAAMA,CAAM,CAAA,MAAA,CAAS,CAAC,CAAE,CAAA,IAAA,EAAO,CAAA,WAAA,EAAkBF,GAAAA,CAAAA,CAAS,IAAK,EAAA,CAAE,aAC1E;;SChCgB,kBACdG,CAAAA,CAAAA,CACyD,CACzD,OAAO,kBAAkBA,CAAQ,CAAA,MAAM,CAAK,EAAA,iBAAA,CAAkBA,EAAQ,MAAM,CAC9E,CAEO,SAAS,kBAAkBJ,CAAyB,CAAA,CACzD,OAAOK,cAAAA,CAAeL,EAAQ,WAAW,CAC3C,CAEO,SAAS,kBAAkBN,CAAqD,CAAA,CAGrF,OAAOY,WAAYZ,CAAAA,CAAAA,CAFF,CAAC,OAAO,CAAA,CACE,EACkB,CAC/C,CAIO,SAAS,gBACdU,CAAAA,CAAAA,CACuD,CACvD,OAAO,eAAA,CAAgBA,CAAQ,CAAA,MAAM,GAAK,eAAgBA,CAAAA,CAAAA,CAAQ,MAAM,CAC1E,CAEgB,SAAA,eAAA,CAAgBJ,CAAyB,CAAA,CACvD,OAAOK,cAAeL,CAAAA,CAAAA,CAAQ,SAAS,CACzC,CAEgB,SAAA,eAAA,CAAgBN,CAAmD,CAAA,CAGjF,OAAOY,WAAYZ,CAAAA,CAAAA,CAFF,CAAC,SAAW,CAAA,OAAA,CAAS,KAAK,CAC1B,CAAA,CAAC,QAAU,CAAA,KAAK,CACY,CAC/C,CAIgB,SAAA,oBAAA,CACdU,EAC2D,CAC3D,OAAO,mBAAoBA,CAAAA,CAAAA,CAAQ,MAAM,CAAK,EAAA,mBAAA,CAAoBA,CAAQ,CAAA,MAAM,CAClF,CAEO,SAAS,mBAAoBJ,CAAAA,CAAAA,CAAyB,CAC3D,OAAOK,cAAAA,CAAeL,CAAQ,CAAA,aAAa,CAC7C,CAEO,SAAS,mBAAoBN,CAAAA,CAAAA,CAAuD,CAGzF,OAAOY,WAAAA,CAAYZ,EAFF,CAAC,IAAA,CAAM,OAAO,CACJ,CAAA,EACkB,CAC/C,CAIgB,SAAA,qBAAA,CACdU,CAC4D,CAAA,CAC5D,OAAO,oBAAqBA,CAAAA,CAAAA,CAAQ,MAAM,CAAA,EAAK,qBAAqBA,CAAQ,CAAA,MAAM,CACpF,CAEO,SAAS,qBAAqBJ,CAAyB,CAAA,CAC5D,OAAOK,cAAAA,CAAeL,EAAQ,cAAc,CAC9C,CAEO,SAAS,qBAAqBN,CAAwD,CAAA,CAG3F,OAAOY,WAAAA,CAAYZ,EAFF,CAAC,IAAA,CAAM,MAAM,CACH,CAAA,EACkB,CAC/C;;AC5DgB,SAAA,qBAAA,CAAsBU,EAAuD,CAC3F,GAAI,CAACG,iBAAkBH,CAAAA,CAAAA,CAAQ,MAAM,CACnC,CAAA,MAAM,IAAI,KAAM,CAAA,+CAA+C,EAEjE,GAAI,CAACI,kBAAkBJ,CAAQ,CAAA,MAAM,EACnC,MAAM,IAAI,KAAM,CAAA,+CAA+C,EAEjE,MAAMV,CAAAA,CAASU,EAAQ,MAEvB,CAAA,OAAAK,WAAWf,CAAQ,CAAA,OAAO,EAEnBA,CACT,UAEgB,mBAAoBU,CAAAA,CAAAA,CAAqD,CACvF,GAAI,CAACM,gBAAgBN,CAAQ,CAAA,MAAM,CACjC,CAAA,MAAM,IAAI,KAAM,CAAA,6CAA6C,EAE/D,GAAI,CAACO,gBAAgBP,CAAQ,CAAA,MAAM,EACjC,MAAM,IAAI,MAAM,6CAA6C,CAAA,CAE/D,MAAMV,CAASU,CAAAA,CAAAA,CAAQ,OAEvB,OAAAK,UAAAA,CAAWf,CAAQ,CAAA,OAAO,EAC1Be,UAAWf,CAAAA,CAAAA,CAAQ,SAAS,CAC5Be,CAAAA,UAAAA,CAAWf,EAAQ,KAAO,CAAA,QAAQ,EAE3BA,CACT,UAEgB,uBAAwBU,CAAAA,CAAAA,CAAyD,CAC/F,GAAI,CAACQ,oBAAoBR,CAAQ,CAAA,MAAM,CACrC,CAAA,MAAM,IAAI,KAAM,CAAA,iDAAiD,EAEnE,GAAI,CAACS,oBAAoBT,CAAQ,CAAA,MAAM,EACrC,MAAM,IAAI,MAAM,iDAAiD,CAAA,CAEnE,MAAMV,CAASU,CAAAA,CAAAA,CAAQ,OAEvB,OAAAK,UAAAA,CAAWf,CAAQ,CAAA,IAAI,EAEhBA,CACT,UAEgB,wBAAyBU,CAAAA,CAAAA,CAA0D,CACjG,GAAI,CAACU,qBAAqBV,CAAQ,CAAA,MAAM,EACtC,MAAM,IAAI,MAAM,kDAAkD,CAAA,CAEpE,GAAI,CAACW,oBAAAA,CAAqBX,CAAQ,CAAA,MAAM,EACtC,MAAM,IAAI,MAAM,kDAAkD,CAAA,CAEpE,MAAMV,CAASU,CAAAA,CAAAA,CAAQ,OAEvB,OAAAK,UAAAA,CAAWf,EAAQ,IAAI,CAAA,CACvBe,WAAWf,CAAQ,CAAA,MAAM,EAElBA,CACT;;ACtEa,MAAA,aAAA,CAA8D,CACzE,IAAA,CAAM,CACJ,OAAA,CAAS,cACT,CAAA,YAAA,CAAc,mBACd,CAAA,SAAA,CAAW,gBACX,CAAA,cAAA,CAAgB,qBAChB,CAAA,YAAA,CAAc,mBACd,CAAA,WAAA,CAAa,kBACb,CAAA,gBAAA,CAAkB,uBAClB,CAAA,kBAAA,CAAoB,yBACtB,CAAA,CACA,GAAK,CAAA,CACH,OAAS,CAAA,aAAA,CACT,YAAc,CAAA,kBAAA,CACd,SAAW,CAAA,eAAA,CACX,eAAgB,oBAChB,CAAA,YAAA,CAAc,kBACd,CAAA,WAAA,CAAa,iBACb,CAAA,gBAAA,CAAkB,sBAClB,CAAA,kBAAA,CAAoB,wBACtB,CAAA,CACA,OAAS,CAAA,CACP,OAAS,CAAA,iBAAA,CACT,YAAc,CAAA,sBAAA,CACd,SAAW,CAAA,mBAAA,CACX,cAAgB,CAAA,wBAAA,CAChB,YAAc,CAAA,sBAAA,CACd,WAAa,CAAA,qBAAA,CACb,gBAAkB,CAAA,0BAAA,CAClB,kBAAoB,CAAA,4BACtB,CACF;;;;;;;;;;;;;;;;;;;;"}