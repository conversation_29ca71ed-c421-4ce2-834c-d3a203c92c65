const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/basicOperations-BOq0NLuf-1754375917692-50a8b124p.js","assets/vendor-D7uqzx8C-1754375917692-50a8b124p.js","assets/index-CaV4ohF9-1754375917692-opz9ain10.js","assets/web3-NCUyEZtP-1754375917692-50a8b124p.js","assets/index-QZjJZq-p-1754375917692-9oqipqqma.css","assets/groupBuyService-BRszvQfM-1754375917692-50a8b124p.js","assets/core-DKqkut0a-1754375917692-50a8b124p.js","assets/roomManagement-BZPf_gXk-1754375917692-50a8b124p.js","assets/rewardOperations-I9R1_Rnl-1754375917692-50a8b124p.js","assets/transaction-DfFoEzbA-1754375917692-50a8b124p.js"])))=>i.map(i=>d[i]);
import{_ as u}from"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";async function f(o){try{console.log(`🔍 直接查询合约中房间 #${o} 的开奖信息...`);const{fetchRoom:i}=await u(async()=>{const{fetchRoom:n}=await import("./basicOperations-BOq0NLuf-1754375917692-50a8b124p.js");return{fetchRoom:n}},__vite__mapDeps([0,1])),e=await i({chainId:97,roomId:o});console.log(`📊 房间 #${o} 完整信息:`,{id:e.id,creator:e.creator,participantsCount:e.participants?.length||0,isClosed:e.isClosed,isSuccessful:e.isSuccessful,readyForWinner:e.readyForWinner,winner:e.winner,winnerAddress:e.winnerAddress,lotteryTxHash:e.lotteryTxHash,lotteryTimestamp:e.lotteryTimestamp,timeLeft:e.timeLeft,isExpired:e.isExpired,status:e.status});let r=null;try{const n=localStorage.getItem(`lottery_${o}`);n?(r=JSON.parse(n),console.log(`💾 房间 #${o} localStorage开奖信息:`,r)):console.log(`💾 房间 #${o} localStorage中无开奖数据`)}catch(n){console.warn(`💾 房间 #${o} localStorage检查失败:`,n)}const s={roomId:o,creator:e.creator,participantsCount:e.participants?.length||0,isClosed:e.isClosed,isSuccessful:e.isSuccessful,timeLeft:e.timeLeft,step1_readyForWinner:!!e.readyForWinner,step1_hasLocalLotteryInfo:!!r,step1_isHistoricalMalicious:!!e._isMaliciousLottery,step2_hasWinner:!!(e.winner&&e.winner!=="0x0000000000000000000000000000000000000000"),step2_isSuccessful:!!(e.isSuccessful&&e.isSuccessful!=="0x0000000000000000000000000000000000000000"),isViolation:!1},l=s.step1_readyForWinner||s.step1_hasLocalLotteryInfo||s.step1_isHistoricalMalicious,t=s.step2_hasWinner||s.step2_isSuccessful,a=e.timeLeft<=0;let c=!1;if(e.createTime&&!a){const n=Date.now(),d=e.createTime instanceof Date?e.createTime.getTime():new Date(e.createTime).getTime(),y=n-d,g=1440*60*1e3;c=y>g}const m=a||c,p=l&&!t;return s.isViolation=m&&p,s}catch(i){throw console.error(`查询房间 #${o} 合约信息失败:`,i),i}}async function x(o){console.log(`🔍 开始批量查询 ${o.length} 个房间的合约开奖状态...`);const i=[];for(const r of o)try{const s=await f(r);i.push(s)}catch(s){console.error(`查询房间 #${r} 失败:`,s),i.push({roomId:r,error:s.message,isViolation:!1})}const e=i.filter(r=>r.isViolation);return console.log("📊 批量查询结果汇总:",{totalRooms:i.length,violationRooms:e.length,violationRoomIds:e.map(r=>r.roomId)}),i}function w(){console.log("🧪 开始测试违规检测逻辑..."),u(async()=>{const{isMaliciousLotteryRoom:o}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(i=>i.m);return{isMaliciousLotteryRoom:o}},__vite__mapDeps([2,1,3,4])).then(({isMaliciousLotteryRoom:o})=>{const i={id:"test1",isClosed:!0,isSuccessful:"0x0000000000000000000000000000000000000000",participants:new Array(8).fill("0x123"),readyForWinner:!1,timeLeft:3600},e={id:"test2",isClosed:!0,isSuccessful:"0x0000000000000000000000000000000000000000",participants:new Array(8).fill("0x123"),readyForWinner:!0,timeLeft:3600},r={id:"test3",isClosed:!0,isSuccessful:"0x456789abcdef",participants:new Array(8).fill("0x123"),readyForWinner:!0,winner:"0x456789abcdef",timeLeft:0};localStorage.setItem("lottery_test4",JSON.stringify({txHash:"0xabc123",winner:{address:"0x789"},timestamp:Date.now()}));const s={id:"test4",isClosed:!0,isSuccessful:"0x0000000000000000000000000000000000000000",participants:new Array(8).fill("0x123"),readyForWinner:!1,timeLeft:3600};console.log("📊 测试结果:"),console.log("1. 正常过期房间:",o(i),"(应该是 false)"),console.log("2. 故意不开奖房间:",o(e),"(应该是 true)"),console.log("3. 正常完成房间:",o(r),"(应该是 false)"),console.log("4. 有本地开奖信息但未完成:",o(s),"(应该是 true)"),localStorage.removeItem("lottery_test4"),console.log("✅ 违规检测逻辑测试完成")})}async function _(){console.log("🚀 开始查询所有过期房间的违规状态...");try{const e=await(await u(()=>import("./groupBuyService-BRszvQfM-1754375917692-50a8b124p.js"),__vite__mapDeps([5,2,1,3,4,6,0,7,8,9]))).default.fetchRooms({chainId:97,currentUserAddress:null,isMyRoomsPage:!1});if(!e||!e.rooms)return console.log("❌ 无法获取房间数据"),[];const r=e.rooms.filter(t=>{const a=t.timeLeft<=0,c=Date.now(),m=t.createTime instanceof Date?t.createTime.getTime():new Date(t.createTime).getTime(),p=c-m,n=1440*60*1e3,d=p>n;return a||d}),{isMaliciousLotteryRoom:s}=await u(async()=>{const{isMaliciousLotteryRoom:t}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(a=>a.m);return{isMaliciousLotteryRoom:t}},__vite__mapDeps([2,1,3,4])),l=[];for(const t of r){try{if(JSON.parse(localStorage.getItem("exempt_rooms")||"{}").hasOwnProperty(t.id))continue}catch(c){console.warn(`检查房间 #${t.id} 豁免状态失败:`,c)}s(t)&&l.push({roomId:t.id,creator:t.creator,participants:t.participants.length,tier:t.tier,createTime:t.createTime,status:t.status,readyForWinner:t.readyForWinner,isViolation:!0})}return console.log(`📊 违规检测结果: 发现 ${l.length} 个违规房间`),l.forEach(t=>{console.log(`🚨 违规房间 #${t.roomId}: 创建者 ${t.creator}, 参与者 ${t.participants}/8`)}),l}catch(o){return console.error("查询过期房间违规状态失败:",o),[]}}typeof window<"u"&&setTimeout(()=>{window.queryExpiredRoomsViolations=_,window.queryContractLotteryInfo=f,window.testViolationDetection=w},2e3);export{x as batchQueryContractLotteryInfo,f as queryContractLotteryInfo,_ as queryExpiredRoomsViolations,w as testViolationDetection};
