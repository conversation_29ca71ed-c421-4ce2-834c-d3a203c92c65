(function(W,bt){typeof exports=="object"&&typeof module<"u"?bt(exports):typeof define=="function"&&define.amd?define(["exports"],bt):(W=typeof globalThis<"u"?globalThis:W||self,bt(W["@walletconnect/sign-client"]={}))})(this,function(W){"use strict";var bt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ha(t){var e=t.default;if(typeof e=="function"){var r=function(){return e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach(function(n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(r,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}),r}var Ke={exports:{}},Lr=typeof Reflect=="object"?Reflect:null,Wa=Lr&&typeof Lr.apply=="function"?Lr.apply:function(e,r,n){return Function.prototype.apply.call(e,r,n)},di;Lr&&typeof Lr.ownKeys=="function"?di=Lr.ownKeys:Object.getOwnPropertySymbols?di=function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:di=function(e){return Object.getOwnPropertyNames(e)};function Qf(t){console&&console.warn&&console.warn(t)}var Ga=Number.isNaN||function(e){return e!==e};function re(){re.init.call(this)}Ke.exports=re,Ke.exports.once=np,re.EventEmitter=re,re.prototype._events=void 0,re.prototype._eventsCount=0,re.prototype._maxListeners=void 0;var Ya=10;function fi(t){if(typeof t!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}Object.defineProperty(re,"defaultMaxListeners",{enumerable:!0,get:function(){return Ya},set:function(t){if(typeof t!="number"||t<0||Ga(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");Ya=t}}),re.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},re.prototype.setMaxListeners=function(e){if(typeof e!="number"||e<0||Ga(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this};function Za(t){return t._maxListeners===void 0?re.defaultMaxListeners:t._maxListeners}re.prototype.getMaxListeners=function(){return Za(this)},re.prototype.emit=function(e){for(var r=[],n=1;n<arguments.length;n++)r.push(arguments[n]);var i=e==="error",s=this._events;if(s!==void 0)i=i&&s.error===void 0;else if(!i)return!1;if(i){var o;if(r.length>0&&(o=r[0]),o instanceof Error)throw o;var a=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var c=s[e];if(c===void 0)return!1;if(typeof c=="function")Wa(c,this,r);else for(var u=c.length,l=tc(c,u),n=0;n<u;++n)Wa(l[n],this,r);return!0};function Xa(t,e,r,n){var i,s,o;if(fi(r),s=t._events,s===void 0?(s=t._events=Object.create(null),t._eventsCount=0):(s.newListener!==void 0&&(t.emit("newListener",e,r.listener?r.listener:r),s=t._events),o=s[e]),o===void 0)o=s[e]=r,++t._eventsCount;else if(typeof o=="function"?o=s[e]=n?[r,o]:[o,r]:n?o.unshift(r):o.push(r),i=Za(t),i>0&&o.length>i&&!o.warned){o.warned=!0;var a=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");a.name="MaxListenersExceededWarning",a.emitter=t,a.type=e,a.count=o.length,Qf(a)}return t}re.prototype.addListener=function(e,r){return Xa(this,e,r,!1)},re.prototype.on=re.prototype.addListener,re.prototype.prependListener=function(e,r){return Xa(this,e,r,!0)};function ep(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Ja(t,e,r){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},i=ep.bind(n);return i.listener=r,n.wrapFn=i,i}re.prototype.once=function(e,r){return fi(r),this.on(e,Ja(this,e,r)),this},re.prototype.prependOnceListener=function(e,r){return fi(r),this.prependListener(e,Ja(this,e,r)),this},re.prototype.removeListener=function(e,r){var n,i,s,o,a;if(fi(r),i=this._events,i===void 0)return this;if(n=i[e],n===void 0)return this;if(n===r||n.listener===r)--this._eventsCount===0?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,n.listener||r));else if(typeof n!="function"){for(s=-1,o=n.length-1;o>=0;o--)if(n[o]===r||n[o].listener===r){a=n[o].listener,s=o;break}if(s<0)return this;s===0?n.shift():tp(n,s),n.length===1&&(i[e]=n[0]),i.removeListener!==void 0&&this.emit("removeListener",e,a||r)}return this},re.prototype.off=re.prototype.removeListener,re.prototype.removeAllListeners=function(e){var r,n,i;if(n=this._events,n===void 0)return this;if(n.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):n[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete n[e]),this;if(arguments.length===0){var s=Object.keys(n),o;for(i=0;i<s.length;++i)o=s[i],o!=="removeListener"&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(r=n[e],typeof r=="function")this.removeListener(e,r);else if(r!==void 0)for(i=r.length-1;i>=0;i--)this.removeListener(e,r[i]);return this};function Qa(t,e,r){var n=t._events;if(n===void 0)return[];var i=n[e];return i===void 0?[]:typeof i=="function"?r?[i.listener||i]:[i]:r?rp(i):tc(i,i.length)}re.prototype.listeners=function(e){return Qa(this,e,!0)},re.prototype.rawListeners=function(e){return Qa(this,e,!1)},re.listenerCount=function(t,e){return typeof t.listenerCount=="function"?t.listenerCount(e):ec.call(t,e)},re.prototype.listenerCount=ec;function ec(t){var e=this._events;if(e!==void 0){var r=e[t];if(typeof r=="function")return 1;if(r!==void 0)return r.length}return 0}re.prototype.eventNames=function(){return this._eventsCount>0?di(this._events):[]};function tc(t,e){for(var r=new Array(e),n=0;n<e;++n)r[n]=t[n];return r}function tp(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}function rp(t){for(var e=new Array(t.length),r=0;r<e.length;++r)e[r]=t[r].listener||t[r];return e}function np(t,e){return new Promise(function(r,n){function i(o){t.removeListener(e,s),n(o)}function s(){typeof t.removeListener=="function"&&t.removeListener("error",i),r([].slice.call(arguments))}rc(t,e,s,{once:!0}),e!=="error"&&ip(t,i,{once:!0})})}function ip(t,e,r){typeof t.on=="function"&&rc(t,"error",e,r)}function rc(t,e,r,n){if(typeof t.on=="function")n.once?t.once(e,r):t.on(e,r);else if(typeof t.addEventListener=="function")t.addEventListener(e,function i(s){n.once&&t.removeEventListener(e,i),r(s)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t)}var F={};/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */var ws=function(t,e){return ws=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)n.hasOwnProperty(i)&&(r[i]=n[i])},ws(t,e)};function sp(t,e){ws(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}var bs=function(){return bs=Object.assign||function(e){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},bs.apply(this,arguments)};function op(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]]);return r}function ap(t,e,r,n){var i=arguments.length,s=i<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,r):n,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s}function cp(t,e){return function(r,n){e(r,n,t)}}function up(t,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(t,e)}function lp(t,e,r,n){function i(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function a(l){try{u(n.next(l))}catch(h){o(h)}}function c(l){try{u(n.throw(l))}catch(h){o(h)}}function u(l){l.done?s(l.value):i(l.value).then(a,c)}u((n=n.apply(t,e||[])).next())})}function hp(t,e){var r={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},n,i,s,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(u){return function(l){return c([u,l])}}function c(u){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,i&&(s=u[0]&2?i.return:u[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,u[1])).done)return s;switch(i=0,s&&(u=[u[0]&2,s.value]),u[0]){case 0:case 1:s=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,i=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(s=r.trys,!(s=s.length>0&&s[s.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!s||u[1]>s[0]&&u[1]<s[3])){r.label=u[1];break}if(u[0]===6&&r.label<s[1]){r.label=s[1],s=u;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(u);break}s[2]&&r.ops.pop(),r.trys.pop();continue}u=e.call(t,r)}catch(l){u=[6,l],i=0}finally{n=s=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function dp(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}function fp(t,e){for(var r in t)r!=="default"&&!e.hasOwnProperty(r)&&(e[r]=t[r])}function ms(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function nc(t,e){var r=typeof Symbol=="function"&&t[Symbol.iterator];if(!r)return t;var n=r.call(t),i,s=[],o;try{for(;(e===void 0||e-- >0)&&!(i=n.next()).done;)s.push(i.value)}catch(a){o={error:a}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(o)throw o.error}}return s}function pp(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(nc(arguments[e]));return t}function gp(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;for(var n=Array(t),i=0,e=0;e<r;e++)for(var s=arguments[e],o=0,a=s.length;o<a;o++,i++)n[i]=s[o];return n}function bn(t){return this instanceof bn?(this.v=t,this):new bn(t)}function yp(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(t,e||[]),i,s=[];return i={},o("next"),o("throw"),o("return"),i[Symbol.asyncIterator]=function(){return this},i;function o(d){n[d]&&(i[d]=function(p){return new Promise(function(f,y){s.push([d,p,f,y])>1||a(d,p)})})}function a(d,p){try{c(n[d](p))}catch(f){h(s[0][3],f)}}function c(d){d.value instanceof bn?Promise.resolve(d.value.v).then(u,l):h(s[0][2],d)}function u(d){a("next",d)}function l(d){a("throw",d)}function h(d,p){d(p),s.shift(),s.length&&a(s[0][0],s[0][1])}}function wp(t){var e,r;return e={},n("next"),n("throw",function(i){throw i}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(i,s){e[i]=t[i]?function(o){return(r=!r)?{value:bn(t[i](o)),done:i==="return"}:s?s(o):o}:s}}function bp(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof ms=="function"?ms(t):t[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(s){r[s]=t[s]&&function(o){return new Promise(function(a,c){o=t[s](o),i(a,c,o.done,o.value)})}}function i(s,o,a,c){Promise.resolve(c).then(function(u){s({value:u,done:a})},o)}}function mp(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function vp(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function Ep(t){return t&&t.__esModule?t:{default:t}}function _p(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function Sp(t,e,r){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,r),r}var Ip=Object.freeze({__proto__:null,__extends:sp,get __assign(){return bs},__rest:op,__decorate:ap,__param:cp,__metadata:up,__awaiter:lp,__generator:hp,__createBinding:dp,__exportStar:fp,__values:ms,__read:nc,__spread:pp,__spreadArrays:gp,__await:bn,__asyncGenerator:yp,__asyncDelegator:wp,__asyncValues:bp,__makeTemplateObject:mp,__importStar:vp,__importDefault:Ep,__classPrivateFieldGet:_p,__classPrivateFieldSet:Sp}),pi=Ha(Ip),vs={},mn={},ic;function xp(){if(ic)return mn;ic=1,Object.defineProperty(mn,"__esModule",{value:!0}),mn.delay=void 0;function t(e){return new Promise(r=>{setTimeout(()=>{r(!0)},e)})}return mn.delay=t,mn}var lr={},Es={},hr={},sc;function Op(){return sc||(sc=1,Object.defineProperty(hr,"__esModule",{value:!0}),hr.ONE_THOUSAND=hr.ONE_HUNDRED=void 0,hr.ONE_HUNDRED=100,hr.ONE_THOUSAND=1e3),hr}var _s={},oc;function Dp(){return oc||(oc=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.ONE_YEAR=t.FOUR_WEEKS=t.THREE_WEEKS=t.TWO_WEEKS=t.ONE_WEEK=t.THIRTY_DAYS=t.SEVEN_DAYS=t.FIVE_DAYS=t.THREE_DAYS=t.ONE_DAY=t.TWENTY_FOUR_HOURS=t.TWELVE_HOURS=t.SIX_HOURS=t.THREE_HOURS=t.ONE_HOUR=t.SIXTY_MINUTES=t.THIRTY_MINUTES=t.TEN_MINUTES=t.FIVE_MINUTES=t.ONE_MINUTE=t.SIXTY_SECONDS=t.THIRTY_SECONDS=t.TEN_SECONDS=t.FIVE_SECONDS=t.ONE_SECOND=void 0,t.ONE_SECOND=1,t.FIVE_SECONDS=5,t.TEN_SECONDS=10,t.THIRTY_SECONDS=30,t.SIXTY_SECONDS=60,t.ONE_MINUTE=t.SIXTY_SECONDS,t.FIVE_MINUTES=t.ONE_MINUTE*5,t.TEN_MINUTES=t.ONE_MINUTE*10,t.THIRTY_MINUTES=t.ONE_MINUTE*30,t.SIXTY_MINUTES=t.ONE_MINUTE*60,t.ONE_HOUR=t.SIXTY_MINUTES,t.THREE_HOURS=t.ONE_HOUR*3,t.SIX_HOURS=t.ONE_HOUR*6,t.TWELVE_HOURS=t.ONE_HOUR*12,t.TWENTY_FOUR_HOURS=t.ONE_HOUR*24,t.ONE_DAY=t.TWENTY_FOUR_HOURS,t.THREE_DAYS=t.ONE_DAY*3,t.FIVE_DAYS=t.ONE_DAY*5,t.SEVEN_DAYS=t.ONE_DAY*7,t.THIRTY_DAYS=t.ONE_DAY*30,t.ONE_WEEK=t.SEVEN_DAYS,t.TWO_WEEKS=t.ONE_WEEK*2,t.THREE_WEEKS=t.ONE_WEEK*3,t.FOUR_WEEKS=t.ONE_WEEK*4,t.ONE_YEAR=t.ONE_DAY*365}(_s)),_s}var ac;function cc(){return ac||(ac=1,function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=pi;e.__exportStar(Op(),t),e.__exportStar(Dp(),t)}(Es)),Es}var uc;function Ap(){if(uc)return lr;uc=1,Object.defineProperty(lr,"__esModule",{value:!0}),lr.fromMiliseconds=lr.toMiliseconds=void 0;const t=cc();function e(n){return n*t.ONE_THOUSAND}lr.toMiliseconds=e;function r(n){return Math.floor(n/t.ONE_THOUSAND)}return lr.fromMiliseconds=r,lr}var lc;function $p(){return lc||(lc=1,function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=pi;e.__exportStar(xp(),t),e.__exportStar(Ap(),t)}(vs)),vs}var Ur={},hc;function Tp(){if(hc)return Ur;hc=1,Object.defineProperty(Ur,"__esModule",{value:!0}),Ur.Watch=void 0;class t{constructor(){this.timestamps=new Map}start(r){if(this.timestamps.has(r))throw new Error(`Watch already started for label: ${r}`);this.timestamps.set(r,{started:Date.now()})}stop(r){const n=this.get(r);if(typeof n.elapsed<"u")throw new Error(`Watch already stopped for label: ${r}`);const i=Date.now()-n.started;this.timestamps.set(r,{started:n.started,elapsed:i})}get(r){const n=this.timestamps.get(r);if(typeof n>"u")throw new Error(`No timestamp found for label: ${r}`);return n}elapsed(r){const n=this.get(r);return n.elapsed||Date.now()-n.started}}return Ur.Watch=t,Ur.default=t,Ur}var Ss={},vn={},dc;function Pp(){if(dc)return vn;dc=1,Object.defineProperty(vn,"__esModule",{value:!0}),vn.IWatch=void 0;class t{}return vn.IWatch=t,vn}var fc;function Np(){return fc||(fc=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),pi.__exportStar(Pp(),t)}(Ss)),Ss}(function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=pi;e.__exportStar($p(),t),e.__exportStar(Tp(),t),e.__exportStar(Np(),t),e.__exportStar(cc(),t)})(F);class dr{}class Rp extends dr{constructor(e){super()}}const pc=F.FIVE_SECONDS,fr={pulse:"heartbeat_pulse"};class Is extends Rp{constructor(e){super(e),this.events=new Ke.exports.EventEmitter,this.interval=pc,this.interval=e?.interval||pc}static async init(e){const r=new Is(e);return await r.init(),r}async init(){await this.initialize()}stop(){clearInterval(this.intervalRef)}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async initialize(){this.intervalRef=setInterval(()=>this.pulse(),F.toMiliseconds(this.interval))}pulse(){this.events.emit(fr.pulse)}}const Cp=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,Bp=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,Fp=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function Lp(t,e){if(t==="__proto__"||t==="constructor"&&e&&typeof e=="object"&&"prototype"in e){Up(t);return}return e}function Up(t){console.warn(`[destr] Dropping "${t}" key to prevent prototype pollution.`)}function gi(t,e={}){if(typeof t!="string")return t;if(t[0]==='"'&&t[t.length-1]==='"'&&t.indexOf("\\")===-1)return t.slice(1,-1);const r=t.trim();if(r.length<=9)switch(r.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!Fp.test(t)){if(e.strict)throw new SyntaxError("[destr] Invalid JSON");return t}try{if(Cp.test(t)||Bp.test(t)){if(e.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(t,Lp)}return JSON.parse(t)}catch(n){if(e.strict)throw n;return t}}function kp(t){return!t||typeof t.then!="function"?Promise.resolve(t):t}function _e(t,...e){try{return kp(t(...e))}catch(r){return Promise.reject(r)}}function jp(t){const e=typeof t;return t===null||e!=="object"&&e!=="function"}function Mp(t){const e=Object.getPrototypeOf(t);return!e||e.isPrototypeOf(Object)}function yi(t){if(jp(t))return String(t);if(Mp(t)||Array.isArray(t))return JSON.stringify(t);if(typeof t.toJSON=="function")return yi(t.toJSON());throw new Error("[unstorage] Cannot stringify value!")}const xs="base64:";function qp(t){return typeof t=="string"?t:xs+Kp(t)}function zp(t){return typeof t!="string"||!t.startsWith(xs)?t:Vp(t.slice(xs.length))}function Vp(t){return globalThis.Buffer?Buffer.from(t,"base64"):Uint8Array.from(globalThis.atob(t),e=>e.codePointAt(0))}function Kp(t){return globalThis.Buffer?Buffer.from(t).toString("base64"):globalThis.btoa(String.fromCodePoint(...t))}function He(t){return t&&t.split("?")[0]?.replace(/[/\\]/g,":").replace(/:+/g,":").replace(/^:|:$/g,"")||""}function Hp(...t){return He(t.join(":"))}function wi(t){return t=He(t),t?t+":":""}function Wp(t,e){if(e===void 0)return!0;let r=0,n=t.indexOf(":");for(;n>-1;)r++,n=t.indexOf(":",n+1);return r<=e}function Gp(t,e){return e?t.startsWith(e)&&t[t.length-1]!=="$":t[t.length-1]!=="$"}function i8(t){return t}const Yp="memory",Zp=()=>{const t=new Map;return{name:Yp,getInstance:()=>t,hasItem(e){return t.has(e)},getItem(e){return t.get(e)??null},getItemRaw(e){return t.get(e)??null},setItem(e,r){t.set(e,r)},setItemRaw(e,r){t.set(e,r)},removeItem(e){t.delete(e)},getKeys(){return[...t.keys()]},clear(){t.clear()},dispose(){t.clear()}}};function Xp(t={}){const e={mounts:{"":t.driver||Zp()},mountpoints:[""],watching:!1,watchListeners:[],unwatch:{}},r=u=>{for(const l of e.mountpoints)if(u.startsWith(l))return{base:l,relativeKey:u.slice(l.length),driver:e.mounts[l]};return{base:"",relativeKey:u,driver:e.mounts[""]}},n=(u,l)=>e.mountpoints.filter(h=>h.startsWith(u)||l&&u.startsWith(h)).map(h=>({relativeBase:u.length>h.length?u.slice(h.length):void 0,mountpoint:h,driver:e.mounts[h]})),i=(u,l)=>{if(e.watching){l=He(l);for(const h of e.watchListeners)h(u,l)}},s=async()=>{if(!e.watching){e.watching=!0;for(const u in e.mounts)e.unwatch[u]=await gc(e.mounts[u],i,u)}},o=async()=>{if(e.watching){for(const u in e.unwatch)await e.unwatch[u]();e.unwatch={},e.watching=!1}},a=(u,l,h)=>{const d=new Map,p=f=>{let y=d.get(f.base);return y||(y={driver:f.driver,base:f.base,items:[]},d.set(f.base,y)),y};for(const f of u){const y=typeof f=="string",g=He(y?f:f.key),b=y?void 0:f.value,w=y||!f.options?l:{...l,...f.options},m=r(g);p(m).items.push({key:g,value:b,relativeKey:m.relativeKey,options:w})}return Promise.all([...d.values()].map(f=>h(f))).then(f=>f.flat())},c={hasItem(u,l={}){u=He(u);const{relativeKey:h,driver:d}=r(u);return _e(d.hasItem,h,l)},getItem(u,l={}){u=He(u);const{relativeKey:h,driver:d}=r(u);return _e(d.getItem,h,l).then(p=>gi(p))},getItems(u,l={}){return a(u,l,h=>h.driver.getItems?_e(h.driver.getItems,h.items.map(d=>({key:d.relativeKey,options:d.options})),l).then(d=>d.map(p=>({key:Hp(h.base,p.key),value:gi(p.value)}))):Promise.all(h.items.map(d=>_e(h.driver.getItem,d.relativeKey,d.options).then(p=>({key:d.key,value:gi(p)})))))},getItemRaw(u,l={}){u=He(u);const{relativeKey:h,driver:d}=r(u);return d.getItemRaw?_e(d.getItemRaw,h,l):_e(d.getItem,h,l).then(p=>zp(p))},async setItem(u,l,h={}){if(l===void 0)return c.removeItem(u);u=He(u);const{relativeKey:d,driver:p}=r(u);p.setItem&&(await _e(p.setItem,d,yi(l),h),p.watch||i("update",u))},async setItems(u,l){await a(u,l,async h=>{if(h.driver.setItems)return _e(h.driver.setItems,h.items.map(d=>({key:d.relativeKey,value:yi(d.value),options:d.options})),l);h.driver.setItem&&await Promise.all(h.items.map(d=>_e(h.driver.setItem,d.relativeKey,yi(d.value),d.options)))})},async setItemRaw(u,l,h={}){if(l===void 0)return c.removeItem(u,h);u=He(u);const{relativeKey:d,driver:p}=r(u);if(p.setItemRaw)await _e(p.setItemRaw,d,l,h);else if(p.setItem)await _e(p.setItem,d,qp(l),h);else return;p.watch||i("update",u)},async removeItem(u,l={}){typeof l=="boolean"&&(l={removeMeta:l}),u=He(u);const{relativeKey:h,driver:d}=r(u);d.removeItem&&(await _e(d.removeItem,h,l),(l.removeMeta||l.removeMata)&&await _e(d.removeItem,h+"$",l),d.watch||i("remove",u))},async getMeta(u,l={}){typeof l=="boolean"&&(l={nativeOnly:l}),u=He(u);const{relativeKey:h,driver:d}=r(u),p=Object.create(null);if(d.getMeta&&Object.assign(p,await _e(d.getMeta,h,l)),!l.nativeOnly){const f=await _e(d.getItem,h+"$",l).then(y=>gi(y));f&&typeof f=="object"&&(typeof f.atime=="string"&&(f.atime=new Date(f.atime)),typeof f.mtime=="string"&&(f.mtime=new Date(f.mtime)),Object.assign(p,f))}return p},setMeta(u,l,h={}){return this.setItem(u+"$",l,h)},removeMeta(u,l={}){return this.removeItem(u+"$",l)},async getKeys(u,l={}){u=wi(u);const h=n(u,!0);let d=[];const p=[];let f=!0;for(const g of h){g.driver.flags?.maxDepth||(f=!1);const b=await _e(g.driver.getKeys,g.relativeBase,l);for(const w of b){const m=g.mountpoint+He(w);d.some(E=>m.startsWith(E))||p.push(m)}d=[g.mountpoint,...d.filter(w=>!w.startsWith(g.mountpoint))]}const y=l.maxDepth!==void 0&&!f;return p.filter(g=>(!y||Wp(g,l.maxDepth))&&Gp(g,u))},async clear(u,l={}){u=wi(u),await Promise.all(n(u,!1).map(async h=>{if(h.driver.clear)return _e(h.driver.clear,h.relativeBase,l);if(h.driver.removeItem){const d=await h.driver.getKeys(h.relativeBase||"",l);return Promise.all(d.map(p=>h.driver.removeItem(p,l)))}}))},async dispose(){await Promise.all(Object.values(e.mounts).map(u=>yc(u)))},async watch(u){return await s(),e.watchListeners.push(u),async()=>{e.watchListeners=e.watchListeners.filter(l=>l!==u),e.watchListeners.length===0&&await o()}},async unwatch(){e.watchListeners=[],await o()},mount(u,l){if(u=wi(u),u&&e.mounts[u])throw new Error(`already mounted at ${u}`);return u&&(e.mountpoints.push(u),e.mountpoints.sort((h,d)=>d.length-h.length)),e.mounts[u]=l,e.watching&&Promise.resolve(gc(l,i,u)).then(h=>{e.unwatch[u]=h}).catch(console.error),c},async unmount(u,l=!0){u=wi(u),!(!u||!e.mounts[u])&&(e.watching&&u in e.unwatch&&(e.unwatch[u]?.(),delete e.unwatch[u]),l&&await yc(e.mounts[u]),e.mountpoints=e.mountpoints.filter(h=>h!==u),delete e.mounts[u])},getMount(u=""){u=He(u)+":";const l=r(u);return{driver:l.driver,base:l.base}},getMounts(u="",l={}){return u=He(u),n(u,l.parents).map(d=>({driver:d.driver,base:d.mountpoint}))},keys:(u,l={})=>c.getKeys(u,l),get:(u,l={})=>c.getItem(u,l),set:(u,l,h={})=>c.setItem(u,l,h),has:(u,l={})=>c.hasItem(u,l),del:(u,l={})=>c.removeItem(u,l),remove:(u,l={})=>c.removeItem(u,l)};return c}function gc(t,e,r){return t.watch?t.watch((n,i)=>e(n,r+i)):()=>{}}async function yc(t){typeof t.dispose=="function"&&await _e(t.dispose)}function pr(t){return new Promise((e,r)=>{t.oncomplete=t.onsuccess=()=>e(t.result),t.onabort=t.onerror=()=>r(t.error)})}function wc(t,e){const r=indexedDB.open(t);r.onupgradeneeded=()=>r.result.createObjectStore(e);const n=pr(r);return(i,s)=>n.then(o=>s(o.transaction(e,i).objectStore(e)))}let Os;function En(){return Os||(Os=wc("keyval-store","keyval")),Os}function bc(t,e=En()){return e("readonly",r=>pr(r.get(t)))}function Jp(t,e,r=En()){return r("readwrite",n=>(n.put(e,t),pr(n.transaction)))}function Qp(t,e=En()){return e("readwrite",r=>(r.delete(t),pr(r.transaction)))}function eg(t=En()){return t("readwrite",e=>(e.clear(),pr(e.transaction)))}function tg(t,e){return t.openCursor().onsuccess=function(){this.result&&(e(this.result),this.result.continue())},pr(t.transaction)}function rg(t=En()){return t("readonly",e=>{if(e.getAllKeys)return pr(e.getAllKeys());const r=[];return tg(e,n=>r.push(n.key)).then(()=>r)})}const ng=t=>JSON.stringify(t,(e,r)=>typeof r=="bigint"?r.toString()+"n":r),ig=t=>{const e=/([\[:])?(\d{17,}|(?:[9](?:[1-9]07199254740991|0[1-9]7199254740991|00[8-9]199254740991|007[2-9]99254740991|007199[3-9]54740991|0071992[6-9]4740991|00719925[5-9]740991|007199254[8-9]40991|0071992547[5-9]0991|00719925474[1-9]991|00719925474099[2-9])))([,\}\]])/g,r=t.replace(e,'$1"$2n"$3');return JSON.parse(r,(n,i)=>typeof i=="string"&&i.match(/^\d+n$/)?BigInt(i.substring(0,i.length-1)):i)};function kr(t){if(typeof t!="string")throw new Error(`Cannot safe json parse value of type ${typeof t}`);try{return ig(t)}catch{return t}}function gr(t){return typeof t=="string"?t:ng(t)||""}const sg="idb-keyval";var og=(t={})=>{const e=t.base&&t.base.length>0?`${t.base}:`:"",r=i=>e+i;let n;return t.dbName&&t.storeName&&(n=wc(t.dbName,t.storeName)),{name:sg,options:t,async hasItem(i){return!(typeof await bc(r(i),n)>"u")},async getItem(i){return await bc(r(i),n)??null},setItem(i,s){return Jp(r(i),s,n)},removeItem(i){return Qp(r(i),n)},getKeys(){return rg(n)},clear(){return eg(n)}}};const ag="WALLET_CONNECT_V2_INDEXED_DB",cg="keyvaluestorage";class ug{constructor(){this.indexedDb=Xp({driver:og({dbName:ag,storeName:cg})})}async getKeys(){return this.indexedDb.getKeys()}async getEntries(){return(await this.indexedDb.getItems(await this.indexedDb.getKeys())).map(e=>[e.key,e.value])}async getItem(e){const r=await this.indexedDb.getItem(e);if(r!==null)return r}async setItem(e,r){await this.indexedDb.setItem(e,gr(r))}async removeItem(e){await this.indexedDb.removeItem(e)}}var Ds=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},bi={exports:{}};(function(){let t;function e(){}t=e,t.prototype.getItem=function(r){return this.hasOwnProperty(r)?String(this[r]):null},t.prototype.setItem=function(r,n){this[r]=String(n)},t.prototype.removeItem=function(r){delete this[r]},t.prototype.clear=function(){const r=this;Object.keys(r).forEach(function(n){r[n]=void 0,delete r[n]})},t.prototype.key=function(r){return r=r||0,Object.keys(this)[r]},t.prototype.__defineGetter__("length",function(){return Object.keys(this).length}),typeof Ds<"u"&&Ds.localStorage?bi.exports=Ds.localStorage:typeof window<"u"&&window.localStorage?bi.exports=window.localStorage:bi.exports=new e})();function lg(t){var e;return[t[0],kr((e=t[1])!=null?e:"")]}class hg{constructor(){this.localStorage=bi.exports}async getKeys(){return Object.keys(this.localStorage)}async getEntries(){return Object.entries(this.localStorage).map(lg)}async getItem(e){const r=this.localStorage.getItem(e);if(r!==null)return kr(r)}async setItem(e,r){this.localStorage.setItem(e,gr(r))}async removeItem(e){this.localStorage.removeItem(e)}}const dg="wc_storage_version",mc=1,fg=async(t,e,r)=>{const n=dg,i=await e.getItem(n);if(i&&i>=mc){r(e);return}const s=await t.getKeys();if(!s.length){r(e);return}const o=[];for(;s.length;){const a=s.shift();if(!a)continue;const c=a.toLowerCase();if(c.includes("wc@")||c.includes("walletconnect")||c.includes("wc_")||c.includes("wallet_connect")){const u=await t.getItem(a);await e.setItem(a,u),o.push(a)}}await e.setItem(n,mc),r(e),pg(t,o)},pg=async(t,e)=>{e.length&&e.forEach(async r=>{await t.removeItem(r)})};class gg{constructor(){this.initialized=!1,this.setInitialized=r=>{this.storage=r,this.initialized=!0};const e=new hg;this.storage=e;try{const r=new ug;fg(e,r,this.setInitialized)}catch{this.initialized=!0}}async getKeys(){return await this.initialize(),this.storage.getKeys()}async getEntries(){return await this.initialize(),this.storage.getEntries()}async getItem(e){return await this.initialize(),this.storage.getItem(e)}async setItem(e,r){return await this.initialize(),this.storage.setItem(e,r)}async removeItem(e){return await this.initialize(),this.storage.removeItem(e)}async initialize(){this.initialized||await new Promise(e=>{const r=setInterval(()=>{this.initialized&&(clearInterval(r),e())},20)})}}function yg(t){try{return JSON.stringify(t)}catch{return'"[Circular]"'}}var wg=bg;function bg(t,e,r){var n=r&&r.stringify||yg,i=1;if(typeof t=="object"&&t!==null){var s=e.length+i;if(s===1)return t;var o=new Array(s);o[0]=n(t);for(var a=1;a<s;a++)o[a]=n(e[a]);return o.join(" ")}if(typeof t!="string")return t;var c=e.length;if(c===0)return t;for(var u="",l=1-i,h=-1,d=t&&t.length||0,p=0;p<d;){if(t.charCodeAt(p)===37&&p+1<d){switch(h=h>-1?h:0,t.charCodeAt(p+1)){case 100:case 102:if(l>=c||e[l]==null)break;h<p&&(u+=t.slice(h,p)),u+=Number(e[l]),h=p+2,p++;break;case 105:if(l>=c||e[l]==null)break;h<p&&(u+=t.slice(h,p)),u+=Math.floor(Number(e[l])),h=p+2,p++;break;case 79:case 111:case 106:if(l>=c||e[l]===void 0)break;h<p&&(u+=t.slice(h,p));var f=typeof e[l];if(f==="string"){u+="'"+e[l]+"'",h=p+2,p++;break}if(f==="function"){u+=e[l].name||"<anonymous>",h=p+2,p++;break}u+=n(e[l]),h=p+2,p++;break;case 115:if(l>=c)break;h<p&&(u+=t.slice(h,p)),u+=String(e[l]),h=p+2,p++;break;case 37:h<p&&(u+=t.slice(h,p)),u+="%",h=p+2,p++,l--;break}++l}++p}return h===-1?t:(h<d&&(u+=t.slice(h)),u)}const vc=wg;var Pt=mt;const _n=Ag().console||{},mg={mapHttpRequest:vi,mapHttpResponse:vi,wrapRequestSerializer:$s,wrapResponseSerializer:$s,wrapErrorSerializer:$s,req:vi,res:vi,err:Ig};function vg(t,e){return Array.isArray(t)?t.filter(function(n){return n!=="!stdSerializers.err"}):t===!0?Object.keys(e):!1}function mt(t){t=t||{},t.browser=t.browser||{};const e=t.browser.transmit;if(e&&typeof e.send!="function")throw Error("pino: transmit option must have a send function");const r=t.browser.write||_n;t.browser.write&&(t.browser.asObject=!0);const n=t.serializers||{},i=vg(t.browser.serialize,n);let s=t.browser.serialize;Array.isArray(t.browser.serialize)&&t.browser.serialize.indexOf("!stdSerializers.err")>-1&&(s=!1);const o=["error","fatal","warn","info","debug","trace"];typeof r=="function"&&(r.error=r.fatal=r.warn=r.info=r.debug=r.trace=r),t.enabled===!1&&(t.level="silent");const a=t.level||"info",c=Object.create(r);c.log||(c.log=Sn),Object.defineProperty(c,"levelVal",{get:l}),Object.defineProperty(c,"level",{get:h,set:d});const u={transmit:e,serialize:i,asObject:t.browser.asObject,levels:o,timestamp:xg(t)};c.levels=mt.levels,c.level=a,c.setMaxListeners=c.getMaxListeners=c.emit=c.addListener=c.on=c.prependListener=c.once=c.prependOnceListener=c.removeListener=c.removeAllListeners=c.listeners=c.listenerCount=c.eventNames=c.write=c.flush=Sn,c.serializers=n,c._serialize=i,c._stdErrSerialize=s,c.child=p,e&&(c._logEvent=As());function l(){return this.level==="silent"?1/0:this.levels.values[this.level]}function h(){return this._level}function d(f){if(f!=="silent"&&!this.levels.values[f])throw Error("unknown level "+f);this._level=f,jr(u,c,"error","log"),jr(u,c,"fatal","error"),jr(u,c,"warn","error"),jr(u,c,"info","log"),jr(u,c,"debug","log"),jr(u,c,"trace","log")}function p(f,y){if(!f)throw new Error("missing bindings for child Pino");y=y||{},i&&f.serializers&&(y.serializers=f.serializers);const g=y.serializers;if(i&&g){var b=Object.assign({},n,g),w=t.browser.serialize===!0?Object.keys(b):i;delete f.serializers,mi([f],w,b,this._stdErrSerialize)}function m(E){this._childLevel=(E._childLevel|0)+1,this.error=Mr(E,f,"error"),this.fatal=Mr(E,f,"fatal"),this.warn=Mr(E,f,"warn"),this.info=Mr(E,f,"info"),this.debug=Mr(E,f,"debug"),this.trace=Mr(E,f,"trace"),b&&(this.serializers=b,this._serialize=w),e&&(this._logEvent=As([].concat(E._logEvent.bindings,f)))}return m.prototype=this,new m(this)}return c}mt.levels={values:{fatal:60,error:50,warn:40,info:30,debug:20,trace:10},labels:{10:"trace",20:"debug",30:"info",40:"warn",50:"error",60:"fatal"}},mt.stdSerializers=mg,mt.stdTimeFunctions=Object.assign({},{nullTime:Ec,epochTime:_c,unixTime:Og,isoTime:Dg});function jr(t,e,r,n){const i=Object.getPrototypeOf(e);e[r]=e.levelVal>e.levels.values[r]?Sn:i[r]?i[r]:_n[r]||_n[n]||Sn,Eg(t,e,r)}function Eg(t,e,r){!t.transmit&&e[r]===Sn||(e[r]=function(n){return function(){const s=t.timestamp(),o=new Array(arguments.length),a=Object.getPrototypeOf&&Object.getPrototypeOf(this)===_n?_n:this;for(var c=0;c<o.length;c++)o[c]=arguments[c];if(t.serialize&&!t.asObject&&mi(o,this._serialize,this.serializers,this._stdErrSerialize),t.asObject?n.call(a,_g(this,r,o,s)):n.apply(a,o),t.transmit){const u=t.transmit.level||e.level,l=mt.levels.values[u],h=mt.levels.values[r];if(h<l)return;Sg(this,{ts:s,methodLevel:r,methodValue:h,transmitLevel:u,transmitValue:mt.levels.values[t.transmit.level||e.level],send:t.transmit.send,val:e.levelVal},o)}}}(e[r]))}function _g(t,e,r,n){t._serialize&&mi(r,t._serialize,t.serializers,t._stdErrSerialize);const i=r.slice();let s=i[0];const o={};n&&(o.time=n),o.level=mt.levels.values[e];let a=(t._childLevel|0)+1;if(a<1&&(a=1),s!==null&&typeof s=="object"){for(;a--&&typeof i[0]=="object";)Object.assign(o,i.shift());s=i.length?vc(i.shift(),i):void 0}else typeof s=="string"&&(s=vc(i.shift(),i));return s!==void 0&&(o.msg=s),o}function mi(t,e,r,n){for(const i in t)if(n&&t[i]instanceof Error)t[i]=mt.stdSerializers.err(t[i]);else if(typeof t[i]=="object"&&!Array.isArray(t[i]))for(const s in t[i])e&&e.indexOf(s)>-1&&s in r&&(t[i][s]=r[s](t[i][s]))}function Mr(t,e,r){return function(){const n=new Array(1+arguments.length);n[0]=e;for(var i=1;i<n.length;i++)n[i]=arguments[i-1];return t[r].apply(this,n)}}function Sg(t,e,r){const n=e.send,i=e.ts,s=e.methodLevel,o=e.methodValue,a=e.val,c=t._logEvent.bindings;mi(r,t._serialize||Object.keys(t.serializers),t.serializers,t._stdErrSerialize===void 0?!0:t._stdErrSerialize),t._logEvent.ts=i,t._logEvent.messages=r.filter(function(u){return c.indexOf(u)===-1}),t._logEvent.level.label=s,t._logEvent.level.value=o,n(s,t._logEvent,a),t._logEvent=As(c)}function As(t){return{ts:0,messages:[],bindings:t||[],level:{label:"",value:0}}}function Ig(t){const e={type:t.constructor.name,msg:t.message,stack:t.stack};for(const r in t)e[r]===void 0&&(e[r]=t[r]);return e}function xg(t){return typeof t.timestamp=="function"?t.timestamp:t.timestamp===!1?Ec:_c}function vi(){return{}}function $s(t){return t}function Sn(){}function Ec(){return!1}function _c(){return Date.now()}function Og(){return Math.round(Date.now()/1e3)}function Dg(){return new Date(Date.now()).toISOString()}function Ag(){function t(e){return typeof e<"u"&&e}try{return typeof globalThis<"u"||Object.defineProperty(Object.prototype,"globalThis",{get:function(){return delete Object.prototype.globalThis,this.globalThis=this},configurable:!0}),globalThis}catch{return t(self)||t(window)||t(this)||{}}}const $g={level:"info"},In="custom_context",Ts=1e3*1024;class Tg{constructor(e){this.nodeValue=e,this.sizeInBytes=new TextEncoder().encode(this.nodeValue).length,this.next=null}get value(){return this.nodeValue}get size(){return this.sizeInBytes}}class Sc{constructor(e){this.head=null,this.tail=null,this.lengthInNodes=0,this.maxSizeInBytes=e,this.sizeInBytes=0}append(e){const r=new Tg(e);if(r.size>this.maxSizeInBytes)throw new Error(`[LinkedList] Value too big to insert into list: ${e} with size ${r.size}`);for(;this.size+r.size>this.maxSizeInBytes;)this.shift();this.head?(this.tail&&(this.tail.next=r),this.tail=r):(this.head=r,this.tail=r),this.lengthInNodes++,this.sizeInBytes+=r.size}shift(){if(!this.head)return;const e=this.head;this.head=this.head.next,this.head||(this.tail=null),this.lengthInNodes--,this.sizeInBytes-=e.size}toArray(){const e=[];let r=this.head;for(;r!==null;)e.push(r.value),r=r.next;return e}get length(){return this.lengthInNodes}get size(){return this.sizeInBytes}toOrderedArray(){return Array.from(this)}[Symbol.iterator](){let e=this.head;return{next:()=>{if(!e)return{done:!0,value:null};const r=e.value;return e=e.next,{done:!1,value:r}}}}}class Ic{constructor(e,r=Ts){this.level=e??"error",this.levelValue=Pt.levels.values[this.level],this.MAX_LOG_SIZE_IN_BYTES=r,this.logs=new Sc(this.MAX_LOG_SIZE_IN_BYTES)}forwardToConsole(e,r){r===Pt.levels.values.error?console.error(e):r===Pt.levels.values.warn?console.warn(e):r===Pt.levels.values.debug?console.debug(e):r===Pt.levels.values.trace?console.trace(e):console.log(e)}appendToLogs(e){this.logs.append(gr({timestamp:new Date().toISOString(),log:e}));const r=typeof e=="string"?JSON.parse(e).level:e.level;r>=this.levelValue&&this.forwardToConsole(e,r)}getLogs(){return this.logs}clearLogs(){this.logs=new Sc(this.MAX_LOG_SIZE_IN_BYTES)}getLogArray(){return Array.from(this.logs)}logsToBlob(e){const r=this.getLogArray();return r.push(gr({extraMetadata:e})),new Blob(r,{type:"application/json"})}}class Pg{constructor(e,r=Ts){this.baseChunkLogger=new Ic(e,r)}write(e){this.baseChunkLogger.appendToLogs(e)}getLogs(){return this.baseChunkLogger.getLogs()}clearLogs(){this.baseChunkLogger.clearLogs()}getLogArray(){return this.baseChunkLogger.getLogArray()}logsToBlob(e){return this.baseChunkLogger.logsToBlob(e)}downloadLogsBlobInBrowser(e){const r=URL.createObjectURL(this.logsToBlob(e)),n=document.createElement("a");n.href=r,n.download=`walletconnect-logs-${new Date().toISOString()}.txt`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(r)}}class Ng{constructor(e,r=Ts){this.baseChunkLogger=new Ic(e,r)}write(e){this.baseChunkLogger.appendToLogs(e)}getLogs(){return this.baseChunkLogger.getLogs()}clearLogs(){this.baseChunkLogger.clearLogs()}getLogArray(){return this.baseChunkLogger.getLogArray()}logsToBlob(e){return this.baseChunkLogger.logsToBlob(e)}}var Rg=Object.defineProperty,Cg=Object.defineProperties,Bg=Object.getOwnPropertyDescriptors,xc=Object.getOwnPropertySymbols,Fg=Object.prototype.hasOwnProperty,Lg=Object.prototype.propertyIsEnumerable,Oc=(t,e,r)=>e in t?Rg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ei=(t,e)=>{for(var r in e||(e={}))Fg.call(e,r)&&Oc(t,r,e[r]);if(xc)for(var r of xc(e))Lg.call(e,r)&&Oc(t,r,e[r]);return t},_i=(t,e)=>Cg(t,Bg(e));function Ps(t){return _i(Ei({},t),{level:t?.level||$g.level})}function Ug(t,e=In){return t[e]||""}function kg(t,e,r=In){return t[r]=e,t}function We(t,e=In){let r="";return typeof t.bindings>"u"?r=Ug(t,e):r=t.bindings().context||"",r}function jg(t,e,r=In){const n=We(t,r);return n.trim()?`${n}/${e}`:e}function je(t,e,r=In){const n=jg(t,e,r),i=t.child({context:n});return kg(i,n,r)}function Mg(t){var e,r;const n=new Pg((e=t.opts)==null?void 0:e.level,t.maxSizeInBytes);return{logger:Pt(_i(Ei({},t.opts),{level:"trace",browser:_i(Ei({},(r=t.opts)==null?void 0:r.browser),{write:i=>n.write(i)})})),chunkLoggerController:n}}function qg(t){var e;const r=new Ng((e=t.opts)==null?void 0:e.level,t.maxSizeInBytes);return{logger:Pt(_i(Ei({},t.opts),{level:"trace"})),chunkLoggerController:r}}function zg(t){return typeof t.loggerOverride<"u"&&typeof t.loggerOverride!="string"?{logger:t.loggerOverride,chunkLoggerController:null}:typeof window<"u"?Mg(t):qg(t)}var Vg=Object.defineProperty,Kg=(t,e,r)=>e in t?Vg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Dc=(t,e,r)=>Kg(t,typeof e!="symbol"?e+"":e,r);class Hg extends dr{constructor(e){super(),this.opts=e,Dc(this,"protocol","wc"),Dc(this,"version",2)}}var Wg=Object.defineProperty,Gg=(t,e,r)=>e in t?Wg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Yg=(t,e,r)=>Gg(t,typeof e!="symbol"?e+"":e,r);class Zg extends dr{constructor(e,r){super(),this.core=e,this.logger=r,Yg(this,"records",new Map)}}class Xg{constructor(e,r){this.logger=e,this.core=r}}class Jg extends dr{constructor(e,r){super(),this.relayer=e,this.logger=r}}class Qg extends dr{constructor(e){super()}}class ey{constructor(e,r,n,i){this.core=e,this.logger=r,this.name=n}}class ty extends dr{constructor(e,r){super(),this.relayer=e,this.logger=r}}class ry extends dr{constructor(e,r){super(),this.core=e,this.logger=r}}class ny{constructor(e,r,n){this.core=e,this.logger=r,this.store=n}}class iy{constructor(e,r){this.projectId=e,this.logger=r}}class sy{constructor(e,r,n){this.core=e,this.logger=r,this.telemetryEnabled=n}}var oy=Object.defineProperty,ay=(t,e,r)=>e in t?oy(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ac=(t,e,r)=>ay(t,typeof e!="symbol"?e+"":e,r);class cy{constructor(e){this.opts=e,Ac(this,"protocol","wc"),Ac(this,"version",2)}}class uy{constructor(e){this.client=e}}function ly(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function $c(t,...e){if(!ly(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function Tc(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function hy(t,e){$c(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const qr=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Ns=t=>new DataView(t.buffer,t.byteOffset,t.byteLength);function dy(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function Pc(t){return typeof t=="string"&&(t=dy(t)),$c(t),t}class fy{clone(){return this._cloneInto()}}function py(t){const e=n=>t().update(Pc(n)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function Nc(t=32){if(qr&&typeof qr.getRandomValues=="function")return qr.getRandomValues(new Uint8Array(t));if(qr&&typeof qr.randomBytes=="function")return qr.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}function gy(t,e,r,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,n);const i=BigInt(32),s=BigInt(**********),o=Number(r>>i&s),a=Number(r&s),c=n?4:0,u=n?0:4;t.setUint32(e+c,o,n),t.setUint32(e+u,a,n)}class yy extends fy{constructor(e,r,n,i){super(),this.blockLen=e,this.outputLen=r,this.padOffset=n,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=Ns(this.buffer)}update(e){Tc(this);const{view:r,buffer:n,blockLen:i}=this;e=Pc(e);const s=e.length;for(let o=0;o<s;){const a=Math.min(i-this.pos,s-o);if(a===i){const c=Ns(e);for(;i<=s-o;o+=i)this.process(c,o);continue}n.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===i&&(this.process(r,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){Tc(this),hy(e,this),this.finished=!0;const{buffer:r,view:n,blockLen:i,isLE:s}=this;let{pos:o}=this;r[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>i-o&&(this.process(n,0),o=0);for(let h=o;h<i;h++)r[h]=0;gy(n,i-8,BigInt(this.length*8),s),this.process(n,0);const a=Ns(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=c/4,l=this.get();if(u>l.length)throw new Error("_sha2: outputLen bigger than state");for(let h=0;h<u;h++)a.setUint32(4*h,l[h],s)}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const n=e.slice(0,r);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:r,buffer:n,length:i,finished:s,destroyed:o,pos:a}=this;return e.length=i,e.pos=a,e.finished=s,e.destroyed=o,i%r&&e.buffer.set(n),e}}const Si=BigInt(2**32-1),Rs=BigInt(32);function Rc(t,e=!1){return e?{h:Number(t&Si),l:Number(t>>Rs&Si)}:{h:Number(t>>Rs&Si)|0,l:Number(t&Si)|0}}function wy(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:s,l:o}=Rc(t[i],e);[r[i],n[i]]=[s,o]}return[r,n]}const by=(t,e)=>BigInt(t>>>0)<<Rs|BigInt(e>>>0),my=(t,e,r)=>t>>>r,vy=(t,e,r)=>t<<32-r|e>>>r,Ey=(t,e,r)=>t>>>r|e<<32-r,_y=(t,e,r)=>t<<32-r|e>>>r,Sy=(t,e,r)=>t<<64-r|e>>>r-32,Iy=(t,e,r)=>t>>>r-32|e<<64-r,xy=(t,e)=>e,Oy=(t,e)=>t,Dy=(t,e,r)=>t<<r|e>>>32-r,Ay=(t,e,r)=>e<<r|t>>>32-r,$y=(t,e,r)=>e<<r-32|t>>>64-r,Ty=(t,e,r)=>t<<r-32|e>>>64-r;function Py(t,e,r,n){const i=(e>>>0)+(n>>>0);return{h:t+r+(i/2**32|0)|0,l:i|0}}const Ny=(t,e,r)=>(t>>>0)+(e>>>0)+(r>>>0),Ry=(t,e,r,n)=>e+r+n+(t/2**32|0)|0,Cy=(t,e,r,n)=>(t>>>0)+(e>>>0)+(r>>>0)+(n>>>0),By=(t,e,r,n,i)=>e+r+n+i+(t/2**32|0)|0,Fy=(t,e,r,n,i)=>(t>>>0)+(e>>>0)+(r>>>0)+(n>>>0)+(i>>>0),Ly=(t,e,r,n,i,s)=>e+r+n+i+s+(t/2**32|0)|0,H={fromBig:Rc,split:wy,toBig:by,shrSH:my,shrSL:vy,rotrSH:Ey,rotrSL:_y,rotrBH:Sy,rotrBL:Iy,rotr32H:xy,rotr32L:Oy,rotlSH:Dy,rotlSL:Ay,rotlBH:$y,rotlBL:Ty,add:Py,add3L:Ny,add3H:Ry,add4L:Cy,add4H:By,add5H:Ly,add5L:Fy},[Uy,ky]=H.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(t=>BigInt(t))),Gt=new Uint32Array(80),Yt=new Uint32Array(80);class jy extends yy{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:e,Al:r,Bh:n,Bl:i,Ch:s,Cl:o,Dh:a,Dl:c,Eh:u,El:l,Fh:h,Fl:d,Gh:p,Gl:f,Hh:y,Hl:g}=this;return[e,r,n,i,s,o,a,c,u,l,h,d,p,f,y,g]}set(e,r,n,i,s,o,a,c,u,l,h,d,p,f,y,g){this.Ah=e|0,this.Al=r|0,this.Bh=n|0,this.Bl=i|0,this.Ch=s|0,this.Cl=o|0,this.Dh=a|0,this.Dl=c|0,this.Eh=u|0,this.El=l|0,this.Fh=h|0,this.Fl=d|0,this.Gh=p|0,this.Gl=f|0,this.Hh=y|0,this.Hl=g|0}process(e,r){for(let m=0;m<16;m++,r+=4)Gt[m]=e.getUint32(r),Yt[m]=e.getUint32(r+=4);for(let m=16;m<80;m++){const E=Gt[m-15]|0,$=Yt[m-15]|0,O=H.rotrSH(E,$,1)^H.rotrSH(E,$,8)^H.shrSH(E,$,7),S=H.rotrSL(E,$,1)^H.rotrSL(E,$,8)^H.shrSL(E,$,7),T=Gt[m-2]|0,I=Yt[m-2]|0,j=H.rotrSH(T,I,19)^H.rotrBH(T,I,61)^H.shrSH(T,I,6),R=H.rotrSL(T,I,19)^H.rotrBL(T,I,61)^H.shrSL(T,I,6),B=H.add4L(S,R,Yt[m-7],Yt[m-16]),M=H.add4H(B,O,j,Gt[m-7],Gt[m-16]);Gt[m]=M|0,Yt[m]=B|0}let{Ah:n,Al:i,Bh:s,Bl:o,Ch:a,Cl:c,Dh:u,Dl:l,Eh:h,El:d,Fh:p,Fl:f,Gh:y,Gl:g,Hh:b,Hl:w}=this;for(let m=0;m<80;m++){const E=H.rotrSH(h,d,14)^H.rotrSH(h,d,18)^H.rotrBH(h,d,41),$=H.rotrSL(h,d,14)^H.rotrSL(h,d,18)^H.rotrBL(h,d,41),O=h&p^~h&y,S=d&f^~d&g,T=H.add5L(w,$,S,ky[m],Yt[m]),I=H.add5H(T,b,E,O,Uy[m],Gt[m]),j=T|0,R=H.rotrSH(n,i,28)^H.rotrBH(n,i,34)^H.rotrBH(n,i,39),B=H.rotrSL(n,i,28)^H.rotrBL(n,i,34)^H.rotrBL(n,i,39),M=n&s^n&a^s&a,x=i&o^i&c^o&c;b=y|0,w=g|0,y=p|0,g=f|0,p=h|0,f=d|0,{h,l:d}=H.add(u|0,l|0,I|0,j|0),u=a|0,l=c|0,a=s|0,c=o|0,s=n|0,o=i|0;const _=H.add3L(j,B,x);n=H.add3H(_,I,R,M),i=_|0}({h:n,l:i}=H.add(this.Ah|0,this.Al|0,n|0,i|0)),{h:s,l:o}=H.add(this.Bh|0,this.Bl|0,s|0,o|0),{h:a,l:c}=H.add(this.Ch|0,this.Cl|0,a|0,c|0),{h:u,l}=H.add(this.Dh|0,this.Dl|0,u|0,l|0),{h,l:d}=H.add(this.Eh|0,this.El|0,h|0,d|0),{h:p,l:f}=H.add(this.Fh|0,this.Fl|0,p|0,f|0),{h:y,l:g}=H.add(this.Gh|0,this.Gl|0,y|0,g|0),{h:b,l:w}=H.add(this.Hh|0,this.Hl|0,b|0,w|0),this.set(n,i,s,o,a,c,u,l,h,d,p,f,y,g,b,w)}roundClean(){Gt.fill(0),Yt.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}const My=py(()=>new jy);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Cs=BigInt(0),Cc=BigInt(1),qy=BigInt(2);function Bs(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function Fs(t){if(!Bs(t))throw new Error("Uint8Array expected")}function Ls(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const zy=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function Us(t){Fs(t);let e="";for(let r=0;r<t.length;r++)e+=zy[t[r]];return e}function Bc(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?Cs:BigInt("0x"+t)}const Nt={_0:48,_9:57,A:65,F:70,a:97,f:102};function Fc(t){if(t>=Nt._0&&t<=Nt._9)return t-Nt._0;if(t>=Nt.A&&t<=Nt.F)return t-(Nt.A-10);if(t>=Nt.a&&t<=Nt.f)return t-(Nt.a-10)}function Lc(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const n=new Uint8Array(r);for(let i=0,s=0;i<r;i++,s+=2){const o=Fc(t.charCodeAt(s)),a=Fc(t.charCodeAt(s+1));if(o===void 0||a===void 0){const c=t[s]+t[s+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+s)}n[i]=o*16+a}return n}function Vy(t){return Bc(Us(t))}function Ii(t){return Fs(t),Bc(Us(Uint8Array.from(t).reverse()))}function Uc(t,e){return Lc(t.toString(16).padStart(e*2,"0"))}function ks(t,e){return Uc(t,e).reverse()}function Rt(t,e,r){let n;if(typeof e=="string")try{n=Lc(e)}catch(s){throw new Error(t+" must be hex string or Uint8Array, cause: "+s)}else if(Bs(e))n=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const i=n.length;if(typeof r=="number"&&i!==r)throw new Error(t+" of length "+r+" expected, got "+i);return n}function kc(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];Fs(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}const js=t=>typeof t=="bigint"&&Cs<=t;function Ky(t,e,r){return js(t)&&js(e)&&js(r)&&e<=t&&t<r}function xn(t,e,r,n){if(!Ky(e,r,n))throw new Error("expected valid "+t+": "+r+" <= n < "+n+", got "+e)}function Hy(t){let e;for(e=0;t>Cs;t>>=Cc,e+=1);return e}const Wy=t=>(qy<<BigInt(t-1))-Cc,Gy={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||Bs(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function Ms(t,e,r={}){const n=(i,s,o)=>{const a=Gy[s];if(typeof a!="function")throw new Error("invalid validator function");const c=t[i];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(i)+" is invalid. Expected "+s+", got "+c)};for(const[i,s]of Object.entries(e))n(i,s,!1);for(const[i,s]of Object.entries(r))n(i,s,!0);return t}function jc(t){const e=new WeakMap;return(r,...n)=>{const i=e.get(r);if(i!==void 0)return i;const s=t(r,...n);return e.set(r,s),s}}const Se=BigInt(0),de=BigInt(1),yr=BigInt(2),Yy=BigInt(3),qs=BigInt(4),Mc=BigInt(5),qc=BigInt(8);function me(t,e){const r=t%e;return r>=Se?r:e+r}function Zy(t,e,r){if(e<Se)throw new Error("invalid exponent, negatives unsupported");if(r<=Se)throw new Error("invalid modulus");if(r===de)return Se;let n=de;for(;e>Se;)e&de&&(n=n*t%r),t=t*t%r,e>>=de;return n}function vt(t,e,r){let n=t;for(;e-- >Se;)n*=n,n%=r;return n}function zc(t,e){if(t===Se)throw new Error("invert: expected non-zero number");if(e<=Se)throw new Error("invert: expected positive modulus, got "+e);let r=me(t,e),n=e,i=Se,s=de;for(;r!==Se;){const o=n/r,a=n%r,c=i-s*o;n=r,r=a,i=s,s=c}if(n!==de)throw new Error("invert: does not exist");return me(i,e)}function Xy(t){const e=(t-de)/yr;let r,n,i;for(r=t-de,n=0;r%yr===Se;r/=yr,n++);for(i=yr;i<t&&Zy(i,e,t)!==t-de;i++)if(i>1e3)throw new Error("Cannot find square root: likely non-prime P");if(n===1){const o=(t+de)/qs;return function(a,c){const u=a.pow(c,o);if(!a.eql(a.sqr(u),c))throw new Error("Cannot find square root");return u}}const s=(r+de)/yr;return function(o,a){if(o.pow(a,e)===o.neg(o.ONE))throw new Error("Cannot find square root");let c=n,u=o.pow(o.mul(o.ONE,i),r),l=o.pow(a,s),h=o.pow(a,r);for(;!o.eql(h,o.ONE);){if(o.eql(h,o.ZERO))return o.ZERO;let d=1;for(let f=o.sqr(h);d<c&&!o.eql(f,o.ONE);d++)f=o.sqr(f);const p=o.pow(u,de<<BigInt(c-d-1));u=o.sqr(p),l=o.mul(l,p),h=o.mul(h,u),c=d}return l}}function Jy(t){if(t%qs===Yy){const e=(t+de)/qs;return function(r,n){const i=r.pow(n,e);if(!r.eql(r.sqr(i),n))throw new Error("Cannot find square root");return i}}if(t%qc===Mc){const e=(t-Mc)/qc;return function(r,n){const i=r.mul(n,yr),s=r.pow(i,e),o=r.mul(n,s),a=r.mul(r.mul(o,yr),s),c=r.mul(o,r.sub(a,r.ONE));if(!r.eql(r.sqr(c),n))throw new Error("Cannot find square root");return c}}return Xy(t)}const Qy=(t,e)=>(me(t,e)&de)===de,ew=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function tw(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},r=ew.reduce((n,i)=>(n[i]="function",n),e);return Ms(t,r)}function rw(t,e,r){if(r<Se)throw new Error("invalid exponent, negatives unsupported");if(r===Se)return t.ONE;if(r===de)return e;let n=t.ONE,i=e;for(;r>Se;)r&de&&(n=t.mul(n,i)),i=t.sqr(i),r>>=de;return n}function nw(t,e){const r=new Array(e.length),n=e.reduce((s,o,a)=>t.is0(o)?s:(r[a]=s,t.mul(s,o)),t.ONE),i=t.inv(n);return e.reduceRight((s,o,a)=>t.is0(o)?s:(r[a]=t.mul(s,r[a]),t.mul(s,o)),i),r}function Vc(t,e){const r=e!==void 0?e:t.toString(2).length,n=Math.ceil(r/8);return{nBitLength:r,nByteLength:n}}function Kc(t,e,r=!1,n={}){if(t<=Se)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:i,nByteLength:s}=Vc(t,e);if(s>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:r,BITS:i,BYTES:s,MASK:Wy(i),ZERO:Se,ONE:de,create:c=>me(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return Se<=c&&c<t},is0:c=>c===Se,isOdd:c=>(c&de)===de,neg:c=>me(-c,t),eql:(c,u)=>c===u,sqr:c=>me(c*c,t),add:(c,u)=>me(c+u,t),sub:(c,u)=>me(c-u,t),mul:(c,u)=>me(c*u,t),pow:(c,u)=>rw(a,c,u),div:(c,u)=>me(c*zc(u,t),t),sqrN:c=>c*c,addN:(c,u)=>c+u,subN:(c,u)=>c-u,mulN:(c,u)=>c*u,inv:c=>zc(c,t),sqrt:n.sqrt||(c=>(o||(o=Jy(t)),o(a,c))),invertBatch:c=>nw(a,c),cmov:(c,u,l)=>l?u:c,toBytes:c=>r?ks(c,s):Uc(c,s),fromBytes:c=>{if(c.length!==s)throw new Error("Field.fromBytes: expected "+s+" bytes, got "+c.length);return r?Ii(c):Vy(c)}});return Object.freeze(a)}const Hc=BigInt(0),xi=BigInt(1);function zs(t,e){const r=e.negate();return t?r:e}function Wc(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function Vs(t,e){Wc(t,e);const r=Math.ceil(e/t)+1,n=2**(t-1);return{windows:r,windowSize:n}}function iw(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((r,n)=>{if(!(r instanceof e))throw new Error("invalid point at index "+n)})}function sw(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((r,n)=>{if(!e.isValid(r))throw new Error("invalid scalar at index "+n)})}const Ks=new WeakMap,Gc=new WeakMap;function Hs(t){return Gc.get(t)||1}function ow(t,e){return{constTimeNegate:zs,hasPrecomputes(r){return Hs(r)!==1},unsafeLadder(r,n,i=t.ZERO){let s=r;for(;n>Hc;)n&xi&&(i=i.add(s)),s=s.double(),n>>=xi;return i},precomputeWindow(r,n){const{windows:i,windowSize:s}=Vs(n,e),o=[];let a=r,c=a;for(let u=0;u<i;u++){c=a,o.push(c);for(let l=1;l<s;l++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(r,n,i){const{windows:s,windowSize:o}=Vs(r,e);let a=t.ZERO,c=t.BASE;const u=BigInt(2**r-1),l=2**r,h=BigInt(r);for(let d=0;d<s;d++){const p=d*o;let f=Number(i&u);i>>=h,f>o&&(f-=l,i+=xi);const y=p,g=p+Math.abs(f)-1,b=d%2!==0,w=f<0;f===0?c=c.add(zs(b,n[y])):a=a.add(zs(w,n[g]))}return{p:a,f:c}},wNAFUnsafe(r,n,i,s=t.ZERO){const{windows:o,windowSize:a}=Vs(r,e),c=BigInt(2**r-1),u=2**r,l=BigInt(r);for(let h=0;h<o;h++){const d=h*a;if(i===Hc)break;let p=Number(i&c);if(i>>=l,p>a&&(p-=u,i+=xi),p===0)continue;let f=n[d+Math.abs(p)-1];p<0&&(f=f.negate()),s=s.add(f)}return s},getPrecomputes(r,n,i){let s=Ks.get(n);return s||(s=this.precomputeWindow(n,r),r!==1&&Ks.set(n,i(s))),s},wNAFCached(r,n,i){const s=Hs(r);return this.wNAF(s,this.getPrecomputes(s,r,i),n)},wNAFCachedUnsafe(r,n,i,s){const o=Hs(r);return o===1?this.unsafeLadder(r,n,s):this.wNAFUnsafe(o,this.getPrecomputes(o,r,i),n,s)},setWindowSize(r,n){Wc(n,e),Gc.set(r,n),Ks.delete(r)}}}function aw(t,e,r,n){if(iw(r,t),sw(n,e),r.length!==n.length)throw new Error("arrays of points and scalars must have equal length");const i=t.ZERO,s=Hy(BigInt(r.length)),o=s>12?s-3:s>4?s-2:s?2:1,a=(1<<o)-1,c=new Array(a+1).fill(i),u=Math.floor((e.BITS-1)/o)*o;let l=i;for(let h=u;h>=0;h-=o){c.fill(i);for(let p=0;p<n.length;p++){const f=n[p],y=Number(f>>BigInt(h)&BigInt(a));c[y]=c[y].add(r[p])}let d=i;for(let p=c.length-1,f=i;p>0;p--)f=f.add(c[p]),d=d.add(f);if(l=l.add(d),h!==0)for(let p=0;p<o;p++)l=l.double()}return l}function cw(t){return tw(t.Fp),Ms(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...Vc(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}const lt=BigInt(0),Ge=BigInt(1),Oi=BigInt(2),uw=BigInt(8),lw={zip215:!0};function hw(t){const e=cw(t);return Ms(t,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...e})}function dw(t){const e=hw(t),{Fp:r,n,prehash:i,hash:s,randomBytes:o,nByteLength:a,h:c}=e,u=Oi<<BigInt(a*8)-Ge,l=r.create,h=Kc(e.n,e.nBitLength),d=e.uvRatio||((_,v)=>{try{return{isValid:!0,value:r.sqrt(_*r.inv(v))}}catch{return{isValid:!1,value:lt}}}),p=e.adjustScalarBytes||(_=>_),f=e.domain||((_,v,D)=>{if(Ls("phflag",D),v.length||D)throw new Error("Contexts/pre-hash are not supported");return _});function y(_,v){xn("coordinate "+_,v,lt,u)}function g(_){if(!(_ instanceof m))throw new Error("ExtendedPoint expected")}const b=jc((_,v)=>{const{ex:D,ey:P,ez:A}=_,C=_.is0();v==null&&(v=C?uw:r.inv(A));const U=l(D*v),k=l(P*v),q=l(A*v);if(C)return{x:lt,y:Ge};if(q!==Ge)throw new Error("invZ was invalid");return{x:U,y:k}}),w=jc(_=>{const{a:v,d:D}=e;if(_.is0())throw new Error("bad point: ZERO");const{ex:P,ey:A,ez:C,et:U}=_,k=l(P*P),q=l(A*A),z=l(C*C),V=l(z*z),K=l(k*v),ee=l(z*l(K+q)),Y=l(V+l(D*l(k*q)));if(ee!==Y)throw new Error("bad point: equation left != right (1)");const G=l(P*A),ge=l(C*U);if(G!==ge)throw new Error("bad point: equation left != right (2)");return!0});class m{constructor(v,D,P,A){this.ex=v,this.ey=D,this.ez=P,this.et=A,y("x",v),y("y",D),y("z",P),y("t",A),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(v){if(v instanceof m)throw new Error("extended point not allowed");const{x:D,y:P}=v||{};return y("x",D),y("y",P),new m(D,P,Ge,l(D*P))}static normalizeZ(v){const D=r.invertBatch(v.map(P=>P.ez));return v.map((P,A)=>P.toAffine(D[A])).map(m.fromAffine)}static msm(v,D){return aw(m,h,v,D)}_setWindowSize(v){O.setWindowSize(this,v)}assertValidity(){w(this)}equals(v){g(v);const{ex:D,ey:P,ez:A}=this,{ex:C,ey:U,ez:k}=v,q=l(D*k),z=l(C*A),V=l(P*k),K=l(U*A);return q===z&&V===K}is0(){return this.equals(m.ZERO)}negate(){return new m(l(-this.ex),this.ey,this.ez,l(-this.et))}double(){const{a:v}=e,{ex:D,ey:P,ez:A}=this,C=l(D*D),U=l(P*P),k=l(Oi*l(A*A)),q=l(v*C),z=D+P,V=l(l(z*z)-C-U),K=q+U,ee=K-k,Y=q-U,G=l(V*ee),ge=l(K*Y),le=l(V*Y),he=l(ee*K);return new m(G,ge,he,le)}add(v){g(v);const{a:D,d:P}=e,{ex:A,ey:C,ez:U,et:k}=this,{ex:q,ey:z,ez:V,et:K}=v;if(D===BigInt(-1)){const Hf=l((C-A)*(z+q)),Wf=l((C+A)*(z-q)),Ka=l(Wf-Hf);if(Ka===lt)return this.double();const Gf=l(U*Oi*K),Yf=l(k*Oi*V),Zf=Yf+Gf,Xf=Wf+Hf,Jf=Yf-Gf,e8=l(Zf*Ka),t8=l(Xf*Jf),r8=l(Zf*Jf),n8=l(Ka*Xf);return new m(e8,t8,n8,r8)}const ee=l(A*q),Y=l(C*z),G=l(k*P*K),ge=l(U*V),le=l((A+C)*(q+z)-ee-Y),he=ge-G,Te=ge+G,be=l(Y-D*ee),Wt=l(le*he),XD=l(Te*be),JD=l(le*be),QD=l(he*Te);return new m(Wt,XD,QD,JD)}subtract(v){return this.add(v.negate())}wNAF(v){return O.wNAFCached(this,v,m.normalizeZ)}multiply(v){const D=v;xn("scalar",D,Ge,n);const{p:P,f:A}=this.wNAF(D);return m.normalizeZ([P,A])[0]}multiplyUnsafe(v,D=m.ZERO){const P=v;return xn("scalar",P,lt,n),P===lt?$:this.is0()||P===Ge?this:O.wNAFCachedUnsafe(this,P,m.normalizeZ,D)}isSmallOrder(){return this.multiplyUnsafe(c).is0()}isTorsionFree(){return O.unsafeLadder(this,n).is0()}toAffine(v){return b(this,v)}clearCofactor(){const{h:v}=e;return v===Ge?this:this.multiplyUnsafe(v)}static fromHex(v,D=!1){const{d:P,a:A}=e,C=r.BYTES;v=Rt("pointHex",v,C),Ls("zip215",D);const U=v.slice(),k=v[C-1];U[C-1]=k&-129;const q=Ii(U),z=D?u:r.ORDER;xn("pointHex.y",q,lt,z);const V=l(q*q),K=l(V-Ge),ee=l(P*V-A);let{isValid:Y,value:G}=d(K,ee);if(!Y)throw new Error("Point.fromHex: invalid y coordinate");const ge=(G&Ge)===Ge,le=(k&128)!==0;if(!D&&G===lt&&le)throw new Error("Point.fromHex: x=0 and x_0=1");return le!==ge&&(G=l(-G)),m.fromAffine({x:G,y:q})}static fromPrivateKey(v){return I(v).point}toRawBytes(){const{x:v,y:D}=this.toAffine(),P=ks(D,r.BYTES);return P[P.length-1]|=v&Ge?128:0,P}toHex(){return Us(this.toRawBytes())}}m.BASE=new m(e.Gx,e.Gy,Ge,l(e.Gx*e.Gy)),m.ZERO=new m(lt,Ge,Ge,lt);const{BASE:E,ZERO:$}=m,O=ow(m,a*8);function S(_){return me(_,n)}function T(_){return S(Ii(_))}function I(_){const v=r.BYTES;_=Rt("private key",_,v);const D=Rt("hashed private key",s(_),2*v),P=p(D.slice(0,v)),A=D.slice(v,2*v),C=T(P),U=E.multiply(C),k=U.toRawBytes();return{head:P,prefix:A,scalar:C,point:U,pointBytes:k}}function j(_){return I(_).pointBytes}function R(_=new Uint8Array,...v){const D=kc(...v);return T(s(f(D,Rt("context",_),!!i)))}function B(_,v,D={}){_=Rt("message",_),i&&(_=i(_));const{prefix:P,scalar:A,pointBytes:C}=I(v),U=R(D.context,P,_),k=E.multiply(U).toRawBytes(),q=R(D.context,k,C,_),z=S(U+q*A);xn("signature.s",z,lt,n);const V=kc(k,ks(z,r.BYTES));return Rt("result",V,r.BYTES*2)}const M=lw;function x(_,v,D,P=M){const{context:A,zip215:C}=P,U=r.BYTES;_=Rt("signature",_,2*U),v=Rt("message",v),D=Rt("publicKey",D,U),C!==void 0&&Ls("zip215",C),i&&(v=i(v));const k=Ii(_.slice(U,2*U));let q,z,V;try{q=m.fromHex(D,C),z=m.fromHex(_.slice(0,U),C),V=E.multiplyUnsafe(k)}catch{return!1}if(!C&&q.isSmallOrder())return!1;const K=R(A,z.toRawBytes(),q.toRawBytes(),v);return z.add(q.multiplyUnsafe(K)).subtract(V).clearCofactor().equals(m.ZERO)}return E._setWindowSize(8),{CURVE:e,getPublicKey:j,sign:B,verify:x,ExtendedPoint:m,utils:{getExtendedPublicKey:I,randomPrivateKey:()=>o(r.BYTES),precompute(_=8,v=m.BASE){return v._setWindowSize(_),v.multiply(BigInt(3)),v}}}}BigInt(0),BigInt(1);const Ws=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),Yc=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752");BigInt(0);const fw=BigInt(1),Zc=BigInt(2);BigInt(3);const pw=BigInt(5),gw=BigInt(8);function yw(t){const e=BigInt(10),r=BigInt(20),n=BigInt(40),i=BigInt(80),s=Ws,o=t*t%s*t%s,a=vt(o,Zc,s)*o%s,c=vt(a,fw,s)*t%s,u=vt(c,pw,s)*c%s,l=vt(u,e,s)*u%s,h=vt(l,r,s)*l%s,d=vt(h,n,s)*h%s,p=vt(d,i,s)*d%s,f=vt(p,i,s)*d%s,y=vt(f,e,s)*u%s;return{pow_p_5_8:vt(y,Zc,s)*t%s,b2:o}}function ww(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}function bw(t,e){const r=Ws,n=me(e*e*e,r),i=me(n*n*e,r),s=yw(t*i).pow_p_5_8;let o=me(t*n*s,r);const a=me(e*o*o,r),c=o,u=me(o*Yc,r),l=a===t,h=a===me(-t,r),d=a===me(-t*Yc,r);return l&&(o=c),(h||d)&&(o=u),Qy(o,r)&&(o=me(-o,r)),{isValid:l||h,value:o}}const mw=Kc(Ws,void 0,!0),vw={a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:mw,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:gw,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:My,randomBytes:Nc,adjustScalarBytes:ww,uvRatio:bw},Xc=dw(vw),Ew="EdDSA",_w="JWT",Di=".",Ai="base64url",Jc="utf8",Qc="utf8",Sw=":",Iw="did",xw="key",eu="base58btc",Ow="z",Dw="K36",Aw=32;function Gs(t){return globalThis.Buffer!=null?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t}function tu(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?Gs(globalThis.Buffer.allocUnsafe(t)):new Uint8Array(t)}function ru(t,e){e||(e=t.reduce((i,s)=>i+s.length,0));const r=tu(e);let n=0;for(const i of t)r.set(i,n),n+=i.length;return Gs(r)}function $w(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var s=t.charAt(i),o=s.charCodeAt(0);if(r[o]!==255)throw new TypeError(s+" is ambiguous");r[o]=i}var a=t.length,c=t.charAt(0),u=Math.log(a)/Math.log(256),l=Math.log(256)/Math.log(a);function h(f){if(f instanceof Uint8Array||(ArrayBuffer.isView(f)?f=new Uint8Array(f.buffer,f.byteOffset,f.byteLength):Array.isArray(f)&&(f=Uint8Array.from(f))),!(f instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(f.length===0)return"";for(var y=0,g=0,b=0,w=f.length;b!==w&&f[b]===0;)b++,y++;for(var m=(w-b)*l+1>>>0,E=new Uint8Array(m);b!==w;){for(var $=f[b],O=0,S=m-1;($!==0||O<g)&&S!==-1;S--,O++)$+=256*E[S]>>>0,E[S]=$%a>>>0,$=$/a>>>0;if($!==0)throw new Error("Non-zero carry");g=O,b++}for(var T=m-g;T!==m&&E[T]===0;)T++;for(var I=c.repeat(y);T<m;++T)I+=t.charAt(E[T]);return I}function d(f){if(typeof f!="string")throw new TypeError("Expected String");if(f.length===0)return new Uint8Array;var y=0;if(f[y]!==" "){for(var g=0,b=0;f[y]===c;)g++,y++;for(var w=(f.length-y)*u+1>>>0,m=new Uint8Array(w);f[y];){var E=r[f.charCodeAt(y)];if(E===255)return;for(var $=0,O=w-1;(E!==0||$<b)&&O!==-1;O--,$++)E+=a*m[O]>>>0,m[O]=E%256>>>0,E=E/256>>>0;if(E!==0)throw new Error("Non-zero carry");b=$,y++}if(f[y]!==" "){for(var S=w-b;S!==w&&m[S]===0;)S++;for(var T=new Uint8Array(g+(w-S)),I=g;S!==w;)T[I++]=m[S++];return T}}}function p(f){var y=d(f);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:h,decodeUnsafe:d,decode:p}}var Tw=$w,Pw=Tw;const nu=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},Nw=t=>new TextEncoder().encode(t),Rw=t=>new TextDecoder().decode(t);class Cw{constructor(e,r,n){this.name=e,this.prefix=r,this.baseEncode=n}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class Bw{constructor(e,r,n){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=n}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return iu(this,e)}}class Fw{constructor(e){this.decoders=e}or(e){return iu(this,e)}decode(e){const r=e[0],n=this.decoders[r];if(n)return n.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const iu=(t,e)=>new Fw({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class Lw{constructor(e,r,n,i){this.name=e,this.prefix=r,this.baseEncode=n,this.baseDecode=i,this.encoder=new Cw(e,r,n),this.decoder=new Bw(e,r,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const $i=({name:t,prefix:e,encode:r,decode:n})=>new Lw(t,e,r,n),On=({prefix:t,name:e,alphabet:r})=>{const{encode:n,decode:i}=Pw(r,e);return $i({prefix:t,name:e,encode:n,decode:s=>nu(i(s))})},Uw=(t,e,r,n)=>{const i={};for(let l=0;l<e.length;++l)i[e[l]]=l;let s=t.length;for(;t[s-1]==="=";)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,u=0;for(let l=0;l<s;++l){const h=i[t[l]];if(h===void 0)throw new SyntaxError(`Non-${n} character`);c=c<<r|h,a+=r,a>=8&&(a-=8,o[u++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},kw=(t,e,r)=>{const n=e[e.length-1]==="=",i=(1<<r)-1;let s="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,s+=e[i&a>>o];if(o&&(s+=e[i&a<<r-o]),n)for(;s.length*r&7;)s+="=";return s},Pe=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>$i({prefix:e,name:t,encode(i){return kw(i,n,r)},decode(i){return Uw(i,n,r,t)}}),jw=$i({prefix:"\0",name:"identity",encode:t=>Rw(t),decode:t=>Nw(t)});var Mw=Object.freeze({__proto__:null,identity:jw});const qw=Pe({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var zw=Object.freeze({__proto__:null,base2:qw});const Vw=Pe({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var Kw=Object.freeze({__proto__:null,base8:Vw});const Hw=On({prefix:"9",name:"base10",alphabet:"0123456789"});var Ww=Object.freeze({__proto__:null,base10:Hw});const Gw=Pe({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Yw=Pe({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var Zw=Object.freeze({__proto__:null,base16:Gw,base16upper:Yw});const Xw=Pe({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),Jw=Pe({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),Qw=Pe({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),eb=Pe({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),tb=Pe({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),rb=Pe({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),nb=Pe({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),ib=Pe({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),sb=Pe({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var ob=Object.freeze({__proto__:null,base32:Xw,base32upper:Jw,base32pad:Qw,base32padupper:eb,base32hex:tb,base32hexupper:rb,base32hexpad:nb,base32hexpadupper:ib,base32z:sb});const ab=On({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),cb=On({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var ub=Object.freeze({__proto__:null,base36:ab,base36upper:cb});const lb=On({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),hb=On({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var db=Object.freeze({__proto__:null,base58btc:lb,base58flickr:hb});const fb=Pe({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),pb=Pe({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),gb=Pe({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),yb=Pe({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var wb=Object.freeze({__proto__:null,base64:fb,base64pad:pb,base64url:gb,base64urlpad:yb});const su=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),bb=su.reduce((t,e,r)=>(t[r]=e,t),[]),mb=su.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function vb(t){return t.reduce((e,r)=>(e+=bb[r],e),"")}function Eb(t){const e=[];for(const r of t){const n=mb[r.codePointAt(0)];if(n===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(n)}return new Uint8Array(e)}const _b=$i({prefix:"\u{1F680}",name:"base256emoji",encode:vb,decode:Eb});var Sb=Object.freeze({__proto__:null,base256emoji:_b}),Ib=au,ou=128,xb=127,Ob=~xb,Db=Math.pow(2,31);function au(t,e,r){e=e||[],r=r||0;for(var n=r;t>=Db;)e[r++]=t&255|ou,t/=128;for(;t&Ob;)e[r++]=t&255|ou,t>>>=7;return e[r]=t|0,au.bytes=r-n+1,e}var Ab=Ys,$b=128,cu=127;function Ys(t,n){var r=0,n=n||0,i=0,s=n,o,a=t.length;do{if(s>=a)throw Ys.bytes=0,new RangeError("Could not decode varint");o=t[s++],r+=i<28?(o&cu)<<i:(o&cu)*Math.pow(2,i),i+=7}while(o>=$b);return Ys.bytes=s-n,r}var Tb=Math.pow(2,7),Pb=Math.pow(2,14),Nb=Math.pow(2,21),Rb=Math.pow(2,28),Cb=Math.pow(2,35),Bb=Math.pow(2,42),Fb=Math.pow(2,49),Lb=Math.pow(2,56),Ub=Math.pow(2,63),kb=function(t){return t<Tb?1:t<Pb?2:t<Nb?3:t<Rb?4:t<Cb?5:t<Bb?6:t<Fb?7:t<Lb?8:t<Ub?9:10},jb={encode:Ib,decode:Ab,encodingLength:kb},uu=jb;const lu=(t,e,r=0)=>(uu.encode(t,e,r),e),hu=t=>uu.encodingLength(t),Zs=(t,e)=>{const r=e.byteLength,n=hu(t),i=n+hu(r),s=new Uint8Array(i+r);return lu(t,s,0),lu(r,s,n),s.set(e,i),new Mb(t,r,e,s)};class Mb{constructor(e,r,n,i){this.code=e,this.size=r,this.digest=n,this.bytes=i}}const du=({name:t,code:e,encode:r})=>new qb(t,e,r);class qb{constructor(e,r,n){this.name=e,this.code=r,this.encode=n}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?Zs(this.code,r):r.then(n=>Zs(this.code,n))}else throw Error("Unknown type, must be binary type")}}const fu=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),zb=du({name:"sha2-256",code:18,encode:fu("SHA-256")}),Vb=du({name:"sha2-512",code:19,encode:fu("SHA-512")});var Kb=Object.freeze({__proto__:null,sha256:zb,sha512:Vb});const pu=0,Hb="identity",gu=nu;var Wb=Object.freeze({__proto__:null,identity:{code:pu,name:Hb,encode:gu,digest:t=>Zs(pu,gu(t))}});new TextEncoder,new TextDecoder;const yu={...Mw,...zw,...Kw,...Ww,...Zw,...ob,...ub,...db,...wb,...Sb};({...Kb,...Wb});function wu(t,e,r,n){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:n}}}const bu=wu("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),Xs=wu("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=tu(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),mu={utf8:bu,"utf-8":bu,hex:yu.base16,latin1:Xs,ascii:Xs,binary:Xs,...yu};function Ti(t,e="utf8"){const r=mu[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8"):r.encoder.encode(t).substring(1)}function zr(t,e="utf8"){const r=mu[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?Gs(globalThis.Buffer.from(t,"utf-8")):r.decoder.decode(`${r.prefix}${t}`)}function vu(t){return kr(Ti(zr(t,Ai),Jc))}function Pi(t){return Ti(zr(gr(t),Jc),Ai)}function Eu(t){const e=zr(Dw,eu),r=Ow+Ti(ru([e,t]),eu);return[Iw,xw,r].join(Sw)}function Gb(t){return Ti(t,Ai)}function Yb(t){return zr(t,Ai)}function Zb(t){return zr([Pi(t.header),Pi(t.payload)].join(Di),Qc)}function Xb(t){return[Pi(t.header),Pi(t.payload),Gb(t.signature)].join(Di)}function Js(t){const e=t.split(Di),r=vu(e[0]),n=vu(e[1]),i=Yb(e[2]),s=zr(e.slice(0,2).join(Di),Qc);return{header:r,payload:n,signature:i,data:s}}function _u(t=Nc(Aw)){const e=Xc.getPublicKey(t);return{secretKey:ru([t,e]),publicKey:e}}async function Jb(t,e,r,n,i=F.fromMiliseconds(Date.now())){const s={alg:Ew,typ:_w},o=Eu(n.publicKey),a=i+r,c={iss:o,sub:t,aud:e,iat:i,exp:a},u=Zb({header:s,payload:c}),l=Xc.sign(u,n.secretKey.slice(0,32));return Xb({header:s,payload:c,signature:l})}var Su=function(t,e,r){if(r||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return t.concat(s||Array.prototype.slice.call(e))},Qb=function(){function t(e,r,n){this.name=e,this.version=r,this.os=n,this.type="browser"}return t}(),e0=function(){function t(e){this.version=e,this.type="node",this.name="node",this.os=process.platform}return t}(),t0=function(){function t(e,r,n,i){this.name=e,this.version=r,this.os=n,this.bot=i,this.type="bot-device"}return t}(),r0=function(){function t(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null}return t}(),n0=function(){function t(){this.type="react-native",this.name="react-native",this.version=null,this.os=null}return t}(),i0=/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/,s0=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,Iu=3,o0=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",i0]],xu=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function a0(t){return t?Ou(t):typeof document>"u"&&typeof navigator<"u"&&navigator.product==="ReactNative"?new n0:typeof navigator<"u"?Ou(navigator.userAgent):l0()}function c0(t){return t!==""&&o0.reduce(function(e,r){var n=r[0],i=r[1];if(e)return e;var s=i.exec(t);return!!s&&[n,s]},!1)}function Ou(t){var e=c0(t);if(!e)return null;var r=e[0],n=e[1];if(r==="searchbot")return new r0;var i=n[1]&&n[1].split(".").join("_").split("_").slice(0,3);i?i.length<Iu&&(i=Su(Su([],i,!0),h0(Iu-i.length),!0)):i=[];var s=i.join("."),o=u0(t),a=s0.exec(t);return a&&a[1]?new t0(r,s,o,a[1]):new Qb(r,s,o)}function u0(t){for(var e=0,r=xu.length;e<r;e++){var n=xu[e],i=n[0],s=n[1],o=s.exec(t);if(o)return i}return null}function l0(){var t=typeof process<"u"&&process.version;return t?new e0(process.version.slice(1)):null}function h0(t){for(var e=[],r=0;r<t;r++)e.push("0");return e}var oe={};Object.defineProperty(oe,"__esModule",{value:!0}),oe.getLocalStorage=oe.getLocalStorageOrThrow=oe.getCrypto=oe.getCryptoOrThrow=Du=oe.getLocation=oe.getLocationOrThrow=Qs=oe.getNavigator=oe.getNavigatorOrThrow=br=oe.getDocument=oe.getDocumentOrThrow=oe.getFromWindowOrThrow=oe.getFromWindow=void 0;function wr(t){let e;return typeof window<"u"&&typeof window[t]<"u"&&(e=window[t]),e}oe.getFromWindow=wr;function Vr(t){const e=wr(t);if(!e)throw new Error(`${t} is not defined in Window`);return e}oe.getFromWindowOrThrow=Vr;function d0(){return Vr("document")}oe.getDocumentOrThrow=d0;function f0(){return wr("document")}var br=oe.getDocument=f0;function p0(){return Vr("navigator")}oe.getNavigatorOrThrow=p0;function g0(){return wr("navigator")}var Qs=oe.getNavigator=g0;function y0(){return Vr("location")}oe.getLocationOrThrow=y0;function w0(){return wr("location")}var Du=oe.getLocation=w0;function b0(){return Vr("crypto")}oe.getCryptoOrThrow=b0;function m0(){return wr("crypto")}oe.getCrypto=m0;function v0(){return Vr("localStorage")}oe.getLocalStorageOrThrow=v0;function E0(){return wr("localStorage")}oe.getLocalStorage=E0;var eo={};Object.defineProperty(eo,"__esModule",{value:!0});var Au=eo.getWindowMetadata=void 0;const $u=oe;function _0(){let t,e;try{t=$u.getDocumentOrThrow(),e=$u.getLocationOrThrow()}catch{return null}function r(){const h=t.getElementsByTagName("link"),d=[];for(let p=0;p<h.length;p++){const f=h[p],y=f.getAttribute("rel");if(y&&y.toLowerCase().indexOf("icon")>-1){const g=f.getAttribute("href");if(g)if(g.toLowerCase().indexOf("https:")===-1&&g.toLowerCase().indexOf("http:")===-1&&g.indexOf("//")!==0){let b=e.protocol+"//"+e.host;if(g.indexOf("/")===0)b+=g;else{const w=e.pathname.split("/");w.pop();const m=w.join("/");b+=m+"/"+g}d.push(b)}else if(g.indexOf("//")===0){const b=e.protocol+g;d.push(b)}else d.push(g)}}return d}function n(...h){const d=t.getElementsByTagName("meta");for(let p=0;p<d.length;p++){const f=d[p],y=["itemprop","property","name"].map(g=>f.getAttribute(g)).filter(g=>g?h.includes(g):!1);if(y.length&&y){const g=f.getAttribute("content");if(g)return g}}return""}function i(){let h=n("name","og:site_name","og:title","twitter:title");return h||(h=t.title),h}function s(){return n("description","og:description","twitter:description","keywords")}const o=i(),a=s(),c=e.origin,u=r();return{description:a,url:c,icons:u,name:o}}Au=eo.getWindowMetadata=_0;function Dn(t,{strict:e=!0}={}){return!t||typeof t!="string"?!1:e?/^0x[0-9a-fA-F]*$/.test(t):t.startsWith("0x")}function Tu(t){return Dn(t,{strict:!1})?Math.ceil((t.length-2)/2):t.length}const Pu="2.23.2";let to={getDocsUrl:({docsBaseUrl:t,docsPath:e="",docsSlug:r})=>e?`${t??"https://viem.sh"}${e}${r?`#${r}`:""}`:void 0,version:`viem@${Pu}`};class mr extends Error{constructor(e,r={}){const n=r.cause instanceof mr?r.cause.details:r.cause?.message?r.cause.message:r.details,i=r.cause instanceof mr&&r.cause.docsPath||r.docsPath,s=to.getDocsUrl?.({...r,docsPath:i}),o=[e||"An error occurred.","",...r.metaMessages?[...r.metaMessages,""]:[],...s?[`Docs: ${s}`]:[],...n?[`Details: ${n}`]:[],...to.version?[`Version: ${to.version}`]:[]].join(`
`);super(o,r.cause?{cause:r.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metaMessages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),this.details=n,this.docsPath=i,this.metaMessages=r.metaMessages,this.name=r.name??this.name,this.shortMessage=e,this.version=Pu}walk(e){return Nu(this,e)}}function Nu(t,e){return e?.(t)?t:t&&typeof t=="object"&&"cause"in t&&t.cause!==void 0?Nu(t.cause,e):e?null:t}class Ru extends mr{constructor({size:e,targetSize:r,type:n}){super(`${n.charAt(0).toUpperCase()}${n.slice(1).toLowerCase()} size (${e}) exceeds padding size (${r}).`,{name:"SizeExceedsPaddingSizeError"})}}function Kr(t,{dir:e,size:r=32}={}){return typeof t=="string"?S0(t,{dir:e,size:r}):I0(t,{dir:e,size:r})}function S0(t,{dir:e,size:r=32}={}){if(r===null)return t;const n=t.replace("0x","");if(n.length>r*2)throw new Ru({size:Math.ceil(n.length/2),targetSize:r,type:"hex"});return`0x${n[e==="right"?"padEnd":"padStart"](r*2,"0")}`}function I0(t,{dir:e,size:r=32}={}){if(r===null)return t;if(t.length>r)throw new Ru({size:t.length,targetSize:r,type:"bytes"});const n=new Uint8Array(r);for(let i=0;i<r;i++){const s=e==="right";n[s?i:r-i-1]=t[s?i:t.length-i-1]}return n}class x0 extends mr{constructor({max:e,min:r,signed:n,size:i,value:s}){super(`Number "${s}" is not in safe ${i?`${i*8}-bit ${n?"signed":"unsigned"} `:""}integer range ${e?`(${r} to ${e})`:`(above ${r})`}`,{name:"IntegerOutOfRangeError"})}}class O0 extends mr{constructor({givenSize:e,maxSize:r}){super(`Size cannot exceed ${r} bytes. Given size: ${e} bytes.`,{name:"SizeOverflowError"})}}function Hr(t,{size:e}){if(Tu(t)>e)throw new O0({givenSize:Tu(t),maxSize:e})}function ro(t,e={}){const{signed:r}=e;e.size&&Hr(t,{size:e.size});const n=BigInt(t);if(!r)return n;const i=(t.length-2)/2,s=(1n<<BigInt(i)*8n-1n)-1n;return n<=s?n:n-BigInt(`0x${"f".padStart(i*2,"f")}`)-1n}function D0(t,e={}){return Number(ro(t,e))}const A0=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function no(t,e={}){return typeof t=="number"||typeof t=="bigint"?Bu(t,e):typeof t=="string"?P0(t,e):typeof t=="boolean"?$0(t,e):Cu(t,e)}function $0(t,e={}){const r=`0x${Number(t)}`;return typeof e.size=="number"?(Hr(r,{size:e.size}),Kr(r,{size:e.size})):r}function Cu(t,e={}){let r="";for(let i=0;i<t.length;i++)r+=A0[t[i]];const n=`0x${r}`;return typeof e.size=="number"?(Hr(n,{size:e.size}),Kr(n,{dir:"right",size:e.size})):n}function Bu(t,e={}){const{signed:r,size:n}=e,i=BigInt(t);let s;n?r?s=(1n<<BigInt(n)*8n-1n)-1n:s=2n**(BigInt(n)*8n)-1n:typeof t=="number"&&(s=BigInt(Number.MAX_SAFE_INTEGER));const o=typeof s=="bigint"&&r?-s-1n:0;if(s&&i>s||i<o){const c=typeof t=="bigint"?"n":"";throw new x0({max:s?`${s}${c}`:void 0,min:`${o}${c}`,signed:r,size:n,value:`${t}${c}`})}const a=`0x${(r&&i<0?(1n<<BigInt(n*8))+BigInt(i):i).toString(16)}`;return n?Kr(a,{size:n}):a}const T0=new TextEncoder;function P0(t,e={}){const r=T0.encode(t);return Cu(r,e)}const N0=new TextEncoder;function R0(t,e={}){return typeof t=="number"||typeof t=="bigint"?B0(t,e):typeof t=="boolean"?C0(t,e):Dn(t)?Lu(t,e):Uu(t,e)}function C0(t,e={}){const r=new Uint8Array(1);return r[0]=Number(t),typeof e.size=="number"?(Hr(r,{size:e.size}),Kr(r,{size:e.size})):r}const Ct={zero:48,nine:57,A:65,F:70,a:97,f:102};function Fu(t){if(t>=Ct.zero&&t<=Ct.nine)return t-Ct.zero;if(t>=Ct.A&&t<=Ct.F)return t-(Ct.A-10);if(t>=Ct.a&&t<=Ct.f)return t-(Ct.a-10)}function Lu(t,e={}){let r=t;e.size&&(Hr(r,{size:e.size}),r=Kr(r,{dir:"right",size:e.size}));let n=r.slice(2);n.length%2&&(n=`0${n}`);const i=n.length/2,s=new Uint8Array(i);for(let o=0,a=0;o<i;o++){const c=Fu(n.charCodeAt(a++)),u=Fu(n.charCodeAt(a++));if(c===void 0||u===void 0)throw new mr(`Invalid byte sequence ("${n[a-2]}${n[a-1]}" in "${n}").`);s[o]=c*16+u}return s}function B0(t,e){const r=Bu(t,e);return Lu(r)}function Uu(t,e={}){const r=N0.encode(t);return typeof e.size=="number"?(Hr(r,{size:e.size}),Kr(r,{dir:"right",size:e.size})):r}function Ni(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function F0(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function An(t,...e){if(!F0(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function L0(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Ni(t.outputLen),Ni(t.blockLen)}function Wr(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function ku(t,e){An(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const Ri=BigInt(2**32-1),ju=BigInt(32);function U0(t,e=!1){return e?{h:Number(t&Ri),l:Number(t>>ju&Ri)}:{h:Number(t>>ju&Ri)|0,l:Number(t&Ri)|0}}function k0(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:s,l:o}=U0(t[i],e);[r[i],n[i]]=[s,o]}return[r,n]}const j0=(t,e,r)=>t<<r|e>>>32-r,M0=(t,e,r)=>e<<r|t>>>32-r,q0=(t,e,r)=>e<<r-32|t>>>64-r,z0=(t,e,r)=>t<<r-32|e>>>64-r,Gr=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;function V0(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function io(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function Et(t,e){return t<<32-e|t>>>e}const Mu=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function K0(t){return t<<24&4278190080|t<<8&16711680|t>>>8&65280|t>>>24&255}function qu(t){for(let e=0;e<t.length;e++)t[e]=K0(t[e])}function H0(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function Ci(t){return typeof t=="string"&&(t=H0(t)),An(t),t}function W0(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];An(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}class so{clone(){return this._cloneInto()}}function zu(t){const e=n=>t().update(Ci(n)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function Vu(t=32){if(Gr&&typeof Gr.getRandomValues=="function")return Gr.getRandomValues(new Uint8Array(t));if(Gr&&typeof Gr.randomBytes=="function")return Gr.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}const Ku=[],Hu=[],Wu=[],G0=BigInt(0),$n=BigInt(1),Y0=BigInt(2),Z0=BigInt(7),X0=BigInt(256),J0=BigInt(113);for(let t=0,e=$n,r=1,n=0;t<24;t++){[r,n]=[n,(2*r+3*n)%5],Ku.push(2*(5*n+r)),Hu.push((t+1)*(t+2)/2%64);let i=G0;for(let s=0;s<7;s++)e=(e<<$n^(e>>Z0)*J0)%X0,e&Y0&&(i^=$n<<($n<<BigInt(s))-$n);Wu.push(i)}const[Q0,em]=k0(Wu,!0),Gu=(t,e,r)=>r>32?q0(t,e,r):j0(t,e,r),Yu=(t,e,r)=>r>32?z0(t,e,r):M0(t,e,r);function tm(t,e=24){const r=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let o=0;o<10;o++)r[o]=t[o]^t[o+10]^t[o+20]^t[o+30]^t[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,c=(o+2)%10,u=r[c],l=r[c+1],h=Gu(u,l,1)^r[a],d=Yu(u,l,1)^r[a+1];for(let p=0;p<50;p+=10)t[o+p]^=h,t[o+p+1]^=d}let i=t[2],s=t[3];for(let o=0;o<24;o++){const a=Hu[o],c=Gu(i,s,a),u=Yu(i,s,a),l=Ku[o];i=t[l],s=t[l+1],t[l]=c,t[l+1]=u}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)r[a]=t[o+a];for(let a=0;a<10;a++)t[o+a]^=~r[(a+2)%10]&r[(a+4)%10]}t[0]^=Q0[n],t[1]^=em[n]}r.fill(0)}class oo extends so{constructor(e,r,n,i=!1,s=24){if(super(),this.blockLen=e,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=s,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,Ni(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=V0(this.state)}keccak(){Mu||qu(this.state32),tm(this.state32,this.rounds),Mu||qu(this.state32),this.posOut=0,this.pos=0}update(e){Wr(this);const{blockLen:r,state:n}=this;e=Ci(e);const i=e.length;for(let s=0;s<i;){const o=Math.min(r-this.pos,i-s);for(let a=0;a<o;a++)n[this.pos++]^=e[s++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:r,pos:n,blockLen:i}=this;e[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),e[i-1]^=128,this.keccak()}writeInto(e){Wr(this,!1),An(e),this.finish();const r=this.state,{blockLen:n}=this;for(let i=0,s=e.length;i<s;){this.posOut>=n&&this.keccak();const o=Math.min(n-this.posOut,s-i);e.set(r.subarray(this.posOut,this.posOut+o),i),this.posOut+=o,i+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return Ni(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(ku(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:r,suffix:n,outputLen:i,rounds:s,enableXOF:o}=this;return e||(e=new oo(r,n,i,o,s)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=s,e.suffix=n,e.outputLen=i,e.enableXOF=o,e.destroyed=this.destroyed,e}}const Zu=((t,e,r)=>zu(()=>new oo(e,t,r)))(1,136,256/8);function Xu(t,e){const r=e||"hex",n=Zu(Dn(t,{strict:!1})?R0(t):t);return r==="bytes"?n:no(n)}class Ju extends Map{constructor(e){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=e}get(e){const r=super.get(e);return super.has(e)&&r!==void 0&&(this.delete(e),super.set(e,r)),r}set(e,r){if(super.set(e,r),this.maxSize&&this.size>this.maxSize){const n=this.keys().next().value;n&&this.delete(n)}return this}}const ao=new Ju(8192);function rm(t,e){if(ao.has(`${t}.${e}`))return ao.get(`${t}.${e}`);const r=e?`${e}${t.toLowerCase()}`:t.substring(2).toLowerCase(),n=Xu(Uu(r),"bytes"),i=(e?r.substring(`${e}0x`.length):r).split("");for(let o=0;o<40;o+=2)n[o>>1]>>4>=8&&i[o]&&(i[o]=i[o].toUpperCase()),(n[o>>1]&15)>=8&&i[o+1]&&(i[o+1]=i[o+1].toUpperCase());const s=`0x${i.join("")}`;return ao.set(`${t}.${e}`,s),s}function nm(t){const e=Xu(`0x${t.substring(4)}`).substring(26);return rm(`0x${e}`)}async function im({hash:t,signature:e}){const r=Dn(t)?t:no(t),{secp256k1:n}=await Promise.resolve().then(function(){return U1});return`0x${(()=>{if(typeof e=="object"&&"r"in e&&"s"in e){const{r:u,s:l,v:h,yParity:d}=e,p=Number(d??h),f=Qu(p);return new n.Signature(ro(u),ro(l)).addRecoveryBit(f)}const o=Dn(e)?e:no(e),a=D0(`0x${o.slice(130)}`),c=Qu(a);return n.Signature.fromCompact(o.substring(2,130)).addRecoveryBit(c)})().recoverPublicKey(r.substring(2)).toHex(!1)}`}function Qu(t){if(t===0||t===1)return t;if(t===27)return 0;if(t===28)return 1;throw new Error("Invalid yParityOrV value")}async function sm({hash:t,signature:e}){return nm(await im({hash:t,signature:e}))}function om(t,e,r,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,n);const i=BigInt(32),s=BigInt(**********),o=Number(r>>i&s),a=Number(r&s),c=n?4:0,u=n?0:4;t.setUint32(e+c,o,n),t.setUint32(e+u,a,n)}function am(t,e,r){return t&e^~t&r}function cm(t,e,r){return t&e^t&r^e&r}class um extends so{constructor(e,r,n,i){super(),this.blockLen=e,this.outputLen=r,this.padOffset=n,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=io(this.buffer)}update(e){Wr(this);const{view:r,buffer:n,blockLen:i}=this;e=Ci(e);const s=e.length;for(let o=0;o<s;){const a=Math.min(i-this.pos,s-o);if(a===i){const c=io(e);for(;i<=s-o;o+=i)this.process(c,o);continue}n.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===i&&(this.process(r,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){Wr(this),ku(e,this),this.finished=!0;const{buffer:r,view:n,blockLen:i,isLE:s}=this;let{pos:o}=this;r[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>i-o&&(this.process(n,0),o=0);for(let h=o;h<i;h++)r[h]=0;om(n,i-8,BigInt(this.length*8),s),this.process(n,0);const a=io(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=c/4,l=this.get();if(u>l.length)throw new Error("_sha2: outputLen bigger than state");for(let h=0;h<u;h++)a.setUint32(4*h,l[h],s)}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const n=e.slice(0,r);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:r,buffer:n,length:i,finished:s,destroyed:o,pos:a}=this;return e.length=i,e.pos=a,e.finished=s,e.destroyed=o,i%r&&e.buffer.set(n),e}}const lm=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Zt=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),Xt=new Uint32Array(64);class hm extends um{constructor(){super(64,32,8,!1),this.A=Zt[0]|0,this.B=Zt[1]|0,this.C=Zt[2]|0,this.D=Zt[3]|0,this.E=Zt[4]|0,this.F=Zt[5]|0,this.G=Zt[6]|0,this.H=Zt[7]|0}get(){const{A:e,B:r,C:n,D:i,E:s,F:o,G:a,H:c}=this;return[e,r,n,i,s,o,a,c]}set(e,r,n,i,s,o,a,c){this.A=e|0,this.B=r|0,this.C=n|0,this.D=i|0,this.E=s|0,this.F=o|0,this.G=a|0,this.H=c|0}process(e,r){for(let h=0;h<16;h++,r+=4)Xt[h]=e.getUint32(r,!1);for(let h=16;h<64;h++){const d=Xt[h-15],p=Xt[h-2],f=Et(d,7)^Et(d,18)^d>>>3,y=Et(p,17)^Et(p,19)^p>>>10;Xt[h]=y+Xt[h-7]+f+Xt[h-16]|0}let{A:n,B:i,C:s,D:o,E:a,F:c,G:u,H:l}=this;for(let h=0;h<64;h++){const d=Et(a,6)^Et(a,11)^Et(a,25),p=l+d+am(a,c,u)+lm[h]+Xt[h]|0,y=(Et(n,2)^Et(n,13)^Et(n,22))+cm(n,i,s)|0;l=u,u=c,c=a,a=o+p|0,o=s,s=i,i=n,n=p+y|0}n=n+this.A|0,i=i+this.B|0,s=s+this.C|0,o=o+this.D|0,a=a+this.E|0,c=c+this.F|0,u=u+this.G|0,l=l+this.H|0,this.set(n,i,s,o,a,c,u,l)}roundClean(){Xt.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const Bi=zu(()=>new hm);new Ju(128);const dm=new Uint8Array([7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8]),el=new Uint8Array(new Array(16).fill(0).map((t,e)=>e)),fm=el.map(t=>(9*t+5)%16);let pm=[el],gm=[fm];for(let t=0;t<4;t++)for(let e of[pm,gm])e.push(e[t].map(r=>dm[r]));/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Fi=BigInt(0),Li=BigInt(1),ym=BigInt(2);function vr(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function _t(t){if(!vr(t))throw new Error("Uint8Array expected")}function Yr(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const wm=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function Zr(t){_t(t);let e="";for(let r=0;r<t.length;r++)e+=wm[t[r]];return e}function Xr(t){const e=t.toString(16);return e.length&1?"0"+e:e}function co(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?Fi:BigInt("0x"+t)}const Bt={_0:48,_9:57,A:65,F:70,a:97,f:102};function tl(t){if(t>=Bt._0&&t<=Bt._9)return t-Bt._0;if(t>=Bt.A&&t<=Bt.F)return t-(Bt.A-10);if(t>=Bt.a&&t<=Bt.f)return t-(Bt.a-10)}function Jr(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const n=new Uint8Array(r);for(let i=0,s=0;i<r;i++,s+=2){const o=tl(t.charCodeAt(s)),a=tl(t.charCodeAt(s+1));if(o===void 0||a===void 0){const c=t[s]+t[s+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+s)}n[i]=o*16+a}return n}function St(t){return co(Zr(t))}function uo(t){return _t(t),co(Zr(Uint8Array.from(t).reverse()))}function Jt(t,e){return Jr(t.toString(16).padStart(e*2,"0"))}function lo(t,e){return Jt(t,e).reverse()}function bm(t){return Jr(Xr(t))}function Ue(t,e,r){let n;if(typeof e=="string")try{n=Jr(e)}catch(s){throw new Error(t+" must be hex string or Uint8Array, cause: "+s)}else if(vr(e))n=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const i=n.length;if(typeof r=="number"&&i!==r)throw new Error(t+" of length "+r+" expected, got "+i);return n}function Qe(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];_t(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}function mm(t,e){if(t.length!==e.length)return!1;let r=0;for(let n=0;n<t.length;n++)r|=t[n]^e[n];return r===0}function Ui(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}const ho=t=>typeof t=="bigint"&&Fi<=t;function Qr(t,e,r){return ho(t)&&ho(e)&&ho(r)&&e<=t&&t<r}function Qt(t,e,r,n){if(!Qr(e,r,n))throw new Error("expected valid "+t+": "+r+" <= n < "+n+", got "+e)}function rl(t){let e;for(e=0;t>Fi;t>>=Li,e+=1);return e}function vm(t,e){return t>>BigInt(e)&Li}function Em(t,e,r){return t|(r?Li:Fi)<<BigInt(e)}const fo=t=>(ym<<BigInt(t-1))-Li,po=t=>new Uint8Array(t),nl=t=>Uint8Array.from(t);function il(t,e,r){if(typeof t!="number"||t<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof r!="function")throw new Error("hmacFn must be a function");let n=po(t),i=po(t),s=0;const o=()=>{n.fill(1),i.fill(0),s=0},a=(...h)=>r(i,n,...h),c=(h=po())=>{i=a(nl([0]),h),n=a(),h.length!==0&&(i=a(nl([1]),h),n=a())},u=()=>{if(s++>=1e3)throw new Error("drbg: tried 1000 values");let h=0;const d=[];for(;h<e;){n=a();const p=n.slice();d.push(p),h+=n.length}return Qe(...d)};return(h,d)=>{o(),c(h);let p;for(;!(p=d(u()));)c();return o(),p}}const _m={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||vr(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function en(t,e,r={}){const n=(i,s,o)=>{const a=_m[s];if(typeof a!="function")throw new Error("invalid validator function");const c=t[i];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(i)+" is invalid. Expected "+s+", got "+c)};for(const[i,s]of Object.entries(e))n(i,s,!1);for(const[i,s]of Object.entries(r))n(i,s,!0);return t}const Sm=()=>{throw new Error("not implemented")};function go(t){const e=new WeakMap;return(r,...n)=>{const i=e.get(r);if(i!==void 0)return i;const s=t(r,...n);return e.set(r,s),s}}var Im=Object.freeze({__proto__:null,isBytes:vr,abytes:_t,abool:Yr,bytesToHex:Zr,numberToHexUnpadded:Xr,hexToNumber:co,hexToBytes:Jr,bytesToNumberBE:St,bytesToNumberLE:uo,numberToBytesBE:Jt,numberToBytesLE:lo,numberToVarBytesBE:bm,ensureBytes:Ue,concatBytes:Qe,equalBytes:mm,utf8ToBytes:Ui,inRange:Qr,aInRange:Qt,bitLen:rl,bitGet:vm,bitSet:Em,bitMask:fo,createHmacDrbg:il,validateObject:en,notImplemented:Sm,memoized:go});const xm="0.1.1";function Om(){return xm}class Me extends Error{constructor(e,r={}){const n=(()=>{if(r.cause instanceof Me){if(r.cause.details)return r.cause.details;if(r.cause.shortMessage)return r.cause.shortMessage}return r.cause?.message?r.cause.message:r.details})(),i=r.cause instanceof Me&&r.cause.docsPath||r.docsPath,o=`https://oxlib.sh${i??""}`,a=[e||"An error occurred.",...r.metaMessages?["",...r.metaMessages]:[],...n||i?["",n?`Details: ${n}`:void 0,i?`See: ${o}`:void 0]:[]].filter(c=>typeof c=="string").join(`
`);super(a,r.cause?{cause:r.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:`ox@${Om()}`}),this.cause=r.cause,this.details=n,this.docs=o,this.docsPath=i,this.shortMessage=e}walk(e){return sl(this,e)}}function sl(t,e){return e?.(t)?t:t&&typeof t=="object"&&"cause"in t&&t.cause?sl(t.cause,e):e?null:t}function Dm(t,e){if(cl(t)>e)throw new Bm({givenSize:cl(t),maxSize:e})}const Ft={zero:48,nine:57,A:65,F:70,a:97,f:102};function ol(t){if(t>=Ft.zero&&t<=Ft.nine)return t-Ft.zero;if(t>=Ft.A&&t<=Ft.F)return t-(Ft.A-10);if(t>=Ft.a&&t<=Ft.f)return t-(Ft.a-10)}function Am(t,e={}){const{dir:r,size:n=32}=e;if(n===0)return t;if(t.length>n)throw new Fm({size:t.length,targetSize:n,type:"Bytes"});const i=new Uint8Array(n);for(let s=0;s<n;s++){const o=r==="right";i[o?s:n-s-1]=t[o?s:t.length-s-1]}return i}function yo(t,e){if(bo(t)>e)throw new zm({givenSize:bo(t),maxSize:e})}function al(t,e={}){const{dir:r,size:n=32}=e;if(n===0)return t;const i=t.replace("0x","");if(i.length>n*2)throw new Vm({size:Math.ceil(i.length/2),targetSize:n,type:"Hex"});return`0x${i[r==="right"?"padEnd":"padStart"](n*2,"0")}`}const $m=new TextEncoder;function Tm(t){return t instanceof Uint8Array?t:typeof t=="string"?Nm(t):Pm(t)}function Pm(t){return t instanceof Uint8Array?t:new Uint8Array(t)}function Nm(t,e={}){const{size:r}=e;let n=t;r&&(yo(t,r),n=wo(t,r));let i=n.slice(2);i.length%2&&(i=`0${i}`);const s=i.length/2,o=new Uint8Array(s);for(let a=0,c=0;a<s;a++){const u=ol(i.charCodeAt(c++)),l=ol(i.charCodeAt(c++));if(u===void 0||l===void 0)throw new Me(`Invalid byte sequence ("${i[c-2]}${i[c-1]}" in "${i}").`);o[a]=u*16+l}return o}function Rm(t,e={}){const{size:r}=e,n=$m.encode(t);return typeof r=="number"?(Dm(n,r),Cm(n,r)):n}function Cm(t,e){return Am(t,{dir:"right",size:e})}function cl(t){return t.length}class Bm extends Me{constructor({givenSize:e,maxSize:r}){super(`Size cannot exceed \`${r}\` bytes. Given size: \`${e}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeOverflowError"})}}class Fm extends Me{constructor({size:e,targetSize:r,type:n}){super(`${n.charAt(0).toUpperCase()}${n.slice(1).toLowerCase()} size (\`${e}\`) exceeds padding size (\`${r}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeExceedsPaddingSizeError"})}}const Lm=new TextEncoder,Um=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function ul(...t){return`0x${t.reduce((e,r)=>e+r.replace("0x",""),"")}`}function km(t,e={}){const r=`0x${Number(t)}`;return typeof e.size=="number"?(yo(r,e.size),ki(r,e.size)):r}function ll(t,e={}){let r="";for(let i=0;i<t.length;i++)r+=Um[t[i]];const n=`0x${r}`;return typeof e.size=="number"?(yo(n,e.size),wo(n,e.size)):n}function jm(t,e={}){const{signed:r,size:n}=e,i=BigInt(t);let s;n?r?s=(1n<<BigInt(n)*8n-1n)-1n:s=2n**(BigInt(n)*8n)-1n:typeof t=="number"&&(s=BigInt(Number.MAX_SAFE_INTEGER));const o=typeof s=="bigint"&&r?-s-1n:0;if(s&&i>s||i<o){const u=typeof t=="bigint"?"n":"";throw new qm({max:s?`${s}${u}`:void 0,min:`${o}${u}`,signed:r,size:n,value:`${t}${u}`})}const c=`0x${(r&&i<0?(1n<<BigInt(n*8))+BigInt(i):i).toString(16)}`;return n?ki(c,n):c}function Mm(t,e={}){return ll(Lm.encode(t),e)}function ki(t,e){return al(t,{dir:"left",size:e})}function wo(t,e){return al(t,{dir:"right",size:e})}function bo(t){return Math.ceil((t.length-2)/2)}class qm extends Me{constructor({max:e,min:r,signed:n,size:i,value:s}){super(`Number \`${s}\` is not in safe${i?` ${i*8}-bit`:""}${n?" signed":" unsigned"} integer range ${e?`(\`${r}\` to \`${e}\`)`:`(above \`${r}\`)`}`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.IntegerOutOfRangeError"})}}class zm extends Me{constructor({givenSize:e,maxSize:r}){super(`Size cannot exceed \`${r}\` bytes. Given size: \`${e}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeOverflowError"})}}class Vm extends Me{constructor({size:e,targetSize:r,type:n}){super(`${n.charAt(0).toUpperCase()}${n.slice(1).toLowerCase()} size (\`${e}\`) exceeds padding size (\`${r}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeExceedsPaddingSizeError"})}}function Km(t,e={}){const{as:r=typeof t=="string"?"Hex":"Bytes"}=e,n=Zu(Tm(t));return r==="Bytes"?n:ll(n)}class Hm extends Map{constructor(e){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=e}get(e){const r=super.get(e);return super.has(e)&&r!==void 0&&(this.delete(e),super.set(e,r)),r}set(e,r){if(super.set(e,r),this.maxSize&&this.size>this.maxSize){const n=this.keys().next().value;n&&this.delete(n)}return this}}const mo={checksum:new Hm(8192)}.checksum,Wm=/^0x[a-fA-F0-9]{40}$/;function hl(t,e={}){const{strict:r=!0}=e;if(!Wm.test(t))throw new dl({address:t,cause:new Ym});if(r){if(t.toLowerCase()===t)return;if(Gm(t)!==t)throw new dl({address:t,cause:new Zm})}}function Gm(t){if(mo.has(t))return mo.get(t);hl(t,{strict:!1});const e=t.substring(2).toLowerCase(),r=Km(Rm(e),{as:"Bytes"}),n=e.split("");for(let s=0;s<40;s+=2)r[s>>1]>>4>=8&&n[s]&&(n[s]=n[s].toUpperCase()),(r[s>>1]&15)>=8&&n[s+1]&&(n[s+1]=n[s+1].toUpperCase());const i=`0x${n.join("")}`;return mo.set(t,i),i}class dl extends Me{constructor({address:e,cause:r}){super(`Address "${e}" is invalid.`,{cause:r}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidAddressError"})}}class Ym extends Me{constructor(){super("Address is not a 20 byte (40 hexadecimal character) value."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidInputError"})}}class Zm extends Me{constructor(){super("Address does not match its checksum counterpart."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidChecksumError"})}}const Xm=/^(.*)\[([0-9]*)\]$/,Jm=/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/,Qm=/^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;function vo(t,e){if(t.length!==e.length)throw new t1({expectedLength:t.length,givenLength:e.length});const r=[];for(let n=0;n<t.length;n++){const i=t[n],s=e[n];r.push(vo.encode(i,s))}return ul(...r)}(function(t){function e(r,n,i=!1){if(r==="address"){const c=n;return hl(c),ki(c.toLowerCase(),i?32:0)}if(r==="string")return Mm(n);if(r==="bytes")return n;if(r==="bool")return ki(km(n),i?32:1);const s=r.match(Qm);if(s){const[c,u,l="256"]=s,h=Number.parseInt(l)/8;return jm(n,{size:i?32:h,signed:u==="int"})}const o=r.match(Jm);if(o){const[c,u]=o;if(Number.parseInt(u)!==(n.length-2)/2)throw new e1({expectedSize:Number.parseInt(u),value:n});return wo(n,i?32:0)}const a=r.match(Xm);if(a&&Array.isArray(n)){const[c,u]=a,l=[];for(let h=0;h<n.length;h++)l.push(e(u,n[h],!0));return l.length===0?"0x":ul(...l)}throw new r1(r)}t.encode=e})(vo||(vo={}));class e1 extends Me{constructor({expectedSize:e,value:r}){super(`Size of bytes "${r}" (bytes${bo(r)}) does not match expected size (bytes${e}).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.BytesSizeMismatchError"})}}class t1 extends Me{constructor({expectedLength:e,givenLength:r}){super(["ABI encoding parameters/values length mismatch.",`Expected length (parameters): ${e}`,`Given length (values): ${r}`].join(`
`)),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.LengthMismatchError"})}}class r1 extends Me{constructor(e){super(`Type \`${e}\` is not a valid ABI Type.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.InvalidTypeError"})}}class fl extends so{constructor(e,r){super(),this.finished=!1,this.destroyed=!1,L0(e);const n=Ci(r);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const i=this.blockLen,s=new Uint8Array(i);s.set(n.length>i?e.create().update(n).digest():n);for(let o=0;o<s.length;o++)s[o]^=54;this.iHash.update(s),this.oHash=e.create();for(let o=0;o<s.length;o++)s[o]^=106;this.oHash.update(s),s.fill(0)}update(e){return Wr(this),this.iHash.update(e),this}digestInto(e){Wr(this),An(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:r,iHash:n,finished:i,destroyed:s,blockLen:o,outputLen:a}=this;return e=e,e.finished=i,e.destroyed=s,e.blockLen=o,e.outputLen=a,e.oHash=r._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const pl=(t,e,r)=>new fl(t,e).update(r).digest();pl.create=(t,e)=>new fl(t,e);const Ie=BigInt(0),fe=BigInt(1),Er=BigInt(2),n1=BigInt(3),Eo=BigInt(4),gl=BigInt(5),yl=BigInt(8);function xe(t,e){const r=t%e;return r>=Ie?r:e+r}function i1(t,e,r){if(e<Ie)throw new Error("invalid exponent, negatives unsupported");if(r<=Ie)throw new Error("invalid modulus");if(r===fe)return Ie;let n=fe;for(;e>Ie;)e&fe&&(n=n*t%r),t=t*t%r,e>>=fe;return n}function st(t,e,r){let n=t;for(;e-- >Ie;)n*=n,n%=r;return n}function _o(t,e){if(t===Ie)throw new Error("invert: expected non-zero number");if(e<=Ie)throw new Error("invert: expected positive modulus, got "+e);let r=xe(t,e),n=e,i=Ie,s=fe;for(;r!==Ie;){const a=n/r,c=n%r,u=i-s*a;n=r,r=c,i=s,s=u}if(n!==fe)throw new Error("invert: does not exist");return xe(i,e)}function s1(t){const e=(t-fe)/Er;let r,n,i;for(r=t-fe,n=0;r%Er===Ie;r/=Er,n++);for(i=Er;i<t&&i1(i,e,t)!==t-fe;i++)if(i>1e3)throw new Error("Cannot find square root: likely non-prime P");if(n===1){const o=(t+fe)/Eo;return function(c,u){const l=c.pow(u,o);if(!c.eql(c.sqr(l),u))throw new Error("Cannot find square root");return l}}const s=(r+fe)/Er;return function(a,c){if(a.pow(c,e)===a.neg(a.ONE))throw new Error("Cannot find square root");let u=n,l=a.pow(a.mul(a.ONE,i),r),h=a.pow(c,s),d=a.pow(c,r);for(;!a.eql(d,a.ONE);){if(a.eql(d,a.ZERO))return a.ZERO;let p=1;for(let y=a.sqr(d);p<u&&!a.eql(y,a.ONE);p++)y=a.sqr(y);const f=a.pow(l,fe<<BigInt(u-p-1));l=a.sqr(f),h=a.mul(h,f),d=a.mul(d,l),u=p}return h}}function o1(t){if(t%Eo===n1){const e=(t+fe)/Eo;return function(n,i){const s=n.pow(i,e);if(!n.eql(n.sqr(s),i))throw new Error("Cannot find square root");return s}}if(t%yl===gl){const e=(t-gl)/yl;return function(n,i){const s=n.mul(i,Er),o=n.pow(s,e),a=n.mul(i,o),c=n.mul(n.mul(a,Er),o),u=n.mul(a,n.sub(c,n.ONE));if(!n.eql(n.sqr(u),i))throw new Error("Cannot find square root");return u}}return s1(t)}const a1=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function wl(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},r=a1.reduce((n,i)=>(n[i]="function",n),e);return en(t,r)}function c1(t,e,r){if(r<Ie)throw new Error("invalid exponent, negatives unsupported");if(r===Ie)return t.ONE;if(r===fe)return e;let n=t.ONE,i=e;for(;r>Ie;)r&fe&&(n=t.mul(n,i)),i=t.sqr(i),r>>=fe;return n}function u1(t,e){const r=new Array(e.length),n=e.reduce((s,o,a)=>t.is0(o)?s:(r[a]=s,t.mul(s,o)),t.ONE),i=t.inv(n);return e.reduceRight((s,o,a)=>t.is0(o)?s:(r[a]=t.mul(s,r[a]),t.mul(s,o)),i),r}function bl(t,e){const r=e!==void 0?e:t.toString(2).length,n=Math.ceil(r/8);return{nBitLength:r,nByteLength:n}}function ml(t,e,r=!1,n={}){if(t<=Ie)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:i,nByteLength:s}=bl(t,e);if(s>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:r,BITS:i,BYTES:s,MASK:fo(i),ZERO:Ie,ONE:fe,create:c=>xe(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return Ie<=c&&c<t},is0:c=>c===Ie,isOdd:c=>(c&fe)===fe,neg:c=>xe(-c,t),eql:(c,u)=>c===u,sqr:c=>xe(c*c,t),add:(c,u)=>xe(c+u,t),sub:(c,u)=>xe(c-u,t),mul:(c,u)=>xe(c*u,t),pow:(c,u)=>c1(a,c,u),div:(c,u)=>xe(c*_o(u,t),t),sqrN:c=>c*c,addN:(c,u)=>c+u,subN:(c,u)=>c-u,mulN:(c,u)=>c*u,inv:c=>_o(c,t),sqrt:n.sqrt||(c=>(o||(o=o1(t)),o(a,c))),invertBatch:c=>u1(a,c),cmov:(c,u,l)=>l?u:c,toBytes:c=>r?lo(c,s):Jt(c,s),fromBytes:c=>{if(c.length!==s)throw new Error("Field.fromBytes: expected "+s+" bytes, got "+c.length);return r?uo(c):St(c)}});return Object.freeze(a)}function vl(t){if(typeof t!="bigint")throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function El(t){const e=vl(t);return e+Math.ceil(e/2)}function l1(t,e,r=!1){const n=t.length,i=vl(e),s=El(e);if(n<16||n<s||n>1024)throw new Error("expected "+s+"-1024 bytes of input, got "+n);const o=r?uo(t):St(t),a=xe(o,e-fe)+fe;return r?lo(a,i):Jt(a,i)}const _l=BigInt(0),ji=BigInt(1);function So(t,e){const r=e.negate();return t?r:e}function Sl(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function Io(t,e){Sl(t,e);const r=Math.ceil(e/t)+1,n=2**(t-1);return{windows:r,windowSize:n}}function h1(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((r,n)=>{if(!(r instanceof e))throw new Error("invalid point at index "+n)})}function d1(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((r,n)=>{if(!e.isValid(r))throw new Error("invalid scalar at index "+n)})}const xo=new WeakMap,Il=new WeakMap;function Oo(t){return Il.get(t)||1}function f1(t,e){return{constTimeNegate:So,hasPrecomputes(r){return Oo(r)!==1},unsafeLadder(r,n,i=t.ZERO){let s=r;for(;n>_l;)n&ji&&(i=i.add(s)),s=s.double(),n>>=ji;return i},precomputeWindow(r,n){const{windows:i,windowSize:s}=Io(n,e),o=[];let a=r,c=a;for(let u=0;u<i;u++){c=a,o.push(c);for(let l=1;l<s;l++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(r,n,i){const{windows:s,windowSize:o}=Io(r,e);let a=t.ZERO,c=t.BASE;const u=BigInt(2**r-1),l=2**r,h=BigInt(r);for(let d=0;d<s;d++){const p=d*o;let f=Number(i&u);i>>=h,f>o&&(f-=l,i+=ji);const y=p,g=p+Math.abs(f)-1,b=d%2!==0,w=f<0;f===0?c=c.add(So(b,n[y])):a=a.add(So(w,n[g]))}return{p:a,f:c}},wNAFUnsafe(r,n,i,s=t.ZERO){const{windows:o,windowSize:a}=Io(r,e),c=BigInt(2**r-1),u=2**r,l=BigInt(r);for(let h=0;h<o;h++){const d=h*a;if(i===_l)break;let p=Number(i&c);if(i>>=l,p>a&&(p-=u,i+=ji),p===0)continue;let f=n[d+Math.abs(p)-1];p<0&&(f=f.negate()),s=s.add(f)}return s},getPrecomputes(r,n,i){let s=xo.get(n);return s||(s=this.precomputeWindow(n,r),r!==1&&xo.set(n,i(s))),s},wNAFCached(r,n,i){const s=Oo(r);return this.wNAF(s,this.getPrecomputes(s,r,i),n)},wNAFCachedUnsafe(r,n,i,s){const o=Oo(r);return o===1?this.unsafeLadder(r,n,s):this.wNAFUnsafe(o,this.getPrecomputes(o,r,i),n,s)},setWindowSize(r,n){Sl(n,e),Il.set(r,n),xo.delete(r)}}}function p1(t,e,r,n){if(h1(r,t),d1(n,e),r.length!==n.length)throw new Error("arrays of points and scalars must have equal length");const i=t.ZERO,s=rl(BigInt(r.length)),o=s>12?s-3:s>4?s-2:s?2:1,a=(1<<o)-1,c=new Array(a+1).fill(i),u=Math.floor((e.BITS-1)/o)*o;let l=i;for(let h=u;h>=0;h-=o){c.fill(i);for(let p=0;p<n.length;p++){const f=n[p],y=Number(f>>BigInt(h)&BigInt(a));c[y]=c[y].add(r[p])}let d=i;for(let p=c.length-1,f=i;p>0;p--)f=f.add(c[p]),d=d.add(f);if(l=l.add(d),h!==0)for(let p=0;p<o;p++)l=l.double()}return l}function xl(t){return wl(t.Fp),en(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...bl(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}function Ol(t){t.lowS!==void 0&&Yr("lowS",t.lowS),t.prehash!==void 0&&Yr("prehash",t.prehash)}function g1(t){const e=xl(t);en(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:r,Fp:n,a:i}=e;if(r){if(!n.eql(i,n.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof r!="object"||typeof r.beta!="bigint"||typeof r.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze({...e})}const{bytesToNumberBE:y1,hexToBytes:w1}=Im;class b1 extends Error{constructor(e=""){super(e)}}const Lt={Err:b1,_tlv:{encode:(t,e)=>{const{Err:r}=Lt;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length&1)throw new r("tlv.encode: unpadded data");const n=e.length/2,i=Xr(n);if(i.length/2&128)throw new r("tlv.encode: long form length too big");const s=n>127?Xr(i.length/2|128):"";return Xr(t)+s+i+e},decode(t,e){const{Err:r}=Lt;let n=0;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length<2||e[n++]!==t)throw new r("tlv.decode: wrong tlv");const i=e[n++],s=!!(i&128);let o=0;if(!s)o=i;else{const c=i&127;if(!c)throw new r("tlv.decode(long): indefinite length not supported");if(c>4)throw new r("tlv.decode(long): byte length is too big");const u=e.subarray(n,n+c);if(u.length!==c)throw new r("tlv.decode: length bytes not complete");if(u[0]===0)throw new r("tlv.decode(long): zero leftmost byte");for(const l of u)o=o<<8|l;if(n+=c,o<128)throw new r("tlv.decode(long): not minimal encoding")}const a=e.subarray(n,n+o);if(a.length!==o)throw new r("tlv.decode: wrong value length");return{v:a,l:e.subarray(n+o)}}},_int:{encode(t){const{Err:e}=Lt;if(t<ht)throw new e("integer: negative integers are not allowed");let r=Xr(t);if(Number.parseInt(r[0],16)&8&&(r="00"+r),r.length&1)throw new e("unexpected DER parsing assertion: unpadded hex");return r},decode(t){const{Err:e}=Lt;if(t[0]&128)throw new e("invalid signature integer: negative");if(t[0]===0&&!(t[1]&128))throw new e("invalid signature integer: unnecessary leading zero");return y1(t)}},toSig(t){const{Err:e,_int:r,_tlv:n}=Lt,i=typeof t=="string"?w1(t):t;_t(i);const{v:s,l:o}=n.decode(48,i);if(o.length)throw new e("invalid signature: left bytes after parsing");const{v:a,l:c}=n.decode(2,s),{v:u,l}=n.decode(2,c);if(l.length)throw new e("invalid signature: left bytes after parsing");return{r:r.decode(a),s:r.decode(u)}},hexFromSig(t){const{_tlv:e,_int:r}=Lt,n=e.encode(2,r.encode(t.r)),i=e.encode(2,r.encode(t.s)),s=n+i;return e.encode(48,s)}},ht=BigInt(0),ne=BigInt(1),er=BigInt(2),Mi=BigInt(3),Dl=BigInt(4);function m1(t){const e=g1(t),{Fp:r}=e,n=ml(e.n,e.nBitLength),i=e.toBytes||((y,g,b)=>{const w=g.toAffine();return Qe(Uint8Array.from([4]),r.toBytes(w.x),r.toBytes(w.y))}),s=e.fromBytes||(y=>{const g=y.subarray(1),b=r.fromBytes(g.subarray(0,r.BYTES)),w=r.fromBytes(g.subarray(r.BYTES,2*r.BYTES));return{x:b,y:w}});function o(y){const{a:g,b}=e,w=r.sqr(y),m=r.mul(w,y);return r.add(r.add(m,r.mul(y,g)),b)}if(!r.eql(r.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function a(y){return Qr(y,ne,e.n)}function c(y){const{allowedPrivateKeyLengths:g,nByteLength:b,wrapPrivateKey:w,n:m}=e;if(g&&typeof y!="bigint"){if(vr(y)&&(y=Zr(y)),typeof y!="string"||!g.includes(y.length))throw new Error("invalid private key");y=y.padStart(b*2,"0")}let E;try{E=typeof y=="bigint"?y:St(Ue("private key",y,b))}catch{throw new Error("invalid private key, expected hex or "+b+" bytes, got "+typeof y)}return w&&(E=xe(E,m)),Qt("private key",E,ne,m),E}function u(y){if(!(y instanceof d))throw new Error("ProjectivePoint expected")}const l=go((y,g)=>{const{px:b,py:w,pz:m}=y;if(r.eql(m,r.ONE))return{x:b,y:w};const E=y.is0();g==null&&(g=E?r.ONE:r.inv(m));const $=r.mul(b,g),O=r.mul(w,g),S=r.mul(m,g);if(E)return{x:r.ZERO,y:r.ZERO};if(!r.eql(S,r.ONE))throw new Error("invZ was invalid");return{x:$,y:O}}),h=go(y=>{if(y.is0()){if(e.allowInfinityPoint&&!r.is0(y.py))return;throw new Error("bad point: ZERO")}const{x:g,y:b}=y.toAffine();if(!r.isValid(g)||!r.isValid(b))throw new Error("bad point: x or y not FE");const w=r.sqr(b),m=o(g);if(!r.eql(w,m))throw new Error("bad point: equation left != right");if(!y.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class d{constructor(g,b,w){if(this.px=g,this.py=b,this.pz=w,g==null||!r.isValid(g))throw new Error("x required");if(b==null||!r.isValid(b))throw new Error("y required");if(w==null||!r.isValid(w))throw new Error("z required");Object.freeze(this)}static fromAffine(g){const{x:b,y:w}=g||{};if(!g||!r.isValid(b)||!r.isValid(w))throw new Error("invalid affine point");if(g instanceof d)throw new Error("projective point not allowed");const m=E=>r.eql(E,r.ZERO);return m(b)&&m(w)?d.ZERO:new d(b,w,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(g){const b=r.invertBatch(g.map(w=>w.pz));return g.map((w,m)=>w.toAffine(b[m])).map(d.fromAffine)}static fromHex(g){const b=d.fromAffine(s(Ue("pointHex",g)));return b.assertValidity(),b}static fromPrivateKey(g){return d.BASE.multiply(c(g))}static msm(g,b){return p1(d,n,g,b)}_setWindowSize(g){f.setWindowSize(this,g)}assertValidity(){h(this)}hasEvenY(){const{y:g}=this.toAffine();if(r.isOdd)return!r.isOdd(g);throw new Error("Field doesn't support isOdd")}equals(g){u(g);const{px:b,py:w,pz:m}=this,{px:E,py:$,pz:O}=g,S=r.eql(r.mul(b,O),r.mul(E,m)),T=r.eql(r.mul(w,O),r.mul($,m));return S&&T}negate(){return new d(this.px,r.neg(this.py),this.pz)}double(){const{a:g,b}=e,w=r.mul(b,Mi),{px:m,py:E,pz:$}=this;let O=r.ZERO,S=r.ZERO,T=r.ZERO,I=r.mul(m,m),j=r.mul(E,E),R=r.mul($,$),B=r.mul(m,E);return B=r.add(B,B),T=r.mul(m,$),T=r.add(T,T),O=r.mul(g,T),S=r.mul(w,R),S=r.add(O,S),O=r.sub(j,S),S=r.add(j,S),S=r.mul(O,S),O=r.mul(B,O),T=r.mul(w,T),R=r.mul(g,R),B=r.sub(I,R),B=r.mul(g,B),B=r.add(B,T),T=r.add(I,I),I=r.add(T,I),I=r.add(I,R),I=r.mul(I,B),S=r.add(S,I),R=r.mul(E,$),R=r.add(R,R),I=r.mul(R,B),O=r.sub(O,I),T=r.mul(R,j),T=r.add(T,T),T=r.add(T,T),new d(O,S,T)}add(g){u(g);const{px:b,py:w,pz:m}=this,{px:E,py:$,pz:O}=g;let S=r.ZERO,T=r.ZERO,I=r.ZERO;const j=e.a,R=r.mul(e.b,Mi);let B=r.mul(b,E),M=r.mul(w,$),x=r.mul(m,O),_=r.add(b,w),v=r.add(E,$);_=r.mul(_,v),v=r.add(B,M),_=r.sub(_,v),v=r.add(b,m);let D=r.add(E,O);return v=r.mul(v,D),D=r.add(B,x),v=r.sub(v,D),D=r.add(w,m),S=r.add($,O),D=r.mul(D,S),S=r.add(M,x),D=r.sub(D,S),I=r.mul(j,v),S=r.mul(R,x),I=r.add(S,I),S=r.sub(M,I),I=r.add(M,I),T=r.mul(S,I),M=r.add(B,B),M=r.add(M,B),x=r.mul(j,x),v=r.mul(R,v),M=r.add(M,x),x=r.sub(B,x),x=r.mul(j,x),v=r.add(v,x),B=r.mul(M,v),T=r.add(T,B),B=r.mul(D,v),S=r.mul(_,S),S=r.sub(S,B),B=r.mul(_,M),I=r.mul(D,I),I=r.add(I,B),new d(S,T,I)}subtract(g){return this.add(g.negate())}is0(){return this.equals(d.ZERO)}wNAF(g){return f.wNAFCached(this,g,d.normalizeZ)}multiplyUnsafe(g){const{endo:b,n:w}=e;Qt("scalar",g,ht,w);const m=d.ZERO;if(g===ht)return m;if(this.is0()||g===ne)return this;if(!b||f.hasPrecomputes(this))return f.wNAFCachedUnsafe(this,g,d.normalizeZ);let{k1neg:E,k1:$,k2neg:O,k2:S}=b.splitScalar(g),T=m,I=m,j=this;for(;$>ht||S>ht;)$&ne&&(T=T.add(j)),S&ne&&(I=I.add(j)),j=j.double(),$>>=ne,S>>=ne;return E&&(T=T.negate()),O&&(I=I.negate()),I=new d(r.mul(I.px,b.beta),I.py,I.pz),T.add(I)}multiply(g){const{endo:b,n:w}=e;Qt("scalar",g,ne,w);let m,E;if(b){const{k1neg:$,k1:O,k2neg:S,k2:T}=b.splitScalar(g);let{p:I,f:j}=this.wNAF(O),{p:R,f:B}=this.wNAF(T);I=f.constTimeNegate($,I),R=f.constTimeNegate(S,R),R=new d(r.mul(R.px,b.beta),R.py,R.pz),m=I.add(R),E=j.add(B)}else{const{p:$,f:O}=this.wNAF(g);m=$,E=O}return d.normalizeZ([m,E])[0]}multiplyAndAddUnsafe(g,b,w){const m=d.BASE,E=(O,S)=>S===ht||S===ne||!O.equals(m)?O.multiplyUnsafe(S):O.multiply(S),$=E(this,b).add(E(g,w));return $.is0()?void 0:$}toAffine(g){return l(this,g)}isTorsionFree(){const{h:g,isTorsionFree:b}=e;if(g===ne)return!0;if(b)return b(d,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:g,clearCofactor:b}=e;return g===ne?this:b?b(d,this):this.multiplyUnsafe(e.h)}toRawBytes(g=!0){return Yr("isCompressed",g),this.assertValidity(),i(d,this,g)}toHex(g=!0){return Yr("isCompressed",g),Zr(this.toRawBytes(g))}}d.BASE=new d(e.Gx,e.Gy,r.ONE),d.ZERO=new d(r.ZERO,r.ONE,r.ZERO);const p=e.nBitLength,f=f1(d,e.endo?Math.ceil(p/2):p);return{CURVE:e,ProjectivePoint:d,normPrivateKeyToScalar:c,weierstrassEquation:o,isWithinCurveOrder:a}}function v1(t){const e=xl(t);return en(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}function E1(t){const e=v1(t),{Fp:r,n}=e,i=r.BYTES+1,s=2*r.BYTES+1;function o(x){return xe(x,n)}function a(x){return _o(x,n)}const{ProjectivePoint:c,normPrivateKeyToScalar:u,weierstrassEquation:l,isWithinCurveOrder:h}=m1({...e,toBytes(x,_,v){const D=_.toAffine(),P=r.toBytes(D.x),A=Qe;return Yr("isCompressed",v),v?A(Uint8Array.from([_.hasEvenY()?2:3]),P):A(Uint8Array.from([4]),P,r.toBytes(D.y))},fromBytes(x){const _=x.length,v=x[0],D=x.subarray(1);if(_===i&&(v===2||v===3)){const P=St(D);if(!Qr(P,ne,r.ORDER))throw new Error("Point is not on curve");const A=l(P);let C;try{C=r.sqrt(A)}catch(q){const z=q instanceof Error?": "+q.message:"";throw new Error("Point is not on curve"+z)}const U=(C&ne)===ne;return(v&1)===1!==U&&(C=r.neg(C)),{x:P,y:C}}else if(_===s&&v===4){const P=r.fromBytes(D.subarray(0,r.BYTES)),A=r.fromBytes(D.subarray(r.BYTES,2*r.BYTES));return{x:P,y:A}}else{const P=i,A=s;throw new Error("invalid Point, expected length of "+P+", or uncompressed "+A+", got "+_)}}}),d=x=>Zr(Jt(x,e.nByteLength));function p(x){const _=n>>ne;return x>_}function f(x){return p(x)?o(-x):x}const y=(x,_,v)=>St(x.slice(_,v));class g{constructor(_,v,D){this.r=_,this.s=v,this.recovery=D,this.assertValidity()}static fromCompact(_){const v=e.nByteLength;return _=Ue("compactSignature",_,v*2),new g(y(_,0,v),y(_,v,2*v))}static fromDER(_){const{r:v,s:D}=Lt.toSig(Ue("DER",_));return new g(v,D)}assertValidity(){Qt("r",this.r,ne,n),Qt("s",this.s,ne,n)}addRecoveryBit(_){return new g(this.r,this.s,_)}recoverPublicKey(_){const{r:v,s:D,recovery:P}=this,A=O(Ue("msgHash",_));if(P==null||![0,1,2,3].includes(P))throw new Error("recovery id invalid");const C=P===2||P===3?v+e.n:v;if(C>=r.ORDER)throw new Error("recovery id 2 or 3 invalid");const U=(P&1)===0?"02":"03",k=c.fromHex(U+d(C)),q=a(C),z=o(-A*q),V=o(D*q),K=c.BASE.multiplyAndAddUnsafe(k,z,V);if(!K)throw new Error("point at infinify");return K.assertValidity(),K}hasHighS(){return p(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return Jr(this.toDERHex())}toDERHex(){return Lt.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return Jr(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const b={isValidPrivateKey(x){try{return u(x),!0}catch{return!1}},normPrivateKeyToScalar:u,randomPrivateKey:()=>{const x=El(e.n);return l1(e.randomBytes(x),e.n)},precompute(x=8,_=c.BASE){return _._setWindowSize(x),_.multiply(BigInt(3)),_}};function w(x,_=!0){return c.fromPrivateKey(x).toRawBytes(_)}function m(x){const _=vr(x),v=typeof x=="string",D=(_||v)&&x.length;return _?D===i||D===s:v?D===2*i||D===2*s:x instanceof c}function E(x,_,v=!0){if(m(x))throw new Error("first arg must be private key");if(!m(_))throw new Error("second arg must be public key");return c.fromHex(_).multiply(u(x)).toRawBytes(v)}const $=e.bits2int||function(x){if(x.length>8192)throw new Error("input is too large");const _=St(x),v=x.length*8-e.nBitLength;return v>0?_>>BigInt(v):_},O=e.bits2int_modN||function(x){return o($(x))},S=fo(e.nBitLength);function T(x){return Qt("num < 2^"+e.nBitLength,x,ht,S),Jt(x,e.nByteLength)}function I(x,_,v=j){if(["recovered","canonical"].some(Y=>Y in v))throw new Error("sign() legacy options not supported");const{hash:D,randomBytes:P}=e;let{lowS:A,prehash:C,extraEntropy:U}=v;A==null&&(A=!0),x=Ue("msgHash",x),Ol(v),C&&(x=Ue("prehashed msgHash",D(x)));const k=O(x),q=u(_),z=[T(q),T(k)];if(U!=null&&U!==!1){const Y=U===!0?P(r.BYTES):U;z.push(Ue("extraEntropy",Y))}const V=Qe(...z),K=k;function ee(Y){const G=$(Y);if(!h(G))return;const ge=a(G),le=c.BASE.multiply(G).toAffine(),he=o(le.x);if(he===ht)return;const Te=o(ge*o(K+he*q));if(Te===ht)return;let be=(le.x===he?0:2)|Number(le.y&ne),Wt=Te;return A&&p(Te)&&(Wt=f(Te),be^=1),new g(he,Wt,be)}return{seed:V,k2sig:ee}}const j={lowS:e.lowS,prehash:!1},R={lowS:e.lowS,prehash:!1};function B(x,_,v=j){const{seed:D,k2sig:P}=I(x,_,v),A=e;return il(A.hash.outputLen,A.nByteLength,A.hmac)(D,P)}c.BASE._setWindowSize(8);function M(x,_,v,D=R){const P=x;_=Ue("msgHash",_),v=Ue("publicKey",v);const{lowS:A,prehash:C,format:U}=D;if(Ol(D),"strict"in D)throw new Error("options.strict was renamed to lowS");if(U!==void 0&&U!=="compact"&&U!=="der")throw new Error("format must be compact or der");const k=typeof P=="string"||vr(P),q=!k&&!U&&typeof P=="object"&&P!==null&&typeof P.r=="bigint"&&typeof P.s=="bigint";if(!k&&!q)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let z,V;try{if(q&&(z=new g(P.r,P.s)),k){try{U!=="compact"&&(z=g.fromDER(P))}catch(be){if(!(be instanceof Lt.Err))throw be}!z&&U!=="der"&&(z=g.fromCompact(P))}V=c.fromHex(v)}catch{return!1}if(!z||A&&z.hasHighS())return!1;C&&(_=e.hash(_));const{r:K,s:ee}=z,Y=O(_),G=a(ee),ge=o(Y*G),le=o(K*G),he=c.BASE.multiplyAndAddUnsafe(V,ge,le)?.toAffine();return he?o(he.x)===K:!1}return{CURVE:e,getPublicKey:w,getSharedSecret:E,sign:B,verify:M,ProjectivePoint:c,Signature:g,utils:b}}function _1(t,e){const r=t.ORDER;let n=ht;for(let f=r-ne;f%er===ht;f/=er)n+=ne;const i=n,s=er<<i-ne-ne,o=s*er,a=(r-ne)/o,c=(a-ne)/er,u=o-ne,l=s,h=t.pow(e,a),d=t.pow(e,(a+ne)/er);let p=(f,y)=>{let g=h,b=t.pow(y,u),w=t.sqr(b);w=t.mul(w,y);let m=t.mul(f,w);m=t.pow(m,c),m=t.mul(m,b),b=t.mul(m,y),w=t.mul(m,f);let E=t.mul(w,b);m=t.pow(E,l);let $=t.eql(m,t.ONE);b=t.mul(w,d),m=t.mul(E,g),w=t.cmov(b,w,$),E=t.cmov(m,E,$);for(let O=i;O>ne;O--){let S=O-er;S=er<<S-ne;let T=t.pow(E,S);const I=t.eql(T,t.ONE);b=t.mul(w,g),g=t.mul(g,g),T=t.mul(E,g),w=t.cmov(b,w,I),E=t.cmov(T,E,I)}return{isValid:$,value:w}};if(t.ORDER%Dl===Mi){const f=(t.ORDER-Mi)/Dl,y=t.sqrt(t.neg(e));p=(g,b)=>{let w=t.sqr(b);const m=t.mul(g,b);w=t.mul(w,m);let E=t.pow(w,f);E=t.mul(E,m);const $=t.mul(E,y),O=t.mul(t.sqr(E),b),S=t.eql(O,g);let T=t.cmov($,E,S);return{isValid:S,value:T}}}return p}function S1(t,e){if(wl(t),!t.isValid(e.A)||!t.isValid(e.B)||!t.isValid(e.Z))throw new Error("mapToCurveSimpleSWU: invalid opts");const r=_1(t,e.Z);if(!t.isOdd)throw new Error("Fp.isOdd is not implemented!");return n=>{let i,s,o,a,c,u,l,h;i=t.sqr(n),i=t.mul(i,e.Z),s=t.sqr(i),s=t.add(s,i),o=t.add(s,t.ONE),o=t.mul(o,e.B),a=t.cmov(e.Z,t.neg(s),!t.eql(s,t.ZERO)),a=t.mul(a,e.A),s=t.sqr(o),u=t.sqr(a),c=t.mul(u,e.A),s=t.add(s,c),s=t.mul(s,o),u=t.mul(u,a),c=t.mul(u,e.B),s=t.add(s,c),l=t.mul(i,o);const{isValid:d,value:p}=r(s,u);h=t.mul(i,n),h=t.mul(h,p),l=t.cmov(l,o,d),h=t.cmov(h,p,d);const f=t.isOdd(n)===t.isOdd(h);return h=t.cmov(t.neg(h),h,f),l=t.div(l,a),{x:l,y:h}}}function I1(t){return{hash:t,hmac:(e,...r)=>pl(t,e,W0(...r)),randomBytes:Vu}}function x1(t,e){const r=n=>E1({...t,...I1(n)});return{...r(e),create:r}}const O1=St;function tr(t,e){if(Tn(t),Tn(e),t<0||t>=1<<8*e)throw new Error("invalid I2OSP input: "+t);const r=Array.from({length:e}).fill(0);for(let n=e-1;n>=0;n--)r[n]=t&255,t>>>=8;return new Uint8Array(r)}function D1(t,e){const r=new Uint8Array(t.length);for(let n=0;n<t.length;n++)r[n]=t[n]^e[n];return r}function Tn(t){if(!Number.isSafeInteger(t))throw new Error("number expected")}function A1(t,e,r,n){_t(t),_t(e),Tn(r),e.length>255&&(e=n(Qe(Ui("H2C-OVERSIZE-DST-"),e)));const{outputLen:i,blockLen:s}=n,o=Math.ceil(r/i);if(r>65535||o>255)throw new Error("expand_message_xmd: invalid lenInBytes");const a=Qe(e,tr(e.length,1)),c=tr(0,s),u=tr(r,2),l=new Array(o),h=n(Qe(c,t,u,tr(0,1),a));l[0]=n(Qe(h,tr(1,1),a));for(let p=1;p<=o;p++){const f=[D1(h,l[p-1]),tr(p+1,1),a];l[p]=n(Qe(...f))}return Qe(...l).slice(0,r)}function $1(t,e,r,n,i){if(_t(t),_t(e),Tn(r),e.length>255){const s=Math.ceil(2*n/8);e=i.create({dkLen:s}).update(Ui("H2C-OVERSIZE-DST-")).update(e).digest()}if(r>65535||e.length>255)throw new Error("expand_message_xof: invalid lenInBytes");return i.create({dkLen:r}).update(t).update(tr(r,2)).update(e).update(tr(e.length,1)).digest()}function Al(t,e,r){en(r,{DST:"stringOrUint8Array",p:"bigint",m:"isSafeInteger",k:"isSafeInteger",hash:"hash"});const{p:n,k:i,m:s,hash:o,expand:a,DST:c}=r;_t(t),Tn(e);const u=typeof c=="string"?Ui(c):c,l=n.toString(2).length,h=Math.ceil((l+i)/8),d=e*s*h;let p;if(a==="xmd")p=A1(t,u,d,o);else if(a==="xof")p=$1(t,u,d,i,o);else if(a==="_internal_pass")p=t;else throw new Error('expand must be "xmd" or "xof"');const f=new Array(e);for(let y=0;y<e;y++){const g=new Array(s);for(let b=0;b<s;b++){const w=h*(b+y*s),m=p.subarray(w,w+h);g[b]=xe(O1(m),n)}f[y]=g}return f}function T1(t,e){const r=e.map(n=>Array.from(n).reverse());return(n,i)=>{const[s,o,a,c]=r.map(u=>u.reduce((l,h)=>t.add(t.mul(l,n),h)));return n=t.div(s,o),i=t.mul(i,t.div(a,c)),{x:n,y:i}}}function P1(t,e,r){if(typeof e!="function")throw new Error("mapToCurve() must be defined");return{hashToCurve(n,i){const s=Al(n,2,{...r,DST:r.DST,...i}),o=t.fromAffine(e(s[0])),a=t.fromAffine(e(s[1])),c=o.add(a).clearCofactor();return c.assertValidity(),c},encodeToCurve(n,i){const s=Al(n,1,{...r,DST:r.encodeDST,...i}),o=t.fromAffine(e(s[0])).clearCofactor();return o.assertValidity(),o},mapToCurve(n){if(!Array.isArray(n))throw new Error("mapToCurve: expected array of bigints");for(const s of n)if(typeof s!="bigint")throw new Error("mapToCurve: expected array of bigints");const i=t.fromAffine(e(n)).clearCofactor();return i.assertValidity(),i}}}const Pn=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),qi=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),Nn=BigInt(1),zi=BigInt(2),$l=(t,e)=>(t+e/zi)/e;function Tl(t){const e=Pn,r=BigInt(3),n=BigInt(6),i=BigInt(11),s=BigInt(22),o=BigInt(23),a=BigInt(44),c=BigInt(88),u=t*t*t%e,l=u*u*t%e,h=st(l,r,e)*l%e,d=st(h,r,e)*l%e,p=st(d,zi,e)*u%e,f=st(p,i,e)*p%e,y=st(f,s,e)*f%e,g=st(y,a,e)*y%e,b=st(g,c,e)*g%e,w=st(b,a,e)*y%e,m=st(w,r,e)*l%e,E=st(m,o,e)*f%e,$=st(E,n,e)*u%e,O=st($,zi,e);if(!rr.eql(rr.sqr(O),t))throw new Error("Cannot find square root");return O}const rr=ml(Pn,void 0,void 0,{sqrt:Tl}),Rn=x1({a:BigInt(0),b:BigInt(7),Fp:rr,n:qi,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:t=>{const e=qi,r=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),n=-Nn*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),s=r,o=BigInt("0x100000000000000000000000000000000"),a=$l(s*t,e),c=$l(-n*t,e);let u=xe(t-a*r-c*i,e),l=xe(-a*n-c*s,e);const h=u>o,d=l>o;if(h&&(u=e-u),d&&(l=e-l),u>o||l>o)throw new Error("splitScalar: Endomorphism failed, k="+t);return{k1neg:h,k1:u,k2neg:d,k2:l}}}},Bi),Pl=BigInt(0),Nl={};function Vi(t,...e){let r=Nl[t];if(r===void 0){const n=Bi(Uint8Array.from(t,i=>i.charCodeAt(0)));r=Qe(n,n),Nl[t]=r}return Bi(Qe(r,...e))}const Do=t=>t.toRawBytes(!0).slice(1),Ao=t=>Jt(t,32),$o=t=>xe(t,Pn),Cn=t=>xe(t,qi),To=Rn.ProjectivePoint,N1=(t,e,r)=>To.BASE.multiplyAndAddUnsafe(t,e,r);function Po(t){let e=Rn.utils.normPrivateKeyToScalar(t),r=To.fromPrivateKey(e);return{scalar:r.hasEvenY()?e:Cn(-e),bytes:Do(r)}}function Rl(t){Qt("x",t,Nn,Pn);const e=$o(t*t),r=$o(e*t+BigInt(7));let n=Tl(r);n%zi!==Pl&&(n=$o(-n));const i=new To(t,n,Nn);return i.assertValidity(),i}const tn=St;function Cl(...t){return Cn(tn(Vi("BIP0340/challenge",...t)))}function R1(t){return Po(t).bytes}function C1(t,e,r=Vu(32)){const n=Ue("message",t),{bytes:i,scalar:s}=Po(e),o=Ue("auxRand",r,32),a=Ao(s^tn(Vi("BIP0340/aux",o))),c=Vi("BIP0340/nonce",a,i,n),u=Cn(tn(c));if(u===Pl)throw new Error("sign failed: k is zero");const{bytes:l,scalar:h}=Po(u),d=Cl(l,i,n),p=new Uint8Array(64);if(p.set(l,0),p.set(Ao(Cn(h+d*s)),32),!Bl(p,n,i))throw new Error("sign: Invalid signature produced");return p}function Bl(t,e,r){const n=Ue("signature",t,64),i=Ue("message",e),s=Ue("publicKey",r,32);try{const o=Rl(tn(s)),a=tn(n.subarray(0,32));if(!Qr(a,Nn,Pn))return!1;const c=tn(n.subarray(32,64));if(!Qr(c,Nn,qi))return!1;const u=Cl(Ao(a),Do(o),i),l=N1(o,c,Cn(-u));return!(!l||!l.hasEvenY()||l.toAffine().x!==a)}catch{return!1}}const B1={getPublicKey:R1,sign:C1,verify:Bl,utils:{randomPrivateKey:Rn.utils.randomPrivateKey,lift_x:Rl,pointToBytes:Do,numberToBytesBE:Jt,bytesToNumberBE:St,taggedHash:Vi,mod:xe}},F1=T1(rr,[["0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7","0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581","0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262","0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c"],["0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b","0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14","0x0000000000000000000000000000000000000000000000000000000000000001"],["0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c","0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3","0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931","0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84"],["0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b","0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573","0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f","0x0000000000000000000000000000000000000000000000000000000000000001"]].map(t=>t.map(e=>BigInt(e)))),L1=S1(rr,{A:BigInt("0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533"),B:BigInt("1771"),Z:rr.create(BigInt("-11"))}),Fl=P1(Rn.ProjectivePoint,t=>{const{x:e,y:r}=L1(rr.create(t[0]));return F1(e,r)},{DST:"secp256k1_XMD:SHA-256_SSWU_RO_",encodeDST:"secp256k1_XMD:SHA-256_SSWU_NU_",p:rr.ORDER,m:1,k:128,expand:"xmd",hash:Bi});var U1=Object.freeze({__proto__:null,secp256k1:Rn,schnorr:B1,hashToCurve:Fl.hashToCurve,encodeToCurve:Fl.encodeToCurve});function k1(t){if(t.length>=255)throw new TypeError("Alphabet too long");const e=new Uint8Array(256);for(let u=0;u<e.length;u++)e[u]=255;for(let u=0;u<t.length;u++){const l=t.charAt(u),h=l.charCodeAt(0);if(e[h]!==255)throw new TypeError(l+" is ambiguous");e[h]=u}const r=t.length,n=t.charAt(0),i=Math.log(r)/Math.log(256),s=Math.log(256)/Math.log(r);function o(u){if(u instanceof Uint8Array||(ArrayBuffer.isView(u)?u=new Uint8Array(u.buffer,u.byteOffset,u.byteLength):Array.isArray(u)&&(u=Uint8Array.from(u))),!(u instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(u.length===0)return"";let l=0,h=0,d=0;const p=u.length;for(;d!==p&&u[d]===0;)d++,l++;const f=(p-d)*s+1>>>0,y=new Uint8Array(f);for(;d!==p;){let w=u[d],m=0;for(let E=f-1;(w!==0||m<h)&&E!==-1;E--,m++)w+=256*y[E]>>>0,y[E]=w%r>>>0,w=w/r>>>0;if(w!==0)throw new Error("Non-zero carry");h=m,d++}let g=f-h;for(;g!==f&&y[g]===0;)g++;let b=n.repeat(l);for(;g<f;++g)b+=t.charAt(y[g]);return b}function a(u){if(typeof u!="string")throw new TypeError("Expected String");if(u.length===0)return new Uint8Array;let l=0,h=0,d=0;for(;u[l]===n;)h++,l++;const p=(u.length-l)*i+1>>>0,f=new Uint8Array(p);for(;l<u.length;){const w=u.charCodeAt(l);if(w>255)return;let m=e[w];if(m===255)return;let E=0;for(let $=p-1;(m!==0||E<d)&&$!==-1;$--,E++)m+=r*f[$]>>>0,f[$]=m%256>>>0,m=m/256>>>0;if(m!==0)throw new Error("Non-zero carry");d=E,l++}let y=p-d;for(;y!==p&&f[y]===0;)y++;const g=new Uint8Array(h+(p-y));let b=h;for(;y!==p;)g[b++]=f[y++];return g}function c(u){const l=a(u);if(l)return l;throw new Error("Non-base"+r+" character")}return{encode:o,decodeUnsafe:a,decode:c}}var j1="**********************************************************",M1=k1(j1);function Ll(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}function Bn(t,e){e||(e=t.reduce((i,s)=>i+s.length,0));const r=Ll(e);let n=0;for(const i of t)r.set(i,n),n+=i.length;return r}function q1(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var s=t.charAt(i),o=s.charCodeAt(0);if(r[o]!==255)throw new TypeError(s+" is ambiguous");r[o]=i}var a=t.length,c=t.charAt(0),u=Math.log(a)/Math.log(256),l=Math.log(256)/Math.log(a);function h(f){if(f instanceof Uint8Array||(ArrayBuffer.isView(f)?f=new Uint8Array(f.buffer,f.byteOffset,f.byteLength):Array.isArray(f)&&(f=Uint8Array.from(f))),!(f instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(f.length===0)return"";for(var y=0,g=0,b=0,w=f.length;b!==w&&f[b]===0;)b++,y++;for(var m=(w-b)*l+1>>>0,E=new Uint8Array(m);b!==w;){for(var $=f[b],O=0,S=m-1;($!==0||O<g)&&S!==-1;S--,O++)$+=256*E[S]>>>0,E[S]=$%a>>>0,$=$/a>>>0;if($!==0)throw new Error("Non-zero carry");g=O,b++}for(var T=m-g;T!==m&&E[T]===0;)T++;for(var I=c.repeat(y);T<m;++T)I+=t.charAt(E[T]);return I}function d(f){if(typeof f!="string")throw new TypeError("Expected String");if(f.length===0)return new Uint8Array;var y=0;if(f[y]!==" "){for(var g=0,b=0;f[y]===c;)g++,y++;for(var w=(f.length-y)*u+1>>>0,m=new Uint8Array(w);f[y];){var E=r[f.charCodeAt(y)];if(E===255)return;for(var $=0,O=w-1;(E!==0||$<b)&&O!==-1;O--,$++)E+=a*m[O]>>>0,m[O]=E%256>>>0,E=E/256>>>0;if(E!==0)throw new Error("Non-zero carry");b=$,y++}if(f[y]!==" "){for(var S=w-b;S!==w&&m[S]===0;)S++;for(var T=new Uint8Array(g+(w-S)),I=g;S!==w;)T[I++]=m[S++];return T}}}function p(f){var y=d(f);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:h,decodeUnsafe:d,decode:p}}var z1=q1,V1=z1;const Ul=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},K1=t=>new TextEncoder().encode(t),H1=t=>new TextDecoder().decode(t);class W1{constructor(e,r,n){this.name=e,this.prefix=r,this.baseEncode=n}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class G1{constructor(e,r,n){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=n}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return kl(this,e)}}class Y1{constructor(e){this.decoders=e}or(e){return kl(this,e)}decode(e){const r=e[0],n=this.decoders[r];if(n)return n.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const kl=(t,e)=>new Y1({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class Z1{constructor(e,r,n,i){this.name=e,this.prefix=r,this.baseEncode=n,this.baseDecode=i,this.encoder=new W1(e,r,n),this.decoder=new G1(e,r,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const Ki=({name:t,prefix:e,encode:r,decode:n})=>new Z1(t,e,r,n),Fn=({prefix:t,name:e,alphabet:r})=>{const{encode:n,decode:i}=V1(r,e);return Ki({prefix:t,name:e,encode:n,decode:s=>Ul(i(s))})},X1=(t,e,r,n)=>{const i={};for(let l=0;l<e.length;++l)i[e[l]]=l;let s=t.length;for(;t[s-1]==="=";)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,u=0;for(let l=0;l<s;++l){const h=i[t[l]];if(h===void 0)throw new SyntaxError(`Non-${n} character`);c=c<<r|h,a+=r,a>=8&&(a-=8,o[u++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},J1=(t,e,r)=>{const n=e[e.length-1]==="=",i=(1<<r)-1;let s="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,s+=e[i&a>>o];if(o&&(s+=e[i&a<<r-o]),n)for(;s.length*r&7;)s+="=";return s},Ne=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>Ki({prefix:e,name:t,encode(i){return J1(i,n,r)},decode(i){return X1(i,n,r,t)}}),Q1=Ki({prefix:"\0",name:"identity",encode:t=>H1(t),decode:t=>K1(t)});var ev=Object.freeze({__proto__:null,identity:Q1});const tv=Ne({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var rv=Object.freeze({__proto__:null,base2:tv});const nv=Ne({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var iv=Object.freeze({__proto__:null,base8:nv});const sv=Fn({prefix:"9",name:"base10",alphabet:"0123456789"});var ov=Object.freeze({__proto__:null,base10:sv});const av=Ne({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),cv=Ne({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var uv=Object.freeze({__proto__:null,base16:av,base16upper:cv});const lv=Ne({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),hv=Ne({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),dv=Ne({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),fv=Ne({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),pv=Ne({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),gv=Ne({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),yv=Ne({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),wv=Ne({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),bv=Ne({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var mv=Object.freeze({__proto__:null,base32:lv,base32upper:hv,base32pad:dv,base32padupper:fv,base32hex:pv,base32hexupper:gv,base32hexpad:yv,base32hexpadupper:wv,base32z:bv});const vv=Fn({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),Ev=Fn({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var _v=Object.freeze({__proto__:null,base36:vv,base36upper:Ev});const Sv=Fn({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),Iv=Fn({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var xv=Object.freeze({__proto__:null,base58btc:Sv,base58flickr:Iv});const Ov=Ne({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),Dv=Ne({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),Av=Ne({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),$v=Ne({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var Tv=Object.freeze({__proto__:null,base64:Ov,base64pad:Dv,base64url:Av,base64urlpad:$v});const jl=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),Pv=jl.reduce((t,e,r)=>(t[r]=e,t),[]),Nv=jl.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function Rv(t){return t.reduce((e,r)=>(e+=Pv[r],e),"")}function Cv(t){const e=[];for(const r of t){const n=Nv[r.codePointAt(0)];if(n===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(n)}return new Uint8Array(e)}const Bv=Ki({prefix:"\u{1F680}",name:"base256emoji",encode:Rv,decode:Cv});var Fv=Object.freeze({__proto__:null,base256emoji:Bv}),Lv=ql,Ml=128,Uv=127,kv=~Uv,jv=Math.pow(2,31);function ql(t,e,r){e=e||[],r=r||0;for(var n=r;t>=jv;)e[r++]=t&255|Ml,t/=128;for(;t&kv;)e[r++]=t&255|Ml,t>>>=7;return e[r]=t|0,ql.bytes=r-n+1,e}var Mv=No,qv=128,zl=127;function No(t,n){var r=0,n=n||0,i=0,s=n,o,a=t.length;do{if(s>=a)throw No.bytes=0,new RangeError("Could not decode varint");o=t[s++],r+=i<28?(o&zl)<<i:(o&zl)*Math.pow(2,i),i+=7}while(o>=qv);return No.bytes=s-n,r}var zv=Math.pow(2,7),Vv=Math.pow(2,14),Kv=Math.pow(2,21),Hv=Math.pow(2,28),Wv=Math.pow(2,35),Gv=Math.pow(2,42),Yv=Math.pow(2,49),Zv=Math.pow(2,56),Xv=Math.pow(2,63),Jv=function(t){return t<zv?1:t<Vv?2:t<Kv?3:t<Hv?4:t<Wv?5:t<Gv?6:t<Yv?7:t<Zv?8:t<Xv?9:10},Qv={encode:Lv,decode:Mv,encodingLength:Jv},Vl=Qv;const Kl=(t,e,r=0)=>(Vl.encode(t,e,r),e),Hl=t=>Vl.encodingLength(t),Ro=(t,e)=>{const r=e.byteLength,n=Hl(t),i=n+Hl(r),s=new Uint8Array(i+r);return Kl(t,s,0),Kl(r,s,n),s.set(e,i),new eE(t,r,e,s)};class eE{constructor(e,r,n,i){this.code=e,this.size=r,this.digest=n,this.bytes=i}}const Wl=({name:t,code:e,encode:r})=>new tE(t,e,r);class tE{constructor(e,r,n){this.name=e,this.code=r,this.encode=n}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?Ro(this.code,r):r.then(n=>Ro(this.code,n))}else throw Error("Unknown type, must be binary type")}}const Gl=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),rE=Wl({name:"sha2-256",code:18,encode:Gl("SHA-256")}),nE=Wl({name:"sha2-512",code:19,encode:Gl("SHA-512")});var iE=Object.freeze({__proto__:null,sha256:rE,sha512:nE});const Yl=0,sE="identity",Zl=Ul;var oE=Object.freeze({__proto__:null,identity:{code:Yl,name:sE,encode:Zl,digest:t=>Ro(Yl,Zl(t))}});new TextEncoder,new TextDecoder;const Xl={...ev,...rv,...iv,...ov,...uv,...mv,..._v,...xv,...Tv,...Fv};({...iE,...oE});function Jl(t,e,r,n){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:n}}}const Ql=Jl("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),Co=Jl("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=Ll(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),eh={utf8:Ql,"utf-8":Ql,hex:Xl.base16,latin1:Co,ascii:Co,binary:Co,...Xl};function ot(t,e="utf8"){const r=eh[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t,"utf8"):r.decoder.decode(`${r.prefix}${t}`)}function Ye(t,e="utf8"){const r=eh[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8"):r.encoder.encode(t).substring(1)}const aE={waku:{publish:"waku_publish",batchPublish:"waku_batchPublish",subscribe:"waku_subscribe",batchSubscribe:"waku_batchSubscribe",subscription:"waku_subscription",unsubscribe:"waku_unsubscribe",batchUnsubscribe:"waku_batchUnsubscribe",batchFetchMessages:"waku_batchFetchMessages"},irn:{publish:"irn_publish",batchPublish:"irn_batchPublish",subscribe:"irn_subscribe",batchSubscribe:"irn_batchSubscribe",subscription:"irn_subscription",unsubscribe:"irn_unsubscribe",batchUnsubscribe:"irn_batchUnsubscribe",batchFetchMessages:"irn_batchFetchMessages"},iridium:{publish:"iridium_publish",batchPublish:"iridium_batchPublish",subscribe:"iridium_subscribe",batchSubscribe:"iridium_batchSubscribe",subscription:"iridium_subscription",unsubscribe:"iridium_unsubscribe",batchUnsubscribe:"iridium_batchUnsubscribe",batchFetchMessages:"iridium_batchFetchMessages"}};var cE=Object.defineProperty,uE=Object.defineProperties,lE=Object.getOwnPropertyDescriptors,th=Object.getOwnPropertySymbols,hE=Object.prototype.hasOwnProperty,dE=Object.prototype.propertyIsEnumerable,rh=(t,e,r)=>e in t?cE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ut=(t,e)=>{for(var r in e||(e={}))hE.call(e,r)&&rh(t,r,e[r]);if(th)for(var r of th(e))dE.call(e,r)&&rh(t,r,e[r]);return t},Bo=(t,e)=>uE(t,lE(e));const fE=":";function Hi(t){const[e,r]=t.split(fE);return{namespace:e,reference:r}}function nh(t,e){return t.includes(":")?[t]:e.chains||[]}var pE=Object.defineProperty,gE=Object.defineProperties,yE=Object.getOwnPropertyDescriptors,ih=Object.getOwnPropertySymbols,wE=Object.prototype.hasOwnProperty,bE=Object.prototype.propertyIsEnumerable,sh=(t,e,r)=>e in t?pE(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,oh=(t,e)=>{for(var r in e||(e={}))wE.call(e,r)&&sh(t,r,e[r]);if(ih)for(var r of ih(e))bE.call(e,r)&&sh(t,r,e[r]);return t},mE=(t,e)=>gE(t,yE(e));const vE="ReactNative",et={reactNative:"react-native",node:"node",browser:"browser",unknown:"unknown"},EE="js";function Wi(){return typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.node<"u"}function nr(){return!br()&&!!Qs()&&navigator.product===vE}function _E(){return nr()&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"&&(global==null?void 0:global.Platform.OS)==="android"}function SE(){return nr()&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"&&(global==null?void 0:global.Platform.OS)==="ios"}function rn(){return!Wi()&&!!Qs()&&!!br()}function Ln(){return nr()?et.reactNative:Wi()?et.node:rn()?et.browser:et.unknown}function ah(){var t;try{return nr()&&typeof global<"u"&&typeof(global==null?void 0:global.Application)<"u"?(t=global.Application)==null?void 0:t.applicationId:void 0}catch{return}}function IE(t,e){const r=new URLSearchParams(t);for(const n of Object.keys(e).sort())if(e.hasOwnProperty(n)){const i=e[n];i!==void 0&&r.set(n,i)}return r.toString()}function xE(t){var e,r;const n=ch();try{return t!=null&&t.url&&n.url&&new URL(t.url).host!==new URL(n.url).host&&(console.warn(`The configured WalletConnect 'metadata.url':${t.url} differs from the actual page url:${n.url}. This is probably unintended and can lead to issues.`),t.url=n.url),(e=t?.icons)!=null&&e.length&&t.icons.length>0&&(t.icons=t.icons.filter(i=>i!=="")),mE(oh(oh({},n),t),{url:t?.url||n.url,name:t?.name||n.name,description:t?.description||n.description,icons:(r=t?.icons)!=null&&r.length&&t.icons.length>0?t.icons:n.icons})}catch(i){return console.warn("Error populating app metadata",i),t||n}}function ch(){return Au()||{name:"",description:"",url:"",icons:[""]}}function OE(){if(Ln()===et.reactNative&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"){const{OS:r,Version:n}=global.Platform;return[r,n].join("-")}const t=a0();if(t===null)return"unknown";const e=t.os?t.os.replace(" ","").toLowerCase():"unknown";return t.type==="browser"?[e,t.name,t.version].join("-"):[e,t.version].join("-")}function DE(){var t;const e=Ln();return e===et.browser?[e,((t=Du())==null?void 0:t.host)||"unknown"].join(":"):e}function uh(t,e,r){const n=OE(),i=DE();return[[t,e].join("-"),[EE,r].join("-"),n,i].join("/")}function AE({protocol:t,version:e,relayUrl:r,sdkVersion:n,auth:i,projectId:s,useOnCloseEvent:o,bundleId:a,packageName:c}){const u=r.split("?"),l=uh(t,e,n),h={auth:i,ua:l,projectId:s,useOnCloseEvent:o||void 0,packageName:c||void 0,bundleId:a||void 0},d=IE(u[1]||"",h);return u[0]+"?"+d}function _r(t,e){return t.filter(r=>e.includes(r)).length===t.length}function Fo(t){return Object.fromEntries(t.entries())}function Lo(t){return new Map(Object.entries(t))}function Sr(t=F.FIVE_MINUTES,e){const r=F.toMiliseconds(t||F.FIVE_MINUTES);let n,i,s,o;return{resolve:a=>{s&&n&&(clearTimeout(s),n(a),o=Promise.resolve(a))},reject:a=>{s&&i&&(clearTimeout(s),i(a))},done:()=>new Promise((a,c)=>{if(o)return a(o);s=setTimeout(()=>{const u=new Error(e);o=Promise.reject(u),c(u)},r),n=a,i=c})}}function ir(t,e,r){return new Promise(async(n,i)=>{const s=setTimeout(()=>i(new Error(r)),e);try{const o=await t;n(o)}catch(o){i(o)}clearTimeout(s)})}function lh(t,e){if(typeof e=="string"&&e.startsWith(`${t}:`))return e;if(t.toLowerCase()==="topic"){if(typeof e!="string")throw new Error('Value must be "string" for expirer target type: topic');return`topic:${e}`}else if(t.toLowerCase()==="id"){if(typeof e!="number")throw new Error('Value must be "number" for expirer target type: id');return`id:${e}`}throw new Error(`Unknown expirer target type: ${t}`)}function $E(t){return lh("topic",t)}function TE(t){return lh("id",t)}function hh(t){const[e,r]=t.split(":"),n={id:void 0,topic:void 0};if(e==="topic"&&typeof r=="string")n.topic=r;else if(e==="id"&&Number.isInteger(Number(r)))n.id=Number(r);else throw new Error(`Invalid target, expected id:number or topic:string, got ${e}:${r}`);return n}function ve(t,e){return F.fromMiliseconds((e||Date.now())+F.toMiliseconds(t))}function sr(t){return Date.now()>=F.toMiliseconds(t)}function te(t,e){return`${t}${e?`:${e}`:""}`}function Ir(t=[],e=[]){return[...new Set([...t,...e])]}async function PE({id:t,topic:e,wcDeepLink:r}){var n;try{if(!r)return;const i=typeof r=="string"?JSON.parse(r):r,s=i?.href;if(typeof s!="string")return;const o=NE(s,t,e),a=Ln();if(a===et.browser){if(!((n=br())!=null&&n.hasFocus())){console.warn("Document does not have focus, skipping deeplink.");return}RE(o)}else a===et.reactNative&&typeof(global==null?void 0:global.Linking)<"u"&&await global.Linking.openURL(o)}catch(i){console.error(i)}}function NE(t,e,r){const n=`requestId=${e}&sessionTopic=${r}`;t.endsWith("/")&&(t=t.slice(0,-1));let i=`${t}`;if(t.startsWith("https://t.me")){const s=t.includes("?")?"&startapp=":"?startapp=";i=`${i}${s}${LE(n,!0)}`}else i=`${i}/wc?${n}`;return i}function RE(t){let e="_self";FE()?e="_top":(BE()||t.startsWith("https://")||t.startsWith("http://"))&&(e="_blank"),window.open(t,e,"noreferrer noopener")}async function CE(t,e){let r="";try{if(rn()&&(r=localStorage.getItem(e),r))return r;r=await t.getItem(e)}catch(n){console.error(n)}return r}function dh(t,e){if(!t.includes(e))return null;const r=t.split(/([&,?,=])/),n=r.indexOf(e);return r[n+2]}function fh(){return typeof crypto<"u"&&crypto!=null&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/gu,t=>{const e=Math.random()*16|0;return(t==="x"?e:e&3|8).toString(16)})}function Uo(){return typeof process<"u"&&process.env.IS_VITEST==="true"}function BE(){return typeof window<"u"&&(!!window.TelegramWebviewProxy||!!window.Telegram||!!window.TelegramWebviewProxyProto)}function FE(){try{return window.self!==window.top}catch{return!1}}function LE(t,e=!1){const r=Buffer.from(t).toString("base64");return e?r.replace(/[=]/g,""):r}function ph(t){return Buffer.from(t,"base64").toString("utf-8")}function UE(t){return new Promise(e=>setTimeout(e,t))}function Un(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function kE(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function kn(t,...e){if(!kE(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function ko(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Un(t.outputLen),Un(t.blockLen)}function nn(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function gh(t,e){kn(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const Gi=BigInt(2**32-1),yh=BigInt(32);function jE(t,e=!1){return e?{h:Number(t&Gi),l:Number(t>>yh&Gi)}:{h:Number(t>>yh&Gi)|0,l:Number(t&Gi)|0}}function ME(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:s,l:o}=jE(t[i],e);[r[i],n[i]]=[s,o]}return[r,n]}const qE=(t,e,r)=>t<<r|e>>>32-r,zE=(t,e,r)=>e<<r|t>>>32-r,VE=(t,e,r)=>e<<r-32|t>>>64-r,KE=(t,e,r)=>t<<r-32|e>>>64-r,sn=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;function HE(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function jo(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function It(t,e){return t<<32-e|t>>>e}const wh=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function WE(t){return t<<24&4278190080|t<<8&16711680|t>>>8&65280|t>>>24&255}function bh(t){for(let e=0;e<t.length;e++)t[e]=WE(t[e])}function GE(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function on(t){return typeof t=="string"&&(t=GE(t)),kn(t),t}function YE(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];kn(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}class Mo{clone(){return this._cloneInto()}}function mh(t){const e=n=>t().update(on(n)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function an(t=32){if(sn&&typeof sn.getRandomValues=="function")return sn.getRandomValues(new Uint8Array(t));if(sn&&typeof sn.randomBytes=="function")return sn.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}const vh=[],Eh=[],_h=[],ZE=BigInt(0),jn=BigInt(1),XE=BigInt(2),JE=BigInt(7),QE=BigInt(256),e_=BigInt(113);for(let t=0,e=jn,r=1,n=0;t<24;t++){[r,n]=[n,(2*r+3*n)%5],vh.push(2*(5*n+r)),Eh.push((t+1)*(t+2)/2%64);let i=ZE;for(let s=0;s<7;s++)e=(e<<jn^(e>>JE)*e_)%QE,e&XE&&(i^=jn<<(jn<<BigInt(s))-jn);_h.push(i)}const[t_,r_]=ME(_h,!0),Sh=(t,e,r)=>r>32?VE(t,e,r):qE(t,e,r),Ih=(t,e,r)=>r>32?KE(t,e,r):zE(t,e,r);function n_(t,e=24){const r=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let o=0;o<10;o++)r[o]=t[o]^t[o+10]^t[o+20]^t[o+30]^t[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,c=(o+2)%10,u=r[c],l=r[c+1],h=Sh(u,l,1)^r[a],d=Ih(u,l,1)^r[a+1];for(let p=0;p<50;p+=10)t[o+p]^=h,t[o+p+1]^=d}let i=t[2],s=t[3];for(let o=0;o<24;o++){const a=Eh[o],c=Sh(i,s,a),u=Ih(i,s,a),l=vh[o];i=t[l],s=t[l+1],t[l]=c,t[l+1]=u}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)r[a]=t[o+a];for(let a=0;a<10;a++)t[o+a]^=~r[(a+2)%10]&r[(a+4)%10]}t[0]^=t_[n],t[1]^=r_[n]}r.fill(0)}class qo extends Mo{constructor(e,r,n,i=!1,s=24){if(super(),this.blockLen=e,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=s,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,Un(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=HE(this.state)}keccak(){wh||bh(this.state32),n_(this.state32,this.rounds),wh||bh(this.state32),this.posOut=0,this.pos=0}update(e){nn(this);const{blockLen:r,state:n}=this;e=on(e);const i=e.length;for(let s=0;s<i;){const o=Math.min(r-this.pos,i-s);for(let a=0;a<o;a++)n[this.pos++]^=e[s++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:r,pos:n,blockLen:i}=this;e[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),e[i-1]^=128,this.keccak()}writeInto(e){nn(this,!1),kn(e),this.finish();const r=this.state,{blockLen:n}=this;for(let i=0,s=e.length;i<s;){this.posOut>=n&&this.keccak();const o=Math.min(n-this.posOut,s-i);e.set(r.subarray(this.posOut,this.posOut+o),i),this.posOut+=o,i+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return Un(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(gh(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:r,suffix:n,outputLen:i,rounds:s,enableXOF:o}=this;return e||(e=new qo(r,n,i,o,s)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=s,e.suffix=n,e.outputLen=i,e.enableXOF=o,e.destroyed=this.destroyed,e}}const i_=(t,e,r)=>mh(()=>new qo(e,t,r)),s_=i_(1,136,256/8),o_="https://rpc.walletconnect.org/v1";function xh(t){const e=`Ethereum Signed Message:
${t.length}`,r=new TextEncoder().encode(e+t);return"0x"+Buffer.from(s_(r)).toString("hex")}async function a_(t,e,r,n,i,s){switch(r.t){case"eip191":return await c_(t,e,r.s);case"eip1271":return await u_(t,e,r.s,n,i,s);default:throw new Error(`verifySignature failed: Attempted to verify CacaoSignature with unknown type: ${r.t}`)}}async function c_(t,e,r){return(await sm({hash:xh(e),signature:r})).toLowerCase()===t.toLowerCase()}async function u_(t,e,r,n,i,s){const o=Hi(n);if(!o.namespace||!o.reference)throw new Error(`isValidEip1271Signature failed: chainId must be in CAIP-2 format, received: ${n}`);try{const a="0x1626ba7e",c="0000000000000000000000000000000000000000000000000000000000000040",u="0000000000000000000000000000000000000000000000000000000000000041",l=r.substring(2),h=xh(e).substring(2),d=a+h+c+u+l,p=await fetch(`${s||o_}/?chainId=${n}&projectId=${i}`,{method:"POST",body:JSON.stringify({id:l_(),jsonrpc:"2.0",method:"eth_call",params:[{to:t,data:d},"latest"]})}),{result:f}=await p.json();return f?f.slice(0,a.length).toLowerCase()===a.toLowerCase():!1}catch(a){return console.error("isValidEip1271Signature: ",a),!1}}function l_(){return Date.now()+Math.floor(Math.random()*1e3)}function h_(t){const e=atob(t),r=new Uint8Array(e.length);for(let o=0;o<e.length;o++)r[o]=e.charCodeAt(o);const n=r[0];if(n===0)throw new Error("No signatures found");const i=1+n*64;if(r.length<i)throw new Error("Transaction data too short for claimed signature count");if(r.length<100)throw new Error("Transaction too short");const s=Buffer.from(t,"base64").slice(1,65);return M1.encode(s)}var d_=Object.defineProperty,f_=Object.defineProperties,p_=Object.getOwnPropertyDescriptors,Oh=Object.getOwnPropertySymbols,g_=Object.prototype.hasOwnProperty,y_=Object.prototype.propertyIsEnumerable,Dh=(t,e,r)=>e in t?d_(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,w_=(t,e)=>{for(var r in e||(e={}))g_.call(e,r)&&Dh(t,r,e[r]);if(Oh)for(var r of Oh(e))y_.call(e,r)&&Dh(t,r,e[r]);return t},b_=(t,e)=>f_(t,p_(e));const m_="did:pkh:",zo=t=>t?.split(":"),v_=t=>{const e=t&&zo(t);if(e)return t.includes(m_)?e[3]:e[1]},Vo=t=>{const e=t&&zo(t);if(e)return e[2]+":"+e[3]},Yi=t=>{const e=t&&zo(t);if(e)return e.pop()};async function Ah(t){const{cacao:e,projectId:r}=t,{s:n,p:i}=e,s=$h(i,i.iss),o=Yi(i.iss);return await a_(o,s,n,Vo(i.iss),r)}const $h=(t,e)=>{const r=`${t.domain} wants you to sign in with your Ethereum account:`,n=Yi(e);if(!t.aud&&!t.uri)throw new Error("Either `aud` or `uri` is required to construct the message");let i=t.statement||void 0;const s=`URI: ${t.aud||t.uri}`,o=`Version: ${t.version}`,a=`Chain ID: ${v_(e)}`,c=`Nonce: ${t.nonce}`,u=`Issued At: ${t.iat}`,l=t.exp?`Expiration Time: ${t.exp}`:void 0,h=t.nbf?`Not Before: ${t.nbf}`:void 0,d=t.requestId?`Request ID: ${t.requestId}`:void 0,p=t.resources?`Resources:${t.resources.map(y=>`
- ${y}`).join("")}`:void 0,f=Zi(t.resources);if(f){const y=Mn(f);i=$_(i,y)}return[r,n,"",i,"",s,o,a,c,u,l,h,d,p].filter(y=>y!=null).join(`
`)};function E_(t){return Buffer.from(JSON.stringify(t)).toString("base64")}function __(t){return JSON.parse(Buffer.from(t,"base64").toString("utf-8"))}function xr(t){if(!t)throw new Error("No recap provided, value is undefined");if(!t.att)throw new Error("No `att` property found");const e=Object.keys(t.att);if(!(e!=null&&e.length))throw new Error("No resources found in `att` property");e.forEach(r=>{const n=t.att[r];if(Array.isArray(n))throw new Error(`Resource must be an object: ${r}`);if(typeof n!="object")throw new Error(`Resource must be an object: ${r}`);if(!Object.keys(n).length)throw new Error(`Resource object is empty: ${r}`);Object.keys(n).forEach(i=>{const s=n[i];if(!Array.isArray(s))throw new Error(`Ability limits ${i} must be an array of objects, found: ${s}`);if(!s.length)throw new Error(`Value of ${i} is empty array, must be an array with objects`);s.forEach(o=>{if(typeof o!="object")throw new Error(`Ability limits (${i}) must be an array of objects, found: ${o}`)})})})}function S_(t,e,r,n={}){return r?.sort((i,s)=>i.localeCompare(s)),{att:{[t]:I_(e,r,n)}}}function I_(t,e,r={}){e=e?.sort((i,s)=>i.localeCompare(s));const n=e.map(i=>({[`${t}/${i}`]:[r]}));return Object.assign({},...n)}function Th(t){return xr(t),`urn:recap:${E_(t).replace(/=/g,"")}`}function Mn(t){const e=__(t.replace("urn:recap:",""));return xr(e),e}function x_(t,e,r){const n=S_(t,e,r);return Th(n)}function O_(t){return t&&t.includes("urn:recap:")}function D_(t,e){const r=Mn(t),n=Mn(e),i=A_(r,n);return Th(i)}function A_(t,e){xr(t),xr(e);const r=Object.keys(t.att).concat(Object.keys(e.att)).sort((i,s)=>i.localeCompare(s)),n={att:{}};return r.forEach(i=>{var s,o;Object.keys(((s=t.att)==null?void 0:s[i])||{}).concat(Object.keys(((o=e.att)==null?void 0:o[i])||{})).sort((a,c)=>a.localeCompare(c)).forEach(a=>{var c,u;n.att[i]=b_(w_({},n.att[i]),{[a]:((c=t.att[i])==null?void 0:c[a])||((u=e.att[i])==null?void 0:u[a])})})}),n}function $_(t="",e){xr(e);const r="I further authorize the stated URI to perform the following actions on my behalf: ";if(t.includes(r))return t;const n=[];let i=0;Object.keys(e.att).forEach(a=>{const c=Object.keys(e.att[a]).map(h=>({ability:h.split("/")[0],action:h.split("/")[1]}));c.sort((h,d)=>h.action.localeCompare(d.action));const u={};c.forEach(h=>{u[h.ability]||(u[h.ability]=[]),u[h.ability].push(h.action)});const l=Object.keys(u).map(h=>(i++,`(${i}) '${h}': '${u[h].join("', '")}' for '${a}'.`));n.push(l.join(", ").replace(".,","."))});const s=n.join(" "),o=`${r}${s}`;return`${t?t+" ":""}${o}`}function Ph(t){var e;const r=Mn(t);xr(r);const n=(e=r.att)==null?void 0:e.eip155;return n?Object.keys(n).map(i=>i.split("/")[1]):[]}function Nh(t){const e=Mn(t);xr(e);const r=[];return Object.values(e.att).forEach(n=>{Object.values(n).forEach(i=>{var s;(s=i?.[0])!=null&&s.chains&&r.push(i[0].chains)})}),[...new Set(r.flat())]}function Zi(t){if(!t)return;const e=t?.[t.length-1];return O_(e)?e:void 0}function Ko(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function Rh(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function tt(t,...e){if(!Rh(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function Ch(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function T_(t,e){tt(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}function Bh(t){if(typeof t!="boolean")throw new Error(`boolean expected, not ${t}`)}const or=t=>new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4)),P_=t=>new DataView(t.buffer,t.byteOffset,t.byteLength);if(!(new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68))throw new Error("Non little-endian hardware is not supported");function N_(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}function Ho(t){if(typeof t=="string")t=N_(t);else if(Rh(t))t=Wo(t);else throw new Error("Uint8Array expected, got "+typeof t);return t}function R_(t,e){if(e==null||typeof e!="object")throw new Error("options must be defined");return Object.assign(t,e)}function C_(t,e){if(t.length!==e.length)return!1;let r=0;for(let n=0;n<t.length;n++)r|=t[n]^e[n];return r===0}const B_=(t,e)=>{function r(n,...i){if(tt(n),t.nonceLength!==void 0){const u=i[0];if(!u)throw new Error("nonce / iv required");t.varSizeNonce?tt(u):tt(u,t.nonceLength)}const s=t.tagLength;s&&i[1]!==void 0&&tt(i[1]);const o=e(n,...i),a=(u,l)=>{if(l!==void 0){if(u!==2)throw new Error("cipher output not supported");tt(l)}};let c=!1;return{encrypt(u,l){if(c)throw new Error("cannot encrypt() twice with same key + nonce");return c=!0,tt(u),a(o.encrypt.length,l),o.encrypt(u,l)},decrypt(u,l){if(tt(u),s&&u.length<s)throw new Error("invalid ciphertext length: smaller than tagLength="+s);return a(o.decrypt.length,l),o.decrypt(u,l)}}}return Object.assign(r,t),r};function Fh(t,e,r=!0){if(e===void 0)return new Uint8Array(t);if(e.length!==t)throw new Error("invalid output length, expected "+t+", got: "+e.length);if(r&&!F_(e))throw new Error("invalid output, must be aligned");return e}function Lh(t,e,r,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,n);const i=BigInt(32),s=BigInt(**********),o=Number(r>>i&s),a=Number(r&s),c=n?4:0,u=n?0:4;t.setUint32(e+c,o,n),t.setUint32(e+u,a,n)}function F_(t){return t.byteOffset%4===0}function Wo(t){return Uint8Array.from(t)}function cn(...t){for(let e=0;e<t.length;e++)t[e].fill(0)}const Uh=t=>Uint8Array.from(t.split("").map(e=>e.charCodeAt(0))),L_=Uh("expand 16-byte k"),U_=Uh("expand 32-byte k"),k_=or(L_),j_=or(U_);function J(t,e){return t<<e|t>>>32-e}function Go(t){return t.byteOffset%4===0}const Xi=64,M_=16,kh=2**32-1,jh=new Uint32Array;function q_(t,e,r,n,i,s,o,a){const c=i.length,u=new Uint8Array(Xi),l=or(u),h=Go(i)&&Go(s),d=h?or(i):jh,p=h?or(s):jh;for(let f=0;f<c;o++){if(t(e,r,n,l,o,a),o>=kh)throw new Error("arx: counter overflow");const y=Math.min(Xi,c-f);if(h&&y===Xi){const g=f/4;if(f%4!==0)throw new Error("arx: invalid block position");for(let b=0,w;b<M_;b++)w=g+b,p[w]=d[w]^l[b];f+=Xi;continue}for(let g=0,b;g<y;g++)b=f+g,s[b]=i[b]^u[g];f+=y}}function z_(t,e){const{allowShortKeys:r,extendNonceFn:n,counterLength:i,counterRight:s,rounds:o}=R_({allowShortKeys:!1,counterLength:8,counterRight:!1,rounds:20},e);if(typeof t!="function")throw new Error("core must be a function");return Ko(i),Ko(o),Bh(s),Bh(r),(a,c,u,l,h=0)=>{tt(a),tt(c),tt(u);const d=u.length;if(l===void 0&&(l=new Uint8Array(d)),tt(l),Ko(h),h<0||h>=kh)throw new Error("arx: counter overflow");if(l.length<d)throw new Error(`arx: output (${l.length}) is shorter than data (${d})`);const p=[];let f=a.length,y,g;if(f===32)p.push(y=Wo(a)),g=j_;else if(f===16&&r)y=new Uint8Array(32),y.set(a),y.set(a,16),g=k_,p.push(y);else throw new Error(`arx: invalid 32-byte key, got length=${f}`);Go(c)||p.push(c=Wo(c));const b=or(y);if(n){if(c.length!==24)throw new Error("arx: extended nonce must be 24 bytes");n(g,b,or(c.subarray(0,16)),b),c=c.subarray(16)}const w=16-i;if(w!==c.length)throw new Error(`arx: nonce must be ${w} or 16 bytes`);if(w!==12){const E=new Uint8Array(12);E.set(c,s?0:12-c.length),c=E,p.push(c)}const m=or(c);return q_(t,g,b,m,u,l,h,o),cn(...p),l}}const Re=(t,e)=>t[e++]&255|(t[e++]&255)<<8;class V_{constructor(e){this.blockLen=16,this.outputLen=16,this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.pos=0,this.finished=!1,e=Ho(e),tt(e,32);const r=Re(e,0),n=Re(e,2),i=Re(e,4),s=Re(e,6),o=Re(e,8),a=Re(e,10),c=Re(e,12),u=Re(e,14);this.r[0]=r&8191,this.r[1]=(r>>>13|n<<3)&8191,this.r[2]=(n>>>10|i<<6)&7939,this.r[3]=(i>>>7|s<<9)&8191,this.r[4]=(s>>>4|o<<12)&255,this.r[5]=o>>>1&8190,this.r[6]=(o>>>14|a<<2)&8191,this.r[7]=(a>>>11|c<<5)&8065,this.r[8]=(c>>>8|u<<8)&8191,this.r[9]=u>>>5&127;for(let l=0;l<8;l++)this.pad[l]=Re(e,16+2*l)}process(e,r,n=!1){const i=n?0:2048,{h:s,r:o}=this,a=o[0],c=o[1],u=o[2],l=o[3],h=o[4],d=o[5],p=o[6],f=o[7],y=o[8],g=o[9],b=Re(e,r+0),w=Re(e,r+2),m=Re(e,r+4),E=Re(e,r+6),$=Re(e,r+8),O=Re(e,r+10),S=Re(e,r+12),T=Re(e,r+14);let I=s[0]+(b&8191),j=s[1]+((b>>>13|w<<3)&8191),R=s[2]+((w>>>10|m<<6)&8191),B=s[3]+((m>>>7|E<<9)&8191),M=s[4]+((E>>>4|$<<12)&8191),x=s[5]+($>>>1&8191),_=s[6]+(($>>>14|O<<2)&8191),v=s[7]+((O>>>11|S<<5)&8191),D=s[8]+((S>>>8|T<<8)&8191),P=s[9]+(T>>>5|i),A=0,C=A+I*a+j*(5*g)+R*(5*y)+B*(5*f)+M*(5*p);A=C>>>13,C&=8191,C+=x*(5*d)+_*(5*h)+v*(5*l)+D*(5*u)+P*(5*c),A+=C>>>13,C&=8191;let U=A+I*c+j*a+R*(5*g)+B*(5*y)+M*(5*f);A=U>>>13,U&=8191,U+=x*(5*p)+_*(5*d)+v*(5*h)+D*(5*l)+P*(5*u),A+=U>>>13,U&=8191;let k=A+I*u+j*c+R*a+B*(5*g)+M*(5*y);A=k>>>13,k&=8191,k+=x*(5*f)+_*(5*p)+v*(5*d)+D*(5*h)+P*(5*l),A+=k>>>13,k&=8191;let q=A+I*l+j*u+R*c+B*a+M*(5*g);A=q>>>13,q&=8191,q+=x*(5*y)+_*(5*f)+v*(5*p)+D*(5*d)+P*(5*h),A+=q>>>13,q&=8191;let z=A+I*h+j*l+R*u+B*c+M*a;A=z>>>13,z&=8191,z+=x*(5*g)+_*(5*y)+v*(5*f)+D*(5*p)+P*(5*d),A+=z>>>13,z&=8191;let V=A+I*d+j*h+R*l+B*u+M*c;A=V>>>13,V&=8191,V+=x*a+_*(5*g)+v*(5*y)+D*(5*f)+P*(5*p),A+=V>>>13,V&=8191;let K=A+I*p+j*d+R*h+B*l+M*u;A=K>>>13,K&=8191,K+=x*c+_*a+v*(5*g)+D*(5*y)+P*(5*f),A+=K>>>13,K&=8191;let ee=A+I*f+j*p+R*d+B*h+M*l;A=ee>>>13,ee&=8191,ee+=x*u+_*c+v*a+D*(5*g)+P*(5*y),A+=ee>>>13,ee&=8191;let Y=A+I*y+j*f+R*p+B*d+M*h;A=Y>>>13,Y&=8191,Y+=x*l+_*u+v*c+D*a+P*(5*g),A+=Y>>>13,Y&=8191;let G=A+I*g+j*y+R*f+B*p+M*d;A=G>>>13,G&=8191,G+=x*h+_*l+v*u+D*c+P*a,A+=G>>>13,G&=8191,A=(A<<2)+A|0,A=A+C|0,C=A&8191,A=A>>>13,U+=A,s[0]=C,s[1]=U,s[2]=k,s[3]=q,s[4]=z,s[5]=V,s[6]=K,s[7]=ee,s[8]=Y,s[9]=G}finalize(){const{h:e,pad:r}=this,n=new Uint16Array(10);let i=e[1]>>>13;e[1]&=8191;for(let a=2;a<10;a++)e[a]+=i,i=e[a]>>>13,e[a]&=8191;e[0]+=i*5,i=e[0]>>>13,e[0]&=8191,e[1]+=i,i=e[1]>>>13,e[1]&=8191,e[2]+=i,n[0]=e[0]+5,i=n[0]>>>13,n[0]&=8191;for(let a=1;a<10;a++)n[a]=e[a]+i,i=n[a]>>>13,n[a]&=8191;n[9]-=8192;let s=(i^1)-1;for(let a=0;a<10;a++)n[a]&=s;s=~s;for(let a=0;a<10;a++)e[a]=e[a]&s|n[a];e[0]=(e[0]|e[1]<<13)&65535,e[1]=(e[1]>>>3|e[2]<<10)&65535,e[2]=(e[2]>>>6|e[3]<<7)&65535,e[3]=(e[3]>>>9|e[4]<<4)&65535,e[4]=(e[4]>>>12|e[5]<<1|e[6]<<14)&65535,e[5]=(e[6]>>>2|e[7]<<11)&65535,e[6]=(e[7]>>>5|e[8]<<8)&65535,e[7]=(e[8]>>>8|e[9]<<5)&65535;let o=e[0]+r[0];e[0]=o&65535;for(let a=1;a<8;a++)o=(e[a]+r[a]|0)+(o>>>16)|0,e[a]=o&65535;cn(n)}update(e){Ch(this);const{buffer:r,blockLen:n}=this;e=Ho(e);const i=e.length;for(let s=0;s<i;){const o=Math.min(n-this.pos,i-s);if(o===n){for(;n<=i-s;s+=n)this.process(e,s);continue}r.set(e.subarray(s,s+o),this.pos),this.pos+=o,s+=o,this.pos===n&&(this.process(r,0,!1),this.pos=0)}return this}destroy(){cn(this.h,this.r,this.buffer,this.pad)}digestInto(e){Ch(this),T_(e,this),this.finished=!0;const{buffer:r,h:n}=this;let{pos:i}=this;if(i){for(r[i++]=1;i<16;i++)r[i]=0;this.process(r,0,!0)}this.finalize();let s=0;for(let o=0;o<8;o++)e[s++]=n[o]>>>0,e[s++]=n[o]>>>8;return e}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const n=e.slice(0,r);return this.destroy(),n}}function K_(t){const e=(n,i)=>t(i).update(Ho(n)).digest(),r=t(new Uint8Array(32));return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=n=>t(n),e}const H_=K_(t=>new V_(t));function W_(t,e,r,n,i,s=20){let o=t[0],a=t[1],c=t[2],u=t[3],l=e[0],h=e[1],d=e[2],p=e[3],f=e[4],y=e[5],g=e[6],b=e[7],w=i,m=r[0],E=r[1],$=r[2],O=o,S=a,T=c,I=u,j=l,R=h,B=d,M=p,x=f,_=y,v=g,D=b,P=w,A=m,C=E,U=$;for(let q=0;q<s;q+=2)O=O+j|0,P=J(P^O,16),x=x+P|0,j=J(j^x,12),O=O+j|0,P=J(P^O,8),x=x+P|0,j=J(j^x,7),S=S+R|0,A=J(A^S,16),_=_+A|0,R=J(R^_,12),S=S+R|0,A=J(A^S,8),_=_+A|0,R=J(R^_,7),T=T+B|0,C=J(C^T,16),v=v+C|0,B=J(B^v,12),T=T+B|0,C=J(C^T,8),v=v+C|0,B=J(B^v,7),I=I+M|0,U=J(U^I,16),D=D+U|0,M=J(M^D,12),I=I+M|0,U=J(U^I,8),D=D+U|0,M=J(M^D,7),O=O+R|0,U=J(U^O,16),v=v+U|0,R=J(R^v,12),O=O+R|0,U=J(U^O,8),v=v+U|0,R=J(R^v,7),S=S+B|0,P=J(P^S,16),D=D+P|0,B=J(B^D,12),S=S+B|0,P=J(P^S,8),D=D+P|0,B=J(B^D,7),T=T+M|0,A=J(A^T,16),x=x+A|0,M=J(M^x,12),T=T+M|0,A=J(A^T,8),x=x+A|0,M=J(M^x,7),I=I+j|0,C=J(C^I,16),_=_+C|0,j=J(j^_,12),I=I+j|0,C=J(C^I,8),_=_+C|0,j=J(j^_,7);let k=0;n[k++]=o+O|0,n[k++]=a+S|0,n[k++]=c+T|0,n[k++]=u+I|0,n[k++]=l+j|0,n[k++]=h+R|0,n[k++]=d+B|0,n[k++]=p+M|0,n[k++]=f+x|0,n[k++]=y+_|0,n[k++]=g+v|0,n[k++]=b+D|0,n[k++]=w+P|0,n[k++]=m+A|0,n[k++]=E+C|0,n[k++]=$+U|0}const G_=z_(W_,{counterRight:!1,counterLength:4,allowShortKeys:!1}),Y_=new Uint8Array(16),Mh=(t,e)=>{t.update(e);const r=e.length%16;r&&t.update(Y_.subarray(r))},Z_=new Uint8Array(32);function qh(t,e,r,n,i){const s=t(e,r,Z_),o=H_.create(s);i&&Mh(o,i),Mh(o,n);const a=new Uint8Array(16),c=P_(a);Lh(c,0,BigInt(i?i.length:0),!0),Lh(c,8,BigInt(n.length),!0),o.update(a);const u=o.digest();return cn(s,a),u}const X_=t=>(e,r,n)=>({encrypt(i,s){const o=i.length;s=Fh(o+16,s,!1),s.set(i);const a=s.subarray(0,-16);t(e,r,a,a,1);const c=qh(t,e,r,a,n);return s.set(c,o),cn(c),s},decrypt(i,s){s=Fh(i.length-16,s,!1);const o=i.subarray(0,-16),a=i.subarray(-16),c=qh(t,e,r,o,n);if(!C_(a,c))throw new Error("invalid tag");return s.set(i.subarray(0,-16)),t(e,r,s,s,1),cn(c),s}}),zh=B_({blockSize:64,nonceLength:12,tagLength:16},X_(G_));class Vh extends Mo{constructor(e,r){super(),this.finished=!1,this.destroyed=!1,ko(e);const n=on(r);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const i=this.blockLen,s=new Uint8Array(i);s.set(n.length>i?e.create().update(n).digest():n);for(let o=0;o<s.length;o++)s[o]^=54;this.iHash.update(s),this.oHash=e.create();for(let o=0;o<s.length;o++)s[o]^=106;this.oHash.update(s),s.fill(0)}update(e){return nn(this),this.iHash.update(e),this}digestInto(e){nn(this),kn(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:r,iHash:n,finished:i,destroyed:s,blockLen:o,outputLen:a}=this;return e=e,e.finished=i,e.destroyed=s,e.blockLen=o,e.outputLen=a,e.oHash=r._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Ji=(t,e,r)=>new Vh(t,e).update(r).digest();Ji.create=(t,e)=>new Vh(t,e);function J_(t,e,r){return ko(t),r===void 0&&(r=new Uint8Array(t.outputLen)),Ji(t,on(r),on(e))}const Yo=new Uint8Array([0]),Kh=new Uint8Array;function Q_(t,e,r,n=32){if(ko(t),Un(n),n>255*t.outputLen)throw new Error("Length should be <= 255*HashLen");const i=Math.ceil(n/t.outputLen);r===void 0&&(r=Kh);const s=new Uint8Array(i*t.outputLen),o=Ji.create(t,e),a=o._cloneInto(),c=new Uint8Array(o.outputLen);for(let u=0;u<i;u++)Yo[0]=u+1,a.update(u===0?Kh:c).update(r).update(Yo).digestInto(c),s.set(c,t.outputLen*u),o._cloneInto(a);return o.destroy(),a.destroy(),c.fill(0),Yo.fill(0),s.slice(0,n)}const e2=(t,e,r,n,i)=>Q_(t,J_(t,e,r),n,i);function t2(t,e,r,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,n);const i=BigInt(32),s=BigInt(**********),o=Number(r>>i&s),a=Number(r&s),c=n?4:0,u=n?0:4;t.setUint32(e+c,o,n),t.setUint32(e+u,a,n)}function r2(t,e,r){return t&e^~t&r}function n2(t,e,r){return t&e^t&r^e&r}class i2 extends Mo{constructor(e,r,n,i){super(),this.blockLen=e,this.outputLen=r,this.padOffset=n,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=jo(this.buffer)}update(e){nn(this);const{view:r,buffer:n,blockLen:i}=this;e=on(e);const s=e.length;for(let o=0;o<s;){const a=Math.min(i-this.pos,s-o);if(a===i){const c=jo(e);for(;i<=s-o;o+=i)this.process(c,o);continue}n.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===i&&(this.process(r,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){nn(this),gh(e,this),this.finished=!0;const{buffer:r,view:n,blockLen:i,isLE:s}=this;let{pos:o}=this;r[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>i-o&&(this.process(n,0),o=0);for(let h=o;h<i;h++)r[h]=0;t2(n,i-8,BigInt(this.length*8),s),this.process(n,0);const a=jo(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=c/4,l=this.get();if(u>l.length)throw new Error("_sha2: outputLen bigger than state");for(let h=0;h<u;h++)a.setUint32(4*h,l[h],s)}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const n=e.slice(0,r);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:r,buffer:n,length:i,finished:s,destroyed:o,pos:a}=this;return e.length=i,e.pos=a,e.finished=s,e.destroyed=o,i%r&&e.buffer.set(n),e}}const s2=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),ar=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),cr=new Uint32Array(64);class o2 extends i2{constructor(){super(64,32,8,!1),this.A=ar[0]|0,this.B=ar[1]|0,this.C=ar[2]|0,this.D=ar[3]|0,this.E=ar[4]|0,this.F=ar[5]|0,this.G=ar[6]|0,this.H=ar[7]|0}get(){const{A:e,B:r,C:n,D:i,E:s,F:o,G:a,H:c}=this;return[e,r,n,i,s,o,a,c]}set(e,r,n,i,s,o,a,c){this.A=e|0,this.B=r|0,this.C=n|0,this.D=i|0,this.E=s|0,this.F=o|0,this.G=a|0,this.H=c|0}process(e,r){for(let h=0;h<16;h++,r+=4)cr[h]=e.getUint32(r,!1);for(let h=16;h<64;h++){const d=cr[h-15],p=cr[h-2],f=It(d,7)^It(d,18)^d>>>3,y=It(p,17)^It(p,19)^p>>>10;cr[h]=y+cr[h-7]+f+cr[h-16]|0}let{A:n,B:i,C:s,D:o,E:a,F:c,G:u,H:l}=this;for(let h=0;h<64;h++){const d=It(a,6)^It(a,11)^It(a,25),p=l+d+r2(a,c,u)+s2[h]+cr[h]|0,f=(It(n,2)^It(n,13)^It(n,22))+n2(n,i,s)|0;l=u,u=c,c=a,a=o+p|0,o=s,s=i,i=n,n=p+f|0}n=n+this.A|0,i=i+this.B|0,s=s+this.C|0,o=o+this.D|0,a=a+this.E|0,c=c+this.F|0,u=u+this.G|0,l=l+this.H|0,this.set(n,i,s,o,a,c,u,l)}roundClean(){cr.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const qn=mh(()=>new o2);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Qi=BigInt(0),es=BigInt(1),a2=BigInt(2);function Or(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function zn(t){if(!Or(t))throw new Error("Uint8Array expected")}function un(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const c2=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function ln(t){zn(t);let e="";for(let r=0;r<t.length;r++)e+=c2[t[r]];return e}function hn(t){const e=t.toString(16);return e.length&1?"0"+e:e}function Zo(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?Qi:BigInt("0x"+t)}const kt={_0:48,_9:57,A:65,F:70,a:97,f:102};function Hh(t){if(t>=kt._0&&t<=kt._9)return t-kt._0;if(t>=kt.A&&t<=kt.F)return t-(kt.A-10);if(t>=kt.a&&t<=kt.f)return t-(kt.a-10)}function dn(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const n=new Uint8Array(r);for(let i=0,s=0;i<r;i++,s+=2){const o=Hh(t.charCodeAt(s)),a=Hh(t.charCodeAt(s+1));if(o===void 0||a===void 0){const c=t[s]+t[s+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+s)}n[i]=o*16+a}return n}function Dr(t){return Zo(ln(t))}function Vn(t){return zn(t),Zo(ln(Uint8Array.from(t).reverse()))}function fn(t,e){return dn(t.toString(16).padStart(e*2,"0"))}function ts(t,e){return fn(t,e).reverse()}function u2(t){return dn(hn(t))}function rt(t,e,r){let n;if(typeof e=="string")try{n=dn(e)}catch(s){throw new Error(t+" must be hex string or Uint8Array, cause: "+s)}else if(Or(e))n=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const i=n.length;if(typeof r=="number"&&i!==r)throw new Error(t+" of length "+r+" expected, got "+i);return n}function Kn(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];zn(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}function l2(t,e){if(t.length!==e.length)return!1;let r=0;for(let n=0;n<t.length;n++)r|=t[n]^e[n];return r===0}function h2(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}const Xo=t=>typeof t=="bigint"&&Qi<=t;function rs(t,e,r){return Xo(t)&&Xo(e)&&Xo(r)&&e<=t&&t<r}function jt(t,e,r,n){if(!rs(e,r,n))throw new Error("expected valid "+t+": "+r+" <= n < "+n+", got "+e)}function Wh(t){let e;for(e=0;t>Qi;t>>=es,e+=1);return e}function d2(t,e){return t>>BigInt(e)&es}function f2(t,e,r){return t|(r?es:Qi)<<BigInt(e)}const Jo=t=>(a2<<BigInt(t-1))-es,Qo=t=>new Uint8Array(t),Gh=t=>Uint8Array.from(t);function Yh(t,e,r){if(typeof t!="number"||t<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof r!="function")throw new Error("hmacFn must be a function");let n=Qo(t),i=Qo(t),s=0;const o=()=>{n.fill(1),i.fill(0),s=0},a=(...l)=>r(i,n,...l),c=(l=Qo())=>{i=a(Gh([0]),l),n=a(),l.length!==0&&(i=a(Gh([1]),l),n=a())},u=()=>{if(s++>=1e3)throw new Error("drbg: tried 1000 values");let l=0;const h=[];for(;l<e;){n=a();const d=n.slice();h.push(d),l+=n.length}return Kn(...h)};return(l,h)=>{o(),c(l);let d;for(;!(d=h(u()));)c();return o(),d}}const p2={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||Or(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function pn(t,e,r={}){const n=(i,s,o)=>{const a=p2[s];if(typeof a!="function")throw new Error("invalid validator function");const c=t[i];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(i)+" is invalid. Expected "+s+", got "+c)};for(const[i,s]of Object.entries(e))n(i,s,!1);for(const[i,s]of Object.entries(r))n(i,s,!0);return t}const g2=()=>{throw new Error("not implemented")};function ea(t){const e=new WeakMap;return(r,...n)=>{const i=e.get(r);if(i!==void 0)return i;const s=t(r,...n);return e.set(r,s),s}}var y2=Object.freeze({__proto__:null,isBytes:Or,abytes:zn,abool:un,bytesToHex:ln,numberToHexUnpadded:hn,hexToNumber:Zo,hexToBytes:dn,bytesToNumberBE:Dr,bytesToNumberLE:Vn,numberToBytesBE:fn,numberToBytesLE:ts,numberToVarBytesBE:u2,ensureBytes:rt,concatBytes:Kn,equalBytes:l2,utf8ToBytes:h2,inRange:rs,aInRange:jt,bitLen:Wh,bitGet:d2,bitSet:f2,bitMask:Jo,createHmacDrbg:Yh,validateObject:pn,notImplemented:g2,memoized:ea});const Oe=BigInt(0),pe=BigInt(1),Ar=BigInt(2),w2=BigInt(3),ta=BigInt(4),Zh=BigInt(5),Xh=BigInt(8);function Ze(t,e){const r=t%e;return r>=Oe?r:e+r}function Jh(t,e,r){if(e<Oe)throw new Error("invalid exponent, negatives unsupported");if(r<=Oe)throw new Error("invalid modulus");if(r===pe)return Oe;let n=pe;for(;e>Oe;)e&pe&&(n=n*t%r),t=t*t%r,e>>=pe;return n}function dt(t,e,r){let n=t;for(;e-- >Oe;)n*=n,n%=r;return n}function ra(t,e){if(t===Oe)throw new Error("invert: expected non-zero number");if(e<=Oe)throw new Error("invert: expected positive modulus, got "+e);let r=Ze(t,e),n=e,i=Oe,s=pe;for(;r!==Oe;){const o=n/r,a=n%r,c=i-s*o;n=r,r=a,i=s,s=c}if(n!==pe)throw new Error("invert: does not exist");return Ze(i,e)}function b2(t){const e=(t-pe)/Ar;let r,n,i;for(r=t-pe,n=0;r%Ar===Oe;r/=Ar,n++);for(i=Ar;i<t&&Jh(i,e,t)!==t-pe;i++)if(i>1e3)throw new Error("Cannot find square root: likely non-prime P");if(n===1){const o=(t+pe)/ta;return function(a,c){const u=a.pow(c,o);if(!a.eql(a.sqr(u),c))throw new Error("Cannot find square root");return u}}const s=(r+pe)/Ar;return function(o,a){if(o.pow(a,e)===o.neg(o.ONE))throw new Error("Cannot find square root");let c=n,u=o.pow(o.mul(o.ONE,i),r),l=o.pow(a,s),h=o.pow(a,r);for(;!o.eql(h,o.ONE);){if(o.eql(h,o.ZERO))return o.ZERO;let d=1;for(let f=o.sqr(h);d<c&&!o.eql(f,o.ONE);d++)f=o.sqr(f);const p=o.pow(u,pe<<BigInt(c-d-1));u=o.sqr(p),l=o.mul(l,p),h=o.mul(h,u),c=d}return l}}function m2(t){if(t%ta===w2){const e=(t+pe)/ta;return function(r,n){const i=r.pow(n,e);if(!r.eql(r.sqr(i),n))throw new Error("Cannot find square root");return i}}if(t%Xh===Zh){const e=(t-Zh)/Xh;return function(r,n){const i=r.mul(n,Ar),s=r.pow(i,e),o=r.mul(n,s),a=r.mul(r.mul(o,Ar),s),c=r.mul(o,r.sub(a,r.ONE));if(!r.eql(r.sqr(c),n))throw new Error("Cannot find square root");return c}}return b2(t)}const v2=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function E2(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},r=v2.reduce((n,i)=>(n[i]="function",n),e);return pn(t,r)}function _2(t,e,r){if(r<Oe)throw new Error("invalid exponent, negatives unsupported");if(r===Oe)return t.ONE;if(r===pe)return e;let n=t.ONE,i=e;for(;r>Oe;)r&pe&&(n=t.mul(n,i)),i=t.sqr(i),r>>=pe;return n}function S2(t,e){const r=new Array(e.length),n=e.reduce((s,o,a)=>t.is0(o)?s:(r[a]=s,t.mul(s,o)),t.ONE),i=t.inv(n);return e.reduceRight((s,o,a)=>t.is0(o)?s:(r[a]=t.mul(s,r[a]),t.mul(s,o)),i),r}function Qh(t,e){const r=e!==void 0?e:t.toString(2).length,n=Math.ceil(r/8);return{nBitLength:r,nByteLength:n}}function ed(t,e,r=!1,n={}){if(t<=Oe)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:i,nByteLength:s}=Qh(t,e);if(s>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:r,BITS:i,BYTES:s,MASK:Jo(i),ZERO:Oe,ONE:pe,create:c=>Ze(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return Oe<=c&&c<t},is0:c=>c===Oe,isOdd:c=>(c&pe)===pe,neg:c=>Ze(-c,t),eql:(c,u)=>c===u,sqr:c=>Ze(c*c,t),add:(c,u)=>Ze(c+u,t),sub:(c,u)=>Ze(c-u,t),mul:(c,u)=>Ze(c*u,t),pow:(c,u)=>_2(a,c,u),div:(c,u)=>Ze(c*ra(u,t),t),sqrN:c=>c*c,addN:(c,u)=>c+u,subN:(c,u)=>c-u,mulN:(c,u)=>c*u,inv:c=>ra(c,t),sqrt:n.sqrt||(c=>(o||(o=m2(t)),o(a,c))),invertBatch:c=>S2(a,c),cmov:(c,u,l)=>l?u:c,toBytes:c=>r?ts(c,s):fn(c,s),fromBytes:c=>{if(c.length!==s)throw new Error("Field.fromBytes: expected "+s+" bytes, got "+c.length);return r?Vn(c):Dr(c)}});return Object.freeze(a)}function td(t){if(typeof t!="bigint")throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function rd(t){const e=td(t);return e+Math.ceil(e/2)}function I2(t,e,r=!1){const n=t.length,i=td(e),s=rd(e);if(n<16||n<s||n>1024)throw new Error("expected "+s+"-1024 bytes of input, got "+n);const o=r?Vn(t):Dr(t),a=Ze(o,e-pe)+pe;return r?ts(a,i):fn(a,i)}const nd=BigInt(0),ns=BigInt(1);function na(t,e){const r=e.negate();return t?r:e}function id(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function ia(t,e){id(t,e);const r=Math.ceil(e/t)+1,n=2**(t-1);return{windows:r,windowSize:n}}function x2(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((r,n)=>{if(!(r instanceof e))throw new Error("invalid point at index "+n)})}function O2(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((r,n)=>{if(!e.isValid(r))throw new Error("invalid scalar at index "+n)})}const sa=new WeakMap,sd=new WeakMap;function oa(t){return sd.get(t)||1}function D2(t,e){return{constTimeNegate:na,hasPrecomputes(r){return oa(r)!==1},unsafeLadder(r,n,i=t.ZERO){let s=r;for(;n>nd;)n&ns&&(i=i.add(s)),s=s.double(),n>>=ns;return i},precomputeWindow(r,n){const{windows:i,windowSize:s}=ia(n,e),o=[];let a=r,c=a;for(let u=0;u<i;u++){c=a,o.push(c);for(let l=1;l<s;l++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(r,n,i){const{windows:s,windowSize:o}=ia(r,e);let a=t.ZERO,c=t.BASE;const u=BigInt(2**r-1),l=2**r,h=BigInt(r);for(let d=0;d<s;d++){const p=d*o;let f=Number(i&u);i>>=h,f>o&&(f-=l,i+=ns);const y=p,g=p+Math.abs(f)-1,b=d%2!==0,w=f<0;f===0?c=c.add(na(b,n[y])):a=a.add(na(w,n[g]))}return{p:a,f:c}},wNAFUnsafe(r,n,i,s=t.ZERO){const{windows:o,windowSize:a}=ia(r,e),c=BigInt(2**r-1),u=2**r,l=BigInt(r);for(let h=0;h<o;h++){const d=h*a;if(i===nd)break;let p=Number(i&c);if(i>>=l,p>a&&(p-=u,i+=ns),p===0)continue;let f=n[d+Math.abs(p)-1];p<0&&(f=f.negate()),s=s.add(f)}return s},getPrecomputes(r,n,i){let s=sa.get(n);return s||(s=this.precomputeWindow(n,r),r!==1&&sa.set(n,i(s))),s},wNAFCached(r,n,i){const s=oa(r);return this.wNAF(s,this.getPrecomputes(s,r,i),n)},wNAFCachedUnsafe(r,n,i,s){const o=oa(r);return o===1?this.unsafeLadder(r,n,s):this.wNAFUnsafe(o,this.getPrecomputes(o,r,i),n,s)},setWindowSize(r,n){id(n,e),sd.set(r,n),sa.delete(r)}}}function A2(t,e,r,n){if(x2(r,t),O2(n,e),r.length!==n.length)throw new Error("arrays of points and scalars must have equal length");const i=t.ZERO,s=Wh(BigInt(r.length)),o=s>12?s-3:s>4?s-2:s?2:1,a=(1<<o)-1,c=new Array(a+1).fill(i),u=Math.floor((e.BITS-1)/o)*o;let l=i;for(let h=u;h>=0;h-=o){c.fill(i);for(let p=0;p<n.length;p++){const f=n[p],y=Number(f>>BigInt(h)&BigInt(a));c[y]=c[y].add(r[p])}let d=i;for(let p=c.length-1,f=i;p>0;p--)f=f.add(c[p]),d=d.add(f);if(l=l.add(d),h!==0)for(let p=0;p<o;p++)l=l.double()}return l}function od(t){return E2(t.Fp),pn(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze(Bo(Ut(Ut({},Qh(t.n,t.nBitLength)),t),{p:t.Fp.ORDER}))}BigInt(0),BigInt(1),BigInt(2),BigInt(8);const gn=BigInt(0),aa=BigInt(1);function $2(t){return pn(t,{a:"bigint"},{montgomeryBits:"isSafeInteger",nByteLength:"isSafeInteger",adjustScalarBytes:"function",domain:"function",powPminus2:"function",Gu:"bigint"}),Object.freeze(Ut({},t))}function T2(t){const e=$2(t),{P:r}=e,n=w=>Ze(w,r),i=e.montgomeryBits,s=Math.ceil(i/8),o=e.nByteLength,a=e.adjustScalarBytes||(w=>w),c=e.powPminus2||(w=>Jh(w,r-BigInt(2),r));function u(w,m,E){const $=n(w*(m-E));return m=n(m-$),E=n(E+$),[m,E]}const l=(e.a-BigInt(2))/BigInt(4);function h(w,m){jt("u",w,gn,r),jt("scalar",m,gn,r);const E=m,$=w;let O=aa,S=gn,T=w,I=aa,j=gn,R;for(let M=BigInt(i-1);M>=gn;M--){const x=E>>M&aa;j^=x,R=u(j,O,T),O=R[0],T=R[1],R=u(j,S,I),S=R[0],I=R[1],j=x;const _=O+S,v=n(_*_),D=O-S,P=n(D*D),A=v-P,C=T+I,U=T-I,k=n(U*_),q=n(C*D),z=k+q,V=k-q;T=n(z*z),I=n($*n(V*V)),O=n(v*P),S=n(A*(v+n(l*A)))}R=u(j,O,T),O=R[0],T=R[1],R=u(j,S,I),S=R[0],I=R[1];const B=c(S);return n(O*B)}function d(w){return ts(n(w),s)}function p(w){const m=rt("u coordinate",w,s);return o===32&&(m[31]&=127),Vn(m)}function f(w){const m=rt("scalar",w),E=m.length;if(E!==s&&E!==o){let $=""+s+" or "+o;throw new Error("invalid scalar, expected "+$+" bytes, got "+E)}return Vn(a(m))}function y(w,m){const E=p(m),$=f(w),O=h(E,$);if(O===gn)throw new Error("invalid private or public key received");return d(O)}const g=d(e.Gu);function b(w){return y(w,g)}return{scalarMult:y,scalarMultBase:b,getSharedSecret:(w,m)=>y(w,m),getPublicKey:w=>b(w),utils:{randomPrivateKey:()=>e.randomBytes(e.nByteLength)},GuBytes:g}}const ca=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949");BigInt(0);const P2=BigInt(1),ad=BigInt(2),N2=BigInt(3),R2=BigInt(5);BigInt(8);function C2(t){const e=BigInt(10),r=BigInt(20),n=BigInt(40),i=BigInt(80),s=ca,o=t*t%s*t%s,a=dt(o,ad,s)*o%s,c=dt(a,P2,s)*t%s,u=dt(c,R2,s)*c%s,l=dt(u,e,s)*u%s,h=dt(l,r,s)*l%s,d=dt(h,n,s)*h%s,p=dt(d,i,s)*d%s,f=dt(p,i,s)*d%s,y=dt(f,e,s)*u%s;return{pow_p_5_8:dt(y,ad,s)*t%s,b2:o}}function B2(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}const ua=T2({P:ca,a:BigInt(486662),montgomeryBits:255,nByteLength:32,Gu:BigInt(9),powPminus2:t=>{const e=ca,{pow_p_5_8:r,b2:n}=C2(t);return Ze(dt(r,N2,e)*n,e)},adjustScalarBytes:B2,randomBytes:an});function cd(t){t.lowS!==void 0&&un("lowS",t.lowS),t.prehash!==void 0&&un("prehash",t.prehash)}function F2(t){const e=od(t);pn(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:r,Fp:n,a:i}=e;if(r){if(!n.eql(i,n.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof r!="object"||typeof r.beta!="bigint"||typeof r.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze(Ut({},e))}const{bytesToNumberBE:L2,hexToBytes:U2}=y2;class k2 extends Error{constructor(e=""){super(e)}}const Mt={Err:k2,_tlv:{encode:(t,e)=>{const{Err:r}=Mt;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length&1)throw new r("tlv.encode: unpadded data");const n=e.length/2,i=hn(n);if(i.length/2&128)throw new r("tlv.encode: long form length too big");const s=n>127?hn(i.length/2|128):"";return hn(t)+s+i+e},decode(t,e){const{Err:r}=Mt;let n=0;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length<2||e[n++]!==t)throw new r("tlv.decode: wrong tlv");const i=e[n++],s=!!(i&128);let o=0;if(!s)o=i;else{const c=i&127;if(!c)throw new r("tlv.decode(long): indefinite length not supported");if(c>4)throw new r("tlv.decode(long): byte length is too big");const u=e.subarray(n,n+c);if(u.length!==c)throw new r("tlv.decode: length bytes not complete");if(u[0]===0)throw new r("tlv.decode(long): zero leftmost byte");for(const l of u)o=o<<8|l;if(n+=c,o<128)throw new r("tlv.decode(long): not minimal encoding")}const a=e.subarray(n,n+o);if(a.length!==o)throw new r("tlv.decode: wrong value length");return{v:a,l:e.subarray(n+o)}}},_int:{encode(t){const{Err:e}=Mt;if(t<qt)throw new e("integer: negative integers are not allowed");let r=hn(t);if(Number.parseInt(r[0],16)&8&&(r="00"+r),r.length&1)throw new e("unexpected DER parsing assertion: unpadded hex");return r},decode(t){const{Err:e}=Mt;if(t[0]&128)throw new e("invalid signature integer: negative");if(t[0]===0&&!(t[1]&128))throw new e("invalid signature integer: unnecessary leading zero");return L2(t)}},toSig(t){const{Err:e,_int:r,_tlv:n}=Mt,i=typeof t=="string"?U2(t):t;zn(i);const{v:s,l:o}=n.decode(48,i);if(o.length)throw new e("invalid signature: left bytes after parsing");const{v:a,l:c}=n.decode(2,s),{v:u,l}=n.decode(2,c);if(l.length)throw new e("invalid signature: left bytes after parsing");return{r:r.decode(a),s:r.decode(u)}},hexFromSig(t){const{_tlv:e,_int:r}=Mt,n=e.encode(2,r.encode(t.r)),i=e.encode(2,r.encode(t.s)),s=n+i;return e.encode(48,s)}},qt=BigInt(0),De=BigInt(1);BigInt(2);const ud=BigInt(3);BigInt(4);function j2(t){const e=F2(t),{Fp:r}=e,n=ed(e.n,e.nBitLength),i=e.toBytes||((y,g,b)=>{const w=g.toAffine();return Kn(Uint8Array.from([4]),r.toBytes(w.x),r.toBytes(w.y))}),s=e.fromBytes||(y=>{const g=y.subarray(1),b=r.fromBytes(g.subarray(0,r.BYTES)),w=r.fromBytes(g.subarray(r.BYTES,2*r.BYTES));return{x:b,y:w}});function o(y){const{a:g,b}=e,w=r.sqr(y),m=r.mul(w,y);return r.add(r.add(m,r.mul(y,g)),b)}if(!r.eql(r.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function a(y){return rs(y,De,e.n)}function c(y){const{allowedPrivateKeyLengths:g,nByteLength:b,wrapPrivateKey:w,n:m}=e;if(g&&typeof y!="bigint"){if(Or(y)&&(y=ln(y)),typeof y!="string"||!g.includes(y.length))throw new Error("invalid private key");y=y.padStart(b*2,"0")}let E;try{E=typeof y=="bigint"?y:Dr(rt("private key",y,b))}catch{throw new Error("invalid private key, expected hex or "+b+" bytes, got "+typeof y)}return w&&(E=Ze(E,m)),jt("private key",E,De,m),E}function u(y){if(!(y instanceof d))throw new Error("ProjectivePoint expected")}const l=ea((y,g)=>{const{px:b,py:w,pz:m}=y;if(r.eql(m,r.ONE))return{x:b,y:w};const E=y.is0();g==null&&(g=E?r.ONE:r.inv(m));const $=r.mul(b,g),O=r.mul(w,g),S=r.mul(m,g);if(E)return{x:r.ZERO,y:r.ZERO};if(!r.eql(S,r.ONE))throw new Error("invZ was invalid");return{x:$,y:O}}),h=ea(y=>{if(y.is0()){if(e.allowInfinityPoint&&!r.is0(y.py))return;throw new Error("bad point: ZERO")}const{x:g,y:b}=y.toAffine();if(!r.isValid(g)||!r.isValid(b))throw new Error("bad point: x or y not FE");const w=r.sqr(b),m=o(g);if(!r.eql(w,m))throw new Error("bad point: equation left != right");if(!y.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class d{constructor(g,b,w){if(this.px=g,this.py=b,this.pz=w,g==null||!r.isValid(g))throw new Error("x required");if(b==null||!r.isValid(b))throw new Error("y required");if(w==null||!r.isValid(w))throw new Error("z required");Object.freeze(this)}static fromAffine(g){const{x:b,y:w}=g||{};if(!g||!r.isValid(b)||!r.isValid(w))throw new Error("invalid affine point");if(g instanceof d)throw new Error("projective point not allowed");const m=E=>r.eql(E,r.ZERO);return m(b)&&m(w)?d.ZERO:new d(b,w,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(g){const b=r.invertBatch(g.map(w=>w.pz));return g.map((w,m)=>w.toAffine(b[m])).map(d.fromAffine)}static fromHex(g){const b=d.fromAffine(s(rt("pointHex",g)));return b.assertValidity(),b}static fromPrivateKey(g){return d.BASE.multiply(c(g))}static msm(g,b){return A2(d,n,g,b)}_setWindowSize(g){f.setWindowSize(this,g)}assertValidity(){h(this)}hasEvenY(){const{y:g}=this.toAffine();if(r.isOdd)return!r.isOdd(g);throw new Error("Field doesn't support isOdd")}equals(g){u(g);const{px:b,py:w,pz:m}=this,{px:E,py:$,pz:O}=g,S=r.eql(r.mul(b,O),r.mul(E,m)),T=r.eql(r.mul(w,O),r.mul($,m));return S&&T}negate(){return new d(this.px,r.neg(this.py),this.pz)}double(){const{a:g,b}=e,w=r.mul(b,ud),{px:m,py:E,pz:$}=this;let O=r.ZERO,S=r.ZERO,T=r.ZERO,I=r.mul(m,m),j=r.mul(E,E),R=r.mul($,$),B=r.mul(m,E);return B=r.add(B,B),T=r.mul(m,$),T=r.add(T,T),O=r.mul(g,T),S=r.mul(w,R),S=r.add(O,S),O=r.sub(j,S),S=r.add(j,S),S=r.mul(O,S),O=r.mul(B,O),T=r.mul(w,T),R=r.mul(g,R),B=r.sub(I,R),B=r.mul(g,B),B=r.add(B,T),T=r.add(I,I),I=r.add(T,I),I=r.add(I,R),I=r.mul(I,B),S=r.add(S,I),R=r.mul(E,$),R=r.add(R,R),I=r.mul(R,B),O=r.sub(O,I),T=r.mul(R,j),T=r.add(T,T),T=r.add(T,T),new d(O,S,T)}add(g){u(g);const{px:b,py:w,pz:m}=this,{px:E,py:$,pz:O}=g;let S=r.ZERO,T=r.ZERO,I=r.ZERO;const j=e.a,R=r.mul(e.b,ud);let B=r.mul(b,E),M=r.mul(w,$),x=r.mul(m,O),_=r.add(b,w),v=r.add(E,$);_=r.mul(_,v),v=r.add(B,M),_=r.sub(_,v),v=r.add(b,m);let D=r.add(E,O);return v=r.mul(v,D),D=r.add(B,x),v=r.sub(v,D),D=r.add(w,m),S=r.add($,O),D=r.mul(D,S),S=r.add(M,x),D=r.sub(D,S),I=r.mul(j,v),S=r.mul(R,x),I=r.add(S,I),S=r.sub(M,I),I=r.add(M,I),T=r.mul(S,I),M=r.add(B,B),M=r.add(M,B),x=r.mul(j,x),v=r.mul(R,v),M=r.add(M,x),x=r.sub(B,x),x=r.mul(j,x),v=r.add(v,x),B=r.mul(M,v),T=r.add(T,B),B=r.mul(D,v),S=r.mul(_,S),S=r.sub(S,B),B=r.mul(_,M),I=r.mul(D,I),I=r.add(I,B),new d(S,T,I)}subtract(g){return this.add(g.negate())}is0(){return this.equals(d.ZERO)}wNAF(g){return f.wNAFCached(this,g,d.normalizeZ)}multiplyUnsafe(g){const{endo:b,n:w}=e;jt("scalar",g,qt,w);const m=d.ZERO;if(g===qt)return m;if(this.is0()||g===De)return this;if(!b||f.hasPrecomputes(this))return f.wNAFCachedUnsafe(this,g,d.normalizeZ);let{k1neg:E,k1:$,k2neg:O,k2:S}=b.splitScalar(g),T=m,I=m,j=this;for(;$>qt||S>qt;)$&De&&(T=T.add(j)),S&De&&(I=I.add(j)),j=j.double(),$>>=De,S>>=De;return E&&(T=T.negate()),O&&(I=I.negate()),I=new d(r.mul(I.px,b.beta),I.py,I.pz),T.add(I)}multiply(g){const{endo:b,n:w}=e;jt("scalar",g,De,w);let m,E;if(b){const{k1neg:$,k1:O,k2neg:S,k2:T}=b.splitScalar(g);let{p:I,f:j}=this.wNAF(O),{p:R,f:B}=this.wNAF(T);I=f.constTimeNegate($,I),R=f.constTimeNegate(S,R),R=new d(r.mul(R.px,b.beta),R.py,R.pz),m=I.add(R),E=j.add(B)}else{const{p:$,f:O}=this.wNAF(g);m=$,E=O}return d.normalizeZ([m,E])[0]}multiplyAndAddUnsafe(g,b,w){const m=d.BASE,E=(O,S)=>S===qt||S===De||!O.equals(m)?O.multiplyUnsafe(S):O.multiply(S),$=E(this,b).add(E(g,w));return $.is0()?void 0:$}toAffine(g){return l(this,g)}isTorsionFree(){const{h:g,isTorsionFree:b}=e;if(g===De)return!0;if(b)return b(d,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:g,clearCofactor:b}=e;return g===De?this:b?b(d,this):this.multiplyUnsafe(e.h)}toRawBytes(g=!0){return un("isCompressed",g),this.assertValidity(),i(d,this,g)}toHex(g=!0){return un("isCompressed",g),ln(this.toRawBytes(g))}}d.BASE=new d(e.Gx,e.Gy,r.ONE),d.ZERO=new d(r.ZERO,r.ONE,r.ZERO);const p=e.nBitLength,f=D2(d,e.endo?Math.ceil(p/2):p);return{CURVE:e,ProjectivePoint:d,normPrivateKeyToScalar:c,weierstrassEquation:o,isWithinCurveOrder:a}}function M2(t){const e=od(t);return pn(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze(Ut({lowS:!0},e))}function q2(t){const e=M2(t),{Fp:r,n}=e,i=r.BYTES+1,s=2*r.BYTES+1;function o(x){return Ze(x,n)}function a(x){return ra(x,n)}const{ProjectivePoint:c,normPrivateKeyToScalar:u,weierstrassEquation:l,isWithinCurveOrder:h}=j2(Bo(Ut({},e),{toBytes(x,_,v){const D=_.toAffine(),P=r.toBytes(D.x),A=Kn;return un("isCompressed",v),v?A(Uint8Array.from([_.hasEvenY()?2:3]),P):A(Uint8Array.from([4]),P,r.toBytes(D.y))},fromBytes(x){const _=x.length,v=x[0],D=x.subarray(1);if(_===i&&(v===2||v===3)){const P=Dr(D);if(!rs(P,De,r.ORDER))throw new Error("Point is not on curve");const A=l(P);let C;try{C=r.sqrt(A)}catch(k){const q=k instanceof Error?": "+k.message:"";throw new Error("Point is not on curve"+q)}const U=(C&De)===De;return(v&1)===1!==U&&(C=r.neg(C)),{x:P,y:C}}else if(_===s&&v===4){const P=r.fromBytes(D.subarray(0,r.BYTES)),A=r.fromBytes(D.subarray(r.BYTES,2*r.BYTES));return{x:P,y:A}}else{const P=i,A=s;throw new Error("invalid Point, expected length of "+P+", or uncompressed "+A+", got "+_)}}})),d=x=>ln(fn(x,e.nByteLength));function p(x){const _=n>>De;return x>_}function f(x){return p(x)?o(-x):x}const y=(x,_,v)=>Dr(x.slice(_,v));class g{constructor(_,v,D){this.r=_,this.s=v,this.recovery=D,this.assertValidity()}static fromCompact(_){const v=e.nByteLength;return _=rt("compactSignature",_,v*2),new g(y(_,0,v),y(_,v,2*v))}static fromDER(_){const{r:v,s:D}=Mt.toSig(rt("DER",_));return new g(v,D)}assertValidity(){jt("r",this.r,De,n),jt("s",this.s,De,n)}addRecoveryBit(_){return new g(this.r,this.s,_)}recoverPublicKey(_){const{r:v,s:D,recovery:P}=this,A=O(rt("msgHash",_));if(P==null||![0,1,2,3].includes(P))throw new Error("recovery id invalid");const C=P===2||P===3?v+e.n:v;if(C>=r.ORDER)throw new Error("recovery id 2 or 3 invalid");const U=(P&1)===0?"02":"03",k=c.fromHex(U+d(C)),q=a(C),z=o(-A*q),V=o(D*q),K=c.BASE.multiplyAndAddUnsafe(k,z,V);if(!K)throw new Error("point at infinify");return K.assertValidity(),K}hasHighS(){return p(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return dn(this.toDERHex())}toDERHex(){return Mt.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return dn(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const b={isValidPrivateKey(x){try{return u(x),!0}catch{return!1}},normPrivateKeyToScalar:u,randomPrivateKey:()=>{const x=rd(e.n);return I2(e.randomBytes(x),e.n)},precompute(x=8,_=c.BASE){return _._setWindowSize(x),_.multiply(BigInt(3)),_}};function w(x,_=!0){return c.fromPrivateKey(x).toRawBytes(_)}function m(x){const _=Or(x),v=typeof x=="string",D=(_||v)&&x.length;return _?D===i||D===s:v?D===2*i||D===2*s:x instanceof c}function E(x,_,v=!0){if(m(x))throw new Error("first arg must be private key");if(!m(_))throw new Error("second arg must be public key");return c.fromHex(_).multiply(u(x)).toRawBytes(v)}const $=e.bits2int||function(x){if(x.length>8192)throw new Error("input is too large");const _=Dr(x),v=x.length*8-e.nBitLength;return v>0?_>>BigInt(v):_},O=e.bits2int_modN||function(x){return o($(x))},S=Jo(e.nBitLength);function T(x){return jt("num < 2^"+e.nBitLength,x,qt,S),fn(x,e.nByteLength)}function I(x,_,v=j){if(["recovered","canonical"].some(Y=>Y in v))throw new Error("sign() legacy options not supported");const{hash:D,randomBytes:P}=e;let{lowS:A,prehash:C,extraEntropy:U}=v;A==null&&(A=!0),x=rt("msgHash",x),cd(v),C&&(x=rt("prehashed msgHash",D(x)));const k=O(x),q=u(_),z=[T(q),T(k)];if(U!=null&&U!==!1){const Y=U===!0?P(r.BYTES):U;z.push(rt("extraEntropy",Y))}const V=Kn(...z),K=k;function ee(Y){const G=$(Y);if(!h(G))return;const ge=a(G),le=c.BASE.multiply(G).toAffine(),he=o(le.x);if(he===qt)return;const Te=o(ge*o(K+he*q));if(Te===qt)return;let be=(le.x===he?0:2)|Number(le.y&De),Wt=Te;return A&&p(Te)&&(Wt=f(Te),be^=1),new g(he,Wt,be)}return{seed:V,k2sig:ee}}const j={lowS:e.lowS,prehash:!1},R={lowS:e.lowS,prehash:!1};function B(x,_,v=j){const{seed:D,k2sig:P}=I(x,_,v),A=e;return Yh(A.hash.outputLen,A.nByteLength,A.hmac)(D,P)}c.BASE._setWindowSize(8);function M(x,_,v,D=R){var P;const A=x;_=rt("msgHash",_),v=rt("publicKey",v);const{lowS:C,prehash:U,format:k}=D;if(cd(D),"strict"in D)throw new Error("options.strict was renamed to lowS");if(k!==void 0&&k!=="compact"&&k!=="der")throw new Error("format must be compact or der");const q=typeof A=="string"||Or(A),z=!q&&!k&&typeof A=="object"&&A!==null&&typeof A.r=="bigint"&&typeof A.s=="bigint";if(!q&&!z)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let V,K;try{if(z&&(V=new g(A.r,A.s)),q){try{k!=="compact"&&(V=g.fromDER(A))}catch(be){if(!(be instanceof Mt.Err))throw be}!V&&k!=="der"&&(V=g.fromCompact(A))}K=c.fromHex(v)}catch{return!1}if(!V||C&&V.hasHighS())return!1;U&&(_=e.hash(_));const{r:ee,s:Y}=V,G=O(_),ge=a(Y),le=o(G*ge),he=o(ee*ge),Te=(P=c.BASE.multiplyAndAddUnsafe(K,le,he))==null?void 0:P.toAffine();return Te?o(Te.x)===ee:!1}return{CURVE:e,getPublicKey:w,getSharedSecret:E,sign:B,verify:M,ProjectivePoint:c,Signature:g,utils:b}}function z2(t){return{hash:t,hmac:(e,...r)=>Ji(t,e,YE(...r)),randomBytes:an}}function V2(t,e){const r=n=>q2(Ut(Ut({},t),z2(n)));return Bo(Ut({},r(e)),{create:r})}const ld=ed(BigInt("0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff")),K2=ld.create(BigInt("-3")),H2=BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"),W2=V2({a:K2,b:H2,Fp:ld,n:BigInt("0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"),Gx:BigInt("0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"),Gy:BigInt("0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"),h:BigInt(1),lowS:!1},qn),hd="base10",qe="base16",ft="base64pad",ur="base64url",Hn="utf8",dd=0,zt=1,Wn=2,G2=0,fd=1,Gn=12,la=32;function Y2(){const t=ua.utils.randomPrivateKey(),e=ua.getPublicKey(t);return{privateKey:Ye(t,qe),publicKey:Ye(e,qe)}}function ha(){const t=an(la);return Ye(t,qe)}function Z2(t,e){const r=ua.getSharedSecret(ot(t,qe),ot(e,qe)),n=e2(qn,r,void 0,void 0,la);return Ye(n,qe)}function is(t){const e=qn(ot(t,qe));return Ye(e,qe)}function xt(t){const e=qn(ot(t,Hn));return Ye(e,qe)}function pd(t){return ot(`${t}`,hd)}function $r(t){return Number(Ye(t,hd))}function gd(t){return t.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function yd(t){const e=t.replace(/-/g,"+").replace(/_/g,"/"),r=(4-e.length%4)%4;return e+"=".repeat(r)}function X2(t){const e=pd(typeof t.type<"u"?t.type:dd);if($r(e)===zt&&typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");const r=typeof t.senderPublicKey<"u"?ot(t.senderPublicKey,qe):void 0,n=typeof t.iv<"u"?ot(t.iv,qe):an(Gn),i=ot(t.symKey,qe),s=zh(i,n).encrypt(ot(t.message,Hn)),o=wd({type:e,sealed:s,iv:n,senderPublicKey:r});return t.encoding===ur?gd(o):o}function J2(t){const e=ot(t.symKey,qe),{sealed:r,iv:n}=Yn({encoded:t.encoded,encoding:t.encoding}),i=zh(e,n).decrypt(r);if(i===null)throw new Error("Failed to decrypt");return Ye(i,Hn)}function Q2(t,e){const r=pd(Wn),n=an(Gn),i=ot(t,Hn),s=wd({type:r,sealed:i,iv:n});return e===ur?gd(s):s}function eS(t,e){const{sealed:r}=Yn({encoded:t,encoding:e});return Ye(r,Hn)}function wd(t){if($r(t.type)===Wn)return Ye(Bn([t.type,t.sealed]),ft);if($r(t.type)===zt){if(typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");return Ye(Bn([t.type,t.senderPublicKey,t.iv,t.sealed]),ft)}return Ye(Bn([t.type,t.iv,t.sealed]),ft)}function Yn(t){const e=(t.encoding||ft)===ur?yd(t.encoded):t.encoded,r=ot(e,ft),n=r.slice(G2,fd),i=fd;if($r(n)===zt){const c=i+la,u=c+Gn,l=r.slice(i,c),h=r.slice(c,u),d=r.slice(u);return{type:n,sealed:d,iv:h,senderPublicKey:l}}if($r(n)===Wn){const c=r.slice(i),u=an(Gn);return{type:n,sealed:c,iv:u}}const s=i+Gn,o=r.slice(i,s),a=r.slice(s);return{type:n,sealed:a,iv:o}}function tS(t,e){const r=Yn({encoded:t,encoding:e?.encoding});return bd({type:$r(r.type),senderPublicKey:typeof r.senderPublicKey<"u"?Ye(r.senderPublicKey,qe):void 0,receiverPublicKey:e?.receiverPublicKey})}function bd(t){const e=t?.type||dd;if(e===zt){if(typeof t?.senderPublicKey>"u")throw new Error("missing sender public key");if(typeof t?.receiverPublicKey>"u")throw new Error("missing receiver public key")}return{type:e,senderPublicKey:t?.senderPublicKey,receiverPublicKey:t?.receiverPublicKey}}function md(t){return t.type===zt&&typeof t.senderPublicKey=="string"&&typeof t.receiverPublicKey=="string"}function vd(t){return t.type===Wn}function rS(t){const e=Buffer.from(t.x,"base64"),r=Buffer.from(t.y,"base64");return Bn([new Uint8Array([4]),e,r])}function nS(t,e){const[r,n,i]=t.split("."),s=Buffer.from(yd(i),"base64");if(s.length!==64)throw new Error("Invalid signature length");const o=s.slice(0,32),a=s.slice(32,64),c=`${r}.${n}`,u=qn(c),l=rS(e);if(!W2.verify(Bn([o,a]),u,l))throw new Error("Invalid signature");return Js(t).payload}const iS="irn";function ss(t){return t?.relay||{protocol:iS}}function Zn(t){const e=aE[t];if(typeof e>"u")throw new Error(`Relay Protocol not supported: ${t}`);return e}function sS(t,e="-"){const r={},n="relay"+e;return Object.keys(t).forEach(i=>{if(i.startsWith(n)){const s=i.replace(n,""),o=t[i];r[s]=o}}),r}function Ed(t){if(!t.includes("wc:")){const u=ph(t);u!=null&&u.includes("wc:")&&(t=u)}t=t.includes("wc://")?t.replace("wc://",""):t,t=t.includes("wc:")?t.replace("wc:",""):t;const e=t.indexOf(":"),r=t.indexOf("?")!==-1?t.indexOf("?"):void 0,n=t.substring(0,e),i=t.substring(e+1,r).split("@"),s=typeof r<"u"?t.substring(r):"",o=new URLSearchParams(s),a={};o.forEach((u,l)=>{a[l]=u});const c=typeof a.methods=="string"?a.methods.split(","):void 0;return{protocol:n,topic:oS(i[0]),version:parseInt(i[1],10),symKey:a.symKey,relay:sS(a),methods:c,expiryTimestamp:a.expiryTimestamp?parseInt(a.expiryTimestamp,10):void 0}}function oS(t){return t.startsWith("//")?t.substring(2):t}function aS(t,e="-"){const r="relay",n={};return Object.keys(t).forEach(i=>{const s=i,o=r+e+s;t[s]&&(n[o]=t[s])}),n}function _d(t){const e=new URLSearchParams,r=aS(t.relay);Object.keys(r).sort().forEach(i=>{e.set(i,r[i])}),e.set("symKey",t.symKey),t.expiryTimestamp&&e.set("expiryTimestamp",t.expiryTimestamp.toString()),t.methods&&e.set("methods",t.methods.join(","));const n=e.toString();return`${t.protocol}:${t.topic}@${t.version}?${n}`}function os(t,e,r){return`${t}?wc_ev=${r}&topic=${e}`}var cS=Object.defineProperty,uS=Object.defineProperties,lS=Object.getOwnPropertyDescriptors,Sd=Object.getOwnPropertySymbols,hS=Object.prototype.hasOwnProperty,dS=Object.prototype.propertyIsEnumerable,Id=(t,e,r)=>e in t?cS(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,fS=(t,e)=>{for(var r in e||(e={}))hS.call(e,r)&&Id(t,r,e[r]);if(Sd)for(var r of Sd(e))dS.call(e,r)&&Id(t,r,e[r]);return t},pS=(t,e)=>uS(t,lS(e));function yn(t){const e=[];return t.forEach(r=>{const[n,i]=r.split(":");e.push(`${n}:${i}`)}),e}function gS(t){const e=[];return Object.values(t).forEach(r=>{e.push(...yn(r.accounts))}),e}function yS(t,e){const r=[];return Object.values(t).forEach(n=>{yn(n.accounts).includes(e)&&r.push(...n.methods)}),r}function wS(t,e){const r=[];return Object.values(t).forEach(n=>{yn(n.accounts).includes(e)&&r.push(...n.events)}),r}function xd(t){return t.includes(":")}function bS(t){return xd(t)?t.split(":")[0]:t}function Od(t){var e,r,n;const i={};if(!Jn(t))return i;for(const[s,o]of Object.entries(t)){const a=xd(s)?[s]:o.chains,c=o.methods||[],u=o.events||[],l=bS(s);i[l]=pS(fS({},i[l]),{chains:Ir(a,(e=i[l])==null?void 0:e.chains),methods:Ir(c,(r=i[l])==null?void 0:r.methods),events:Ir(u,(n=i[l])==null?void 0:n.events)})}return i}function mS(t){const e={};return t?.forEach(r=>{var n;const[i,s]=r.split(":");e[i]||(e[i]={accounts:[],chains:[],events:[],methods:[]}),e[i].accounts.push(r),(n=e[i].chains)==null||n.push(`${i}:${s}`)}),e}function Dd(t,e){e=e.map(n=>n.replace("did:pkh:",""));const r=mS(e);for(const[n,i]of Object.entries(r))i.methods?i.methods=Ir(i.methods,t):i.methods=t,i.events=["chainChanged","accountsChanged"];return r}function vS(t,e){var r,n,i,s,o,a;const c=Od(t),u=Od(e),l={},h=Object.keys(c).concat(Object.keys(u));for(const d of h)l[d]={chains:Ir((r=c[d])==null?void 0:r.chains,(n=u[d])==null?void 0:n.chains),methods:Ir((i=c[d])==null?void 0:i.methods,(s=u[d])==null?void 0:s.methods),events:Ir((o=c[d])==null?void 0:o.events,(a=u[d])==null?void 0:a.events)};return l}const ES={INVALID_METHOD:{message:"Invalid method.",code:1001},INVALID_EVENT:{message:"Invalid event.",code:1002},INVALID_UPDATE_REQUEST:{message:"Invalid update request.",code:1003},INVALID_EXTEND_REQUEST:{message:"Invalid extend request.",code:1004},INVALID_SESSION_SETTLE_REQUEST:{message:"Invalid session settle request.",code:1005},UNAUTHORIZED_METHOD:{message:"Unauthorized method.",code:3001},UNAUTHORIZED_EVENT:{message:"Unauthorized event.",code:3002},UNAUTHORIZED_UPDATE_REQUEST:{message:"Unauthorized update request.",code:3003},UNAUTHORIZED_EXTEND_REQUEST:{message:"Unauthorized extend request.",code:3004},USER_REJECTED:{message:"User rejected.",code:5e3},USER_REJECTED_CHAINS:{message:"User rejected chains.",code:5001},USER_REJECTED_METHODS:{message:"User rejected methods.",code:5002},USER_REJECTED_EVENTS:{message:"User rejected events.",code:5003},UNSUPPORTED_CHAINS:{message:"Unsupported chains.",code:5100},UNSUPPORTED_METHODS:{message:"Unsupported methods.",code:5101},UNSUPPORTED_EVENTS:{message:"Unsupported events.",code:5102},UNSUPPORTED_ACCOUNTS:{message:"Unsupported accounts.",code:5103},UNSUPPORTED_NAMESPACE_KEY:{message:"Unsupported namespace key.",code:5104},USER_DISCONNECTED:{message:"User disconnected.",code:6e3},SESSION_SETTLEMENT_FAILED:{message:"Session settlement failed.",code:7e3},WC_METHOD_UNSUPPORTED:{message:"Unsupported wc_ method.",code:10001}},_S={NOT_INITIALIZED:{message:"Not initialized.",code:1},NO_MATCHING_KEY:{message:"No matching key.",code:2},RESTORE_WILL_OVERRIDE:{message:"Restore will override.",code:3},RESUBSCRIBED:{message:"Resubscribed.",code:4},MISSING_OR_INVALID:{message:"Missing or invalid.",code:5},EXPIRED:{message:"Expired.",code:6},UNKNOWN_TYPE:{message:"Unknown type.",code:7},MISMATCHED_TOPIC:{message:"Mismatched topic.",code:8},NON_CONFORMING_NAMESPACES:{message:"Non conforming namespaces.",code:9}};function L(t,e){const{message:r,code:n}=_S[t];return{message:e?`${r} ${e}`:r,code:n}}function ae(t,e){const{message:r,code:n}=ES[t];return{message:e?`${r} ${e}`:r,code:n}}function Xn(t,e){return Array.isArray(t)?typeof e<"u"&&t.length?t.every(e):!0:!1}function Jn(t){return Object.getPrototypeOf(t)===Object.prototype&&Object.keys(t).length}function Ce(t){return typeof t>"u"}function ye(t,e){return e&&Ce(t)?!0:typeof t=="string"&&!!t.trim().length}function da(t,e){return e&&Ce(t)?!0:typeof t=="number"&&!isNaN(t)}function SS(t,e){const{requiredNamespaces:r}=e,n=Object.keys(t.namespaces),i=Object.keys(r);let s=!0;return _r(i,n)?(n.forEach(o=>{const{accounts:a,methods:c,events:u}=t.namespaces[o],l=yn(a),h=r[o];(!_r(nh(o,h),l)||!_r(h.methods,c)||!_r(h.events,u))&&(s=!1)}),s):!1}function as(t){return ye(t,!1)&&t.includes(":")?t.split(":").length===2:!1}function IS(t){if(ye(t,!1)&&t.includes(":")){const e=t.split(":");if(e.length===3){const r=e[0]+":"+e[1];return!!e[2]&&as(r)}}return!1}function xS(t){function e(r){try{return typeof new URL(r)<"u"}catch{return!1}}try{if(ye(t,!1)){if(e(t))return!0;const r=ph(t);return e(r)}}catch{}return!1}function OS(t){var e;return(e=t?.proposer)==null?void 0:e.publicKey}function DS(t){return t?.topic}function AS(t,e){let r=null;return ye(t?.publicKey,!1)||(r=L("MISSING_OR_INVALID",`${e} controller public key should be a string`)),r}function Ad(t){let e=!0;return Xn(t)?t.length&&(e=t.every(r=>ye(r,!1))):e=!1,e}function $S(t,e,r){let n=null;return Xn(e)&&e.length?e.forEach(i=>{n||as(i)||(n=ae("UNSUPPORTED_CHAINS",`${r}, chain ${i} should be a string and conform to "namespace:chainId" format`))}):as(t)||(n=ae("UNSUPPORTED_CHAINS",`${r}, chains must be defined as "namespace:chainId" e.g. "eip155:1": {...} in the namespace key OR as an array of CAIP-2 chainIds e.g. eip155: { chains: ["eip155:1", "eip155:5"] }`)),n}function TS(t,e,r){let n=null;return Object.entries(t).forEach(([i,s])=>{if(n)return;const o=$S(i,nh(i,s),`${e} ${r}`);o&&(n=o)}),n}function PS(t,e){let r=null;return Xn(t)?t.forEach(n=>{r||IS(n)||(r=ae("UNSUPPORTED_ACCOUNTS",`${e}, account ${n} should be a string and conform to "namespace:chainId:address" format`))}):r=ae("UNSUPPORTED_ACCOUNTS",`${e}, accounts should be an array of strings conforming to "namespace:chainId:address" format`),r}function NS(t,e){let r=null;return Object.values(t).forEach(n=>{if(r)return;const i=PS(n?.accounts,`${e} namespace`);i&&(r=i)}),r}function RS(t,e){let r=null;return Ad(t?.methods)?Ad(t?.events)||(r=ae("UNSUPPORTED_EVENTS",`${e}, events should be an array of strings or empty array for no events`)):r=ae("UNSUPPORTED_METHODS",`${e}, methods should be an array of strings or empty array for no methods`),r}function $d(t,e){let r=null;return Object.values(t).forEach(n=>{if(r)return;const i=RS(n,`${e}, namespace`);i&&(r=i)}),r}function CS(t,e,r){let n=null;if(t&&Jn(t)){const i=$d(t,e);i&&(n=i);const s=TS(t,e,r);s&&(n=s)}else n=L("MISSING_OR_INVALID",`${e}, ${r} should be an object with data`);return n}function fa(t,e){let r=null;if(t&&Jn(t)){const n=$d(t,e);n&&(r=n);const i=NS(t,e);i&&(r=i)}else r=L("MISSING_OR_INVALID",`${e}, namespaces should be an object with data`);return r}function Td(t){return ye(t.protocol,!0)}function BS(t,e){let r=!1;return e&&!t?r=!0:t&&Xn(t)&&t.length&&t.forEach(n=>{r=Td(n)}),r}function FS(t){return typeof t=="number"}function Xe(t){return typeof t<"u"&&typeof t!==null}function LS(t){return!(!t||typeof t!="object"||!t.code||!da(t.code,!1)||!t.message||!ye(t.message,!1))}function US(t){return!(Ce(t)||!ye(t.method,!1))}function kS(t){return!(Ce(t)||Ce(t.result)&&Ce(t.error)||!da(t.id,!1)||!ye(t.jsonrpc,!1))}function jS(t){return!(Ce(t)||!ye(t.name,!1))}function Pd(t,e){return!(!as(e)||!gS(t).includes(e))}function MS(t,e,r){return ye(r,!1)?yS(t,e).includes(r):!1}function qS(t,e,r){return ye(r,!1)?wS(t,e).includes(r):!1}function Nd(t,e,r){let n=null;const i=zS(t),s=VS(e),o=Object.keys(i),a=Object.keys(s),c=Rd(Object.keys(t)),u=Rd(Object.keys(e)),l=c.filter(h=>!u.includes(h));return l.length&&(n=L("NON_CONFORMING_NAMESPACES",`${r} namespaces keys don't satisfy requiredNamespaces.
      Required: ${l.toString()}
      Received: ${Object.keys(e).toString()}`)),_r(o,a)||(n=L("NON_CONFORMING_NAMESPACES",`${r} namespaces chains don't satisfy required namespaces.
      Required: ${o.toString()}
      Approved: ${a.toString()}`)),Object.keys(e).forEach(h=>{if(!h.includes(":")||n)return;const d=yn(e[h].accounts);d.includes(h)||(n=L("NON_CONFORMING_NAMESPACES",`${r} namespaces accounts don't satisfy namespace accounts for ${h}
        Required: ${h}
        Approved: ${d.toString()}`))}),o.forEach(h=>{n||(_r(i[h].methods,s[h].methods)?_r(i[h].events,s[h].events)||(n=L("NON_CONFORMING_NAMESPACES",`${r} namespaces events don't satisfy namespace events for ${h}`)):n=L("NON_CONFORMING_NAMESPACES",`${r} namespaces methods don't satisfy namespace methods for ${h}`))}),n}function zS(t){const e={};return Object.keys(t).forEach(r=>{var n;r.includes(":")?e[r]=t[r]:(n=t[r].chains)==null||n.forEach(i=>{e[i]={methods:t[r].methods,events:t[r].events}})}),e}function Rd(t){return[...new Set(t.map(e=>e.includes(":")?e.split(":")[0]:e))]}function VS(t){const e={};return Object.keys(t).forEach(r=>{if(r.includes(":"))e[r]=t[r];else{const n=yn(t[r].accounts);n?.forEach(i=>{e[i]={accounts:t[r].accounts.filter(s=>s.includes(`${i}:`)),methods:t[r].methods,events:t[r].events}})}}),e}function KS(t,e){return da(t,!1)&&t<=e.max&&t>=e.min}function Cd(){const t=Ln();return new Promise(e=>{switch(t){case et.browser:e(HS());break;case et.reactNative:e(WS());break;case et.node:e(GS());break;default:e(!0)}})}function HS(){return rn()&&navigator?.onLine}async function WS(){if(nr()&&typeof global<"u"&&global!=null&&global.NetInfo){const t=await(global==null?void 0:global.NetInfo.fetch());return t?.isConnected}return!0}function GS(){return!0}function YS(t){switch(Ln()){case et.browser:ZS(t);break;case et.reactNative:XS(t);break}}function ZS(t){!nr()&&rn()&&(window.addEventListener("online",()=>t(!0)),window.addEventListener("offline",()=>t(!1)))}function XS(t){nr()&&typeof global<"u"&&global!=null&&global.NetInfo&&global?.NetInfo.addEventListener(e=>t(e?.isConnected))}function JS(){var t;return rn()&&br()?((t=br())==null?void 0:t.visibilityState)==="visible":!0}const pa={};class Qn{static get(e){return pa[e]}static set(e,r){pa[e]=r}static delete(e){delete pa[e]}}const QS="PARSE_ERROR",eI="INVALID_REQUEST",tI="METHOD_NOT_FOUND",rI="INVALID_PARAMS",Bd="INTERNAL_ERROR",ga="SERVER_ERROR",nI=[-32700,-32600,-32601,-32602,-32603],ei={[QS]:{code:-32700,message:"Parse error"},[eI]:{code:-32600,message:"Invalid Request"},[tI]:{code:-32601,message:"Method not found"},[rI]:{code:-32602,message:"Invalid params"},[Bd]:{code:-32603,message:"Internal error"},[ga]:{code:-32e3,message:"Server error"}},Fd=ga;function iI(t){return nI.includes(t)}function Ld(t){return Object.keys(ei).includes(t)?ei[t]:ei[Fd]}function sI(t){const e=Object.values(ei).find(r=>r.code===t);return e||ei[Fd]}function oI(t,e,r){return t.message.includes("getaddrinfo ENOTFOUND")||t.message.includes("connect ECONNREFUSED")?new Error(`Unavailable ${r} RPC url at ${e}`):t}var Ud={};/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */var ya=function(t,e){return ya=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)n.hasOwnProperty(i)&&(r[i]=n[i])},ya(t,e)};function aI(t,e){ya(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}var wa=function(){return wa=Object.assign||function(e){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},wa.apply(this,arguments)};function cI(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]]);return r}function uI(t,e,r,n){var i=arguments.length,s=i<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,r):n,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s}function lI(t,e){return function(r,n){e(r,n,t)}}function hI(t,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(t,e)}function dI(t,e,r,n){function i(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function a(l){try{u(n.next(l))}catch(h){o(h)}}function c(l){try{u(n.throw(l))}catch(h){o(h)}}function u(l){l.done?s(l.value):i(l.value).then(a,c)}u((n=n.apply(t,e||[])).next())})}function fI(t,e){var r={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},n,i,s,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(u){return function(l){return c([u,l])}}function c(u){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,i&&(s=u[0]&2?i.return:u[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,u[1])).done)return s;switch(i=0,s&&(u=[u[0]&2,s.value]),u[0]){case 0:case 1:s=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,i=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(s=r.trys,!(s=s.length>0&&s[s.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!s||u[1]>s[0]&&u[1]<s[3])){r.label=u[1];break}if(u[0]===6&&r.label<s[1]){r.label=s[1],s=u;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(u);break}s[2]&&r.ops.pop(),r.trys.pop();continue}u=e.call(t,r)}catch(l){u=[6,l],i=0}finally{n=s=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function pI(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}function gI(t,e){for(var r in t)r!=="default"&&!e.hasOwnProperty(r)&&(e[r]=t[r])}function ba(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function kd(t,e){var r=typeof Symbol=="function"&&t[Symbol.iterator];if(!r)return t;var n=r.call(t),i,s=[],o;try{for(;(e===void 0||e-- >0)&&!(i=n.next()).done;)s.push(i.value)}catch(a){o={error:a}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(o)throw o.error}}return s}function yI(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(kd(arguments[e]));return t}function wI(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;for(var n=Array(t),i=0,e=0;e<r;e++)for(var s=arguments[e],o=0,a=s.length;o<a;o++,i++)n[i]=s[o];return n}function ti(t){return this instanceof ti?(this.v=t,this):new ti(t)}function bI(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(t,e||[]),i,s=[];return i={},o("next"),o("throw"),o("return"),i[Symbol.asyncIterator]=function(){return this},i;function o(d){n[d]&&(i[d]=function(p){return new Promise(function(f,y){s.push([d,p,f,y])>1||a(d,p)})})}function a(d,p){try{c(n[d](p))}catch(f){h(s[0][3],f)}}function c(d){d.value instanceof ti?Promise.resolve(d.value.v).then(u,l):h(s[0][2],d)}function u(d){a("next",d)}function l(d){a("throw",d)}function h(d,p){d(p),s.shift(),s.length&&a(s[0][0],s[0][1])}}function mI(t){var e,r;return e={},n("next"),n("throw",function(i){throw i}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(i,s){e[i]=t[i]?function(o){return(r=!r)?{value:ti(t[i](o)),done:i==="return"}:s?s(o):o}:s}}function vI(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof ba=="function"?ba(t):t[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(s){r[s]=t[s]&&function(o){return new Promise(function(a,c){o=t[s](o),i(a,c,o.done,o.value)})}}function i(s,o,a,c){Promise.resolve(c).then(function(u){s({value:u,done:a})},o)}}function EI(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function _I(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function SI(t){return t&&t.__esModule?t:{default:t}}function II(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function xI(t,e,r){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,r),r}var OI=Object.freeze({__proto__:null,__extends:aI,get __assign(){return wa},__rest:cI,__decorate:uI,__param:lI,__metadata:hI,__awaiter:dI,__generator:fI,__createBinding:pI,__exportStar:gI,__values:ba,__read:kd,__spread:yI,__spreadArrays:wI,__await:ti,__asyncGenerator:bI,__asyncDelegator:mI,__asyncValues:vI,__makeTemplateObject:EI,__importStar:_I,__importDefault:SI,__classPrivateFieldGet:II,__classPrivateFieldSet:xI}),DI=Ha(OI),Vt={},jd;function AI(){if(jd)return Vt;jd=1,Object.defineProperty(Vt,"__esModule",{value:!0}),Vt.isBrowserCryptoAvailable=Vt.getSubtleCrypto=Vt.getBrowerCrypto=void 0;function t(){return bt?.crypto||bt?.msCrypto||{}}Vt.getBrowerCrypto=t;function e(){const n=t();return n.subtle||n.webkitSubtle}Vt.getSubtleCrypto=e;function r(){return!!t()&&!!e()}return Vt.isBrowserCryptoAvailable=r,Vt}var Kt={},Md;function $I(){if(Md)return Kt;Md=1,Object.defineProperty(Kt,"__esModule",{value:!0}),Kt.isBrowser=Kt.isNode=Kt.isReactNative=void 0;function t(){return typeof document>"u"&&typeof navigator<"u"&&navigator.product==="ReactNative"}Kt.isReactNative=t;function e(){return typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.node<"u"}Kt.isNode=e;function r(){return!t()&&!e()}return Kt.isBrowser=r,Kt}(function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=DI;e.__exportStar(AI(),t),e.__exportStar($I(),t)})(Ud);function Ot(t=3){const e=Date.now()*Math.pow(10,t),r=Math.floor(Math.random()*Math.pow(10,t));return e+r}function Tr(t=6){return BigInt(Ot(t))}function Pr(t,e,r){return{id:r||Ot(),jsonrpc:"2.0",method:t,params:e}}function ma(t,e){return{id:t,jsonrpc:"2.0",result:e}}function va(t,e,r){return{id:t,jsonrpc:"2.0",error:TI(e,r)}}function TI(t,e){return typeof t>"u"?Ld(Bd):(typeof t=="string"&&(t=Object.assign(Object.assign({},Ld(ga)),{message:t})),typeof e<"u"&&(t.data=e),iI(t.code)&&(t=sI(t.code)),t)}class PI{}class NI extends PI{constructor(){super()}}class RI extends NI{constructor(e){super()}}const CI="^wss?:";function BI(t){const e=t.match(new RegExp(/^\w+:/,"gi"));if(!(!e||!e.length))return e[0]}function FI(t,e){const r=BI(t);return typeof r>"u"?!1:new RegExp(e).test(r)}function qd(t){return FI(t,CI)}function LI(t){return new RegExp("wss?://localhost(:d{2,5})?").test(t)}function zd(t){return typeof t=="object"&&"id"in t&&"jsonrpc"in t&&t.jsonrpc==="2.0"}function Ea(t){return zd(t)&&"method"in t}function cs(t){return zd(t)&&(Dt(t)||at(t))}function Dt(t){return"result"in t}function at(t){return"error"in t}class UI extends RI{constructor(e){super(e),this.events=new Ke.exports.EventEmitter,this.hasRegisteredEventListeners=!1,this.connection=this.setConnection(e),this.connection.connected&&this.registerEventListeners()}async connect(e=this.connection){await this.open(e)}async disconnect(){await this.close()}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async request(e,r){return this.requestStrict(Pr(e.method,e.params||[],e.id||Tr().toString()),r)}async requestStrict(e,r){return new Promise(async(n,i)=>{if(!this.connection.connected)try{await this.open()}catch(s){i(s)}this.events.on(`${e.id}`,s=>{at(s)?i(s.error):n(s.result)});try{await this.connection.send(e,r)}catch(s){i(s)}})}setConnection(e=this.connection){return e}onPayload(e){this.events.emit("payload",e),cs(e)?this.events.emit(`${e.id}`,e):this.events.emit("message",{type:e.method,data:e.params})}onClose(e){e&&e.code===3e3&&this.events.emit("error",new Error(`WebSocket connection closed abnormally with code: ${e.code} ${e.reason?`(${e.reason})`:""}`)),this.events.emit("disconnect")}async open(e=this.connection){this.connection===e&&this.connection.connected||(this.connection.connected&&this.close(),typeof e=="string"&&(await this.connection.open(e),e=this.connection),this.connection=this.setConnection(e),await this.connection.open(),this.registerEventListeners(),this.events.emit("connect"))}async close(){await this.connection.close()}registerEventListeners(){this.hasRegisteredEventListeners||(this.connection.on("payload",e=>this.onPayload(e)),this.connection.on("close",e=>this.onClose(e)),this.connection.on("error",e=>this.events.emit("error",e)),this.connection.on("register_error",e=>this.onClose()),this.hasRegisteredEventListeners=!0)}}const kI=()=>typeof WebSocket<"u"?WebSocket:typeof global<"u"&&typeof global.WebSocket<"u"?global.WebSocket:typeof window<"u"&&typeof window.WebSocket<"u"?window.WebSocket:typeof self<"u"&&typeof self.WebSocket<"u"?self.WebSocket:require("ws"),jI=()=>typeof WebSocket<"u"||typeof global<"u"&&typeof global.WebSocket<"u"||typeof window<"u"&&typeof window.WebSocket<"u"||typeof self<"u"&&typeof self.WebSocket<"u",Vd=t=>t.split("?")[0],Kd=10,MI=kI();class qI{constructor(e){if(this.url=e,this.events=new Ke.exports.EventEmitter,this.registering=!1,!qd(e))throw new Error(`Provided URL is not compatible with WebSocket connection: ${e}`);this.url=e}get connected(){return typeof this.socket<"u"}get connecting(){return this.registering}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async open(e=this.url){await this.register(e)}async close(){return new Promise((e,r)=>{if(typeof this.socket>"u"){r(new Error("Connection already closed"));return}this.socket.onclose=n=>{this.onClose(n),e()},this.socket.close()})}async send(e){typeof this.socket>"u"&&(this.socket=await this.register());try{this.socket.send(gr(e))}catch(r){this.onError(e.id,r)}}register(e=this.url){if(!qd(e))throw new Error(`Provided URL is not compatible with WebSocket connection: ${e}`);if(this.registering){const r=this.events.getMaxListeners();return(this.events.listenerCount("register_error")>=r||this.events.listenerCount("open")>=r)&&this.events.setMaxListeners(r+1),new Promise((n,i)=>{this.events.once("register_error",s=>{this.resetMaxListeners(),i(s)}),this.events.once("open",()=>{if(this.resetMaxListeners(),typeof this.socket>"u")return i(new Error("WebSocket connection is missing or invalid"));n(this.socket)})})}return this.url=e,this.registering=!0,new Promise((r,n)=>{const i=Ud.isReactNative()?void 0:{rejectUnauthorized:!LI(e)},s=new MI(e,[],i);jI()?s.onerror=o=>{const a=o;n(this.emitError(a.error))}:s.on("error",o=>{n(this.emitError(o))}),s.onopen=()=>{this.onOpen(s),r(s)}})}onOpen(e){e.onmessage=r=>this.onPayload(r),e.onclose=r=>this.onClose(r),this.socket=e,this.registering=!1,this.events.emit("open")}onClose(e){this.socket=void 0,this.registering=!1,this.events.emit("close",e)}onPayload(e){if(typeof e.data>"u")return;const r=typeof e.data=="string"?kr(e.data):e.data;this.events.emit("payload",r)}onError(e,r){const n=this.parseError(r),i=n.message||n.toString(),s=va(e,i);this.events.emit("payload",s)}parseError(e,r=this.url){return oI(e,Vd(r),"WS")}resetMaxListeners(){this.events.getMaxListeners()>Kd&&this.events.setMaxListeners(Kd)}emitError(e){const r=this.parseError(new Error(e?.message||`WebSocket connection failed for host: ${Vd(this.url)}`));return this.events.emit("register_error",r),r}}var zI=Object.defineProperty,Hd=Object.getOwnPropertySymbols,VI=Object.prototype.hasOwnProperty,KI=Object.prototype.propertyIsEnumerable,Wd=(t,e,r)=>e in t?zI(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ze=(t,e)=>{for(var r in e||(e={}))VI.call(e,r)&&Wd(t,r,e[r]);if(Hd)for(var r of Hd(e))KI.call(e,r)&&Wd(t,r,e[r]);return t};const Gd="wc",Yd=2,us="core",At=`${Gd}@2:${us}:`,HI={name:us,logger:"error"},WI={database:":memory:"},GI="crypto",Zd="client_ed25519_seed",YI=F.ONE_DAY,ZI="keychain",XI="0.3",JI="messages",QI="0.3",Xd=F.SIX_HOURS,ex="publisher",Jd="irn",tx="error",Qd="wss://relay.walletconnect.org",rx="relayer",Ae={message:"relayer_message",message_ack:"relayer_message_ack",connect:"relayer_connect",disconnect:"relayer_disconnect",error:"relayer_error",connection_stalled:"relayer_connection_stalled",transport_closed:"relayer_transport_closed",publish:"relayer_publish"},nx="_subscription",ct={payload:"payload",connect:"connect",disconnect:"disconnect",error:"error"},ix=.1,_a="2.21.1",ue={link_mode:"link_mode",relay:"relay"},ls={inbound:"inbound",outbound:"outbound"},sx="0.3",ox="WALLETCONNECT_CLIENT_ID",ef="WALLETCONNECT_LINK_MODE_APPS",nt={created:"subscription_created",deleted:"subscription_deleted",expired:"subscription_expired",disabled:"subscription_disabled",sync:"subscription_sync",resubscribed:"subscription_resubscribed"},ax="subscription",cx="0.3",ux="pairing",lx="0.3",ri={wc_pairingDelete:{req:{ttl:F.ONE_DAY,prompt:!1,tag:1e3},res:{ttl:F.ONE_DAY,prompt:!1,tag:1001}},wc_pairingPing:{req:{ttl:F.THIRTY_SECONDS,prompt:!1,tag:1002},res:{ttl:F.THIRTY_SECONDS,prompt:!1,tag:1003}},unregistered_method:{req:{ttl:F.ONE_DAY,prompt:!1,tag:0},res:{ttl:F.ONE_DAY,prompt:!1,tag:0}}},Nr={create:"pairing_create",expire:"pairing_expire",delete:"pairing_delete",ping:"pairing_ping"},pt={created:"history_created",updated:"history_updated",deleted:"history_deleted",sync:"history_sync"},hx="history",dx="0.3",fx="expirer",ut={created:"expirer_created",deleted:"expirer_deleted",expired:"expirer_expired",sync:"expirer_sync"},px="0.3",gx="verify-api",yx="https://verify.walletconnect.com",tf="https://verify.walletconnect.org",ni=tf,wx=`${ni}/v3`,bx=[yx,tf],mx="echo",vx="https://echo.walletconnect.com",$t={pairing_started:"pairing_started",pairing_uri_validation_success:"pairing_uri_validation_success",pairing_uri_not_expired:"pairing_uri_not_expired",store_new_pairing:"store_new_pairing",subscribing_pairing_topic:"subscribing_pairing_topic",subscribe_pairing_topic_success:"subscribe_pairing_topic_success",existing_pairing:"existing_pairing",pairing_not_expired:"pairing_not_expired",emit_inactive_pairing:"emit_inactive_pairing",emit_session_proposal:"emit_session_proposal",subscribing_to_pairing_topic:"subscribing_to_pairing_topic"},Ht={no_wss_connection:"no_wss_connection",no_internet_connection:"no_internet_connection",malformed_pairing_uri:"malformed_pairing_uri",active_pairing_already_exists:"active_pairing_already_exists",subscribe_pairing_topic_failure:"subscribe_pairing_topic_failure",pairing_expired:"pairing_expired",proposal_expired:"proposal_expired",proposal_listener_not_found:"proposal_listener_not_found"},gt={session_approve_started:"session_approve_started",proposal_not_expired:"proposal_not_expired",session_namespaces_validation_success:"session_namespaces_validation_success",create_session_topic:"create_session_topic",subscribing_session_topic:"subscribing_session_topic",subscribe_session_topic_success:"subscribe_session_topic_success",publishing_session_approve:"publishing_session_approve",session_approve_publish_success:"session_approve_publish_success",store_session:"store_session",publishing_session_settle:"publishing_session_settle",session_settle_publish_success:"session_settle_publish_success"},Rr={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",proposal_expired:"proposal_expired",subscribe_session_topic_failure:"subscribe_session_topic_failure",session_approve_publish_failure:"session_approve_publish_failure",session_settle_publish_failure:"session_settle_publish_failure",session_approve_namespace_validation_failure:"session_approve_namespace_validation_failure",proposal_not_found:"proposal_not_found"},Cr={authenticated_session_approve_started:"authenticated_session_approve_started",authenticated_session_not_expired:"authenticated_session_not_expired",chains_caip2_compliant:"chains_caip2_compliant",chains_evm_compliant:"chains_evm_compliant",create_authenticated_session_topic:"create_authenticated_session_topic",cacaos_verified:"cacaos_verified",store_authenticated_session:"store_authenticated_session",subscribing_authenticated_session_topic:"subscribing_authenticated_session_topic",subscribe_authenticated_session_topic_success:"subscribe_authenticated_session_topic_success",publishing_authenticated_session_approve:"publishing_authenticated_session_approve",authenticated_session_approve_publish_success:"authenticated_session_approve_publish_success"},ii={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",missing_session_authenticate_request:"missing_session_authenticate_request",session_authenticate_request_expired:"session_authenticate_request_expired",chains_caip2_compliant_failure:"chains_caip2_compliant_failure",chains_evm_compliant_failure:"chains_evm_compliant_failure",invalid_cacao:"invalid_cacao",subscribe_authenticated_session_topic_failure:"subscribe_authenticated_session_topic_failure",authenticated_session_approve_publish_failure:"authenticated_session_approve_publish_failure",authenticated_session_pending_request_not_found:"authenticated_session_pending_request_not_found"},Ex=.1,_x="event-client",Sx=86400,Ix="https://pulse.walletconnect.org/batch";function xx(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var s=t.charAt(i),o=s.charCodeAt(0);if(r[o]!==255)throw new TypeError(s+" is ambiguous");r[o]=i}var a=t.length,c=t.charAt(0),u=Math.log(a)/Math.log(256),l=Math.log(256)/Math.log(a);function h(f){if(f instanceof Uint8Array||(ArrayBuffer.isView(f)?f=new Uint8Array(f.buffer,f.byteOffset,f.byteLength):Array.isArray(f)&&(f=Uint8Array.from(f))),!(f instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(f.length===0)return"";for(var y=0,g=0,b=0,w=f.length;b!==w&&f[b]===0;)b++,y++;for(var m=(w-b)*l+1>>>0,E=new Uint8Array(m);b!==w;){for(var $=f[b],O=0,S=m-1;($!==0||O<g)&&S!==-1;S--,O++)$+=256*E[S]>>>0,E[S]=$%a>>>0,$=$/a>>>0;if($!==0)throw new Error("Non-zero carry");g=O,b++}for(var T=m-g;T!==m&&E[T]===0;)T++;for(var I=c.repeat(y);T<m;++T)I+=t.charAt(E[T]);return I}function d(f){if(typeof f!="string")throw new TypeError("Expected String");if(f.length===0)return new Uint8Array;var y=0;if(f[y]!==" "){for(var g=0,b=0;f[y]===c;)g++,y++;for(var w=(f.length-y)*u+1>>>0,m=new Uint8Array(w);f[y];){var E=r[f.charCodeAt(y)];if(E===255)return;for(var $=0,O=w-1;(E!==0||$<b)&&O!==-1;O--,$++)E+=a*m[O]>>>0,m[O]=E%256>>>0,E=E/256>>>0;if(E!==0)throw new Error("Non-zero carry");b=$,y++}if(f[y]!==" "){for(var S=w-b;S!==w&&m[S]===0;)S++;for(var T=new Uint8Array(g+(w-S)),I=g;S!==w;)T[I++]=m[S++];return T}}}function p(f){var y=d(f);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:h,decodeUnsafe:d,decode:p}}var Ox=xx,Dx=Ox;const rf=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},Ax=t=>new TextEncoder().encode(t),$x=t=>new TextDecoder().decode(t);class Tx{constructor(e,r,n){this.name=e,this.prefix=r,this.baseEncode=n}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class Px{constructor(e,r,n){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=n}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return nf(this,e)}}class Nx{constructor(e){this.decoders=e}or(e){return nf(this,e)}decode(e){const r=e[0],n=this.decoders[r];if(n)return n.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const nf=(t,e)=>new Nx(ze(ze({},t.decoders||{[t.prefix]:t}),e.decoders||{[e.prefix]:e}));class Rx{constructor(e,r,n,i){this.name=e,this.prefix=r,this.baseEncode=n,this.baseDecode=i,this.encoder=new Tx(e,r,n),this.decoder=new Px(e,r,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const hs=({name:t,prefix:e,encode:r,decode:n})=>new Rx(t,e,r,n),si=({prefix:t,name:e,alphabet:r})=>{const{encode:n,decode:i}=Dx(r,e);return hs({prefix:t,name:e,encode:n,decode:s=>rf(i(s))})},Cx=(t,e,r,n)=>{const i={};for(let l=0;l<e.length;++l)i[e[l]]=l;let s=t.length;for(;t[s-1]==="=";)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,u=0;for(let l=0;l<s;++l){const h=i[t[l]];if(h===void 0)throw new SyntaxError(`Non-${n} character`);c=c<<r|h,a+=r,a>=8&&(a-=8,o[u++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},Bx=(t,e,r)=>{const n=e[e.length-1]==="=",i=(1<<r)-1;let s="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,s+=e[i&a>>o];if(o&&(s+=e[i&a<<r-o]),n)for(;s.length*r&7;)s+="=";return s},Be=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>hs({prefix:e,name:t,encode(i){return Bx(i,n,r)},decode(i){return Cx(i,n,r,t)}}),Fx=hs({prefix:"\0",name:"identity",encode:t=>$x(t),decode:t=>Ax(t)});var Lx=Object.freeze({__proto__:null,identity:Fx});const Ux=Be({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var kx=Object.freeze({__proto__:null,base2:Ux});const jx=Be({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var Mx=Object.freeze({__proto__:null,base8:jx});const qx=si({prefix:"9",name:"base10",alphabet:"0123456789"});var zx=Object.freeze({__proto__:null,base10:qx});const Vx=Be({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Kx=Be({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var Hx=Object.freeze({__proto__:null,base16:Vx,base16upper:Kx});const Wx=Be({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),Gx=Be({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),Yx=Be({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),Zx=Be({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),Xx=Be({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),Jx=Be({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),Qx=Be({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),e3=Be({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),t3=Be({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var r3=Object.freeze({__proto__:null,base32:Wx,base32upper:Gx,base32pad:Yx,base32padupper:Zx,base32hex:Xx,base32hexupper:Jx,base32hexpad:Qx,base32hexpadupper:e3,base32z:t3});const n3=si({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),i3=si({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var s3=Object.freeze({__proto__:null,base36:n3,base36upper:i3});const o3=si({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),a3=si({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var c3=Object.freeze({__proto__:null,base58btc:o3,base58flickr:a3});const u3=Be({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),l3=Be({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),h3=Be({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),d3=Be({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var f3=Object.freeze({__proto__:null,base64:u3,base64pad:l3,base64url:h3,base64urlpad:d3});const sf=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),p3=sf.reduce((t,e,r)=>(t[r]=e,t),[]),g3=sf.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function y3(t){return t.reduce((e,r)=>(e+=p3[r],e),"")}function w3(t){const e=[];for(const r of t){const n=g3[r.codePointAt(0)];if(n===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(n)}return new Uint8Array(e)}const b3=hs({prefix:"\u{1F680}",name:"base256emoji",encode:y3,decode:w3});var m3=Object.freeze({__proto__:null,base256emoji:b3}),v3=af,of=128,E3=127,_3=~E3,S3=Math.pow(2,31);function af(t,e,r){e=e||[],r=r||0;for(var n=r;t>=S3;)e[r++]=t&255|of,t/=128;for(;t&_3;)e[r++]=t&255|of,t>>>=7;return e[r]=t|0,af.bytes=r-n+1,e}var I3=Sa,x3=128,cf=127;function Sa(t,n){var r=0,n=n||0,i=0,s=n,o,a=t.length;do{if(s>=a)throw Sa.bytes=0,new RangeError("Could not decode varint");o=t[s++],r+=i<28?(o&cf)<<i:(o&cf)*Math.pow(2,i),i+=7}while(o>=x3);return Sa.bytes=s-n,r}var O3=Math.pow(2,7),D3=Math.pow(2,14),A3=Math.pow(2,21),$3=Math.pow(2,28),T3=Math.pow(2,35),P3=Math.pow(2,42),N3=Math.pow(2,49),R3=Math.pow(2,56),C3=Math.pow(2,63),B3=function(t){return t<O3?1:t<D3?2:t<A3?3:t<$3?4:t<T3?5:t<P3?6:t<N3?7:t<R3?8:t<C3?9:10},F3={encode:v3,decode:I3,encodingLength:B3},uf=F3;const lf=(t,e,r=0)=>(uf.encode(t,e,r),e),hf=t=>uf.encodingLength(t),Ia=(t,e)=>{const r=e.byteLength,n=hf(t),i=n+hf(r),s=new Uint8Array(i+r);return lf(t,s,0),lf(r,s,n),s.set(e,i),new L3(t,r,e,s)};class L3{constructor(e,r,n,i){this.code=e,this.size=r,this.digest=n,this.bytes=i}}const df=({name:t,code:e,encode:r})=>new U3(t,e,r);class U3{constructor(e,r,n){this.name=e,this.code=r,this.encode=n}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?Ia(this.code,r):r.then(n=>Ia(this.code,n))}else throw Error("Unknown type, must be binary type")}}const ff=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),k3=df({name:"sha2-256",code:18,encode:ff("SHA-256")}),j3=df({name:"sha2-512",code:19,encode:ff("SHA-512")});var M3=Object.freeze({__proto__:null,sha256:k3,sha512:j3});const pf=0,q3="identity",gf=rf;var z3=Object.freeze({__proto__:null,identity:{code:pf,name:q3,encode:gf,digest:t=>Ia(pf,gf(t))}});new TextEncoder,new TextDecoder;const yf=ze(ze(ze(ze(ze(ze(ze(ze(ze(ze({},Lx),kx),Mx),zx),Hx),r3),s3),c3),f3),m3);ze(ze({},M3),z3);function V3(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}function wf(t,e,r,n){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:n}}}const bf=wf("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),xa=wf("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=V3(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),K3=ze({utf8:bf,"utf-8":bf,hex:yf.base16,latin1:xa,ascii:xa,binary:xa},yf);function H3(t,e="utf8"){const r=K3[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t,"utf8"):r.decoder.decode(`${r.prefix}${t}`)}var W3=Object.defineProperty,G3=(t,e,r)=>e in t?W3(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Tt=(t,e,r)=>G3(t,typeof e!="symbol"?e+"":e,r);class Y3{constructor(e,r){this.core=e,this.logger=r,Tt(this,"keychain",new Map),Tt(this,"name",ZI),Tt(this,"version",XI),Tt(this,"initialized",!1),Tt(this,"storagePrefix",At),Tt(this,"init",async()=>{if(!this.initialized){const n=await this.getKeyChain();typeof n<"u"&&(this.keychain=n),this.initialized=!0}}),Tt(this,"has",n=>(this.isInitialized(),this.keychain.has(n))),Tt(this,"set",async(n,i)=>{this.isInitialized(),this.keychain.set(n,i),await this.persist()}),Tt(this,"get",n=>{this.isInitialized();const i=this.keychain.get(n);if(typeof i>"u"){const{message:s}=L("NO_MATCHING_KEY",`${this.name}: ${n}`);throw new Error(s)}return i}),Tt(this,"del",async n=>{this.isInitialized(),this.keychain.delete(n),await this.persist()}),this.core=e,this.logger=je(r,this.name)}get context(){return We(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setKeyChain(e){await this.core.storage.setItem(this.storageKey,Fo(e))}async getKeyChain(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?Lo(e):void 0}async persist(){await this.setKeyChain(this.keychain)}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Z3=Object.defineProperty,X3=(t,e,r)=>e in t?Z3(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Fe=(t,e,r)=>X3(t,typeof e!="symbol"?e+"":e,r);class J3{constructor(e,r,n){this.core=e,this.logger=r,Fe(this,"name",GI),Fe(this,"keychain"),Fe(this,"randomSessionIdentifier",ha()),Fe(this,"initialized",!1),Fe(this,"init",async()=>{this.initialized||(await this.keychain.init(),this.initialized=!0)}),Fe(this,"hasKeys",i=>(this.isInitialized(),this.keychain.has(i))),Fe(this,"getClientId",async()=>{this.isInitialized();const i=await this.getClientSeed(),s=_u(i);return Eu(s.publicKey)}),Fe(this,"generateKeyPair",()=>{this.isInitialized();const i=Y2();return this.setPrivateKey(i.publicKey,i.privateKey)}),Fe(this,"signJWT",async i=>{this.isInitialized();const s=await this.getClientSeed(),o=_u(s),a=this.randomSessionIdentifier;return await Jb(a,i,YI,o)}),Fe(this,"generateSharedKey",(i,s,o)=>{this.isInitialized();const a=this.getPrivateKey(i),c=Z2(a,s);return this.setSymKey(c,o)}),Fe(this,"setSymKey",async(i,s)=>{this.isInitialized();const o=s||is(i);return await this.keychain.set(o,i),o}),Fe(this,"deleteKeyPair",async i=>{this.isInitialized(),await this.keychain.del(i)}),Fe(this,"deleteSymKey",async i=>{this.isInitialized(),await this.keychain.del(i)}),Fe(this,"encode",async(i,s,o)=>{this.isInitialized();const a=bd(o),c=gr(s);if(vd(a))return Q2(c,o?.encoding);if(md(a)){const d=a.senderPublicKey,p=a.receiverPublicKey;i=await this.generateSharedKey(d,p)}const u=this.getSymKey(i),{type:l,senderPublicKey:h}=a;return X2({type:l,symKey:u,message:c,senderPublicKey:h,encoding:o?.encoding})}),Fe(this,"decode",async(i,s,o)=>{this.isInitialized();const a=tS(s,o);if(vd(a)){const c=eS(s,o?.encoding);return kr(c)}if(md(a)){const c=a.receiverPublicKey,u=a.senderPublicKey;i=await this.generateSharedKey(c,u)}try{const c=this.getSymKey(i),u=J2({symKey:c,encoded:s,encoding:o?.encoding});return kr(u)}catch(c){this.logger.error(`Failed to decode message from topic: '${i}', clientId: '${await this.getClientId()}'`),this.logger.error(c)}}),Fe(this,"getPayloadType",(i,s=ft)=>{const o=Yn({encoded:i,encoding:s});return $r(o.type)}),Fe(this,"getPayloadSenderPublicKey",(i,s=ft)=>{const o=Yn({encoded:i,encoding:s});return o.senderPublicKey?Ye(o.senderPublicKey,qe):void 0}),this.core=e,this.logger=je(r,this.name),this.keychain=n||new Y3(this.core,this.logger)}get context(){return We(this.logger)}async setPrivateKey(e,r){return await this.keychain.set(e,r),e}getPrivateKey(e){return this.keychain.get(e)}async getClientSeed(){let e="";try{e=this.keychain.get(Zd)}catch{e=ha(),await this.keychain.set(Zd,e)}return H3(e,"base16")}getSymKey(e){return this.keychain.get(e)}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}}var Q3=Object.defineProperty,eO=Object.defineProperties,tO=Object.getOwnPropertyDescriptors,mf=Object.getOwnPropertySymbols,rO=Object.prototype.hasOwnProperty,nO=Object.prototype.propertyIsEnumerable,Oa=(t,e,r)=>e in t?Q3(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,iO=(t,e)=>{for(var r in e||(e={}))rO.call(e,r)&&Oa(t,r,e[r]);if(mf)for(var r of mf(e))nO.call(e,r)&&Oa(t,r,e[r]);return t},sO=(t,e)=>eO(t,tO(e)),it=(t,e,r)=>Oa(t,typeof e!="symbol"?e+"":e,r);class oO extends Xg{constructor(e,r){super(e,r),this.logger=e,this.core=r,it(this,"messages",new Map),it(this,"messagesWithoutClientAck",new Map),it(this,"name",JI),it(this,"version",QI),it(this,"initialized",!1),it(this,"storagePrefix",At),it(this,"init",async()=>{if(!this.initialized){this.logger.trace("Initialized");try{const n=await this.getRelayerMessages();typeof n<"u"&&(this.messages=n);const i=await this.getRelayerMessagesWithoutClientAck();typeof i<"u"&&(this.messagesWithoutClientAck=i),this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",size:this.messages.size})}catch(n){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(n)}finally{this.initialized=!0}}}),it(this,"set",async(n,i,s)=>{this.isInitialized();const o=xt(i);let a=this.messages.get(n);if(typeof a>"u"&&(a={}),typeof a[o]<"u")return o;if(a[o]=i,this.messages.set(n,a),s===ls.inbound){const c=this.messagesWithoutClientAck.get(n)||{};this.messagesWithoutClientAck.set(n,sO(iO({},c),{[o]:i}))}return await this.persist(),o}),it(this,"get",n=>{this.isInitialized();let i=this.messages.get(n);return typeof i>"u"&&(i={}),i}),it(this,"getWithoutAck",n=>{this.isInitialized();const i={};for(const s of n){const o=this.messagesWithoutClientAck.get(s)||{};i[s]=Object.values(o)}return i}),it(this,"has",(n,i)=>{this.isInitialized();const s=this.get(n),o=xt(i);return typeof s[o]<"u"}),it(this,"ack",async(n,i)=>{this.isInitialized();const s=this.messagesWithoutClientAck.get(n);if(typeof s>"u")return;const o=xt(i);delete s[o],Object.keys(s).length===0?this.messagesWithoutClientAck.delete(n):this.messagesWithoutClientAck.set(n,s),await this.persist()}),it(this,"del",async n=>{this.isInitialized(),this.messages.delete(n),this.messagesWithoutClientAck.delete(n),await this.persist()}),this.logger=je(e,this.name),this.core=r}get context(){return We(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get storageKeyWithoutClientAck(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name+"_withoutClientAck"}async setRelayerMessages(e){await this.core.storage.setItem(this.storageKey,Fo(e))}async setRelayerMessagesWithoutClientAck(e){await this.core.storage.setItem(this.storageKeyWithoutClientAck,Fo(e))}async getRelayerMessages(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?Lo(e):void 0}async getRelayerMessagesWithoutClientAck(){const e=await this.core.storage.getItem(this.storageKeyWithoutClientAck);return typeof e<"u"?Lo(e):void 0}async persist(){await this.setRelayerMessages(this.messages),await this.setRelayerMessagesWithoutClientAck(this.messagesWithoutClientAck)}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}}var aO=Object.defineProperty,cO=Object.defineProperties,uO=Object.getOwnPropertyDescriptors,vf=Object.getOwnPropertySymbols,lO=Object.prototype.hasOwnProperty,hO=Object.prototype.propertyIsEnumerable,Da=(t,e,r)=>e in t?aO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ds=(t,e)=>{for(var r in e||(e={}))lO.call(e,r)&&Da(t,r,e[r]);if(vf)for(var r of vf(e))hO.call(e,r)&&Da(t,r,e[r]);return t},Aa=(t,e)=>cO(t,uO(e)),yt=(t,e,r)=>Da(t,typeof e!="symbol"?e+"":e,r);class dO extends Jg{constructor(e,r){super(e,r),this.relayer=e,this.logger=r,yt(this,"events",new Ke.exports.EventEmitter),yt(this,"name",ex),yt(this,"queue",new Map),yt(this,"publishTimeout",F.toMiliseconds(F.ONE_MINUTE)),yt(this,"initialPublishTimeout",F.toMiliseconds(F.ONE_SECOND*15)),yt(this,"needsTransportRestart",!1),yt(this,"publish",async(n,i,s)=>{var o;this.logger.debug("Publishing Payload"),this.logger.trace({type:"method",method:"publish",params:{topic:n,message:i,opts:s}});const a=s?.ttl||Xd,c=ss(s),u=s?.prompt||!1,l=s?.tag||0,h=s?.id||Tr().toString(),d={topic:n,message:i,opts:{ttl:a,relay:c,prompt:u,tag:l,id:h,attestation:s?.attestation,tvf:s?.tvf}},p=`Failed to publish payload, please try again. id:${h} tag:${l}`;try{const f=new Promise(async y=>{const g=({id:w})=>{d.opts.id===w&&(this.removeRequestFromQueue(w),this.relayer.events.removeListener(Ae.publish,g),y(d))};this.relayer.events.on(Ae.publish,g);const b=ir(new Promise((w,m)=>{this.rpcPublish({topic:n,message:i,ttl:a,prompt:u,tag:l,id:h,attestation:s?.attestation,tvf:s?.tvf}).then(w).catch(E=>{this.logger.warn(E,E?.message),m(E)})}),this.initialPublishTimeout,`Failed initial publish, retrying.... id:${h} tag:${l}`);try{await b,this.events.removeListener(Ae.publish,g)}catch(w){this.queue.set(h,Aa(ds({},d),{attempt:1})),this.logger.warn(w,w?.message)}});this.logger.trace({type:"method",method:"publish",params:{id:h,topic:n,message:i,opts:s}}),await ir(f,this.publishTimeout,p)}catch(f){if(this.logger.debug("Failed to Publish Payload"),this.logger.error(f),(o=s?.internal)!=null&&o.throwOnFailedPublish)throw f}finally{this.queue.delete(h)}}),yt(this,"on",(n,i)=>{this.events.on(n,i)}),yt(this,"once",(n,i)=>{this.events.once(n,i)}),yt(this,"off",(n,i)=>{this.events.off(n,i)}),yt(this,"removeListener",(n,i)=>{this.events.removeListener(n,i)}),this.relayer=e,this.logger=je(r,this.name),this.registerEventListeners()}get context(){return We(this.logger)}async rpcPublish(e){var r,n,i,s;const{topic:o,message:a,ttl:c=Xd,prompt:u,tag:l,id:h,attestation:d,tvf:p}=e,f={method:Zn(ss().protocol).publish,params:ds({topic:o,message:a,ttl:c,prompt:u,tag:l,attestation:d},p),id:h};Ce((r=f.params)==null?void 0:r.prompt)&&((n=f.params)==null||delete n.prompt),Ce((i=f.params)==null?void 0:i.tag)&&((s=f.params)==null||delete s.tag),this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"message",direction:"outgoing",request:f});const y=await this.relayer.request(f);return this.relayer.events.emit(Ae.publish,e),this.logger.debug("Successfully Published Payload"),y}removeRequestFromQueue(e){this.queue.delete(e)}checkQueue(){this.queue.forEach(async(e,r)=>{const n=e.attempt+1;this.queue.set(r,Aa(ds({},e),{attempt:n}));const{topic:i,message:s,opts:o,attestation:a}=e;this.logger.warn({},`Publisher: queue->publishing: ${e.opts.id}, tag: ${e.opts.tag}, attempt: ${n}`),await this.rpcPublish(Aa(ds({},e),{topic:i,message:s,ttl:o.ttl,prompt:o.prompt,tag:o.tag,id:o.id,attestation:a,tvf:o.tvf})),this.logger.warn({},`Publisher: queue->published: ${e.opts.id}`)})}registerEventListeners(){this.relayer.core.heartbeat.on(fr.pulse,()=>{if(this.needsTransportRestart){this.needsTransportRestart=!1,this.relayer.events.emit(Ae.connection_stalled);return}this.checkQueue()}),this.relayer.on(Ae.message_ack,e=>{this.removeRequestFromQueue(e.id.toString())})}}var fO=Object.defineProperty,pO=(t,e,r)=>e in t?fO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,wn=(t,e,r)=>pO(t,typeof e!="symbol"?e+"":e,r);class gO{constructor(){wn(this,"map",new Map),wn(this,"set",(e,r)=>{const n=this.get(e);this.exists(e,r)||this.map.set(e,[...n,r])}),wn(this,"get",e=>this.map.get(e)||[]),wn(this,"exists",(e,r)=>this.get(e).includes(r)),wn(this,"delete",(e,r)=>{if(typeof r>"u"){this.map.delete(e);return}if(!this.map.has(e))return;const n=this.get(e);if(!this.exists(e,r))return;const i=n.filter(s=>s!==r);if(!i.length){this.map.delete(e);return}this.map.set(e,i)}),wn(this,"clear",()=>{this.map.clear()})}get topics(){return Array.from(this.map.keys())}}var yO=Object.defineProperty,wO=Object.defineProperties,bO=Object.getOwnPropertyDescriptors,Ef=Object.getOwnPropertySymbols,mO=Object.prototype.hasOwnProperty,vO=Object.prototype.propertyIsEnumerable,$a=(t,e,r)=>e in t?yO(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,oi=(t,e)=>{for(var r in e||(e={}))mO.call(e,r)&&$a(t,r,e[r]);if(Ef)for(var r of Ef(e))vO.call(e,r)&&$a(t,r,e[r]);return t},Ta=(t,e)=>wO(t,bO(e)),ie=(t,e,r)=>$a(t,typeof e!="symbol"?e+"":e,r);class EO extends ty{constructor(e,r){super(e,r),this.relayer=e,this.logger=r,ie(this,"subscriptions",new Map),ie(this,"topicMap",new gO),ie(this,"events",new Ke.exports.EventEmitter),ie(this,"name",ax),ie(this,"version",cx),ie(this,"pending",new Map),ie(this,"cached",[]),ie(this,"initialized",!1),ie(this,"storagePrefix",At),ie(this,"subscribeTimeout",F.toMiliseconds(F.ONE_MINUTE)),ie(this,"initialSubscribeTimeout",F.toMiliseconds(F.ONE_SECOND*15)),ie(this,"clientId"),ie(this,"batchSubscribeTopicsLimit",500),ie(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),this.registerEventListeners(),await this.restore()),this.initialized=!0}),ie(this,"subscribe",async(n,i)=>{this.isInitialized(),this.logger.debug("Subscribing Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:n,opts:i}});try{const s=ss(i),o={topic:n,relay:s,transportType:i?.transportType};this.pending.set(n,o);const a=await this.rpcSubscribe(n,s,i);return typeof a=="string"&&(this.onSubscribe(a,o),this.logger.debug("Successfully Subscribed Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:n,opts:i}})),a}catch(s){throw this.logger.debug("Failed to Subscribe Topic"),this.logger.error(s),s}}),ie(this,"unsubscribe",async(n,i)=>{this.isInitialized(),typeof i?.id<"u"?await this.unsubscribeById(n,i.id,i):await this.unsubscribeByTopic(n,i)}),ie(this,"isSubscribed",n=>new Promise(i=>{i(this.topicMap.topics.includes(n))})),ie(this,"isKnownTopic",n=>new Promise(i=>{i(this.topicMap.topics.includes(n)||this.pending.has(n)||this.cached.some(s=>s.topic===n))})),ie(this,"on",(n,i)=>{this.events.on(n,i)}),ie(this,"once",(n,i)=>{this.events.once(n,i)}),ie(this,"off",(n,i)=>{this.events.off(n,i)}),ie(this,"removeListener",(n,i)=>{this.events.removeListener(n,i)}),ie(this,"start",async()=>{await this.onConnect()}),ie(this,"stop",async()=>{await this.onDisconnect()}),ie(this,"restart",async()=>{await this.restore(),await this.onRestart()}),ie(this,"checkPending",async()=>{if(this.pending.size===0&&(!this.initialized||!this.relayer.connected))return;const n=[];this.pending.forEach(i=>{n.push(i)}),await this.batchSubscribe(n)}),ie(this,"registerEventListeners",()=>{this.relayer.core.heartbeat.on(fr.pulse,async()=>{await this.checkPending()}),this.events.on(nt.created,async n=>{const i=nt.created;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:n}),await this.persist()}),this.events.on(nt.deleted,async n=>{const i=nt.deleted;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:n}),await this.persist()})}),this.relayer=e,this.logger=je(r,this.name),this.clientId=""}get context(){return We(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.relayer.core.customStoragePrefix+"//"+this.name}get length(){return this.subscriptions.size}get ids(){return Array.from(this.subscriptions.keys())}get values(){return Array.from(this.subscriptions.values())}get topics(){return this.topicMap.topics}get hasAnyTopics(){return this.topicMap.topics.length>0||this.pending.size>0||this.cached.length>0||this.subscriptions.size>0}hasSubscription(e,r){let n=!1;try{n=this.getSubscription(e).topic===r}catch{}return n}reset(){this.cached=[],this.initialized=!0}onDisable(){this.values.length>0&&(this.cached=this.values),this.subscriptions.clear(),this.topicMap.clear()}async unsubscribeByTopic(e,r){const n=this.topicMap.get(e);await Promise.all(n.map(async i=>await this.unsubscribeById(e,i,r)))}async unsubscribeById(e,r,n){this.logger.debug("Unsubscribing Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:r,opts:n}});try{const i=ss(n);await this.restartToComplete({topic:e,id:r,relay:i}),await this.rpcUnsubscribe(e,r,i);const s=ae("USER_DISCONNECTED",`${this.name}, ${e}`);await this.onUnsubscribe(e,r,s),this.logger.debug("Successfully Unsubscribed Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:r,opts:n}})}catch(i){throw this.logger.debug("Failed to Unsubscribe Topic"),this.logger.error(i),i}}async rpcSubscribe(e,r,n){var i;(!n||n?.transportType===ue.relay)&&await this.restartToComplete({topic:e,id:e,relay:r});const s={method:Zn(r.protocol).subscribe,params:{topic:e}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:s});const o=(i=n?.internal)==null?void 0:i.throwOnFailedPublish;try{const a=await this.getSubscriptionId(e);if(n?.transportType===ue.link_mode)return setTimeout(()=>{(this.relayer.connected||this.relayer.connecting)&&this.relayer.request(s).catch(l=>this.logger.warn(l))},F.toMiliseconds(F.ONE_SECOND)),a;const c=new Promise(async l=>{const h=d=>{d.topic===e&&(this.events.removeListener(nt.created,h),l(d.id))};this.events.on(nt.created,h);try{const d=await ir(new Promise((p,f)=>{this.relayer.request(s).catch(y=>{this.logger.warn(y,y?.message),f(y)}).then(p)}),this.initialSubscribeTimeout,`Subscribing to ${e} failed, please try again`);this.events.removeListener(nt.created,h),l(d)}catch{}}),u=await ir(c,this.subscribeTimeout,`Subscribing to ${e} failed, please try again`);if(!u&&o)throw new Error(`Subscribing to ${e} failed, please try again`);return u?a:null}catch(a){if(this.logger.debug("Outgoing Relay Subscribe Payload stalled"),this.relayer.events.emit(Ae.connection_stalled),o)throw a}return null}async rpcBatchSubscribe(e){if(!e.length)return;const r=e[0].relay,n={method:Zn(r.protocol).batchSubscribe,params:{topics:e.map(i=>i.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:n});try{await await ir(new Promise(i=>{this.relayer.request(n).catch(s=>this.logger.warn(s)).then(i)}),this.subscribeTimeout,"rpcBatchSubscribe failed, please try again")}catch{this.relayer.events.emit(Ae.connection_stalled)}}async rpcBatchFetchMessages(e){if(!e.length)return;const r=e[0].relay,n={method:Zn(r.protocol).batchFetchMessages,params:{topics:e.map(s=>s.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:n});let i;try{i=await await ir(new Promise((s,o)=>{this.relayer.request(n).catch(a=>{this.logger.warn(a),o(a)}).then(s)}),this.subscribeTimeout,"rpcBatchFetchMessages failed, please try again")}catch{this.relayer.events.emit(Ae.connection_stalled)}return i}rpcUnsubscribe(e,r,n){const i={method:Zn(n.protocol).unsubscribe,params:{topic:e,id:r}};return this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:i}),this.relayer.request(i)}onSubscribe(e,r){this.setSubscription(e,Ta(oi({},r),{id:e})),this.pending.delete(r.topic)}onBatchSubscribe(e){e.length&&e.forEach(r=>{this.setSubscription(r.id,oi({},r)),this.pending.delete(r.topic)})}async onUnsubscribe(e,r,n){this.events.removeAllListeners(r),this.hasSubscription(r,e)&&this.deleteSubscription(r,n),await this.relayer.messages.del(e)}async setRelayerSubscriptions(e){await this.relayer.core.storage.setItem(this.storageKey,e)}async getRelayerSubscriptions(){return await this.relayer.core.storage.getItem(this.storageKey)}setSubscription(e,r){this.logger.debug("Setting subscription"),this.logger.trace({type:"method",method:"setSubscription",id:e,subscription:r}),this.addSubscription(e,r)}addSubscription(e,r){this.subscriptions.set(e,oi({},r)),this.topicMap.set(r.topic,e),this.events.emit(nt.created,r)}getSubscription(e){this.logger.debug("Getting subscription"),this.logger.trace({type:"method",method:"getSubscription",id:e});const r=this.subscriptions.get(e);if(!r){const{message:n}=L("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(n)}return r}deleteSubscription(e,r){this.logger.debug("Deleting subscription"),this.logger.trace({type:"method",method:"deleteSubscription",id:e,reason:r});const n=this.getSubscription(e);this.subscriptions.delete(e),this.topicMap.delete(n.topic,e),this.events.emit(nt.deleted,Ta(oi({},n),{reason:r}))}async persist(){await this.setRelayerSubscriptions(this.values),this.events.emit(nt.sync)}async onRestart(){if(this.cached.length){const e=[...this.cached],r=Math.ceil(this.cached.length/this.batchSubscribeTopicsLimit);for(let n=0;n<r;n++){const i=e.splice(0,this.batchSubscribeTopicsLimit);await this.batchSubscribe(i)}}this.events.emit(nt.resubscribed)}async restore(){try{const e=await this.getRelayerSubscriptions();if(typeof e>"u"||!e.length)return;if(this.subscriptions.size){const{message:r}=L("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored subscriptions for ${this.name}`),this.logger.trace({type:"method",method:"restore",subscriptions:this.values})}catch(e){this.logger.debug(`Failed to Restore subscriptions for ${this.name}`),this.logger.error(e)}}async batchSubscribe(e){e.length&&(await this.rpcBatchSubscribe(e),this.onBatchSubscribe(await Promise.all(e.map(async r=>Ta(oi({},r),{id:await this.getSubscriptionId(r.topic)})))))}async batchFetchMessages(e){if(!e.length)return;this.logger.trace(`Fetching batch messages for ${e.length} subscriptions`);const r=await this.rpcBatchFetchMessages(e);r&&r.messages&&(await UE(F.toMiliseconds(F.ONE_SECOND)),await this.relayer.handleBatchMessageEvents(r.messages))}async onConnect(){await this.restart(),this.reset()}onDisconnect(){this.onDisable()}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}async restartToComplete(e){!this.relayer.connected&&!this.relayer.connecting&&(this.cached.push(e),await this.relayer.transportOpen())}async getClientId(){return this.clientId||(this.clientId=await this.relayer.core.crypto.getClientId()),this.clientId}async getSubscriptionId(e){return xt(e+await this.getClientId())}}var _O=Object.defineProperty,_f=Object.getOwnPropertySymbols,SO=Object.prototype.hasOwnProperty,IO=Object.prototype.propertyIsEnumerable,Pa=(t,e,r)=>e in t?_O(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Sf=(t,e)=>{for(var r in e||(e={}))SO.call(e,r)&&Pa(t,r,e[r]);if(_f)for(var r of _f(e))IO.call(e,r)&&Pa(t,r,e[r]);return t},Q=(t,e,r)=>Pa(t,typeof e!="symbol"?e+"":e,r);class xO extends Qg{constructor(e){super(e),Q(this,"protocol","wc"),Q(this,"version",2),Q(this,"core"),Q(this,"logger"),Q(this,"events",new Ke.exports.EventEmitter),Q(this,"provider"),Q(this,"messages"),Q(this,"subscriber"),Q(this,"publisher"),Q(this,"name",rx),Q(this,"transportExplicitlyClosed",!1),Q(this,"initialized",!1),Q(this,"connectionAttemptInProgress",!1),Q(this,"relayUrl"),Q(this,"projectId"),Q(this,"packageName"),Q(this,"bundleId"),Q(this,"hasExperiencedNetworkDisruption",!1),Q(this,"pingTimeout"),Q(this,"heartBeatTimeout",F.toMiliseconds(F.THIRTY_SECONDS+F.FIVE_SECONDS)),Q(this,"reconnectTimeout"),Q(this,"connectPromise"),Q(this,"reconnectInProgress",!1),Q(this,"requestsInFlight",[]),Q(this,"connectTimeout",F.toMiliseconds(F.ONE_SECOND*15)),Q(this,"request",async r=>{var n,i;this.logger.debug("Publishing Request Payload");const s=r.id||Tr().toString();await this.toEstablishConnection();try{this.logger.trace({id:s,method:r.method,topic:(n=r.params)==null?void 0:n.topic},"relayer.request - publishing...");const o=`${s}:${((i=r.params)==null?void 0:i.tag)||""}`;this.requestsInFlight.push(o);const a=await this.provider.request(r);return this.requestsInFlight=this.requestsInFlight.filter(c=>c!==o),a}catch(o){throw this.logger.debug(`Failed to Publish Request: ${s}`),o}}),Q(this,"resetPingTimeout",()=>{Wi()&&(clearTimeout(this.pingTimeout),this.pingTimeout=setTimeout(()=>{var r,n,i,s;try{this.logger.debug({},"pingTimeout: Connection stalled, terminating..."),(s=(i=(n=(r=this.provider)==null?void 0:r.connection)==null?void 0:n.socket)==null?void 0:i.terminate)==null||s.call(i)}catch(o){this.logger.warn(o,o?.message)}},this.heartBeatTimeout))}),Q(this,"onPayloadHandler",r=>{this.onProviderPayload(r),this.resetPingTimeout()}),Q(this,"onConnectHandler",()=>{this.logger.warn({},"Relayer connected \u{1F6DC}"),this.startPingTimeout(),this.events.emit(Ae.connect)}),Q(this,"onDisconnectHandler",()=>{this.logger.warn({},"Relayer disconnected \u{1F6D1}"),this.requestsInFlight=[],this.onProviderDisconnect()}),Q(this,"onProviderErrorHandler",r=>{this.logger.fatal(`Fatal socket error: ${r.message}`),this.events.emit(Ae.error,r),this.logger.fatal("Fatal socket error received, closing transport"),this.transportClose()}),Q(this,"registerProviderListeners",()=>{this.provider.on(ct.payload,this.onPayloadHandler),this.provider.on(ct.connect,this.onConnectHandler),this.provider.on(ct.disconnect,this.onDisconnectHandler),this.provider.on(ct.error,this.onProviderErrorHandler)}),this.core=e.core,this.logger=typeof e.logger<"u"&&typeof e.logger!="string"?je(e.logger,this.name):Pt(Ps({level:e.logger||tx})),this.messages=new oO(this.logger,e.core),this.subscriber=new EO(this,this.logger),this.publisher=new dO(this,this.logger),this.relayUrl=e?.relayUrl||Qd,this.projectId=e.projectId,_E()?this.packageName=ah():SE()&&(this.bundleId=ah()),this.provider={}}async init(){if(this.logger.trace("Initialized"),this.registerEventListeners(),await Promise.all([this.messages.init(),this.subscriber.init()]),this.initialized=!0,this.subscriber.hasAnyTopics)try{await this.transportOpen()}catch(e){this.logger.warn(e,e?.message)}}get context(){return We(this.logger)}get connected(){var e,r,n;return((n=(r=(e=this.provider)==null?void 0:e.connection)==null?void 0:r.socket)==null?void 0:n.readyState)===1||!1}get connecting(){var e,r,n;return((n=(r=(e=this.provider)==null?void 0:e.connection)==null?void 0:r.socket)==null?void 0:n.readyState)===0||this.connectPromise!==void 0||!1}async publish(e,r,n){this.isInitialized(),await this.publisher.publish(e,r,n),await this.recordMessageEvent({topic:e,message:r,publishedAt:Date.now(),transportType:ue.relay},ls.outbound)}async subscribe(e,r){var n,i,s;this.isInitialized(),(!(r!=null&&r.transportType)||r?.transportType==="relay")&&await this.toEstablishConnection();const o=typeof((n=r?.internal)==null?void 0:n.throwOnFailedPublish)>"u"?!0:(i=r?.internal)==null?void 0:i.throwOnFailedPublish;let a=((s=this.subscriber.topicMap.get(e))==null?void 0:s[0])||"",c;const u=l=>{l.topic===e&&(this.subscriber.off(nt.created,u),c())};return await Promise.all([new Promise(l=>{c=l,this.subscriber.on(nt.created,u)}),new Promise(async(l,h)=>{a=await this.subscriber.subscribe(e,Sf({internal:{throwOnFailedPublish:o}},r)).catch(d=>{o&&h(d)})||a,l()})]),a}async unsubscribe(e,r){this.isInitialized(),await this.subscriber.unsubscribe(e,r)}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async transportDisconnect(){this.provider.disconnect&&(this.hasExperiencedNetworkDisruption||this.connected)?await ir(this.provider.disconnect(),2e3,"provider.disconnect()").catch(()=>this.onProviderDisconnect()):this.onProviderDisconnect()}async transportClose(){this.transportExplicitlyClosed=!0,await this.transportDisconnect()}async transportOpen(e){if(!this.subscriber.hasAnyTopics){this.logger.warn("Starting WS connection skipped because the client has no topics to work with.");return}if(this.connectPromise?(this.logger.debug({},"Waiting for existing connection attempt to resolve..."),await this.connectPromise,this.logger.debug({},"Existing connection attempt resolved")):(this.connectPromise=new Promise(async(r,n)=>{await this.connect(e).then(r).catch(n).finally(()=>{this.connectPromise=void 0})}),await this.connectPromise),!this.connected)throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`)}async restartTransport(e){this.logger.debug({},"Restarting transport..."),!this.connectionAttemptInProgress&&(this.relayUrl=e||this.relayUrl,await this.confirmOnlineStateOrThrow(),await this.transportClose(),await this.transportOpen())}async confirmOnlineStateOrThrow(){if(!await Cd())throw new Error("No internet connection detected. Please restart your network and try again.")}async handleBatchMessageEvents(e){if(e?.length===0){this.logger.trace("Batch message events is empty. Ignoring...");return}const r=e.sort((n,i)=>n.publishedAt-i.publishedAt);this.logger.debug(`Batch of ${r.length} message events sorted`);for(const n of r)try{await this.onMessageEvent(n)}catch(i){this.logger.warn(i,"Error while processing batch message event: "+i?.message)}this.logger.trace(`Batch of ${r.length} message events processed`)}async onLinkMessageEvent(e,r){const{topic:n}=e;if(!r.sessionExists){const i=ve(F.FIVE_MINUTES),s={topic:n,expiry:i,relay:{protocol:"irn"},active:!1};await this.core.pairing.pairings.set(n,s)}this.events.emit(Ae.message,e),await this.recordMessageEvent(e,ls.inbound)}async connect(e){await this.confirmOnlineStateOrThrow(),e&&e!==this.relayUrl&&(this.relayUrl=e,await this.transportDisconnect()),this.connectionAttemptInProgress=!0,this.transportExplicitlyClosed=!1;let r=1;for(;r<6;){try{if(this.transportExplicitlyClosed)break;this.logger.debug({},`Connecting to ${this.relayUrl}, attempt: ${r}...`),await this.createProvider(),await new Promise(async(n,i)=>{const s=()=>{i(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(ct.disconnect,s),await ir(new Promise((o,a)=>{this.provider.connect().then(o).catch(a)}),this.connectTimeout,`Socket stalled when trying to connect to ${this.relayUrl}`).catch(o=>{i(o)}).finally(()=>{this.provider.off(ct.disconnect,s),clearTimeout(this.reconnectTimeout)}),await new Promise(async(o,a)=>{const c=()=>{a(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(ct.disconnect,c),await this.subscriber.start().then(o).catch(a).finally(()=>{this.provider.off(ct.disconnect,c)})}),this.hasExperiencedNetworkDisruption=!1,n()})}catch(n){await this.subscriber.stop();const i=n;this.logger.warn({},i.message),this.hasExperiencedNetworkDisruption=!0}finally{this.connectionAttemptInProgress=!1}if(this.connected){this.logger.debug({},`Connected to ${this.relayUrl} successfully on attempt: ${r}`);break}await new Promise(n=>setTimeout(n,F.toMiliseconds(r*1))),r++}}startPingTimeout(){var e,r,n,i,s;if(Wi())try{(r=(e=this.provider)==null?void 0:e.connection)!=null&&r.socket&&((s=(i=(n=this.provider)==null?void 0:n.connection)==null?void 0:i.socket)==null||s.on("ping",()=>{this.resetPingTimeout()})),this.resetPingTimeout()}catch(o){this.logger.warn(o,o?.message)}}async createProvider(){this.provider.connection&&this.unregisterProviderListeners();const e=await this.core.crypto.signJWT(this.relayUrl);this.provider=new UI(new qI(AE({sdkVersion:_a,protocol:this.protocol,version:this.version,relayUrl:this.relayUrl,projectId:this.projectId,auth:e,useOnCloseEvent:!0,bundleId:this.bundleId,packageName:this.packageName}))),this.registerProviderListeners()}async recordMessageEvent(e,r){const{topic:n,message:i}=e;await this.messages.set(n,i,r)}async shouldIgnoreMessageEvent(e){const{topic:r,message:n}=e;if(!n||n.length===0)return this.logger.warn(`Ignoring invalid/empty message: ${n}`),!0;if(!await this.subscriber.isKnownTopic(r))return this.logger.warn(`Ignoring message for unknown topic ${r}`),!0;const i=this.messages.has(r,n);return i&&this.logger.warn(`Ignoring duplicate message: ${n}`),i}async onProviderPayload(e){if(this.logger.debug("Incoming Relay Payload"),this.logger.trace({type:"payload",direction:"incoming",payload:e}),Ea(e)){if(!e.method.endsWith(nx))return;const r=e.params,{topic:n,message:i,publishedAt:s,attestation:o}=r.data,a={topic:n,message:i,publishedAt:s,transportType:ue.relay,attestation:o};this.logger.debug("Emitting Relayer Payload"),this.logger.trace(Sf({type:"event",event:r.id},a)),this.events.emit(r.id,a),await this.acknowledgePayload(e),await this.onMessageEvent(a)}else cs(e)&&this.events.emit(Ae.message_ack,e)}async onMessageEvent(e){await this.shouldIgnoreMessageEvent(e)||(await this.recordMessageEvent(e,ls.inbound),this.events.emit(Ae.message,e))}async acknowledgePayload(e){const r=ma(e.id,!0);await this.provider.connection.send(r)}unregisterProviderListeners(){this.provider.off(ct.payload,this.onPayloadHandler),this.provider.off(ct.connect,this.onConnectHandler),this.provider.off(ct.disconnect,this.onDisconnectHandler),this.provider.off(ct.error,this.onProviderErrorHandler),clearTimeout(this.pingTimeout)}async registerEventListeners(){let e=await Cd();YS(async r=>{e!==r&&(e=r,r?await this.transportOpen().catch(n=>this.logger.error(n,n?.message)):(this.hasExperiencedNetworkDisruption=!0,await this.transportDisconnect(),this.transportExplicitlyClosed=!1))}),this.core.heartbeat.on(fr.pulse,async()=>{if(!this.transportExplicitlyClosed&&!this.connected&&JS())try{await this.confirmOnlineStateOrThrow(),await this.transportOpen()}catch(r){this.logger.warn(r,r?.message)}})}async onProviderDisconnect(){clearTimeout(this.pingTimeout),this.events.emit(Ae.disconnect),this.connectionAttemptInProgress=!1,!this.reconnectInProgress&&(this.reconnectInProgress=!0,await this.subscriber.stop(),this.subscriber.hasAnyTopics&&(this.transportExplicitlyClosed||(this.reconnectTimeout=setTimeout(async()=>{await this.transportOpen().catch(e=>this.logger.error(e,e?.message)),this.reconnectTimeout=void 0,this.reconnectInProgress=!1},F.toMiliseconds(ix)))))}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}async toEstablishConnection(){if(await this.confirmOnlineStateOrThrow(),!this.connected){if(this.connectPromise){await this.connectPromise;return}await this.connect()}}}function OO(){}function If(t){if(!t||typeof t!="object")return!1;const e=Object.getPrototypeOf(t);return e===null||e===Object.prototype||Object.getPrototypeOf(e)===null?Object.prototype.toString.call(t)==="[object Object]":!1}function xf(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}function Of(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const DO="[object RegExp]",AO="[object String]",$O="[object Number]",TO="[object Boolean]",Df="[object Arguments]",PO="[object Symbol]",NO="[object Date]",RO="[object Map]",CO="[object Set]",BO="[object Array]",FO="[object Function]",LO="[object ArrayBuffer]",Na="[object Object]",UO="[object Error]",kO="[object DataView]",jO="[object Uint8Array]",MO="[object Uint8ClampedArray]",qO="[object Uint16Array]",zO="[object Uint32Array]",VO="[object BigUint64Array]",KO="[object Int8Array]",HO="[object Int16Array]",WO="[object Int32Array]",GO="[object BigInt64Array]",YO="[object Float32Array]",ZO="[object Float64Array]";function XO(t,e){return t===e||Number.isNaN(t)&&Number.isNaN(e)}function JO(t,e,r){return ai(t,e,void 0,void 0,void 0,void 0,r)}function ai(t,e,r,n,i,s,o){const a=o(t,e,r,n,i,s);if(a!==void 0)return a;if(typeof t==typeof e)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":return t===e;case"number":return t===e||Object.is(t,e);case"function":return t===e;case"object":return ci(t,e,s,o)}return ci(t,e,s,o)}function ci(t,e,r,n){if(Object.is(t,e))return!0;let i=Of(t),s=Of(e);if(i===Df&&(i=Na),s===Df&&(s=Na),i!==s)return!1;switch(i){case AO:return t.toString()===e.toString();case $O:{const c=t.valueOf(),u=e.valueOf();return XO(c,u)}case TO:case NO:case PO:return Object.is(t.valueOf(),e.valueOf());case DO:return t.source===e.source&&t.flags===e.flags;case FO:return t===e}r=r??new Map;const o=r.get(t),a=r.get(e);if(o!=null&&a!=null)return o===e;r.set(t,e),r.set(e,t);try{switch(i){case RO:{if(t.size!==e.size)return!1;for(const[c,u]of t.entries())if(!e.has(c)||!ai(u,e.get(c),c,t,e,r,n))return!1;return!0}case CO:{if(t.size!==e.size)return!1;const c=Array.from(t.values()),u=Array.from(e.values());for(let l=0;l<c.length;l++){const h=c[l],d=u.findIndex(p=>ai(h,p,void 0,t,e,r,n));if(d===-1)return!1;u.splice(d,1)}return!0}case BO:case jO:case MO:case qO:case zO:case VO:case KO:case HO:case WO:case GO:case YO:case ZO:{if(typeof Buffer<"u"&&Buffer.isBuffer(t)!==Buffer.isBuffer(e)||t.length!==e.length)return!1;for(let c=0;c<t.length;c++)if(!ai(t[c],e[c],c,t,e,r,n))return!1;return!0}case LO:return t.byteLength!==e.byteLength?!1:ci(new Uint8Array(t),new Uint8Array(e),r,n);case kO:return t.byteLength!==e.byteLength||t.byteOffset!==e.byteOffset?!1:ci(new Uint8Array(t),new Uint8Array(e),r,n);case UO:return t.name===e.name&&t.message===e.message;case Na:{if(!(ci(t.constructor,e.constructor,r,n)||If(t)&&If(e)))return!1;const c=[...Object.keys(t),...xf(t)],u=[...Object.keys(e),...xf(e)];if(c.length!==u.length)return!1;for(let l=0;l<c.length;l++){const h=c[l],d=t[h];if(!Object.hasOwn(e,h))return!1;const p=e[h];if(!ai(d,p,h,t,e,r,n))return!1}return!0}default:return!1}}finally{r.delete(t),r.delete(e)}}function QO(t,e){return JO(t,e,OO)}var eD=Object.defineProperty,Af=Object.getOwnPropertySymbols,tD=Object.prototype.hasOwnProperty,rD=Object.prototype.propertyIsEnumerable,Ra=(t,e,r)=>e in t?eD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,$f=(t,e)=>{for(var r in e||(e={}))tD.call(e,r)&&Ra(t,r,e[r]);if(Af)for(var r of Af(e))rD.call(e,r)&&Ra(t,r,e[r]);return t},Je=(t,e,r)=>Ra(t,typeof e!="symbol"?e+"":e,r);class Br extends ey{constructor(e,r,n,i=At,s=void 0){super(e,r,n,i),this.core=e,this.logger=r,this.name=n,Je(this,"map",new Map),Je(this,"version",sx),Je(this,"cached",[]),Je(this,"initialized",!1),Je(this,"getKey"),Je(this,"storagePrefix",At),Je(this,"recentlyDeleted",[]),Je(this,"recentlyDeletedLimit",200),Je(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(o=>{this.getKey&&o!==null&&!Ce(o)?this.map.set(this.getKey(o),o):OS(o)?this.map.set(o.id,o):DS(o)&&this.map.set(o.topic,o)}),this.cached=[],this.initialized=!0)}),Je(this,"set",async(o,a)=>{this.isInitialized(),this.map.has(o)?await this.update(o,a):(this.logger.debug("Setting value"),this.logger.trace({type:"method",method:"set",key:o,value:a}),this.map.set(o,a),await this.persist())}),Je(this,"get",o=>(this.isInitialized(),this.logger.debug("Getting value"),this.logger.trace({type:"method",method:"get",key:o}),this.getData(o))),Je(this,"getAll",o=>(this.isInitialized(),o?this.values.filter(a=>Object.keys(o).every(c=>QO(a[c],o[c]))):this.values)),Je(this,"update",async(o,a)=>{this.isInitialized(),this.logger.debug("Updating value"),this.logger.trace({type:"method",method:"update",key:o,update:a});const c=$f($f({},this.getData(o)),a);this.map.set(o,c),await this.persist()}),Je(this,"delete",async(o,a)=>{this.isInitialized(),this.map.has(o)&&(this.logger.debug("Deleting value"),this.logger.trace({type:"method",method:"delete",key:o,reason:a}),this.map.delete(o),this.addToRecentlyDeleted(o),await this.persist())}),this.logger=je(r,this.name),this.storagePrefix=i,this.getKey=s}get context(){return We(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.map.size}get keys(){return Array.from(this.map.keys())}get values(){return Array.from(this.map.values())}addToRecentlyDeleted(e){this.recentlyDeleted.push(e),this.recentlyDeleted.length>=this.recentlyDeletedLimit&&this.recentlyDeleted.splice(0,this.recentlyDeletedLimit/2)}async setDataStore(e){await this.core.storage.setItem(this.storageKey,e)}async getDataStore(){return await this.core.storage.getItem(this.storageKey)}getData(e){const r=this.map.get(e);if(!r){if(this.recentlyDeleted.includes(e)){const{message:i}=L("MISSING_OR_INVALID",`Record was recently deleted - ${this.name}: ${e}`);throw this.logger.error(i),new Error(i)}const{message:n}=L("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.error(n),new Error(n)}return r}async persist(){await this.setDataStore(this.values)}async restore(){try{const e=await this.getDataStore();if(typeof e>"u"||!e.length)return;if(this.map.size){const{message:r}=L("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored value for ${this.name}`),this.logger.trace({type:"method",method:"restore",value:this.values})}catch(e){this.logger.debug(`Failed to Restore value for ${this.name}`),this.logger.error(e)}}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}}var nD=Object.defineProperty,iD=(t,e,r)=>e in t?nD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Z=(t,e,r)=>iD(t,typeof e!="symbol"?e+"":e,r);class sD{constructor(e,r){this.core=e,this.logger=r,Z(this,"name",ux),Z(this,"version",lx),Z(this,"events",new Ke.exports),Z(this,"pairings"),Z(this,"initialized",!1),Z(this,"storagePrefix",At),Z(this,"ignoredPayloadTypes",[zt]),Z(this,"registeredMethods",[]),Z(this,"init",async()=>{this.initialized||(await this.pairings.init(),await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.initialized=!0,this.logger.trace("Initialized"))}),Z(this,"register",({methods:n})=>{this.isInitialized(),this.registeredMethods=[...new Set([...this.registeredMethods,...n])]}),Z(this,"create",async n=>{this.isInitialized();const i=ha(),s=await this.core.crypto.setSymKey(i),o=ve(F.FIVE_MINUTES),a={protocol:Jd},c={topic:s,expiry:o,relay:a,active:!1,methods:n?.methods},u=_d({protocol:this.core.protocol,version:this.core.version,topic:s,symKey:i,relay:a,expiryTimestamp:o,methods:n?.methods});return this.events.emit(Nr.create,c),this.core.expirer.set(s,o),await this.pairings.set(s,c),await this.core.relayer.subscribe(s,{transportType:n?.transportType}),{topic:s,uri:u}}),Z(this,"pair",async n=>{this.isInitialized();const i=this.core.eventClient.createEvent({properties:{topic:n?.uri,trace:[$t.pairing_started]}});this.isValidPair(n,i);const{topic:s,symKey:o,relay:a,expiryTimestamp:c,methods:u}=Ed(n.uri);i.props.properties.topic=s,i.addTrace($t.pairing_uri_validation_success),i.addTrace($t.pairing_uri_not_expired);let l;if(this.pairings.keys.includes(s)){if(l=this.pairings.get(s),i.addTrace($t.existing_pairing),l.active)throw i.setError(Ht.active_pairing_already_exists),new Error(`Pairing already exists: ${s}. Please try again with a new connection URI.`);i.addTrace($t.pairing_not_expired)}const h=c||ve(F.FIVE_MINUTES),d={topic:s,relay:a,expiry:h,active:!1,methods:u};this.core.expirer.set(s,h),await this.pairings.set(s,d),i.addTrace($t.store_new_pairing),n.activatePairing&&await this.activate({topic:s}),this.events.emit(Nr.create,d),i.addTrace($t.emit_inactive_pairing),this.core.crypto.keychain.has(s)||await this.core.crypto.setSymKey(o,s),i.addTrace($t.subscribing_pairing_topic);try{await this.core.relayer.confirmOnlineStateOrThrow()}catch{i.setError(Ht.no_internet_connection)}try{await this.core.relayer.subscribe(s,{relay:a})}catch(p){throw i.setError(Ht.subscribe_pairing_topic_failure),p}return i.addTrace($t.subscribe_pairing_topic_success),d}),Z(this,"activate",async({topic:n})=>{this.isInitialized();const i=ve(F.FIVE_MINUTES);this.core.expirer.set(n,i),await this.pairings.update(n,{active:!0,expiry:i})}),Z(this,"ping",async n=>{this.isInitialized(),await this.isValidPing(n),this.logger.warn("ping() is deprecated and will be removed in the next major release.");const{topic:i}=n;if(this.pairings.keys.includes(i)){const s=await this.sendRequest(i,"wc_pairingPing",{}),{done:o,resolve:a,reject:c}=Sr();this.events.once(te("pairing_ping",s),({error:u})=>{u?c(u):a()}),await o()}}),Z(this,"updateExpiry",async({topic:n,expiry:i})=>{this.isInitialized(),await this.pairings.update(n,{expiry:i})}),Z(this,"updateMetadata",async({topic:n,metadata:i})=>{this.isInitialized(),await this.pairings.update(n,{peerMetadata:i})}),Z(this,"getPairings",()=>(this.isInitialized(),this.pairings.values)),Z(this,"disconnect",async n=>{this.isInitialized(),await this.isValidDisconnect(n);const{topic:i}=n;this.pairings.keys.includes(i)&&(await this.sendRequest(i,"wc_pairingDelete",ae("USER_DISCONNECTED")),await this.deletePairing(i))}),Z(this,"formatUriFromPairing",n=>{this.isInitialized();const{topic:i,relay:s,expiry:o,methods:a}=n,c=this.core.crypto.keychain.get(i);return _d({protocol:this.core.protocol,version:this.core.version,topic:i,symKey:c,relay:s,expiryTimestamp:o,methods:a})}),Z(this,"sendRequest",async(n,i,s)=>{const o=Pr(i,s),a=await this.core.crypto.encode(n,o),c=ri[i].req;return this.core.history.set(n,o),this.core.relayer.publish(n,a,c),o.id}),Z(this,"sendResult",async(n,i,s)=>{const o=ma(n,s),a=await this.core.crypto.encode(i,o),c=(await this.core.history.get(i,n)).request.method,u=ri[c].res;await this.core.relayer.publish(i,a,u),await this.core.history.resolve(o)}),Z(this,"sendError",async(n,i,s)=>{const o=va(n,s),a=await this.core.crypto.encode(i,o),c=(await this.core.history.get(i,n)).request.method,u=ri[c]?ri[c].res:ri.unregistered_method.res;await this.core.relayer.publish(i,a,u),await this.core.history.resolve(o)}),Z(this,"deletePairing",async(n,i)=>{await this.core.relayer.unsubscribe(n),await Promise.all([this.pairings.delete(n,ae("USER_DISCONNECTED")),this.core.crypto.deleteSymKey(n),i?Promise.resolve():this.core.expirer.del(n)])}),Z(this,"cleanup",async()=>{const n=this.pairings.getAll().filter(i=>sr(i.expiry));await Promise.all(n.map(i=>this.deletePairing(i.topic)))}),Z(this,"onRelayEventRequest",async n=>{const{topic:i,payload:s}=n;switch(s.method){case"wc_pairingPing":return await this.onPairingPingRequest(i,s);case"wc_pairingDelete":return await this.onPairingDeleteRequest(i,s);default:return await this.onUnknownRpcMethodRequest(i,s)}}),Z(this,"onRelayEventResponse",async n=>{const{topic:i,payload:s}=n,o=(await this.core.history.get(i,s.id)).request.method;switch(o){case"wc_pairingPing":return this.onPairingPingResponse(i,s);default:return this.onUnknownRpcMethodResponse(o)}}),Z(this,"onPairingPingRequest",async(n,i)=>{const{id:s}=i;try{this.isValidPing({topic:n}),await this.sendResult(s,n,!0),this.events.emit(Nr.ping,{id:s,topic:n})}catch(o){await this.sendError(s,n,o),this.logger.error(o)}}),Z(this,"onPairingPingResponse",(n,i)=>{const{id:s}=i;setTimeout(()=>{Dt(i)?this.events.emit(te("pairing_ping",s),{}):at(i)&&this.events.emit(te("pairing_ping",s),{error:i.error})},500)}),Z(this,"onPairingDeleteRequest",async(n,i)=>{const{id:s}=i;try{this.isValidDisconnect({topic:n}),await this.deletePairing(n),this.events.emit(Nr.delete,{id:s,topic:n})}catch(o){await this.sendError(s,n,o),this.logger.error(o)}}),Z(this,"onUnknownRpcMethodRequest",async(n,i)=>{const{id:s,method:o}=i;try{if(this.registeredMethods.includes(o))return;const a=ae("WC_METHOD_UNSUPPORTED",o);await this.sendError(s,n,a),this.logger.error(a)}catch(a){await this.sendError(s,n,a),this.logger.error(a)}}),Z(this,"onUnknownRpcMethodResponse",n=>{this.registeredMethods.includes(n)||this.logger.error(ae("WC_METHOD_UNSUPPORTED",n))}),Z(this,"isValidPair",(n,i)=>{var s;if(!Xe(n)){const{message:a}=L("MISSING_OR_INVALID",`pair() params: ${n}`);throw i.setError(Ht.malformed_pairing_uri),new Error(a)}if(!xS(n.uri)){const{message:a}=L("MISSING_OR_INVALID",`pair() uri: ${n.uri}`);throw i.setError(Ht.malformed_pairing_uri),new Error(a)}const o=Ed(n?.uri);if(!((s=o?.relay)!=null&&s.protocol)){const{message:a}=L("MISSING_OR_INVALID","pair() uri#relay-protocol");throw i.setError(Ht.malformed_pairing_uri),new Error(a)}if(!(o!=null&&o.symKey)){const{message:a}=L("MISSING_OR_INVALID","pair() uri#symKey");throw i.setError(Ht.malformed_pairing_uri),new Error(a)}if(o!=null&&o.expiryTimestamp&&F.toMiliseconds(o?.expiryTimestamp)<Date.now()){i.setError(Ht.pairing_expired);const{message:a}=L("EXPIRED","pair() URI has expired. Please try again with a new connection URI.");throw new Error(a)}}),Z(this,"isValidPing",async n=>{if(!Xe(n)){const{message:s}=L("MISSING_OR_INVALID",`ping() params: ${n}`);throw new Error(s)}const{topic:i}=n;await this.isValidPairingTopic(i)}),Z(this,"isValidDisconnect",async n=>{if(!Xe(n)){const{message:s}=L("MISSING_OR_INVALID",`disconnect() params: ${n}`);throw new Error(s)}const{topic:i}=n;await this.isValidPairingTopic(i)}),Z(this,"isValidPairingTopic",async n=>{if(!ye(n,!1)){const{message:i}=L("MISSING_OR_INVALID",`pairing topic should be a string: ${n}`);throw new Error(i)}if(!this.pairings.keys.includes(n)){const{message:i}=L("NO_MATCHING_KEY",`pairing topic doesn't exist: ${n}`);throw new Error(i)}if(sr(this.pairings.get(n).expiry)){await this.deletePairing(n);const{message:i}=L("EXPIRED",`pairing topic: ${n}`);throw new Error(i)}}),this.core=e,this.logger=je(r,this.name),this.pairings=new Br(this.core,this.logger,this.name,this.storagePrefix)}get context(){return We(this.logger)}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}registerRelayerEvents(){this.core.relayer.on(Ae.message,async e=>{const{topic:r,message:n,transportType:i}=e;if(this.pairings.keys.includes(r)&&i!==ue.link_mode&&!this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(n)))try{const s=await this.core.crypto.decode(r,n);Ea(s)?(this.core.history.set(r,s),await this.onRelayEventRequest({topic:r,payload:s})):cs(s)&&(await this.core.history.resolve(s),await this.onRelayEventResponse({topic:r,payload:s}),this.core.history.delete(r,s.id)),await this.core.relayer.messages.ack(r,n)}catch(s){this.logger.error(s)}})}registerExpirerEvents(){this.core.expirer.on(ut.expired,async e=>{const{topic:r}=hh(e.target);r&&this.pairings.keys.includes(r)&&(await this.deletePairing(r,!0),this.events.emit(Nr.expire,{topic:r}))})}}var oD=Object.defineProperty,aD=(t,e,r)=>e in t?oD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Le=(t,e,r)=>aD(t,typeof e!="symbol"?e+"":e,r);class cD extends Zg{constructor(e,r){super(e,r),this.core=e,this.logger=r,Le(this,"records",new Map),Le(this,"events",new Ke.exports.EventEmitter),Le(this,"name",hx),Le(this,"version",dx),Le(this,"cached",[]),Le(this,"initialized",!1),Le(this,"storagePrefix",At),Le(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(n=>this.records.set(n.id,n)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),Le(this,"set",(n,i,s)=>{if(this.isInitialized(),this.logger.debug("Setting JSON-RPC request history record"),this.logger.trace({type:"method",method:"set",topic:n,request:i,chainId:s}),this.records.has(i.id))return;const o={id:i.id,topic:n,request:{method:i.method,params:i.params||null},chainId:s,expiry:ve(F.THIRTY_DAYS)};this.records.set(o.id,o),this.persist(),this.events.emit(pt.created,o)}),Le(this,"resolve",async n=>{if(this.isInitialized(),this.logger.debug("Updating JSON-RPC response history record"),this.logger.trace({type:"method",method:"update",response:n}),!this.records.has(n.id))return;const i=await this.getRecord(n.id);typeof i.response>"u"&&(i.response=at(n)?{error:n.error}:{result:n.result},this.records.set(i.id,i),this.persist(),this.events.emit(pt.updated,i))}),Le(this,"get",async(n,i)=>(this.isInitialized(),this.logger.debug("Getting record"),this.logger.trace({type:"method",method:"get",topic:n,id:i}),await this.getRecord(i))),Le(this,"delete",(n,i)=>{this.isInitialized(),this.logger.debug("Deleting record"),this.logger.trace({type:"method",method:"delete",id:i}),this.values.forEach(s=>{if(s.topic===n){if(typeof i<"u"&&s.id!==i)return;this.records.delete(s.id),this.events.emit(pt.deleted,s)}}),this.persist()}),Le(this,"exists",async(n,i)=>(this.isInitialized(),this.records.has(i)?(await this.getRecord(i)).topic===n:!1)),Le(this,"on",(n,i)=>{this.events.on(n,i)}),Le(this,"once",(n,i)=>{this.events.once(n,i)}),Le(this,"off",(n,i)=>{this.events.off(n,i)}),Le(this,"removeListener",(n,i)=>{this.events.removeListener(n,i)}),this.logger=je(r,this.name)}get context(){return We(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get size(){return this.records.size}get keys(){return Array.from(this.records.keys())}get values(){return Array.from(this.records.values())}get pending(){const e=[];return this.values.forEach(r=>{if(typeof r.response<"u")return;const n={topic:r.topic,request:Pr(r.request.method,r.request.params,r.id),chainId:r.chainId};return e.push(n)}),e}async setJsonRpcRecords(e){await this.core.storage.setItem(this.storageKey,e)}async getJsonRpcRecords(){return await this.core.storage.getItem(this.storageKey)}getRecord(e){this.isInitialized();const r=this.records.get(e);if(!r){const{message:n}=L("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(n)}return r}async persist(){await this.setJsonRpcRecords(this.values),this.events.emit(pt.sync)}async restore(){try{const e=await this.getJsonRpcRecords();if(typeof e>"u"||!e.length)return;if(this.records.size){const{message:r}=L("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",records:this.values})}catch(e){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(e)}}registerEventListeners(){this.events.on(pt.created,e=>{const r=pt.created;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.events.on(pt.updated,e=>{const r=pt.updated;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.events.on(pt.deleted,e=>{const r=pt.deleted;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.core.heartbeat.on(fr.pulse,()=>{this.cleanup()})}cleanup(){try{this.isInitialized();let e=!1;this.records.forEach(r=>{F.toMiliseconds(r.expiry||0)-Date.now()<=0&&(this.logger.info(`Deleting expired history log: ${r.id}`),this.records.delete(r.id),this.events.emit(pt.deleted,r,!1),e=!0)}),e&&this.persist()}catch(e){this.logger.warn(e)}}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}}var uD=Object.defineProperty,lD=(t,e,r)=>e in t?uD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ke=(t,e,r)=>lD(t,typeof e!="symbol"?e+"":e,r);class hD extends ry{constructor(e,r){super(e,r),this.core=e,this.logger=r,ke(this,"expirations",new Map),ke(this,"events",new Ke.exports.EventEmitter),ke(this,"name",fx),ke(this,"version",px),ke(this,"cached",[]),ke(this,"initialized",!1),ke(this,"storagePrefix",At),ke(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(n=>this.expirations.set(n.target,n)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),ke(this,"has",n=>{try{const i=this.formatTarget(n);return typeof this.getExpiration(i)<"u"}catch{return!1}}),ke(this,"set",(n,i)=>{this.isInitialized();const s=this.formatTarget(n),o={target:s,expiry:i};this.expirations.set(s,o),this.checkExpiry(s,o),this.events.emit(ut.created,{target:s,expiration:o})}),ke(this,"get",n=>{this.isInitialized();const i=this.formatTarget(n);return this.getExpiration(i)}),ke(this,"del",n=>{if(this.isInitialized(),this.has(n)){const i=this.formatTarget(n),s=this.getExpiration(i);this.expirations.delete(i),this.events.emit(ut.deleted,{target:i,expiration:s})}}),ke(this,"on",(n,i)=>{this.events.on(n,i)}),ke(this,"once",(n,i)=>{this.events.once(n,i)}),ke(this,"off",(n,i)=>{this.events.off(n,i)}),ke(this,"removeListener",(n,i)=>{this.events.removeListener(n,i)}),this.logger=je(r,this.name)}get context(){return We(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.expirations.size}get keys(){return Array.from(this.expirations.keys())}get values(){return Array.from(this.expirations.values())}formatTarget(e){if(typeof e=="string")return $E(e);if(typeof e=="number")return TE(e);const{message:r}=L("UNKNOWN_TYPE",`Target type: ${typeof e}`);throw new Error(r)}async setExpirations(e){await this.core.storage.setItem(this.storageKey,e)}async getExpirations(){return await this.core.storage.getItem(this.storageKey)}async persist(){await this.setExpirations(this.values),this.events.emit(ut.sync)}async restore(){try{const e=await this.getExpirations();if(typeof e>"u"||!e.length)return;if(this.expirations.size){const{message:r}=L("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored expirations for ${this.name}`),this.logger.trace({type:"method",method:"restore",expirations:this.values})}catch(e){this.logger.debug(`Failed to Restore expirations for ${this.name}`),this.logger.error(e)}}getExpiration(e){const r=this.expirations.get(e);if(!r){const{message:n}=L("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.warn(n),new Error(n)}return r}checkExpiry(e,r){const{expiry:n}=r;F.toMiliseconds(n)-Date.now()<=0&&this.expire(e,r)}expire(e,r){this.expirations.delete(e),this.events.emit(ut.expired,{target:e,expiration:r})}checkExpirations(){this.core.relayer.connected&&this.expirations.forEach((e,r)=>this.checkExpiry(r,e))}registerEventListeners(){this.core.heartbeat.on(fr.pulse,()=>this.checkExpirations()),this.events.on(ut.created,e=>{const r=ut.created;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()}),this.events.on(ut.expired,e=>{const r=ut.expired;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()}),this.events.on(ut.deleted,e=>{const r=ut.deleted;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()})}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}}var dD=Object.defineProperty,fD=(t,e,r)=>e in t?dD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,we=(t,e,r)=>fD(t,typeof e!="symbol"?e+"":e,r);class pD extends ny{constructor(e,r,n){super(e,r,n),this.core=e,this.logger=r,this.store=n,we(this,"name",gx),we(this,"abortController"),we(this,"isDevEnv"),we(this,"verifyUrlV3",wx),we(this,"storagePrefix",At),we(this,"version",Yd),we(this,"publicKey"),we(this,"fetchPromise"),we(this,"init",async()=>{var i;this.isDevEnv||(this.publicKey=await this.store.getItem(this.storeKey),this.publicKey&&F.toMiliseconds((i=this.publicKey)==null?void 0:i.expiresAt)<Date.now()&&(this.logger.debug("verify v2 public key expired"),await this.removePublicKey()))}),we(this,"register",async i=>{if(!rn()||this.isDevEnv)return;const s=window.location.origin,{id:o,decryptedId:a}=i,c=`${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${s}&id=${o}&decryptedId=${a}`;try{const u=br(),l=this.startAbortTimer(F.ONE_SECOND*5),h=await new Promise((d,p)=>{const f=()=>{window.removeEventListener("message",g),u.body.removeChild(y),p("attestation aborted")};this.abortController.signal.addEventListener("abort",f);const y=u.createElement("iframe");y.src=c,y.style.display="none",y.addEventListener("error",f,{signal:this.abortController.signal});const g=b=>{if(b.data&&typeof b.data=="string")try{const w=JSON.parse(b.data);if(w.type==="verify_attestation"){if(Js(w.attestation).payload.id!==o)return;clearInterval(l),u.body.removeChild(y),this.abortController.signal.removeEventListener("abort",f),window.removeEventListener("message",g),d(w.attestation===null?"":w.attestation)}}catch(w){this.logger.warn(w)}};u.body.appendChild(y),window.addEventListener("message",g,{signal:this.abortController.signal})});return this.logger.debug("jwt attestation",h),h}catch(u){this.logger.warn(u)}return""}),we(this,"resolve",async i=>{if(this.isDevEnv)return"";const{attestationId:s,hash:o,encryptedId:a}=i;if(s===""){this.logger.debug("resolve: attestationId is empty, skipping");return}if(s){if(Js(s).payload.id!==a)return;const u=await this.isValidJwtAttestation(s);if(u){if(!u.isVerified){this.logger.warn("resolve: jwt attestation: origin url not verified");return}return u}}if(!o)return;const c=this.getVerifyUrl(i?.verifyUrl);return this.fetchAttestation(o,c)}),we(this,"fetchAttestation",async(i,s)=>{this.logger.debug(`resolving attestation: ${i} from url: ${s}`);const o=this.startAbortTimer(F.ONE_SECOND*5),a=await fetch(`${s}/attestation/${i}?v2Supported=true`,{signal:this.abortController.signal});return clearTimeout(o),a.status===200?await a.json():void 0}),we(this,"getVerifyUrl",i=>{let s=i||ni;return bx.includes(s)||(this.logger.info(`verify url: ${s}, not included in trusted list, assigning default: ${ni}`),s=ni),s}),we(this,"fetchPublicKey",async()=>{try{this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);const i=this.startAbortTimer(F.FIVE_SECONDS),s=await fetch(`${this.verifyUrlV3}/public-key`,{signal:this.abortController.signal});return clearTimeout(i),await s.json()}catch(i){this.logger.warn(i)}}),we(this,"persistPublicKey",async i=>{this.logger.debug("persisting public key to local storage",i),await this.store.setItem(this.storeKey,i),this.publicKey=i}),we(this,"removePublicKey",async()=>{this.logger.debug("removing verify v2 public key from storage"),await this.store.removeItem(this.storeKey),this.publicKey=void 0}),we(this,"isValidJwtAttestation",async i=>{const s=await this.getPublicKey();try{if(s)return this.validateAttestation(i,s)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}const o=await this.fetchAndPersistPublicKey();try{if(o)return this.validateAttestation(i,o)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}}),we(this,"getPublicKey",async()=>this.publicKey?this.publicKey:await this.fetchAndPersistPublicKey()),we(this,"fetchAndPersistPublicKey",async()=>{if(this.fetchPromise)return await this.fetchPromise,this.publicKey;this.fetchPromise=new Promise(async s=>{const o=await this.fetchPublicKey();o&&(await this.persistPublicKey(o),s(o))});const i=await this.fetchPromise;return this.fetchPromise=void 0,i}),we(this,"validateAttestation",(i,s)=>{const o=nS(i,s.publicKey),a={hasExpired:F.toMiliseconds(o.exp)<Date.now(),payload:o};if(a.hasExpired)throw this.logger.warn("resolve: jwt attestation expired"),new Error("JWT attestation expired");return{origin:a.payload.origin,isScam:a.payload.isScam,isVerified:a.payload.isVerified}}),this.logger=je(r,this.name),this.abortController=new AbortController,this.isDevEnv=Uo(),this.init()}get storeKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//verify:public:key"}get context(){return We(this.logger)}startAbortTimer(e){return this.abortController=new AbortController,setTimeout(()=>this.abortController.abort(),F.toMiliseconds(e))}}var gD=Object.defineProperty,yD=(t,e,r)=>e in t?gD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Tf=(t,e,r)=>yD(t,typeof e!="symbol"?e+"":e,r);class wD extends iy{constructor(e,r){super(e,r),this.projectId=e,this.logger=r,Tf(this,"context",mx),Tf(this,"registerDeviceToken",async n=>{const{clientId:i,token:s,notificationType:o,enableEncrypted:a=!1}=n,c=`${vx}/${this.projectId}/clients`;await fetch(c,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:i,type:o,token:s,always_raw:a})})}),this.logger=je(r,this.context)}}var bD=Object.defineProperty,Pf=Object.getOwnPropertySymbols,mD=Object.prototype.hasOwnProperty,vD=Object.prototype.propertyIsEnumerable,Ca=(t,e,r)=>e in t?bD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ui=(t,e)=>{for(var r in e||(e={}))mD.call(e,r)&&Ca(t,r,e[r]);if(Pf)for(var r of Pf(e))vD.call(e,r)&&Ca(t,r,e[r]);return t},$e=(t,e,r)=>Ca(t,typeof e!="symbol"?e+"":e,r);class ED extends sy{constructor(e,r,n=!0){super(e,r,n),this.core=e,this.logger=r,$e(this,"context",_x),$e(this,"storagePrefix",At),$e(this,"storageVersion",Ex),$e(this,"events",new Map),$e(this,"shouldPersist",!1),$e(this,"init",async()=>{if(!Uo())try{const i={eventId:fh(),timestamp:Date.now(),domain:this.getAppDomain(),props:{event:"INIT",type:"",properties:{client_id:await this.core.crypto.getClientId(),user_agent:uh(this.core.relayer.protocol,this.core.relayer.version,_a)}}};await this.sendEvent([i])}catch(i){this.logger.warn(i)}}),$e(this,"createEvent",i=>{const{event:s="ERROR",type:o="",properties:{topic:a,trace:c}}=i,u=fh(),l=this.core.projectId||"",h=Date.now(),d=ui({eventId:u,timestamp:h,props:{event:s,type:o,properties:{topic:a,trace:c}},bundleId:l,domain:this.getAppDomain()},this.setMethods(u));return this.telemetryEnabled&&(this.events.set(u,d),this.shouldPersist=!0),d}),$e(this,"getEvent",i=>{const{eventId:s,topic:o}=i;if(s)return this.events.get(s);const a=Array.from(this.events.values()).find(c=>c.props.properties.topic===o);if(a)return ui(ui({},a),this.setMethods(a.eventId))}),$e(this,"deleteEvent",i=>{const{eventId:s}=i;this.events.delete(s),this.shouldPersist=!0}),$e(this,"setEventListeners",()=>{this.core.heartbeat.on(fr.pulse,async()=>{this.shouldPersist&&await this.persist(),this.events.forEach(i=>{F.fromMiliseconds(Date.now())-F.fromMiliseconds(i.timestamp)>Sx&&(this.events.delete(i.eventId),this.shouldPersist=!0)})})}),$e(this,"setMethods",i=>({addTrace:s=>this.addTrace(i,s),setError:s=>this.setError(i,s)})),$e(this,"addTrace",(i,s)=>{const o=this.events.get(i);o&&(o.props.properties.trace.push(s),this.events.set(i,o),this.shouldPersist=!0)}),$e(this,"setError",(i,s)=>{const o=this.events.get(i);o&&(o.props.type=s,o.timestamp=Date.now(),this.events.set(i,o),this.shouldPersist=!0)}),$e(this,"persist",async()=>{await this.core.storage.setItem(this.storageKey,Array.from(this.events.values())),this.shouldPersist=!1}),$e(this,"restore",async()=>{try{const i=await this.core.storage.getItem(this.storageKey)||[];if(!i.length)return;i.forEach(s=>{this.events.set(s.eventId,ui(ui({},s),this.setMethods(s.eventId)))})}catch(i){this.logger.warn(i)}}),$e(this,"submit",async()=>{if(!this.telemetryEnabled||this.events.size===0)return;const i=[];for(const[s,o]of this.events)o.props.type&&i.push(o);if(i.length!==0)try{if((await this.sendEvent(i)).ok)for(const s of i)this.events.delete(s.eventId),this.shouldPersist=!0}catch(s){this.logger.warn(s)}}),$e(this,"sendEvent",async i=>{const s=this.getAppDomain()?"":"&sp=desktop";return await fetch(`${Ix}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${_a}${s}`,{method:"POST",body:JSON.stringify(i)})}),$e(this,"getAppDomain",()=>ch().url),this.logger=je(r,this.context),this.telemetryEnabled=n,n?this.restore().then(async()=>{await this.submit(),this.setEventListeners()}):this.persist()}get storageKey(){return this.storagePrefix+this.storageVersion+this.core.customStoragePrefix+"//"+this.context}}var _D=Object.defineProperty,Nf=Object.getOwnPropertySymbols,SD=Object.prototype.hasOwnProperty,ID=Object.prototype.propertyIsEnumerable,Ba=(t,e,r)=>e in t?_D(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Rf=(t,e)=>{for(var r in e||(e={}))SD.call(e,r)&&Ba(t,r,e[r]);if(Nf)for(var r of Nf(e))ID.call(e,r)&&Ba(t,r,e[r]);return t},ce=(t,e,r)=>Ba(t,typeof e!="symbol"?e+"":e,r);class Fa extends Hg{constructor(e){var r;super(e),ce(this,"protocol",Gd),ce(this,"version",Yd),ce(this,"name",us),ce(this,"relayUrl"),ce(this,"projectId"),ce(this,"customStoragePrefix"),ce(this,"events",new Ke.exports.EventEmitter),ce(this,"logger"),ce(this,"heartbeat"),ce(this,"relayer"),ce(this,"crypto"),ce(this,"storage"),ce(this,"history"),ce(this,"expirer"),ce(this,"pairing"),ce(this,"verify"),ce(this,"echoClient"),ce(this,"linkModeSupportedApps"),ce(this,"eventClient"),ce(this,"initialized",!1),ce(this,"logChunkController"),ce(this,"on",(a,c)=>this.events.on(a,c)),ce(this,"once",(a,c)=>this.events.once(a,c)),ce(this,"off",(a,c)=>this.events.off(a,c)),ce(this,"removeListener",(a,c)=>this.events.removeListener(a,c)),ce(this,"dispatchEnvelope",({topic:a,message:c,sessionExists:u})=>{if(!a||!c)return;const l={topic:a,message:c,publishedAt:Date.now(),transportType:ue.link_mode};this.relayer.onLinkMessageEvent(l,{sessionExists:u})});const n=this.getGlobalCore(e?.customStoragePrefix);if(n)try{return this.customStoragePrefix=n.customStoragePrefix,this.logger=n.logger,this.heartbeat=n.heartbeat,this.crypto=n.crypto,this.history=n.history,this.expirer=n.expirer,this.storage=n.storage,this.relayer=n.relayer,this.pairing=n.pairing,this.verify=n.verify,this.echoClient=n.echoClient,this.linkModeSupportedApps=n.linkModeSupportedApps,this.eventClient=n.eventClient,this.initialized=n.initialized,this.logChunkController=n.logChunkController,n}catch(a){console.warn("Failed to copy global core",a)}this.projectId=e?.projectId,this.relayUrl=e?.relayUrl||Qd,this.customStoragePrefix=e!=null&&e.customStoragePrefix?`:${e.customStoragePrefix}`:"";const i=Ps({level:typeof e?.logger=="string"&&e.logger?e.logger:HI.logger,name:us}),{logger:s,chunkLoggerController:o}=zg({opts:i,maxSizeInBytes:e?.maxLogBlobSizeInBytes,loggerOverride:e?.logger});this.logChunkController=o,(r=this.logChunkController)!=null&&r.downloadLogsBlobInBrowser&&(window.downloadLogsBlobInBrowser=async()=>{var a,c;(a=this.logChunkController)!=null&&a.downloadLogsBlobInBrowser&&((c=this.logChunkController)==null||c.downloadLogsBlobInBrowser({clientId:await this.crypto.getClientId()}))}),this.logger=je(s,this.name),this.heartbeat=new Is,this.crypto=new J3(this,this.logger,e?.keychain),this.history=new cD(this,this.logger),this.expirer=new hD(this,this.logger),this.storage=e!=null&&e.storage?e.storage:new gg(Rf(Rf({},WI),e?.storageOptions)),this.relayer=new xO({core:this,logger:this.logger,relayUrl:this.relayUrl,projectId:this.projectId}),this.pairing=new sD(this,this.logger),this.verify=new pD(this,this.logger,this.storage),this.echoClient=new wD(this.projectId||"",this.logger),this.linkModeSupportedApps=[],this.eventClient=new ED(this,this.logger,e?.telemetryEnabled),this.setGlobalCore(this)}static async init(e){const r=new Fa(e);await r.initialize();const n=await r.crypto.getClientId();return await r.storage.setItem(ox,n),r}get context(){return We(this.logger)}async start(){this.initialized||await this.initialize()}async getLogsBlob(){var e;return(e=this.logChunkController)==null?void 0:e.logsToBlob({clientId:await this.crypto.getClientId()})}async addLinkModeSupportedApp(e){this.linkModeSupportedApps.includes(e)||(this.linkModeSupportedApps.push(e),await this.storage.setItem(ef,this.linkModeSupportedApps))}async initialize(){this.logger.trace("Initialized");try{await this.crypto.init(),await this.history.init(),await this.expirer.init(),await this.relayer.init(),await this.heartbeat.init(),await this.pairing.init(),this.linkModeSupportedApps=await this.storage.getItem(ef)||[],this.initialized=!0,this.logger.info("Core Initialization Success")}catch(e){throw this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`,e),this.logger.error(e.message),e}}getGlobalCore(e=""){try{if(this.isGlobalCoreDisabled())return;const r=`_walletConnectCore_${e}`,n=`${r}_count`;return globalThis[n]=(globalThis[n]||0)+1,globalThis[n]>1&&console.warn(`WalletConnect Core is already initialized. This is probably a mistake and can lead to unexpected behavior. Init() was called ${globalThis[n]} times.`),globalThis[r]}catch(r){console.warn("Failed to get global WalletConnect core",r);return}}setGlobalCore(e){var r;try{if(this.isGlobalCoreDisabled())return;const n=`_walletConnectCore_${((r=e.opts)==null?void 0:r.customStoragePrefix)||""}`;globalThis[n]=e}catch(n){console.warn("Failed to set global WalletConnect core",n)}}isGlobalCoreDisabled(){try{return typeof process<"u"&&process.env.DISABLE_GLOBAL_CORE==="true"}catch{return!0}}}const xD=Fa,La="wc",Ua=2,ka="client",fs=`${La}@${Ua}:${ka}:`,ps={name:ka,logger:"error",controller:!1,relayUrl:"wss://relay.walletconnect.org"},OD={session_proposal:"session_proposal",session_update:"session_update",session_extend:"session_extend",session_ping:"session_ping",session_delete:"session_delete",session_expire:"session_expire",session_request:"session_request",session_request_sent:"session_request_sent",session_event:"session_event",proposal_expire:"proposal_expire",session_authenticate:"session_authenticate",session_request_expire:"session_request_expire",session_connect:"session_connect"},DD={database:":memory:"},ja="WALLETCONNECT_DEEPLINK_CHOICE",AD={created:"history_created",updated:"history_updated",deleted:"history_deleted",sync:"history_sync"},$D="history",TD="0.3",Cf="proposal",PD=F.THIRTY_DAYS,Ma="Proposal expired",Bf="session",Fr=F.SEVEN_DAYS,Ff="engine",Ee={wc_sessionPropose:{req:{ttl:F.FIVE_MINUTES,prompt:!0,tag:1100},res:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1101},reject:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1120},autoReject:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1121}},wc_sessionSettle:{req:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1102},res:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1103}},wc_sessionUpdate:{req:{ttl:F.ONE_DAY,prompt:!1,tag:1104},res:{ttl:F.ONE_DAY,prompt:!1,tag:1105}},wc_sessionExtend:{req:{ttl:F.ONE_DAY,prompt:!1,tag:1106},res:{ttl:F.ONE_DAY,prompt:!1,tag:1107}},wc_sessionRequest:{req:{ttl:F.FIVE_MINUTES,prompt:!0,tag:1108},res:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1109}},wc_sessionEvent:{req:{ttl:F.FIVE_MINUTES,prompt:!0,tag:1110},res:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1111}},wc_sessionDelete:{req:{ttl:F.ONE_DAY,prompt:!1,tag:1112},res:{ttl:F.ONE_DAY,prompt:!1,tag:1113}},wc_sessionPing:{req:{ttl:F.ONE_DAY,prompt:!1,tag:1114},res:{ttl:F.ONE_DAY,prompt:!1,tag:1115}},wc_sessionAuthenticate:{req:{ttl:F.ONE_HOUR,prompt:!0,tag:1116},res:{ttl:F.ONE_HOUR,prompt:!1,tag:1117},reject:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1118},autoReject:{ttl:F.FIVE_MINUTES,prompt:!1,tag:1119}}},gs={min:F.FIVE_MINUTES,max:F.SEVEN_DAYS},wt={idle:"IDLE",active:"ACTIVE"},qa={eth_sendTransaction:{key:""},eth_sendRawTransaction:{key:""},wallet_sendCalls:{key:""},solana_signTransaction:{key:"signature"},solana_signAllTransactions:{key:"transactions"},solana_signAndSendTransaction:{key:"signature"}},Lf="request",Uf=["wc_sessionPropose","wc_sessionRequest","wc_authRequest","wc_sessionAuthenticate"],kf="wc",ND=1.5,jf="auth",Mf="authKeys",qf="pairingTopics",zf="requests",li=`${kf}@${1.5}:${jf}:`,hi=`${li}:PUB_KEY`;var RD=Object.defineProperty,CD=Object.defineProperties,BD=Object.getOwnPropertyDescriptors,Vf=Object.getOwnPropertySymbols,FD=Object.prototype.hasOwnProperty,LD=Object.prototype.propertyIsEnumerable,za=(t,e,r)=>e in t?RD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,se=(t,e)=>{for(var r in e||(e={}))FD.call(e,r)&&za(t,r,e[r]);if(Vf)for(var r of Vf(e))LD.call(e,r)&&za(t,r,e[r]);return t},Ve=(t,e)=>CD(t,BD(e)),N=(t,e,r)=>za(t,typeof e!="symbol"?e+"":e,r);class UD extends uy{constructor(e){super(e),N(this,"name",Ff),N(this,"events",new Ke.exports),N(this,"initialized",!1),N(this,"requestQueue",{state:wt.idle,queue:[]}),N(this,"sessionRequestQueue",{state:wt.idle,queue:[]}),N(this,"requestQueueDelay",F.ONE_SECOND),N(this,"expectedPairingMethodMap",new Map),N(this,"recentlyDeletedMap",new Map),N(this,"recentlyDeletedLimit",200),N(this,"relayMessageCache",[]),N(this,"pendingSessions",new Map),N(this,"init",async()=>{this.initialized||(await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.registerPairingEvents(),await this.registerLinkModeListeners(),this.client.core.pairing.register({methods:Object.keys(Ee)}),this.initialized=!0,setTimeout(async()=>{await this.processPendingMessageEvents(),this.sessionRequestQueue.queue=this.getPendingSessionRequests(),this.processSessionRequestQueue()},F.toMiliseconds(this.requestQueueDelay)))}),N(this,"connect",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();const n=Ve(se({},r),{requiredNamespaces:r.requiredNamespaces||{},optionalNamespaces:r.optionalNamespaces||{}});await this.isValidConnect(n),n.optionalNamespaces=vS(n.requiredNamespaces,n.optionalNamespaces),n.requiredNamespaces={};const{pairingTopic:i,requiredNamespaces:s,optionalNamespaces:o,sessionProperties:a,scopedProperties:c,relays:u}=n;let l=i,h,d=!1;try{if(l){const O=this.client.core.pairing.pairings.get(l);this.client.logger.warn("connect() with existing pairing topic is deprecated and will be removed in the next major release."),d=O.active}}catch(O){throw this.client.logger.error(`connect() -> pairing.get(${l}) failed`),O}if(!l||!d){const{topic:O,uri:S}=await this.client.core.pairing.create();l=O,h=S}if(!l){const{message:O}=L("NO_MATCHING_KEY",`connect() pairing topic: ${l}`);throw new Error(O)}const p=await this.client.core.crypto.generateKeyPair(),f=Ee.wc_sessionPropose.req.ttl||F.FIVE_MINUTES,y=ve(f),g=Ve(se(se({requiredNamespaces:s,optionalNamespaces:o,relays:u??[{protocol:Jd}],proposer:{publicKey:p,metadata:this.client.metadata},expiryTimestamp:y,pairingTopic:l},a&&{sessionProperties:a}),c&&{scopedProperties:c}),{id:Ot()}),b=te("session_connect",g.id),{reject:w,resolve:m,done:E}=Sr(f,Ma),$=({id:O})=>{O===g.id&&(this.client.events.off("proposal_expire",$),this.pendingSessions.delete(g.id),this.events.emit(b,{error:{message:Ma,code:0}}))};return this.client.events.on("proposal_expire",$),this.events.once(b,({error:O,session:S})=>{this.client.events.off("proposal_expire",$),O?w(O):S&&m(S)}),await this.sendRequest({topic:l,method:"wc_sessionPropose",params:g,throwOnFailedPublish:!0,clientRpcId:g.id}),await this.setProposal(g.id,g),{uri:h,approval:E}}),N(this,"pair",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{return await this.client.core.pairing.pair(r)}catch(n){throw this.client.logger.error("pair() failed"),n}}),N(this,"approve",async r=>{var n,i,s;const o=this.client.core.eventClient.createEvent({properties:{topic:(n=r?.id)==null?void 0:n.toString(),trace:[gt.session_approve_started]}});try{this.isInitialized(),await this.confirmOnlineStateOrThrow()}catch(I){throw o.setError(Rr.no_internet_connection),I}try{await this.isValidProposalId(r?.id)}catch(I){throw this.client.logger.error(`approve() -> proposal.get(${r?.id}) failed`),o.setError(Rr.proposal_not_found),I}try{await this.isValidApprove(r)}catch(I){throw this.client.logger.error("approve() -> isValidApprove() failed"),o.setError(Rr.session_approve_namespace_validation_failure),I}const{id:a,relayProtocol:c,namespaces:u,sessionProperties:l,scopedProperties:h,sessionConfig:d}=r,p=this.client.proposal.get(a);this.client.core.eventClient.deleteEvent({eventId:o.eventId});const{pairingTopic:f,proposer:y,requiredNamespaces:g,optionalNamespaces:b}=p;let w=(i=this.client.core.eventClient)==null?void 0:i.getEvent({topic:f});w||(w=(s=this.client.core.eventClient)==null?void 0:s.createEvent({type:gt.session_approve_started,properties:{topic:f,trace:[gt.session_approve_started,gt.session_namespaces_validation_success]}}));const m=await this.client.core.crypto.generateKeyPair(),E=y.publicKey,$=await this.client.core.crypto.generateSharedKey(m,E),O=se(se(se({relay:{protocol:c??"irn"},namespaces:u,controller:{publicKey:m,metadata:this.client.metadata},expiry:ve(Fr)},l&&{sessionProperties:l}),h&&{scopedProperties:h}),d&&{sessionConfig:d}),S=ue.relay;w.addTrace(gt.subscribing_session_topic);try{await this.client.core.relayer.subscribe($,{transportType:S})}catch(I){throw w.setError(Rr.subscribe_session_topic_failure),I}w.addTrace(gt.subscribe_session_topic_success);const T=Ve(se({},O),{topic:$,requiredNamespaces:g,optionalNamespaces:b,pairingTopic:f,acknowledged:!1,self:O.controller,peer:{publicKey:y.publicKey,metadata:y.metadata},controller:m,transportType:ue.relay});await this.client.session.set($,T),w.addTrace(gt.store_session);try{w.addTrace(gt.publishing_session_settle),await this.sendRequest({topic:$,method:"wc_sessionSettle",params:O,throwOnFailedPublish:!0}).catch(I=>{throw w?.setError(Rr.session_settle_publish_failure),I}),w.addTrace(gt.session_settle_publish_success),w.addTrace(gt.publishing_session_approve),await this.sendResult({id:a,topic:f,result:{relay:{protocol:c??"irn"},responderPublicKey:m},throwOnFailedPublish:!0}).catch(I=>{throw w?.setError(Rr.session_approve_publish_failure),I}),w.addTrace(gt.session_approve_publish_success)}catch(I){throw this.client.logger.error(I),this.client.session.delete($,ae("USER_DISCONNECTED")),await this.client.core.relayer.unsubscribe($),I}return this.client.core.eventClient.deleteEvent({eventId:w.eventId}),await this.client.core.pairing.updateMetadata({topic:f,metadata:y.metadata}),await this.client.proposal.delete(a,ae("USER_DISCONNECTED")),await this.client.core.pairing.activate({topic:f}),await this.setExpiry($,ve(Fr)),{topic:$,acknowledged:()=>Promise.resolve(this.client.session.get($))}}),N(this,"reject",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidReject(r)}catch(o){throw this.client.logger.error("reject() -> isValidReject() failed"),o}const{id:n,reason:i}=r;let s;try{s=this.client.proposal.get(n).pairingTopic}catch(o){throw this.client.logger.error(`reject() -> proposal.get(${n}) failed`),o}s&&(await this.sendError({id:n,topic:s,error:i,rpcOpts:Ee.wc_sessionPropose.reject}),await this.client.proposal.delete(n,ae("USER_DISCONNECTED")))}),N(this,"update",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidUpdate(r)}catch(h){throw this.client.logger.error("update() -> isValidUpdate() failed"),h}const{topic:n,namespaces:i}=r,{done:s,resolve:o,reject:a}=Sr(),c=Ot(),u=Tr().toString(),l=this.client.session.get(n).namespaces;return this.events.once(te("session_update",c),({error:h})=>{h?a(h):o()}),await this.client.session.update(n,{namespaces:i}),await this.sendRequest({topic:n,method:"wc_sessionUpdate",params:{namespaces:i},throwOnFailedPublish:!0,clientRpcId:c,relayRpcId:u}).catch(h=>{this.client.logger.error(h),this.client.session.update(n,{namespaces:l}),a(h)}),{acknowledged:s}}),N(this,"extend",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidExtend(r)}catch(c){throw this.client.logger.error("extend() -> isValidExtend() failed"),c}const{topic:n}=r,i=Ot(),{done:s,resolve:o,reject:a}=Sr();return this.events.once(te("session_extend",i),({error:c})=>{c?a(c):o()}),await this.setExpiry(n,ve(Fr)),this.sendRequest({topic:n,method:"wc_sessionExtend",params:{},clientRpcId:i,throwOnFailedPublish:!0}).catch(c=>{a(c)}),{acknowledged:s}}),N(this,"request",async r=>{this.isInitialized();try{await this.isValidRequest(r)}catch(b){throw this.client.logger.error("request() -> isValidRequest() failed"),b}const{chainId:n,request:i,topic:s,expiry:o=Ee.wc_sessionRequest.req.ttl}=r,a=this.client.session.get(s);a?.transportType===ue.relay&&await this.confirmOnlineStateOrThrow();const c=Ot(),u=Tr().toString(),{done:l,resolve:h,reject:d}=Sr(o,"Request expired. Please try again.");this.events.once(te("session_request",c),({error:b,result:w})=>{b?d(b):h(w)});const p="wc_sessionRequest",f=this.getAppLinkIfEnabled(a.peer.metadata,a.transportType);if(f)return await this.sendRequest({clientRpcId:c,relayRpcId:u,topic:s,method:p,params:{request:Ve(se({},i),{expiryTimestamp:ve(o)}),chainId:n},expiry:o,throwOnFailedPublish:!0,appLink:f}).catch(b=>d(b)),this.client.events.emit("session_request_sent",{topic:s,request:i,chainId:n,id:c}),await l();const y={request:Ve(se({},i),{expiryTimestamp:ve(o)}),chainId:n},g=this.shouldSetTVF(p,y);return await Promise.all([new Promise(async b=>{await this.sendRequest(se({clientRpcId:c,relayRpcId:u,topic:s,method:p,params:y,expiry:o,throwOnFailedPublish:!0},g&&{tvf:this.getTVFParams(c,y)})).catch(w=>d(w)),this.client.events.emit("session_request_sent",{topic:s,request:i,chainId:n,id:c}),b()}),new Promise(async b=>{var w;if(!((w=a.sessionConfig)!=null&&w.disableDeepLink)){const m=await CE(this.client.core.storage,ja);await PE({id:c,topic:s,wcDeepLink:m})}b()}),l()]).then(b=>b[2])}),N(this,"respond",async r=>{this.isInitialized(),await this.isValidRespond(r);const{topic:n,response:i}=r,{id:s}=i,o=this.client.session.get(n);o.transportType===ue.relay&&await this.confirmOnlineStateOrThrow();const a=this.getAppLinkIfEnabled(o.peer.metadata,o.transportType);Dt(i)?await this.sendResult({id:s,topic:n,result:i.result,throwOnFailedPublish:!0,appLink:a}):at(i)&&await this.sendError({id:s,topic:n,error:i.error,appLink:a}),this.cleanupAfterResponse(r)}),N(this,"ping",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidPing(r)}catch(i){throw this.client.logger.error("ping() -> isValidPing() failed"),i}const{topic:n}=r;if(this.client.session.keys.includes(n)){const i=Ot(),s=Tr().toString(),{done:o,resolve:a,reject:c}=Sr();this.events.once(te("session_ping",i),({error:u})=>{u?c(u):a()}),await Promise.all([this.sendRequest({topic:n,method:"wc_sessionPing",params:{},throwOnFailedPublish:!0,clientRpcId:i,relayRpcId:s}),o()])}else this.client.core.pairing.pairings.keys.includes(n)&&(this.client.logger.warn("ping() on pairing topic is deprecated and will be removed in the next major release."),await this.client.core.pairing.ping({topic:n}))}),N(this,"emit",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow(),await this.isValidEmit(r);const{topic:n,event:i,chainId:s}=r,o=Tr().toString(),a=Ot();await this.sendRequest({topic:n,method:"wc_sessionEvent",params:{event:i,chainId:s},throwOnFailedPublish:!0,relayRpcId:o,clientRpcId:a})}),N(this,"disconnect",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow(),await this.isValidDisconnect(r);const{topic:n}=r;if(this.client.session.keys.includes(n))await this.sendRequest({topic:n,method:"wc_sessionDelete",params:ae("USER_DISCONNECTED"),throwOnFailedPublish:!0}),await this.deleteSession({topic:n,emitEvent:!1});else if(this.client.core.pairing.pairings.keys.includes(n))await this.client.core.pairing.disconnect({topic:n});else{const{message:i}=L("MISMATCHED_TOPIC",`Session or pairing topic not found: ${n}`);throw new Error(i)}}),N(this,"find",r=>(this.isInitialized(),this.client.session.getAll().filter(n=>SS(n,r)))),N(this,"getPendingSessionRequests",()=>this.client.pendingRequest.getAll()),N(this,"authenticate",async(r,n)=>{var i;this.isInitialized(),this.isValidAuthenticate(r);const s=n&&this.client.core.linkModeSupportedApps.includes(n)&&((i=this.client.metadata.redirect)==null?void 0:i.linkMode),o=s?ue.link_mode:ue.relay;o===ue.relay&&await this.confirmOnlineStateOrThrow();const{chains:a,statement:c="",uri:u,domain:l,nonce:h,type:d,exp:p,nbf:f,methods:y=[],expiry:g}=r,b=[...r.resources||[]],{topic:w,uri:m}=await this.client.core.pairing.create({methods:["wc_sessionAuthenticate"],transportType:o});this.client.logger.info({message:"Generated new pairing",pairing:{topic:w,uri:m}});const E=await this.client.core.crypto.generateKeyPair(),$=is(E);if(await Promise.all([this.client.auth.authKeys.set(hi,{responseTopic:$,publicKey:E}),this.client.auth.pairingTopics.set($,{topic:$,pairingTopic:w})]),await this.client.core.relayer.subscribe($,{transportType:o}),this.client.logger.info(`sending request to new pairing topic: ${w}`),y.length>0){const{namespace:A}=Hi(a[0]);let C=x_(A,"request",y);Zi(b)&&(C=D_(C,b.pop())),b.push(C)}const O=g&&g>Ee.wc_sessionAuthenticate.req.ttl?g:Ee.wc_sessionAuthenticate.req.ttl,S={authPayload:{type:d??"caip122",chains:a,statement:c,aud:u,domain:l,version:"1",nonce:h,iat:new Date().toISOString(),exp:p,nbf:f,resources:b},requester:{publicKey:E,metadata:this.client.metadata},expiryTimestamp:ve(O)},T={eip155:{chains:a,methods:[...new Set(["personal_sign",...y])],events:["chainChanged","accountsChanged"]}},I={requiredNamespaces:{},optionalNamespaces:T,relays:[{protocol:"irn"}],pairingTopic:w,proposer:{publicKey:E,metadata:this.client.metadata},expiryTimestamp:ve(Ee.wc_sessionPropose.req.ttl),id:Ot()},{done:j,resolve:R,reject:B}=Sr(O,"Request expired"),M=Ot(),x=te("session_connect",I.id),_=te("session_request",M),v=async({error:A,session:C})=>{this.events.off(_,D),A?B(A):C&&R({session:C})},D=async A=>{var C,U,k;if(await this.deletePendingAuthRequest(M,{message:"fulfilled",code:0}),A.error){const G=ae("WC_METHOD_UNSUPPORTED","wc_sessionAuthenticate");return A.error.code===G.code?void 0:(this.events.off(x,v),B(A.error.message))}await this.deleteProposal(I.id),this.events.off(x,v);const{cacaos:q,responder:z}=A.result,V=[],K=[];for(const G of q){await Ah({cacao:G,projectId:this.client.core.projectId})||(this.client.logger.error(G,"Signature verification failed"),B(ae("SESSION_SETTLEMENT_FAILED","Signature verification failed")));const{p:ge}=G,le=Zi(ge.resources),he=[Vo(ge.iss)],Te=Yi(ge.iss);if(le){const be=Ph(le),Wt=Nh(le);V.push(...be),he.push(...Wt)}for(const be of he)K.push(`${be}:${Te}`)}const ee=await this.client.core.crypto.generateSharedKey(E,z.publicKey);let Y;V.length>0&&(Y={topic:ee,acknowledged:!0,self:{publicKey:E,metadata:this.client.metadata},peer:z,controller:z.publicKey,expiry:ve(Fr),requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:w,namespaces:Dd([...new Set(V)],[...new Set(K)]),transportType:o},await this.client.core.relayer.subscribe(ee,{transportType:o}),await this.client.session.set(ee,Y),w&&await this.client.core.pairing.updateMetadata({topic:w,metadata:z.metadata}),Y=this.client.session.get(ee)),(C=this.client.metadata.redirect)!=null&&C.linkMode&&(U=z.metadata.redirect)!=null&&U.linkMode&&(k=z.metadata.redirect)!=null&&k.universal&&n&&(this.client.core.addLinkModeSupportedApp(z.metadata.redirect.universal),this.client.session.update(ee,{transportType:ue.link_mode})),R({auths:q,session:Y})};this.events.once(x,v),this.events.once(_,D);let P;try{if(s){const A=Pr("wc_sessionAuthenticate",S,M);this.client.core.history.set(w,A);const C=await this.client.core.crypto.encode("",A,{type:Wn,encoding:ur});P=os(n,w,C)}else await Promise.all([this.sendRequest({topic:w,method:"wc_sessionAuthenticate",params:S,expiry:r.expiry,throwOnFailedPublish:!0,clientRpcId:M}),this.sendRequest({topic:w,method:"wc_sessionPropose",params:I,expiry:Ee.wc_sessionPropose.req.ttl,throwOnFailedPublish:!0,clientRpcId:I.id})])}catch(A){throw this.events.off(x,v),this.events.off(_,D),A}return await this.setProposal(I.id,I),await this.setAuthRequest(M,{request:Ve(se({},S),{verifyContext:{}}),pairingTopic:w,transportType:o}),{uri:P??m,response:j}}),N(this,"approveSessionAuthenticate",async r=>{const{id:n,auths:i}=r,s=this.client.core.eventClient.createEvent({properties:{topic:n.toString(),trace:[Cr.authenticated_session_approve_started]}});try{this.isInitialized()}catch(g){throw s.setError(ii.no_internet_connection),g}const o=this.getPendingAuthRequest(n);if(!o)throw s.setError(ii.authenticated_session_pending_request_not_found),new Error(`Could not find pending auth request with id ${n}`);const a=o.transportType||ue.relay;a===ue.relay&&await this.confirmOnlineStateOrThrow();const c=o.requester.publicKey,u=await this.client.core.crypto.generateKeyPair(),l=is(c),h={type:zt,receiverPublicKey:c,senderPublicKey:u},d=[],p=[];for(const g of i){if(!await Ah({cacao:g,projectId:this.client.core.projectId})){s.setError(ii.invalid_cacao);const $=ae("SESSION_SETTLEMENT_FAILED","Signature verification failed");throw await this.sendError({id:n,topic:l,error:$,encodeOpts:h}),new Error($.message)}s.addTrace(Cr.cacaos_verified);const{p:b}=g,w=Zi(b.resources),m=[Vo(b.iss)],E=Yi(b.iss);if(w){const $=Ph(w),O=Nh(w);d.push(...$),m.push(...O)}for(const $ of m)p.push(`${$}:${E}`)}const f=await this.client.core.crypto.generateSharedKey(u,c);s.addTrace(Cr.create_authenticated_session_topic);let y;if(d?.length>0){y={topic:f,acknowledged:!0,self:{publicKey:u,metadata:this.client.metadata},peer:{publicKey:c,metadata:o.requester.metadata},controller:c,expiry:ve(Fr),authentication:i,requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:o.pairingTopic,namespaces:Dd([...new Set(d)],[...new Set(p)]),transportType:a},s.addTrace(Cr.subscribing_authenticated_session_topic);try{await this.client.core.relayer.subscribe(f,{transportType:a})}catch(g){throw s.setError(ii.subscribe_authenticated_session_topic_failure),g}s.addTrace(Cr.subscribe_authenticated_session_topic_success),await this.client.session.set(f,y),s.addTrace(Cr.store_authenticated_session),await this.client.core.pairing.updateMetadata({topic:o.pairingTopic,metadata:o.requester.metadata})}s.addTrace(Cr.publishing_authenticated_session_approve);try{await this.sendResult({topic:l,id:n,result:{cacaos:i,responder:{publicKey:u,metadata:this.client.metadata}},encodeOpts:h,throwOnFailedPublish:!0,appLink:this.getAppLinkIfEnabled(o.requester.metadata,a)})}catch(g){throw s.setError(ii.authenticated_session_approve_publish_failure),g}return await this.client.auth.requests.delete(n,{message:"fulfilled",code:0}),await this.client.core.pairing.activate({topic:o.pairingTopic}),this.client.core.eventClient.deleteEvent({eventId:s.eventId}),{session:y}}),N(this,"rejectSessionAuthenticate",async r=>{this.isInitialized();const{id:n,reason:i}=r,s=this.getPendingAuthRequest(n);if(!s)throw new Error(`Could not find pending auth request with id ${n}`);s.transportType===ue.relay&&await this.confirmOnlineStateOrThrow();const o=s.requester.publicKey,a=await this.client.core.crypto.generateKeyPair(),c=is(o),u={type:zt,receiverPublicKey:o,senderPublicKey:a};await this.sendError({id:n,topic:c,error:i,encodeOpts:u,rpcOpts:Ee.wc_sessionAuthenticate.reject,appLink:this.getAppLinkIfEnabled(s.requester.metadata,s.transportType)}),await this.client.auth.requests.delete(n,{message:"rejected",code:0}),await this.client.proposal.delete(n,ae("USER_DISCONNECTED"))}),N(this,"formatAuthMessage",r=>{this.isInitialized();const{request:n,iss:i}=r;return $h(n,i)}),N(this,"processRelayMessageCache",()=>{setTimeout(async()=>{if(this.relayMessageCache.length!==0)for(;this.relayMessageCache.length>0;)try{const r=this.relayMessageCache.shift();r&&await this.onRelayMessage(r)}catch(r){this.client.logger.error(r)}},50)}),N(this,"cleanupDuplicatePairings",async r=>{if(r.pairingTopic)try{const n=this.client.core.pairing.pairings.get(r.pairingTopic),i=this.client.core.pairing.pairings.getAll().filter(s=>{var o,a;return((o=s.peerMetadata)==null?void 0:o.url)&&((a=s.peerMetadata)==null?void 0:a.url)===r.peer.metadata.url&&s.topic&&s.topic!==n.topic});if(i.length===0)return;this.client.logger.info(`Cleaning up ${i.length} duplicate pairing(s)`),await Promise.all(i.map(s=>this.client.core.pairing.disconnect({topic:s.topic}))),this.client.logger.info("Duplicate pairings clean up finished")}catch(n){this.client.logger.error(n)}}),N(this,"deleteSession",async r=>{var n;const{topic:i,expirerHasDeleted:s=!1,emitEvent:o=!0,id:a=0}=r,{self:c}=this.client.session.get(i);await this.client.core.relayer.unsubscribe(i),await this.client.session.delete(i,ae("USER_DISCONNECTED")),this.addToRecentlyDeleted(i,"session"),this.client.core.crypto.keychain.has(c.publicKey)&&await this.client.core.crypto.deleteKeyPair(c.publicKey),this.client.core.crypto.keychain.has(i)&&await this.client.core.crypto.deleteSymKey(i),s||this.client.core.expirer.del(i),this.client.core.storage.removeItem(ja).catch(u=>this.client.logger.warn(u)),this.getPendingSessionRequests().forEach(u=>{u.topic===i&&this.deletePendingSessionRequest(u.id,ae("USER_DISCONNECTED"))}),i===((n=this.sessionRequestQueue.queue[0])==null?void 0:n.topic)&&(this.sessionRequestQueue.state=wt.idle),o&&this.client.events.emit("session_delete",{id:a,topic:i})}),N(this,"deleteProposal",async(r,n)=>{if(n)try{const i=this.client.proposal.get(r),s=this.client.core.eventClient.getEvent({topic:i.pairingTopic});s?.setError(Rr.proposal_expired)}catch{}await Promise.all([this.client.proposal.delete(r,ae("USER_DISCONNECTED")),n?Promise.resolve():this.client.core.expirer.del(r)]),this.addToRecentlyDeleted(r,"proposal")}),N(this,"deletePendingSessionRequest",async(r,n,i=!1)=>{await Promise.all([this.client.pendingRequest.delete(r,n),i?Promise.resolve():this.client.core.expirer.del(r)]),this.addToRecentlyDeleted(r,"request"),this.sessionRequestQueue.queue=this.sessionRequestQueue.queue.filter(s=>s.id!==r),i&&(this.sessionRequestQueue.state=wt.idle,this.client.events.emit("session_request_expire",{id:r}))}),N(this,"deletePendingAuthRequest",async(r,n,i=!1)=>{await Promise.all([this.client.auth.requests.delete(r,n),i?Promise.resolve():this.client.core.expirer.del(r)])}),N(this,"setExpiry",async(r,n)=>{this.client.session.keys.includes(r)&&(this.client.core.expirer.set(r,n),await this.client.session.update(r,{expiry:n}))}),N(this,"setProposal",async(r,n)=>{this.client.core.expirer.set(r,ve(Ee.wc_sessionPropose.req.ttl)),await this.client.proposal.set(r,n)}),N(this,"setAuthRequest",async(r,n)=>{const{request:i,pairingTopic:s,transportType:o=ue.relay}=n;this.client.core.expirer.set(r,i.expiryTimestamp),await this.client.auth.requests.set(r,{authPayload:i.authPayload,requester:i.requester,expiryTimestamp:i.expiryTimestamp,id:r,pairingTopic:s,verifyContext:i.verifyContext,transportType:o})}),N(this,"setPendingSessionRequest",async r=>{const{id:n,topic:i,params:s,verifyContext:o}=r,a=s.request.expiryTimestamp||ve(Ee.wc_sessionRequest.req.ttl);this.client.core.expirer.set(n,a),await this.client.pendingRequest.set(n,{id:n,topic:i,params:s,verifyContext:o})}),N(this,"sendRequest",async r=>{const{topic:n,method:i,params:s,expiry:o,relayRpcId:a,clientRpcId:c,throwOnFailedPublish:u,appLink:l,tvf:h}=r,d=Pr(i,s,c);let p;const f=!!l;try{const b=f?ur:ft;p=await this.client.core.crypto.encode(n,d,{encoding:b})}catch(b){throw await this.cleanup(),this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${n} failed`),b}let y;if(Uf.includes(i)){const b=xt(JSON.stringify(d)),w=xt(p);y=await this.client.core.verify.register({id:w,decryptedId:b})}const g=Ee[i].req;if(g.attestation=y,o&&(g.ttl=o),a&&(g.id=a),this.client.core.history.set(n,d),f){const b=os(l,n,p);await global.Linking.openURL(b,this.client.name)}else{const b=Ee[i].req;o&&(b.ttl=o),a&&(b.id=a),b.tvf=Ve(se({},h),{correlationId:d.id}),u?(b.internal=Ve(se({},b.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(n,p,b)):this.client.core.relayer.publish(n,p,b).catch(w=>this.client.logger.error(w))}return d.id}),N(this,"sendResult",async r=>{const{id:n,topic:i,result:s,throwOnFailedPublish:o,encodeOpts:a,appLink:c}=r,u=ma(n,s);let l;const h=c&&typeof(global==null?void 0:global.Linking)<"u";try{const f=h?ur:ft;l=await this.client.core.crypto.encode(i,u,Ve(se({},a||{}),{encoding:f}))}catch(f){throw await this.cleanup(),this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${i} failed`),f}let d,p;try{d=await this.client.core.history.get(i,n);const f=d.request;try{this.shouldSetTVF(f.method,f.params)&&(p=this.getTVFParams(n,f.params,s))}catch(y){this.client.logger.warn("sendResult() -> getTVFParams() failed",y)}}catch(f){throw this.client.logger.error(`sendResult() -> history.get(${i}, ${n}) failed`),f}if(h){const f=os(c,i,l);await global.Linking.openURL(f,this.client.name)}else{const f=d.request.method,y=Ee[f].res;y.tvf=Ve(se({},p),{correlationId:n}),o?(y.internal=Ve(se({},y.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(i,l,y)):this.client.core.relayer.publish(i,l,y).catch(g=>this.client.logger.error(g))}await this.client.core.history.resolve(u)}),N(this,"sendError",async r=>{const{id:n,topic:i,error:s,encodeOpts:o,rpcOpts:a,appLink:c}=r,u=va(n,s);let l;const h=c&&typeof(global==null?void 0:global.Linking)<"u";try{const p=h?ur:ft;l=await this.client.core.crypto.encode(i,u,Ve(se({},o||{}),{encoding:p}))}catch(p){throw await this.cleanup(),this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${i} failed`),p}let d;try{d=await this.client.core.history.get(i,n)}catch(p){throw this.client.logger.error(`sendError() -> history.get(${i}, ${n}) failed`),p}if(h){const p=os(c,i,l);await global.Linking.openURL(p,this.client.name)}else{const p=d.request.method,f=a||Ee[p].res;this.client.core.relayer.publish(i,l,f)}await this.client.core.history.resolve(u)}),N(this,"cleanup",async()=>{const r=[],n=[];this.client.session.getAll().forEach(i=>{let s=!1;sr(i.expiry)&&(s=!0),this.client.core.crypto.keychain.has(i.topic)||(s=!0),s&&r.push(i.topic)}),this.client.proposal.getAll().forEach(i=>{sr(i.expiryTimestamp)&&n.push(i.id)}),await Promise.all([...r.map(i=>this.deleteSession({topic:i})),...n.map(i=>this.deleteProposal(i))])}),N(this,"onProviderMessageEvent",async r=>{!this.initialized||this.relayMessageCache.length>0?this.relayMessageCache.push(r):await this.onRelayMessage(r)}),N(this,"onRelayEventRequest",async r=>{this.requestQueue.queue.push(r),await this.processRequestsQueue()}),N(this,"processRequestsQueue",async()=>{if(this.requestQueue.state===wt.active){this.client.logger.info("Request queue already active, skipping...");return}for(this.client.logger.info(`Request queue starting with ${this.requestQueue.queue.length} requests`);this.requestQueue.queue.length>0;){this.requestQueue.state=wt.active;const r=this.requestQueue.queue.shift();if(r)try{await this.processRequest(r)}catch(n){this.client.logger.warn(n)}}this.requestQueue.state=wt.idle}),N(this,"processRequest",async r=>{const{topic:n,payload:i,attestation:s,transportType:o,encryptedId:a}=r,c=i.method;if(!this.shouldIgnorePairingRequest({topic:n,requestMethod:c}))switch(c){case"wc_sessionPropose":return await this.onSessionProposeRequest({topic:n,payload:i,attestation:s,encryptedId:a});case"wc_sessionSettle":return await this.onSessionSettleRequest(n,i);case"wc_sessionUpdate":return await this.onSessionUpdateRequest(n,i);case"wc_sessionExtend":return await this.onSessionExtendRequest(n,i);case"wc_sessionPing":return await this.onSessionPingRequest(n,i);case"wc_sessionDelete":return await this.onSessionDeleteRequest(n,i);case"wc_sessionRequest":return await this.onSessionRequest({topic:n,payload:i,attestation:s,encryptedId:a,transportType:o});case"wc_sessionEvent":return await this.onSessionEventRequest(n,i);case"wc_sessionAuthenticate":return await this.onSessionAuthenticateRequest({topic:n,payload:i,attestation:s,encryptedId:a,transportType:o});default:return this.client.logger.info(`Unsupported request method ${c}`)}}),N(this,"onRelayEventResponse",async r=>{const{topic:n,payload:i,transportType:s}=r,o=(await this.client.core.history.get(n,i.id)).request.method;switch(o){case"wc_sessionPropose":return this.onSessionProposeResponse(n,i,s);case"wc_sessionSettle":return this.onSessionSettleResponse(n,i);case"wc_sessionUpdate":return this.onSessionUpdateResponse(n,i);case"wc_sessionExtend":return this.onSessionExtendResponse(n,i);case"wc_sessionPing":return this.onSessionPingResponse(n,i);case"wc_sessionRequest":return this.onSessionRequestResponse(n,i);case"wc_sessionAuthenticate":return this.onSessionAuthenticateResponse(n,i);default:return this.client.logger.info(`Unsupported response method ${o}`)}}),N(this,"onRelayEventUnknownPayload",r=>{const{topic:n}=r,{message:i}=L("MISSING_OR_INVALID",`Decoded payload on topic ${n} is not identifiable as a JSON-RPC request or a response.`);throw new Error(i)}),N(this,"shouldIgnorePairingRequest",r=>{const{topic:n,requestMethod:i}=r,s=this.expectedPairingMethodMap.get(n);return!s||s.includes(i)?!1:!!(s.includes("wc_sessionAuthenticate")&&this.client.events.listenerCount("session_authenticate")>0)}),N(this,"onSessionProposeRequest",async r=>{const{topic:n,payload:i,attestation:s,encryptedId:o}=r,{params:a,id:c}=i;try{const u=this.client.core.eventClient.getEvent({topic:n});this.client.events.listenerCount("session_proposal")===0&&(console.warn("No listener for session_proposal event"),u?.setError(Ht.proposal_listener_not_found)),this.isValidConnect(se({},i.params));const l=a.expiryTimestamp||ve(Ee.wc_sessionPropose.req.ttl),h=se({id:c,pairingTopic:n,expiryTimestamp:l},a);await this.setProposal(c,h);const d=await this.getVerifyContext({attestationId:s,hash:xt(JSON.stringify(i)),encryptedId:o,metadata:h.proposer.metadata});u?.addTrace($t.emit_session_proposal),this.client.events.emit("session_proposal",{id:c,params:h,verifyContext:d})}catch(u){await this.sendError({id:c,topic:n,error:u,rpcOpts:Ee.wc_sessionPropose.autoReject}),this.client.logger.error(u)}}),N(this,"onSessionProposeResponse",async(r,n,i)=>{const{id:s}=n;if(Dt(n)){const{result:o}=n;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",result:o});const a=this.client.proposal.get(s);this.client.logger.trace({type:"method",method:"onSessionProposeResponse",proposal:a});const c=a.proposer.publicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",selfPublicKey:c});const u=o.responderPublicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",peerPublicKey:u});const l=await this.client.core.crypto.generateSharedKey(c,u);this.pendingSessions.set(s,{sessionTopic:l,pairingTopic:r,proposalId:s,publicKey:c});const h=await this.client.core.relayer.subscribe(l,{transportType:i});this.client.logger.trace({type:"method",method:"onSessionProposeResponse",subscriptionId:h}),await this.client.core.pairing.activate({topic:r})}else if(at(n)){await this.client.proposal.delete(s,ae("USER_DISCONNECTED"));const o=te("session_connect",s);if(this.events.listenerCount(o)===0)throw new Error(`emitting ${o} without any listeners, 954`);this.events.emit(o,{error:n.error})}}),N(this,"onSessionSettleRequest",async(r,n)=>{const{id:i,params:s}=n;try{this.isValidSessionSettleRequest(s);const{relay:o,controller:a,expiry:c,namespaces:u,sessionProperties:l,scopedProperties:h,sessionConfig:d}=n.params,p=[...this.pendingSessions.values()].find(g=>g.sessionTopic===r);if(!p)return this.client.logger.error(`Pending session not found for topic ${r}`);const f=this.client.proposal.get(p.proposalId),y=Ve(se(se(se({topic:r,relay:o,expiry:c,namespaces:u,acknowledged:!0,pairingTopic:p.pairingTopic,requiredNamespaces:f.requiredNamespaces,optionalNamespaces:f.optionalNamespaces,controller:a.publicKey,self:{publicKey:p.publicKey,metadata:this.client.metadata},peer:{publicKey:a.publicKey,metadata:a.metadata}},l&&{sessionProperties:l}),h&&{scopedProperties:h}),d&&{sessionConfig:d}),{transportType:ue.relay});await this.client.session.set(y.topic,y),await this.setExpiry(y.topic,y.expiry),await this.client.core.pairing.updateMetadata({topic:p.pairingTopic,metadata:y.peer.metadata}),this.client.events.emit("session_connect",{session:y}),this.events.emit(te("session_connect",p.proposalId),{session:y}),this.pendingSessions.delete(p.proposalId),this.deleteProposal(p.proposalId,!1),this.cleanupDuplicatePairings(y),await this.sendResult({id:n.id,topic:r,result:!0,throwOnFailedPublish:!0})}catch(o){await this.sendError({id:i,topic:r,error:o}),this.client.logger.error(o)}}),N(this,"onSessionSettleResponse",async(r,n)=>{const{id:i}=n;Dt(n)?(await this.client.session.update(r,{acknowledged:!0}),this.events.emit(te("session_approve",i),{})):at(n)&&(await this.client.session.delete(r,ae("USER_DISCONNECTED")),this.events.emit(te("session_approve",i),{error:n.error}))}),N(this,"onSessionUpdateRequest",async(r,n)=>{const{params:i,id:s}=n;try{const o=`${r}_session_update`,a=Qn.get(o);if(a&&this.isRequestOutOfSync(a,s)){this.client.logger.warn(`Discarding out of sync request - ${s}`),this.sendError({id:s,topic:r,error:ae("INVALID_UPDATE_REQUEST")});return}this.isValidUpdate(se({topic:r},i));try{Qn.set(o,s),await this.client.session.update(r,{namespaces:i.namespaces}),await this.sendResult({id:s,topic:r,result:!0,throwOnFailedPublish:!0})}catch(c){throw Qn.delete(o),c}this.client.events.emit("session_update",{id:s,topic:r,params:i})}catch(o){await this.sendError({id:s,topic:r,error:o}),this.client.logger.error(o)}}),N(this,"isRequestOutOfSync",(r,n)=>n.toString().slice(0,-3)<r.toString().slice(0,-3)),N(this,"onSessionUpdateResponse",(r,n)=>{const{id:i}=n,s=te("session_update",i);if(this.events.listenerCount(s)===0)throw new Error(`emitting ${s} without any listeners`);Dt(n)?this.events.emit(te("session_update",i),{}):at(n)&&this.events.emit(te("session_update",i),{error:n.error})}),N(this,"onSessionExtendRequest",async(r,n)=>{const{id:i}=n;try{this.isValidExtend({topic:r}),await this.setExpiry(r,ve(Fr)),await this.sendResult({id:i,topic:r,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_extend",{id:i,topic:r})}catch(s){await this.sendError({id:i,topic:r,error:s}),this.client.logger.error(s)}}),N(this,"onSessionExtendResponse",(r,n)=>{const{id:i}=n,s=te("session_extend",i);if(this.events.listenerCount(s)===0)throw new Error(`emitting ${s} without any listeners`);Dt(n)?this.events.emit(te("session_extend",i),{}):at(n)&&this.events.emit(te("session_extend",i),{error:n.error})}),N(this,"onSessionPingRequest",async(r,n)=>{const{id:i}=n;try{this.isValidPing({topic:r}),await this.sendResult({id:i,topic:r,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_ping",{id:i,topic:r})}catch(s){await this.sendError({id:i,topic:r,error:s}),this.client.logger.error(s)}}),N(this,"onSessionPingResponse",(r,n)=>{const{id:i}=n,s=te("session_ping",i);setTimeout(()=>{if(this.events.listenerCount(s)===0)throw new Error(`emitting ${s} without any listeners 2176`);Dt(n)?this.events.emit(te("session_ping",i),{}):at(n)&&this.events.emit(te("session_ping",i),{error:n.error})},500)}),N(this,"onSessionDeleteRequest",async(r,n)=>{const{id:i}=n;try{this.isValidDisconnect({topic:r,reason:n.params}),Promise.all([new Promise(s=>{this.client.core.relayer.once(Ae.publish,async()=>{s(await this.deleteSession({topic:r,id:i}))})}),this.sendResult({id:i,topic:r,result:!0,throwOnFailedPublish:!0}),this.cleanupPendingSentRequestsForTopic({topic:r,error:ae("USER_DISCONNECTED")})]).catch(s=>this.client.logger.error(s))}catch(s){this.client.logger.error(s)}}),N(this,"onSessionRequest",async r=>{var n,i,s;const{topic:o,payload:a,attestation:c,encryptedId:u,transportType:l}=r,{id:h,params:d}=a;try{await this.isValidRequest(se({topic:o},d));const p=this.client.session.get(o),f=await this.getVerifyContext({attestationId:c,hash:xt(JSON.stringify(Pr("wc_sessionRequest",d,h))),encryptedId:u,metadata:p.peer.metadata,transportType:l}),y={id:h,topic:o,params:d,verifyContext:f};await this.setPendingSessionRequest(y),l===ue.link_mode&&(n=p.peer.metadata.redirect)!=null&&n.universal&&this.client.core.addLinkModeSupportedApp((i=p.peer.metadata.redirect)==null?void 0:i.universal),(s=this.client.signConfig)!=null&&s.disableRequestQueue?this.emitSessionRequest(y):(this.addSessionRequestToSessionRequestQueue(y),this.processSessionRequestQueue())}catch(p){await this.sendError({id:h,topic:o,error:p}),this.client.logger.error(p)}}),N(this,"onSessionRequestResponse",(r,n)=>{const{id:i}=n,s=te("session_request",i);if(this.events.listenerCount(s)===0)throw new Error(`emitting ${s} without any listeners`);Dt(n)?this.events.emit(te("session_request",i),{result:n.result}):at(n)&&this.events.emit(te("session_request",i),{error:n.error})}),N(this,"onSessionEventRequest",async(r,n)=>{const{id:i,params:s}=n;try{const o=`${r}_session_event_${s.event.name}`,a=Qn.get(o);if(a&&this.isRequestOutOfSync(a,i)){this.client.logger.info(`Discarding out of sync request - ${i}`);return}this.isValidEmit(se({topic:r},s)),this.client.events.emit("session_event",{id:i,topic:r,params:s}),Qn.set(o,i)}catch(o){await this.sendError({id:i,topic:r,error:o}),this.client.logger.error(o)}}),N(this,"onSessionAuthenticateResponse",(r,n)=>{const{id:i}=n;this.client.logger.trace({type:"method",method:"onSessionAuthenticateResponse",topic:r,payload:n}),Dt(n)?this.events.emit(te("session_request",i),{result:n.result}):at(n)&&this.events.emit(te("session_request",i),{error:n.error})}),N(this,"onSessionAuthenticateRequest",async r=>{var n;const{topic:i,payload:s,attestation:o,encryptedId:a,transportType:c}=r;try{const{requester:u,authPayload:l,expiryTimestamp:h}=s.params,d=await this.getVerifyContext({attestationId:o,hash:xt(JSON.stringify(s)),encryptedId:a,metadata:u.metadata,transportType:c}),p={requester:u,pairingTopic:i,id:s.id,authPayload:l,verifyContext:d,expiryTimestamp:h};await this.setAuthRequest(s.id,{request:p,pairingTopic:i,transportType:c}),c===ue.link_mode&&(n=u.metadata.redirect)!=null&&n.universal&&this.client.core.addLinkModeSupportedApp(u.metadata.redirect.universal),this.client.events.emit("session_authenticate",{topic:i,params:s.params,id:s.id,verifyContext:d})}catch(u){this.client.logger.error(u);const l=s.params.requester.publicKey,h=await this.client.core.crypto.generateKeyPair(),d=this.getAppLinkIfEnabled(s.params.requester.metadata,c),p={type:zt,receiverPublicKey:l,senderPublicKey:h};await this.sendError({id:s.id,topic:i,error:u,encodeOpts:p,rpcOpts:Ee.wc_sessionAuthenticate.autoReject,appLink:d})}}),N(this,"addSessionRequestToSessionRequestQueue",r=>{this.sessionRequestQueue.queue.push(r)}),N(this,"cleanupAfterResponse",r=>{this.deletePendingSessionRequest(r.response.id,{message:"fulfilled",code:0}),setTimeout(()=>{this.sessionRequestQueue.state=wt.idle,this.processSessionRequestQueue()},F.toMiliseconds(this.requestQueueDelay))}),N(this,"cleanupPendingSentRequestsForTopic",({topic:r,error:n})=>{const i=this.client.core.history.pending;i.length>0&&i.filter(s=>s.topic===r&&s.request.method==="wc_sessionRequest").forEach(s=>{const o=s.request.id,a=te("session_request",o);if(this.events.listenerCount(a)===0)throw new Error(`emitting ${a} without any listeners`);this.events.emit(te("session_request",s.request.id),{error:n})})}),N(this,"processSessionRequestQueue",()=>{if(this.sessionRequestQueue.state===wt.active){this.client.logger.info("session request queue is already active.");return}const r=this.sessionRequestQueue.queue[0];if(!r){this.client.logger.info("session request queue is empty.");return}try{this.sessionRequestQueue.state=wt.active,this.emitSessionRequest(r)}catch(n){this.client.logger.error(n)}}),N(this,"emitSessionRequest",r=>{this.client.events.emit("session_request",r)}),N(this,"onPairingCreated",r=>{if(r.methods&&this.expectedPairingMethodMap.set(r.topic,r.methods),r.active)return;const n=this.client.proposal.getAll().find(i=>i.pairingTopic===r.topic);n&&this.onSessionProposeRequest({topic:r.topic,payload:Pr("wc_sessionPropose",Ve(se({},n),{requiredNamespaces:n.requiredNamespaces,optionalNamespaces:n.optionalNamespaces,relays:n.relays,proposer:n.proposer,sessionProperties:n.sessionProperties,scopedProperties:n.scopedProperties}),n.id)})}),N(this,"isValidConnect",async r=>{if(!Xe(r)){const{message:u}=L("MISSING_OR_INVALID",`connect() params: ${JSON.stringify(r)}`);throw new Error(u)}const{pairingTopic:n,requiredNamespaces:i,optionalNamespaces:s,sessionProperties:o,scopedProperties:a,relays:c}=r;if(Ce(n)||await this.isValidPairingTopic(n),!BS(c,!0)){const{message:u}=L("MISSING_OR_INVALID",`connect() relays: ${c}`);throw new Error(u)}if(!Ce(i)&&Jn(i)!==0){const u="requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces";["fatal","error","silent"].includes(this.client.logger.level)?console.warn(u):this.client.logger.warn(u),this.validateNamespaces(i,"requiredNamespaces")}if(!Ce(s)&&Jn(s)!==0&&this.validateNamespaces(s,"optionalNamespaces"),Ce(o)||this.validateSessionProps(o,"sessionProperties"),!Ce(a)){this.validateSessionProps(a,"scopedProperties");const u=Object.keys(i||{}).concat(Object.keys(s||{}));if(!Object.keys(a).every(l=>u.includes(l)))throw new Error(`Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(a)}, required/optional namespaces: ${JSON.stringify(u)}`)}}),N(this,"validateNamespaces",(r,n)=>{const i=CS(r,"connect()",n);if(i)throw new Error(i.message)}),N(this,"isValidApprove",async r=>{if(!Xe(r))throw new Error(L("MISSING_OR_INVALID",`approve() params: ${r}`).message);const{id:n,namespaces:i,relayProtocol:s,sessionProperties:o,scopedProperties:a}=r;this.checkRecentlyDeleted(n),await this.isValidProposalId(n);const c=this.client.proposal.get(n),u=fa(i,"approve()");if(u)throw new Error(u.message);const l=Nd(c.requiredNamespaces,i,"approve()");if(l)throw new Error(l.message);if(!ye(s,!0)){const{message:h}=L("MISSING_OR_INVALID",`approve() relayProtocol: ${s}`);throw new Error(h)}if(Ce(o)||this.validateSessionProps(o,"sessionProperties"),!Ce(a)){this.validateSessionProps(a,"scopedProperties");const h=new Set(Object.keys(i));if(!Object.keys(a).every(d=>h.has(d)))throw new Error(`Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(a)}, approved namespaces: ${Array.from(h).join(", ")}`)}}),N(this,"isValidReject",async r=>{if(!Xe(r)){const{message:s}=L("MISSING_OR_INVALID",`reject() params: ${r}`);throw new Error(s)}const{id:n,reason:i}=r;if(this.checkRecentlyDeleted(n),await this.isValidProposalId(n),!LS(i)){const{message:s}=L("MISSING_OR_INVALID",`reject() reason: ${JSON.stringify(i)}`);throw new Error(s)}}),N(this,"isValidSessionSettleRequest",r=>{if(!Xe(r)){const{message:u}=L("MISSING_OR_INVALID",`onSessionSettleRequest() params: ${r}`);throw new Error(u)}const{relay:n,controller:i,namespaces:s,expiry:o}=r;if(!Td(n)){const{message:u}=L("MISSING_OR_INVALID","onSessionSettleRequest() relay protocol should be a string");throw new Error(u)}const a=AS(i,"onSessionSettleRequest()");if(a)throw new Error(a.message);const c=fa(s,"onSessionSettleRequest()");if(c)throw new Error(c.message);if(sr(o)){const{message:u}=L("EXPIRED","onSessionSettleRequest()");throw new Error(u)}}),N(this,"isValidUpdate",async r=>{if(!Xe(r)){const{message:c}=L("MISSING_OR_INVALID",`update() params: ${r}`);throw new Error(c)}const{topic:n,namespaces:i}=r;this.checkRecentlyDeleted(n),await this.isValidSessionTopic(n);const s=this.client.session.get(n),o=fa(i,"update()");if(o)throw new Error(o.message);const a=Nd(s.requiredNamespaces,i,"update()");if(a)throw new Error(a.message)}),N(this,"isValidExtend",async r=>{if(!Xe(r)){const{message:i}=L("MISSING_OR_INVALID",`extend() params: ${r}`);throw new Error(i)}const{topic:n}=r;this.checkRecentlyDeleted(n),await this.isValidSessionTopic(n)}),N(this,"isValidRequest",async r=>{if(!Xe(r)){const{message:c}=L("MISSING_OR_INVALID",`request() params: ${r}`);throw new Error(c)}const{topic:n,request:i,chainId:s,expiry:o}=r;this.checkRecentlyDeleted(n),await this.isValidSessionTopic(n);const{namespaces:a}=this.client.session.get(n);if(!Pd(a,s)){const{message:c}=L("MISSING_OR_INVALID",`request() chainId: ${s}`);throw new Error(c)}if(!US(i)){const{message:c}=L("MISSING_OR_INVALID",`request() ${JSON.stringify(i)}`);throw new Error(c)}if(!MS(a,s,i.method)){const{message:c}=L("MISSING_OR_INVALID",`request() method: ${i.method}`);throw new Error(c)}if(o&&!KS(o,gs)){const{message:c}=L("MISSING_OR_INVALID",`request() expiry: ${o}. Expiry must be a number (in seconds) between ${gs.min} and ${gs.max}`);throw new Error(c)}}),N(this,"isValidRespond",async r=>{var n;if(!Xe(r)){const{message:o}=L("MISSING_OR_INVALID",`respond() params: ${r}`);throw new Error(o)}const{topic:i,response:s}=r;try{await this.isValidSessionTopic(i)}catch(o){throw(n=r?.response)!=null&&n.id&&this.cleanupAfterResponse(r),o}if(!kS(s)){const{message:o}=L("MISSING_OR_INVALID",`respond() response: ${JSON.stringify(s)}`);throw new Error(o)}}),N(this,"isValidPing",async r=>{if(!Xe(r)){const{message:i}=L("MISSING_OR_INVALID",`ping() params: ${r}`);throw new Error(i)}const{topic:n}=r;await this.isValidSessionOrPairingTopic(n)}),N(this,"isValidEmit",async r=>{if(!Xe(r)){const{message:a}=L("MISSING_OR_INVALID",`emit() params: ${r}`);throw new Error(a)}const{topic:n,event:i,chainId:s}=r;await this.isValidSessionTopic(n);const{namespaces:o}=this.client.session.get(n);if(!Pd(o,s)){const{message:a}=L("MISSING_OR_INVALID",`emit() chainId: ${s}`);throw new Error(a)}if(!jS(i)){const{message:a}=L("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(i)}`);throw new Error(a)}if(!qS(o,s,i.name)){const{message:a}=L("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(i)}`);throw new Error(a)}}),N(this,"isValidDisconnect",async r=>{if(!Xe(r)){const{message:i}=L("MISSING_OR_INVALID",`disconnect() params: ${r}`);throw new Error(i)}const{topic:n}=r;await this.isValidSessionOrPairingTopic(n)}),N(this,"isValidAuthenticate",r=>{const{chains:n,uri:i,domain:s,nonce:o}=r;if(!Array.isArray(n)||n.length===0)throw new Error("chains is required and must be a non-empty array");if(!ye(i,!1))throw new Error("uri is required parameter");if(!ye(s,!1))throw new Error("domain is required parameter");if(!ye(o,!1))throw new Error("nonce is required parameter");if([...new Set(n.map(c=>Hi(c).namespace))].length>1)throw new Error("Multi-namespace requests are not supported. Please request single namespace only.");const{namespace:a}=Hi(n[0]);if(a!=="eip155")throw new Error("Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.")}),N(this,"getVerifyContext",async r=>{const{attestationId:n,hash:i,encryptedId:s,metadata:o,transportType:a}=r,c={verified:{verifyUrl:o.verifyUrl||ni,validation:"UNKNOWN",origin:o.url||""}};try{if(a===ue.link_mode){const l=this.getAppLinkIfEnabled(o,a);return c.verified.validation=l&&new URL(l).origin===new URL(o.url).origin?"VALID":"INVALID",c}const u=await this.client.core.verify.resolve({attestationId:n,hash:i,encryptedId:s,verifyUrl:o.verifyUrl});u&&(c.verified.origin=u.origin,c.verified.isScam=u.isScam,c.verified.validation=u.origin===new URL(o.url).origin?"VALID":"INVALID")}catch(u){this.client.logger.warn(u)}return this.client.logger.debug(`Verify context: ${JSON.stringify(c)}`),c}),N(this,"validateSessionProps",(r,n)=>{Object.values(r).forEach((i,s)=>{if(i==null){const{message:o}=L("MISSING_OR_INVALID",`${n} must contain an existing value for each key. Received: ${i} for key ${Object.keys(r)[s]}`);throw new Error(o)}})}),N(this,"getPendingAuthRequest",r=>{const n=this.client.auth.requests.get(r);return typeof n=="object"?n:void 0}),N(this,"addToRecentlyDeleted",(r,n)=>{if(this.recentlyDeletedMap.set(r,n),this.recentlyDeletedMap.size>=this.recentlyDeletedLimit){let i=0;const s=this.recentlyDeletedLimit/2;for(const o of this.recentlyDeletedMap.keys()){if(i++>=s)break;this.recentlyDeletedMap.delete(o)}}}),N(this,"checkRecentlyDeleted",r=>{const n=this.recentlyDeletedMap.get(r);if(n){const{message:i}=L("MISSING_OR_INVALID",`Record was recently deleted - ${n}: ${r}`);throw new Error(i)}}),N(this,"isLinkModeEnabled",(r,n)=>{var i,s,o,a,c,u,l,h,d;return!r||n!==ue.link_mode?!1:((s=(i=this.client.metadata)==null?void 0:i.redirect)==null?void 0:s.linkMode)===!0&&((a=(o=this.client.metadata)==null?void 0:o.redirect)==null?void 0:a.universal)!==void 0&&((u=(c=this.client.metadata)==null?void 0:c.redirect)==null?void 0:u.universal)!==""&&((l=r?.redirect)==null?void 0:l.universal)!==void 0&&((h=r?.redirect)==null?void 0:h.universal)!==""&&((d=r?.redirect)==null?void 0:d.linkMode)===!0&&this.client.core.linkModeSupportedApps.includes(r.redirect.universal)&&typeof(global==null?void 0:global.Linking)<"u"}),N(this,"getAppLinkIfEnabled",(r,n)=>{var i;return this.isLinkModeEnabled(r,n)?(i=r?.redirect)==null?void 0:i.universal:void 0}),N(this,"handleLinkModeMessage",({url:r})=>{if(!r||!r.includes("wc_ev")||!r.includes("topic"))return;const n=dh(r,"topic")||"",i=decodeURIComponent(dh(r,"wc_ev")||""),s=this.client.session.keys.includes(n);s&&this.client.session.update(n,{transportType:ue.link_mode}),this.client.core.dispatchEnvelope({topic:n,message:i,sessionExists:s})}),N(this,"registerLinkModeListeners",async()=>{var r;if(Uo()||nr()&&(r=this.client.metadata.redirect)!=null&&r.linkMode){const n=global==null?void 0:global.Linking;if(typeof n<"u"){n.addEventListener("url",this.handleLinkModeMessage,this.client.name);const i=await n.getInitialURL();i&&setTimeout(()=>{this.handleLinkModeMessage({url:i})},50)}}}),N(this,"shouldSetTVF",(r,n)=>{if(!n||r!=="wc_sessionRequest")return!1;const{request:i}=n;return Object.keys(qa).includes(i.method)}),N(this,"getTVFParams",(r,n,i)=>{var s,o;try{const a=n.request.method,c=this.extractTxHashesFromResult(a,i);return Ve(se({correlationId:r,rpcMethods:[a],chainId:n.chainId},this.isValidContractData(n.request.params)&&{contractAddresses:[(o=(s=n.request.params)==null?void 0:s[0])==null?void 0:o.to]}),{txHashes:c})}catch(a){this.client.logger.warn("Error getting TVF params",a)}return{}}),N(this,"isValidContractData",r=>{var n;if(!r)return!1;try{const i=r?.data||((n=r?.[0])==null?void 0:n.data);if(!i.startsWith("0x"))return!1;const s=i.slice(2);return/^[0-9a-fA-F]*$/.test(s)?s.length%2===0:!1}catch{}return!1}),N(this,"extractTxHashesFromResult",(r,n)=>{try{const i=qa[r];if(typeof n=="string")return[n];const s=n[i.key];if(Xn(s))return r==="solana_signAllTransactions"?s.map(o=>h_(o)):s;if(typeof s=="string")return[s]}catch(i){this.client.logger.warn("Error extracting tx hashes from result",i)}return[]})}async processPendingMessageEvents(){try{const e=this.client.session.keys,r=this.client.core.relayer.messages.getWithoutAck(e);for(const[n,i]of Object.entries(r))for(const s of i)try{await this.onProviderMessageEvent({topic:n,message:s,publishedAt:Date.now()})}catch{this.client.logger.warn(`Error processing pending message event for topic: ${n}, message: ${s}`)}}catch(e){this.client.logger.warn("processPendingMessageEvents failed",e)}}isInitialized(){if(!this.initialized){const{message:e}=L("NOT_INITIALIZED",this.name);throw new Error(e)}}async confirmOnlineStateOrThrow(){await this.client.core.relayer.confirmOnlineStateOrThrow()}registerRelayerEvents(){this.client.core.relayer.on(Ae.message,e=>{this.onProviderMessageEvent(e)})}async onRelayMessage(e){const{topic:r,message:n,attestation:i,transportType:s}=e,{publicKey:o}=this.client.auth.authKeys.keys.includes(hi)?this.client.auth.authKeys.get(hi):{responseTopic:void 0,publicKey:void 0};try{const a=await this.client.core.crypto.decode(r,n,{receiverPublicKey:o,encoding:s===ue.link_mode?ur:ft});Ea(a)?(this.client.core.history.set(r,a),await this.onRelayEventRequest({topic:r,payload:a,attestation:i,transportType:s,encryptedId:xt(n)})):cs(a)?(await this.client.core.history.resolve(a),await this.onRelayEventResponse({topic:r,payload:a,transportType:s}),this.client.core.history.delete(r,a.id)):await this.onRelayEventUnknownPayload({topic:r,payload:a,transportType:s}),await this.client.core.relayer.messages.ack(r,n)}catch(a){this.client.logger.error(a)}}registerExpirerEvents(){this.client.core.expirer.on(ut.expired,async e=>{const{topic:r,id:n}=hh(e.target);if(n&&this.client.pendingRequest.keys.includes(n))return await this.deletePendingSessionRequest(n,L("EXPIRED"),!0);if(n&&this.client.auth.requests.keys.includes(n))return await this.deletePendingAuthRequest(n,L("EXPIRED"),!0);r?this.client.session.keys.includes(r)&&(await this.deleteSession({topic:r,expirerHasDeleted:!0}),this.client.events.emit("session_expire",{topic:r})):n&&(await this.deleteProposal(n,!0),this.client.events.emit("proposal_expire",{id:n}))})}registerPairingEvents(){this.client.core.pairing.events.on(Nr.create,e=>this.onPairingCreated(e)),this.client.core.pairing.events.on(Nr.delete,e=>{this.addToRecentlyDeleted(e.topic,"pairing")})}isValidPairingTopic(e){if(!ye(e,!1)){const{message:r}=L("MISSING_OR_INVALID",`pairing topic should be a string: ${e}`);throw new Error(r)}if(!this.client.core.pairing.pairings.keys.includes(e)){const{message:r}=L("NO_MATCHING_KEY",`pairing topic doesn't exist: ${e}`);throw new Error(r)}if(sr(this.client.core.pairing.pairings.get(e).expiry)){const{message:r}=L("EXPIRED",`pairing topic: ${e}`);throw new Error(r)}}async isValidSessionTopic(e){if(!ye(e,!1)){const{message:r}=L("MISSING_OR_INVALID",`session topic should be a string: ${e}`);throw new Error(r)}if(this.checkRecentlyDeleted(e),!this.client.session.keys.includes(e)){const{message:r}=L("NO_MATCHING_KEY",`session topic doesn't exist: ${e}`);throw new Error(r)}if(sr(this.client.session.get(e).expiry)){await this.deleteSession({topic:e});const{message:r}=L("EXPIRED",`session topic: ${e}`);throw new Error(r)}if(!this.client.core.crypto.keychain.has(e)){const{message:r}=L("MISSING_OR_INVALID",`session topic does not exist in keychain: ${e}`);throw await this.deleteSession({topic:e}),new Error(r)}}async isValidSessionOrPairingTopic(e){if(this.checkRecentlyDeleted(e),this.client.session.keys.includes(e))await this.isValidSessionTopic(e);else if(this.client.core.pairing.pairings.keys.includes(e))this.isValidPairingTopic(e);else if(ye(e,!1)){const{message:r}=L("NO_MATCHING_KEY",`session or pairing topic doesn't exist: ${e}`);throw new Error(r)}else{const{message:r}=L("MISSING_OR_INVALID",`session or pairing topic should be a string: ${e}`);throw new Error(r)}}async isValidProposalId(e){if(!FS(e)){const{message:r}=L("MISSING_OR_INVALID",`proposal id should be a number: ${e}`);throw new Error(r)}if(!this.client.proposal.keys.includes(e)){const{message:r}=L("NO_MATCHING_KEY",`proposal id doesn't exist: ${e}`);throw new Error(r)}if(sr(this.client.proposal.get(e).expiryTimestamp)){await this.deleteProposal(e);const{message:r}=L("EXPIRED",`proposal id: ${e}`);throw new Error(r)}}}class kD extends Br{constructor(e,r){super(e,r,Cf,fs),this.core=e,this.logger=r}}class Kf extends Br{constructor(e,r){super(e,r,Bf,fs),this.core=e,this.logger=r}}class jD extends Br{constructor(e,r){super(e,r,Lf,fs,n=>n.id),this.core=e,this.logger=r}}class MD extends Br{constructor(e,r){super(e,r,Mf,li,()=>hi),this.core=e,this.logger=r}}class qD extends Br{constructor(e,r){super(e,r,qf,li),this.core=e,this.logger=r}}class zD extends Br{constructor(e,r){super(e,r,zf,li,n=>n.id),this.core=e,this.logger=r}}var VD=Object.defineProperty,KD=(t,e,r)=>e in t?VD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Va=(t,e,r)=>KD(t,typeof e!="symbol"?e+"":e,r);class HD{constructor(e,r){this.core=e,this.logger=r,Va(this,"authKeys"),Va(this,"pairingTopics"),Va(this,"requests"),this.authKeys=new MD(this.core,this.logger),this.pairingTopics=new qD(this.core,this.logger),this.requests=new zD(this.core,this.logger)}async init(){await this.authKeys.init(),await this.pairingTopics.init(),await this.requests.init()}}var WD=Object.defineProperty,GD=(t,e,r)=>e in t?WD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,X=(t,e,r)=>GD(t,typeof e!="symbol"?e+"":e,r);class ys extends cy{constructor(e){super(e),X(this,"protocol",La),X(this,"version",Ua),X(this,"name",ps.name),X(this,"metadata"),X(this,"core"),X(this,"logger"),X(this,"events",new Ke.exports.EventEmitter),X(this,"engine"),X(this,"session"),X(this,"proposal"),X(this,"pendingRequest"),X(this,"auth"),X(this,"signConfig"),X(this,"on",(n,i)=>this.events.on(n,i)),X(this,"once",(n,i)=>this.events.once(n,i)),X(this,"off",(n,i)=>this.events.off(n,i)),X(this,"removeListener",(n,i)=>this.events.removeListener(n,i)),X(this,"removeAllListeners",n=>this.events.removeAllListeners(n)),X(this,"connect",async n=>{try{return await this.engine.connect(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"pair",async n=>{try{return await this.engine.pair(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"approve",async n=>{try{return await this.engine.approve(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"reject",async n=>{try{return await this.engine.reject(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"update",async n=>{try{return await this.engine.update(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"extend",async n=>{try{return await this.engine.extend(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"request",async n=>{try{return await this.engine.request(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"respond",async n=>{try{return await this.engine.respond(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"ping",async n=>{try{return await this.engine.ping(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"emit",async n=>{try{return await this.engine.emit(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"disconnect",async n=>{try{return await this.engine.disconnect(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"find",n=>{try{return this.engine.find(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"getPendingSessionRequests",()=>{try{return this.engine.getPendingSessionRequests()}catch(n){throw this.logger.error(n.message),n}}),X(this,"authenticate",async(n,i)=>{try{return await this.engine.authenticate(n,i)}catch(s){throw this.logger.error(s.message),s}}),X(this,"formatAuthMessage",n=>{try{return this.engine.formatAuthMessage(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"approveSessionAuthenticate",async n=>{try{return await this.engine.approveSessionAuthenticate(n)}catch(i){throw this.logger.error(i.message),i}}),X(this,"rejectSessionAuthenticate",async n=>{try{return await this.engine.rejectSessionAuthenticate(n)}catch(i){throw this.logger.error(i.message),i}}),this.name=e?.name||ps.name,this.metadata=xE(e?.metadata),this.signConfig=e?.signConfig;const r=typeof e?.logger<"u"&&typeof e?.logger!="string"?e.logger:Pt(Ps({level:e?.logger||ps.logger}));this.core=e?.core||new xD(e),this.logger=je(r,this.name),this.session=new Kf(this.core,this.logger),this.proposal=new kD(this.core,this.logger),this.pendingRequest=new jD(this.core,this.logger),this.engine=new UD(this),this.auth=new HD(this.core,this.logger)}static async init(e){const r=new ys(e);return await r.initialize(),r}get context(){return We(this.logger)}get pairing(){return this.core.pairing.pairings}async initialize(){this.logger.trace("Initialized");try{await this.core.start(),await this.session.init(),await this.proposal.init(),await this.pendingRequest.init(),await this.auth.init(),await this.engine.init(),this.logger.info("SignClient Initialization Success"),setTimeout(()=>{this.engine.processRelayMessageCache()},F.toMiliseconds(F.ONE_SECOND))}catch(e){throw this.logger.info("SignClient Initialization Failure"),this.logger.error(e.message),e}}}const YD=Kf,ZD=ys;W.AUTH_CONTEXT=jf,W.AUTH_KEYS_CONTEXT=Mf,W.AUTH_PAIRING_TOPIC_CONTEXT=qf,W.AUTH_PROTOCOL=kf,W.AUTH_PUBLIC_KEY_NAME=hi,W.AUTH_REQUEST_CONTEXT=zf,W.AUTH_STORAGE_PREFIX=li,W.AUTH_VERSION=ND,W.ENGINE_CONTEXT=Ff,W.ENGINE_QUEUE_STATES=wt,W.ENGINE_RPC_OPTS=Ee,W.HISTORY_CONTEXT=$D,W.HISTORY_EVENTS=AD,W.HISTORY_STORAGE_VERSION=TD,W.METHODS_TO_VERIFY=Uf,W.PROPOSAL_CONTEXT=Cf,W.PROPOSAL_EXPIRY=PD,W.PROPOSAL_EXPIRY_MESSAGE=Ma,W.REQUEST_CONTEXT=Lf,W.SESSION_CONTEXT=Bf,W.SESSION_EXPIRY=Fr,W.SESSION_REQUEST_EXPIRY_BOUNDARIES=gs,W.SIGN_CLIENT_CONTEXT=ka,W.SIGN_CLIENT_DEFAULT=ps,W.SIGN_CLIENT_EVENTS=OD,W.SIGN_CLIENT_PROTOCOL=La,W.SIGN_CLIENT_STORAGE_OPTIONS=DD,W.SIGN_CLIENT_STORAGE_PREFIX=fs,W.SIGN_CLIENT_VERSION=Ua,W.SessionStore=YD,W.SignClient=ZD,W.TVF_METHODS=qa,W.WALLETCONNECT_DEEPLINK_CHOICE=ja,W.default=ys,Object.defineProperty(W,"__esModule",{value:!0})});
//# sourceMappingURL=index.umd.js.map
