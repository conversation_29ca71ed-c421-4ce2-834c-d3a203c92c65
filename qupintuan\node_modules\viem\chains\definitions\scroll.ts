import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const scroll = /*#__PURE__*/ defineChain({
  id: 534_352,
  name: '<PERSON><PERSON>',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.scroll.io'],
      webSocket: ['wss://wss-rpc.scroll.io/ws'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Scrollscan',
      url: 'https://scrollscan.com',
      apiUrl: 'https://api.scrollscan.com/api',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 14,
    },
  },
  testnet: false,
})
