const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-CaV4ohF9-*************-opz9ain10.js","assets/vendor-D7uqzx8C-*************-50a8b124p.js","assets/web3-NCUyEZtP-*************-50a8b124p.js","assets/index-QZjJZq-p-*************-9oqipqqma.css","assets/enhancedGroupBuyService-3fr3dnRp-*************-50a8b124p.js","assets/claimRewardStable-PtwoUOXa-*************-50a8b124p.js"])))=>i.map(i=>d[i]);
import{_ as a}from"./vendor-D7uqzx8C-*************-50a8b124p.js";async function P({chainId:o,roomId:s,signer:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);if(!e)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await a(async()=>{const{getContractAddress:t}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(r=>r.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),{ABIS:_}=await a(async()=>{const{ABIS:t}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(r=>r.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),m=n(o,"GroupBuyRoom"),w=_.GroupBuyRoom,u=await e.writeContract({address:m,abi:w,functionName:"claimCreatorCommission",args:[BigInt(s)]}),{createPublicClient:l,http:A}=await a(async()=>{const{createPublicClient:t,http:r}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(E=>E.x);return{createPublicClient:t,http:r}},__vite__mapDeps([2,1])),{bscTestnet:c}=await a(async()=>{const{bscTestnet:t}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(r=>r.A);return{bscTestnet:t}},__vite__mapDeps([2,1]));return{receipt:await l({chain:c,transport:A()}).waitForTransactionReceipt({hash:u,timeout:6e4}),txHash:u}}catch(n){throw console.error("领取创建者佣金失败:",n),n}}async function T({chainId:o,roomId:s,signer:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);if(!e)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await a(async()=>{const{getContractAddress:t}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(r=>r.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),{ABIS:_}=await a(async()=>{const{ABIS:t}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(r=>r.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),m=n(o,"GroupBuyRoom"),w=_.GroupBuyRoom,u=await e.writeContract({address:m,abi:w,functionName:"claimParticipantRefund",args:[BigInt(s)]}),{createPublicClient:l,http:A}=await a(async()=>{const{createPublicClient:t,http:r}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(E=>E.x);return{createPublicClient:t,http:r}},__vite__mapDeps([2,1])),{bscTestnet:c}=await a(async()=>{const{bscTestnet:t}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(r=>r.A);return{bscTestnet:t}},__vite__mapDeps([2,1]));return{receipt:await l({chain:c,transport:A()}).waitForTransactionReceipt({hash:u,timeout:6e4}),txHash:u}}catch(n){throw console.error("领取参与者退款失败:",n),n}}async function I({chainId:o,signer:s,roomId:e,tierAmount:n}){if(n){const{claimWithUSDTValidation:w}=await a(async()=>{const{claimWithUSDTValidation:l}=await import("./enhancedGroupBuyService-3fr3dnRp-*************-50a8b124p.js");return{claimWithUSDTValidation:l}},__vite__mapDeps([4,1,0,2,3]));return await w(e,n,s.account.address,s)}const{claimRewardStable:_}=await a(async()=>{const{claimRewardStable:w}=await import("./claimRewardStable-PtwoUOXa-*************-50a8b124p.js");return{claimRewardStable:w}},__vite__mapDeps([5,1,0,2,3]));return await _({chainId:o,roomId:e,signer:s})}async function f({chainId:o,roomId:s}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);const{getContractAddress:e}=await a(async()=>{const{getContractAddress:c}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(i=>i.j);return{getContractAddress:c}},__vite__mapDeps([0,1,2,3])),{ABIS:n}=await a(async()=>{const{ABIS:c}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(i=>i.k);return{ABIS:c}},__vite__mapDeps([0,1,2,3])),_=e(o,"GroupBuyRoom"),m=n.GroupBuyRoom,{createPublicClient:w,http:u}=await a(async()=>{const{createPublicClient:c,http:i}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(d=>d.x);return{createPublicClient:c,http:i}},__vite__mapDeps([2,1])),{bscTestnet:l}=await a(async()=>{const{bscTestnet:c}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(i=>i.A);return{bscTestnet:c}},__vite__mapDeps([2,1])),A=w({chain:l,transport:u()});try{const i=(await A.readContract({address:_,abi:m,functionName:"getRoomRewardInfo",args:[BigInt(s)]}))[0];return Number(i)/1e6}catch{try{const d=(await A.readContract({address:_,abi:m,functionName:"rooms",args:[BigInt(s)]}))[7];return Number(d)/1e6}catch(i){return console.warn("无法查询房间佣金信息:",i),0}}}catch(e){return console.error("获取创建者佣金金额失败:",e),0}}async function B({chainId:o,roomId:s,userAddress:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);const{getContractAddress:n}=await a(async()=>{const{getContractAddress:p}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(h=>h.j);return{getContractAddress:p}},__vite__mapDeps([0,1,2,3])),{ABIS:_}=await a(async()=>{const{ABIS:p}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(h=>h.k);return{ABIS:p}},__vite__mapDeps([0,1,2,3])),m=n(o,"GroupBuyRoom"),w=n(o,"QPTBuyback"),u=_.GroupBuyRoom,l=_.QPTBuyback,{createPublicClient:A,http:c}=await a(async()=>{const{createPublicClient:p,http:h}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(b=>b.x);return{createPublicClient:p,http:h}},__vite__mapDeps([2,1])),{bscTestnet:i}=await a(async()=>{const{bscTestnet:p}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(h=>h.A);return{bscTestnet:p}},__vite__mapDeps([2,1])),d=A({chain:i,transport:c()}),t=await d.readContract({address:m,abi:u,functionName:"rooms",args:[BigInt(s)]}),r=t[1],E=Number(r)/1e6;let y=0;try{const p=await d.readContract({address:m,abi:u,functionName:"getRoomRewardInfo",args:[BigInt(s)]});y=Number(p[1])/1e6}catch{const h=t[6];y=Number(h)/1e6}if(e&&y>0)try{const p=await d.readContract({address:w,abi:l,functionName:"getRefundStatus",args:[BigInt(s),e]}),[h,b,R]=p;(Number(b)===1||Number(b)===3)&&(y=0)}catch(p){console.warn("查询退款状态失败，保持原有补贴金额:",p)}return{refundAmount:E,subsidyAmount:y}}catch(n){return console.error("获取参与者退款金额失败:",n),{refundAmount:0,subsidyAmount:0}}}async function D({chainId:o,roomId:s,signer:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);if(!e)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await a(async()=>{const{getContractAddress:t}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(r=>r.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),{ABIS:_}=await a(async()=>{const{ABIS:t}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(r=>r.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),m=n(o,"GroupBuyRoom"),w=_.GroupBuyRoom,u=await e.writeContract({address:m,abi:w,functionName:"claimWinnerQPT",args:[BigInt(s)]}),{createPublicClient:l,http:A}=await a(async()=>{const{createPublicClient:t,http:r}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(E=>E.x);return{createPublicClient:t,http:r}},__vite__mapDeps([2,1])),{bscTestnet:c}=await a(async()=>{const{bscTestnet:t}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(r=>r.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),d=await l({chain:c,transport:A()}).waitForTransactionReceipt({hash:u,timeout:6e4});if(d.status==="reverted")throw new Error("交易被回滚，QPT奖励领取失败");return{receipt:d,txHash:u}}catch(n){throw console.error("领取QPT奖励失败:",n),n}}async function g({chainId:o,roomId:s,signer:e}){try{if(o!==97)throw new Error(`不支持的链ID: ${o}`);if(!e)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await a(async()=>{const{getContractAddress:t}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(r=>r.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),{ABIS:_}=await a(async()=>{const{ABIS:t}=await import("./index-CaV4ohF9-*************-opz9ain10.js").then(r=>r.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),m=n(o,"GroupBuyRoom"),w=_.GroupBuyRoom,u=await e.writeContract({address:m,abi:w,functionName:"claimWinnerPoints",args:[BigInt(s)]}),{createPublicClient:l,http:A}=await a(async()=>{const{createPublicClient:t,http:r}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(E=>E.x);return{createPublicClient:t,http:r}},__vite__mapDeps([2,1])),{bscTestnet:c}=await a(async()=>{const{bscTestnet:t}=await import("./web3-NCUyEZtP-*************-50a8b124p.js").then(r=>r.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),d=await l({chain:c,transport:A()}).waitForTransactionReceipt({hash:u,timeout:6e4});if(d.status==="reverted")throw new Error("交易被回滚，积分奖励领取失败");return{receipt:d,txHash:u}}catch(n){throw console.error("领取积分奖励失败:",n),n}}export{D as a,g as b,P as c,T as d,I as e,B as f,f as g};
