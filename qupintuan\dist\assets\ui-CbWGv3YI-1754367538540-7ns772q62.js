import{r as u,s as M,n as A,a as j,i as ee,Q as te,M as re}from"./web3-uglCpOhK-1754367538540-7ns772q62.js";import{r as se}from"./vendor-CgHzTxSQ-1754367538540-7ns772q62.js";var T={exports:{}},E={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var I;function ae(){if(I)return E;I=1;var e=se(),t=Symbol.for("react.element"),s=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,l=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,r={key:!0,ref:!0,__self:!0,__source:!0};function a(n,i,c){var d,p={},f=null,y=null;c!==void 0&&(f=""+c),i.key!==void 0&&(f=""+i.key),i.ref!==void 0&&(y=i.ref);for(d in i)o.call(i,d)&&!r.hasOwnProperty(d)&&(p[d]=i[d]);if(n&&n.defaultProps)for(d in i=n.defaultProps,i)p[d]===void 0&&(p[d]=i[d]);return{$$typeof:t,type:n,key:f,ref:y,props:p,_owner:l.current}}return E.Fragment=s,E.jsx=a,E.jsxs=a,E}var Q;function oe(){return Q||(Q=1,T.exports=ae()),T.exports}var ie=oe(),z=u.createContext(void 0),F=e=>{const t=u.useContext(z);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},at=({client:e,children:t})=>(u.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),ie.jsx(z.Provider,{value:e,children:t})),H=u.createContext(!1),ne=()=>u.useContext(H);H.Provider;function le(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var ue=u.createContext(le()),ce=()=>u.useContext(ue),de=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},pe=e=>{u.useEffect(()=>{e.clearReset()},[e])},fe=({result:e,errorResetBoundary:t,throwOnError:s,query:o,suspense:l})=>e.isError&&!t.isReset()&&!e.isFetching&&o&&(l&&e.data===void 0||M(s,[e.error,o])),me=e=>{if(e.suspense){const t=o=>o==="static"?o:Math.max(o??1e3,1e3),s=e.staleTime;e.staleTime=typeof s=="function"?(...o)=>t(s(...o)):t(s),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},ye=(e,t)=>e.isLoading&&e.isFetching&&!t,ge=(e,t)=>e?.suspense&&t.isPending,q=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function he(e,t,s){const o=ne(),l=ce(),r=F(),a=r.defaultQueryOptions(e);r.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=o?"isRestoring":"optimistic",me(a),de(a,l),pe(l);const n=!r.getQueryCache().get(a.queryHash),[i]=u.useState(()=>new t(r,a)),c=i.getOptimisticResult(a),d=!o&&e.subscribed!==!1;if(u.useSyncExternalStore(u.useCallback(p=>{const f=d?i.subscribe(A.batchCalls(p)):j;return i.updateResult(),f},[i,d]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),u.useEffect(()=>{i.setOptions(a)},[a,i]),ge(a,c))throw q(a,i,l);if(fe({result:c,errorResetBoundary:l,throwOnError:a.throwOnError,query:r.getQueryCache().get(a.queryHash),suspense:a.suspense}))throw c.error;return r.getDefaultOptions().queries?._experimental_afterQuery?.(a,c),a.experimental_prefetchInRender&&!ee&&ye(c,o)&&(n?q(a,i,l):r.getQueryCache().get(a.queryHash)?.promise)?.catch(j).finally(()=>{i.updateResult()}),a.notifyOnChangeProps?c:i.trackResult(c)}function ot(e,t){return he(e,te)}function it(e,t){const s=F(),[o]=u.useState(()=>new re(s,e));u.useEffect(()=>{o.setOptions(e)},[o,e]);const l=u.useSyncExternalStore(u.useCallback(a=>o.subscribe(A.batchCalls(a)),[o]),()=>o.getCurrentResult(),()=>o.getCurrentResult()),r=u.useCallback((a,n)=>{o.mutate(a,n).catch(j)},[o]);if(l.error&&M(o.options.throwOnError,[l.error]))throw l.error;return{...l,mutate:r,mutateAsync:l.mutate}}let be={data:""},ve=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||be,xe=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,we=/\/\*[^]*?\*\/|  +/g,N=/\n+/g,b=(e,t)=>{let s="",o="",l="";for(let r in e){let a=e[r];r[0]=="@"?r[1]=="i"?s=r+" "+a+";":o+=r[1]=="f"?b(a,r):r+"{"+b(a,r[1]=="k"?"":t)+"}":typeof a=="object"?o+=b(a,t?t.replace(/([^,])+/g,n=>r.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,i=>/&/.test(i)?i.replace(/&/g,n):n?n+" "+i:i)):r):a!=null&&(r=/^--/.test(r)?r:r.replace(/[A-Z]/g,"-$&").toLowerCase(),l+=b.p?b.p(r,a):r+":"+a+";")}return s+(t&&l?t+"{"+l+"}":l)+o},g={},L=e=>{if(typeof e=="object"){let t="";for(let s in e)t+=s+L(e[s]);return t}return e},Ee=(e,t,s,o,l)=>{let r=L(e),a=g[r]||(g[r]=(i=>{let c=0,d=11;for(;c<i.length;)d=101*d+i.charCodeAt(c++)>>>0;return"go"+d})(r));if(!g[a]){let i=r!==e?e:(c=>{let d,p,f=[{}];for(;d=xe.exec(c.replace(we,""));)d[4]?f.shift():d[3]?(p=d[3].replace(N," ").trim(),f.unshift(f[0][p]=f[0][p]||{})):f[0][d[1]]=d[2].replace(N," ").trim();return f[0]})(e);g[a]=b(l?{["@keyframes "+a]:i}:i,s?"":"."+a)}let n=s&&g.g?g.g:null;return s&&(g.g=g[a]),((i,c,d,p)=>{p?c.data=c.data.replace(p,i):c.data.indexOf(i)===-1&&(c.data=d?i+c.data:c.data+i)})(g[a],t,o,n),a},Ce=(e,t,s)=>e.reduce((o,l,r)=>{let a=t[r];if(a&&a.call){let n=a(s),i=n&&n.props&&n.props.className||/^go/.test(n)&&n;a=i?"."+i:n&&typeof n=="object"?n.props?"":b(n,""):n===!1?"":n}return o+l+(a??"")},"");function S(e){let t=this||{},s=e.call?e(t.p):e;return Ee(s.unshift?s.raw?Ce(s,[].slice.call(arguments,1),t.p):s.reduce((o,l)=>Object.assign(o,l&&l.call?l(t.p):l),{}):s,ve(t.target),t.g,t.o,t.k)}let B,P,D;S.bind({g:1});let h=S.bind({k:1});function Re(e,t,s,o){b.p=t,B=e,P=s,D=o}function v(e,t){let s=this||{};return function(){let o=arguments;function l(r,a){let n=Object.assign({},r),i=n.className||l.className;s.p=Object.assign({theme:P&&P()},n),s.o=/ *go\d+/.test(i),n.className=S.apply(s,o)+(i?" "+i:"");let c=e;return e[0]&&(c=n.as||e,delete n.as),D&&c[0]&&D(n),B(c,n)}return l}}var _e=e=>typeof e=="function",R=(e,t)=>_e(e)?e(t):e,Oe=(()=>{let e=0;return()=>(++e).toString()})(),J=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),ke=20,U=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,ke)};case 1:return{...e,toasts:e.toasts.map(r=>r.id===t.toast.id?{...r,...t.toast}:r)};case 2:let{toast:s}=t;return U(e,{type:e.toasts.find(r=>r.id===s.id)?1:0,toast:s});case 3:let{toastId:o}=t;return{...e,toasts:e.toasts.map(r=>r.id===o||o===void 0?{...r,dismissed:!0,visible:!1}:r)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(r=>r.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let l=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(r=>({...r,pauseDuration:r.pauseDuration+l}))}}},k=[],x={toasts:[],pausedAt:void 0},w=e=>{x=U(x,e),k.forEach(t=>{t(x)})},Se={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},V=(e={})=>{let[t,s]=u.useState(x),o=u.useRef(x);u.useEffect(()=>(o.current!==x&&s(x),k.push(s),()=>{let r=k.indexOf(s);r>-1&&k.splice(r,1)}),[]);let l=t.toasts.map(r=>{var a,n,i;return{...e,...e[r.type],...r,removeDelay:r.removeDelay||((a=e[r.type])==null?void 0:a.removeDelay)||e?.removeDelay,duration:r.duration||((n=e[r.type])==null?void 0:n.duration)||e?.duration||Se[r.type],style:{...e.style,...(i=e[r.type])==null?void 0:i.style,...r.style}}});return{...t,toasts:l}},$e=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:s?.id||Oe()}),_=e=>(t,s)=>{let o=$e(t,e,s);return w({type:2,toast:o}),o.id},m=(e,t)=>_("blank")(e,t);m.error=_("error");m.success=_("success");m.loading=_("loading");m.custom=_("custom");m.dismiss=e=>{w({type:3,toastId:e})};m.remove=e=>w({type:4,toastId:e});m.promise=(e,t,s)=>{let o=m.loading(t.loading,{...s,...s?.loading});return typeof e=="function"&&(e=e()),e.then(l=>{let r=t.success?R(t.success,l):void 0;return r?m.success(r,{id:o,...s,...s?.success}):m.dismiss(o),l}).catch(l=>{let r=t.error?R(t.error,l):void 0;r?m.error(r,{id:o,...s,...s?.error}):m.dismiss(o)}),e};var Te=(e,t)=>{w({type:1,toast:{id:e,height:t}})},je=()=>{w({type:5,time:Date.now()})},C=new Map,Pe=1e3,De=(e,t=Pe)=>{if(C.has(e))return;let s=setTimeout(()=>{C.delete(e),w({type:4,toastId:e})},t);C.set(e,s)},Y=e=>{let{toasts:t,pausedAt:s}=V(e);u.useEffect(()=>{if(s)return;let r=Date.now(),a=t.map(n=>{if(n.duration===1/0)return;let i=(n.duration||0)+n.pauseDuration-(r-n.createdAt);if(i<0){n.visible&&m.dismiss(n.id);return}return setTimeout(()=>m.dismiss(n.id),i)});return()=>{a.forEach(n=>n&&clearTimeout(n))}},[t,s]);let o=u.useCallback(()=>{s&&w({type:6,time:Date.now()})},[s]),l=u.useCallback((r,a)=>{let{reverseOrder:n=!1,gutter:i=8,defaultPosition:c}=a||{},d=t.filter(y=>(y.position||c)===(r.position||c)&&y.height),p=d.findIndex(y=>y.id===r.id),f=d.filter((y,$)=>$<p&&y.visible).length;return d.filter(y=>y.visible).slice(...n?[f+1]:[0,f]).reduce((y,$)=>y+($.height||0)+i,0)},[t]);return u.useEffect(()=>{t.forEach(r=>{if(r.dismissed)De(r.id,r.removeDelay);else{let a=C.get(r.id);a&&(clearTimeout(a),C.delete(r.id))}})},[t]),{toasts:t,handlers:{updateHeight:Te,startPause:je,endPause:o,calculateOffset:l}}},Ie=h`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Qe=h`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,qe=h`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,W=v("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Ie} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Qe} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${qe} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Ne=h`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Z=v("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Ne} 1s linear infinite;
`,Me=h`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Ae=h`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,K=v("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Me} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Ae} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,ze=v("div")`
  position: absolute;
`,Fe=v("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,He=h`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Le=v("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${He} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,X=({toast:e})=>{let{icon:t,type:s,iconTheme:o}=e;return t!==void 0?typeof t=="string"?u.createElement(Le,null,t):t:s==="blank"?null:u.createElement(Fe,null,u.createElement(Z,{...o}),s!=="loading"&&u.createElement(ze,null,s==="error"?u.createElement(W,{...o}):u.createElement(K,{...o})))},Be=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Je=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,Ue="0%{opacity:0;} 100%{opacity:1;}",Ve="0%{opacity:1;} 100%{opacity:0;}",Ye=v("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,We=v("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Ze=(e,t)=>{let s=e.includes("top")?1:-1,[o,l]=J()?[Ue,Ve]:[Be(s),Je(s)];return{animation:t?`${h(o)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${h(l)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},G=u.memo(({toast:e,position:t,style:s,children:o})=>{let l=e.height?Ze(e.position||t||"top-center",e.visible):{opacity:0},r=u.createElement(X,{toast:e}),a=u.createElement(We,{...e.ariaProps},R(e.message,e));return u.createElement(Ye,{className:e.className,style:{...l,...s,...e.style}},typeof o=="function"?o({icon:r,message:a}):u.createElement(u.Fragment,null,r,a))});Re(u.createElement);var Ke=({id:e,className:t,style:s,onHeightUpdate:o,children:l})=>{let r=u.useCallback(a=>{if(a){let n=()=>{let i=a.getBoundingClientRect().height;o(e,i)};n(),new MutationObserver(n).observe(a,{subtree:!0,childList:!0,characterData:!0})}},[e,o]);return u.createElement("div",{ref:r,className:t,style:s},l)},Xe=(e,t)=>{let s=e.includes("top"),o=s?{top:0}:{bottom:0},l=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:J()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...o,...l}},Ge=S`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,O=16,et=({reverseOrder:e,position:t="top-center",toastOptions:s,gutter:o,children:l,containerStyle:r,containerClassName:a})=>{let{toasts:n,handlers:i}=Y(s);return u.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:O,left:O,right:O,bottom:O,pointerEvents:"none",...r},className:a,onMouseEnter:i.startPause,onMouseLeave:i.endPause},n.map(c=>{let d=c.position||t,p=i.calculateOffset(c,{reverseOrder:e,gutter:o,defaultPosition:t}),f=Xe(d,p);return u.createElement(Ke,{id:c.id,key:c.id,onHeightUpdate:i.updateHeight,className:c.visible?Ge:"",style:f},c.type==="custom"?R(c.message,c):l?l(c):u.createElement(G,{toast:c,position:d}))}))},tt=m;const nt=Object.freeze(Object.defineProperty({__proto__:null,CheckmarkIcon:K,ErrorIcon:W,LoaderIcon:Z,ToastBar:G,ToastIcon:X,Toaster:et,default:tt,resolveValue:R,toast:m,useToaster:Y,useToasterStore:V},Symbol.toStringTag,{value:"Module"}));export{et as O,at as Q,tt as V,it as a,F as b,m as c,nt as i,ie as j,ot as u};
