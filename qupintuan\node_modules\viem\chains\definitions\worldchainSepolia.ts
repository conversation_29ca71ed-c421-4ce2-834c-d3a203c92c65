import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 11_155_111 // sepolia

export const worldchainSepolia = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 4801,
  name: 'World Chain Sepolia',
  network: 'worldchain-sepolia',
  nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: { http: ['https://worldchain-sepolia.g.alchemy.com/public'] },
  },
  blockExplorers: {
    default: {
      name: 'Worldscan Sepolia',
      url: 'https://sepolia.worldscan.org',
      apiUrl: 'https://api-sepolia.worldscan.org/api',
    },
    blockscout: {
      name: 'Blockscout',
      url: 'https://worldchain-sepolia.explorer.alchemy.com',
      apiUrl: 'https://worldchain-sepolia.explorer.alchemy.com/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    multicall3: {
      address: '******************************************',
      blockCreated: 0,
    },
    disputeGameFactory: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '0xd7DF54b3989855eb66497301a4aAEc33Dbb3F8DE',
      },
    },
  },
  testnet: true,
  sourceId,
})
