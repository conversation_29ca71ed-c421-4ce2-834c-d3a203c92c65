const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-CaV4ohF9-1754375917692-opz9ain10.js","assets/vendor-D7uqzx8C-1754375917692-50a8b124p.js","assets/web3-NCUyEZtP-1754375917692-50a8b124p.js","assets/index-QZjJZq-p-1754375917692-9oqipqqma.css","assets/enhancedGroupBuyService-3fr3dnRp-1754375917692-50a8b124p.js"])))=>i.map(i=>d[i]);
import{fetchRoom as D,fetchTotalRooms as V}from"./basicOperations-BOq0NLuf-1754375917692-50a8b124p.js";import{approveQPTForCreate as O,approveUSDTForJoin as N,closeRoom as Q,createRoomWithQPTVerification as S,createRoomWithQPTVerification as x,expireRoom as W,joinRoomWithPaymentVerification as j,joinRoomWithPaymentVerification as q,lockQPTForCreate as G}from"./roomManagement-BZPf_gXk-1754375917692-50a8b124p.js";import{_ as o}from"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";import{c as M,d as F,e as H,b as $,a as J,g as z,f as K}from"./rewardOperations-I9R1_Rnl-1754375917692-50a8b124p.js";async function E({chainId:_,roomId:d,winner:c,lotteryTxHash:n,lotteryTimestamp:m,signer:p}){try{if(!p)throw new Error("签名者未定义，请确保钱包已连接");if(!c||!n||!m)throw new Error("缺少必要参数：winner, lotteryTxHash, lotteryTimestamp");const{getContractAddress:i}=await o(async()=>{const{getContractAddress:t}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(r=>r.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),w=i(_,"GroupBuyRoom"),{ABIS:h}=await o(async()=>{const{ABIS:t}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(r=>r.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),T=h.GroupBuyRoom,P=await p.writeContract({address:w,abi:T,functionName:"setWinner",args:[BigInt(d),c,n,BigInt(m)]}),{createPublicClient:C,http:l}=await o(async()=>{const{createPublicClient:t,http:r}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(a=>a.x);return{createPublicClient:t,http:r}},__vite__mapDeps([2,1])),{bscTestnet:e}=await o(async()=>{const{bscTestnet:t}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(r=>r.A);return{bscTestnet:t}},__vite__mapDeps([2,1]));return{receipt:await C({chain:e,transport:l()}).waitForTransactionReceipt({hash:P,timeout:6e4}),txHash:P}}catch(i){throw console.error("设置赢家失败:",i),i}}async function g({chainId:_,roomId:d}){try{const{getContractAddress:c}=await o(async()=>{const{getContractAddress:a}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(A=>A.j);return{getContractAddress:a}},__vite__mapDeps([0,1,2,3])),{ABIS:n}=await o(async()=>{const{ABIS:a}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(A=>A.k);return{ABIS:a}},__vite__mapDeps([0,1,2,3])),m=c(_,"QPTLocker"),p=n.QPTLocker,{createPublicClient:i,http:w}=await o(async()=>{const{createPublicClient:a,http:A}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(b=>b.x);return{createPublicClient:a,http:A}},__vite__mapDeps([2,1])),{bscTestnet:h}=await o(async()=>{const{bscTestnet:a}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(A=>A.A);return{bscTestnet:a}},__vite__mapDeps([2,1])),P=await i({chain:h,transport:w()}).readContract({address:m,abi:p,functionName:"getRoomInfo",args:[BigInt(d)]}),[C,l,e,s,u]=P,t=Math.floor(Date.now()/1e3),r=Math.max(0,Number(e)-t);return{creator:C,amount:Number(l),unlockTime:Number(e),isSuccess:s,isClaimed:u,remainingTime:r,isUnlocked:r===0}}catch(c){return console.error("获取QPT锁仓信息失败:",c),{creator:"0x0000000000000000000000000000000000000000",amount:0,unlockTime:0,isSuccess:!1,isClaimed:!1,remainingTime:0,isUnlocked:!1}}}async function y({chainId:_,roomId:d,signer:c}){try{if(!c)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await o(async()=>{const{getContractAddress:e}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(s=>s.j);return{getContractAddress:e}},__vite__mapDeps([0,1,2,3])),{ABIS:m}=await o(async()=>{const{ABIS:e}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(s=>s.k);return{ABIS:e}},__vite__mapDeps([0,1,2,3])),p=n(_,"QPTLocker"),i=m.QPTLocker,w=await c.writeContract({address:p,abi:i,functionName:"claimLockedQPT",args:[BigInt(d)]}),{createPublicClient:h,http:T}=await o(async()=>{const{createPublicClient:e,http:s}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(u=>u.x);return{createPublicClient:e,http:s}},__vite__mapDeps([2,1])),{bscTestnet:P}=await o(async()=>{const{bscTestnet:e}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(s=>s.A);return{bscTestnet:e}},__vite__mapDeps([2,1]));return{receipt:await h({chain:P,transport:T()}).waitForTransactionReceipt({hash:w,timeout:6e4}),txHash:w}}catch(n){throw console.error("领取锁定QPT失败:",n),n}}async function I({chainId:_,roomId:d,userAddress:c}){try{const{getUserPaidAmount:n}=await o(async()=>{const{getUserPaidAmount:p}=await import("./enhancedGroupBuyService-3fr3dnRp-1754375917692-50a8b124p.js");return{getUserPaidAmount:p}},__vite__mapDeps([4,1,0,2,3]));return await n(d,c)}catch(n){return console.error("获取用户支付金额失败:",n),"0"}}async function R({chainId:_,roomId:d,userAddress:c,role:n}){try{const{getContractAddress:m}=await o(async()=>{const{getContractAddress:r}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(a=>a.j);return{getContractAddress:r}},__vite__mapDeps([0,1,2,3])),{ABIS:p}=await o(async()=>{const{ABIS:r}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(a=>a.k);return{ABIS:r}},__vite__mapDeps([0,1,2,3])),i=m(_,"GroupBuyRoom"),w=p.GroupBuyRoom,{createPublicClient:h,http:T}=await o(async()=>{const{createPublicClient:r,http:a}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(A=>A.x);return{createPublicClient:r,http:a}},__vite__mapDeps([2,1])),{bscTestnet:P}=await o(async()=>{const{bscTestnet:r}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(a=>a.A);return{bscTestnet:r}},__vite__mapDeps([2,1])),C=h({chain:P,transport:T()}),[l,e,s,u]=await C.readContract({address:i,abi:w,functionName:"getUserClaimStatus",args:[BigInt(d),c]});let t;switch(n){case"creator":t=l;break;case"winner":try{const[r,a]=await C.readContract({address:i,abi:w,functionName:"getWinnerRewardStatus",args:[BigInt(d),c]});t=r||a}catch{t=!1}break;case"participant":t=e;break;default:throw new Error(`不支持的角色类型: ${n}`)}return t}catch(m){return m.message?.includes("not found on ABI"),!1}}async function L({chainId:_,roomId:d,winnerAddress:c}){try{if(_!==97)throw new Error(`不支持的链ID: ${_}`);const{getContractAddress:n}=await o(async()=>{const{getContractAddress:u}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(t=>t.j);return{getContractAddress:u}},__vite__mapDeps([0,1,2,3])),{ABIS:m}=await o(async()=>{const{ABIS:u}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(t=>t.k);return{ABIS:u}},__vite__mapDeps([0,1,2,3])),p=n(_,"QPTLocker"),i=n(_,"GroupBuyRoom"),w=m.QPTLocker,h=m.GroupBuyRoom,{createPublicClient:T,http:P}=await o(async()=>{const{createPublicClient:u,http:t}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(r=>r.x);return{createPublicClient:u,http:t}},__vite__mapDeps([2,1])),{bscTestnet:C}=await o(async()=>{const{bscTestnet:u}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(t=>t.A);return{bscTestnet:u}},__vite__mapDeps([2,1])),l=T({chain:C,transport:P()});let e=0,s=0;try{const t=(await l.readContract({address:i,abi:h,functionName:"rooms",args:[BigInt(d)]}))[1],r=await l.readContract({address:p,abi:w,functionName:"amountMappings",args:[t]});e=Number(r[2])/1e18;const a=await l.readContract({address:i,abi:h,functionName:"tierPoints",args:[t]}).catch(()=>0n);s=Number(a)/1e6}catch{try{const r=(await l.readContract({address:i,abi:h,functionName:"rooms",args:[BigInt(d)]}))[1],a=await l.readContract({address:p,abi:w,functionName:"amountMappings",args:[r]}).catch(()=>null);a&&(e=Number(a[2])/1e18);const A=await l.readContract({address:i,abi:h,functionName:"tierPoints",args:[r]}).catch(()=>0n);s=Number(A)/1e6}catch(t){console.warn("备用方法查询赢家奖励失败:",t)}}return{qptAmount:e,pointsAmount:s}}catch(n){return console.error("获取赢家奖励数据失败:",n),{qptAmount:0,pointsAmount:0}}}export{O as approveQPTForCreate,N as approveUSDTForJoin,R as checkRoleClaimStatus,M as claimCreatorCommission,y as claimLockedQPT,F as claimParticipantRefund,H as claimReward,$ as claimWinnerPoints,J as claimWinnerQPT,Q as closeRoom,S as createRoom,x as createRoomWithQPTVerification,W as expireRoom,D as fetchRoom,V as fetchTotalRooms,z as getCreatorCommissionAmount,K as getParticipantRefundAmount,g as getQPTLockInfo,I as getUserPaidAmount,L as getWinnerRewards,j as joinRoom,q as joinRoomWithPaymentVerification,G as lockQPTForCreate,E as setWinner};
