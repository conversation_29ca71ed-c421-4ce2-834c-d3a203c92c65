import {
  svg
} from "./chunk-NISX6KSY.js";
import "./chunk-ONY6HBPH.js";

// node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/filters.js
var filtersSvg = svg`<svg fill="none" viewBox="0 0 16 16">
  <path
    fill="currentColor"
    fill-rule="evenodd"
    d="M0 3a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H1a1 1 0 0 1-1-1Zm2.63 5.25a1 1 0 0 1 1-1h8.75a1 1 0 1 1 0 2H3.63a1 1 0 0 1-1-1Zm2.62 5.25a1 1 0 0 1 1-1h3.5a1 1 0 0 1 0 2h-3.5a1 1 0 0 1-1-1Z"
    clip-rule="evenodd"
  />
</svg>`;
export {
  filtersSvg
};
//# sourceMappingURL=filters-D7AON44B.js.map
