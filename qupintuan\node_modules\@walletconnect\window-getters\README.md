# window-getters [![npm version](https://badge.fury.io/js/window-getters.svg)](https://badge.fury.io/js/window-getters)

Safe Window Getters

## API

```typescript
function getFromWindow<T>(name: string): T | undefined;
function getFromWindowOrThrow<T>(name: string): T;

function getDocumentOrThrow(): Document;
function getDocument(): Document | undefined;

function getNavigatorOrThrow(): Navigator;
function getNavigator(): Navigator | undefined;

function getLocationOrThrow(): Location;
function getLocation(): Location | undefined;

function getCryptoOrThrow(): Crypto;
function getCrypto(): Crypto | undefined;

function getLocalStorageOrThrow(): Storage;
function getLocalStorage(): Storage | undefined;
```
