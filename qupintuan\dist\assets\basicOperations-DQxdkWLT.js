const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-BDw84Puy.js","assets/web3-DnWbColA.js","assets/vendor-CgHzTxSQ.js","assets/ui-B9ZzSjJF.js","assets/index-QZjJZq-p.css"])))=>i.map(i=>d[i]);
import{_ as u}from"./web3-DnWbColA.js";import"./vendor-CgHzTxSQ.js";import"./ui-B9ZzSjJF.js";const O=new Map,b=new Map,q=15e3,K=8e3;async function j({chainId:h}){try{const{getContractAddress:c}=await u(async()=>{const{getContractAddress:n}=await import("./index-BDw84Puy.js").then(o=>o.n);return{getContractAddress:n}},__vite__mapDeps([0,1,2,3,4])),r=c(h,"GroupBuyRoom"),{ABIS:a}=await u(async()=>{const{ABIS:n}=await import("./index-BDw84Puy.js").then(o=>o.o);return{ABIS:n}},__vite__mapDeps([0,1,2,3,4])),p=a.GroupBuyRoom,{createPublicClient:f,http:A}=await u(async()=>{const{createPublicClient:n,http:o}=await import("./web3-DnWbColA.js").then(R=>R.aL);return{createPublicClient:n,http:o}},__vite__mapDeps([1,2,3])),{bscTestnet:w}=await u(async()=>{const{bscTestnet:n}=await import("./index-BDw84Puy.js").then(o=>o.m);return{bscTestnet:n}},__vite__mapDeps([0,1,2,3,4]));return await f({chain:w,transport:A()}).readContract({address:r,abi:p,functionName:"totalRooms"})}catch(c){return console.error("获取总房间数量失败:",c),0n}}async function z({chainId:h,roomId:c}){try{const r=BigInt(c),a=`room_${h}_${c}`,p=O.get(a);if(p&&Date.now()-p.timestamp<q)return p.data;if(b.has(a))return await b.get(a);if(r<0)throw new Error(`无效的房间ID: ${c}，房间ID应该大于等于0`);const f=(async()=>{try{const{getContractAddress:A}=await u(async()=>{const{getContractAddress:t}=await import("./index-BDw84Puy.js").then(e=>e.n);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3,4])),w=A(h,"GroupBuyRoom"),{ABIS:g}=await u(async()=>{const{ABIS:t}=await import("./index-BDw84Puy.js").then(e=>e.o);return{ABIS:t}},__vite__mapDeps([0,1,2,3,4])),T=g.GroupBuyRoom,{createPublicClient:n,http:o}=await u(async()=>{const{createPublicClient:t,http:e}=await import("./web3-DnWbColA.js").then(m=>m.aL);return{createPublicClient:t,http:e}},__vite__mapDeps([1,2,3])),{bscTestnet:R}=await u(async()=>{const{bscTestnet:t}=await import("./index-BDw84Puy.js").then(e=>e.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4])),C=n({chain:R,transport:o()}),G=new Promise((t,e)=>{setTimeout(()=>e(new Error("请求超时")),K)}),H=await Promise.race([C.readContract({address:w,abi:T,functionName:"getRoom",args:[r]}),G]),[M,v,F,V,E,s,N,W,S]=H;let l=null;try{E&&(l=await C.readContract({address:w,abi:T,functionName:"getLotteryInfo",args:[r]}))}catch(t){t.message?.includes("limit exceeded")||console.warn(`获取房间 ${r} 开奖信息失败:`,t.message)}let $=!1;try{E&&($=await C.readContract({address:w,abi:T,functionName:"roomReadyForWinner",args:[r]}))}catch(t){t.message?.includes("limit exceeded")||console.warn(`获取房间 ${r} readyForWinner状态失败:`,t.message)}const y=F.map(t=>t.toLowerCase());let d=null;try{const t=localStorage.getItem(`lottery_${r}`);t&&(d=JSON.parse(t))}catch{}let i=0,_=!1,P=Number(V),D=null;try{D=await C.readContract({address:w,abi:T,functionName:"getRoomTimeInfo",args:[r]})}catch(t){t.message?.includes("limit exceeded")||console.warn(`获取房间 ${r} 时间信息失败:`,t.message)}let x=!1;if(D){const[t,e,m,B,U]=D;P=Number(t),i=Number(U),x=B}else{const t=Math.floor(Date.now()/1e3),e=1440*60,m=Number(V);if(m>1e9){const B=m+e;i=Math.max(0,B-t),x=i===0,P=m}else x=!1,i=0}E?s!=="0x0000000000000000000000000000000000000000"?(_=!1,i=0):d&&d.txHash||y.length>=8?(_=!1,i=i>0?i:3600):(_=!0,i=0):y.length>=8?_=!1:x?(_=!0,i=0):_=!1;const I={id:Number(r),creator:M.toLowerCase(),tier:Number(v),tierAmount:Number(v),participants:y,participantsCount:y.length,maxParticipants:8,createTime:P,timeLeft:i,isExpired:_,isClosed:E,isSuccessful:s,winnerIndex:Number(N),lotteryTxHash:l&&l[2]?l[2]:s!=="0x0000000000000000000000000000000000000000"&&W?W:d?.txHash||null,lotteryTimestamp:l&&l[3]?Number(l[3]):s!=="0x0000000000000000000000000000000000000000"&&S?Number(S):d?.timestamp||null,winner:(()=>{if(s!=="0x0000000000000000000000000000000000000000"){const t=y.findIndex(e=>e===s.toLowerCase());return{index:t>=0?t+1:Number(N)+1,address:s}}return null})(),winnerAddress:s!=="0x0000000000000000000000000000000000000000"?s:null,calculatedWinner:d?.winner||null,winnerInfo:(()=>{if(s!=="0x0000000000000000000000000000000000000000"){const t=y.findIndex(e=>e===s.toLowerCase());return{address:s,winnerAddress:s,index:t>=0?t+1:Number(N)+1,source:"contract"}}if(l&&l[1]!=="0x0000000000000000000000000000000000000000"){const t=l[1],e=y.findIndex(m=>m===t.toLowerCase());return{address:t,winnerAddress:t,index:e>=0?e+1:Number(l[4])+1,source:"contract-lottery"}}return d?.winner?{...d.winner,source:"local"}:null})(),readyForWinner:$||d&&d.txHash,status:E&&s?"completed":E&&!s?"failed":_?"expired":y.length>=8?"full":"active"};let L=I;try{if(I.isClosed||I.participants&&I.participants.length>=8){const{enhanceRoomWithLotteryInfo:e}=await u(async()=>{const{enhanceRoomWithLotteryInfo:m}=await import("./lotteryInfoPersistence-FEnoUD8d.js");return{enhanceRoomWithLotteryInfo:m}},[]);L=e(I)}else L={...I,readyForWinner:!1,lotteryTxHash:null,lotteryTimestamp:null,calculatedWinner:null,lotteryInfo:null}}catch(t){console.warn(`增强房间 #${I.id} 开奖信息失败:`,t)}return O.set(a,{data:L,timestamp:Date.now()}),L}finally{b.delete(a)}})();return b.set(a,f),await f}catch(r){b.delete(cacheKey);const a=r.message||"";if(a.includes("limit exceeded")||a.includes("Failed to fetch")||a.includes("CORS")||a.includes("403")||a.includes("请求超时")){const f=O.get(cacheKey);return f?f.data:null}throw console.error(`获取房间 ${c} 信息失败:`,r),r}}async function X(h){try{const{createPublicClient:c,http:r}=await u(async()=>{const{createPublicClient:n,http:o}=await import("./web3-DnWbColA.js").then(R=>R.aL);return{createPublicClient:n,http:o}},__vite__mapDeps([1,2,3])),{bscTestnet:a}=await u(async()=>{const{bscTestnet:n}=await import("./index-BDw84Puy.js").then(o=>o.m);return{bscTestnet:n}},__vite__mapDeps([0,1,2,3,4])),{getContractAddress:p}=await u(async()=>{const{getContractAddress:n}=await import("./index-BDw84Puy.js").then(o=>o.n);return{getContractAddress:n}},__vite__mapDeps([0,1,2,3,4])),{ABIS:f}=await u(async()=>{const{ABIS:n}=await import("./index-BDw84Puy.js").then(o=>o.o);return{ABIS:n}},__vite__mapDeps([0,1,2,3,4])),A=c({chain:a,transport:r()}),w=p(97,"GroupBuyRoom"),g=f.GroupBuyRoom;return{lotteryInfo:await A.readContract({address:w,abi:g,functionName:"getLotteryInfo",args:[BigInt(h)]})}}catch(c){throw console.error(`验证房间#${h} 合约状态失败:`,c),c}}export{X as debugRoomContractState,z as fetchRoom,j as fetchTotalRooms};
