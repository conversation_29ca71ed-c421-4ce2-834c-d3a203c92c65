const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-BDw84Puy.js","assets/web3-DnWbColA.js","assets/vendor-CgHzTxSQ.js","assets/ui-B9ZzSjJF.js","assets/index-QZjJZq-p.css","assets/enhancedGroupBuyService-_E3fW6SS.js"])))=>i.map(i=>d[i]);
import{fetchRoom as v,fetchTotalRooms as O}from"./basicOperations-DQxdkWLT.js";import{approveQPTForCreate as Q,approveUSDTForJoin as S,closeRoom as W,createRoomWithQPTVerification as x,createRoomWithQPTVerification as q,expireRoom as G,joinRoomWithPaymentVerification as U,joinRoomWithPaymentVerification as M,lockQPTForCreate as F}from"./roomManagement-D4JBlV07.js";import{_ as o}from"./web3-DnWbColA.js";import{c as H,d as $,e as J,b as z,a as K,g as X,f as Y}from"./rewardOperations-CoNCQb06.js";import"./vendor-CgHzTxSQ.js";import"./ui-B9ZzSjJF.js";async function y({chainId:_,roomId:d,winner:c,lotteryTxHash:n,lotteryTimestamp:m,signer:p}){try{if(!p)throw new Error("签名者未定义，请确保钱包已连接");if(!c||!n||!m)throw new Error("缺少必要参数：winner, lotteryTxHash, lotteryTimestamp");const{getContractAddress:i}=await o(async()=>{const{getContractAddress:t}=await import("./index-BDw84Puy.js").then(r=>r.n);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3,4])),w=i(_,"GroupBuyRoom"),{ABIS:h}=await o(async()=>{const{ABIS:t}=await import("./index-BDw84Puy.js").then(r=>r.o);return{ABIS:t}},__vite__mapDeps([0,1,2,3,4])),T=h.GroupBuyRoom,P=await p.writeContract({address:w,abi:T,functionName:"setWinner",args:[BigInt(d),c,n,BigInt(m)]}),{createPublicClient:C,http:l}=await o(async()=>{const{createPublicClient:t,http:r}=await import("./web3-DnWbColA.js").then(a=>a.aL);return{createPublicClient:t,http:r}},__vite__mapDeps([1,2,3])),{bscTestnet:e}=await o(async()=>{const{bscTestnet:t}=await import("./index-BDw84Puy.js").then(r=>r.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4]));return{receipt:await C({chain:e,transport:l()}).waitForTransactionReceipt({hash:P,timeout:6e4}),txHash:P}}catch(i){throw console.error("设置赢家失败:",i),i}}async function I({chainId:_,roomId:d}){try{const{getContractAddress:c}=await o(async()=>{const{getContractAddress:a}=await import("./index-BDw84Puy.js").then(A=>A.n);return{getContractAddress:a}},__vite__mapDeps([0,1,2,3,4])),{ABIS:n}=await o(async()=>{const{ABIS:a}=await import("./index-BDw84Puy.js").then(A=>A.o);return{ABIS:a}},__vite__mapDeps([0,1,2,3,4])),m=c(_,"QPTLocker"),p=n.QPTLocker,{createPublicClient:i,http:w}=await o(async()=>{const{createPublicClient:a,http:A}=await import("./web3-DnWbColA.js").then(b=>b.aL);return{createPublicClient:a,http:A}},__vite__mapDeps([1,2,3])),{bscTestnet:h}=await o(async()=>{const{bscTestnet:a}=await import("./index-BDw84Puy.js").then(A=>A.m);return{bscTestnet:a}},__vite__mapDeps([0,1,2,3,4])),P=await i({chain:h,transport:w()}).readContract({address:m,abi:p,functionName:"getRoomInfo",args:[BigInt(d)]}),[C,l,e,s,u]=P,t=Math.floor(Date.now()/1e3),r=Math.max(0,Number(e)-t);return{creator:C,amount:Number(l),unlockTime:Number(e),isSuccess:s,isClaimed:u,remainingTime:r,isUnlocked:r===0}}catch(c){return console.error("获取QPT锁仓信息失败:",c),{creator:"0x0000000000000000000000000000000000000000",amount:0,unlockTime:0,isSuccess:!1,isClaimed:!1,remainingTime:0,isUnlocked:!1}}}async function R({chainId:_,roomId:d,signer:c}){try{if(!c)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:n}=await o(async()=>{const{getContractAddress:e}=await import("./index-BDw84Puy.js").then(s=>s.n);return{getContractAddress:e}},__vite__mapDeps([0,1,2,3,4])),{ABIS:m}=await o(async()=>{const{ABIS:e}=await import("./index-BDw84Puy.js").then(s=>s.o);return{ABIS:e}},__vite__mapDeps([0,1,2,3,4])),p=n(_,"QPTLocker"),i=m.QPTLocker,w=await c.writeContract({address:p,abi:i,functionName:"claimLockedQPT",args:[BigInt(d)]}),{createPublicClient:h,http:T}=await o(async()=>{const{createPublicClient:e,http:s}=await import("./web3-DnWbColA.js").then(u=>u.aL);return{createPublicClient:e,http:s}},__vite__mapDeps([1,2,3])),{bscTestnet:P}=await o(async()=>{const{bscTestnet:e}=await import("./index-BDw84Puy.js").then(s=>s.m);return{bscTestnet:e}},__vite__mapDeps([0,1,2,3,4]));return{receipt:await h({chain:P,transport:T()}).waitForTransactionReceipt({hash:w,timeout:6e4}),txHash:w}}catch(n){throw console.error("领取锁定QPT失败:",n),n}}async function L({chainId:_,roomId:d,userAddress:c}){try{const{getUserPaidAmount:n}=await o(async()=>{const{getUserPaidAmount:p}=await import("./enhancedGroupBuyService-_E3fW6SS.js");return{getUserPaidAmount:p}},__vite__mapDeps([5,1,2,3,0,4]));return await n(d,c)}catch(n){return console.error("获取用户支付金额失败:",n),"0"}}async function B({chainId:_,roomId:d,userAddress:c,role:n}){try{const{getContractAddress:m}=await o(async()=>{const{getContractAddress:r}=await import("./index-BDw84Puy.js").then(a=>a.n);return{getContractAddress:r}},__vite__mapDeps([0,1,2,3,4])),{ABIS:p}=await o(async()=>{const{ABIS:r}=await import("./index-BDw84Puy.js").then(a=>a.o);return{ABIS:r}},__vite__mapDeps([0,1,2,3,4])),i=m(_,"GroupBuyRoom"),w=p.GroupBuyRoom,{createPublicClient:h,http:T}=await o(async()=>{const{createPublicClient:r,http:a}=await import("./web3-DnWbColA.js").then(A=>A.aL);return{createPublicClient:r,http:a}},__vite__mapDeps([1,2,3])),{bscTestnet:P}=await o(async()=>{const{bscTestnet:r}=await import("./index-BDw84Puy.js").then(a=>a.m);return{bscTestnet:r}},__vite__mapDeps([0,1,2,3,4])),C=h({chain:P,transport:T()}),[l,e,s,u]=await C.readContract({address:i,abi:w,functionName:"getUserClaimStatus",args:[BigInt(d),c]});let t;switch(n){case"creator":t=l;break;case"winner":try{const[r,a]=await C.readContract({address:i,abi:w,functionName:"getWinnerRewardStatus",args:[BigInt(d),c]});t=r||a}catch{t=!1}break;case"participant":t=e;break;default:throw new Error(`不支持的角色类型: ${n}`)}return t}catch(m){return m.message?.includes("not found on ABI"),!1}}async function D({chainId:_,roomId:d,winnerAddress:c}){try{if(_!==97)throw new Error(`不支持的链ID: ${_}`);const{getContractAddress:n}=await o(async()=>{const{getContractAddress:u}=await import("./index-BDw84Puy.js").then(t=>t.n);return{getContractAddress:u}},__vite__mapDeps([0,1,2,3,4])),{ABIS:m}=await o(async()=>{const{ABIS:u}=await import("./index-BDw84Puy.js").then(t=>t.o);return{ABIS:u}},__vite__mapDeps([0,1,2,3,4])),p=n(_,"QPTLocker"),i=n(_,"GroupBuyRoom"),w=m.QPTLocker,h=m.GroupBuyRoom,{createPublicClient:T,http:P}=await o(async()=>{const{createPublicClient:u,http:t}=await import("./web3-DnWbColA.js").then(r=>r.aL);return{createPublicClient:u,http:t}},__vite__mapDeps([1,2,3])),{bscTestnet:C}=await o(async()=>{const{bscTestnet:u}=await import("./index-BDw84Puy.js").then(t=>t.m);return{bscTestnet:u}},__vite__mapDeps([0,1,2,3,4])),l=T({chain:C,transport:P()});let e=0,s=0;try{const t=(await l.readContract({address:i,abi:h,functionName:"rooms",args:[BigInt(d)]}))[1],r=await l.readContract({address:p,abi:w,functionName:"amountMappings",args:[t]});e=Number(r[2])/1e18;const a=await l.readContract({address:i,abi:h,functionName:"tierPoints",args:[t]}).catch(()=>0n);s=Number(a)/1e6}catch{try{const r=(await l.readContract({address:i,abi:h,functionName:"rooms",args:[BigInt(d)]}))[1],a=await l.readContract({address:p,abi:w,functionName:"amountMappings",args:[r]}).catch(()=>null);a&&(e=Number(a[2])/1e18);const A=await l.readContract({address:i,abi:h,functionName:"tierPoints",args:[r]}).catch(()=>0n);s=Number(A)/1e6}catch(t){console.warn("备用方法查询赢家奖励失败:",t)}}return{qptAmount:e,pointsAmount:s}}catch(n){return console.error("获取赢家奖励数据失败:",n),{qptAmount:0,pointsAmount:0}}}export{Q as approveQPTForCreate,S as approveUSDTForJoin,B as checkRoleClaimStatus,H as claimCreatorCommission,R as claimLockedQPT,$ as claimParticipantRefund,J as claimReward,z as claimWinnerPoints,K as claimWinnerQPT,W as closeRoom,x as createRoom,q as createRoomWithQPTVerification,G as expireRoom,v as fetchRoom,O as fetchTotalRooms,X as getCreatorCommissionAmount,Y as getParticipantRefundAmount,I as getQPTLockInfo,L as getUserPaidAmount,D as getWinnerRewards,U as joinRoom,M as joinRoomWithPaymentVerification,F as lockQPTForCreate,y as setWinner};
