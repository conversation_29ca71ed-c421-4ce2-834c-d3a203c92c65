(function(vr,Ke){typeof exports=="object"&&typeof module<"u"?Ke(exports):typeof define=="function"&&define.amd?define(["exports"],Ke):(vr=typeof globalThis<"u"?globalThis:vr||self,Ke(vr["@walletconnect/universal-provider"]={}))})(this,function(vr){"use strict";var Ke=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function rg(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Ic(t){var e=t.default;if(typeof e=="function"){var r=function(){return e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach(function(n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(r,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}),r}var Ue={exports:{}},Wr=typeof Reflect=="object"?Reflect:null,Sc=Wr&&typeof Wr.apply=="function"?Wr.apply:function(e,r,n){return Function.prototype.apply.call(e,r,n)},Ri;Wr&&typeof Wr.ownKeys=="function"?Ri=Wr.ownKeys:Object.getOwnPropertySymbols?Ri=function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Ri=function(e){return Object.getOwnPropertyNames(e)};function ng(t){console&&console.warn&&console.warn(t)}var xc=Number.isNaN||function(e){return e!==e};function re(){re.init.call(this)}Ue.exports=re,Ue.exports.once=ag,re.EventEmitter=re,re.prototype._events=void 0,re.prototype._eventsCount=0,re.prototype._maxListeners=void 0;var Oc=10;function Ti(t){if(typeof t!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}Object.defineProperty(re,"defaultMaxListeners",{enumerable:!0,get:function(){return Oc},set:function(t){if(typeof t!="number"||t<0||xc(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");Oc=t}}),re.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},re.prototype.setMaxListeners=function(e){if(typeof e!="number"||e<0||xc(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this};function Dc(t){return t._maxListeners===void 0?re.defaultMaxListeners:t._maxListeners}re.prototype.getMaxListeners=function(){return Dc(this)},re.prototype.emit=function(e){for(var r=[],n=1;n<arguments.length;n++)r.push(arguments[n]);var i=e==="error",s=this._events;if(s!==void 0)i=i&&s.error===void 0;else if(!i)return!1;if(i){var o;if(r.length>0&&(o=r[0]),o instanceof Error)throw o;var a=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var c=s[e];if(c===void 0)return!1;if(typeof c=="function")Sc(c,this,r);else for(var u=c.length,l=Nc(c,u),n=0;n<u;++n)Sc(l[n],this,r);return!0};function $c(t,e,r,n){var i,s,o;if(Ti(r),s=t._events,s===void 0?(s=t._events=Object.create(null),t._eventsCount=0):(s.newListener!==void 0&&(t.emit("newListener",e,r.listener?r.listener:r),s=t._events),o=s[e]),o===void 0)o=s[e]=r,++t._eventsCount;else if(typeof o=="function"?o=s[e]=n?[r,o]:[o,r]:n?o.unshift(r):o.push(r),i=Dc(t),i>0&&o.length>i&&!o.warned){o.warned=!0;var a=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");a.name="MaxListenersExceededWarning",a.emitter=t,a.type=e,a.count=o.length,ng(a)}return t}re.prototype.addListener=function(e,r){return $c(this,e,r,!1)},re.prototype.on=re.prototype.addListener,re.prototype.prependListener=function(e,r){return $c(this,e,r,!0)};function ig(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Ac(t,e,r){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},i=ig.bind(n);return i.listener=r,n.wrapFn=i,i}re.prototype.once=function(e,r){return Ti(r),this.on(e,Ac(this,e,r)),this},re.prototype.prependOnceListener=function(e,r){return Ti(r),this.prependListener(e,Ac(this,e,r)),this},re.prototype.removeListener=function(e,r){var n,i,s,o,a;if(Ti(r),i=this._events,i===void 0)return this;if(n=i[e],n===void 0)return this;if(n===r||n.listener===r)--this._eventsCount===0?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,n.listener||r));else if(typeof n!="function"){for(s=-1,o=n.length-1;o>=0;o--)if(n[o]===r||n[o].listener===r){a=n[o].listener,s=o;break}if(s<0)return this;s===0?n.shift():sg(n,s),n.length===1&&(i[e]=n[0]),i.removeListener!==void 0&&this.emit("removeListener",e,a||r)}return this},re.prototype.off=re.prototype.removeListener,re.prototype.removeAllListeners=function(e){var r,n,i;if(n=this._events,n===void 0)return this;if(n.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):n[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete n[e]),this;if(arguments.length===0){var s=Object.keys(n),o;for(i=0;i<s.length;++i)o=s[i],o!=="removeListener"&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(r=n[e],typeof r=="function")this.removeListener(e,r);else if(r!==void 0)for(i=r.length-1;i>=0;i--)this.removeListener(e,r[i]);return this};function Pc(t,e,r){var n=t._events;if(n===void 0)return[];var i=n[e];return i===void 0?[]:typeof i=="function"?r?[i.listener||i]:[i]:r?og(i):Nc(i,i.length)}re.prototype.listeners=function(e){return Pc(this,e,!0)},re.prototype.rawListeners=function(e){return Pc(this,e,!1)},re.listenerCount=function(t,e){return typeof t.listenerCount=="function"?t.listenerCount(e):Cc.call(t,e)},re.prototype.listenerCount=Cc;function Cc(t){var e=this._events;if(e!==void 0){var r=e[t];if(typeof r=="function")return 1;if(r!==void 0)return r.length}return 0}re.prototype.eventNames=function(){return this._eventsCount>0?Ri(this._events):[]};function Nc(t,e){for(var r=new Array(e),n=0;n<e;++n)r[n]=t[n];return r}function sg(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}function og(t){for(var e=new Array(t.length),r=0;r<e.length;++r)e[r]=t[r].listener||t[r];return e}function ag(t,e){return new Promise(function(r,n){function i(o){t.removeListener(e,s),n(o)}function s(){typeof t.removeListener=="function"&&t.removeListener("error",i),r([].slice.call(arguments))}Rc(t,e,s,{once:!0}),e!=="error"&&cg(t,i,{once:!0})})}function cg(t,e,r){typeof t.on=="function"&&Rc(t,"error",e,r)}function Rc(t,e,r,n){if(typeof t.on=="function")n.once?t.once(e,r):t.on(e,r);else if(typeof t.addEventListener=="function")t.addEventListener(e,function i(s){n.once&&t.removeEventListener(e,i),r(s)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t)}var L={};/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */var Vs=function(t,e){return Vs=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)n.hasOwnProperty(i)&&(r[i]=n[i])},Vs(t,e)};function ug(t,e){Vs(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}var Ks=function(){return Ks=Object.assign||function(e){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},Ks.apply(this,arguments)};function lg(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]]);return r}function hg(t,e,r,n){var i=arguments.length,s=i<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,r):n,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s}function dg(t,e){return function(r,n){e(r,n,t)}}function fg(t,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(t,e)}function pg(t,e,r,n){function i(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function a(l){try{u(n.next(l))}catch(h){o(h)}}function c(l){try{u(n.throw(l))}catch(h){o(h)}}function u(l){l.done?s(l.value):i(l.value).then(a,c)}u((n=n.apply(t,e||[])).next())})}function gg(t,e){var r={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},n,i,s,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(u){return function(l){return c([u,l])}}function c(u){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,i&&(s=u[0]&2?i.return:u[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,u[1])).done)return s;switch(i=0,s&&(u=[u[0]&2,s.value]),u[0]){case 0:case 1:s=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,i=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(s=r.trys,!(s=s.length>0&&s[s.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!s||u[1]>s[0]&&u[1]<s[3])){r.label=u[1];break}if(u[0]===6&&r.label<s[1]){r.label=s[1],s=u;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(u);break}s[2]&&r.ops.pop(),r.trys.pop();continue}u=e.call(t,r)}catch(l){u=[6,l],i=0}finally{n=s=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function yg(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}function mg(t,e){for(var r in t)r!=="default"&&!e.hasOwnProperty(r)&&(e[r]=t[r])}function Ws(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Tc(t,e){var r=typeof Symbol=="function"&&t[Symbol.iterator];if(!r)return t;var n=r.call(t),i,s=[],o;try{for(;(e===void 0||e-- >0)&&!(i=n.next()).done;)s.push(i.value)}catch(a){o={error:a}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(o)throw o.error}}return s}function wg(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(Tc(arguments[e]));return t}function bg(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;for(var n=Array(t),i=0,e=0;e<r;e++)for(var s=arguments[e],o=0,a=s.length;o<a;o++,i++)n[i]=s[o];return n}function zn(t){return this instanceof zn?(this.v=t,this):new zn(t)}function vg(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(t,e||[]),i,s=[];return i={},o("next"),o("throw"),o("return"),i[Symbol.asyncIterator]=function(){return this},i;function o(d){n[d]&&(i[d]=function(f){return new Promise(function(p,y){s.push([d,f,p,y])>1||a(d,f)})})}function a(d,f){try{c(n[d](f))}catch(p){h(s[0][3],p)}}function c(d){d.value instanceof zn?Promise.resolve(d.value.v).then(u,l):h(s[0][2],d)}function u(d){a("next",d)}function l(d){a("throw",d)}function h(d,f){d(f),s.shift(),s.length&&a(s[0][0],s[0][1])}}function Eg(t){var e,r;return e={},n("next"),n("throw",function(i){throw i}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(i,s){e[i]=t[i]?function(o){return(r=!r)?{value:zn(t[i](o)),done:i==="return"}:s?s(o):o}:s}}function _g(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof Ws=="function"?Ws(t):t[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(s){r[s]=t[s]&&function(o){return new Promise(function(a,c){o=t[s](o),i(a,c,o.done,o.value)})}}function i(s,o,a,c){Promise.resolve(c).then(function(u){s({value:u,done:a})},o)}}function Ig(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function Sg(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function xg(t){return t&&t.__esModule?t:{default:t}}function Og(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function Dg(t,e,r){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,r),r}var $g=Object.freeze({__proto__:null,__extends:ug,get __assign(){return Ks},__rest:lg,__decorate:hg,__param:dg,__metadata:fg,__awaiter:pg,__generator:gg,__createBinding:yg,__exportStar:mg,__values:Ws,__read:Tc,__spread:wg,__spreadArrays:bg,__await:zn,__asyncGenerator:vg,__asyncDelegator:Eg,__asyncValues:_g,__makeTemplateObject:Ig,__importStar:Sg,__importDefault:xg,__classPrivateFieldGet:Og,__classPrivateFieldSet:Dg}),Bi=Ic($g),Gs={},Hn={},Bc;function Ag(){if(Bc)return Hn;Bc=1,Object.defineProperty(Hn,"__esModule",{value:!0}),Hn.delay=void 0;function t(e){return new Promise(r=>{setTimeout(()=>{r(!0)},e)})}return Hn.delay=t,Hn}var Er={},Ys={},_r={},Fc;function Pg(){return Fc||(Fc=1,Object.defineProperty(_r,"__esModule",{value:!0}),_r.ONE_THOUSAND=_r.ONE_HUNDRED=void 0,_r.ONE_HUNDRED=100,_r.ONE_THOUSAND=1e3),_r}var Zs={},Lc;function Cg(){return Lc||(Lc=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.ONE_YEAR=t.FOUR_WEEKS=t.THREE_WEEKS=t.TWO_WEEKS=t.ONE_WEEK=t.THIRTY_DAYS=t.SEVEN_DAYS=t.FIVE_DAYS=t.THREE_DAYS=t.ONE_DAY=t.TWENTY_FOUR_HOURS=t.TWELVE_HOURS=t.SIX_HOURS=t.THREE_HOURS=t.ONE_HOUR=t.SIXTY_MINUTES=t.THIRTY_MINUTES=t.TEN_MINUTES=t.FIVE_MINUTES=t.ONE_MINUTE=t.SIXTY_SECONDS=t.THIRTY_SECONDS=t.TEN_SECONDS=t.FIVE_SECONDS=t.ONE_SECOND=void 0,t.ONE_SECOND=1,t.FIVE_SECONDS=5,t.TEN_SECONDS=10,t.THIRTY_SECONDS=30,t.SIXTY_SECONDS=60,t.ONE_MINUTE=t.SIXTY_SECONDS,t.FIVE_MINUTES=t.ONE_MINUTE*5,t.TEN_MINUTES=t.ONE_MINUTE*10,t.THIRTY_MINUTES=t.ONE_MINUTE*30,t.SIXTY_MINUTES=t.ONE_MINUTE*60,t.ONE_HOUR=t.SIXTY_MINUTES,t.THREE_HOURS=t.ONE_HOUR*3,t.SIX_HOURS=t.ONE_HOUR*6,t.TWELVE_HOURS=t.ONE_HOUR*12,t.TWENTY_FOUR_HOURS=t.ONE_HOUR*24,t.ONE_DAY=t.TWENTY_FOUR_HOURS,t.THREE_DAYS=t.ONE_DAY*3,t.FIVE_DAYS=t.ONE_DAY*5,t.SEVEN_DAYS=t.ONE_DAY*7,t.THIRTY_DAYS=t.ONE_DAY*30,t.ONE_WEEK=t.SEVEN_DAYS,t.TWO_WEEKS=t.ONE_WEEK*2,t.THREE_WEEKS=t.ONE_WEEK*3,t.FOUR_WEEKS=t.ONE_WEEK*4,t.ONE_YEAR=t.ONE_DAY*365}(Zs)),Zs}var Uc;function jc(){return Uc||(Uc=1,function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=Bi;e.__exportStar(Pg(),t),e.__exportStar(Cg(),t)}(Ys)),Ys}var qc;function Ng(){if(qc)return Er;qc=1,Object.defineProperty(Er,"__esModule",{value:!0}),Er.fromMiliseconds=Er.toMiliseconds=void 0;const t=jc();function e(n){return n*t.ONE_THOUSAND}Er.toMiliseconds=e;function r(n){return Math.floor(n/t.ONE_THOUSAND)}return Er.fromMiliseconds=r,Er}var kc;function Rg(){return kc||(kc=1,function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=Bi;e.__exportStar(Ag(),t),e.__exportStar(Ng(),t)}(Gs)),Gs}var Gr={},Mc;function Tg(){if(Mc)return Gr;Mc=1,Object.defineProperty(Gr,"__esModule",{value:!0}),Gr.Watch=void 0;class t{constructor(){this.timestamps=new Map}start(r){if(this.timestamps.has(r))throw new Error(`Watch already started for label: ${r}`);this.timestamps.set(r,{started:Date.now()})}stop(r){const n=this.get(r);if(typeof n.elapsed<"u")throw new Error(`Watch already stopped for label: ${r}`);const i=Date.now()-n.started;this.timestamps.set(r,{started:n.started,elapsed:i})}get(r){const n=this.timestamps.get(r);if(typeof n>"u")throw new Error(`No timestamp found for label: ${r}`);return n}elapsed(r){const n=this.get(r);return n.elapsed||Date.now()-n.started}}return Gr.Watch=t,Gr.default=t,Gr}var Js={},Vn={},zc;function Bg(){if(zc)return Vn;zc=1,Object.defineProperty(Vn,"__esModule",{value:!0}),Vn.IWatch=void 0;class t{}return Vn.IWatch=t,Vn}var Hc;function Fg(){return Hc||(Hc=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),Bi.__exportStar(Bg(),t)}(Js)),Js}(function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=Bi;e.__exportStar(Rg(),t),e.__exportStar(Tg(),t),e.__exportStar(Fg(),t),e.__exportStar(jc(),t)})(L);class Ir{}class Lg extends Ir{constructor(e){super()}}const Vc=L.FIVE_SECONDS,Sr={pulse:"heartbeat_pulse"};class Xs extends Lg{constructor(e){super(e),this.events=new Ue.exports.EventEmitter,this.interval=Vc,this.interval=e?.interval||Vc}static async init(e){const r=new Xs(e);return await r.init(),r}async init(){await this.initialize()}stop(){clearInterval(this.intervalRef)}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async initialize(){this.intervalRef=setInterval(()=>this.pulse(),L.toMiliseconds(this.interval))}pulse(){this.events.emit(Sr.pulse)}}const Ug=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,jg=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,qg=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function kg(t,e){if(t==="__proto__"||t==="constructor"&&e&&typeof e=="object"&&"prototype"in e){Mg(t);return}return e}function Mg(t){console.warn(`[destr] Dropping "${t}" key to prevent prototype pollution.`)}function Fi(t,e={}){if(typeof t!="string")return t;if(t[0]==='"'&&t[t.length-1]==='"'&&t.indexOf("\\")===-1)return t.slice(1,-1);const r=t.trim();if(r.length<=9)switch(r.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!qg.test(t)){if(e.strict)throw new SyntaxError("[destr] Invalid JSON");return t}try{if(Ug.test(t)||jg.test(t)){if(e.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(t,kg)}return JSON.parse(t)}catch(n){if(e.strict)throw n;return t}}function zg(t){return!t||typeof t.then!="function"?Promise.resolve(t):t}function Ee(t,...e){try{return zg(t(...e))}catch(r){return Promise.reject(r)}}function Hg(t){const e=typeof t;return t===null||e!=="object"&&e!=="function"}function Vg(t){const e=Object.getPrototypeOf(t);return!e||e.isPrototypeOf(Object)}function Li(t){if(Hg(t))return String(t);if(Vg(t)||Array.isArray(t))return JSON.stringify(t);if(typeof t.toJSON=="function")return Li(t.toJSON());throw new Error("[unstorage] Cannot stringify value!")}const Qs="base64:";function Kg(t){return typeof t=="string"?t:Qs+Yg(t)}function Wg(t){return typeof t!="string"||!t.startsWith(Qs)?t:Gg(t.slice(Qs.length))}function Gg(t){return globalThis.Buffer?Buffer.from(t,"base64"):Uint8Array.from(globalThis.atob(t),e=>e.codePointAt(0))}function Yg(t){return globalThis.Buffer?Buffer.from(t).toString("base64"):globalThis.btoa(String.fromCodePoint(...t))}function We(t){return t&&t.split("?")[0]?.replace(/[/\\]/g,":").replace(/:+/g,":").replace(/^:|:$/g,"")||""}function Zg(...t){return We(t.join(":"))}function Ui(t){return t=We(t),t?t+":":""}function Jg(t,e){if(e===void 0)return!0;let r=0,n=t.indexOf(":");for(;n>-1;)r++,n=t.indexOf(":",n+1);return r<=e}function Xg(t,e){return e?t.startsWith(e)&&t[t.length-1]!=="$":t[t.length-1]!=="$"}function qP(t){return t}const Qg="memory",ey=()=>{const t=new Map;return{name:Qg,getInstance:()=>t,hasItem(e){return t.has(e)},getItem(e){return t.get(e)??null},getItemRaw(e){return t.get(e)??null},setItem(e,r){t.set(e,r)},setItemRaw(e,r){t.set(e,r)},removeItem(e){t.delete(e)},getKeys(){return[...t.keys()]},clear(){t.clear()},dispose(){t.clear()}}};function ty(t={}){const e={mounts:{"":t.driver||ey()},mountpoints:[""],watching:!1,watchListeners:[],unwatch:{}},r=u=>{for(const l of e.mountpoints)if(u.startsWith(l))return{base:l,relativeKey:u.slice(l.length),driver:e.mounts[l]};return{base:"",relativeKey:u,driver:e.mounts[""]}},n=(u,l)=>e.mountpoints.filter(h=>h.startsWith(u)||l&&u.startsWith(h)).map(h=>({relativeBase:u.length>h.length?u.slice(h.length):void 0,mountpoint:h,driver:e.mounts[h]})),i=(u,l)=>{if(e.watching){l=We(l);for(const h of e.watchListeners)h(u,l)}},s=async()=>{if(!e.watching){e.watching=!0;for(const u in e.mounts)e.unwatch[u]=await Kc(e.mounts[u],i,u)}},o=async()=>{if(e.watching){for(const u in e.unwatch)await e.unwatch[u]();e.unwatch={},e.watching=!1}},a=(u,l,h)=>{const d=new Map,f=p=>{let y=d.get(p.base);return y||(y={driver:p.driver,base:p.base,items:[]},d.set(p.base,y)),y};for(const p of u){const y=typeof p=="string",g=We(y?p:p.key),v=y?void 0:p.value,b=y||!p.options?l:{...l,...p.options},E=r(g);f(E).items.push({key:g,value:v,relativeKey:E.relativeKey,options:b})}return Promise.all([...d.values()].map(p=>h(p))).then(p=>p.flat())},c={hasItem(u,l={}){u=We(u);const{relativeKey:h,driver:d}=r(u);return Ee(d.hasItem,h,l)},getItem(u,l={}){u=We(u);const{relativeKey:h,driver:d}=r(u);return Ee(d.getItem,h,l).then(f=>Fi(f))},getItems(u,l={}){return a(u,l,h=>h.driver.getItems?Ee(h.driver.getItems,h.items.map(d=>({key:d.relativeKey,options:d.options})),l).then(d=>d.map(f=>({key:Zg(h.base,f.key),value:Fi(f.value)}))):Promise.all(h.items.map(d=>Ee(h.driver.getItem,d.relativeKey,d.options).then(f=>({key:d.key,value:Fi(f)})))))},getItemRaw(u,l={}){u=We(u);const{relativeKey:h,driver:d}=r(u);return d.getItemRaw?Ee(d.getItemRaw,h,l):Ee(d.getItem,h,l).then(f=>Wg(f))},async setItem(u,l,h={}){if(l===void 0)return c.removeItem(u);u=We(u);const{relativeKey:d,driver:f}=r(u);f.setItem&&(await Ee(f.setItem,d,Li(l),h),f.watch||i("update",u))},async setItems(u,l){await a(u,l,async h=>{if(h.driver.setItems)return Ee(h.driver.setItems,h.items.map(d=>({key:d.relativeKey,value:Li(d.value),options:d.options})),l);h.driver.setItem&&await Promise.all(h.items.map(d=>Ee(h.driver.setItem,d.relativeKey,Li(d.value),d.options)))})},async setItemRaw(u,l,h={}){if(l===void 0)return c.removeItem(u,h);u=We(u);const{relativeKey:d,driver:f}=r(u);if(f.setItemRaw)await Ee(f.setItemRaw,d,l,h);else if(f.setItem)await Ee(f.setItem,d,Kg(l),h);else return;f.watch||i("update",u)},async removeItem(u,l={}){typeof l=="boolean"&&(l={removeMeta:l}),u=We(u);const{relativeKey:h,driver:d}=r(u);d.removeItem&&(await Ee(d.removeItem,h,l),(l.removeMeta||l.removeMata)&&await Ee(d.removeItem,h+"$",l),d.watch||i("remove",u))},async getMeta(u,l={}){typeof l=="boolean"&&(l={nativeOnly:l}),u=We(u);const{relativeKey:h,driver:d}=r(u),f=Object.create(null);if(d.getMeta&&Object.assign(f,await Ee(d.getMeta,h,l)),!l.nativeOnly){const p=await Ee(d.getItem,h+"$",l).then(y=>Fi(y));p&&typeof p=="object"&&(typeof p.atime=="string"&&(p.atime=new Date(p.atime)),typeof p.mtime=="string"&&(p.mtime=new Date(p.mtime)),Object.assign(f,p))}return f},setMeta(u,l,h={}){return this.setItem(u+"$",l,h)},removeMeta(u,l={}){return this.removeItem(u+"$",l)},async getKeys(u,l={}){u=Ui(u);const h=n(u,!0);let d=[];const f=[];let p=!0;for(const g of h){g.driver.flags?.maxDepth||(p=!1);const v=await Ee(g.driver.getKeys,g.relativeBase,l);for(const b of v){const E=g.mountpoint+We(b);d.some(_=>E.startsWith(_))||f.push(E)}d=[g.mountpoint,...d.filter(b=>!b.startsWith(g.mountpoint))]}const y=l.maxDepth!==void 0&&!p;return f.filter(g=>(!y||Jg(g,l.maxDepth))&&Xg(g,u))},async clear(u,l={}){u=Ui(u),await Promise.all(n(u,!1).map(async h=>{if(h.driver.clear)return Ee(h.driver.clear,h.relativeBase,l);if(h.driver.removeItem){const d=await h.driver.getKeys(h.relativeBase||"",l);return Promise.all(d.map(f=>h.driver.removeItem(f,l)))}}))},async dispose(){await Promise.all(Object.values(e.mounts).map(u=>Wc(u)))},async watch(u){return await s(),e.watchListeners.push(u),async()=>{e.watchListeners=e.watchListeners.filter(l=>l!==u),e.watchListeners.length===0&&await o()}},async unwatch(){e.watchListeners=[],await o()},mount(u,l){if(u=Ui(u),u&&e.mounts[u])throw new Error(`already mounted at ${u}`);return u&&(e.mountpoints.push(u),e.mountpoints.sort((h,d)=>d.length-h.length)),e.mounts[u]=l,e.watching&&Promise.resolve(Kc(l,i,u)).then(h=>{e.unwatch[u]=h}).catch(console.error),c},async unmount(u,l=!0){u=Ui(u),!(!u||!e.mounts[u])&&(e.watching&&u in e.unwatch&&(e.unwatch[u]?.(),delete e.unwatch[u]),l&&await Wc(e.mounts[u]),e.mountpoints=e.mountpoints.filter(h=>h!==u),delete e.mounts[u])},getMount(u=""){u=We(u)+":";const l=r(u);return{driver:l.driver,base:l.base}},getMounts(u="",l={}){return u=We(u),n(u,l.parents).map(d=>({driver:d.driver,base:d.mountpoint}))},keys:(u,l={})=>c.getKeys(u,l),get:(u,l={})=>c.getItem(u,l),set:(u,l,h={})=>c.setItem(u,l,h),has:(u,l={})=>c.hasItem(u,l),del:(u,l={})=>c.removeItem(u,l),remove:(u,l={})=>c.removeItem(u,l)};return c}function Kc(t,e,r){return t.watch?t.watch((n,i)=>e(n,r+i)):()=>{}}async function Wc(t){typeof t.dispose=="function"&&await Ee(t.dispose)}function xr(t){return new Promise((e,r)=>{t.oncomplete=t.onsuccess=()=>e(t.result),t.onabort=t.onerror=()=>r(t.error)})}function Gc(t,e){const r=indexedDB.open(t);r.onupgradeneeded=()=>r.result.createObjectStore(e);const n=xr(r);return(i,s)=>n.then(o=>s(o.transaction(e,i).objectStore(e)))}let eo;function Kn(){return eo||(eo=Gc("keyval-store","keyval")),eo}function Yc(t,e=Kn()){return e("readonly",r=>xr(r.get(t)))}function ry(t,e,r=Kn()){return r("readwrite",n=>(n.put(e,t),xr(n.transaction)))}function ny(t,e=Kn()){return e("readwrite",r=>(r.delete(t),xr(r.transaction)))}function iy(t=Kn()){return t("readwrite",e=>(e.clear(),xr(e.transaction)))}function sy(t,e){return t.openCursor().onsuccess=function(){this.result&&(e(this.result),this.result.continue())},xr(t.transaction)}function oy(t=Kn()){return t("readonly",e=>{if(e.getAllKeys)return xr(e.getAllKeys());const r=[];return sy(e,n=>r.push(n.key)).then(()=>r)})}const ay=t=>JSON.stringify(t,(e,r)=>typeof r=="bigint"?r.toString()+"n":r),cy=t=>{const e=/([\[:])?(\d{17,}|(?:[9](?:[1-9]07199254740991|0[1-9]7199254740991|00[8-9]199254740991|007[2-9]99254740991|007199[3-9]54740991|0071992[6-9]4740991|00719925[5-9]740991|007199254[8-9]40991|0071992547[5-9]0991|00719925474[1-9]991|00719925474099[2-9])))([,\}\]])/g,r=t.replace(e,'$1"$2n"$3');return JSON.parse(r,(n,i)=>typeof i=="string"&&i.match(/^\d+n$/)?BigInt(i.substring(0,i.length-1)):i)};function Or(t){if(typeof t!="string")throw new Error(`Cannot safe json parse value of type ${typeof t}`);try{return cy(t)}catch{return t}}function Ut(t){return typeof t=="string"?t:ay(t)||""}const uy="idb-keyval";var ly=(t={})=>{const e=t.base&&t.base.length>0?`${t.base}:`:"",r=i=>e+i;let n;return t.dbName&&t.storeName&&(n=Gc(t.dbName,t.storeName)),{name:uy,options:t,async hasItem(i){return!(typeof await Yc(r(i),n)>"u")},async getItem(i){return await Yc(r(i),n)??null},setItem(i,s){return ry(r(i),s,n)},removeItem(i){return ny(r(i),n)},getKeys(){return oy(n)},clear(){return iy(n)}}};const hy="WALLET_CONNECT_V2_INDEXED_DB",dy="keyvaluestorage";class fy{constructor(){this.indexedDb=ty({driver:ly({dbName:hy,storeName:dy})})}async getKeys(){return this.indexedDb.getKeys()}async getEntries(){return(await this.indexedDb.getItems(await this.indexedDb.getKeys())).map(e=>[e.key,e.value])}async getItem(e){const r=await this.indexedDb.getItem(e);if(r!==null)return r}async setItem(e,r){await this.indexedDb.setItem(e,Ut(r))}async removeItem(e){await this.indexedDb.removeItem(e)}}var to=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ji={exports:{}};(function(){let t;function e(){}t=e,t.prototype.getItem=function(r){return this.hasOwnProperty(r)?String(this[r]):null},t.prototype.setItem=function(r,n){this[r]=String(n)},t.prototype.removeItem=function(r){delete this[r]},t.prototype.clear=function(){const r=this;Object.keys(r).forEach(function(n){r[n]=void 0,delete r[n]})},t.prototype.key=function(r){return r=r||0,Object.keys(this)[r]},t.prototype.__defineGetter__("length",function(){return Object.keys(this).length}),typeof to<"u"&&to.localStorage?ji.exports=to.localStorage:typeof window<"u"&&window.localStorage?ji.exports=window.localStorage:ji.exports=new e})();function py(t){var e;return[t[0],Or((e=t[1])!=null?e:"")]}class gy{constructor(){this.localStorage=ji.exports}async getKeys(){return Object.keys(this.localStorage)}async getEntries(){return Object.entries(this.localStorage).map(py)}async getItem(e){const r=this.localStorage.getItem(e);if(r!==null)return Or(r)}async setItem(e,r){this.localStorage.setItem(e,Ut(r))}async removeItem(e){this.localStorage.removeItem(e)}}const yy="wc_storage_version",Zc=1,my=async(t,e,r)=>{const n=yy,i=await e.getItem(n);if(i&&i>=Zc){r(e);return}const s=await t.getKeys();if(!s.length){r(e);return}const o=[];for(;s.length;){const a=s.shift();if(!a)continue;const c=a.toLowerCase();if(c.includes("wc@")||c.includes("walletconnect")||c.includes("wc_")||c.includes("wallet_connect")){const u=await t.getItem(a);await e.setItem(a,u),o.push(a)}}await e.setItem(n,Zc),r(e),wy(t,o)},wy=async(t,e)=>{e.length&&e.forEach(async r=>{await t.removeItem(r)})};class by{constructor(){this.initialized=!1,this.setInitialized=r=>{this.storage=r,this.initialized=!0};const e=new gy;this.storage=e;try{const r=new fy;my(e,r,this.setInitialized)}catch{this.initialized=!0}}async getKeys(){return await this.initialize(),this.storage.getKeys()}async getEntries(){return await this.initialize(),this.storage.getEntries()}async getItem(e){return await this.initialize(),this.storage.getItem(e)}async setItem(e,r){return await this.initialize(),this.storage.setItem(e,r)}async removeItem(e){return await this.initialize(),this.storage.removeItem(e)}async initialize(){this.initialized||await new Promise(e=>{const r=setInterval(()=>{this.initialized&&(clearInterval(r),e())},20)})}}function vy(t){try{return JSON.stringify(t)}catch{return'"[Circular]"'}}var Ey=_y;function _y(t,e,r){var n=r&&r.stringify||vy,i=1;if(typeof t=="object"&&t!==null){var s=e.length+i;if(s===1)return t;var o=new Array(s);o[0]=n(t);for(var a=1;a<s;a++)o[a]=n(e[a]);return o.join(" ")}if(typeof t!="string")return t;var c=e.length;if(c===0)return t;for(var u="",l=1-i,h=-1,d=t&&t.length||0,f=0;f<d;){if(t.charCodeAt(f)===37&&f+1<d){switch(h=h>-1?h:0,t.charCodeAt(f+1)){case 100:case 102:if(l>=c||e[l]==null)break;h<f&&(u+=t.slice(h,f)),u+=Number(e[l]),h=f+2,f++;break;case 105:if(l>=c||e[l]==null)break;h<f&&(u+=t.slice(h,f)),u+=Math.floor(Number(e[l])),h=f+2,f++;break;case 79:case 111:case 106:if(l>=c||e[l]===void 0)break;h<f&&(u+=t.slice(h,f));var p=typeof e[l];if(p==="string"){u+="'"+e[l]+"'",h=f+2,f++;break}if(p==="function"){u+=e[l].name||"<anonymous>",h=f+2,f++;break}u+=n(e[l]),h=f+2,f++;break;case 115:if(l>=c)break;h<f&&(u+=t.slice(h,f)),u+=String(e[l]),h=f+2,f++;break;case 37:h<f&&(u+=t.slice(h,f)),u+="%",h=f+2,f++,l--;break}++l}++f}return h===-1?t:(h<d&&(u+=t.slice(h)),u)}const Jc=Ey;var It=St;const Wn=Ny().console||{},Iy={mapHttpRequest:ki,mapHttpResponse:ki,wrapRequestSerializer:no,wrapResponseSerializer:no,wrapErrorSerializer:no,req:ki,res:ki,err:$y};function Sy(t,e){return Array.isArray(t)?t.filter(function(n){return n!=="!stdSerializers.err"}):t===!0?Object.keys(e):!1}function St(t){t=t||{},t.browser=t.browser||{};const e=t.browser.transmit;if(e&&typeof e.send!="function")throw Error("pino: transmit option must have a send function");const r=t.browser.write||Wn;t.browser.write&&(t.browser.asObject=!0);const n=t.serializers||{},i=Sy(t.browser.serialize,n);let s=t.browser.serialize;Array.isArray(t.browser.serialize)&&t.browser.serialize.indexOf("!stdSerializers.err")>-1&&(s=!1);const o=["error","fatal","warn","info","debug","trace"];typeof r=="function"&&(r.error=r.fatal=r.warn=r.info=r.debug=r.trace=r),t.enabled===!1&&(t.level="silent");const a=t.level||"info",c=Object.create(r);c.log||(c.log=Gn),Object.defineProperty(c,"levelVal",{get:l}),Object.defineProperty(c,"level",{get:h,set:d});const u={transmit:e,serialize:i,asObject:t.browser.asObject,levels:o,timestamp:Ay(t)};c.levels=St.levels,c.level=a,c.setMaxListeners=c.getMaxListeners=c.emit=c.addListener=c.on=c.prependListener=c.once=c.prependOnceListener=c.removeListener=c.removeAllListeners=c.listeners=c.listenerCount=c.eventNames=c.write=c.flush=Gn,c.serializers=n,c._serialize=i,c._stdErrSerialize=s,c.child=f,e&&(c._logEvent=ro());function l(){return this.level==="silent"?1/0:this.levels.values[this.level]}function h(){return this._level}function d(p){if(p!=="silent"&&!this.levels.values[p])throw Error("unknown level "+p);this._level=p,Yr(u,c,"error","log"),Yr(u,c,"fatal","error"),Yr(u,c,"warn","error"),Yr(u,c,"info","log"),Yr(u,c,"debug","log"),Yr(u,c,"trace","log")}function f(p,y){if(!p)throw new Error("missing bindings for child Pino");y=y||{},i&&p.serializers&&(y.serializers=p.serializers);const g=y.serializers;if(i&&g){var v=Object.assign({},n,g),b=t.browser.serialize===!0?Object.keys(v):i;delete p.serializers,qi([p],b,v,this._stdErrSerialize)}function E(_){this._childLevel=(_._childLevel|0)+1,this.error=Zr(_,p,"error"),this.fatal=Zr(_,p,"fatal"),this.warn=Zr(_,p,"warn"),this.info=Zr(_,p,"info"),this.debug=Zr(_,p,"debug"),this.trace=Zr(_,p,"trace"),v&&(this.serializers=v,this._serialize=b),e&&(this._logEvent=ro([].concat(_._logEvent.bindings,p)))}return E.prototype=this,new E(this)}return c}St.levels={values:{fatal:60,error:50,warn:40,info:30,debug:20,trace:10},labels:{10:"trace",20:"debug",30:"info",40:"warn",50:"error",60:"fatal"}},St.stdSerializers=Iy,St.stdTimeFunctions=Object.assign({},{nullTime:Xc,epochTime:Qc,unixTime:Py,isoTime:Cy});function Yr(t,e,r,n){const i=Object.getPrototypeOf(e);e[r]=e.levelVal>e.levels.values[r]?Gn:i[r]?i[r]:Wn[r]||Wn[n]||Gn,xy(t,e,r)}function xy(t,e,r){!t.transmit&&e[r]===Gn||(e[r]=function(n){return function(){const s=t.timestamp(),o=new Array(arguments.length),a=Object.getPrototypeOf&&Object.getPrototypeOf(this)===Wn?Wn:this;for(var c=0;c<o.length;c++)o[c]=arguments[c];if(t.serialize&&!t.asObject&&qi(o,this._serialize,this.serializers,this._stdErrSerialize),t.asObject?n.call(a,Oy(this,r,o,s)):n.apply(a,o),t.transmit){const u=t.transmit.level||e.level,l=St.levels.values[u],h=St.levels.values[r];if(h<l)return;Dy(this,{ts:s,methodLevel:r,methodValue:h,transmitLevel:u,transmitValue:St.levels.values[t.transmit.level||e.level],send:t.transmit.send,val:e.levelVal},o)}}}(e[r]))}function Oy(t,e,r,n){t._serialize&&qi(r,t._serialize,t.serializers,t._stdErrSerialize);const i=r.slice();let s=i[0];const o={};n&&(o.time=n),o.level=St.levels.values[e];let a=(t._childLevel|0)+1;if(a<1&&(a=1),s!==null&&typeof s=="object"){for(;a--&&typeof i[0]=="object";)Object.assign(o,i.shift());s=i.length?Jc(i.shift(),i):void 0}else typeof s=="string"&&(s=Jc(i.shift(),i));return s!==void 0&&(o.msg=s),o}function qi(t,e,r,n){for(const i in t)if(n&&t[i]instanceof Error)t[i]=St.stdSerializers.err(t[i]);else if(typeof t[i]=="object"&&!Array.isArray(t[i]))for(const s in t[i])e&&e.indexOf(s)>-1&&s in r&&(t[i][s]=r[s](t[i][s]))}function Zr(t,e,r){return function(){const n=new Array(1+arguments.length);n[0]=e;for(var i=1;i<n.length;i++)n[i]=arguments[i-1];return t[r].apply(this,n)}}function Dy(t,e,r){const n=e.send,i=e.ts,s=e.methodLevel,o=e.methodValue,a=e.val,c=t._logEvent.bindings;qi(r,t._serialize||Object.keys(t.serializers),t.serializers,t._stdErrSerialize===void 0?!0:t._stdErrSerialize),t._logEvent.ts=i,t._logEvent.messages=r.filter(function(u){return c.indexOf(u)===-1}),t._logEvent.level.label=s,t._logEvent.level.value=o,n(s,t._logEvent,a),t._logEvent=ro(c)}function ro(t){return{ts:0,messages:[],bindings:t||[],level:{label:"",value:0}}}function $y(t){const e={type:t.constructor.name,msg:t.message,stack:t.stack};for(const r in t)e[r]===void 0&&(e[r]=t[r]);return e}function Ay(t){return typeof t.timestamp=="function"?t.timestamp:t.timestamp===!1?Xc:Qc}function ki(){return{}}function no(t){return t}function Gn(){}function Xc(){return!1}function Qc(){return Date.now()}function Py(){return Math.round(Date.now()/1e3)}function Cy(){return new Date(Date.now()).toISOString()}function Ny(){function t(e){return typeof e<"u"&&e}try{return typeof globalThis<"u"||Object.defineProperty(Object.prototype,"globalThis",{get:function(){return delete Object.prototype.globalThis,this.globalThis=this},configurable:!0}),globalThis}catch{return t(self)||t(window)||t(this)||{}}}const Ry={level:"info"},Yn="custom_context",io=1e3*1024;class Ty{constructor(e){this.nodeValue=e,this.sizeInBytes=new TextEncoder().encode(this.nodeValue).length,this.next=null}get value(){return this.nodeValue}get size(){return this.sizeInBytes}}class eu{constructor(e){this.head=null,this.tail=null,this.lengthInNodes=0,this.maxSizeInBytes=e,this.sizeInBytes=0}append(e){const r=new Ty(e);if(r.size>this.maxSizeInBytes)throw new Error(`[LinkedList] Value too big to insert into list: ${e} with size ${r.size}`);for(;this.size+r.size>this.maxSizeInBytes;)this.shift();this.head?(this.tail&&(this.tail.next=r),this.tail=r):(this.head=r,this.tail=r),this.lengthInNodes++,this.sizeInBytes+=r.size}shift(){if(!this.head)return;const e=this.head;this.head=this.head.next,this.head||(this.tail=null),this.lengthInNodes--,this.sizeInBytes-=e.size}toArray(){const e=[];let r=this.head;for(;r!==null;)e.push(r.value),r=r.next;return e}get length(){return this.lengthInNodes}get size(){return this.sizeInBytes}toOrderedArray(){return Array.from(this)}[Symbol.iterator](){let e=this.head;return{next:()=>{if(!e)return{done:!0,value:null};const r=e.value;return e=e.next,{done:!1,value:r}}}}}class tu{constructor(e,r=io){this.level=e??"error",this.levelValue=It.levels.values[this.level],this.MAX_LOG_SIZE_IN_BYTES=r,this.logs=new eu(this.MAX_LOG_SIZE_IN_BYTES)}forwardToConsole(e,r){r===It.levels.values.error?console.error(e):r===It.levels.values.warn?console.warn(e):r===It.levels.values.debug?console.debug(e):r===It.levels.values.trace?console.trace(e):console.log(e)}appendToLogs(e){this.logs.append(Ut({timestamp:new Date().toISOString(),log:e}));const r=typeof e=="string"?JSON.parse(e).level:e.level;r>=this.levelValue&&this.forwardToConsole(e,r)}getLogs(){return this.logs}clearLogs(){this.logs=new eu(this.MAX_LOG_SIZE_IN_BYTES)}getLogArray(){return Array.from(this.logs)}logsToBlob(e){const r=this.getLogArray();return r.push(Ut({extraMetadata:e})),new Blob(r,{type:"application/json"})}}class By{constructor(e,r=io){this.baseChunkLogger=new tu(e,r)}write(e){this.baseChunkLogger.appendToLogs(e)}getLogs(){return this.baseChunkLogger.getLogs()}clearLogs(){this.baseChunkLogger.clearLogs()}getLogArray(){return this.baseChunkLogger.getLogArray()}logsToBlob(e){return this.baseChunkLogger.logsToBlob(e)}downloadLogsBlobInBrowser(e){const r=URL.createObjectURL(this.logsToBlob(e)),n=document.createElement("a");n.href=r,n.download=`walletconnect-logs-${new Date().toISOString()}.txt`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(r)}}class Fy{constructor(e,r=io){this.baseChunkLogger=new tu(e,r)}write(e){this.baseChunkLogger.appendToLogs(e)}getLogs(){return this.baseChunkLogger.getLogs()}clearLogs(){this.baseChunkLogger.clearLogs()}getLogArray(){return this.baseChunkLogger.getLogArray()}logsToBlob(e){return this.baseChunkLogger.logsToBlob(e)}}var Ly=Object.defineProperty,Uy=Object.defineProperties,jy=Object.getOwnPropertyDescriptors,ru=Object.getOwnPropertySymbols,qy=Object.prototype.hasOwnProperty,ky=Object.prototype.propertyIsEnumerable,nu=(t,e,r)=>e in t?Ly(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Mi=(t,e)=>{for(var r in e||(e={}))qy.call(e,r)&&nu(t,r,e[r]);if(ru)for(var r of ru(e))ky.call(e,r)&&nu(t,r,e[r]);return t},zi=(t,e)=>Uy(t,jy(e));function Hi(t){return zi(Mi({},t),{level:t?.level||Ry.level})}function My(t,e=Yn){return t[e]||""}function zy(t,e,r=Yn){return t[r]=e,t}function Ge(t,e=Yn){let r="";return typeof t.bindings>"u"?r=My(t,e):r=t.bindings().context||"",r}function Hy(t,e,r=Yn){const n=Ge(t,r);return n.trim()?`${n}/${e}`:e}function ke(t,e,r=Yn){const n=Hy(t,e,r),i=t.child({context:n});return zy(i,n,r)}function Vy(t){var e,r;const n=new By((e=t.opts)==null?void 0:e.level,t.maxSizeInBytes);return{logger:It(zi(Mi({},t.opts),{level:"trace",browser:zi(Mi({},(r=t.opts)==null?void 0:r.browser),{write:i=>n.write(i)})})),chunkLoggerController:n}}function Ky(t){var e;const r=new Fy((e=t.opts)==null?void 0:e.level,t.maxSizeInBytes);return{logger:It(zi(Mi({},t.opts),{level:"trace"})),chunkLoggerController:r}}function Wy(t){return typeof t.loggerOverride<"u"&&typeof t.loggerOverride!="string"?{logger:t.loggerOverride,chunkLoggerController:null}:typeof window<"u"?Vy(t):Ky(t)}var Gy=Object.defineProperty,Yy=(t,e,r)=>e in t?Gy(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,iu=(t,e,r)=>Yy(t,typeof e!="symbol"?e+"":e,r);class Zy extends Ir{constructor(e){super(),this.opts=e,iu(this,"protocol","wc"),iu(this,"version",2)}}var Jy=Object.defineProperty,Xy=(t,e,r)=>e in t?Jy(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Qy=(t,e,r)=>Xy(t,typeof e!="symbol"?e+"":e,r);class em extends Ir{constructor(e,r){super(),this.core=e,this.logger=r,Qy(this,"records",new Map)}}class tm{constructor(e,r){this.logger=e,this.core=r}}class rm extends Ir{constructor(e,r){super(),this.relayer=e,this.logger=r}}class nm extends Ir{constructor(e){super()}}class im{constructor(e,r,n,i){this.core=e,this.logger=r,this.name=n}}class sm extends Ir{constructor(e,r){super(),this.relayer=e,this.logger=r}}class om extends Ir{constructor(e,r){super(),this.core=e,this.logger=r}}class am{constructor(e,r,n){this.core=e,this.logger=r,this.store=n}}class cm{constructor(e,r){this.projectId=e,this.logger=r}}class um{constructor(e,r,n){this.core=e,this.logger=r,this.telemetryEnabled=n}}var lm=Object.defineProperty,hm=(t,e,r)=>e in t?lm(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,su=(t,e,r)=>hm(t,typeof e!="symbol"?e+"":e,r);class dm{constructor(e){this.opts=e,su(this,"protocol","wc"),su(this,"version",2)}}class fm{constructor(e){this.client=e}}function pm(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function ou(t,...e){if(!pm(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function au(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function gm(t,e){ou(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const Jr=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */const so=t=>new DataView(t.buffer,t.byteOffset,t.byteLength);function ym(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function cu(t){return typeof t=="string"&&(t=ym(t)),ou(t),t}class mm{clone(){return this._cloneInto()}}function wm(t){const e=n=>t().update(cu(n)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function uu(t=32){if(Jr&&typeof Jr.getRandomValues=="function")return Jr.getRandomValues(new Uint8Array(t));if(Jr&&typeof Jr.randomBytes=="function")return Jr.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}function bm(t,e,r,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,n);const i=BigInt(32),s=BigInt(**********),o=Number(r>>i&s),a=Number(r&s),c=n?4:0,u=n?0:4;t.setUint32(e+c,o,n),t.setUint32(e+u,a,n)}class vm extends mm{constructor(e,r,n,i){super(),this.blockLen=e,this.outputLen=r,this.padOffset=n,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=so(this.buffer)}update(e){au(this);const{view:r,buffer:n,blockLen:i}=this;e=cu(e);const s=e.length;for(let o=0;o<s;){const a=Math.min(i-this.pos,s-o);if(a===i){const c=so(e);for(;i<=s-o;o+=i)this.process(c,o);continue}n.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===i&&(this.process(r,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){au(this),gm(e,this),this.finished=!0;const{buffer:r,view:n,blockLen:i,isLE:s}=this;let{pos:o}=this;r[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>i-o&&(this.process(n,0),o=0);for(let h=o;h<i;h++)r[h]=0;bm(n,i-8,BigInt(this.length*8),s),this.process(n,0);const a=so(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=c/4,l=this.get();if(u>l.length)throw new Error("_sha2: outputLen bigger than state");for(let h=0;h<u;h++)a.setUint32(4*h,l[h],s)}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const n=e.slice(0,r);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:r,buffer:n,length:i,finished:s,destroyed:o,pos:a}=this;return e.length=i,e.pos=a,e.finished=s,e.destroyed=o,i%r&&e.buffer.set(n),e}}const Vi=BigInt(2**32-1),oo=BigInt(32);function lu(t,e=!1){return e?{h:Number(t&Vi),l:Number(t>>oo&Vi)}:{h:Number(t>>oo&Vi)|0,l:Number(t&Vi)|0}}function Em(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:s,l:o}=lu(t[i],e);[r[i],n[i]]=[s,o]}return[r,n]}const _m=(t,e)=>BigInt(t>>>0)<<oo|BigInt(e>>>0),Im=(t,e,r)=>t>>>r,Sm=(t,e,r)=>t<<32-r|e>>>r,xm=(t,e,r)=>t>>>r|e<<32-r,Om=(t,e,r)=>t<<32-r|e>>>r,Dm=(t,e,r)=>t<<64-r|e>>>r-32,$m=(t,e,r)=>t>>>r-32|e<<64-r,Am=(t,e)=>e,Pm=(t,e)=>t,Cm=(t,e,r)=>t<<r|e>>>32-r,Nm=(t,e,r)=>e<<r|t>>>32-r,Rm=(t,e,r)=>e<<r-32|t>>>64-r,Tm=(t,e,r)=>t<<r-32|e>>>64-r;function Bm(t,e,r,n){const i=(e>>>0)+(n>>>0);return{h:t+r+(i/2**32|0)|0,l:i|0}}const Fm=(t,e,r)=>(t>>>0)+(e>>>0)+(r>>>0),Lm=(t,e,r,n)=>e+r+n+(t/2**32|0)|0,Um=(t,e,r,n)=>(t>>>0)+(e>>>0)+(r>>>0)+(n>>>0),jm=(t,e,r,n,i)=>e+r+n+i+(t/2**32|0)|0,qm=(t,e,r,n,i)=>(t>>>0)+(e>>>0)+(r>>>0)+(n>>>0)+(i>>>0),km=(t,e,r,n,i,s)=>e+r+n+i+s+(t/2**32|0)|0,K={fromBig:lu,split:Em,toBig:_m,shrSH:Im,shrSL:Sm,rotrSH:xm,rotrSL:Om,rotrBH:Dm,rotrBL:$m,rotr32H:Am,rotr32L:Pm,rotlSH:Cm,rotlSL:Nm,rotlBH:Rm,rotlBL:Tm,add:Bm,add3L:Fm,add3H:Lm,add4L:Um,add4H:jm,add5H:km,add5L:qm},[Mm,zm]=K.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(t=>BigInt(t))),tr=new Uint32Array(80),rr=new Uint32Array(80);class Hm extends vm{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){const{Ah:e,Al:r,Bh:n,Bl:i,Ch:s,Cl:o,Dh:a,Dl:c,Eh:u,El:l,Fh:h,Fl:d,Gh:f,Gl:p,Hh:y,Hl:g}=this;return[e,r,n,i,s,o,a,c,u,l,h,d,f,p,y,g]}set(e,r,n,i,s,o,a,c,u,l,h,d,f,p,y,g){this.Ah=e|0,this.Al=r|0,this.Bh=n|0,this.Bl=i|0,this.Ch=s|0,this.Cl=o|0,this.Dh=a|0,this.Dl=c|0,this.Eh=u|0,this.El=l|0,this.Fh=h|0,this.Fl=d|0,this.Gh=f|0,this.Gl=p|0,this.Hh=y|0,this.Hl=g|0}process(e,r){for(let E=0;E<16;E++,r+=4)tr[E]=e.getUint32(r),rr[E]=e.getUint32(r+=4);for(let E=16;E<80;E++){const _=tr[E-15]|0,P=rr[E-15]|0,$=K.rotrSH(_,P,1)^K.rotrSH(_,P,8)^K.shrSH(_,P,7),O=K.rotrSL(_,P,1)^K.rotrSL(_,P,8)^K.shrSL(_,P,7),C=tr[E-2]|0,S=rr[E-2]|0,k=K.rotrSH(C,S,19)^K.rotrBH(C,S,61)^K.shrSH(C,S,6),T=K.rotrSL(C,S,19)^K.rotrBL(C,S,61)^K.shrSL(C,S,6),R=K.add4L(O,T,rr[E-7],rr[E-16]),M=K.add4H(R,$,k,tr[E-7],tr[E-16]);tr[E]=M|0,rr[E]=R|0}let{Ah:n,Al:i,Bh:s,Bl:o,Ch:a,Cl:c,Dh:u,Dl:l,Eh:h,El:d,Fh:f,Fl:p,Gh:y,Gl:g,Hh:v,Hl:b}=this;for(let E=0;E<80;E++){const _=K.rotrSH(h,d,14)^K.rotrSH(h,d,18)^K.rotrBH(h,d,41),P=K.rotrSL(h,d,14)^K.rotrSL(h,d,18)^K.rotrBL(h,d,41),$=h&f^~h&y,O=d&p^~d&g,C=K.add5L(b,P,O,zm[E],rr[E]),S=K.add5H(C,v,_,$,Mm[E],tr[E]),k=C|0,T=K.rotrSH(n,i,28)^K.rotrBH(n,i,34)^K.rotrBH(n,i,39),R=K.rotrSL(n,i,28)^K.rotrBL(n,i,34)^K.rotrBL(n,i,39),M=n&s^n&a^s&a,D=i&o^i&c^o&c;v=y|0,b=g|0,y=f|0,g=p|0,f=h|0,p=d|0,{h,l:d}=K.add(u|0,l|0,S|0,k|0),u=a|0,l=c|0,a=s|0,c=o|0,s=n|0,o=i|0;const m=K.add3L(k,R,D);n=K.add3H(m,S,T,M),i=m|0}({h:n,l:i}=K.add(this.Ah|0,this.Al|0,n|0,i|0)),{h:s,l:o}=K.add(this.Bh|0,this.Bl|0,s|0,o|0),{h:a,l:c}=K.add(this.Ch|0,this.Cl|0,a|0,c|0),{h:u,l}=K.add(this.Dh|0,this.Dl|0,u|0,l|0),{h,l:d}=K.add(this.Eh|0,this.El|0,h|0,d|0),{h:f,l:p}=K.add(this.Fh|0,this.Fl|0,f|0,p|0),{h:y,l:g}=K.add(this.Gh|0,this.Gl|0,y|0,g|0),{h:v,l:b}=K.add(this.Hh|0,this.Hl|0,v|0,b|0),this.set(n,i,s,o,a,c,u,l,h,d,f,p,y,g,v,b)}roundClean(){tr.fill(0),rr.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}}const Vm=wm(()=>new Hm);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const ao=BigInt(0),hu=BigInt(1),Km=BigInt(2);function co(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function uo(t){if(!co(t))throw new Error("Uint8Array expected")}function lo(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const Wm=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function ho(t){uo(t);let e="";for(let r=0;r<t.length;r++)e+=Wm[t[r]];return e}function du(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?ao:BigInt("0x"+t)}const jt={_0:48,_9:57,A:65,F:70,a:97,f:102};function fu(t){if(t>=jt._0&&t<=jt._9)return t-jt._0;if(t>=jt.A&&t<=jt.F)return t-(jt.A-10);if(t>=jt.a&&t<=jt.f)return t-(jt.a-10)}function pu(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const n=new Uint8Array(r);for(let i=0,s=0;i<r;i++,s+=2){const o=fu(t.charCodeAt(s)),a=fu(t.charCodeAt(s+1));if(o===void 0||a===void 0){const c=t[s]+t[s+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+s)}n[i]=o*16+a}return n}function Gm(t){return du(ho(t))}function Ki(t){return uo(t),du(ho(Uint8Array.from(t).reverse()))}function gu(t,e){return pu(t.toString(16).padStart(e*2,"0"))}function fo(t,e){return gu(t,e).reverse()}function qt(t,e,r){let n;if(typeof e=="string")try{n=pu(e)}catch(s){throw new Error(t+" must be hex string or Uint8Array, cause: "+s)}else if(co(e))n=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const i=n.length;if(typeof r=="number"&&i!==r)throw new Error(t+" of length "+r+" expected, got "+i);return n}function yu(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];uo(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}const po=t=>typeof t=="bigint"&&ao<=t;function Ym(t,e,r){return po(t)&&po(e)&&po(r)&&e<=t&&t<r}function Zn(t,e,r,n){if(!Ym(e,r,n))throw new Error("expected valid "+t+": "+r+" <= n < "+n+", got "+e)}function Zm(t){let e;for(e=0;t>ao;t>>=hu,e+=1);return e}const Jm=t=>(Km<<BigInt(t-1))-hu,Xm={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||co(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function go(t,e,r={}){const n=(i,s,o)=>{const a=Xm[s];if(typeof a!="function")throw new Error("invalid validator function");const c=t[i];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(i)+" is invalid. Expected "+s+", got "+c)};for(const[i,s]of Object.entries(e))n(i,s,!1);for(const[i,s]of Object.entries(r))n(i,s,!0);return t}function mu(t){const e=new WeakMap;return(r,...n)=>{const i=e.get(r);if(i!==void 0)return i;const s=t(r,...n);return e.set(r,s),s}}const _e=BigInt(0),de=BigInt(1),Dr=BigInt(2),Qm=BigInt(3),yo=BigInt(4),wu=BigInt(5),bu=BigInt(8);function be(t,e){const r=t%e;return r>=_e?r:e+r}function ew(t,e,r){if(e<_e)throw new Error("invalid exponent, negatives unsupported");if(r<=_e)throw new Error("invalid modulus");if(r===de)return _e;let n=de;for(;e>_e;)e&de&&(n=n*t%r),t=t*t%r,e>>=de;return n}function xt(t,e,r){let n=t;for(;e-- >_e;)n*=n,n%=r;return n}function vu(t,e){if(t===_e)throw new Error("invert: expected non-zero number");if(e<=_e)throw new Error("invert: expected positive modulus, got "+e);let r=be(t,e),n=e,i=_e,s=de;for(;r!==_e;){const o=n/r,a=n%r,c=i-s*o;n=r,r=a,i=s,s=c}if(n!==de)throw new Error("invert: does not exist");return be(i,e)}function tw(t){const e=(t-de)/Dr;let r,n,i;for(r=t-de,n=0;r%Dr===_e;r/=Dr,n++);for(i=Dr;i<t&&ew(i,e,t)!==t-de;i++)if(i>1e3)throw new Error("Cannot find square root: likely non-prime P");if(n===1){const o=(t+de)/yo;return function(a,c){const u=a.pow(c,o);if(!a.eql(a.sqr(u),c))throw new Error("Cannot find square root");return u}}const s=(r+de)/Dr;return function(o,a){if(o.pow(a,e)===o.neg(o.ONE))throw new Error("Cannot find square root");let c=n,u=o.pow(o.mul(o.ONE,i),r),l=o.pow(a,s),h=o.pow(a,r);for(;!o.eql(h,o.ONE);){if(o.eql(h,o.ZERO))return o.ZERO;let d=1;for(let p=o.sqr(h);d<c&&!o.eql(p,o.ONE);d++)p=o.sqr(p);const f=o.pow(u,de<<BigInt(c-d-1));u=o.sqr(f),l=o.mul(l,f),h=o.mul(h,u),c=d}return l}}function rw(t){if(t%yo===Qm){const e=(t+de)/yo;return function(r,n){const i=r.pow(n,e);if(!r.eql(r.sqr(i),n))throw new Error("Cannot find square root");return i}}if(t%bu===wu){const e=(t-wu)/bu;return function(r,n){const i=r.mul(n,Dr),s=r.pow(i,e),o=r.mul(n,s),a=r.mul(r.mul(o,Dr),s),c=r.mul(o,r.sub(a,r.ONE));if(!r.eql(r.sqr(c),n))throw new Error("Cannot find square root");return c}}return tw(t)}const nw=(t,e)=>(be(t,e)&de)===de,iw=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function sw(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},r=iw.reduce((n,i)=>(n[i]="function",n),e);return go(t,r)}function ow(t,e,r){if(r<_e)throw new Error("invalid exponent, negatives unsupported");if(r===_e)return t.ONE;if(r===de)return e;let n=t.ONE,i=e;for(;r>_e;)r&de&&(n=t.mul(n,i)),i=t.sqr(i),r>>=de;return n}function aw(t,e){const r=new Array(e.length),n=e.reduce((s,o,a)=>t.is0(o)?s:(r[a]=s,t.mul(s,o)),t.ONE),i=t.inv(n);return e.reduceRight((s,o,a)=>t.is0(o)?s:(r[a]=t.mul(s,r[a]),t.mul(s,o)),i),r}function Eu(t,e){const r=e!==void 0?e:t.toString(2).length,n=Math.ceil(r/8);return{nBitLength:r,nByteLength:n}}function _u(t,e,r=!1,n={}){if(t<=_e)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:i,nByteLength:s}=Eu(t,e);if(s>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:r,BITS:i,BYTES:s,MASK:Jm(i),ZERO:_e,ONE:de,create:c=>be(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return _e<=c&&c<t},is0:c=>c===_e,isOdd:c=>(c&de)===de,neg:c=>be(-c,t),eql:(c,u)=>c===u,sqr:c=>be(c*c,t),add:(c,u)=>be(c+u,t),sub:(c,u)=>be(c-u,t),mul:(c,u)=>be(c*u,t),pow:(c,u)=>ow(a,c,u),div:(c,u)=>be(c*vu(u,t),t),sqrN:c=>c*c,addN:(c,u)=>c+u,subN:(c,u)=>c-u,mulN:(c,u)=>c*u,inv:c=>vu(c,t),sqrt:n.sqrt||(c=>(o||(o=rw(t)),o(a,c))),invertBatch:c=>aw(a,c),cmov:(c,u,l)=>l?u:c,toBytes:c=>r?fo(c,s):gu(c,s),fromBytes:c=>{if(c.length!==s)throw new Error("Field.fromBytes: expected "+s+" bytes, got "+c.length);return r?Ki(c):Gm(c)}});return Object.freeze(a)}const Iu=BigInt(0),Wi=BigInt(1);function mo(t,e){const r=e.negate();return t?r:e}function Su(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function wo(t,e){Su(t,e);const r=Math.ceil(e/t)+1,n=2**(t-1);return{windows:r,windowSize:n}}function cw(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((r,n)=>{if(!(r instanceof e))throw new Error("invalid point at index "+n)})}function uw(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((r,n)=>{if(!e.isValid(r))throw new Error("invalid scalar at index "+n)})}const bo=new WeakMap,xu=new WeakMap;function vo(t){return xu.get(t)||1}function lw(t,e){return{constTimeNegate:mo,hasPrecomputes(r){return vo(r)!==1},unsafeLadder(r,n,i=t.ZERO){let s=r;for(;n>Iu;)n&Wi&&(i=i.add(s)),s=s.double(),n>>=Wi;return i},precomputeWindow(r,n){const{windows:i,windowSize:s}=wo(n,e),o=[];let a=r,c=a;for(let u=0;u<i;u++){c=a,o.push(c);for(let l=1;l<s;l++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(r,n,i){const{windows:s,windowSize:o}=wo(r,e);let a=t.ZERO,c=t.BASE;const u=BigInt(2**r-1),l=2**r,h=BigInt(r);for(let d=0;d<s;d++){const f=d*o;let p=Number(i&u);i>>=h,p>o&&(p-=l,i+=Wi);const y=f,g=f+Math.abs(p)-1,v=d%2!==0,b=p<0;p===0?c=c.add(mo(v,n[y])):a=a.add(mo(b,n[g]))}return{p:a,f:c}},wNAFUnsafe(r,n,i,s=t.ZERO){const{windows:o,windowSize:a}=wo(r,e),c=BigInt(2**r-1),u=2**r,l=BigInt(r);for(let h=0;h<o;h++){const d=h*a;if(i===Iu)break;let f=Number(i&c);if(i>>=l,f>a&&(f-=u,i+=Wi),f===0)continue;let p=n[d+Math.abs(f)-1];f<0&&(p=p.negate()),s=s.add(p)}return s},getPrecomputes(r,n,i){let s=bo.get(n);return s||(s=this.precomputeWindow(n,r),r!==1&&bo.set(n,i(s))),s},wNAFCached(r,n,i){const s=vo(r);return this.wNAF(s,this.getPrecomputes(s,r,i),n)},wNAFCachedUnsafe(r,n,i,s){const o=vo(r);return o===1?this.unsafeLadder(r,n,s):this.wNAFUnsafe(o,this.getPrecomputes(o,r,i),n,s)},setWindowSize(r,n){Su(n,e),xu.set(r,n),bo.delete(r)}}}function hw(t,e,r,n){if(cw(r,t),uw(n,e),r.length!==n.length)throw new Error("arrays of points and scalars must have equal length");const i=t.ZERO,s=Zm(BigInt(r.length)),o=s>12?s-3:s>4?s-2:s?2:1,a=(1<<o)-1,c=new Array(a+1).fill(i),u=Math.floor((e.BITS-1)/o)*o;let l=i;for(let h=u;h>=0;h-=o){c.fill(i);for(let f=0;f<n.length;f++){const p=n[f],y=Number(p>>BigInt(h)&BigInt(a));c[y]=c[y].add(r[f])}let d=i;for(let f=c.length-1,p=i;f>0;f--)p=p.add(c[f]),d=d.add(p);if(l=l.add(d),h!==0)for(let f=0;f<o;f++)l=l.double()}return l}function dw(t){return sw(t.Fp),go(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...Eu(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}const gt=BigInt(0),Ye=BigInt(1),Gi=BigInt(2),fw=BigInt(8),pw={zip215:!0};function gw(t){const e=dw(t);return go(t,{hash:"function",a:"bigint",d:"bigint",randomBytes:"function"},{adjustScalarBytes:"function",domain:"function",uvRatio:"function",mapToCurve:"function"}),Object.freeze({...e})}function yw(t){const e=gw(t),{Fp:r,n,prehash:i,hash:s,randomBytes:o,nByteLength:a,h:c}=e,u=Gi<<BigInt(a*8)-Ye,l=r.create,h=_u(e.n,e.nBitLength),d=e.uvRatio||((m,w)=>{try{return{isValid:!0,value:r.sqrt(m*r.inv(w))}}catch{return{isValid:!1,value:gt}}}),f=e.adjustScalarBytes||(m=>m),p=e.domain||((m,w,I)=>{if(lo("phflag",I),w.length||I)throw new Error("Contexts/pre-hash are not supported");return m});function y(m,w){Zn("coordinate "+m,w,gt,u)}function g(m){if(!(m instanceof E))throw new Error("ExtendedPoint expected")}const v=mu((m,w)=>{const{ex:I,ey:A,ez:x}=m,N=m.is0();w==null&&(w=N?fw:r.inv(x));const F=l(I*w),j=l(A*w),z=l(x*w);if(N)return{x:gt,y:Ye};if(z!==Ye)throw new Error("invZ was invalid");return{x:F,y:j}}),b=mu(m=>{const{a:w,d:I}=e;if(m.is0())throw new Error("bad point: ZERO");const{ex:A,ey:x,ez:N,et:F}=m,j=l(A*A),z=l(x*x),q=l(N*N),H=l(q*q),V=l(j*w),ee=l(q*l(V+z)),G=l(H+l(I*l(j*z)));if(ee!==G)throw new Error("bad point: equation left != right (1)");const W=l(A*x),ge=l(N*F);if(W!==ge)throw new Error("bad point: equation left != right (2)");return!0});class E{constructor(w,I,A,x){this.ex=w,this.ey=I,this.ez=A,this.et=x,y("x",w),y("y",I),y("z",A),y("t",x),Object.freeze(this)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static fromAffine(w){if(w instanceof E)throw new Error("extended point not allowed");const{x:I,y:A}=w||{};return y("x",I),y("y",A),new E(I,A,Ye,l(I*A))}static normalizeZ(w){const I=r.invertBatch(w.map(A=>A.ez));return w.map((A,x)=>A.toAffine(I[x])).map(E.fromAffine)}static msm(w,I){return hw(E,h,w,I)}_setWindowSize(w){$.setWindowSize(this,w)}assertValidity(){b(this)}equals(w){g(w);const{ex:I,ey:A,ez:x}=this,{ex:N,ey:F,ez:j}=w,z=l(I*j),q=l(N*x),H=l(A*j),V=l(F*x);return z===q&&H===V}is0(){return this.equals(E.ZERO)}negate(){return new E(l(-this.ex),this.ey,this.ez,l(-this.et))}double(){const{a:w}=e,{ex:I,ey:A,ez:x}=this,N=l(I*I),F=l(A*A),j=l(Gi*l(x*x)),z=l(w*N),q=I+A,H=l(l(q*q)-N-F),V=z+F,ee=V-j,G=z-F,W=l(H*ee),ge=l(V*G),le=l(H*G),he=l(ee*V);return new E(W,ge,he,le)}add(w){g(w);const{a:I,d:A}=e,{ex:x,ey:N,ez:F,et:j}=this,{ex:z,ey:q,ez:H,et:V}=w;if(I===BigInt(-1)){const Yp=l((N-x)*(q+z)),Zp=l((N+x)*(q-z)),_c=l(Zp-Yp);if(_c===gt)return this.double();const Jp=l(F*Gi*V),Xp=l(j*Gi*H),Qp=Xp+Jp,eg=Zp+Yp,tg=Xp-Jp,FP=l(Qp*_c),LP=l(eg*tg),UP=l(Qp*tg),jP=l(_c*eg);return new E(FP,LP,jP,UP)}const ee=l(x*z),G=l(N*q),W=l(j*A*V),ge=l(F*H),le=l((x+N)*(z+q)-ee-G),he=ge-W,Pe=ge+W,we=l(G-I*ee),er=l(le*he),RP=l(Pe*we),TP=l(le*we),BP=l(he*Pe);return new E(er,RP,BP,TP)}subtract(w){return this.add(w.negate())}wNAF(w){return $.wNAFCached(this,w,E.normalizeZ)}multiply(w){const I=w;Zn("scalar",I,Ye,n);const{p:A,f:x}=this.wNAF(I);return E.normalizeZ([A,x])[0]}multiplyUnsafe(w,I=E.ZERO){const A=w;return Zn("scalar",A,gt,n),A===gt?P:this.is0()||A===Ye?this:$.wNAFCachedUnsafe(this,A,E.normalizeZ,I)}isSmallOrder(){return this.multiplyUnsafe(c).is0()}isTorsionFree(){return $.unsafeLadder(this,n).is0()}toAffine(w){return v(this,w)}clearCofactor(){const{h:w}=e;return w===Ye?this:this.multiplyUnsafe(w)}static fromHex(w,I=!1){const{d:A,a:x}=e,N=r.BYTES;w=qt("pointHex",w,N),lo("zip215",I);const F=w.slice(),j=w[N-1];F[N-1]=j&-129;const z=Ki(F),q=I?u:r.ORDER;Zn("pointHex.y",z,gt,q);const H=l(z*z),V=l(H-Ye),ee=l(A*H-x);let{isValid:G,value:W}=d(V,ee);if(!G)throw new Error("Point.fromHex: invalid y coordinate");const ge=(W&Ye)===Ye,le=(j&128)!==0;if(!I&&W===gt&&le)throw new Error("Point.fromHex: x=0 and x_0=1");return le!==ge&&(W=l(-W)),E.fromAffine({x:W,y:z})}static fromPrivateKey(w){return S(w).point}toRawBytes(){const{x:w,y:I}=this.toAffine(),A=fo(I,r.BYTES);return A[A.length-1]|=w&Ye?128:0,A}toHex(){return ho(this.toRawBytes())}}E.BASE=new E(e.Gx,e.Gy,Ye,l(e.Gx*e.Gy)),E.ZERO=new E(gt,Ye,Ye,gt);const{BASE:_,ZERO:P}=E,$=lw(E,a*8);function O(m){return be(m,n)}function C(m){return O(Ki(m))}function S(m){const w=r.BYTES;m=qt("private key",m,w);const I=qt("hashed private key",s(m),2*w),A=f(I.slice(0,w)),x=I.slice(w,2*w),N=C(A),F=_.multiply(N),j=F.toRawBytes();return{head:A,prefix:x,scalar:N,point:F,pointBytes:j}}function k(m){return S(m).pointBytes}function T(m=new Uint8Array,...w){const I=yu(...w);return C(s(p(I,qt("context",m),!!i)))}function R(m,w,I={}){m=qt("message",m),i&&(m=i(m));const{prefix:A,scalar:x,pointBytes:N}=S(w),F=T(I.context,A,m),j=_.multiply(F).toRawBytes(),z=T(I.context,j,N,m),q=O(F+z*x);Zn("signature.s",q,gt,n);const H=yu(j,fo(q,r.BYTES));return qt("result",H,r.BYTES*2)}const M=pw;function D(m,w,I,A=M){const{context:x,zip215:N}=A,F=r.BYTES;m=qt("signature",m,2*F),w=qt("message",w),I=qt("publicKey",I,F),N!==void 0&&lo("zip215",N),i&&(w=i(w));const j=Ki(m.slice(F,2*F));let z,q,H;try{z=E.fromHex(I,N),q=E.fromHex(m.slice(0,F),N),H=_.multiplyUnsafe(j)}catch{return!1}if(!N&&z.isSmallOrder())return!1;const V=T(x,q.toRawBytes(),z.toRawBytes(),w);return q.add(z.multiplyUnsafe(V)).subtract(H).clearCofactor().equals(E.ZERO)}return _._setWindowSize(8),{CURVE:e,getPublicKey:k,sign:R,verify:D,ExtendedPoint:E,utils:{getExtendedPublicKey:S,randomPrivateKey:()=>o(r.BYTES),precompute(m=8,w=E.BASE){return w._setWindowSize(m),w.multiply(BigInt(3)),w}}}}BigInt(0),BigInt(1);const Eo=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),Ou=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752");BigInt(0);const mw=BigInt(1),Du=BigInt(2);BigInt(3);const ww=BigInt(5),bw=BigInt(8);function vw(t){const e=BigInt(10),r=BigInt(20),n=BigInt(40),i=BigInt(80),s=Eo,o=t*t%s*t%s,a=xt(o,Du,s)*o%s,c=xt(a,mw,s)*t%s,u=xt(c,ww,s)*c%s,l=xt(u,e,s)*u%s,h=xt(l,r,s)*l%s,d=xt(h,n,s)*h%s,f=xt(d,i,s)*d%s,p=xt(f,i,s)*d%s,y=xt(p,e,s)*u%s;return{pow_p_5_8:xt(y,Du,s)*t%s,b2:o}}function Ew(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}function _w(t,e){const r=Eo,n=be(e*e*e,r),i=be(n*n*e,r),s=vw(t*i).pow_p_5_8;let o=be(t*n*s,r);const a=be(e*o*o,r),c=o,u=be(o*Ou,r),l=a===t,h=a===be(-t,r),d=a===be(-t*Ou,r);return l&&(o=c),(h||d)&&(o=u),nw(o,r)&&(o=be(-o,r)),{isValid:l||h,value:o}}const Iw=_u(Eo,void 0,!0),Sw={a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),Fp:Iw,n:BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),h:bw,Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960"),hash:Vm,randomBytes:uu,adjustScalarBytes:Ew,uvRatio:_w},$u=yw(Sw),xw="EdDSA",Ow="JWT",Yi=".",Zi="base64url",Au="utf8",Pu="utf8",Dw=":",$w="did",Aw="key",Cu="base58btc",Pw="z",Cw="K36",Nw=32;function _o(t){return globalThis.Buffer!=null?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):t}function Nu(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?_o(globalThis.Buffer.allocUnsafe(t)):new Uint8Array(t)}function Ru(t,e){e||(e=t.reduce((i,s)=>i+s.length,0));const r=Nu(e);let n=0;for(const i of t)r.set(i,n),n+=i.length;return _o(r)}function Rw(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var s=t.charAt(i),o=s.charCodeAt(0);if(r[o]!==255)throw new TypeError(s+" is ambiguous");r[o]=i}var a=t.length,c=t.charAt(0),u=Math.log(a)/Math.log(256),l=Math.log(256)/Math.log(a);function h(p){if(p instanceof Uint8Array||(ArrayBuffer.isView(p)?p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength):Array.isArray(p)&&(p=Uint8Array.from(p))),!(p instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(p.length===0)return"";for(var y=0,g=0,v=0,b=p.length;v!==b&&p[v]===0;)v++,y++;for(var E=(b-v)*l+1>>>0,_=new Uint8Array(E);v!==b;){for(var P=p[v],$=0,O=E-1;(P!==0||$<g)&&O!==-1;O--,$++)P+=256*_[O]>>>0,_[O]=P%a>>>0,P=P/a>>>0;if(P!==0)throw new Error("Non-zero carry");g=$,v++}for(var C=E-g;C!==E&&_[C]===0;)C++;for(var S=c.repeat(y);C<E;++C)S+=t.charAt(_[C]);return S}function d(p){if(typeof p!="string")throw new TypeError("Expected String");if(p.length===0)return new Uint8Array;var y=0;if(p[y]!==" "){for(var g=0,v=0;p[y]===c;)g++,y++;for(var b=(p.length-y)*u+1>>>0,E=new Uint8Array(b);p[y];){var _=r[p.charCodeAt(y)];if(_===255)return;for(var P=0,$=b-1;(_!==0||P<v)&&$!==-1;$--,P++)_+=a*E[$]>>>0,E[$]=_%256>>>0,_=_/256>>>0;if(_!==0)throw new Error("Non-zero carry");v=P,y++}if(p[y]!==" "){for(var O=b-v;O!==b&&E[O]===0;)O++;for(var C=new Uint8Array(g+(b-O)),S=g;O!==b;)C[S++]=E[O++];return C}}}function f(p){var y=d(p);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:h,decodeUnsafe:d,decode:f}}var Tw=Rw,Bw=Tw;const Tu=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},Fw=t=>new TextEncoder().encode(t),Lw=t=>new TextDecoder().decode(t);class Uw{constructor(e,r,n){this.name=e,this.prefix=r,this.baseEncode=n}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class jw{constructor(e,r,n){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=n}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return Bu(this,e)}}class qw{constructor(e){this.decoders=e}or(e){return Bu(this,e)}decode(e){const r=e[0],n=this.decoders[r];if(n)return n.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const Bu=(t,e)=>new qw({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class kw{constructor(e,r,n,i){this.name=e,this.prefix=r,this.baseEncode=n,this.baseDecode=i,this.encoder=new Uw(e,r,n),this.decoder=new jw(e,r,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const Ji=({name:t,prefix:e,encode:r,decode:n})=>new kw(t,e,r,n),Jn=({prefix:t,name:e,alphabet:r})=>{const{encode:n,decode:i}=Bw(r,e);return Ji({prefix:t,name:e,encode:n,decode:s=>Tu(i(s))})},Mw=(t,e,r,n)=>{const i={};for(let l=0;l<e.length;++l)i[e[l]]=l;let s=t.length;for(;t[s-1]==="=";)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,u=0;for(let l=0;l<s;++l){const h=i[t[l]];if(h===void 0)throw new SyntaxError(`Non-${n} character`);c=c<<r|h,a+=r,a>=8&&(a-=8,o[u++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},zw=(t,e,r)=>{const n=e[e.length-1]==="=",i=(1<<r)-1;let s="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,s+=e[i&a>>o];if(o&&(s+=e[i&a<<r-o]),n)for(;s.length*r&7;)s+="=";return s},Ce=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>Ji({prefix:e,name:t,encode(i){return zw(i,n,r)},decode(i){return Mw(i,n,r,t)}}),Hw=Ji({prefix:"\0",name:"identity",encode:t=>Lw(t),decode:t=>Fw(t)});var Vw=Object.freeze({__proto__:null,identity:Hw});const Kw=Ce({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var Ww=Object.freeze({__proto__:null,base2:Kw});const Gw=Ce({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var Yw=Object.freeze({__proto__:null,base8:Gw});const Zw=Jn({prefix:"9",name:"base10",alphabet:"0123456789"});var Jw=Object.freeze({__proto__:null,base10:Zw});const Xw=Ce({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),Qw=Ce({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var eb=Object.freeze({__proto__:null,base16:Xw,base16upper:Qw});const tb=Ce({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),rb=Ce({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),nb=Ce({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),ib=Ce({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),sb=Ce({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),ob=Ce({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),ab=Ce({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),cb=Ce({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),ub=Ce({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var lb=Object.freeze({__proto__:null,base32:tb,base32upper:rb,base32pad:nb,base32padupper:ib,base32hex:sb,base32hexupper:ob,base32hexpad:ab,base32hexpadupper:cb,base32z:ub});const hb=Jn({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),db=Jn({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var fb=Object.freeze({__proto__:null,base36:hb,base36upper:db});const pb=Jn({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),gb=Jn({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var yb=Object.freeze({__proto__:null,base58btc:pb,base58flickr:gb});const mb=Ce({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),wb=Ce({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),bb=Ce({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),vb=Ce({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var Eb=Object.freeze({__proto__:null,base64:mb,base64pad:wb,base64url:bb,base64urlpad:vb});const Fu=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),_b=Fu.reduce((t,e,r)=>(t[r]=e,t),[]),Ib=Fu.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function Sb(t){return t.reduce((e,r)=>(e+=_b[r],e),"")}function xb(t){const e=[];for(const r of t){const n=Ib[r.codePointAt(0)];if(n===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(n)}return new Uint8Array(e)}const Ob=Ji({prefix:"\u{1F680}",name:"base256emoji",encode:Sb,decode:xb});var Db=Object.freeze({__proto__:null,base256emoji:Ob}),$b=Uu,Lu=128,Ab=127,Pb=~Ab,Cb=Math.pow(2,31);function Uu(t,e,r){e=e||[],r=r||0;for(var n=r;t>=Cb;)e[r++]=t&255|Lu,t/=128;for(;t&Pb;)e[r++]=t&255|Lu,t>>>=7;return e[r]=t|0,Uu.bytes=r-n+1,e}var Nb=Io,Rb=128,ju=127;function Io(t,n){var r=0,n=n||0,i=0,s=n,o,a=t.length;do{if(s>=a)throw Io.bytes=0,new RangeError("Could not decode varint");o=t[s++],r+=i<28?(o&ju)<<i:(o&ju)*Math.pow(2,i),i+=7}while(o>=Rb);return Io.bytes=s-n,r}var Tb=Math.pow(2,7),Bb=Math.pow(2,14),Fb=Math.pow(2,21),Lb=Math.pow(2,28),Ub=Math.pow(2,35),jb=Math.pow(2,42),qb=Math.pow(2,49),kb=Math.pow(2,56),Mb=Math.pow(2,63),zb=function(t){return t<Tb?1:t<Bb?2:t<Fb?3:t<Lb?4:t<Ub?5:t<jb?6:t<qb?7:t<kb?8:t<Mb?9:10},Hb={encode:$b,decode:Nb,encodingLength:zb},qu=Hb;const ku=(t,e,r=0)=>(qu.encode(t,e,r),e),Mu=t=>qu.encodingLength(t),So=(t,e)=>{const r=e.byteLength,n=Mu(t),i=n+Mu(r),s=new Uint8Array(i+r);return ku(t,s,0),ku(r,s,n),s.set(e,i),new Vb(t,r,e,s)};class Vb{constructor(e,r,n,i){this.code=e,this.size=r,this.digest=n,this.bytes=i}}const zu=({name:t,code:e,encode:r})=>new Kb(t,e,r);class Kb{constructor(e,r,n){this.name=e,this.code=r,this.encode=n}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?So(this.code,r):r.then(n=>So(this.code,n))}else throw Error("Unknown type, must be binary type")}}const Hu=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),Wb=zu({name:"sha2-256",code:18,encode:Hu("SHA-256")}),Gb=zu({name:"sha2-512",code:19,encode:Hu("SHA-512")});var Yb=Object.freeze({__proto__:null,sha256:Wb,sha512:Gb});const Vu=0,Zb="identity",Ku=Tu;var Jb=Object.freeze({__proto__:null,identity:{code:Vu,name:Zb,encode:Ku,digest:t=>So(Vu,Ku(t))}});new TextEncoder,new TextDecoder;const Wu={...Vw,...Ww,...Yw,...Jw,...eb,...lb,...fb,...yb,...Eb,...Db};({...Yb,...Jb});function Gu(t,e,r,n){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:n}}}const Yu=Gu("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),xo=Gu("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=Nu(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),Zu={utf8:Yu,"utf-8":Yu,hex:Wu.base16,latin1:xo,ascii:xo,binary:xo,...Wu};function Xi(t,e="utf8"){const r=Zu[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8"):r.encoder.encode(t).substring(1)}function Xr(t,e="utf8"){const r=Zu[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?_o(globalThis.Buffer.from(t,"utf-8")):r.decoder.decode(`${r.prefix}${t}`)}function Ju(t){return Or(Xi(Xr(t,Zi),Au))}function Qi(t){return Xi(Xr(Ut(t),Au),Zi)}function Xu(t){const e=Xr(Cw,Cu),r=Pw+Xi(Ru([e,t]),Cu);return[$w,Aw,r].join(Dw)}function Xb(t){return Xi(t,Zi)}function Qb(t){return Xr(t,Zi)}function e0(t){return Xr([Qi(t.header),Qi(t.payload)].join(Yi),Pu)}function t0(t){return[Qi(t.header),Qi(t.payload),Xb(t.signature)].join(Yi)}function Oo(t){const e=t.split(Yi),r=Ju(e[0]),n=Ju(e[1]),i=Qb(e[2]),s=Xr(e.slice(0,2).join(Yi),Pu);return{header:r,payload:n,signature:i,data:s}}function Qu(t=uu(Nw)){const e=$u.getPublicKey(t);return{secretKey:Ru([t,e]),publicKey:e}}async function r0(t,e,r,n,i=L.fromMiliseconds(Date.now())){const s={alg:xw,typ:Ow},o=Xu(n.publicKey),a=i+r,c={iss:o,sub:t,aud:e,iat:i,exp:a},u=e0({header:s,payload:c}),l=$u.sign(u,n.secretKey.slice(0,32));return t0({header:s,payload:c,signature:l})}var el=function(t,e,r){if(r||arguments.length===2)for(var n=0,i=e.length,s;n<i;n++)(s||!(n in e))&&(s||(s=Array.prototype.slice.call(e,0,n)),s[n]=e[n]);return t.concat(s||Array.prototype.slice.call(e))},n0=function(){function t(e,r,n){this.name=e,this.version=r,this.os=n,this.type="browser"}return t}(),i0=function(){function t(e){this.version=e,this.type="node",this.name="node",this.os=process.platform}return t}(),s0=function(){function t(e,r,n,i){this.name=e,this.version=r,this.os=n,this.bot=i,this.type="bot-device"}return t}(),o0=function(){function t(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null}return t}(),a0=function(){function t(){this.type="react-native",this.name="react-native",this.version=null,this.os=null}return t}(),c0=/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/,u0=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,tl=3,l0=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",c0]],rl=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function h0(t){return t?nl(t):typeof document>"u"&&typeof navigator<"u"&&navigator.product==="ReactNative"?new a0:typeof navigator<"u"?nl(navigator.userAgent):p0()}function d0(t){return t!==""&&l0.reduce(function(e,r){var n=r[0],i=r[1];if(e)return e;var s=i.exec(t);return!!s&&[n,s]},!1)}function nl(t){var e=d0(t);if(!e)return null;var r=e[0],n=e[1];if(r==="searchbot")return new o0;var i=n[1]&&n[1].split(".").join("_").split("_").slice(0,3);i?i.length<tl&&(i=el(el([],i,!0),g0(tl-i.length),!0)):i=[];var s=i.join("."),o=f0(t),a=u0.exec(t);return a&&a[1]?new s0(r,s,o,a[1]):new n0(r,s,o)}function f0(t){for(var e=0,r=rl.length;e<r;e++){var n=rl[e],i=n[0],s=n[1],o=s.exec(t);if(o)return i}return null}function p0(){var t=typeof process<"u"&&process.version;return t?new i0(process.version.slice(1)):null}function g0(t){for(var e=[],r=0;r<t;r++)e.push("0");return e}var ae={};Object.defineProperty(ae,"__esModule",{value:!0}),ae.getLocalStorage=ae.getLocalStorageOrThrow=ae.getCrypto=ae.getCryptoOrThrow=il=ae.getLocation=ae.getLocationOrThrow=Do=ae.getNavigator=ae.getNavigatorOrThrow=Ar=ae.getDocument=ae.getDocumentOrThrow=ae.getFromWindowOrThrow=ae.getFromWindow=void 0;function $r(t){let e;return typeof window<"u"&&typeof window[t]<"u"&&(e=window[t]),e}ae.getFromWindow=$r;function Qr(t){const e=$r(t);if(!e)throw new Error(`${t} is not defined in Window`);return e}ae.getFromWindowOrThrow=Qr;function y0(){return Qr("document")}ae.getDocumentOrThrow=y0;function m0(){return $r("document")}var Ar=ae.getDocument=m0;function w0(){return Qr("navigator")}ae.getNavigatorOrThrow=w0;function b0(){return $r("navigator")}var Do=ae.getNavigator=b0;function v0(){return Qr("location")}ae.getLocationOrThrow=v0;function E0(){return $r("location")}var il=ae.getLocation=E0;function _0(){return Qr("crypto")}ae.getCryptoOrThrow=_0;function I0(){return $r("crypto")}ae.getCrypto=I0;function S0(){return Qr("localStorage")}ae.getLocalStorageOrThrow=S0;function x0(){return $r("localStorage")}ae.getLocalStorage=x0;var $o={};Object.defineProperty($o,"__esModule",{value:!0});var sl=$o.getWindowMetadata=void 0;const ol=ae;function O0(){let t,e;try{t=ol.getDocumentOrThrow(),e=ol.getLocationOrThrow()}catch{return null}function r(){const h=t.getElementsByTagName("link"),d=[];for(let f=0;f<h.length;f++){const p=h[f],y=p.getAttribute("rel");if(y&&y.toLowerCase().indexOf("icon")>-1){const g=p.getAttribute("href");if(g)if(g.toLowerCase().indexOf("https:")===-1&&g.toLowerCase().indexOf("http:")===-1&&g.indexOf("//")!==0){let v=e.protocol+"//"+e.host;if(g.indexOf("/")===0)v+=g;else{const b=e.pathname.split("/");b.pop();const E=b.join("/");v+=E+"/"+g}d.push(v)}else if(g.indexOf("//")===0){const v=e.protocol+g;d.push(v)}else d.push(g)}}return d}function n(...h){const d=t.getElementsByTagName("meta");for(let f=0;f<d.length;f++){const p=d[f],y=["itemprop","property","name"].map(g=>p.getAttribute(g)).filter(g=>g?h.includes(g):!1);if(y.length&&y){const g=p.getAttribute("content");if(g)return g}}return""}function i(){let h=n("name","og:site_name","og:title","twitter:title");return h||(h=t.title),h}function s(){return n("description","og:description","twitter:description","keywords")}const o=i(),a=s(),c=e.origin,u=r();return{description:a,url:c,icons:u,name:o}}sl=$o.getWindowMetadata=O0;function Xn(t,{strict:e=!0}={}){return!t||typeof t!="string"?!1:e?/^0x[0-9a-fA-F]*$/.test(t):t.startsWith("0x")}function al(t){return Xn(t,{strict:!1})?Math.ceil((t.length-2)/2):t.length}const cl="2.23.2";let Ao={getDocsUrl:({docsBaseUrl:t,docsPath:e="",docsSlug:r})=>e?`${t??"https://viem.sh"}${e}${r?`#${r}`:""}`:void 0,version:`viem@${cl}`};class Pr extends Error{constructor(e,r={}){const n=r.cause instanceof Pr?r.cause.details:r.cause?.message?r.cause.message:r.details,i=r.cause instanceof Pr&&r.cause.docsPath||r.docsPath,s=Ao.getDocsUrl?.({...r,docsPath:i}),o=[e||"An error occurred.","",...r.metaMessages?[...r.metaMessages,""]:[],...s?[`Docs: ${s}`]:[],...n?[`Details: ${n}`]:[],...Ao.version?[`Version: ${Ao.version}`]:[]].join(`
`);super(o,r.cause?{cause:r.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metaMessages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),this.details=n,this.docsPath=i,this.metaMessages=r.metaMessages,this.name=r.name??this.name,this.shortMessage=e,this.version=cl}walk(e){return ul(this,e)}}function ul(t,e){return e?.(t)?t:t&&typeof t=="object"&&"cause"in t&&t.cause!==void 0?ul(t.cause,e):e?null:t}class ll extends Pr{constructor({size:e,targetSize:r,type:n}){super(`${n.charAt(0).toUpperCase()}${n.slice(1).toLowerCase()} size (${e}) exceeds padding size (${r}).`,{name:"SizeExceedsPaddingSizeError"})}}function en(t,{dir:e,size:r=32}={}){return typeof t=="string"?D0(t,{dir:e,size:r}):$0(t,{dir:e,size:r})}function D0(t,{dir:e,size:r=32}={}){if(r===null)return t;const n=t.replace("0x","");if(n.length>r*2)throw new ll({size:Math.ceil(n.length/2),targetSize:r,type:"hex"});return`0x${n[e==="right"?"padEnd":"padStart"](r*2,"0")}`}function $0(t,{dir:e,size:r=32}={}){if(r===null)return t;if(t.length>r)throw new ll({size:t.length,targetSize:r,type:"bytes"});const n=new Uint8Array(r);for(let i=0;i<r;i++){const s=e==="right";n[s?i:r-i-1]=t[s?i:t.length-i-1]}return n}class A0 extends Pr{constructor({max:e,min:r,signed:n,size:i,value:s}){super(`Number "${s}" is not in safe ${i?`${i*8}-bit ${n?"signed":"unsigned"} `:""}integer range ${e?`(${r} to ${e})`:`(above ${r})`}`,{name:"IntegerOutOfRangeError"})}}class P0 extends Pr{constructor({givenSize:e,maxSize:r}){super(`Size cannot exceed ${r} bytes. Given size: ${e} bytes.`,{name:"SizeOverflowError"})}}function tn(t,{size:e}){if(al(t)>e)throw new P0({givenSize:al(t),maxSize:e})}function Po(t,e={}){const{signed:r}=e;e.size&&tn(t,{size:e.size});const n=BigInt(t);if(!r)return n;const i=(t.length-2)/2,s=(1n<<BigInt(i)*8n-1n)-1n;return n<=s?n:n-BigInt(`0x${"f".padStart(i*2,"f")}`)-1n}function C0(t,e={}){return Number(Po(t,e))}const N0=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function Co(t,e={}){return typeof t=="number"||typeof t=="bigint"?dl(t,e):typeof t=="string"?B0(t,e):typeof t=="boolean"?R0(t,e):hl(t,e)}function R0(t,e={}){const r=`0x${Number(t)}`;return typeof e.size=="number"?(tn(r,{size:e.size}),en(r,{size:e.size})):r}function hl(t,e={}){let r="";for(let i=0;i<t.length;i++)r+=N0[t[i]];const n=`0x${r}`;return typeof e.size=="number"?(tn(n,{size:e.size}),en(n,{dir:"right",size:e.size})):n}function dl(t,e={}){const{signed:r,size:n}=e,i=BigInt(t);let s;n?r?s=(1n<<BigInt(n)*8n-1n)-1n:s=2n**(BigInt(n)*8n)-1n:typeof t=="number"&&(s=BigInt(Number.MAX_SAFE_INTEGER));const o=typeof s=="bigint"&&r?-s-1n:0;if(s&&i>s||i<o){const c=typeof t=="bigint"?"n":"";throw new A0({max:s?`${s}${c}`:void 0,min:`${o}${c}`,signed:r,size:n,value:`${t}${c}`})}const a=`0x${(r&&i<0?(1n<<BigInt(n*8))+BigInt(i):i).toString(16)}`;return n?en(a,{size:n}):a}const T0=new TextEncoder;function B0(t,e={}){const r=T0.encode(t);return hl(r,e)}const F0=new TextEncoder;function L0(t,e={}){return typeof t=="number"||typeof t=="bigint"?j0(t,e):typeof t=="boolean"?U0(t,e):Xn(t)?pl(t,e):gl(t,e)}function U0(t,e={}){const r=new Uint8Array(1);return r[0]=Number(t),typeof e.size=="number"?(tn(r,{size:e.size}),en(r,{size:e.size})):r}const kt={zero:48,nine:57,A:65,F:70,a:97,f:102};function fl(t){if(t>=kt.zero&&t<=kt.nine)return t-kt.zero;if(t>=kt.A&&t<=kt.F)return t-(kt.A-10);if(t>=kt.a&&t<=kt.f)return t-(kt.a-10)}function pl(t,e={}){let r=t;e.size&&(tn(r,{size:e.size}),r=en(r,{dir:"right",size:e.size}));let n=r.slice(2);n.length%2&&(n=`0${n}`);const i=n.length/2,s=new Uint8Array(i);for(let o=0,a=0;o<i;o++){const c=fl(n.charCodeAt(a++)),u=fl(n.charCodeAt(a++));if(c===void 0||u===void 0)throw new Pr(`Invalid byte sequence ("${n[a-2]}${n[a-1]}" in "${n}").`);s[o]=c*16+u}return s}function j0(t,e){const r=dl(t,e);return pl(r)}function gl(t,e={}){const r=F0.encode(t);return typeof e.size=="number"?(tn(r,{size:e.size}),en(r,{dir:"right",size:e.size})):r}function es(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function q0(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function Qn(t,...e){if(!q0(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function k0(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");es(t.outputLen),es(t.blockLen)}function rn(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function yl(t,e){Qn(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const ts=BigInt(2**32-1),ml=BigInt(32);function M0(t,e=!1){return e?{h:Number(t&ts),l:Number(t>>ml&ts)}:{h:Number(t>>ml&ts)|0,l:Number(t&ts)|0}}function z0(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:s,l:o}=M0(t[i],e);[r[i],n[i]]=[s,o]}return[r,n]}const H0=(t,e,r)=>t<<r|e>>>32-r,V0=(t,e,r)=>e<<r|t>>>32-r,K0=(t,e,r)=>e<<r-32|t>>>64-r,W0=(t,e,r)=>t<<r-32|e>>>64-r,nn=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;function G0(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function No(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function Ot(t,e){return t<<32-e|t>>>e}const wl=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Y0(t){return t<<24&4278190080|t<<8&16711680|t>>>8&65280|t>>>24&255}function bl(t){for(let e=0;e<t.length;e++)t[e]=Y0(t[e])}function Z0(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function rs(t){return typeof t=="string"&&(t=Z0(t)),Qn(t),t}function J0(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];Qn(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}class Ro{clone(){return this._cloneInto()}}function vl(t){const e=n=>t().update(rs(n)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function El(t=32){if(nn&&typeof nn.getRandomValues=="function")return nn.getRandomValues(new Uint8Array(t));if(nn&&typeof nn.randomBytes=="function")return nn.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}const _l=[],Il=[],Sl=[],X0=BigInt(0),ei=BigInt(1),Q0=BigInt(2),e1=BigInt(7),t1=BigInt(256),r1=BigInt(113);for(let t=0,e=ei,r=1,n=0;t<24;t++){[r,n]=[n,(2*r+3*n)%5],_l.push(2*(5*n+r)),Il.push((t+1)*(t+2)/2%64);let i=X0;for(let s=0;s<7;s++)e=(e<<ei^(e>>e1)*r1)%t1,e&Q0&&(i^=ei<<(ei<<BigInt(s))-ei);Sl.push(i)}const[n1,i1]=z0(Sl,!0),xl=(t,e,r)=>r>32?K0(t,e,r):H0(t,e,r),Ol=(t,e,r)=>r>32?W0(t,e,r):V0(t,e,r);function s1(t,e=24){const r=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let o=0;o<10;o++)r[o]=t[o]^t[o+10]^t[o+20]^t[o+30]^t[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,c=(o+2)%10,u=r[c],l=r[c+1],h=xl(u,l,1)^r[a],d=Ol(u,l,1)^r[a+1];for(let f=0;f<50;f+=10)t[o+f]^=h,t[o+f+1]^=d}let i=t[2],s=t[3];for(let o=0;o<24;o++){const a=Il[o],c=xl(i,s,a),u=Ol(i,s,a),l=_l[o];i=t[l],s=t[l+1],t[l]=c,t[l+1]=u}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)r[a]=t[o+a];for(let a=0;a<10;a++)t[o+a]^=~r[(a+2)%10]&r[(a+4)%10]}t[0]^=n1[n],t[1]^=i1[n]}r.fill(0)}class To extends Ro{constructor(e,r,n,i=!1,s=24){if(super(),this.blockLen=e,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=s,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,es(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=G0(this.state)}keccak(){wl||bl(this.state32),s1(this.state32,this.rounds),wl||bl(this.state32),this.posOut=0,this.pos=0}update(e){rn(this);const{blockLen:r,state:n}=this;e=rs(e);const i=e.length;for(let s=0;s<i;){const o=Math.min(r-this.pos,i-s);for(let a=0;a<o;a++)n[this.pos++]^=e[s++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:r,pos:n,blockLen:i}=this;e[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),e[i-1]^=128,this.keccak()}writeInto(e){rn(this,!1),Qn(e),this.finish();const r=this.state,{blockLen:n}=this;for(let i=0,s=e.length;i<s;){this.posOut>=n&&this.keccak();const o=Math.min(n-this.posOut,s-i);e.set(r.subarray(this.posOut,this.posOut+o),i),this.posOut+=o,i+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return es(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(yl(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:r,suffix:n,outputLen:i,rounds:s,enableXOF:o}=this;return e||(e=new To(r,n,i,o,s)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=s,e.suffix=n,e.outputLen=i,e.enableXOF=o,e.destroyed=this.destroyed,e}}const Dl=((t,e,r)=>vl(()=>new To(e,t,r)))(1,136,256/8);function $l(t,e){const r=e||"hex",n=Dl(Xn(t,{strict:!1})?L0(t):t);return r==="bytes"?n:Co(n)}class Al extends Map{constructor(e){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=e}get(e){const r=super.get(e);return super.has(e)&&r!==void 0&&(this.delete(e),super.set(e,r)),r}set(e,r){if(super.set(e,r),this.maxSize&&this.size>this.maxSize){const n=this.keys().next().value;n&&this.delete(n)}return this}}const Bo=new Al(8192);function o1(t,e){if(Bo.has(`${t}.${e}`))return Bo.get(`${t}.${e}`);const r=e?`${e}${t.toLowerCase()}`:t.substring(2).toLowerCase(),n=$l(gl(r),"bytes"),i=(e?r.substring(`${e}0x`.length):r).split("");for(let o=0;o<40;o+=2)n[o>>1]>>4>=8&&i[o]&&(i[o]=i[o].toUpperCase()),(n[o>>1]&15)>=8&&i[o+1]&&(i[o+1]=i[o+1].toUpperCase());const s=`0x${i.join("")}`;return Bo.set(`${t}.${e}`,s),s}function a1(t){const e=$l(`0x${t.substring(4)}`).substring(26);return o1(`0x${e}`)}async function c1({hash:t,signature:e}){const r=Xn(t)?t:Co(t),{secp256k1:n}=await Promise.resolve().then(function(){return Mv});return`0x${(()=>{if(typeof e=="object"&&"r"in e&&"s"in e){const{r:u,s:l,v:h,yParity:d}=e,f=Number(d??h),p=Pl(f);return new n.Signature(Po(u),Po(l)).addRecoveryBit(p)}const o=Xn(e)?e:Co(e),a=C0(`0x${o.slice(130)}`),c=Pl(a);return n.Signature.fromCompact(o.substring(2,130)).addRecoveryBit(c)})().recoverPublicKey(r.substring(2)).toHex(!1)}`}function Pl(t){if(t===0||t===1)return t;if(t===27)return 0;if(t===28)return 1;throw new Error("Invalid yParityOrV value")}async function u1({hash:t,signature:e}){return a1(await c1({hash:t,signature:e}))}function l1(t,e,r,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,n);const i=BigInt(32),s=BigInt(**********),o=Number(r>>i&s),a=Number(r&s),c=n?4:0,u=n?0:4;t.setUint32(e+c,o,n),t.setUint32(e+u,a,n)}function h1(t,e,r){return t&e^~t&r}function d1(t,e,r){return t&e^t&r^e&r}class f1 extends Ro{constructor(e,r,n,i){super(),this.blockLen=e,this.outputLen=r,this.padOffset=n,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=No(this.buffer)}update(e){rn(this);const{view:r,buffer:n,blockLen:i}=this;e=rs(e);const s=e.length;for(let o=0;o<s;){const a=Math.min(i-this.pos,s-o);if(a===i){const c=No(e);for(;i<=s-o;o+=i)this.process(c,o);continue}n.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===i&&(this.process(r,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){rn(this),yl(e,this),this.finished=!0;const{buffer:r,view:n,blockLen:i,isLE:s}=this;let{pos:o}=this;r[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>i-o&&(this.process(n,0),o=0);for(let h=o;h<i;h++)r[h]=0;l1(n,i-8,BigInt(this.length*8),s),this.process(n,0);const a=No(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=c/4,l=this.get();if(u>l.length)throw new Error("_sha2: outputLen bigger than state");for(let h=0;h<u;h++)a.setUint32(4*h,l[h],s)}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const n=e.slice(0,r);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:r,buffer:n,length:i,finished:s,destroyed:o,pos:a}=this;return e.length=i,e.pos=a,e.finished=s,e.destroyed=o,i%r&&e.buffer.set(n),e}}const p1=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),nr=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),ir=new Uint32Array(64);class g1 extends f1{constructor(){super(64,32,8,!1),this.A=nr[0]|0,this.B=nr[1]|0,this.C=nr[2]|0,this.D=nr[3]|0,this.E=nr[4]|0,this.F=nr[5]|0,this.G=nr[6]|0,this.H=nr[7]|0}get(){const{A:e,B:r,C:n,D:i,E:s,F:o,G:a,H:c}=this;return[e,r,n,i,s,o,a,c]}set(e,r,n,i,s,o,a,c){this.A=e|0,this.B=r|0,this.C=n|0,this.D=i|0,this.E=s|0,this.F=o|0,this.G=a|0,this.H=c|0}process(e,r){for(let h=0;h<16;h++,r+=4)ir[h]=e.getUint32(r,!1);for(let h=16;h<64;h++){const d=ir[h-15],f=ir[h-2],p=Ot(d,7)^Ot(d,18)^d>>>3,y=Ot(f,17)^Ot(f,19)^f>>>10;ir[h]=y+ir[h-7]+p+ir[h-16]|0}let{A:n,B:i,C:s,D:o,E:a,F:c,G:u,H:l}=this;for(let h=0;h<64;h++){const d=Ot(a,6)^Ot(a,11)^Ot(a,25),f=l+d+h1(a,c,u)+p1[h]+ir[h]|0,y=(Ot(n,2)^Ot(n,13)^Ot(n,22))+d1(n,i,s)|0;l=u,u=c,c=a,a=o+f|0,o=s,s=i,i=n,n=f+y|0}n=n+this.A|0,i=i+this.B|0,s=s+this.C|0,o=o+this.D|0,a=a+this.E|0,c=c+this.F|0,u=u+this.G|0,l=l+this.H|0,this.set(n,i,s,o,a,c,u,l)}roundClean(){ir.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const ns=vl(()=>new g1);new Al(128);const y1=new Uint8Array([7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8]),Cl=new Uint8Array(new Array(16).fill(0).map((t,e)=>e)),m1=Cl.map(t=>(9*t+5)%16);let w1=[Cl],b1=[m1];for(let t=0;t<4;t++)for(let e of[w1,b1])e.push(e[t].map(r=>y1[r]));/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const is=BigInt(0),ss=BigInt(1),v1=BigInt(2);function Cr(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function Dt(t){if(!Cr(t))throw new Error("Uint8Array expected")}function sn(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const E1=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function on(t){Dt(t);let e="";for(let r=0;r<t.length;r++)e+=E1[t[r]];return e}function an(t){const e=t.toString(16);return e.length&1?"0"+e:e}function Fo(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?is:BigInt("0x"+t)}const Mt={_0:48,_9:57,A:65,F:70,a:97,f:102};function Nl(t){if(t>=Mt._0&&t<=Mt._9)return t-Mt._0;if(t>=Mt.A&&t<=Mt.F)return t-(Mt.A-10);if(t>=Mt.a&&t<=Mt.f)return t-(Mt.a-10)}function cn(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const n=new Uint8Array(r);for(let i=0,s=0;i<r;i++,s+=2){const o=Nl(t.charCodeAt(s)),a=Nl(t.charCodeAt(s+1));if(o===void 0||a===void 0){const c=t[s]+t[s+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+s)}n[i]=o*16+a}return n}function $t(t){return Fo(on(t))}function Lo(t){return Dt(t),Fo(on(Uint8Array.from(t).reverse()))}function sr(t,e){return cn(t.toString(16).padStart(e*2,"0"))}function Uo(t,e){return sr(t,e).reverse()}function _1(t){return cn(an(t))}function je(t,e,r){let n;if(typeof e=="string")try{n=cn(e)}catch(s){throw new Error(t+" must be hex string or Uint8Array, cause: "+s)}else if(Cr(e))n=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const i=n.length;if(typeof r=="number"&&i!==r)throw new Error(t+" of length "+r+" expected, got "+i);return n}function et(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];Dt(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}function I1(t,e){if(t.length!==e.length)return!1;let r=0;for(let n=0;n<t.length;n++)r|=t[n]^e[n];return r===0}function os(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}const jo=t=>typeof t=="bigint"&&is<=t;function un(t,e,r){return jo(t)&&jo(e)&&jo(r)&&e<=t&&t<r}function or(t,e,r,n){if(!un(e,r,n))throw new Error("expected valid "+t+": "+r+" <= n < "+n+", got "+e)}function Rl(t){let e;for(e=0;t>is;t>>=ss,e+=1);return e}function S1(t,e){return t>>BigInt(e)&ss}function x1(t,e,r){return t|(r?ss:is)<<BigInt(e)}const qo=t=>(v1<<BigInt(t-1))-ss,ko=t=>new Uint8Array(t),Tl=t=>Uint8Array.from(t);function Bl(t,e,r){if(typeof t!="number"||t<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof r!="function")throw new Error("hmacFn must be a function");let n=ko(t),i=ko(t),s=0;const o=()=>{n.fill(1),i.fill(0),s=0},a=(...h)=>r(i,n,...h),c=(h=ko())=>{i=a(Tl([0]),h),n=a(),h.length!==0&&(i=a(Tl([1]),h),n=a())},u=()=>{if(s++>=1e3)throw new Error("drbg: tried 1000 values");let h=0;const d=[];for(;h<e;){n=a();const f=n.slice();d.push(f),h+=n.length}return et(...d)};return(h,d)=>{o(),c(h);let f;for(;!(f=d(u()));)c();return o(),f}}const O1={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||Cr(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function ln(t,e,r={}){const n=(i,s,o)=>{const a=O1[s];if(typeof a!="function")throw new Error("invalid validator function");const c=t[i];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(i)+" is invalid. Expected "+s+", got "+c)};for(const[i,s]of Object.entries(e))n(i,s,!1);for(const[i,s]of Object.entries(r))n(i,s,!0);return t}const D1=()=>{throw new Error("not implemented")};function Mo(t){const e=new WeakMap;return(r,...n)=>{const i=e.get(r);if(i!==void 0)return i;const s=t(r,...n);return e.set(r,s),s}}var $1=Object.freeze({__proto__:null,isBytes:Cr,abytes:Dt,abool:sn,bytesToHex:on,numberToHexUnpadded:an,hexToNumber:Fo,hexToBytes:cn,bytesToNumberBE:$t,bytesToNumberLE:Lo,numberToBytesBE:sr,numberToBytesLE:Uo,numberToVarBytesBE:_1,ensureBytes:je,concatBytes:et,equalBytes:I1,utf8ToBytes:os,inRange:un,aInRange:or,bitLen:Rl,bitGet:S1,bitSet:x1,bitMask:qo,createHmacDrbg:Bl,validateObject:ln,notImplemented:D1,memoized:Mo});const A1="0.1.1";function P1(){return A1}class Me extends Error{constructor(e,r={}){const n=(()=>{if(r.cause instanceof Me){if(r.cause.details)return r.cause.details;if(r.cause.shortMessage)return r.cause.shortMessage}return r.cause?.message?r.cause.message:r.details})(),i=r.cause instanceof Me&&r.cause.docsPath||r.docsPath,o=`https://oxlib.sh${i??""}`,a=[e||"An error occurred.",...r.metaMessages?["",...r.metaMessages]:[],...n||i?["",n?`Details: ${n}`:void 0,i?`See: ${o}`:void 0]:[]].filter(c=>typeof c=="string").join(`
`);super(a,r.cause?{cause:r.cause}:void 0),Object.defineProperty(this,"details",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"docsPath",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"shortMessage",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cause",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"BaseError"}),Object.defineProperty(this,"version",{enumerable:!0,configurable:!0,writable:!0,value:`ox@${P1()}`}),this.cause=r.cause,this.details=n,this.docs=o,this.docsPath=i,this.shortMessage=e}walk(e){return Fl(this,e)}}function Fl(t,e){return e?.(t)?t:t&&typeof t=="object"&&"cause"in t&&t.cause?Fl(t.cause,e):e?null:t}function C1(t,e){if(jl(t)>e)throw new j1({givenSize:jl(t),maxSize:e})}const zt={zero:48,nine:57,A:65,F:70,a:97,f:102};function Ll(t){if(t>=zt.zero&&t<=zt.nine)return t-zt.zero;if(t>=zt.A&&t<=zt.F)return t-(zt.A-10);if(t>=zt.a&&t<=zt.f)return t-(zt.a-10)}function N1(t,e={}){const{dir:r,size:n=32}=e;if(n===0)return t;if(t.length>n)throw new q1({size:t.length,targetSize:n,type:"Bytes"});const i=new Uint8Array(n);for(let s=0;s<n;s++){const o=r==="right";i[o?s:n-s-1]=t[o?s:t.length-s-1]}return i}function zo(t,e){if(Vo(t)>e)throw new W1({givenSize:Vo(t),maxSize:e})}function Ul(t,e={}){const{dir:r,size:n=32}=e;if(n===0)return t;const i=t.replace("0x","");if(i.length>n*2)throw new G1({size:Math.ceil(i.length/2),targetSize:n,type:"Hex"});return`0x${i[r==="right"?"padEnd":"padStart"](n*2,"0")}`}const R1=new TextEncoder;function T1(t){return t instanceof Uint8Array?t:typeof t=="string"?F1(t):B1(t)}function B1(t){return t instanceof Uint8Array?t:new Uint8Array(t)}function F1(t,e={}){const{size:r}=e;let n=t;r&&(zo(t,r),n=Ho(t,r));let i=n.slice(2);i.length%2&&(i=`0${i}`);const s=i.length/2,o=new Uint8Array(s);for(let a=0,c=0;a<s;a++){const u=Ll(i.charCodeAt(c++)),l=Ll(i.charCodeAt(c++));if(u===void 0||l===void 0)throw new Me(`Invalid byte sequence ("${i[c-2]}${i[c-1]}" in "${i}").`);o[a]=u*16+l}return o}function L1(t,e={}){const{size:r}=e,n=R1.encode(t);return typeof r=="number"?(C1(n,r),U1(n,r)):n}function U1(t,e){return N1(t,{dir:"right",size:e})}function jl(t){return t.length}class j1 extends Me{constructor({givenSize:e,maxSize:r}){super(`Size cannot exceed \`${r}\` bytes. Given size: \`${e}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeOverflowError"})}}class q1 extends Me{constructor({size:e,targetSize:r,type:n}){super(`${n.charAt(0).toUpperCase()}${n.slice(1).toLowerCase()} size (\`${e}\`) exceeds padding size (\`${r}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeExceedsPaddingSizeError"})}}const k1=new TextEncoder,M1=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function ql(...t){return`0x${t.reduce((e,r)=>e+r.replace("0x",""),"")}`}function z1(t,e={}){const r=`0x${Number(t)}`;return typeof e.size=="number"?(zo(r,e.size),as(r,e.size)):r}function kl(t,e={}){let r="";for(let i=0;i<t.length;i++)r+=M1[t[i]];const n=`0x${r}`;return typeof e.size=="number"?(zo(n,e.size),Ho(n,e.size)):n}function H1(t,e={}){const{signed:r,size:n}=e,i=BigInt(t);let s;n?r?s=(1n<<BigInt(n)*8n-1n)-1n:s=2n**(BigInt(n)*8n)-1n:typeof t=="number"&&(s=BigInt(Number.MAX_SAFE_INTEGER));const o=typeof s=="bigint"&&r?-s-1n:0;if(s&&i>s||i<o){const u=typeof t=="bigint"?"n":"";throw new K1({max:s?`${s}${u}`:void 0,min:`${o}${u}`,signed:r,size:n,value:`${t}${u}`})}const c=`0x${(r&&i<0?(1n<<BigInt(n*8))+BigInt(i):i).toString(16)}`;return n?as(c,n):c}function V1(t,e={}){return kl(k1.encode(t),e)}function as(t,e){return Ul(t,{dir:"left",size:e})}function Ho(t,e){return Ul(t,{dir:"right",size:e})}function Vo(t){return Math.ceil((t.length-2)/2)}class K1 extends Me{constructor({max:e,min:r,signed:n,size:i,value:s}){super(`Number \`${s}\` is not in safe${i?` ${i*8}-bit`:""}${n?" signed":" unsigned"} integer range ${e?`(\`${r}\` to \`${e}\`)`:`(above \`${r}\`)`}`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.IntegerOutOfRangeError"})}}class W1 extends Me{constructor({givenSize:e,maxSize:r}){super(`Size cannot exceed \`${r}\` bytes. Given size: \`${e}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeOverflowError"})}}class G1 extends Me{constructor({size:e,targetSize:r,type:n}){super(`${n.charAt(0).toUpperCase()}${n.slice(1).toLowerCase()} size (\`${e}\`) exceeds padding size (\`${r}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Hex.SizeExceedsPaddingSizeError"})}}function Y1(t,e={}){const{as:r=typeof t=="string"?"Hex":"Bytes"}=e,n=Dl(T1(t));return r==="Bytes"?n:kl(n)}class Z1 extends Map{constructor(e){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=e}get(e){const r=super.get(e);return super.has(e)&&r!==void 0&&(this.delete(e),super.set(e,r)),r}set(e,r){if(super.set(e,r),this.maxSize&&this.size>this.maxSize){const n=this.keys().next().value;n&&this.delete(n)}return this}}const Ko={checksum:new Z1(8192)}.checksum,J1=/^0x[a-fA-F0-9]{40}$/;function Ml(t,e={}){const{strict:r=!0}=e;if(!J1.test(t))throw new zl({address:t,cause:new Q1});if(r){if(t.toLowerCase()===t)return;if(X1(t)!==t)throw new zl({address:t,cause:new ev})}}function X1(t){if(Ko.has(t))return Ko.get(t);Ml(t,{strict:!1});const e=t.substring(2).toLowerCase(),r=Y1(L1(e),{as:"Bytes"}),n=e.split("");for(let s=0;s<40;s+=2)r[s>>1]>>4>=8&&n[s]&&(n[s]=n[s].toUpperCase()),(r[s>>1]&15)>=8&&n[s+1]&&(n[s+1]=n[s+1].toUpperCase());const i=`0x${n.join("")}`;return Ko.set(t,i),i}class zl extends Me{constructor({address:e,cause:r}){super(`Address "${e}" is invalid.`,{cause:r}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidAddressError"})}}class Q1 extends Me{constructor(){super("Address is not a 20 byte (40 hexadecimal character) value."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidInputError"})}}class ev extends Me{constructor(){super("Address does not match its checksum counterpart."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidChecksumError"})}}const tv=/^(.*)\[([0-9]*)\]$/,rv=/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/,nv=/^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;function Wo(t,e){if(t.length!==e.length)throw new sv({expectedLength:t.length,givenLength:e.length});const r=[];for(let n=0;n<t.length;n++){const i=t[n],s=e[n];r.push(Wo.encode(i,s))}return ql(...r)}(function(t){function e(r,n,i=!1){if(r==="address"){const c=n;return Ml(c),as(c.toLowerCase(),i?32:0)}if(r==="string")return V1(n);if(r==="bytes")return n;if(r==="bool")return as(z1(n),i?32:1);const s=r.match(nv);if(s){const[c,u,l="256"]=s,h=Number.parseInt(l)/8;return H1(n,{size:i?32:h,signed:u==="int"})}const o=r.match(rv);if(o){const[c,u]=o;if(Number.parseInt(u)!==(n.length-2)/2)throw new iv({expectedSize:Number.parseInt(u),value:n});return Ho(n,i?32:0)}const a=r.match(tv);if(a&&Array.isArray(n)){const[c,u]=a,l=[];for(let h=0;h<n.length;h++)l.push(e(u,n[h],!0));return l.length===0?"0x":ql(...l)}throw new ov(r)}t.encode=e})(Wo||(Wo={}));class iv extends Me{constructor({expectedSize:e,value:r}){super(`Size of bytes "${r}" (bytes${Vo(r)}) does not match expected size (bytes${e}).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.BytesSizeMismatchError"})}}class sv extends Me{constructor({expectedLength:e,givenLength:r}){super(["ABI encoding parameters/values length mismatch.",`Expected length (parameters): ${e}`,`Given length (values): ${r}`].join(`
`)),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.LengthMismatchError"})}}class ov extends Me{constructor(e){super(`Type \`${e}\` is not a valid ABI Type.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.InvalidTypeError"})}}class Hl extends Ro{constructor(e,r){super(),this.finished=!1,this.destroyed=!1,k0(e);const n=rs(r);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const i=this.blockLen,s=new Uint8Array(i);s.set(n.length>i?e.create().update(n).digest():n);for(let o=0;o<s.length;o++)s[o]^=54;this.iHash.update(s),this.oHash=e.create();for(let o=0;o<s.length;o++)s[o]^=106;this.oHash.update(s),s.fill(0)}update(e){return rn(this),this.iHash.update(e),this}digestInto(e){rn(this),Qn(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:r,iHash:n,finished:i,destroyed:s,blockLen:o,outputLen:a}=this;return e=e,e.finished=i,e.destroyed=s,e.blockLen=o,e.outputLen=a,e.oHash=r._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Vl=(t,e,r)=>new Hl(t,e).update(r).digest();Vl.create=(t,e)=>new Hl(t,e);const Ie=BigInt(0),fe=BigInt(1),Nr=BigInt(2),av=BigInt(3),Go=BigInt(4),Kl=BigInt(5),Wl=BigInt(8);function Se(t,e){const r=t%e;return r>=Ie?r:e+r}function cv(t,e,r){if(e<Ie)throw new Error("invalid exponent, negatives unsupported");if(r<=Ie)throw new Error("invalid modulus");if(r===fe)return Ie;let n=fe;for(;e>Ie;)e&fe&&(n=n*t%r),t=t*t%r,e>>=fe;return n}function at(t,e,r){let n=t;for(;e-- >Ie;)n*=n,n%=r;return n}function Yo(t,e){if(t===Ie)throw new Error("invert: expected non-zero number");if(e<=Ie)throw new Error("invert: expected positive modulus, got "+e);let r=Se(t,e),n=e,i=Ie,s=fe;for(;r!==Ie;){const a=n/r,c=n%r,u=i-s*a;n=r,r=c,i=s,s=u}if(n!==fe)throw new Error("invert: does not exist");return Se(i,e)}function uv(t){const e=(t-fe)/Nr;let r,n,i;for(r=t-fe,n=0;r%Nr===Ie;r/=Nr,n++);for(i=Nr;i<t&&cv(i,e,t)!==t-fe;i++)if(i>1e3)throw new Error("Cannot find square root: likely non-prime P");if(n===1){const o=(t+fe)/Go;return function(c,u){const l=c.pow(u,o);if(!c.eql(c.sqr(l),u))throw new Error("Cannot find square root");return l}}const s=(r+fe)/Nr;return function(a,c){if(a.pow(c,e)===a.neg(a.ONE))throw new Error("Cannot find square root");let u=n,l=a.pow(a.mul(a.ONE,i),r),h=a.pow(c,s),d=a.pow(c,r);for(;!a.eql(d,a.ONE);){if(a.eql(d,a.ZERO))return a.ZERO;let f=1;for(let y=a.sqr(d);f<u&&!a.eql(y,a.ONE);f++)y=a.sqr(y);const p=a.pow(l,fe<<BigInt(u-f-1));l=a.sqr(p),h=a.mul(h,p),d=a.mul(d,l),u=f}return h}}function lv(t){if(t%Go===av){const e=(t+fe)/Go;return function(n,i){const s=n.pow(i,e);if(!n.eql(n.sqr(s),i))throw new Error("Cannot find square root");return s}}if(t%Wl===Kl){const e=(t-Kl)/Wl;return function(n,i){const s=n.mul(i,Nr),o=n.pow(s,e),a=n.mul(i,o),c=n.mul(n.mul(a,Nr),o),u=n.mul(a,n.sub(c,n.ONE));if(!n.eql(n.sqr(u),i))throw new Error("Cannot find square root");return u}}return uv(t)}const hv=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function Gl(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},r=hv.reduce((n,i)=>(n[i]="function",n),e);return ln(t,r)}function dv(t,e,r){if(r<Ie)throw new Error("invalid exponent, negatives unsupported");if(r===Ie)return t.ONE;if(r===fe)return e;let n=t.ONE,i=e;for(;r>Ie;)r&fe&&(n=t.mul(n,i)),i=t.sqr(i),r>>=fe;return n}function fv(t,e){const r=new Array(e.length),n=e.reduce((s,o,a)=>t.is0(o)?s:(r[a]=s,t.mul(s,o)),t.ONE),i=t.inv(n);return e.reduceRight((s,o,a)=>t.is0(o)?s:(r[a]=t.mul(s,r[a]),t.mul(s,o)),i),r}function Yl(t,e){const r=e!==void 0?e:t.toString(2).length,n=Math.ceil(r/8);return{nBitLength:r,nByteLength:n}}function Zl(t,e,r=!1,n={}){if(t<=Ie)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:i,nByteLength:s}=Yl(t,e);if(s>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:r,BITS:i,BYTES:s,MASK:qo(i),ZERO:Ie,ONE:fe,create:c=>Se(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return Ie<=c&&c<t},is0:c=>c===Ie,isOdd:c=>(c&fe)===fe,neg:c=>Se(-c,t),eql:(c,u)=>c===u,sqr:c=>Se(c*c,t),add:(c,u)=>Se(c+u,t),sub:(c,u)=>Se(c-u,t),mul:(c,u)=>Se(c*u,t),pow:(c,u)=>dv(a,c,u),div:(c,u)=>Se(c*Yo(u,t),t),sqrN:c=>c*c,addN:(c,u)=>c+u,subN:(c,u)=>c-u,mulN:(c,u)=>c*u,inv:c=>Yo(c,t),sqrt:n.sqrt||(c=>(o||(o=lv(t)),o(a,c))),invertBatch:c=>fv(a,c),cmov:(c,u,l)=>l?u:c,toBytes:c=>r?Uo(c,s):sr(c,s),fromBytes:c=>{if(c.length!==s)throw new Error("Field.fromBytes: expected "+s+" bytes, got "+c.length);return r?Lo(c):$t(c)}});return Object.freeze(a)}function Jl(t){if(typeof t!="bigint")throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function Xl(t){const e=Jl(t);return e+Math.ceil(e/2)}function pv(t,e,r=!1){const n=t.length,i=Jl(e),s=Xl(e);if(n<16||n<s||n>1024)throw new Error("expected "+s+"-1024 bytes of input, got "+n);const o=r?Lo(t):$t(t),a=Se(o,e-fe)+fe;return r?Uo(a,i):sr(a,i)}const Ql=BigInt(0),cs=BigInt(1);function Zo(t,e){const r=e.negate();return t?r:e}function eh(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function Jo(t,e){eh(t,e);const r=Math.ceil(e/t)+1,n=2**(t-1);return{windows:r,windowSize:n}}function gv(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((r,n)=>{if(!(r instanceof e))throw new Error("invalid point at index "+n)})}function yv(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((r,n)=>{if(!e.isValid(r))throw new Error("invalid scalar at index "+n)})}const Xo=new WeakMap,th=new WeakMap;function Qo(t){return th.get(t)||1}function mv(t,e){return{constTimeNegate:Zo,hasPrecomputes(r){return Qo(r)!==1},unsafeLadder(r,n,i=t.ZERO){let s=r;for(;n>Ql;)n&cs&&(i=i.add(s)),s=s.double(),n>>=cs;return i},precomputeWindow(r,n){const{windows:i,windowSize:s}=Jo(n,e),o=[];let a=r,c=a;for(let u=0;u<i;u++){c=a,o.push(c);for(let l=1;l<s;l++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(r,n,i){const{windows:s,windowSize:o}=Jo(r,e);let a=t.ZERO,c=t.BASE;const u=BigInt(2**r-1),l=2**r,h=BigInt(r);for(let d=0;d<s;d++){const f=d*o;let p=Number(i&u);i>>=h,p>o&&(p-=l,i+=cs);const y=f,g=f+Math.abs(p)-1,v=d%2!==0,b=p<0;p===0?c=c.add(Zo(v,n[y])):a=a.add(Zo(b,n[g]))}return{p:a,f:c}},wNAFUnsafe(r,n,i,s=t.ZERO){const{windows:o,windowSize:a}=Jo(r,e),c=BigInt(2**r-1),u=2**r,l=BigInt(r);for(let h=0;h<o;h++){const d=h*a;if(i===Ql)break;let f=Number(i&c);if(i>>=l,f>a&&(f-=u,i+=cs),f===0)continue;let p=n[d+Math.abs(f)-1];f<0&&(p=p.negate()),s=s.add(p)}return s},getPrecomputes(r,n,i){let s=Xo.get(n);return s||(s=this.precomputeWindow(n,r),r!==1&&Xo.set(n,i(s))),s},wNAFCached(r,n,i){const s=Qo(r);return this.wNAF(s,this.getPrecomputes(s,r,i),n)},wNAFCachedUnsafe(r,n,i,s){const o=Qo(r);return o===1?this.unsafeLadder(r,n,s):this.wNAFUnsafe(o,this.getPrecomputes(o,r,i),n,s)},setWindowSize(r,n){eh(n,e),th.set(r,n),Xo.delete(r)}}}function wv(t,e,r,n){if(gv(r,t),yv(n,e),r.length!==n.length)throw new Error("arrays of points and scalars must have equal length");const i=t.ZERO,s=Rl(BigInt(r.length)),o=s>12?s-3:s>4?s-2:s?2:1,a=(1<<o)-1,c=new Array(a+1).fill(i),u=Math.floor((e.BITS-1)/o)*o;let l=i;for(let h=u;h>=0;h-=o){c.fill(i);for(let f=0;f<n.length;f++){const p=n[f],y=Number(p>>BigInt(h)&BigInt(a));c[y]=c[y].add(r[f])}let d=i;for(let f=c.length-1,p=i;f>0;f--)p=p.add(c[f]),d=d.add(p);if(l=l.add(d),h!==0)for(let f=0;f<o;f++)l=l.double()}return l}function rh(t){return Gl(t.Fp),ln(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...Yl(t.n,t.nBitLength),...t,p:t.Fp.ORDER})}function nh(t){t.lowS!==void 0&&sn("lowS",t.lowS),t.prehash!==void 0&&sn("prehash",t.prehash)}function bv(t){const e=rh(t);ln(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:r,Fp:n,a:i}=e;if(r){if(!n.eql(i,n.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof r!="object"||typeof r.beta!="bigint"||typeof r.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze({...e})}const{bytesToNumberBE:vv,hexToBytes:Ev}=$1;class _v extends Error{constructor(e=""){super(e)}}const Ht={Err:_v,_tlv:{encode:(t,e)=>{const{Err:r}=Ht;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length&1)throw new r("tlv.encode: unpadded data");const n=e.length/2,i=an(n);if(i.length/2&128)throw new r("tlv.encode: long form length too big");const s=n>127?an(i.length/2|128):"";return an(t)+s+i+e},decode(t,e){const{Err:r}=Ht;let n=0;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length<2||e[n++]!==t)throw new r("tlv.decode: wrong tlv");const i=e[n++],s=!!(i&128);let o=0;if(!s)o=i;else{const c=i&127;if(!c)throw new r("tlv.decode(long): indefinite length not supported");if(c>4)throw new r("tlv.decode(long): byte length is too big");const u=e.subarray(n,n+c);if(u.length!==c)throw new r("tlv.decode: length bytes not complete");if(u[0]===0)throw new r("tlv.decode(long): zero leftmost byte");for(const l of u)o=o<<8|l;if(n+=c,o<128)throw new r("tlv.decode(long): not minimal encoding")}const a=e.subarray(n,n+o);if(a.length!==o)throw new r("tlv.decode: wrong value length");return{v:a,l:e.subarray(n+o)}}},_int:{encode(t){const{Err:e}=Ht;if(t<yt)throw new e("integer: negative integers are not allowed");let r=an(t);if(Number.parseInt(r[0],16)&8&&(r="00"+r),r.length&1)throw new e("unexpected DER parsing assertion: unpadded hex");return r},decode(t){const{Err:e}=Ht;if(t[0]&128)throw new e("invalid signature integer: negative");if(t[0]===0&&!(t[1]&128))throw new e("invalid signature integer: unnecessary leading zero");return vv(t)}},toSig(t){const{Err:e,_int:r,_tlv:n}=Ht,i=typeof t=="string"?Ev(t):t;Dt(i);const{v:s,l:o}=n.decode(48,i);if(o.length)throw new e("invalid signature: left bytes after parsing");const{v:a,l:c}=n.decode(2,s),{v:u,l}=n.decode(2,c);if(l.length)throw new e("invalid signature: left bytes after parsing");return{r:r.decode(a),s:r.decode(u)}},hexFromSig(t){const{_tlv:e,_int:r}=Ht,n=e.encode(2,r.encode(t.r)),i=e.encode(2,r.encode(t.s)),s=n+i;return e.encode(48,s)}},yt=BigInt(0),ne=BigInt(1),ar=BigInt(2),us=BigInt(3),ih=BigInt(4);function Iv(t){const e=bv(t),{Fp:r}=e,n=Zl(e.n,e.nBitLength),i=e.toBytes||((y,g,v)=>{const b=g.toAffine();return et(Uint8Array.from([4]),r.toBytes(b.x),r.toBytes(b.y))}),s=e.fromBytes||(y=>{const g=y.subarray(1),v=r.fromBytes(g.subarray(0,r.BYTES)),b=r.fromBytes(g.subarray(r.BYTES,2*r.BYTES));return{x:v,y:b}});function o(y){const{a:g,b:v}=e,b=r.sqr(y),E=r.mul(b,y);return r.add(r.add(E,r.mul(y,g)),v)}if(!r.eql(r.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function a(y){return un(y,ne,e.n)}function c(y){const{allowedPrivateKeyLengths:g,nByteLength:v,wrapPrivateKey:b,n:E}=e;if(g&&typeof y!="bigint"){if(Cr(y)&&(y=on(y)),typeof y!="string"||!g.includes(y.length))throw new Error("invalid private key");y=y.padStart(v*2,"0")}let _;try{_=typeof y=="bigint"?y:$t(je("private key",y,v))}catch{throw new Error("invalid private key, expected hex or "+v+" bytes, got "+typeof y)}return b&&(_=Se(_,E)),or("private key",_,ne,E),_}function u(y){if(!(y instanceof d))throw new Error("ProjectivePoint expected")}const l=Mo((y,g)=>{const{px:v,py:b,pz:E}=y;if(r.eql(E,r.ONE))return{x:v,y:b};const _=y.is0();g==null&&(g=_?r.ONE:r.inv(E));const P=r.mul(v,g),$=r.mul(b,g),O=r.mul(E,g);if(_)return{x:r.ZERO,y:r.ZERO};if(!r.eql(O,r.ONE))throw new Error("invZ was invalid");return{x:P,y:$}}),h=Mo(y=>{if(y.is0()){if(e.allowInfinityPoint&&!r.is0(y.py))return;throw new Error("bad point: ZERO")}const{x:g,y:v}=y.toAffine();if(!r.isValid(g)||!r.isValid(v))throw new Error("bad point: x or y not FE");const b=r.sqr(v),E=o(g);if(!r.eql(b,E))throw new Error("bad point: equation left != right");if(!y.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class d{constructor(g,v,b){if(this.px=g,this.py=v,this.pz=b,g==null||!r.isValid(g))throw new Error("x required");if(v==null||!r.isValid(v))throw new Error("y required");if(b==null||!r.isValid(b))throw new Error("z required");Object.freeze(this)}static fromAffine(g){const{x:v,y:b}=g||{};if(!g||!r.isValid(v)||!r.isValid(b))throw new Error("invalid affine point");if(g instanceof d)throw new Error("projective point not allowed");const E=_=>r.eql(_,r.ZERO);return E(v)&&E(b)?d.ZERO:new d(v,b,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(g){const v=r.invertBatch(g.map(b=>b.pz));return g.map((b,E)=>b.toAffine(v[E])).map(d.fromAffine)}static fromHex(g){const v=d.fromAffine(s(je("pointHex",g)));return v.assertValidity(),v}static fromPrivateKey(g){return d.BASE.multiply(c(g))}static msm(g,v){return wv(d,n,g,v)}_setWindowSize(g){p.setWindowSize(this,g)}assertValidity(){h(this)}hasEvenY(){const{y:g}=this.toAffine();if(r.isOdd)return!r.isOdd(g);throw new Error("Field doesn't support isOdd")}equals(g){u(g);const{px:v,py:b,pz:E}=this,{px:_,py:P,pz:$}=g,O=r.eql(r.mul(v,$),r.mul(_,E)),C=r.eql(r.mul(b,$),r.mul(P,E));return O&&C}negate(){return new d(this.px,r.neg(this.py),this.pz)}double(){const{a:g,b:v}=e,b=r.mul(v,us),{px:E,py:_,pz:P}=this;let $=r.ZERO,O=r.ZERO,C=r.ZERO,S=r.mul(E,E),k=r.mul(_,_),T=r.mul(P,P),R=r.mul(E,_);return R=r.add(R,R),C=r.mul(E,P),C=r.add(C,C),$=r.mul(g,C),O=r.mul(b,T),O=r.add($,O),$=r.sub(k,O),O=r.add(k,O),O=r.mul($,O),$=r.mul(R,$),C=r.mul(b,C),T=r.mul(g,T),R=r.sub(S,T),R=r.mul(g,R),R=r.add(R,C),C=r.add(S,S),S=r.add(C,S),S=r.add(S,T),S=r.mul(S,R),O=r.add(O,S),T=r.mul(_,P),T=r.add(T,T),S=r.mul(T,R),$=r.sub($,S),C=r.mul(T,k),C=r.add(C,C),C=r.add(C,C),new d($,O,C)}add(g){u(g);const{px:v,py:b,pz:E}=this,{px:_,py:P,pz:$}=g;let O=r.ZERO,C=r.ZERO,S=r.ZERO;const k=e.a,T=r.mul(e.b,us);let R=r.mul(v,_),M=r.mul(b,P),D=r.mul(E,$),m=r.add(v,b),w=r.add(_,P);m=r.mul(m,w),w=r.add(R,M),m=r.sub(m,w),w=r.add(v,E);let I=r.add(_,$);return w=r.mul(w,I),I=r.add(R,D),w=r.sub(w,I),I=r.add(b,E),O=r.add(P,$),I=r.mul(I,O),O=r.add(M,D),I=r.sub(I,O),S=r.mul(k,w),O=r.mul(T,D),S=r.add(O,S),O=r.sub(M,S),S=r.add(M,S),C=r.mul(O,S),M=r.add(R,R),M=r.add(M,R),D=r.mul(k,D),w=r.mul(T,w),M=r.add(M,D),D=r.sub(R,D),D=r.mul(k,D),w=r.add(w,D),R=r.mul(M,w),C=r.add(C,R),R=r.mul(I,w),O=r.mul(m,O),O=r.sub(O,R),R=r.mul(m,M),S=r.mul(I,S),S=r.add(S,R),new d(O,C,S)}subtract(g){return this.add(g.negate())}is0(){return this.equals(d.ZERO)}wNAF(g){return p.wNAFCached(this,g,d.normalizeZ)}multiplyUnsafe(g){const{endo:v,n:b}=e;or("scalar",g,yt,b);const E=d.ZERO;if(g===yt)return E;if(this.is0()||g===ne)return this;if(!v||p.hasPrecomputes(this))return p.wNAFCachedUnsafe(this,g,d.normalizeZ);let{k1neg:_,k1:P,k2neg:$,k2:O}=v.splitScalar(g),C=E,S=E,k=this;for(;P>yt||O>yt;)P&ne&&(C=C.add(k)),O&ne&&(S=S.add(k)),k=k.double(),P>>=ne,O>>=ne;return _&&(C=C.negate()),$&&(S=S.negate()),S=new d(r.mul(S.px,v.beta),S.py,S.pz),C.add(S)}multiply(g){const{endo:v,n:b}=e;or("scalar",g,ne,b);let E,_;if(v){const{k1neg:P,k1:$,k2neg:O,k2:C}=v.splitScalar(g);let{p:S,f:k}=this.wNAF($),{p:T,f:R}=this.wNAF(C);S=p.constTimeNegate(P,S),T=p.constTimeNegate(O,T),T=new d(r.mul(T.px,v.beta),T.py,T.pz),E=S.add(T),_=k.add(R)}else{const{p:P,f:$}=this.wNAF(g);E=P,_=$}return d.normalizeZ([E,_])[0]}multiplyAndAddUnsafe(g,v,b){const E=d.BASE,_=($,O)=>O===yt||O===ne||!$.equals(E)?$.multiplyUnsafe(O):$.multiply(O),P=_(this,v).add(_(g,b));return P.is0()?void 0:P}toAffine(g){return l(this,g)}isTorsionFree(){const{h:g,isTorsionFree:v}=e;if(g===ne)return!0;if(v)return v(d,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:g,clearCofactor:v}=e;return g===ne?this:v?v(d,this):this.multiplyUnsafe(e.h)}toRawBytes(g=!0){return sn("isCompressed",g),this.assertValidity(),i(d,this,g)}toHex(g=!0){return sn("isCompressed",g),on(this.toRawBytes(g))}}d.BASE=new d(e.Gx,e.Gy,r.ONE),d.ZERO=new d(r.ZERO,r.ONE,r.ZERO);const f=e.nBitLength,p=mv(d,e.endo?Math.ceil(f/2):f);return{CURVE:e,ProjectivePoint:d,normPrivateKeyToScalar:c,weierstrassEquation:o,isWithinCurveOrder:a}}function Sv(t){const e=rh(t);return ln(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...e})}function xv(t){const e=Sv(t),{Fp:r,n}=e,i=r.BYTES+1,s=2*r.BYTES+1;function o(D){return Se(D,n)}function a(D){return Yo(D,n)}const{ProjectivePoint:c,normPrivateKeyToScalar:u,weierstrassEquation:l,isWithinCurveOrder:h}=Iv({...e,toBytes(D,m,w){const I=m.toAffine(),A=r.toBytes(I.x),x=et;return sn("isCompressed",w),w?x(Uint8Array.from([m.hasEvenY()?2:3]),A):x(Uint8Array.from([4]),A,r.toBytes(I.y))},fromBytes(D){const m=D.length,w=D[0],I=D.subarray(1);if(m===i&&(w===2||w===3)){const A=$t(I);if(!un(A,ne,r.ORDER))throw new Error("Point is not on curve");const x=l(A);let N;try{N=r.sqrt(x)}catch(z){const q=z instanceof Error?": "+z.message:"";throw new Error("Point is not on curve"+q)}const F=(N&ne)===ne;return(w&1)===1!==F&&(N=r.neg(N)),{x:A,y:N}}else if(m===s&&w===4){const A=r.fromBytes(I.subarray(0,r.BYTES)),x=r.fromBytes(I.subarray(r.BYTES,2*r.BYTES));return{x:A,y:x}}else{const A=i,x=s;throw new Error("invalid Point, expected length of "+A+", or uncompressed "+x+", got "+m)}}}),d=D=>on(sr(D,e.nByteLength));function f(D){const m=n>>ne;return D>m}function p(D){return f(D)?o(-D):D}const y=(D,m,w)=>$t(D.slice(m,w));class g{constructor(m,w,I){this.r=m,this.s=w,this.recovery=I,this.assertValidity()}static fromCompact(m){const w=e.nByteLength;return m=je("compactSignature",m,w*2),new g(y(m,0,w),y(m,w,2*w))}static fromDER(m){const{r:w,s:I}=Ht.toSig(je("DER",m));return new g(w,I)}assertValidity(){or("r",this.r,ne,n),or("s",this.s,ne,n)}addRecoveryBit(m){return new g(this.r,this.s,m)}recoverPublicKey(m){const{r:w,s:I,recovery:A}=this,x=$(je("msgHash",m));if(A==null||![0,1,2,3].includes(A))throw new Error("recovery id invalid");const N=A===2||A===3?w+e.n:w;if(N>=r.ORDER)throw new Error("recovery id 2 or 3 invalid");const F=(A&1)===0?"02":"03",j=c.fromHex(F+d(N)),z=a(N),q=o(-x*z),H=o(I*z),V=c.BASE.multiplyAndAddUnsafe(j,q,H);if(!V)throw new Error("point at infinify");return V.assertValidity(),V}hasHighS(){return f(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return cn(this.toDERHex())}toDERHex(){return Ht.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return cn(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const v={isValidPrivateKey(D){try{return u(D),!0}catch{return!1}},normPrivateKeyToScalar:u,randomPrivateKey:()=>{const D=Xl(e.n);return pv(e.randomBytes(D),e.n)},precompute(D=8,m=c.BASE){return m._setWindowSize(D),m.multiply(BigInt(3)),m}};function b(D,m=!0){return c.fromPrivateKey(D).toRawBytes(m)}function E(D){const m=Cr(D),w=typeof D=="string",I=(m||w)&&D.length;return m?I===i||I===s:w?I===2*i||I===2*s:D instanceof c}function _(D,m,w=!0){if(E(D))throw new Error("first arg must be private key");if(!E(m))throw new Error("second arg must be public key");return c.fromHex(m).multiply(u(D)).toRawBytes(w)}const P=e.bits2int||function(D){if(D.length>8192)throw new Error("input is too large");const m=$t(D),w=D.length*8-e.nBitLength;return w>0?m>>BigInt(w):m},$=e.bits2int_modN||function(D){return o(P(D))},O=qo(e.nBitLength);function C(D){return or("num < 2^"+e.nBitLength,D,yt,O),sr(D,e.nByteLength)}function S(D,m,w=k){if(["recovered","canonical"].some(G=>G in w))throw new Error("sign() legacy options not supported");const{hash:I,randomBytes:A}=e;let{lowS:x,prehash:N,extraEntropy:F}=w;x==null&&(x=!0),D=je("msgHash",D),nh(w),N&&(D=je("prehashed msgHash",I(D)));const j=$(D),z=u(m),q=[C(z),C(j)];if(F!=null&&F!==!1){const G=F===!0?A(r.BYTES):F;q.push(je("extraEntropy",G))}const H=et(...q),V=j;function ee(G){const W=P(G);if(!h(W))return;const ge=a(W),le=c.BASE.multiply(W).toAffine(),he=o(le.x);if(he===yt)return;const Pe=o(ge*o(V+he*z));if(Pe===yt)return;let we=(le.x===he?0:2)|Number(le.y&ne),er=Pe;return x&&f(Pe)&&(er=p(Pe),we^=1),new g(he,er,we)}return{seed:H,k2sig:ee}}const k={lowS:e.lowS,prehash:!1},T={lowS:e.lowS,prehash:!1};function R(D,m,w=k){const{seed:I,k2sig:A}=S(D,m,w),x=e;return Bl(x.hash.outputLen,x.nByteLength,x.hmac)(I,A)}c.BASE._setWindowSize(8);function M(D,m,w,I=T){const A=D;m=je("msgHash",m),w=je("publicKey",w);const{lowS:x,prehash:N,format:F}=I;if(nh(I),"strict"in I)throw new Error("options.strict was renamed to lowS");if(F!==void 0&&F!=="compact"&&F!=="der")throw new Error("format must be compact or der");const j=typeof A=="string"||Cr(A),z=!j&&!F&&typeof A=="object"&&A!==null&&typeof A.r=="bigint"&&typeof A.s=="bigint";if(!j&&!z)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let q,H;try{if(z&&(q=new g(A.r,A.s)),j){try{F!=="compact"&&(q=g.fromDER(A))}catch(we){if(!(we instanceof Ht.Err))throw we}!q&&F!=="der"&&(q=g.fromCompact(A))}H=c.fromHex(w)}catch{return!1}if(!q||x&&q.hasHighS())return!1;N&&(m=e.hash(m));const{r:V,s:ee}=q,G=$(m),W=a(ee),ge=o(G*W),le=o(V*W),he=c.BASE.multiplyAndAddUnsafe(H,ge,le)?.toAffine();return he?o(he.x)===V:!1}return{CURVE:e,getPublicKey:b,getSharedSecret:_,sign:R,verify:M,ProjectivePoint:c,Signature:g,utils:v}}function Ov(t,e){const r=t.ORDER;let n=yt;for(let p=r-ne;p%ar===yt;p/=ar)n+=ne;const i=n,s=ar<<i-ne-ne,o=s*ar,a=(r-ne)/o,c=(a-ne)/ar,u=o-ne,l=s,h=t.pow(e,a),d=t.pow(e,(a+ne)/ar);let f=(p,y)=>{let g=h,v=t.pow(y,u),b=t.sqr(v);b=t.mul(b,y);let E=t.mul(p,b);E=t.pow(E,c),E=t.mul(E,v),v=t.mul(E,y),b=t.mul(E,p);let _=t.mul(b,v);E=t.pow(_,l);let P=t.eql(E,t.ONE);v=t.mul(b,d),E=t.mul(_,g),b=t.cmov(v,b,P),_=t.cmov(E,_,P);for(let $=i;$>ne;$--){let O=$-ar;O=ar<<O-ne;let C=t.pow(_,O);const S=t.eql(C,t.ONE);v=t.mul(b,g),g=t.mul(g,g),C=t.mul(_,g),b=t.cmov(v,b,S),_=t.cmov(C,_,S)}return{isValid:P,value:b}};if(t.ORDER%ih===us){const p=(t.ORDER-us)/ih,y=t.sqrt(t.neg(e));f=(g,v)=>{let b=t.sqr(v);const E=t.mul(g,v);b=t.mul(b,E);let _=t.pow(b,p);_=t.mul(_,E);const P=t.mul(_,y),$=t.mul(t.sqr(_),v),O=t.eql($,g);let C=t.cmov(P,_,O);return{isValid:O,value:C}}}return f}function Dv(t,e){if(Gl(t),!t.isValid(e.A)||!t.isValid(e.B)||!t.isValid(e.Z))throw new Error("mapToCurveSimpleSWU: invalid opts");const r=Ov(t,e.Z);if(!t.isOdd)throw new Error("Fp.isOdd is not implemented!");return n=>{let i,s,o,a,c,u,l,h;i=t.sqr(n),i=t.mul(i,e.Z),s=t.sqr(i),s=t.add(s,i),o=t.add(s,t.ONE),o=t.mul(o,e.B),a=t.cmov(e.Z,t.neg(s),!t.eql(s,t.ZERO)),a=t.mul(a,e.A),s=t.sqr(o),u=t.sqr(a),c=t.mul(u,e.A),s=t.add(s,c),s=t.mul(s,o),u=t.mul(u,a),c=t.mul(u,e.B),s=t.add(s,c),l=t.mul(i,o);const{isValid:d,value:f}=r(s,u);h=t.mul(i,n),h=t.mul(h,f),l=t.cmov(l,o,d),h=t.cmov(h,f,d);const p=t.isOdd(n)===t.isOdd(h);return h=t.cmov(t.neg(h),h,p),l=t.div(l,a),{x:l,y:h}}}function $v(t){return{hash:t,hmac:(e,...r)=>Vl(t,e,J0(...r)),randomBytes:El}}function Av(t,e){const r=n=>xv({...t,...$v(n)});return{...r(e),create:r}}const Pv=$t;function cr(t,e){if(ti(t),ti(e),t<0||t>=1<<8*e)throw new Error("invalid I2OSP input: "+t);const r=Array.from({length:e}).fill(0);for(let n=e-1;n>=0;n--)r[n]=t&255,t>>>=8;return new Uint8Array(r)}function Cv(t,e){const r=new Uint8Array(t.length);for(let n=0;n<t.length;n++)r[n]=t[n]^e[n];return r}function ti(t){if(!Number.isSafeInteger(t))throw new Error("number expected")}function Nv(t,e,r,n){Dt(t),Dt(e),ti(r),e.length>255&&(e=n(et(os("H2C-OVERSIZE-DST-"),e)));const{outputLen:i,blockLen:s}=n,o=Math.ceil(r/i);if(r>65535||o>255)throw new Error("expand_message_xmd: invalid lenInBytes");const a=et(e,cr(e.length,1)),c=cr(0,s),u=cr(r,2),l=new Array(o),h=n(et(c,t,u,cr(0,1),a));l[0]=n(et(h,cr(1,1),a));for(let f=1;f<=o;f++){const p=[Cv(h,l[f-1]),cr(f+1,1),a];l[f]=n(et(...p))}return et(...l).slice(0,r)}function Rv(t,e,r,n,i){if(Dt(t),Dt(e),ti(r),e.length>255){const s=Math.ceil(2*n/8);e=i.create({dkLen:s}).update(os("H2C-OVERSIZE-DST-")).update(e).digest()}if(r>65535||e.length>255)throw new Error("expand_message_xof: invalid lenInBytes");return i.create({dkLen:r}).update(t).update(cr(r,2)).update(e).update(cr(e.length,1)).digest()}function sh(t,e,r){ln(r,{DST:"stringOrUint8Array",p:"bigint",m:"isSafeInteger",k:"isSafeInteger",hash:"hash"});const{p:n,k:i,m:s,hash:o,expand:a,DST:c}=r;Dt(t),ti(e);const u=typeof c=="string"?os(c):c,l=n.toString(2).length,h=Math.ceil((l+i)/8),d=e*s*h;let f;if(a==="xmd")f=Nv(t,u,d,o);else if(a==="xof")f=Rv(t,u,d,i,o);else if(a==="_internal_pass")f=t;else throw new Error('expand must be "xmd" or "xof"');const p=new Array(e);for(let y=0;y<e;y++){const g=new Array(s);for(let v=0;v<s;v++){const b=h*(v+y*s),E=f.subarray(b,b+h);g[v]=Se(Pv(E),n)}p[y]=g}return p}function Tv(t,e){const r=e.map(n=>Array.from(n).reverse());return(n,i)=>{const[s,o,a,c]=r.map(u=>u.reduce((l,h)=>t.add(t.mul(l,n),h)));return n=t.div(s,o),i=t.mul(i,t.div(a,c)),{x:n,y:i}}}function Bv(t,e,r){if(typeof e!="function")throw new Error("mapToCurve() must be defined");return{hashToCurve(n,i){const s=sh(n,2,{...r,DST:r.DST,...i}),o=t.fromAffine(e(s[0])),a=t.fromAffine(e(s[1])),c=o.add(a).clearCofactor();return c.assertValidity(),c},encodeToCurve(n,i){const s=sh(n,1,{...r,DST:r.encodeDST,...i}),o=t.fromAffine(e(s[0])).clearCofactor();return o.assertValidity(),o},mapToCurve(n){if(!Array.isArray(n))throw new Error("mapToCurve: expected array of bigints");for(const s of n)if(typeof s!="bigint")throw new Error("mapToCurve: expected array of bigints");const i=t.fromAffine(e(n)).clearCofactor();return i.assertValidity(),i}}}const ri=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),ls=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),ni=BigInt(1),hs=BigInt(2),oh=(t,e)=>(t+e/hs)/e;function ah(t){const e=ri,r=BigInt(3),n=BigInt(6),i=BigInt(11),s=BigInt(22),o=BigInt(23),a=BigInt(44),c=BigInt(88),u=t*t*t%e,l=u*u*t%e,h=at(l,r,e)*l%e,d=at(h,r,e)*l%e,f=at(d,hs,e)*u%e,p=at(f,i,e)*f%e,y=at(p,s,e)*p%e,g=at(y,a,e)*y%e,v=at(g,c,e)*g%e,b=at(v,a,e)*y%e,E=at(b,r,e)*l%e,_=at(E,o,e)*p%e,P=at(_,n,e)*u%e,$=at(P,hs,e);if(!ur.eql(ur.sqr($),t))throw new Error("Cannot find square root");return $}const ur=Zl(ri,void 0,void 0,{sqrt:ah}),ii=Av({a:BigInt(0),b:BigInt(7),Fp:ur,n:ls,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:t=>{const e=ls,r=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),n=-ni*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),i=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),s=r,o=BigInt("0x100000000000000000000000000000000"),a=oh(s*t,e),c=oh(-n*t,e);let u=Se(t-a*r-c*i,e),l=Se(-a*n-c*s,e);const h=u>o,d=l>o;if(h&&(u=e-u),d&&(l=e-l),u>o||l>o)throw new Error("splitScalar: Endomorphism failed, k="+t);return{k1neg:h,k1:u,k2neg:d,k2:l}}}},ns),ch=BigInt(0),uh={};function ds(t,...e){let r=uh[t];if(r===void 0){const n=ns(Uint8Array.from(t,i=>i.charCodeAt(0)));r=et(n,n),uh[t]=r}return ns(et(r,...e))}const ea=t=>t.toRawBytes(!0).slice(1),ta=t=>sr(t,32),ra=t=>Se(t,ri),si=t=>Se(t,ls),na=ii.ProjectivePoint,Fv=(t,e,r)=>na.BASE.multiplyAndAddUnsafe(t,e,r);function ia(t){let e=ii.utils.normPrivateKeyToScalar(t),r=na.fromPrivateKey(e);return{scalar:r.hasEvenY()?e:si(-e),bytes:ea(r)}}function lh(t){or("x",t,ni,ri);const e=ra(t*t),r=ra(e*t+BigInt(7));let n=ah(r);n%hs!==ch&&(n=ra(-n));const i=new na(t,n,ni);return i.assertValidity(),i}const hn=$t;function hh(...t){return si(hn(ds("BIP0340/challenge",...t)))}function Lv(t){return ia(t).bytes}function Uv(t,e,r=El(32)){const n=je("message",t),{bytes:i,scalar:s}=ia(e),o=je("auxRand",r,32),a=ta(s^hn(ds("BIP0340/aux",o))),c=ds("BIP0340/nonce",a,i,n),u=si(hn(c));if(u===ch)throw new Error("sign failed: k is zero");const{bytes:l,scalar:h}=ia(u),d=hh(l,i,n),f=new Uint8Array(64);if(f.set(l,0),f.set(ta(si(h+d*s)),32),!dh(f,n,i))throw new Error("sign: Invalid signature produced");return f}function dh(t,e,r){const n=je("signature",t,64),i=je("message",e),s=je("publicKey",r,32);try{const o=lh(hn(s)),a=hn(n.subarray(0,32));if(!un(a,ni,ri))return!1;const c=hn(n.subarray(32,64));if(!un(c,ni,ls))return!1;const u=hh(ta(a),ea(o),i),l=Fv(o,c,si(-u));return!(!l||!l.hasEvenY()||l.toAffine().x!==a)}catch{return!1}}const jv={getPublicKey:Lv,sign:Uv,verify:dh,utils:{randomPrivateKey:ii.utils.randomPrivateKey,lift_x:lh,pointToBytes:ea,numberToBytesBE:sr,bytesToNumberBE:$t,taggedHash:ds,mod:Se}},qv=Tv(ur,[["0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7","0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581","0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262","0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c"],["0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b","0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14","0x0000000000000000000000000000000000000000000000000000000000000001"],["0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c","0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3","0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931","0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84"],["0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b","0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573","0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f","0x0000000000000000000000000000000000000000000000000000000000000001"]].map(t=>t.map(e=>BigInt(e)))),kv=Dv(ur,{A:BigInt("0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533"),B:BigInt("1771"),Z:ur.create(BigInt("-11"))}),fh=Bv(ii.ProjectivePoint,t=>{const{x:e,y:r}=kv(ur.create(t[0]));return qv(e,r)},{DST:"secp256k1_XMD:SHA-256_SSWU_RO_",encodeDST:"secp256k1_XMD:SHA-256_SSWU_NU_",p:ur.ORDER,m:1,k:128,expand:"xmd",hash:ns});var Mv=Object.freeze({__proto__:null,secp256k1:ii,schnorr:jv,hashToCurve:fh.hashToCurve,encodeToCurve:fh.encodeToCurve});function zv(t){if(t.length>=255)throw new TypeError("Alphabet too long");const e=new Uint8Array(256);for(let u=0;u<e.length;u++)e[u]=255;for(let u=0;u<t.length;u++){const l=t.charAt(u),h=l.charCodeAt(0);if(e[h]!==255)throw new TypeError(l+" is ambiguous");e[h]=u}const r=t.length,n=t.charAt(0),i=Math.log(r)/Math.log(256),s=Math.log(256)/Math.log(r);function o(u){if(u instanceof Uint8Array||(ArrayBuffer.isView(u)?u=new Uint8Array(u.buffer,u.byteOffset,u.byteLength):Array.isArray(u)&&(u=Uint8Array.from(u))),!(u instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(u.length===0)return"";let l=0,h=0,d=0;const f=u.length;for(;d!==f&&u[d]===0;)d++,l++;const p=(f-d)*s+1>>>0,y=new Uint8Array(p);for(;d!==f;){let b=u[d],E=0;for(let _=p-1;(b!==0||E<h)&&_!==-1;_--,E++)b+=256*y[_]>>>0,y[_]=b%r>>>0,b=b/r>>>0;if(b!==0)throw new Error("Non-zero carry");h=E,d++}let g=p-h;for(;g!==p&&y[g]===0;)g++;let v=n.repeat(l);for(;g<p;++g)v+=t.charAt(y[g]);return v}function a(u){if(typeof u!="string")throw new TypeError("Expected String");if(u.length===0)return new Uint8Array;let l=0,h=0,d=0;for(;u[l]===n;)h++,l++;const f=(u.length-l)*i+1>>>0,p=new Uint8Array(f);for(;l<u.length;){const b=u.charCodeAt(l);if(b>255)return;let E=e[b];if(E===255)return;let _=0;for(let P=f-1;(E!==0||_<d)&&P!==-1;P--,_++)E+=r*p[P]>>>0,p[P]=E%256>>>0,E=E/256>>>0;if(E!==0)throw new Error("Non-zero carry");d=_,l++}let y=f-d;for(;y!==f&&p[y]===0;)y++;const g=new Uint8Array(h+(f-y));let v=h;for(;y!==f;)g[v++]=p[y++];return g}function c(u){const l=a(u);if(l)return l;throw new Error("Non-base"+r+" character")}return{encode:o,decodeUnsafe:a,decode:c}}var Hv="**********************************************************",Vv=zv(Hv);function ph(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}function oi(t,e){e||(e=t.reduce((i,s)=>i+s.length,0));const r=ph(e);let n=0;for(const i of t)r.set(i,n),n+=i.length;return r}function Kv(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var s=t.charAt(i),o=s.charCodeAt(0);if(r[o]!==255)throw new TypeError(s+" is ambiguous");r[o]=i}var a=t.length,c=t.charAt(0),u=Math.log(a)/Math.log(256),l=Math.log(256)/Math.log(a);function h(p){if(p instanceof Uint8Array||(ArrayBuffer.isView(p)?p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength):Array.isArray(p)&&(p=Uint8Array.from(p))),!(p instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(p.length===0)return"";for(var y=0,g=0,v=0,b=p.length;v!==b&&p[v]===0;)v++,y++;for(var E=(b-v)*l+1>>>0,_=new Uint8Array(E);v!==b;){for(var P=p[v],$=0,O=E-1;(P!==0||$<g)&&O!==-1;O--,$++)P+=256*_[O]>>>0,_[O]=P%a>>>0,P=P/a>>>0;if(P!==0)throw new Error("Non-zero carry");g=$,v++}for(var C=E-g;C!==E&&_[C]===0;)C++;for(var S=c.repeat(y);C<E;++C)S+=t.charAt(_[C]);return S}function d(p){if(typeof p!="string")throw new TypeError("Expected String");if(p.length===0)return new Uint8Array;var y=0;if(p[y]!==" "){for(var g=0,v=0;p[y]===c;)g++,y++;for(var b=(p.length-y)*u+1>>>0,E=new Uint8Array(b);p[y];){var _=r[p.charCodeAt(y)];if(_===255)return;for(var P=0,$=b-1;(_!==0||P<v)&&$!==-1;$--,P++)_+=a*E[$]>>>0,E[$]=_%256>>>0,_=_/256>>>0;if(_!==0)throw new Error("Non-zero carry");v=P,y++}if(p[y]!==" "){for(var O=b-v;O!==b&&E[O]===0;)O++;for(var C=new Uint8Array(g+(b-O)),S=g;O!==b;)C[S++]=E[O++];return C}}}function f(p){var y=d(p);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:h,decodeUnsafe:d,decode:f}}var Wv=Kv,Gv=Wv;const gh=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},Yv=t=>new TextEncoder().encode(t),Zv=t=>new TextDecoder().decode(t);class Jv{constructor(e,r,n){this.name=e,this.prefix=r,this.baseEncode=n}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class Xv{constructor(e,r,n){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=n}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return yh(this,e)}}class Qv{constructor(e){this.decoders=e}or(e){return yh(this,e)}decode(e){const r=e[0],n=this.decoders[r];if(n)return n.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const yh=(t,e)=>new Qv({...t.decoders||{[t.prefix]:t},...e.decoders||{[e.prefix]:e}});class eE{constructor(e,r,n,i){this.name=e,this.prefix=r,this.baseEncode=n,this.baseDecode=i,this.encoder=new Jv(e,r,n),this.decoder=new Xv(e,r,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const fs=({name:t,prefix:e,encode:r,decode:n})=>new eE(t,e,r,n),ai=({prefix:t,name:e,alphabet:r})=>{const{encode:n,decode:i}=Gv(r,e);return fs({prefix:t,name:e,encode:n,decode:s=>gh(i(s))})},tE=(t,e,r,n)=>{const i={};for(let l=0;l<e.length;++l)i[e[l]]=l;let s=t.length;for(;t[s-1]==="=";)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,u=0;for(let l=0;l<s;++l){const h=i[t[l]];if(h===void 0)throw new SyntaxError(`Non-${n} character`);c=c<<r|h,a+=r,a>=8&&(a-=8,o[u++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},rE=(t,e,r)=>{const n=e[e.length-1]==="=",i=(1<<r)-1;let s="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,s+=e[i&a>>o];if(o&&(s+=e[i&a<<r-o]),n)for(;s.length*r&7;)s+="=";return s},Ne=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>fs({prefix:e,name:t,encode(i){return rE(i,n,r)},decode(i){return tE(i,n,r,t)}}),nE=fs({prefix:"\0",name:"identity",encode:t=>Zv(t),decode:t=>Yv(t)});var iE=Object.freeze({__proto__:null,identity:nE});const sE=Ne({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var oE=Object.freeze({__proto__:null,base2:sE});const aE=Ne({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var cE=Object.freeze({__proto__:null,base8:aE});const uE=ai({prefix:"9",name:"base10",alphabet:"0123456789"});var lE=Object.freeze({__proto__:null,base10:uE});const hE=Ne({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),dE=Ne({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var fE=Object.freeze({__proto__:null,base16:hE,base16upper:dE});const pE=Ne({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),gE=Ne({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),yE=Ne({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),mE=Ne({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),wE=Ne({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),bE=Ne({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),vE=Ne({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),EE=Ne({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),_E=Ne({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var IE=Object.freeze({__proto__:null,base32:pE,base32upper:gE,base32pad:yE,base32padupper:mE,base32hex:wE,base32hexupper:bE,base32hexpad:vE,base32hexpadupper:EE,base32z:_E});const SE=ai({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),xE=ai({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var OE=Object.freeze({__proto__:null,base36:SE,base36upper:xE});const DE=ai({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),$E=ai({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var AE=Object.freeze({__proto__:null,base58btc:DE,base58flickr:$E});const PE=Ne({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),CE=Ne({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),NE=Ne({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),RE=Ne({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var TE=Object.freeze({__proto__:null,base64:PE,base64pad:CE,base64url:NE,base64urlpad:RE});const mh=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),BE=mh.reduce((t,e,r)=>(t[r]=e,t),[]),FE=mh.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function LE(t){return t.reduce((e,r)=>(e+=BE[r],e),"")}function UE(t){const e=[];for(const r of t){const n=FE[r.codePointAt(0)];if(n===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(n)}return new Uint8Array(e)}const jE=fs({prefix:"\u{1F680}",name:"base256emoji",encode:LE,decode:UE});var qE=Object.freeze({__proto__:null,base256emoji:jE}),kE=bh,wh=128,ME=127,zE=~ME,HE=Math.pow(2,31);function bh(t,e,r){e=e||[],r=r||0;for(var n=r;t>=HE;)e[r++]=t&255|wh,t/=128;for(;t&zE;)e[r++]=t&255|wh,t>>>=7;return e[r]=t|0,bh.bytes=r-n+1,e}var VE=sa,KE=128,vh=127;function sa(t,n){var r=0,n=n||0,i=0,s=n,o,a=t.length;do{if(s>=a)throw sa.bytes=0,new RangeError("Could not decode varint");o=t[s++],r+=i<28?(o&vh)<<i:(o&vh)*Math.pow(2,i),i+=7}while(o>=KE);return sa.bytes=s-n,r}var WE=Math.pow(2,7),GE=Math.pow(2,14),YE=Math.pow(2,21),ZE=Math.pow(2,28),JE=Math.pow(2,35),XE=Math.pow(2,42),QE=Math.pow(2,49),e2=Math.pow(2,56),t2=Math.pow(2,63),r2=function(t){return t<WE?1:t<GE?2:t<YE?3:t<ZE?4:t<JE?5:t<XE?6:t<QE?7:t<e2?8:t<t2?9:10},n2={encode:kE,decode:VE,encodingLength:r2},Eh=n2;const _h=(t,e,r=0)=>(Eh.encode(t,e,r),e),Ih=t=>Eh.encodingLength(t),oa=(t,e)=>{const r=e.byteLength,n=Ih(t),i=n+Ih(r),s=new Uint8Array(i+r);return _h(t,s,0),_h(r,s,n),s.set(e,i),new i2(t,r,e,s)};class i2{constructor(e,r,n,i){this.code=e,this.size=r,this.digest=n,this.bytes=i}}const Sh=({name:t,code:e,encode:r})=>new s2(t,e,r);class s2{constructor(e,r,n){this.name=e,this.code=r,this.encode=n}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?oa(this.code,r):r.then(n=>oa(this.code,n))}else throw Error("Unknown type, must be binary type")}}const xh=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),o2=Sh({name:"sha2-256",code:18,encode:xh("SHA-256")}),a2=Sh({name:"sha2-512",code:19,encode:xh("SHA-512")});var c2=Object.freeze({__proto__:null,sha256:o2,sha512:a2});const Oh=0,u2="identity",Dh=gh;var l2=Object.freeze({__proto__:null,identity:{code:Oh,name:u2,encode:Dh,digest:t=>oa(Oh,Dh(t))}});new TextEncoder,new TextDecoder;const $h={...iE,...oE,...cE,...lE,...fE,...IE,...OE,...AE,...TE,...qE};({...c2,...l2});function Ah(t,e,r,n){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:n}}}const Ph=Ah("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),aa=Ah("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=ph(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),Ch={utf8:Ph,"utf-8":Ph,hex:$h.base16,latin1:aa,ascii:aa,binary:aa,...$h};function ct(t,e="utf8"){const r=Ch[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t,"utf8"):r.decoder.decode(`${r.prefix}${t}`)}function Ze(t,e="utf8"){const r=Ch[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString("utf8"):r.encoder.encode(t).substring(1)}const h2={waku:{publish:"waku_publish",batchPublish:"waku_batchPublish",subscribe:"waku_subscribe",batchSubscribe:"waku_batchSubscribe",subscription:"waku_subscription",unsubscribe:"waku_unsubscribe",batchUnsubscribe:"waku_batchUnsubscribe",batchFetchMessages:"waku_batchFetchMessages"},irn:{publish:"irn_publish",batchPublish:"irn_batchPublish",subscribe:"irn_subscribe",batchSubscribe:"irn_batchSubscribe",subscription:"irn_subscription",unsubscribe:"irn_unsubscribe",batchUnsubscribe:"irn_batchUnsubscribe",batchFetchMessages:"irn_batchFetchMessages"},iridium:{publish:"iridium_publish",batchPublish:"iridium_batchPublish",subscribe:"iridium_subscribe",batchSubscribe:"iridium_batchSubscribe",subscription:"iridium_subscription",unsubscribe:"iridium_unsubscribe",batchUnsubscribe:"iridium_batchUnsubscribe",batchFetchMessages:"iridium_batchFetchMessages"}};var d2=Object.defineProperty,f2=Object.defineProperties,p2=Object.getOwnPropertyDescriptors,Nh=Object.getOwnPropertySymbols,g2=Object.prototype.hasOwnProperty,y2=Object.prototype.propertyIsEnumerable,Rh=(t,e,r)=>e in t?d2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Vt=(t,e)=>{for(var r in e||(e={}))g2.call(e,r)&&Rh(t,r,e[r]);if(Nh)for(var r of Nh(e))y2.call(e,r)&&Rh(t,r,e[r]);return t},ca=(t,e)=>f2(t,p2(e));const m2=":";function dn(t){const[e,r]=t.split(m2);return{namespace:e,reference:r}}function Th(t,e){return t.includes(":")?[t]:e.chains||[]}var w2=Object.defineProperty,b2=Object.defineProperties,v2=Object.getOwnPropertyDescriptors,Bh=Object.getOwnPropertySymbols,E2=Object.prototype.hasOwnProperty,_2=Object.prototype.propertyIsEnumerable,Fh=(t,e,r)=>e in t?w2(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Lh=(t,e)=>{for(var r in e||(e={}))E2.call(e,r)&&Fh(t,r,e[r]);if(Bh)for(var r of Bh(e))_2.call(e,r)&&Fh(t,r,e[r]);return t},I2=(t,e)=>b2(t,v2(e));const S2="ReactNative",tt={reactNative:"react-native",node:"node",browser:"browser",unknown:"unknown"},x2="js";function ps(){return typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.node<"u"}function lr(){return!Ar()&&!!Do()&&navigator.product===S2}function O2(){return lr()&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"&&(global==null?void 0:global.Platform.OS)==="android"}function D2(){return lr()&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"&&(global==null?void 0:global.Platform.OS)==="ios"}function fn(){return!ps()&&!!Do()&&!!Ar()}function ci(){return lr()?tt.reactNative:ps()?tt.node:fn()?tt.browser:tt.unknown}function Uh(){var t;try{return lr()&&typeof global<"u"&&typeof(global==null?void 0:global.Application)<"u"?(t=global.Application)==null?void 0:t.applicationId:void 0}catch{return}}function $2(t,e){const r=new URLSearchParams(t);for(const n of Object.keys(e).sort())if(e.hasOwnProperty(n)){const i=e[n];i!==void 0&&r.set(n,i)}return r.toString()}function A2(t){var e,r;const n=jh();try{return t!=null&&t.url&&n.url&&new URL(t.url).host!==new URL(n.url).host&&(console.warn(`The configured WalletConnect 'metadata.url':${t.url} differs from the actual page url:${n.url}. This is probably unintended and can lead to issues.`),t.url=n.url),(e=t?.icons)!=null&&e.length&&t.icons.length>0&&(t.icons=t.icons.filter(i=>i!=="")),I2(Lh(Lh({},n),t),{url:t?.url||n.url,name:t?.name||n.name,description:t?.description||n.description,icons:(r=t?.icons)!=null&&r.length&&t.icons.length>0?t.icons:n.icons})}catch(i){return console.warn("Error populating app metadata",i),t||n}}function jh(){return sl()||{name:"",description:"",url:"",icons:[""]}}function P2(){if(ci()===tt.reactNative&&typeof global<"u"&&typeof(global==null?void 0:global.Platform)<"u"){const{OS:r,Version:n}=global.Platform;return[r,n].join("-")}const t=h0();if(t===null)return"unknown";const e=t.os?t.os.replace(" ","").toLowerCase():"unknown";return t.type==="browser"?[e,t.name,t.version].join("-"):[e,t.version].join("-")}function C2(){var t;const e=ci();return e===tt.browser?[e,((t=il())==null?void 0:t.host)||"unknown"].join(":"):e}function qh(t,e,r){const n=P2(),i=C2();return[[t,e].join("-"),[x2,r].join("-"),n,i].join("/")}function N2({protocol:t,version:e,relayUrl:r,sdkVersion:n,auth:i,projectId:s,useOnCloseEvent:o,bundleId:a,packageName:c}){const u=r.split("?"),l=qh(t,e,n),h={auth:i,ua:l,projectId:s,useOnCloseEvent:o||void 0,packageName:c||void 0,bundleId:a||void 0},d=$2(u[1]||"",h);return u[0]+"?"+d}function Rr(t,e){return t.filter(r=>e.includes(r)).length===t.length}function ua(t){return Object.fromEntries(t.entries())}function la(t){return new Map(Object.entries(t))}function Tr(t=L.FIVE_MINUTES,e){const r=L.toMiliseconds(t||L.FIVE_MINUTES);let n,i,s,o;return{resolve:a=>{s&&n&&(clearTimeout(s),n(a),o=Promise.resolve(a))},reject:a=>{s&&i&&(clearTimeout(s),i(a))},done:()=>new Promise((a,c)=>{if(o)return a(o);s=setTimeout(()=>{const u=new Error(e);o=Promise.reject(u),c(u)},r),n=a,i=c})}}function hr(t,e,r){return new Promise(async(n,i)=>{const s=setTimeout(()=>i(new Error(r)),e);try{const o=await t;n(o)}catch(o){i(o)}clearTimeout(s)})}function kh(t,e){if(typeof e=="string"&&e.startsWith(`${t}:`))return e;if(t.toLowerCase()==="topic"){if(typeof e!="string")throw new Error('Value must be "string" for expirer target type: topic');return`topic:${e}`}else if(t.toLowerCase()==="id"){if(typeof e!="number")throw new Error('Value must be "number" for expirer target type: id');return`id:${e}`}throw new Error(`Unknown expirer target type: ${t}`)}function R2(t){return kh("topic",t)}function T2(t){return kh("id",t)}function Mh(t){const[e,r]=t.split(":"),n={id:void 0,topic:void 0};if(e==="topic"&&typeof r=="string")n.topic=r;else if(e==="id"&&Number.isInteger(Number(r)))n.id=Number(r);else throw new Error(`Invalid target, expected id:number or topic:string, got ${e}:${r}`);return n}function ve(t,e){return L.fromMiliseconds((e||Date.now())+L.toMiliseconds(t))}function dr(t){return Date.now()>=L.toMiliseconds(t)}function te(t,e){return`${t}${e?`:${e}`:""}`}function At(t=[],e=[]){return[...new Set([...t,...e])]}async function B2({id:t,topic:e,wcDeepLink:r}){var n;try{if(!r)return;const i=typeof r=="string"?JSON.parse(r):r,s=i?.href;if(typeof s!="string")return;const o=F2(s,t,e),a=ci();if(a===tt.browser){if(!((n=Ar())!=null&&n.hasFocus())){console.warn("Document does not have focus, skipping deeplink.");return}L2(o)}else a===tt.reactNative&&typeof(global==null?void 0:global.Linking)<"u"&&await global.Linking.openURL(o)}catch(i){console.error(i)}}function F2(t,e,r){const n=`requestId=${e}&sessionTopic=${r}`;t.endsWith("/")&&(t=t.slice(0,-1));let i=`${t}`;if(t.startsWith("https://t.me")){const s=t.includes("?")?"&startapp=":"?startapp=";i=`${i}${s}${k2(n,!0)}`}else i=`${i}/wc?${n}`;return i}function L2(t){let e="_self";q2()?e="_top":(j2()||t.startsWith("https://")||t.startsWith("http://"))&&(e="_blank"),window.open(t,e,"noreferrer noopener")}async function U2(t,e){let r="";try{if(fn()&&(r=localStorage.getItem(e),r))return r;r=await t.getItem(e)}catch(n){console.error(n)}return r}function zh(t,e){if(!t.includes(e))return null;const r=t.split(/([&,?,=])/),n=r.indexOf(e);return r[n+2]}function Hh(){return typeof crypto<"u"&&crypto!=null&&crypto.randomUUID?crypto.randomUUID():"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/gu,t=>{const e=Math.random()*16|0;return(t==="x"?e:e&3|8).toString(16)})}function ha(){return typeof process<"u"&&process.env.IS_VITEST==="true"}function j2(){return typeof window<"u"&&(!!window.TelegramWebviewProxy||!!window.Telegram||!!window.TelegramWebviewProxyProto)}function q2(){try{return window.self!==window.top}catch{return!1}}function k2(t,e=!1){const r=Buffer.from(t).toString("base64");return e?r.replace(/[=]/g,""):r}function Vh(t){return Buffer.from(t,"base64").toString("utf-8")}function M2(t){return new Promise(e=>setTimeout(e,t))}function ui(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function z2(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function li(t,...e){if(!z2(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function da(t){if(typeof t!="function"||typeof t.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");ui(t.outputLen),ui(t.blockLen)}function pn(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function Kh(t,e){li(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}const gs=BigInt(2**32-1),Wh=BigInt(32);function H2(t,e=!1){return e?{h:Number(t&gs),l:Number(t>>Wh&gs)}:{h:Number(t>>Wh&gs)|0,l:Number(t&gs)|0}}function V2(t,e=!1){let r=new Uint32Array(t.length),n=new Uint32Array(t.length);for(let i=0;i<t.length;i++){const{h:s,l:o}=H2(t[i],e);[r[i],n[i]]=[s,o]}return[r,n]}const K2=(t,e,r)=>t<<r|e>>>32-r,W2=(t,e,r)=>e<<r|t>>>32-r,G2=(t,e,r)=>e<<r-32|t>>>64-r,Y2=(t,e,r)=>t<<r-32|e>>>64-r,gn=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;function Z2(t){return new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4))}function fa(t){return new DataView(t.buffer,t.byteOffset,t.byteLength)}function Pt(t,e){return t<<32-e|t>>>e}const Gh=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function J2(t){return t<<24&4278190080|t<<8&16711680|t>>>8&65280|t>>>24&255}function Yh(t){for(let e=0;e<t.length;e++)t[e]=J2(t[e])}function X2(t){if(typeof t!="string")throw new Error("utf8ToBytes expected string, got "+typeof t);return new Uint8Array(new TextEncoder().encode(t))}function yn(t){return typeof t=="string"&&(t=X2(t)),li(t),t}function Q2(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];li(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}class pa{clone(){return this._cloneInto()}}function Zh(t){const e=n=>t().update(yn(n)).digest(),r=t();return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=()=>t(),e}function mn(t=32){if(gn&&typeof gn.getRandomValues=="function")return gn.getRandomValues(new Uint8Array(t));if(gn&&typeof gn.randomBytes=="function")return gn.randomBytes(t);throw new Error("crypto.getRandomValues must be defined")}const Jh=[],Xh=[],Qh=[],e_=BigInt(0),hi=BigInt(1),t_=BigInt(2),r_=BigInt(7),n_=BigInt(256),i_=BigInt(113);for(let t=0,e=hi,r=1,n=0;t<24;t++){[r,n]=[n,(2*r+3*n)%5],Jh.push(2*(5*n+r)),Xh.push((t+1)*(t+2)/2%64);let i=e_;for(let s=0;s<7;s++)e=(e<<hi^(e>>r_)*i_)%n_,e&t_&&(i^=hi<<(hi<<BigInt(s))-hi);Qh.push(i)}const[s_,o_]=V2(Qh,!0),ed=(t,e,r)=>r>32?G2(t,e,r):K2(t,e,r),td=(t,e,r)=>r>32?Y2(t,e,r):W2(t,e,r);function a_(t,e=24){const r=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let o=0;o<10;o++)r[o]=t[o]^t[o+10]^t[o+20]^t[o+30]^t[o+40];for(let o=0;o<10;o+=2){const a=(o+8)%10,c=(o+2)%10,u=r[c],l=r[c+1],h=ed(u,l,1)^r[a],d=td(u,l,1)^r[a+1];for(let f=0;f<50;f+=10)t[o+f]^=h,t[o+f+1]^=d}let i=t[2],s=t[3];for(let o=0;o<24;o++){const a=Xh[o],c=ed(i,s,a),u=td(i,s,a),l=Jh[o];i=t[l],s=t[l+1],t[l]=c,t[l+1]=u}for(let o=0;o<50;o+=10){for(let a=0;a<10;a++)r[a]=t[o+a];for(let a=0;a<10;a++)t[o+a]^=~r[(a+2)%10]&r[(a+4)%10]}t[0]^=s_[n],t[1]^=o_[n]}r.fill(0)}class ga extends pa{constructor(e,r,n,i=!1,s=24){if(super(),this.blockLen=e,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=s,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,ui(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=Z2(this.state)}keccak(){Gh||Yh(this.state32),a_(this.state32,this.rounds),Gh||Yh(this.state32),this.posOut=0,this.pos=0}update(e){pn(this);const{blockLen:r,state:n}=this;e=yn(e);const i=e.length;for(let s=0;s<i;){const o=Math.min(r-this.pos,i-s);for(let a=0;a<o;a++)n[this.pos++]^=e[s++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:e,suffix:r,pos:n,blockLen:i}=this;e[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),e[i-1]^=128,this.keccak()}writeInto(e){pn(this,!1),li(e),this.finish();const r=this.state,{blockLen:n}=this;for(let i=0,s=e.length;i<s;){this.posOut>=n&&this.keccak();const o=Math.min(n-this.posOut,s-i);e.set(r.subarray(this.posOut,this.posOut+o),i),this.posOut+=o,i+=o}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return ui(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(Kh(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){const{blockLen:r,suffix:n,outputLen:i,rounds:s,enableXOF:o}=this;return e||(e=new ga(r,n,i,o,s)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=s,e.suffix=n,e.outputLen=i,e.enableXOF=o,e.destroyed=this.destroyed,e}}const c_=(t,e,r)=>Zh(()=>new ga(e,t,r)),u_=c_(1,136,256/8),l_="https://rpc.walletconnect.org/v1";function rd(t){const e=`Ethereum Signed Message:
${t.length}`,r=new TextEncoder().encode(e+t);return"0x"+Buffer.from(u_(r)).toString("hex")}async function h_(t,e,r,n,i,s){switch(r.t){case"eip191":return await d_(t,e,r.s);case"eip1271":return await f_(t,e,r.s,n,i,s);default:throw new Error(`verifySignature failed: Attempted to verify CacaoSignature with unknown type: ${r.t}`)}}async function d_(t,e,r){return(await u1({hash:rd(e),signature:r})).toLowerCase()===t.toLowerCase()}async function f_(t,e,r,n,i,s){const o=dn(n);if(!o.namespace||!o.reference)throw new Error(`isValidEip1271Signature failed: chainId must be in CAIP-2 format, received: ${n}`);try{const a="0x1626ba7e",c="0000000000000000000000000000000000000000000000000000000000000040",u="0000000000000000000000000000000000000000000000000000000000000041",l=r.substring(2),h=rd(e).substring(2),d=a+h+c+u+l,f=await fetch(`${s||l_}/?chainId=${n}&projectId=${i}`,{method:"POST",body:JSON.stringify({id:p_(),jsonrpc:"2.0",method:"eth_call",params:[{to:t,data:d},"latest"]})}),{result:p}=await f.json();return p?p.slice(0,a.length).toLowerCase()===a.toLowerCase():!1}catch(a){return console.error("isValidEip1271Signature: ",a),!1}}function p_(){return Date.now()+Math.floor(Math.random()*1e3)}function g_(t){const e=atob(t),r=new Uint8Array(e.length);for(let o=0;o<e.length;o++)r[o]=e.charCodeAt(o);const n=r[0];if(n===0)throw new Error("No signatures found");const i=1+n*64;if(r.length<i)throw new Error("Transaction data too short for claimed signature count");if(r.length<100)throw new Error("Transaction too short");const s=Buffer.from(t,"base64").slice(1,65);return Vv.encode(s)}var y_=Object.defineProperty,m_=Object.defineProperties,w_=Object.getOwnPropertyDescriptors,nd=Object.getOwnPropertySymbols,b_=Object.prototype.hasOwnProperty,v_=Object.prototype.propertyIsEnumerable,id=(t,e,r)=>e in t?y_(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,E_=(t,e)=>{for(var r in e||(e={}))b_.call(e,r)&&id(t,r,e[r]);if(nd)for(var r of nd(e))v_.call(e,r)&&id(t,r,e[r]);return t},__=(t,e)=>m_(t,w_(e));const I_="did:pkh:",ya=t=>t?.split(":"),S_=t=>{const e=t&&ya(t);if(e)return t.includes(I_)?e[3]:e[1]},ma=t=>{const e=t&&ya(t);if(e)return e[2]+":"+e[3]},ys=t=>{const e=t&&ya(t);if(e)return e.pop()};async function sd(t){const{cacao:e,projectId:r}=t,{s:n,p:i}=e,s=od(i,i.iss),o=ys(i.iss);return await h_(o,s,n,ma(i.iss),r)}const od=(t,e)=>{const r=`${t.domain} wants you to sign in with your Ethereum account:`,n=ys(e);if(!t.aud&&!t.uri)throw new Error("Either `aud` or `uri` is required to construct the message");let i=t.statement||void 0;const s=`URI: ${t.aud||t.uri}`,o=`Version: ${t.version}`,a=`Chain ID: ${S_(e)}`,c=`Nonce: ${t.nonce}`,u=`Issued At: ${t.iat}`,l=t.exp?`Expiration Time: ${t.exp}`:void 0,h=t.nbf?`Not Before: ${t.nbf}`:void 0,d=t.requestId?`Request ID: ${t.requestId}`:void 0,f=t.resources?`Resources:${t.resources.map(y=>`
- ${y}`).join("")}`:void 0,p=ms(t.resources);if(p){const y=di(p);i=R_(i,y)}return[r,n,"",i,"",s,o,a,c,u,l,h,d,f].filter(y=>y!=null).join(`
`)};function x_(t){return Buffer.from(JSON.stringify(t)).toString("base64")}function O_(t){return JSON.parse(Buffer.from(t,"base64").toString("utf-8"))}function Br(t){if(!t)throw new Error("No recap provided, value is undefined");if(!t.att)throw new Error("No `att` property found");const e=Object.keys(t.att);if(!(e!=null&&e.length))throw new Error("No resources found in `att` property");e.forEach(r=>{const n=t.att[r];if(Array.isArray(n))throw new Error(`Resource must be an object: ${r}`);if(typeof n!="object")throw new Error(`Resource must be an object: ${r}`);if(!Object.keys(n).length)throw new Error(`Resource object is empty: ${r}`);Object.keys(n).forEach(i=>{const s=n[i];if(!Array.isArray(s))throw new Error(`Ability limits ${i} must be an array of objects, found: ${s}`);if(!s.length)throw new Error(`Value of ${i} is empty array, must be an array with objects`);s.forEach(o=>{if(typeof o!="object")throw new Error(`Ability limits (${i}) must be an array of objects, found: ${o}`)})})})}function D_(t,e,r,n={}){return r?.sort((i,s)=>i.localeCompare(s)),{att:{[t]:$_(e,r,n)}}}function $_(t,e,r={}){e=e?.sort((i,s)=>i.localeCompare(s));const n=e.map(i=>({[`${t}/${i}`]:[r]}));return Object.assign({},...n)}function ad(t){return Br(t),`urn:recap:${x_(t).replace(/=/g,"")}`}function di(t){const e=O_(t.replace("urn:recap:",""));return Br(e),e}function A_(t,e,r){const n=D_(t,e,r);return ad(n)}function P_(t){return t&&t.includes("urn:recap:")}function C_(t,e){const r=di(t),n=di(e),i=N_(r,n);return ad(i)}function N_(t,e){Br(t),Br(e);const r=Object.keys(t.att).concat(Object.keys(e.att)).sort((i,s)=>i.localeCompare(s)),n={att:{}};return r.forEach(i=>{var s,o;Object.keys(((s=t.att)==null?void 0:s[i])||{}).concat(Object.keys(((o=e.att)==null?void 0:o[i])||{})).sort((a,c)=>a.localeCompare(c)).forEach(a=>{var c,u;n.att[i]=__(E_({},n.att[i]),{[a]:((c=t.att[i])==null?void 0:c[a])||((u=e.att[i])==null?void 0:u[a])})})}),n}function R_(t="",e){Br(e);const r="I further authorize the stated URI to perform the following actions on my behalf: ";if(t.includes(r))return t;const n=[];let i=0;Object.keys(e.att).forEach(a=>{const c=Object.keys(e.att[a]).map(h=>({ability:h.split("/")[0],action:h.split("/")[1]}));c.sort((h,d)=>h.action.localeCompare(d.action));const u={};c.forEach(h=>{u[h.ability]||(u[h.ability]=[]),u[h.ability].push(h.action)});const l=Object.keys(u).map(h=>(i++,`(${i}) '${h}': '${u[h].join("', '")}' for '${a}'.`));n.push(l.join(", ").replace(".,","."))});const s=n.join(" "),o=`${r}${s}`;return`${t?t+" ":""}${o}`}function cd(t){var e;const r=di(t);Br(r);const n=(e=r.att)==null?void 0:e.eip155;return n?Object.keys(n).map(i=>i.split("/")[1]):[]}function ud(t){const e=di(t);Br(e);const r=[];return Object.values(e.att).forEach(n=>{Object.values(n).forEach(i=>{var s;(s=i?.[0])!=null&&s.chains&&r.push(i[0].chains)})}),[...new Set(r.flat())]}function ms(t){if(!t)return;const e=t?.[t.length-1];return P_(e)?e:void 0}function wa(t){if(!Number.isSafeInteger(t)||t<0)throw new Error("positive integer expected, got "+t)}function ld(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function rt(t,...e){if(!ld(t))throw new Error("Uint8Array expected");if(e.length>0&&!e.includes(t.length))throw new Error("Uint8Array expected of length "+e+", got length="+t.length)}function hd(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function T_(t,e){rt(t);const r=e.outputLen;if(t.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}function dd(t){if(typeof t!="boolean")throw new Error(`boolean expected, not ${t}`)}const fr=t=>new Uint32Array(t.buffer,t.byteOffset,Math.floor(t.byteLength/4)),B_=t=>new DataView(t.buffer,t.byteOffset,t.byteLength);if(!(new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68))throw new Error("Non little-endian hardware is not supported");function F_(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}function ba(t){if(typeof t=="string")t=F_(t);else if(ld(t))t=va(t);else throw new Error("Uint8Array expected, got "+typeof t);return t}function L_(t,e){if(e==null||typeof e!="object")throw new Error("options must be defined");return Object.assign(t,e)}function U_(t,e){if(t.length!==e.length)return!1;let r=0;for(let n=0;n<t.length;n++)r|=t[n]^e[n];return r===0}const j_=(t,e)=>{function r(n,...i){if(rt(n),t.nonceLength!==void 0){const u=i[0];if(!u)throw new Error("nonce / iv required");t.varSizeNonce?rt(u):rt(u,t.nonceLength)}const s=t.tagLength;s&&i[1]!==void 0&&rt(i[1]);const o=e(n,...i),a=(u,l)=>{if(l!==void 0){if(u!==2)throw new Error("cipher output not supported");rt(l)}};let c=!1;return{encrypt(u,l){if(c)throw new Error("cannot encrypt() twice with same key + nonce");return c=!0,rt(u),a(o.encrypt.length,l),o.encrypt(u,l)},decrypt(u,l){if(rt(u),s&&u.length<s)throw new Error("invalid ciphertext length: smaller than tagLength="+s);return a(o.decrypt.length,l),o.decrypt(u,l)}}}return Object.assign(r,t),r};function fd(t,e,r=!0){if(e===void 0)return new Uint8Array(t);if(e.length!==t)throw new Error("invalid output length, expected "+t+", got: "+e.length);if(r&&!q_(e))throw new Error("invalid output, must be aligned");return e}function pd(t,e,r,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,n);const i=BigInt(32),s=BigInt(**********),o=Number(r>>i&s),a=Number(r&s),c=n?4:0,u=n?0:4;t.setUint32(e+c,o,n),t.setUint32(e+u,a,n)}function q_(t){return t.byteOffset%4===0}function va(t){return Uint8Array.from(t)}function wn(...t){for(let e=0;e<t.length;e++)t[e].fill(0)}const gd=t=>Uint8Array.from(t.split("").map(e=>e.charCodeAt(0))),k_=gd("expand 16-byte k"),M_=gd("expand 32-byte k"),z_=fr(k_),H_=fr(M_);function J(t,e){return t<<e|t>>>32-e}function Ea(t){return t.byteOffset%4===0}const ws=64,V_=16,yd=2**32-1,md=new Uint32Array;function K_(t,e,r,n,i,s,o,a){const c=i.length,u=new Uint8Array(ws),l=fr(u),h=Ea(i)&&Ea(s),d=h?fr(i):md,f=h?fr(s):md;for(let p=0;p<c;o++){if(t(e,r,n,l,o,a),o>=yd)throw new Error("arx: counter overflow");const y=Math.min(ws,c-p);if(h&&y===ws){const g=p/4;if(p%4!==0)throw new Error("arx: invalid block position");for(let v=0,b;v<V_;v++)b=g+v,f[b]=d[b]^l[v];p+=ws;continue}for(let g=0,v;g<y;g++)v=p+g,s[v]=i[v]^u[g];p+=y}}function W_(t,e){const{allowShortKeys:r,extendNonceFn:n,counterLength:i,counterRight:s,rounds:o}=L_({allowShortKeys:!1,counterLength:8,counterRight:!1,rounds:20},e);if(typeof t!="function")throw new Error("core must be a function");return wa(i),wa(o),dd(s),dd(r),(a,c,u,l,h=0)=>{rt(a),rt(c),rt(u);const d=u.length;if(l===void 0&&(l=new Uint8Array(d)),rt(l),wa(h),h<0||h>=yd)throw new Error("arx: counter overflow");if(l.length<d)throw new Error(`arx: output (${l.length}) is shorter than data (${d})`);const f=[];let p=a.length,y,g;if(p===32)f.push(y=va(a)),g=H_;else if(p===16&&r)y=new Uint8Array(32),y.set(a),y.set(a,16),g=z_,f.push(y);else throw new Error(`arx: invalid 32-byte key, got length=${p}`);Ea(c)||f.push(c=va(c));const v=fr(y);if(n){if(c.length!==24)throw new Error("arx: extended nonce must be 24 bytes");n(g,v,fr(c.subarray(0,16)),v),c=c.subarray(16)}const b=16-i;if(b!==c.length)throw new Error(`arx: nonce must be ${b} or 16 bytes`);if(b!==12){const _=new Uint8Array(12);_.set(c,s?0:12-c.length),c=_,f.push(c)}const E=fr(c);return K_(t,g,v,E,u,l,h,o),wn(...f),l}}const Re=(t,e)=>t[e++]&255|(t[e++]&255)<<8;class G_{constructor(e){this.blockLen=16,this.outputLen=16,this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.pos=0,this.finished=!1,e=ba(e),rt(e,32);const r=Re(e,0),n=Re(e,2),i=Re(e,4),s=Re(e,6),o=Re(e,8),a=Re(e,10),c=Re(e,12),u=Re(e,14);this.r[0]=r&8191,this.r[1]=(r>>>13|n<<3)&8191,this.r[2]=(n>>>10|i<<6)&7939,this.r[3]=(i>>>7|s<<9)&8191,this.r[4]=(s>>>4|o<<12)&255,this.r[5]=o>>>1&8190,this.r[6]=(o>>>14|a<<2)&8191,this.r[7]=(a>>>11|c<<5)&8065,this.r[8]=(c>>>8|u<<8)&8191,this.r[9]=u>>>5&127;for(let l=0;l<8;l++)this.pad[l]=Re(e,16+2*l)}process(e,r,n=!1){const i=n?0:2048,{h:s,r:o}=this,a=o[0],c=o[1],u=o[2],l=o[3],h=o[4],d=o[5],f=o[6],p=o[7],y=o[8],g=o[9],v=Re(e,r+0),b=Re(e,r+2),E=Re(e,r+4),_=Re(e,r+6),P=Re(e,r+8),$=Re(e,r+10),O=Re(e,r+12),C=Re(e,r+14);let S=s[0]+(v&8191),k=s[1]+((v>>>13|b<<3)&8191),T=s[2]+((b>>>10|E<<6)&8191),R=s[3]+((E>>>7|_<<9)&8191),M=s[4]+((_>>>4|P<<12)&8191),D=s[5]+(P>>>1&8191),m=s[6]+((P>>>14|$<<2)&8191),w=s[7]+(($>>>11|O<<5)&8191),I=s[8]+((O>>>8|C<<8)&8191),A=s[9]+(C>>>5|i),x=0,N=x+S*a+k*(5*g)+T*(5*y)+R*(5*p)+M*(5*f);x=N>>>13,N&=8191,N+=D*(5*d)+m*(5*h)+w*(5*l)+I*(5*u)+A*(5*c),x+=N>>>13,N&=8191;let F=x+S*c+k*a+T*(5*g)+R*(5*y)+M*(5*p);x=F>>>13,F&=8191,F+=D*(5*f)+m*(5*d)+w*(5*h)+I*(5*l)+A*(5*u),x+=F>>>13,F&=8191;let j=x+S*u+k*c+T*a+R*(5*g)+M*(5*y);x=j>>>13,j&=8191,j+=D*(5*p)+m*(5*f)+w*(5*d)+I*(5*h)+A*(5*l),x+=j>>>13,j&=8191;let z=x+S*l+k*u+T*c+R*a+M*(5*g);x=z>>>13,z&=8191,z+=D*(5*y)+m*(5*p)+w*(5*f)+I*(5*d)+A*(5*h),x+=z>>>13,z&=8191;let q=x+S*h+k*l+T*u+R*c+M*a;x=q>>>13,q&=8191,q+=D*(5*g)+m*(5*y)+w*(5*p)+I*(5*f)+A*(5*d),x+=q>>>13,q&=8191;let H=x+S*d+k*h+T*l+R*u+M*c;x=H>>>13,H&=8191,H+=D*a+m*(5*g)+w*(5*y)+I*(5*p)+A*(5*f),x+=H>>>13,H&=8191;let V=x+S*f+k*d+T*h+R*l+M*u;x=V>>>13,V&=8191,V+=D*c+m*a+w*(5*g)+I*(5*y)+A*(5*p),x+=V>>>13,V&=8191;let ee=x+S*p+k*f+T*d+R*h+M*l;x=ee>>>13,ee&=8191,ee+=D*u+m*c+w*a+I*(5*g)+A*(5*y),x+=ee>>>13,ee&=8191;let G=x+S*y+k*p+T*f+R*d+M*h;x=G>>>13,G&=8191,G+=D*l+m*u+w*c+I*a+A*(5*g),x+=G>>>13,G&=8191;let W=x+S*g+k*y+T*p+R*f+M*d;x=W>>>13,W&=8191,W+=D*h+m*l+w*u+I*c+A*a,x+=W>>>13,W&=8191,x=(x<<2)+x|0,x=x+N|0,N=x&8191,x=x>>>13,F+=x,s[0]=N,s[1]=F,s[2]=j,s[3]=z,s[4]=q,s[5]=H,s[6]=V,s[7]=ee,s[8]=G,s[9]=W}finalize(){const{h:e,pad:r}=this,n=new Uint16Array(10);let i=e[1]>>>13;e[1]&=8191;for(let a=2;a<10;a++)e[a]+=i,i=e[a]>>>13,e[a]&=8191;e[0]+=i*5,i=e[0]>>>13,e[0]&=8191,e[1]+=i,i=e[1]>>>13,e[1]&=8191,e[2]+=i,n[0]=e[0]+5,i=n[0]>>>13,n[0]&=8191;for(let a=1;a<10;a++)n[a]=e[a]+i,i=n[a]>>>13,n[a]&=8191;n[9]-=8192;let s=(i^1)-1;for(let a=0;a<10;a++)n[a]&=s;s=~s;for(let a=0;a<10;a++)e[a]=e[a]&s|n[a];e[0]=(e[0]|e[1]<<13)&65535,e[1]=(e[1]>>>3|e[2]<<10)&65535,e[2]=(e[2]>>>6|e[3]<<7)&65535,e[3]=(e[3]>>>9|e[4]<<4)&65535,e[4]=(e[4]>>>12|e[5]<<1|e[6]<<14)&65535,e[5]=(e[6]>>>2|e[7]<<11)&65535,e[6]=(e[7]>>>5|e[8]<<8)&65535,e[7]=(e[8]>>>8|e[9]<<5)&65535;let o=e[0]+r[0];e[0]=o&65535;for(let a=1;a<8;a++)o=(e[a]+r[a]|0)+(o>>>16)|0,e[a]=o&65535;wn(n)}update(e){hd(this);const{buffer:r,blockLen:n}=this;e=ba(e);const i=e.length;for(let s=0;s<i;){const o=Math.min(n-this.pos,i-s);if(o===n){for(;n<=i-s;s+=n)this.process(e,s);continue}r.set(e.subarray(s,s+o),this.pos),this.pos+=o,s+=o,this.pos===n&&(this.process(r,0,!1),this.pos=0)}return this}destroy(){wn(this.h,this.r,this.buffer,this.pad)}digestInto(e){hd(this),T_(e,this),this.finished=!0;const{buffer:r,h:n}=this;let{pos:i}=this;if(i){for(r[i++]=1;i<16;i++)r[i]=0;this.process(r,0,!0)}this.finalize();let s=0;for(let o=0;o<8;o++)e[s++]=n[o]>>>0,e[s++]=n[o]>>>8;return e}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const n=e.slice(0,r);return this.destroy(),n}}function Y_(t){const e=(n,i)=>t(i).update(ba(n)).digest(),r=t(new Uint8Array(32));return e.outputLen=r.outputLen,e.blockLen=r.blockLen,e.create=n=>t(n),e}const Z_=Y_(t=>new G_(t));function J_(t,e,r,n,i,s=20){let o=t[0],a=t[1],c=t[2],u=t[3],l=e[0],h=e[1],d=e[2],f=e[3],p=e[4],y=e[5],g=e[6],v=e[7],b=i,E=r[0],_=r[1],P=r[2],$=o,O=a,C=c,S=u,k=l,T=h,R=d,M=f,D=p,m=y,w=g,I=v,A=b,x=E,N=_,F=P;for(let z=0;z<s;z+=2)$=$+k|0,A=J(A^$,16),D=D+A|0,k=J(k^D,12),$=$+k|0,A=J(A^$,8),D=D+A|0,k=J(k^D,7),O=O+T|0,x=J(x^O,16),m=m+x|0,T=J(T^m,12),O=O+T|0,x=J(x^O,8),m=m+x|0,T=J(T^m,7),C=C+R|0,N=J(N^C,16),w=w+N|0,R=J(R^w,12),C=C+R|0,N=J(N^C,8),w=w+N|0,R=J(R^w,7),S=S+M|0,F=J(F^S,16),I=I+F|0,M=J(M^I,12),S=S+M|0,F=J(F^S,8),I=I+F|0,M=J(M^I,7),$=$+T|0,F=J(F^$,16),w=w+F|0,T=J(T^w,12),$=$+T|0,F=J(F^$,8),w=w+F|0,T=J(T^w,7),O=O+R|0,A=J(A^O,16),I=I+A|0,R=J(R^I,12),O=O+R|0,A=J(A^O,8),I=I+A|0,R=J(R^I,7),C=C+M|0,x=J(x^C,16),D=D+x|0,M=J(M^D,12),C=C+M|0,x=J(x^C,8),D=D+x|0,M=J(M^D,7),S=S+k|0,N=J(N^S,16),m=m+N|0,k=J(k^m,12),S=S+k|0,N=J(N^S,8),m=m+N|0,k=J(k^m,7);let j=0;n[j++]=o+$|0,n[j++]=a+O|0,n[j++]=c+C|0,n[j++]=u+S|0,n[j++]=l+k|0,n[j++]=h+T|0,n[j++]=d+R|0,n[j++]=f+M|0,n[j++]=p+D|0,n[j++]=y+m|0,n[j++]=g+w|0,n[j++]=v+I|0,n[j++]=b+A|0,n[j++]=E+x|0,n[j++]=_+N|0,n[j++]=P+F|0}const X_=W_(J_,{counterRight:!1,counterLength:4,allowShortKeys:!1}),Q_=new Uint8Array(16),wd=(t,e)=>{t.update(e);const r=e.length%16;r&&t.update(Q_.subarray(r))},eI=new Uint8Array(32);function bd(t,e,r,n,i){const s=t(e,r,eI),o=Z_.create(s);i&&wd(o,i),wd(o,n);const a=new Uint8Array(16),c=B_(a);pd(c,0,BigInt(i?i.length:0),!0),pd(c,8,BigInt(n.length),!0),o.update(a);const u=o.digest();return wn(s,a),u}const tI=t=>(e,r,n)=>({encrypt(i,s){const o=i.length;s=fd(o+16,s,!1),s.set(i);const a=s.subarray(0,-16);t(e,r,a,a,1);const c=bd(t,e,r,a,n);return s.set(c,o),wn(c),s},decrypt(i,s){s=fd(i.length-16,s,!1);const o=i.subarray(0,-16),a=i.subarray(-16),c=bd(t,e,r,o,n);if(!U_(a,c))throw new Error("invalid tag");return s.set(i.subarray(0,-16)),t(e,r,s,s,1),wn(c),s}}),vd=j_({blockSize:64,nonceLength:12,tagLength:16},tI(X_));class Ed extends pa{constructor(e,r){super(),this.finished=!1,this.destroyed=!1,da(e);const n=yn(r);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const i=this.blockLen,s=new Uint8Array(i);s.set(n.length>i?e.create().update(n).digest():n);for(let o=0;o<s.length;o++)s[o]^=54;this.iHash.update(s),this.oHash=e.create();for(let o=0;o<s.length;o++)s[o]^=106;this.oHash.update(s),s.fill(0)}update(e){return pn(this),this.iHash.update(e),this}digestInto(e){pn(this),li(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){const e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));const{oHash:r,iHash:n,finished:i,destroyed:s,blockLen:o,outputLen:a}=this;return e=e,e.finished=i,e.destroyed=s,e.blockLen=o,e.outputLen=a,e.oHash=r._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const bs=(t,e,r)=>new Ed(t,e).update(r).digest();bs.create=(t,e)=>new Ed(t,e);function rI(t,e,r){return da(t),r===void 0&&(r=new Uint8Array(t.outputLen)),bs(t,yn(r),yn(e))}const _a=new Uint8Array([0]),_d=new Uint8Array;function nI(t,e,r,n=32){if(da(t),ui(n),n>255*t.outputLen)throw new Error("Length should be <= 255*HashLen");const i=Math.ceil(n/t.outputLen);r===void 0&&(r=_d);const s=new Uint8Array(i*t.outputLen),o=bs.create(t,e),a=o._cloneInto(),c=new Uint8Array(o.outputLen);for(let u=0;u<i;u++)_a[0]=u+1,a.update(u===0?_d:c).update(r).update(_a).digestInto(c),s.set(c,t.outputLen*u),o._cloneInto(a);return o.destroy(),a.destroy(),c.fill(0),_a.fill(0),s.slice(0,n)}const iI=(t,e,r,n,i)=>nI(t,rI(t,e,r),n,i);function sI(t,e,r,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,r,n);const i=BigInt(32),s=BigInt(**********),o=Number(r>>i&s),a=Number(r&s),c=n?4:0,u=n?0:4;t.setUint32(e+c,o,n),t.setUint32(e+u,a,n)}function oI(t,e,r){return t&e^~t&r}function aI(t,e,r){return t&e^t&r^e&r}class cI extends pa{constructor(e,r,n,i){super(),this.blockLen=e,this.outputLen=r,this.padOffset=n,this.isLE=i,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=fa(this.buffer)}update(e){pn(this);const{view:r,buffer:n,blockLen:i}=this;e=yn(e);const s=e.length;for(let o=0;o<s;){const a=Math.min(i-this.pos,s-o);if(a===i){const c=fa(e);for(;i<=s-o;o+=i)this.process(c,o);continue}n.set(e.subarray(o,o+a),this.pos),this.pos+=a,o+=a,this.pos===i&&(this.process(r,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){pn(this),Kh(e,this),this.finished=!0;const{buffer:r,view:n,blockLen:i,isLE:s}=this;let{pos:o}=this;r[o++]=128,this.buffer.subarray(o).fill(0),this.padOffset>i-o&&(this.process(n,0),o=0);for(let h=o;h<i;h++)r[h]=0;sI(n,i-8,BigInt(this.length*8),s),this.process(n,0);const a=fa(e),c=this.outputLen;if(c%4)throw new Error("_sha2: outputLen should be aligned to 32bit");const u=c/4,l=this.get();if(u>l.length)throw new Error("_sha2: outputLen bigger than state");for(let h=0;h<u;h++)a.setUint32(4*h,l[h],s)}digest(){const{buffer:e,outputLen:r}=this;this.digestInto(e);const n=e.slice(0,r);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());const{blockLen:r,buffer:n,length:i,finished:s,destroyed:o,pos:a}=this;return e.length=i,e.pos=a,e.finished=s,e.destroyed=o,i%r&&e.buffer.set(n),e}}const uI=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),pr=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),gr=new Uint32Array(64);class lI extends cI{constructor(){super(64,32,8,!1),this.A=pr[0]|0,this.B=pr[1]|0,this.C=pr[2]|0,this.D=pr[3]|0,this.E=pr[4]|0,this.F=pr[5]|0,this.G=pr[6]|0,this.H=pr[7]|0}get(){const{A:e,B:r,C:n,D:i,E:s,F:o,G:a,H:c}=this;return[e,r,n,i,s,o,a,c]}set(e,r,n,i,s,o,a,c){this.A=e|0,this.B=r|0,this.C=n|0,this.D=i|0,this.E=s|0,this.F=o|0,this.G=a|0,this.H=c|0}process(e,r){for(let h=0;h<16;h++,r+=4)gr[h]=e.getUint32(r,!1);for(let h=16;h<64;h++){const d=gr[h-15],f=gr[h-2],p=Pt(d,7)^Pt(d,18)^d>>>3,y=Pt(f,17)^Pt(f,19)^f>>>10;gr[h]=y+gr[h-7]+p+gr[h-16]|0}let{A:n,B:i,C:s,D:o,E:a,F:c,G:u,H:l}=this;for(let h=0;h<64;h++){const d=Pt(a,6)^Pt(a,11)^Pt(a,25),f=l+d+oI(a,c,u)+uI[h]+gr[h]|0,p=(Pt(n,2)^Pt(n,13)^Pt(n,22))+aI(n,i,s)|0;l=u,u=c,c=a,a=o+f|0,o=s,s=i,i=n,n=f+p|0}n=n+this.A|0,i=i+this.B|0,s=s+this.C|0,o=o+this.D|0,a=a+this.E|0,c=c+this.F|0,u=u+this.G|0,l=l+this.H|0,this.set(n,i,s,o,a,c,u,l)}roundClean(){gr.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}const fi=Zh(()=>new lI);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const vs=BigInt(0),Es=BigInt(1),hI=BigInt(2);function Fr(t){return t instanceof Uint8Array||ArrayBuffer.isView(t)&&t.constructor.name==="Uint8Array"}function pi(t){if(!Fr(t))throw new Error("Uint8Array expected")}function bn(t,e){if(typeof e!="boolean")throw new Error(t+" boolean expected, got "+e)}const dI=Array.from({length:256},(t,e)=>e.toString(16).padStart(2,"0"));function vn(t){pi(t);let e="";for(let r=0;r<t.length;r++)e+=dI[t[r]];return e}function En(t){const e=t.toString(16);return e.length&1?"0"+e:e}function Ia(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);return t===""?vs:BigInt("0x"+t)}const Kt={_0:48,_9:57,A:65,F:70,a:97,f:102};function Id(t){if(t>=Kt._0&&t<=Kt._9)return t-Kt._0;if(t>=Kt.A&&t<=Kt.F)return t-(Kt.A-10);if(t>=Kt.a&&t<=Kt.f)return t-(Kt.a-10)}function _n(t){if(typeof t!="string")throw new Error("hex string expected, got "+typeof t);const e=t.length,r=e/2;if(e%2)throw new Error("hex string expected, got unpadded hex of length "+e);const n=new Uint8Array(r);for(let i=0,s=0;i<r;i++,s+=2){const o=Id(t.charCodeAt(s)),a=Id(t.charCodeAt(s+1));if(o===void 0||a===void 0){const c=t[s]+t[s+1];throw new Error('hex string expected, got non-hex character "'+c+'" at index '+s)}n[i]=o*16+a}return n}function Lr(t){return Ia(vn(t))}function gi(t){return pi(t),Ia(vn(Uint8Array.from(t).reverse()))}function In(t,e){return _n(t.toString(16).padStart(e*2,"0"))}function _s(t,e){return In(t,e).reverse()}function fI(t){return _n(En(t))}function nt(t,e,r){let n;if(typeof e=="string")try{n=_n(e)}catch(s){throw new Error(t+" must be hex string or Uint8Array, cause: "+s)}else if(Fr(e))n=Uint8Array.from(e);else throw new Error(t+" must be hex string or Uint8Array");const i=n.length;if(typeof r=="number"&&i!==r)throw new Error(t+" of length "+r+" expected, got "+i);return n}function yi(...t){let e=0;for(let n=0;n<t.length;n++){const i=t[n];pi(i),e+=i.length}const r=new Uint8Array(e);for(let n=0,i=0;n<t.length;n++){const s=t[n];r.set(s,i),i+=s.length}return r}function pI(t,e){if(t.length!==e.length)return!1;let r=0;for(let n=0;n<t.length;n++)r|=t[n]^e[n];return r===0}function gI(t){if(typeof t!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(t))}const Sa=t=>typeof t=="bigint"&&vs<=t;function Is(t,e,r){return Sa(t)&&Sa(e)&&Sa(r)&&e<=t&&t<r}function Wt(t,e,r,n){if(!Is(e,r,n))throw new Error("expected valid "+t+": "+r+" <= n < "+n+", got "+e)}function Sd(t){let e;for(e=0;t>vs;t>>=Es,e+=1);return e}function yI(t,e){return t>>BigInt(e)&Es}function mI(t,e,r){return t|(r?Es:vs)<<BigInt(e)}const xa=t=>(hI<<BigInt(t-1))-Es,Oa=t=>new Uint8Array(t),xd=t=>Uint8Array.from(t);function Od(t,e,r){if(typeof t!="number"||t<2)throw new Error("hashLen must be a number");if(typeof e!="number"||e<2)throw new Error("qByteLen must be a number");if(typeof r!="function")throw new Error("hmacFn must be a function");let n=Oa(t),i=Oa(t),s=0;const o=()=>{n.fill(1),i.fill(0),s=0},a=(...l)=>r(i,n,...l),c=(l=Oa())=>{i=a(xd([0]),l),n=a(),l.length!==0&&(i=a(xd([1]),l),n=a())},u=()=>{if(s++>=1e3)throw new Error("drbg: tried 1000 values");let l=0;const h=[];for(;l<e;){n=a();const d=n.slice();h.push(d),l+=n.length}return yi(...h)};return(l,h)=>{o(),c(l);let d;for(;!(d=h(u()));)c();return o(),d}}const wI={bigint:t=>typeof t=="bigint",function:t=>typeof t=="function",boolean:t=>typeof t=="boolean",string:t=>typeof t=="string",stringOrUint8Array:t=>typeof t=="string"||Fr(t),isSafeInteger:t=>Number.isSafeInteger(t),array:t=>Array.isArray(t),field:(t,e)=>e.Fp.isValid(t),hash:t=>typeof t=="function"&&Number.isSafeInteger(t.outputLen)};function Sn(t,e,r={}){const n=(i,s,o)=>{const a=wI[s];if(typeof a!="function")throw new Error("invalid validator function");const c=t[i];if(!(o&&c===void 0)&&!a(c,t))throw new Error("param "+String(i)+" is invalid. Expected "+s+", got "+c)};for(const[i,s]of Object.entries(e))n(i,s,!1);for(const[i,s]of Object.entries(r))n(i,s,!0);return t}const bI=()=>{throw new Error("not implemented")};function Da(t){const e=new WeakMap;return(r,...n)=>{const i=e.get(r);if(i!==void 0)return i;const s=t(r,...n);return e.set(r,s),s}}var vI=Object.freeze({__proto__:null,isBytes:Fr,abytes:pi,abool:bn,bytesToHex:vn,numberToHexUnpadded:En,hexToNumber:Ia,hexToBytes:_n,bytesToNumberBE:Lr,bytesToNumberLE:gi,numberToBytesBE:In,numberToBytesLE:_s,numberToVarBytesBE:fI,ensureBytes:nt,concatBytes:yi,equalBytes:pI,utf8ToBytes:gI,inRange:Is,aInRange:Wt,bitLen:Sd,bitGet:yI,bitSet:mI,bitMask:xa,createHmacDrbg:Od,validateObject:Sn,notImplemented:bI,memoized:Da});const xe=BigInt(0),pe=BigInt(1),Ur=BigInt(2),EI=BigInt(3),$a=BigInt(4),Dd=BigInt(5),$d=BigInt(8);function Je(t,e){const r=t%e;return r>=xe?r:e+r}function Ad(t,e,r){if(e<xe)throw new Error("invalid exponent, negatives unsupported");if(r<=xe)throw new Error("invalid modulus");if(r===pe)return xe;let n=pe;for(;e>xe;)e&pe&&(n=n*t%r),t=t*t%r,e>>=pe;return n}function mt(t,e,r){let n=t;for(;e-- >xe;)n*=n,n%=r;return n}function Aa(t,e){if(t===xe)throw new Error("invert: expected non-zero number");if(e<=xe)throw new Error("invert: expected positive modulus, got "+e);let r=Je(t,e),n=e,i=xe,s=pe;for(;r!==xe;){const o=n/r,a=n%r,c=i-s*o;n=r,r=a,i=s,s=c}if(n!==pe)throw new Error("invert: does not exist");return Je(i,e)}function _I(t){const e=(t-pe)/Ur;let r,n,i;for(r=t-pe,n=0;r%Ur===xe;r/=Ur,n++);for(i=Ur;i<t&&Ad(i,e,t)!==t-pe;i++)if(i>1e3)throw new Error("Cannot find square root: likely non-prime P");if(n===1){const o=(t+pe)/$a;return function(a,c){const u=a.pow(c,o);if(!a.eql(a.sqr(u),c))throw new Error("Cannot find square root");return u}}const s=(r+pe)/Ur;return function(o,a){if(o.pow(a,e)===o.neg(o.ONE))throw new Error("Cannot find square root");let c=n,u=o.pow(o.mul(o.ONE,i),r),l=o.pow(a,s),h=o.pow(a,r);for(;!o.eql(h,o.ONE);){if(o.eql(h,o.ZERO))return o.ZERO;let d=1;for(let p=o.sqr(h);d<c&&!o.eql(p,o.ONE);d++)p=o.sqr(p);const f=o.pow(u,pe<<BigInt(c-d-1));u=o.sqr(f),l=o.mul(l,f),h=o.mul(h,u),c=d}return l}}function II(t){if(t%$a===EI){const e=(t+pe)/$a;return function(r,n){const i=r.pow(n,e);if(!r.eql(r.sqr(i),n))throw new Error("Cannot find square root");return i}}if(t%$d===Dd){const e=(t-Dd)/$d;return function(r,n){const i=r.mul(n,Ur),s=r.pow(i,e),o=r.mul(n,s),a=r.mul(r.mul(o,Ur),s),c=r.mul(o,r.sub(a,r.ONE));if(!r.eql(r.sqr(c),n))throw new Error("Cannot find square root");return c}}return _I(t)}const SI=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function xI(t){const e={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},r=SI.reduce((n,i)=>(n[i]="function",n),e);return Sn(t,r)}function OI(t,e,r){if(r<xe)throw new Error("invalid exponent, negatives unsupported");if(r===xe)return t.ONE;if(r===pe)return e;let n=t.ONE,i=e;for(;r>xe;)r&pe&&(n=t.mul(n,i)),i=t.sqr(i),r>>=pe;return n}function DI(t,e){const r=new Array(e.length),n=e.reduce((s,o,a)=>t.is0(o)?s:(r[a]=s,t.mul(s,o)),t.ONE),i=t.inv(n);return e.reduceRight((s,o,a)=>t.is0(o)?s:(r[a]=t.mul(s,r[a]),t.mul(s,o)),i),r}function Pd(t,e){const r=e!==void 0?e:t.toString(2).length,n=Math.ceil(r/8);return{nBitLength:r,nByteLength:n}}function Cd(t,e,r=!1,n={}){if(t<=xe)throw new Error("invalid field: expected ORDER > 0, got "+t);const{nBitLength:i,nByteLength:s}=Pd(t,e);if(s>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let o;const a=Object.freeze({ORDER:t,isLE:r,BITS:i,BYTES:s,MASK:xa(i),ZERO:xe,ONE:pe,create:c=>Je(c,t),isValid:c=>{if(typeof c!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof c);return xe<=c&&c<t},is0:c=>c===xe,isOdd:c=>(c&pe)===pe,neg:c=>Je(-c,t),eql:(c,u)=>c===u,sqr:c=>Je(c*c,t),add:(c,u)=>Je(c+u,t),sub:(c,u)=>Je(c-u,t),mul:(c,u)=>Je(c*u,t),pow:(c,u)=>OI(a,c,u),div:(c,u)=>Je(c*Aa(u,t),t),sqrN:c=>c*c,addN:(c,u)=>c+u,subN:(c,u)=>c-u,mulN:(c,u)=>c*u,inv:c=>Aa(c,t),sqrt:n.sqrt||(c=>(o||(o=II(t)),o(a,c))),invertBatch:c=>DI(a,c),cmov:(c,u,l)=>l?u:c,toBytes:c=>r?_s(c,s):In(c,s),fromBytes:c=>{if(c.length!==s)throw new Error("Field.fromBytes: expected "+s+" bytes, got "+c.length);return r?gi(c):Lr(c)}});return Object.freeze(a)}function Nd(t){if(typeof t!="bigint")throw new Error("field order must be bigint");const e=t.toString(2).length;return Math.ceil(e/8)}function Rd(t){const e=Nd(t);return e+Math.ceil(e/2)}function $I(t,e,r=!1){const n=t.length,i=Nd(e),s=Rd(e);if(n<16||n<s||n>1024)throw new Error("expected "+s+"-1024 bytes of input, got "+n);const o=r?gi(t):Lr(t),a=Je(o,e-pe)+pe;return r?_s(a,i):In(a,i)}const Td=BigInt(0),Ss=BigInt(1);function Pa(t,e){const r=e.negate();return t?r:e}function Bd(t,e){if(!Number.isSafeInteger(t)||t<=0||t>e)throw new Error("invalid window size, expected [1.."+e+"], got W="+t)}function Ca(t,e){Bd(t,e);const r=Math.ceil(e/t)+1,n=2**(t-1);return{windows:r,windowSize:n}}function AI(t,e){if(!Array.isArray(t))throw new Error("array expected");t.forEach((r,n)=>{if(!(r instanceof e))throw new Error("invalid point at index "+n)})}function PI(t,e){if(!Array.isArray(t))throw new Error("array of scalars expected");t.forEach((r,n)=>{if(!e.isValid(r))throw new Error("invalid scalar at index "+n)})}const Na=new WeakMap,Fd=new WeakMap;function Ra(t){return Fd.get(t)||1}function CI(t,e){return{constTimeNegate:Pa,hasPrecomputes(r){return Ra(r)!==1},unsafeLadder(r,n,i=t.ZERO){let s=r;for(;n>Td;)n&Ss&&(i=i.add(s)),s=s.double(),n>>=Ss;return i},precomputeWindow(r,n){const{windows:i,windowSize:s}=Ca(n,e),o=[];let a=r,c=a;for(let u=0;u<i;u++){c=a,o.push(c);for(let l=1;l<s;l++)c=c.add(a),o.push(c);a=c.double()}return o},wNAF(r,n,i){const{windows:s,windowSize:o}=Ca(r,e);let a=t.ZERO,c=t.BASE;const u=BigInt(2**r-1),l=2**r,h=BigInt(r);for(let d=0;d<s;d++){const f=d*o;let p=Number(i&u);i>>=h,p>o&&(p-=l,i+=Ss);const y=f,g=f+Math.abs(p)-1,v=d%2!==0,b=p<0;p===0?c=c.add(Pa(v,n[y])):a=a.add(Pa(b,n[g]))}return{p:a,f:c}},wNAFUnsafe(r,n,i,s=t.ZERO){const{windows:o,windowSize:a}=Ca(r,e),c=BigInt(2**r-1),u=2**r,l=BigInt(r);for(let h=0;h<o;h++){const d=h*a;if(i===Td)break;let f=Number(i&c);if(i>>=l,f>a&&(f-=u,i+=Ss),f===0)continue;let p=n[d+Math.abs(f)-1];f<0&&(p=p.negate()),s=s.add(p)}return s},getPrecomputes(r,n,i){let s=Na.get(n);return s||(s=this.precomputeWindow(n,r),r!==1&&Na.set(n,i(s))),s},wNAFCached(r,n,i){const s=Ra(r);return this.wNAF(s,this.getPrecomputes(s,r,i),n)},wNAFCachedUnsafe(r,n,i,s){const o=Ra(r);return o===1?this.unsafeLadder(r,n,s):this.wNAFUnsafe(o,this.getPrecomputes(o,r,i),n,s)},setWindowSize(r,n){Bd(n,e),Fd.set(r,n),Na.delete(r)}}}function NI(t,e,r,n){if(AI(r,t),PI(n,e),r.length!==n.length)throw new Error("arrays of points and scalars must have equal length");const i=t.ZERO,s=Sd(BigInt(r.length)),o=s>12?s-3:s>4?s-2:s?2:1,a=(1<<o)-1,c=new Array(a+1).fill(i),u=Math.floor((e.BITS-1)/o)*o;let l=i;for(let h=u;h>=0;h-=o){c.fill(i);for(let f=0;f<n.length;f++){const p=n[f],y=Number(p>>BigInt(h)&BigInt(a));c[y]=c[y].add(r[f])}let d=i;for(let f=c.length-1,p=i;f>0;f--)p=p.add(c[f]),d=d.add(p);if(l=l.add(d),h!==0)for(let f=0;f<o;f++)l=l.double()}return l}function Ld(t){return xI(t.Fp),Sn(t,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze(ca(Vt(Vt({},Pd(t.n,t.nBitLength)),t),{p:t.Fp.ORDER}))}BigInt(0),BigInt(1),BigInt(2),BigInt(8);const xn=BigInt(0),Ta=BigInt(1);function RI(t){return Sn(t,{a:"bigint"},{montgomeryBits:"isSafeInteger",nByteLength:"isSafeInteger",adjustScalarBytes:"function",domain:"function",powPminus2:"function",Gu:"bigint"}),Object.freeze(Vt({},t))}function TI(t){const e=RI(t),{P:r}=e,n=b=>Je(b,r),i=e.montgomeryBits,s=Math.ceil(i/8),o=e.nByteLength,a=e.adjustScalarBytes||(b=>b),c=e.powPminus2||(b=>Ad(b,r-BigInt(2),r));function u(b,E,_){const P=n(b*(E-_));return E=n(E-P),_=n(_+P),[E,_]}const l=(e.a-BigInt(2))/BigInt(4);function h(b,E){Wt("u",b,xn,r),Wt("scalar",E,xn,r);const _=E,P=b;let $=Ta,O=xn,C=b,S=Ta,k=xn,T;for(let M=BigInt(i-1);M>=xn;M--){const D=_>>M&Ta;k^=D,T=u(k,$,C),$=T[0],C=T[1],T=u(k,O,S),O=T[0],S=T[1],k=D;const m=$+O,w=n(m*m),I=$-O,A=n(I*I),x=w-A,N=C+S,F=C-S,j=n(F*m),z=n(N*I),q=j+z,H=j-z;C=n(q*q),S=n(P*n(H*H)),$=n(w*A),O=n(x*(w+n(l*x)))}T=u(k,$,C),$=T[0],C=T[1],T=u(k,O,S),O=T[0],S=T[1];const R=c(O);return n($*R)}function d(b){return _s(n(b),s)}function f(b){const E=nt("u coordinate",b,s);return o===32&&(E[31]&=127),gi(E)}function p(b){const E=nt("scalar",b),_=E.length;if(_!==s&&_!==o){let P=""+s+" or "+o;throw new Error("invalid scalar, expected "+P+" bytes, got "+_)}return gi(a(E))}function y(b,E){const _=f(E),P=p(b),$=h(_,P);if($===xn)throw new Error("invalid private or public key received");return d($)}const g=d(e.Gu);function v(b){return y(b,g)}return{scalarMult:y,scalarMultBase:v,getSharedSecret:(b,E)=>y(b,E),getPublicKey:b=>v(b),utils:{randomPrivateKey:()=>e.randomBytes(e.nByteLength)},GuBytes:g}}const Ba=BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949");BigInt(0);const BI=BigInt(1),Ud=BigInt(2),FI=BigInt(3),LI=BigInt(5);BigInt(8);function UI(t){const e=BigInt(10),r=BigInt(20),n=BigInt(40),i=BigInt(80),s=Ba,o=t*t%s*t%s,a=mt(o,Ud,s)*o%s,c=mt(a,BI,s)*t%s,u=mt(c,LI,s)*c%s,l=mt(u,e,s)*u%s,h=mt(l,r,s)*l%s,d=mt(h,n,s)*h%s,f=mt(d,i,s)*d%s,p=mt(f,i,s)*d%s,y=mt(p,e,s)*u%s;return{pow_p_5_8:mt(y,Ud,s)*t%s,b2:o}}function jI(t){return t[0]&=248,t[31]&=127,t[31]|=64,t}const Fa=TI({P:Ba,a:BigInt(486662),montgomeryBits:255,nByteLength:32,Gu:BigInt(9),powPminus2:t=>{const e=Ba,{pow_p_5_8:r,b2:n}=UI(t);return Je(mt(r,FI,e)*n,e)},adjustScalarBytes:jI,randomBytes:mn});function jd(t){t.lowS!==void 0&&bn("lowS",t.lowS),t.prehash!==void 0&&bn("prehash",t.prehash)}function qI(t){const e=Ld(t);Sn(e,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:r,Fp:n,a:i}=e;if(r){if(!n.eql(i,n.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof r!="object"||typeof r.beta!="bigint"||typeof r.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze(Vt({},e))}const{bytesToNumberBE:kI,hexToBytes:MI}=vI;class zI extends Error{constructor(e=""){super(e)}}const Gt={Err:zI,_tlv:{encode:(t,e)=>{const{Err:r}=Gt;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length&1)throw new r("tlv.encode: unpadded data");const n=e.length/2,i=En(n);if(i.length/2&128)throw new r("tlv.encode: long form length too big");const s=n>127?En(i.length/2|128):"";return En(t)+s+i+e},decode(t,e){const{Err:r}=Gt;let n=0;if(t<0||t>256)throw new r("tlv.encode: wrong tag");if(e.length<2||e[n++]!==t)throw new r("tlv.decode: wrong tlv");const i=e[n++],s=!!(i&128);let o=0;if(!s)o=i;else{const c=i&127;if(!c)throw new r("tlv.decode(long): indefinite length not supported");if(c>4)throw new r("tlv.decode(long): byte length is too big");const u=e.subarray(n,n+c);if(u.length!==c)throw new r("tlv.decode: length bytes not complete");if(u[0]===0)throw new r("tlv.decode(long): zero leftmost byte");for(const l of u)o=o<<8|l;if(n+=c,o<128)throw new r("tlv.decode(long): not minimal encoding")}const a=e.subarray(n,n+o);if(a.length!==o)throw new r("tlv.decode: wrong value length");return{v:a,l:e.subarray(n+o)}}},_int:{encode(t){const{Err:e}=Gt;if(t<Yt)throw new e("integer: negative integers are not allowed");let r=En(t);if(Number.parseInt(r[0],16)&8&&(r="00"+r),r.length&1)throw new e("unexpected DER parsing assertion: unpadded hex");return r},decode(t){const{Err:e}=Gt;if(t[0]&128)throw new e("invalid signature integer: negative");if(t[0]===0&&!(t[1]&128))throw new e("invalid signature integer: unnecessary leading zero");return kI(t)}},toSig(t){const{Err:e,_int:r,_tlv:n}=Gt,i=typeof t=="string"?MI(t):t;pi(i);const{v:s,l:o}=n.decode(48,i);if(o.length)throw new e("invalid signature: left bytes after parsing");const{v:a,l:c}=n.decode(2,s),{v:u,l}=n.decode(2,c);if(l.length)throw new e("invalid signature: left bytes after parsing");return{r:r.decode(a),s:r.decode(u)}},hexFromSig(t){const{_tlv:e,_int:r}=Gt,n=e.encode(2,r.encode(t.r)),i=e.encode(2,r.encode(t.s)),s=n+i;return e.encode(48,s)}},Yt=BigInt(0),Oe=BigInt(1);BigInt(2);const qd=BigInt(3);BigInt(4);function HI(t){const e=qI(t),{Fp:r}=e,n=Cd(e.n,e.nBitLength),i=e.toBytes||((y,g,v)=>{const b=g.toAffine();return yi(Uint8Array.from([4]),r.toBytes(b.x),r.toBytes(b.y))}),s=e.fromBytes||(y=>{const g=y.subarray(1),v=r.fromBytes(g.subarray(0,r.BYTES)),b=r.fromBytes(g.subarray(r.BYTES,2*r.BYTES));return{x:v,y:b}});function o(y){const{a:g,b:v}=e,b=r.sqr(y),E=r.mul(b,y);return r.add(r.add(E,r.mul(y,g)),v)}if(!r.eql(r.sqr(e.Gy),o(e.Gx)))throw new Error("bad generator point: equation left != right");function a(y){return Is(y,Oe,e.n)}function c(y){const{allowedPrivateKeyLengths:g,nByteLength:v,wrapPrivateKey:b,n:E}=e;if(g&&typeof y!="bigint"){if(Fr(y)&&(y=vn(y)),typeof y!="string"||!g.includes(y.length))throw new Error("invalid private key");y=y.padStart(v*2,"0")}let _;try{_=typeof y=="bigint"?y:Lr(nt("private key",y,v))}catch{throw new Error("invalid private key, expected hex or "+v+" bytes, got "+typeof y)}return b&&(_=Je(_,E)),Wt("private key",_,Oe,E),_}function u(y){if(!(y instanceof d))throw new Error("ProjectivePoint expected")}const l=Da((y,g)=>{const{px:v,py:b,pz:E}=y;if(r.eql(E,r.ONE))return{x:v,y:b};const _=y.is0();g==null&&(g=_?r.ONE:r.inv(E));const P=r.mul(v,g),$=r.mul(b,g),O=r.mul(E,g);if(_)return{x:r.ZERO,y:r.ZERO};if(!r.eql(O,r.ONE))throw new Error("invZ was invalid");return{x:P,y:$}}),h=Da(y=>{if(y.is0()){if(e.allowInfinityPoint&&!r.is0(y.py))return;throw new Error("bad point: ZERO")}const{x:g,y:v}=y.toAffine();if(!r.isValid(g)||!r.isValid(v))throw new Error("bad point: x or y not FE");const b=r.sqr(v),E=o(g);if(!r.eql(b,E))throw new Error("bad point: equation left != right");if(!y.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class d{constructor(g,v,b){if(this.px=g,this.py=v,this.pz=b,g==null||!r.isValid(g))throw new Error("x required");if(v==null||!r.isValid(v))throw new Error("y required");if(b==null||!r.isValid(b))throw new Error("z required");Object.freeze(this)}static fromAffine(g){const{x:v,y:b}=g||{};if(!g||!r.isValid(v)||!r.isValid(b))throw new Error("invalid affine point");if(g instanceof d)throw new Error("projective point not allowed");const E=_=>r.eql(_,r.ZERO);return E(v)&&E(b)?d.ZERO:new d(v,b,r.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(g){const v=r.invertBatch(g.map(b=>b.pz));return g.map((b,E)=>b.toAffine(v[E])).map(d.fromAffine)}static fromHex(g){const v=d.fromAffine(s(nt("pointHex",g)));return v.assertValidity(),v}static fromPrivateKey(g){return d.BASE.multiply(c(g))}static msm(g,v){return NI(d,n,g,v)}_setWindowSize(g){p.setWindowSize(this,g)}assertValidity(){h(this)}hasEvenY(){const{y:g}=this.toAffine();if(r.isOdd)return!r.isOdd(g);throw new Error("Field doesn't support isOdd")}equals(g){u(g);const{px:v,py:b,pz:E}=this,{px:_,py:P,pz:$}=g,O=r.eql(r.mul(v,$),r.mul(_,E)),C=r.eql(r.mul(b,$),r.mul(P,E));return O&&C}negate(){return new d(this.px,r.neg(this.py),this.pz)}double(){const{a:g,b:v}=e,b=r.mul(v,qd),{px:E,py:_,pz:P}=this;let $=r.ZERO,O=r.ZERO,C=r.ZERO,S=r.mul(E,E),k=r.mul(_,_),T=r.mul(P,P),R=r.mul(E,_);return R=r.add(R,R),C=r.mul(E,P),C=r.add(C,C),$=r.mul(g,C),O=r.mul(b,T),O=r.add($,O),$=r.sub(k,O),O=r.add(k,O),O=r.mul($,O),$=r.mul(R,$),C=r.mul(b,C),T=r.mul(g,T),R=r.sub(S,T),R=r.mul(g,R),R=r.add(R,C),C=r.add(S,S),S=r.add(C,S),S=r.add(S,T),S=r.mul(S,R),O=r.add(O,S),T=r.mul(_,P),T=r.add(T,T),S=r.mul(T,R),$=r.sub($,S),C=r.mul(T,k),C=r.add(C,C),C=r.add(C,C),new d($,O,C)}add(g){u(g);const{px:v,py:b,pz:E}=this,{px:_,py:P,pz:$}=g;let O=r.ZERO,C=r.ZERO,S=r.ZERO;const k=e.a,T=r.mul(e.b,qd);let R=r.mul(v,_),M=r.mul(b,P),D=r.mul(E,$),m=r.add(v,b),w=r.add(_,P);m=r.mul(m,w),w=r.add(R,M),m=r.sub(m,w),w=r.add(v,E);let I=r.add(_,$);return w=r.mul(w,I),I=r.add(R,D),w=r.sub(w,I),I=r.add(b,E),O=r.add(P,$),I=r.mul(I,O),O=r.add(M,D),I=r.sub(I,O),S=r.mul(k,w),O=r.mul(T,D),S=r.add(O,S),O=r.sub(M,S),S=r.add(M,S),C=r.mul(O,S),M=r.add(R,R),M=r.add(M,R),D=r.mul(k,D),w=r.mul(T,w),M=r.add(M,D),D=r.sub(R,D),D=r.mul(k,D),w=r.add(w,D),R=r.mul(M,w),C=r.add(C,R),R=r.mul(I,w),O=r.mul(m,O),O=r.sub(O,R),R=r.mul(m,M),S=r.mul(I,S),S=r.add(S,R),new d(O,C,S)}subtract(g){return this.add(g.negate())}is0(){return this.equals(d.ZERO)}wNAF(g){return p.wNAFCached(this,g,d.normalizeZ)}multiplyUnsafe(g){const{endo:v,n:b}=e;Wt("scalar",g,Yt,b);const E=d.ZERO;if(g===Yt)return E;if(this.is0()||g===Oe)return this;if(!v||p.hasPrecomputes(this))return p.wNAFCachedUnsafe(this,g,d.normalizeZ);let{k1neg:_,k1:P,k2neg:$,k2:O}=v.splitScalar(g),C=E,S=E,k=this;for(;P>Yt||O>Yt;)P&Oe&&(C=C.add(k)),O&Oe&&(S=S.add(k)),k=k.double(),P>>=Oe,O>>=Oe;return _&&(C=C.negate()),$&&(S=S.negate()),S=new d(r.mul(S.px,v.beta),S.py,S.pz),C.add(S)}multiply(g){const{endo:v,n:b}=e;Wt("scalar",g,Oe,b);let E,_;if(v){const{k1neg:P,k1:$,k2neg:O,k2:C}=v.splitScalar(g);let{p:S,f:k}=this.wNAF($),{p:T,f:R}=this.wNAF(C);S=p.constTimeNegate(P,S),T=p.constTimeNegate(O,T),T=new d(r.mul(T.px,v.beta),T.py,T.pz),E=S.add(T),_=k.add(R)}else{const{p:P,f:$}=this.wNAF(g);E=P,_=$}return d.normalizeZ([E,_])[0]}multiplyAndAddUnsafe(g,v,b){const E=d.BASE,_=($,O)=>O===Yt||O===Oe||!$.equals(E)?$.multiplyUnsafe(O):$.multiply(O),P=_(this,v).add(_(g,b));return P.is0()?void 0:P}toAffine(g){return l(this,g)}isTorsionFree(){const{h:g,isTorsionFree:v}=e;if(g===Oe)return!0;if(v)return v(d,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:g,clearCofactor:v}=e;return g===Oe?this:v?v(d,this):this.multiplyUnsafe(e.h)}toRawBytes(g=!0){return bn("isCompressed",g),this.assertValidity(),i(d,this,g)}toHex(g=!0){return bn("isCompressed",g),vn(this.toRawBytes(g))}}d.BASE=new d(e.Gx,e.Gy,r.ONE),d.ZERO=new d(r.ZERO,r.ONE,r.ZERO);const f=e.nBitLength,p=CI(d,e.endo?Math.ceil(f/2):f);return{CURVE:e,ProjectivePoint:d,normPrivateKeyToScalar:c,weierstrassEquation:o,isWithinCurveOrder:a}}function VI(t){const e=Ld(t);return Sn(e,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze(Vt({lowS:!0},e))}function KI(t){const e=VI(t),{Fp:r,n}=e,i=r.BYTES+1,s=2*r.BYTES+1;function o(D){return Je(D,n)}function a(D){return Aa(D,n)}const{ProjectivePoint:c,normPrivateKeyToScalar:u,weierstrassEquation:l,isWithinCurveOrder:h}=HI(ca(Vt({},e),{toBytes(D,m,w){const I=m.toAffine(),A=r.toBytes(I.x),x=yi;return bn("isCompressed",w),w?x(Uint8Array.from([m.hasEvenY()?2:3]),A):x(Uint8Array.from([4]),A,r.toBytes(I.y))},fromBytes(D){const m=D.length,w=D[0],I=D.subarray(1);if(m===i&&(w===2||w===3)){const A=Lr(I);if(!Is(A,Oe,r.ORDER))throw new Error("Point is not on curve");const x=l(A);let N;try{N=r.sqrt(x)}catch(j){const z=j instanceof Error?": "+j.message:"";throw new Error("Point is not on curve"+z)}const F=(N&Oe)===Oe;return(w&1)===1!==F&&(N=r.neg(N)),{x:A,y:N}}else if(m===s&&w===4){const A=r.fromBytes(I.subarray(0,r.BYTES)),x=r.fromBytes(I.subarray(r.BYTES,2*r.BYTES));return{x:A,y:x}}else{const A=i,x=s;throw new Error("invalid Point, expected length of "+A+", or uncompressed "+x+", got "+m)}}})),d=D=>vn(In(D,e.nByteLength));function f(D){const m=n>>Oe;return D>m}function p(D){return f(D)?o(-D):D}const y=(D,m,w)=>Lr(D.slice(m,w));class g{constructor(m,w,I){this.r=m,this.s=w,this.recovery=I,this.assertValidity()}static fromCompact(m){const w=e.nByteLength;return m=nt("compactSignature",m,w*2),new g(y(m,0,w),y(m,w,2*w))}static fromDER(m){const{r:w,s:I}=Gt.toSig(nt("DER",m));return new g(w,I)}assertValidity(){Wt("r",this.r,Oe,n),Wt("s",this.s,Oe,n)}addRecoveryBit(m){return new g(this.r,this.s,m)}recoverPublicKey(m){const{r:w,s:I,recovery:A}=this,x=$(nt("msgHash",m));if(A==null||![0,1,2,3].includes(A))throw new Error("recovery id invalid");const N=A===2||A===3?w+e.n:w;if(N>=r.ORDER)throw new Error("recovery id 2 or 3 invalid");const F=(A&1)===0?"02":"03",j=c.fromHex(F+d(N)),z=a(N),q=o(-x*z),H=o(I*z),V=c.BASE.multiplyAndAddUnsafe(j,q,H);if(!V)throw new Error("point at infinify");return V.assertValidity(),V}hasHighS(){return f(this.s)}normalizeS(){return this.hasHighS()?new g(this.r,o(-this.s),this.recovery):this}toDERRawBytes(){return _n(this.toDERHex())}toDERHex(){return Gt.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return _n(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const v={isValidPrivateKey(D){try{return u(D),!0}catch{return!1}},normPrivateKeyToScalar:u,randomPrivateKey:()=>{const D=Rd(e.n);return $I(e.randomBytes(D),e.n)},precompute(D=8,m=c.BASE){return m._setWindowSize(D),m.multiply(BigInt(3)),m}};function b(D,m=!0){return c.fromPrivateKey(D).toRawBytes(m)}function E(D){const m=Fr(D),w=typeof D=="string",I=(m||w)&&D.length;return m?I===i||I===s:w?I===2*i||I===2*s:D instanceof c}function _(D,m,w=!0){if(E(D))throw new Error("first arg must be private key");if(!E(m))throw new Error("second arg must be public key");return c.fromHex(m).multiply(u(D)).toRawBytes(w)}const P=e.bits2int||function(D){if(D.length>8192)throw new Error("input is too large");const m=Lr(D),w=D.length*8-e.nBitLength;return w>0?m>>BigInt(w):m},$=e.bits2int_modN||function(D){return o(P(D))},O=xa(e.nBitLength);function C(D){return Wt("num < 2^"+e.nBitLength,D,Yt,O),In(D,e.nByteLength)}function S(D,m,w=k){if(["recovered","canonical"].some(G=>G in w))throw new Error("sign() legacy options not supported");const{hash:I,randomBytes:A}=e;let{lowS:x,prehash:N,extraEntropy:F}=w;x==null&&(x=!0),D=nt("msgHash",D),jd(w),N&&(D=nt("prehashed msgHash",I(D)));const j=$(D),z=u(m),q=[C(z),C(j)];if(F!=null&&F!==!1){const G=F===!0?A(r.BYTES):F;q.push(nt("extraEntropy",G))}const H=yi(...q),V=j;function ee(G){const W=P(G);if(!h(W))return;const ge=a(W),le=c.BASE.multiply(W).toAffine(),he=o(le.x);if(he===Yt)return;const Pe=o(ge*o(V+he*z));if(Pe===Yt)return;let we=(le.x===he?0:2)|Number(le.y&Oe),er=Pe;return x&&f(Pe)&&(er=p(Pe),we^=1),new g(he,er,we)}return{seed:H,k2sig:ee}}const k={lowS:e.lowS,prehash:!1},T={lowS:e.lowS,prehash:!1};function R(D,m,w=k){const{seed:I,k2sig:A}=S(D,m,w),x=e;return Od(x.hash.outputLen,x.nByteLength,x.hmac)(I,A)}c.BASE._setWindowSize(8);function M(D,m,w,I=T){var A;const x=D;m=nt("msgHash",m),w=nt("publicKey",w);const{lowS:N,prehash:F,format:j}=I;if(jd(I),"strict"in I)throw new Error("options.strict was renamed to lowS");if(j!==void 0&&j!=="compact"&&j!=="der")throw new Error("format must be compact or der");const z=typeof x=="string"||Fr(x),q=!z&&!j&&typeof x=="object"&&x!==null&&typeof x.r=="bigint"&&typeof x.s=="bigint";if(!z&&!q)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let H,V;try{if(q&&(H=new g(x.r,x.s)),z){try{j!=="compact"&&(H=g.fromDER(x))}catch(we){if(!(we instanceof Gt.Err))throw we}!H&&j!=="der"&&(H=g.fromCompact(x))}V=c.fromHex(w)}catch{return!1}if(!H||N&&H.hasHighS())return!1;F&&(m=e.hash(m));const{r:ee,s:G}=H,W=$(m),ge=a(G),le=o(W*ge),he=o(ee*ge),Pe=(A=c.BASE.multiplyAndAddUnsafe(V,le,he))==null?void 0:A.toAffine();return Pe?o(Pe.x)===ee:!1}return{CURVE:e,getPublicKey:b,getSharedSecret:_,sign:R,verify:M,ProjectivePoint:c,Signature:g,utils:v}}function WI(t){return{hash:t,hmac:(e,...r)=>bs(t,e,Q2(...r)),randomBytes:mn}}function GI(t,e){const r=n=>KI(Vt(Vt({},t),WI(n)));return ca(Vt({},r(e)),{create:r})}const kd=Cd(BigInt("0xffffffff00000001000000000000000000000000ffffffffffffffffffffffff")),YI=kd.create(BigInt("-3")),ZI=BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"),JI=GI({a:YI,b:ZI,Fp:kd,n:BigInt("0xffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"),Gx:BigInt("0x6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"),Gy:BigInt("0x4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5"),h:BigInt(1),lowS:!1},fi),Md="base10",ze="base16",wt="base64pad",yr="base64url",mi="utf8",zd=0,Zt=1,wi=2,XI=0,Hd=1,bi=12,La=32;function QI(){const t=Fa.utils.randomPrivateKey(),e=Fa.getPublicKey(t);return{privateKey:Ze(t,ze),publicKey:Ze(e,ze)}}function Ua(){const t=mn(La);return Ze(t,ze)}function eS(t,e){const r=Fa.getSharedSecret(ct(t,ze),ct(e,ze)),n=iI(fi,r,void 0,void 0,La);return Ze(n,ze)}function xs(t){const e=fi(ct(t,ze));return Ze(e,ze)}function Ct(t){const e=fi(ct(t,mi));return Ze(e,ze)}function Vd(t){return ct(`${t}`,Md)}function jr(t){return Number(Ze(t,Md))}function Kd(t){return t.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function Wd(t){const e=t.replace(/-/g,"+").replace(/_/g,"/"),r=(4-e.length%4)%4;return e+"=".repeat(r)}function tS(t){const e=Vd(typeof t.type<"u"?t.type:zd);if(jr(e)===Zt&&typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");const r=typeof t.senderPublicKey<"u"?ct(t.senderPublicKey,ze):void 0,n=typeof t.iv<"u"?ct(t.iv,ze):mn(bi),i=ct(t.symKey,ze),s=vd(i,n).encrypt(ct(t.message,mi)),o=Gd({type:e,sealed:s,iv:n,senderPublicKey:r});return t.encoding===yr?Kd(o):o}function rS(t){const e=ct(t.symKey,ze),{sealed:r,iv:n}=vi({encoded:t.encoded,encoding:t.encoding}),i=vd(e,n).decrypt(r);if(i===null)throw new Error("Failed to decrypt");return Ze(i,mi)}function nS(t,e){const r=Vd(wi),n=mn(bi),i=ct(t,mi),s=Gd({type:r,sealed:i,iv:n});return e===yr?Kd(s):s}function iS(t,e){const{sealed:r}=vi({encoded:t,encoding:e});return Ze(r,mi)}function Gd(t){if(jr(t.type)===wi)return Ze(oi([t.type,t.sealed]),wt);if(jr(t.type)===Zt){if(typeof t.senderPublicKey>"u")throw new Error("Missing sender public key for type 1 envelope");return Ze(oi([t.type,t.senderPublicKey,t.iv,t.sealed]),wt)}return Ze(oi([t.type,t.iv,t.sealed]),wt)}function vi(t){const e=(t.encoding||wt)===yr?Wd(t.encoded):t.encoded,r=ct(e,wt),n=r.slice(XI,Hd),i=Hd;if(jr(n)===Zt){const c=i+La,u=c+bi,l=r.slice(i,c),h=r.slice(c,u),d=r.slice(u);return{type:n,sealed:d,iv:h,senderPublicKey:l}}if(jr(n)===wi){const c=r.slice(i),u=mn(bi);return{type:n,sealed:c,iv:u}}const s=i+bi,o=r.slice(i,s),a=r.slice(s);return{type:n,sealed:a,iv:o}}function sS(t,e){const r=vi({encoded:t,encoding:e?.encoding});return Yd({type:jr(r.type),senderPublicKey:typeof r.senderPublicKey<"u"?Ze(r.senderPublicKey,ze):void 0,receiverPublicKey:e?.receiverPublicKey})}function Yd(t){const e=t?.type||zd;if(e===Zt){if(typeof t?.senderPublicKey>"u")throw new Error("missing sender public key");if(typeof t?.receiverPublicKey>"u")throw new Error("missing receiver public key")}return{type:e,senderPublicKey:t?.senderPublicKey,receiverPublicKey:t?.receiverPublicKey}}function Zd(t){return t.type===Zt&&typeof t.senderPublicKey=="string"&&typeof t.receiverPublicKey=="string"}function Jd(t){return t.type===wi}function oS(t){const e=Buffer.from(t.x,"base64"),r=Buffer.from(t.y,"base64");return oi([new Uint8Array([4]),e,r])}function aS(t,e){const[r,n,i]=t.split("."),s=Buffer.from(Wd(i),"base64");if(s.length!==64)throw new Error("Invalid signature length");const o=s.slice(0,32),a=s.slice(32,64),c=`${r}.${n}`,u=fi(c),l=oS(e);if(!JI.verify(oi([o,a]),u,l))throw new Error("Invalid signature");return Oo(t).payload}const cS="irn";function Os(t){return t?.relay||{protocol:cS}}function Ei(t){const e=h2[t];if(typeof e>"u")throw new Error(`Relay Protocol not supported: ${t}`);return e}function uS(t,e="-"){const r={},n="relay"+e;return Object.keys(t).forEach(i=>{if(i.startsWith(n)){const s=i.replace(n,""),o=t[i];r[s]=o}}),r}function Xd(t){if(!t.includes("wc:")){const u=Vh(t);u!=null&&u.includes("wc:")&&(t=u)}t=t.includes("wc://")?t.replace("wc://",""):t,t=t.includes("wc:")?t.replace("wc:",""):t;const e=t.indexOf(":"),r=t.indexOf("?")!==-1?t.indexOf("?"):void 0,n=t.substring(0,e),i=t.substring(e+1,r).split("@"),s=typeof r<"u"?t.substring(r):"",o=new URLSearchParams(s),a={};o.forEach((u,l)=>{a[l]=u});const c=typeof a.methods=="string"?a.methods.split(","):void 0;return{protocol:n,topic:lS(i[0]),version:parseInt(i[1],10),symKey:a.symKey,relay:uS(a),methods:c,expiryTimestamp:a.expiryTimestamp?parseInt(a.expiryTimestamp,10):void 0}}function lS(t){return t.startsWith("//")?t.substring(2):t}function hS(t,e="-"){const r="relay",n={};return Object.keys(t).forEach(i=>{const s=i,o=r+e+s;t[s]&&(n[o]=t[s])}),n}function Qd(t){const e=new URLSearchParams,r=hS(t.relay);Object.keys(r).sort().forEach(i=>{e.set(i,r[i])}),e.set("symKey",t.symKey),t.expiryTimestamp&&e.set("expiryTimestamp",t.expiryTimestamp.toString()),t.methods&&e.set("methods",t.methods.join(","));const n=e.toString();return`${t.protocol}:${t.topic}@${t.version}?${n}`}function Ds(t,e,r){return`${t}?wc_ev=${r}&topic=${e}`}var dS=Object.defineProperty,fS=Object.defineProperties,pS=Object.getOwnPropertyDescriptors,ef=Object.getOwnPropertySymbols,gS=Object.prototype.hasOwnProperty,yS=Object.prototype.propertyIsEnumerable,tf=(t,e,r)=>e in t?dS(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,mS=(t,e)=>{for(var r in e||(e={}))gS.call(e,r)&&tf(t,r,e[r]);if(ef)for(var r of ef(e))yS.call(e,r)&&tf(t,r,e[r]);return t},wS=(t,e)=>fS(t,pS(e));function On(t){const e=[];return t.forEach(r=>{const[n,i]=r.split(":");e.push(`${n}:${i}`)}),e}function bS(t){const e=[];return Object.values(t).forEach(r=>{e.push(...On(r.accounts))}),e}function vS(t,e){const r=[];return Object.values(t).forEach(n=>{On(n.accounts).includes(e)&&r.push(...n.methods)}),r}function ES(t,e){const r=[];return Object.values(t).forEach(n=>{On(n.accounts).includes(e)&&r.push(...n.events)}),r}function $s(t){return t.includes(":")}function Dn(t){return $s(t)?t.split(":")[0]:t}function rf(t){var e,r,n;const i={};if(!wr(t))return i;for(const[s,o]of Object.entries(t)){const a=$s(s)?[s]:o.chains,c=o.methods||[],u=o.events||[],l=Dn(s);i[l]=wS(mS({},i[l]),{chains:At(a,(e=i[l])==null?void 0:e.chains),methods:At(c,(r=i[l])==null?void 0:r.methods),events:At(u,(n=i[l])==null?void 0:n.events)})}return i}function _S(t){const e={};return t?.forEach(r=>{var n;const[i,s]=r.split(":");e[i]||(e[i]={accounts:[],chains:[],events:[],methods:[]}),e[i].accounts.push(r),(n=e[i].chains)==null||n.push(`${i}:${s}`)}),e}function nf(t,e){e=e.map(n=>n.replace("did:pkh:",""));const r=_S(e);for(const[n,i]of Object.entries(r))i.methods?i.methods=At(i.methods,t):i.methods=t,i.events=["chainChanged","accountsChanged"];return r}function IS(t,e){var r,n,i,s,o,a;const c=rf(t),u=rf(e),l={},h=Object.keys(c).concat(Object.keys(u));for(const d of h)l[d]={chains:At((r=c[d])==null?void 0:r.chains,(n=u[d])==null?void 0:n.chains),methods:At((i=c[d])==null?void 0:i.methods,(s=u[d])==null?void 0:s.methods),events:At((o=c[d])==null?void 0:o.events,(a=u[d])==null?void 0:a.events)};return l}const SS={INVALID_METHOD:{message:"Invalid method.",code:1001},INVALID_EVENT:{message:"Invalid event.",code:1002},INVALID_UPDATE_REQUEST:{message:"Invalid update request.",code:1003},INVALID_EXTEND_REQUEST:{message:"Invalid extend request.",code:1004},INVALID_SESSION_SETTLE_REQUEST:{message:"Invalid session settle request.",code:1005},UNAUTHORIZED_METHOD:{message:"Unauthorized method.",code:3001},UNAUTHORIZED_EVENT:{message:"Unauthorized event.",code:3002},UNAUTHORIZED_UPDATE_REQUEST:{message:"Unauthorized update request.",code:3003},UNAUTHORIZED_EXTEND_REQUEST:{message:"Unauthorized extend request.",code:3004},USER_REJECTED:{message:"User rejected.",code:5e3},USER_REJECTED_CHAINS:{message:"User rejected chains.",code:5001},USER_REJECTED_METHODS:{message:"User rejected methods.",code:5002},USER_REJECTED_EVENTS:{message:"User rejected events.",code:5003},UNSUPPORTED_CHAINS:{message:"Unsupported chains.",code:5100},UNSUPPORTED_METHODS:{message:"Unsupported methods.",code:5101},UNSUPPORTED_EVENTS:{message:"Unsupported events.",code:5102},UNSUPPORTED_ACCOUNTS:{message:"Unsupported accounts.",code:5103},UNSUPPORTED_NAMESPACE_KEY:{message:"Unsupported namespace key.",code:5104},USER_DISCONNECTED:{message:"User disconnected.",code:6e3},SESSION_SETTLEMENT_FAILED:{message:"Session settlement failed.",code:7e3},WC_METHOD_UNSUPPORTED:{message:"Unsupported wc_ method.",code:10001}},xS={NOT_INITIALIZED:{message:"Not initialized.",code:1},NO_MATCHING_KEY:{message:"No matching key.",code:2},RESTORE_WILL_OVERRIDE:{message:"Restore will override.",code:3},RESUBSCRIBED:{message:"Resubscribed.",code:4},MISSING_OR_INVALID:{message:"Missing or invalid.",code:5},EXPIRED:{message:"Expired.",code:6},UNKNOWN_TYPE:{message:"Unknown type.",code:7},MISMATCHED_TOPIC:{message:"Mismatched topic.",code:8},NON_CONFORMING_NAMESPACES:{message:"Non conforming namespaces.",code:9}};function U(t,e){const{message:r,code:n}=xS[t];return{message:e?`${r} ${e}`:r,code:n}}function ie(t,e){const{message:r,code:n}=SS[t];return{message:e?`${r} ${e}`:r,code:n}}function mr(t,e){return Array.isArray(t)?typeof e<"u"&&t.length?t.every(e):!0:!1}function wr(t){return Object.getPrototypeOf(t)===Object.prototype&&Object.keys(t).length}function Te(t){return typeof t>"u"}function ye(t,e){return e&&Te(t)?!0:typeof t=="string"&&!!t.trim().length}function ja(t,e){return e&&Te(t)?!0:typeof t=="number"&&!isNaN(t)}function OS(t,e){const{requiredNamespaces:r}=e,n=Object.keys(t.namespaces),i=Object.keys(r);let s=!0;return Rr(i,n)?(n.forEach(o=>{const{accounts:a,methods:c,events:u}=t.namespaces[o],l=On(a),h=r[o];(!Rr(Th(o,h),l)||!Rr(h.methods,c)||!Rr(h.events,u))&&(s=!1)}),s):!1}function As(t){return ye(t,!1)&&t.includes(":")?t.split(":").length===2:!1}function DS(t){if(ye(t,!1)&&t.includes(":")){const e=t.split(":");if(e.length===3){const r=e[0]+":"+e[1];return!!e[2]&&As(r)}}return!1}function $S(t){function e(r){try{return typeof new URL(r)<"u"}catch{return!1}}try{if(ye(t,!1)){if(e(t))return!0;const r=Vh(t);return e(r)}}catch{}return!1}function AS(t){var e;return(e=t?.proposer)==null?void 0:e.publicKey}function PS(t){return t?.topic}function CS(t,e){let r=null;return ye(t?.publicKey,!1)||(r=U("MISSING_OR_INVALID",`${e} controller public key should be a string`)),r}function sf(t){let e=!0;return mr(t)?t.length&&(e=t.every(r=>ye(r,!1))):e=!1,e}function NS(t,e,r){let n=null;return mr(e)&&e.length?e.forEach(i=>{n||As(i)||(n=ie("UNSUPPORTED_CHAINS",`${r}, chain ${i} should be a string and conform to "namespace:chainId" format`))}):As(t)||(n=ie("UNSUPPORTED_CHAINS",`${r}, chains must be defined as "namespace:chainId" e.g. "eip155:1": {...} in the namespace key OR as an array of CAIP-2 chainIds e.g. eip155: { chains: ["eip155:1", "eip155:5"] }`)),n}function RS(t,e,r){let n=null;return Object.entries(t).forEach(([i,s])=>{if(n)return;const o=NS(i,Th(i,s),`${e} ${r}`);o&&(n=o)}),n}function TS(t,e){let r=null;return mr(t)?t.forEach(n=>{r||DS(n)||(r=ie("UNSUPPORTED_ACCOUNTS",`${e}, account ${n} should be a string and conform to "namespace:chainId:address" format`))}):r=ie("UNSUPPORTED_ACCOUNTS",`${e}, accounts should be an array of strings conforming to "namespace:chainId:address" format`),r}function BS(t,e){let r=null;return Object.values(t).forEach(n=>{if(r)return;const i=TS(n?.accounts,`${e} namespace`);i&&(r=i)}),r}function FS(t,e){let r=null;return sf(t?.methods)?sf(t?.events)||(r=ie("UNSUPPORTED_EVENTS",`${e}, events should be an array of strings or empty array for no events`)):r=ie("UNSUPPORTED_METHODS",`${e}, methods should be an array of strings or empty array for no methods`),r}function of(t,e){let r=null;return Object.values(t).forEach(n=>{if(r)return;const i=FS(n,`${e}, namespace`);i&&(r=i)}),r}function LS(t,e,r){let n=null;if(t&&wr(t)){const i=of(t,e);i&&(n=i);const s=RS(t,e,r);s&&(n=s)}else n=U("MISSING_OR_INVALID",`${e}, ${r} should be an object with data`);return n}function qa(t,e){let r=null;if(t&&wr(t)){const n=of(t,e);n&&(r=n);const i=BS(t,e);i&&(r=i)}else r=U("MISSING_OR_INVALID",`${e}, namespaces should be an object with data`);return r}function af(t){return ye(t.protocol,!0)}function US(t,e){let r=!1;return e&&!t?r=!0:t&&mr(t)&&t.length&&t.forEach(n=>{r=af(n)}),r}function jS(t){return typeof t=="number"}function Xe(t){return typeof t<"u"&&typeof t!==null}function qS(t){return!(!t||typeof t!="object"||!t.code||!ja(t.code,!1)||!t.message||!ye(t.message,!1))}function kS(t){return!(Te(t)||!ye(t.method,!1))}function MS(t){return!(Te(t)||Te(t.result)&&Te(t.error)||!ja(t.id,!1)||!ye(t.jsonrpc,!1))}function zS(t){return!(Te(t)||!ye(t.name,!1))}function cf(t,e){return!(!As(e)||!bS(t).includes(e))}function HS(t,e,r){return ye(r,!1)?vS(t,e).includes(r):!1}function VS(t,e,r){return ye(r,!1)?ES(t,e).includes(r):!1}function uf(t,e,r){let n=null;const i=KS(t),s=WS(e),o=Object.keys(i),a=Object.keys(s),c=lf(Object.keys(t)),u=lf(Object.keys(e)),l=c.filter(h=>!u.includes(h));return l.length&&(n=U("NON_CONFORMING_NAMESPACES",`${r} namespaces keys don't satisfy requiredNamespaces.
      Required: ${l.toString()}
      Received: ${Object.keys(e).toString()}`)),Rr(o,a)||(n=U("NON_CONFORMING_NAMESPACES",`${r} namespaces chains don't satisfy required namespaces.
      Required: ${o.toString()}
      Approved: ${a.toString()}`)),Object.keys(e).forEach(h=>{if(!h.includes(":")||n)return;const d=On(e[h].accounts);d.includes(h)||(n=U("NON_CONFORMING_NAMESPACES",`${r} namespaces accounts don't satisfy namespace accounts for ${h}
        Required: ${h}
        Approved: ${d.toString()}`))}),o.forEach(h=>{n||(Rr(i[h].methods,s[h].methods)?Rr(i[h].events,s[h].events)||(n=U("NON_CONFORMING_NAMESPACES",`${r} namespaces events don't satisfy namespace events for ${h}`)):n=U("NON_CONFORMING_NAMESPACES",`${r} namespaces methods don't satisfy namespace methods for ${h}`))}),n}function KS(t){const e={};return Object.keys(t).forEach(r=>{var n;r.includes(":")?e[r]=t[r]:(n=t[r].chains)==null||n.forEach(i=>{e[i]={methods:t[r].methods,events:t[r].events}})}),e}function lf(t){return[...new Set(t.map(e=>e.includes(":")?e.split(":")[0]:e))]}function WS(t){const e={};return Object.keys(t).forEach(r=>{if(r.includes(":"))e[r]=t[r];else{const n=On(t[r].accounts);n?.forEach(i=>{e[i]={accounts:t[r].accounts.filter(s=>s.includes(`${i}:`)),methods:t[r].methods,events:t[r].events}})}}),e}function GS(t,e){return ja(t,!1)&&t<=e.max&&t>=e.min}function hf(){const t=ci();return new Promise(e=>{switch(t){case tt.browser:e(YS());break;case tt.reactNative:e(ZS());break;case tt.node:e(JS());break;default:e(!0)}})}function YS(){return fn()&&navigator?.onLine}async function ZS(){if(lr()&&typeof global<"u"&&global!=null&&global.NetInfo){const t=await(global==null?void 0:global.NetInfo.fetch());return t?.isConnected}return!0}function JS(){return!0}function XS(t){switch(ci()){case tt.browser:QS(t);break;case tt.reactNative:ex(t);break}}function QS(t){!lr()&&fn()&&(window.addEventListener("online",()=>t(!0)),window.addEventListener("offline",()=>t(!1)))}function ex(t){lr()&&typeof global<"u"&&global!=null&&global.NetInfo&&global?.NetInfo.addEventListener(e=>t(e?.isConnected))}function tx(){var t;return fn()&&Ar()?((t=Ar())==null?void 0:t.visibilityState)==="visible":!0}const ka={};class _i{static get(e){return ka[e]}static set(e,r){ka[e]=r}static delete(e){delete ka[e]}}const rx="PARSE_ERROR",nx="INVALID_REQUEST",ix="METHOD_NOT_FOUND",sx="INVALID_PARAMS",df="INTERNAL_ERROR",Ma="SERVER_ERROR",ox=[-32700,-32600,-32601,-32602,-32603],Ii={[rx]:{code:-32700,message:"Parse error"},[nx]:{code:-32600,message:"Invalid Request"},[ix]:{code:-32601,message:"Method not found"},[sx]:{code:-32602,message:"Invalid params"},[df]:{code:-32603,message:"Internal error"},[Ma]:{code:-32e3,message:"Server error"}},ff=Ma;function ax(t){return ox.includes(t)}function pf(t){return Object.keys(Ii).includes(t)?Ii[t]:Ii[ff]}function cx(t){const e=Object.values(Ii).find(r=>r.code===t);return e||Ii[ff]}function gf(t,e,r){return t.message.includes("getaddrinfo ENOTFOUND")||t.message.includes("connect ECONNREFUSED")?new Error(`Unavailable ${r} RPC url at ${e}`):t}var yf={};/*! *****************************************************************************
	Copyright (c) Microsoft Corporation.

	Permission to use, copy, modify, and/or distribute this software for any
	purpose with or without fee is hereby granted.

	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
	REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
	AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
	INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
	LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
	OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
	PERFORMANCE OF THIS SOFTWARE.
	***************************************************************************** */var za=function(t,e){return za=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)n.hasOwnProperty(i)&&(r[i]=n[i])},za(t,e)};function ux(t,e){za(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}var Ha=function(){return Ha=Object.assign||function(e){for(var r,n=1,i=arguments.length;n<i;n++){r=arguments[n];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},Ha.apply(this,arguments)};function lx(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(t);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]]);return r}function hx(t,e,r,n){var i=arguments.length,s=i<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,r):n,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(t,e,r,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(i<3?o(s):i>3?o(e,r,s):o(e,r))||s);return i>3&&s&&Object.defineProperty(e,r,s),s}function dx(t,e){return function(r,n){e(r,n,t)}}function fx(t,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(t,e)}function px(t,e,r,n){function i(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function a(l){try{u(n.next(l))}catch(h){o(h)}}function c(l){try{u(n.throw(l))}catch(h){o(h)}}function u(l){l.done?s(l.value):i(l.value).then(a,c)}u((n=n.apply(t,e||[])).next())})}function gx(t,e){var r={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},n,i,s,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(u){return function(l){return c([u,l])}}function c(u){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,i&&(s=u[0]&2?i.return:u[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,u[1])).done)return s;switch(i=0,s&&(u=[u[0]&2,s.value]),u[0]){case 0:case 1:s=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,i=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(s=r.trys,!(s=s.length>0&&s[s.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!s||u[1]>s[0]&&u[1]<s[3])){r.label=u[1];break}if(u[0]===6&&r.label<s[1]){r.label=s[1],s=u;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(u);break}s[2]&&r.ops.pop(),r.trys.pop();continue}u=e.call(t,r)}catch(l){u=[6,l],i=0}finally{n=s=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}function yx(t,e,r,n){n===void 0&&(n=r),t[n]=e[r]}function mx(t,e){for(var r in t)r!=="default"&&!e.hasOwnProperty(r)&&(e[r]=t[r])}function Va(t){var e=typeof Symbol=="function"&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function mf(t,e){var r=typeof Symbol=="function"&&t[Symbol.iterator];if(!r)return t;var n=r.call(t),i,s=[],o;try{for(;(e===void 0||e-- >0)&&!(i=n.next()).done;)s.push(i.value)}catch(a){o={error:a}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(o)throw o.error}}return s}function wx(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(mf(arguments[e]));return t}function bx(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;for(var n=Array(t),i=0,e=0;e<r;e++)for(var s=arguments[e],o=0,a=s.length;o<a;o++,i++)n[i]=s[o];return n}function Si(t){return this instanceof Si?(this.v=t,this):new Si(t)}function vx(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(t,e||[]),i,s=[];return i={},o("next"),o("throw"),o("return"),i[Symbol.asyncIterator]=function(){return this},i;function o(d){n[d]&&(i[d]=function(f){return new Promise(function(p,y){s.push([d,f,p,y])>1||a(d,f)})})}function a(d,f){try{c(n[d](f))}catch(p){h(s[0][3],p)}}function c(d){d.value instanceof Si?Promise.resolve(d.value.v).then(u,l):h(s[0][2],d)}function u(d){a("next",d)}function l(d){a("throw",d)}function h(d,f){d(f),s.shift(),s.length&&a(s[0][0],s[0][1])}}function Ex(t){var e,r;return e={},n("next"),n("throw",function(i){throw i}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(i,s){e[i]=t[i]?function(o){return(r=!r)?{value:Si(t[i](o)),done:i==="return"}:s?s(o):o}:s}}function _x(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],r;return e?e.call(t):(t=typeof Va=="function"?Va(t):t[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(s){r[s]=t[s]&&function(o){return new Promise(function(a,c){o=t[s](o),i(a,c,o.done,o.value)})}}function i(s,o,a,c){Promise.resolve(c).then(function(u){s({value:u,done:a})},o)}}function Ix(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function Sx(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var r in t)Object.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e.default=t,e}function xx(t){return t&&t.__esModule?t:{default:t}}function Ox(t,e){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return e.get(t)}function Dx(t,e,r){if(!e.has(t))throw new TypeError("attempted to set private field on non-instance");return e.set(t,r),r}var $x=Object.freeze({__proto__:null,__extends:ux,get __assign(){return Ha},__rest:lx,__decorate:hx,__param:dx,__metadata:fx,__awaiter:px,__generator:gx,__createBinding:yx,__exportStar:mx,__values:Va,__read:mf,__spread:wx,__spreadArrays:bx,__await:Si,__asyncGenerator:vx,__asyncDelegator:Ex,__asyncValues:_x,__makeTemplateObject:Ix,__importStar:Sx,__importDefault:xx,__classPrivateFieldGet:Ox,__classPrivateFieldSet:Dx}),Ax=Ic($x),Jt={},wf;function Px(){if(wf)return Jt;wf=1,Object.defineProperty(Jt,"__esModule",{value:!0}),Jt.isBrowserCryptoAvailable=Jt.getSubtleCrypto=Jt.getBrowerCrypto=void 0;function t(){return Ke?.crypto||Ke?.msCrypto||{}}Jt.getBrowerCrypto=t;function e(){const n=t();return n.subtle||n.webkitSubtle}Jt.getSubtleCrypto=e;function r(){return!!t()&&!!e()}return Jt.isBrowserCryptoAvailable=r,Jt}var Xt={},bf;function Cx(){if(bf)return Xt;bf=1,Object.defineProperty(Xt,"__esModule",{value:!0}),Xt.isBrowser=Xt.isNode=Xt.isReactNative=void 0;function t(){return typeof document>"u"&&typeof navigator<"u"&&navigator.product==="ReactNative"}Xt.isReactNative=t;function e(){return typeof process<"u"&&typeof process.versions<"u"&&typeof process.versions.node<"u"}Xt.isNode=e;function r(){return!t()&&!e()}return Xt.isBrowser=r,Xt}(function(t){Object.defineProperty(t,"__esModule",{value:!0});const e=Ax;e.__exportStar(Px(),t),e.__exportStar(Cx(),t)})(yf);function Nt(t=3){const e=Date.now()*Math.pow(10,t),r=Math.floor(Math.random()*Math.pow(10,t));return e+r}function qr(t=6){return BigInt(Nt(t))}function br(t,e,r){return{id:r||Nt(),jsonrpc:"2.0",method:t,params:e}}function Ps(t,e){return{id:t,jsonrpc:"2.0",result:e}}function Cs(t,e,r){return{id:t,jsonrpc:"2.0",error:Nx(e,r)}}function Nx(t,e){return typeof t>"u"?pf(df):(typeof t=="string"&&(t=Object.assign(Object.assign({},pf(Ma)),{message:t})),typeof e<"u"&&(t.data=e),ax(t.code)&&(t=cx(t.code)),t)}class Rx{}class Tx extends Rx{constructor(){super()}}class Bx extends Tx{constructor(e){super()}}const Fx="^https?:",Lx="^wss?:";function Ux(t){const e=t.match(new RegExp(/^\w+:/,"gi"));if(!(!e||!e.length))return e[0]}function vf(t,e){const r=Ux(t);return typeof r>"u"?!1:new RegExp(e).test(r)}function Ef(t){return vf(t,Fx)}function _f(t){return vf(t,Lx)}function jx(t){return new RegExp("wss?://localhost(:d{2,5})?").test(t)}function If(t){return typeof t=="object"&&"id"in t&&"jsonrpc"in t&&t.jsonrpc==="2.0"}function Ka(t){return If(t)&&"method"in t}function Ns(t){return If(t)&&(Rt(t)||ut(t))}function Rt(t){return"result"in t}function ut(t){return"error"in t}class lt extends Bx{constructor(e){super(e),this.events=new Ue.exports.EventEmitter,this.hasRegisteredEventListeners=!1,this.connection=this.setConnection(e),this.connection.connected&&this.registerEventListeners()}async connect(e=this.connection){await this.open(e)}async disconnect(){await this.close()}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async request(e,r){return this.requestStrict(br(e.method,e.params||[],e.id||qr().toString()),r)}async requestStrict(e,r){return new Promise(async(n,i)=>{if(!this.connection.connected)try{await this.open()}catch(s){i(s)}this.events.on(`${e.id}`,s=>{ut(s)?i(s.error):n(s.result)});try{await this.connection.send(e,r)}catch(s){i(s)}})}setConnection(e=this.connection){return e}onPayload(e){this.events.emit("payload",e),Ns(e)?this.events.emit(`${e.id}`,e):this.events.emit("message",{type:e.method,data:e.params})}onClose(e){e&&e.code===3e3&&this.events.emit("error",new Error(`WebSocket connection closed abnormally with code: ${e.code} ${e.reason?`(${e.reason})`:""}`)),this.events.emit("disconnect")}async open(e=this.connection){this.connection===e&&this.connection.connected||(this.connection.connected&&this.close(),typeof e=="string"&&(await this.connection.open(e),e=this.connection),this.connection=this.setConnection(e),await this.connection.open(),this.registerEventListeners(),this.events.emit("connect"))}async close(){await this.connection.close()}registerEventListeners(){this.hasRegisteredEventListeners||(this.connection.on("payload",e=>this.onPayload(e)),this.connection.on("close",e=>this.onClose(e)),this.connection.on("error",e=>this.events.emit("error",e)),this.connection.on("register_error",e=>this.onClose()),this.hasRegisteredEventListeners=!0)}}const qx=()=>typeof WebSocket<"u"?WebSocket:typeof global<"u"&&typeof global.WebSocket<"u"?global.WebSocket:typeof window<"u"&&typeof window.WebSocket<"u"?window.WebSocket:typeof self<"u"&&typeof self.WebSocket<"u"?self.WebSocket:require("ws"),kx=()=>typeof WebSocket<"u"||typeof global<"u"&&typeof global.WebSocket<"u"||typeof window<"u"&&typeof window.WebSocket<"u"||typeof self<"u"&&typeof self.WebSocket<"u",Sf=t=>t.split("?")[0],xf=10,Mx=qx();class zx{constructor(e){if(this.url=e,this.events=new Ue.exports.EventEmitter,this.registering=!1,!_f(e))throw new Error(`Provided URL is not compatible with WebSocket connection: ${e}`);this.url=e}get connected(){return typeof this.socket<"u"}get connecting(){return this.registering}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async open(e=this.url){await this.register(e)}async close(){return new Promise((e,r)=>{if(typeof this.socket>"u"){r(new Error("Connection already closed"));return}this.socket.onclose=n=>{this.onClose(n),e()},this.socket.close()})}async send(e){typeof this.socket>"u"&&(this.socket=await this.register());try{this.socket.send(Ut(e))}catch(r){this.onError(e.id,r)}}register(e=this.url){if(!_f(e))throw new Error(`Provided URL is not compatible with WebSocket connection: ${e}`);if(this.registering){const r=this.events.getMaxListeners();return(this.events.listenerCount("register_error")>=r||this.events.listenerCount("open")>=r)&&this.events.setMaxListeners(r+1),new Promise((n,i)=>{this.events.once("register_error",s=>{this.resetMaxListeners(),i(s)}),this.events.once("open",()=>{if(this.resetMaxListeners(),typeof this.socket>"u")return i(new Error("WebSocket connection is missing or invalid"));n(this.socket)})})}return this.url=e,this.registering=!0,new Promise((r,n)=>{const i=yf.isReactNative()?void 0:{rejectUnauthorized:!jx(e)},s=new Mx(e,[],i);kx()?s.onerror=o=>{const a=o;n(this.emitError(a.error))}:s.on("error",o=>{n(this.emitError(o))}),s.onopen=()=>{this.onOpen(s),r(s)}})}onOpen(e){e.onmessage=r=>this.onPayload(r),e.onclose=r=>this.onClose(r),this.socket=e,this.registering=!1,this.events.emit("open")}onClose(e){this.socket=void 0,this.registering=!1,this.events.emit("close",e)}onPayload(e){if(typeof e.data>"u")return;const r=typeof e.data=="string"?Or(e.data):e.data;this.events.emit("payload",r)}onError(e,r){const n=this.parseError(r),i=n.message||n.toString(),s=Cs(e,i);this.events.emit("payload",s)}parseError(e,r=this.url){return gf(e,Sf(r),"WS")}resetMaxListeners(){this.events.getMaxListeners()>xf&&this.events.setMaxListeners(xf)}emitError(e){const r=this.parseError(new Error(e?.message||`WebSocket connection failed for host: ${Sf(this.url)}`));return this.events.emit("register_error",r),r}}var Hx=Object.defineProperty,Of=Object.getOwnPropertySymbols,Vx=Object.prototype.hasOwnProperty,Kx=Object.prototype.propertyIsEnumerable,Df=(t,e,r)=>e in t?Hx(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,He=(t,e)=>{for(var r in e||(e={}))Vx.call(e,r)&&Df(t,r,e[r]);if(Of)for(var r of Of(e))Kx.call(e,r)&&Df(t,r,e[r]);return t};const $f="wc",Af=2,Rs="core",Tt=`${$f}@2:${Rs}:`,Wx={name:Rs,logger:"error"},Gx={database:":memory:"},Yx="crypto",Pf="client_ed25519_seed",Zx=L.ONE_DAY,Jx="keychain",Xx="0.3",Qx="messages",eO="0.3",Cf=L.SIX_HOURS,tO="publisher",Nf="irn",rO="error",Rf="wss://relay.walletconnect.org",nO="relayer",De={message:"relayer_message",message_ack:"relayer_message_ack",connect:"relayer_connect",disconnect:"relayer_disconnect",error:"relayer_error",connection_stalled:"relayer_connection_stalled",transport_closed:"relayer_transport_closed",publish:"relayer_publish"},iO="_subscription",ht={payload:"payload",connect:"connect",disconnect:"disconnect",error:"error"},sO=.1,Wa="2.21.1",ue={link_mode:"link_mode",relay:"relay"},Ts={inbound:"inbound",outbound:"outbound"},oO="0.3",aO="WALLETCONNECT_CLIENT_ID",Tf="WALLETCONNECT_LINK_MODE_APPS",it={created:"subscription_created",deleted:"subscription_deleted",expired:"subscription_expired",disabled:"subscription_disabled",sync:"subscription_sync",resubscribed:"subscription_resubscribed"},cO="subscription",uO="0.3",lO="pairing",hO="0.3",xi={wc_pairingDelete:{req:{ttl:L.ONE_DAY,prompt:!1,tag:1e3},res:{ttl:L.ONE_DAY,prompt:!1,tag:1001}},wc_pairingPing:{req:{ttl:L.THIRTY_SECONDS,prompt:!1,tag:1002},res:{ttl:L.THIRTY_SECONDS,prompt:!1,tag:1003}},unregistered_method:{req:{ttl:L.ONE_DAY,prompt:!1,tag:0},res:{ttl:L.ONE_DAY,prompt:!1,tag:0}}},kr={create:"pairing_create",expire:"pairing_expire",delete:"pairing_delete",ping:"pairing_ping"},bt={created:"history_created",updated:"history_updated",deleted:"history_deleted",sync:"history_sync"},dO="history",fO="0.3",pO="expirer",dt={created:"expirer_created",deleted:"expirer_deleted",expired:"expirer_expired",sync:"expirer_sync"},gO="0.3",yO="verify-api",mO="https://verify.walletconnect.com",Bf="https://verify.walletconnect.org",Oi=Bf,wO=`${Oi}/v3`,bO=[mO,Bf],vO="echo",EO="https://echo.walletconnect.com",Bt={pairing_started:"pairing_started",pairing_uri_validation_success:"pairing_uri_validation_success",pairing_uri_not_expired:"pairing_uri_not_expired",store_new_pairing:"store_new_pairing",subscribing_pairing_topic:"subscribing_pairing_topic",subscribe_pairing_topic_success:"subscribe_pairing_topic_success",existing_pairing:"existing_pairing",pairing_not_expired:"pairing_not_expired",emit_inactive_pairing:"emit_inactive_pairing",emit_session_proposal:"emit_session_proposal",subscribing_to_pairing_topic:"subscribing_to_pairing_topic"},Qt={no_wss_connection:"no_wss_connection",no_internet_connection:"no_internet_connection",malformed_pairing_uri:"malformed_pairing_uri",active_pairing_already_exists:"active_pairing_already_exists",subscribe_pairing_topic_failure:"subscribe_pairing_topic_failure",pairing_expired:"pairing_expired",proposal_expired:"proposal_expired",proposal_listener_not_found:"proposal_listener_not_found"},vt={session_approve_started:"session_approve_started",proposal_not_expired:"proposal_not_expired",session_namespaces_validation_success:"session_namespaces_validation_success",create_session_topic:"create_session_topic",subscribing_session_topic:"subscribing_session_topic",subscribe_session_topic_success:"subscribe_session_topic_success",publishing_session_approve:"publishing_session_approve",session_approve_publish_success:"session_approve_publish_success",store_session:"store_session",publishing_session_settle:"publishing_session_settle",session_settle_publish_success:"session_settle_publish_success"},Mr={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",proposal_expired:"proposal_expired",subscribe_session_topic_failure:"subscribe_session_topic_failure",session_approve_publish_failure:"session_approve_publish_failure",session_settle_publish_failure:"session_settle_publish_failure",session_approve_namespace_validation_failure:"session_approve_namespace_validation_failure",proposal_not_found:"proposal_not_found"},zr={authenticated_session_approve_started:"authenticated_session_approve_started",authenticated_session_not_expired:"authenticated_session_not_expired",chains_caip2_compliant:"chains_caip2_compliant",chains_evm_compliant:"chains_evm_compliant",create_authenticated_session_topic:"create_authenticated_session_topic",cacaos_verified:"cacaos_verified",store_authenticated_session:"store_authenticated_session",subscribing_authenticated_session_topic:"subscribing_authenticated_session_topic",subscribe_authenticated_session_topic_success:"subscribe_authenticated_session_topic_success",publishing_authenticated_session_approve:"publishing_authenticated_session_approve",authenticated_session_approve_publish_success:"authenticated_session_approve_publish_success"},Di={no_internet_connection:"no_internet_connection",no_wss_connection:"no_wss_connection",missing_session_authenticate_request:"missing_session_authenticate_request",session_authenticate_request_expired:"session_authenticate_request_expired",chains_caip2_compliant_failure:"chains_caip2_compliant_failure",chains_evm_compliant_failure:"chains_evm_compliant_failure",invalid_cacao:"invalid_cacao",subscribe_authenticated_session_topic_failure:"subscribe_authenticated_session_topic_failure",authenticated_session_approve_publish_failure:"authenticated_session_approve_publish_failure",authenticated_session_pending_request_not_found:"authenticated_session_pending_request_not_found"},_O=.1,IO="event-client",SO=86400,xO="https://pulse.walletconnect.org/batch";function OO(t,e){if(t.length>=255)throw new TypeError("Alphabet too long");for(var r=new Uint8Array(256),n=0;n<r.length;n++)r[n]=255;for(var i=0;i<t.length;i++){var s=t.charAt(i),o=s.charCodeAt(0);if(r[o]!==255)throw new TypeError(s+" is ambiguous");r[o]=i}var a=t.length,c=t.charAt(0),u=Math.log(a)/Math.log(256),l=Math.log(256)/Math.log(a);function h(p){if(p instanceof Uint8Array||(ArrayBuffer.isView(p)?p=new Uint8Array(p.buffer,p.byteOffset,p.byteLength):Array.isArray(p)&&(p=Uint8Array.from(p))),!(p instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(p.length===0)return"";for(var y=0,g=0,v=0,b=p.length;v!==b&&p[v]===0;)v++,y++;for(var E=(b-v)*l+1>>>0,_=new Uint8Array(E);v!==b;){for(var P=p[v],$=0,O=E-1;(P!==0||$<g)&&O!==-1;O--,$++)P+=256*_[O]>>>0,_[O]=P%a>>>0,P=P/a>>>0;if(P!==0)throw new Error("Non-zero carry");g=$,v++}for(var C=E-g;C!==E&&_[C]===0;)C++;for(var S=c.repeat(y);C<E;++C)S+=t.charAt(_[C]);return S}function d(p){if(typeof p!="string")throw new TypeError("Expected String");if(p.length===0)return new Uint8Array;var y=0;if(p[y]!==" "){for(var g=0,v=0;p[y]===c;)g++,y++;for(var b=(p.length-y)*u+1>>>0,E=new Uint8Array(b);p[y];){var _=r[p.charCodeAt(y)];if(_===255)return;for(var P=0,$=b-1;(_!==0||P<v)&&$!==-1;$--,P++)_+=a*E[$]>>>0,E[$]=_%256>>>0,_=_/256>>>0;if(_!==0)throw new Error("Non-zero carry");v=P,y++}if(p[y]!==" "){for(var O=b-v;O!==b&&E[O]===0;)O++;for(var C=new Uint8Array(g+(b-O)),S=g;O!==b;)C[S++]=E[O++];return C}}}function f(p){var y=d(p);if(y)return y;throw new Error(`Non-${e} character`)}return{encode:h,decodeUnsafe:d,decode:f}}var DO=OO,$O=DO;const Ff=t=>{if(t instanceof Uint8Array&&t.constructor.name==="Uint8Array")return t;if(t instanceof ArrayBuffer)return new Uint8Array(t);if(ArrayBuffer.isView(t))return new Uint8Array(t.buffer,t.byteOffset,t.byteLength);throw new Error("Unknown type, must be binary type")},AO=t=>new TextEncoder().encode(t),PO=t=>new TextDecoder().decode(t);class CO{constructor(e,r,n){this.name=e,this.prefix=r,this.baseEncode=n}encode(e){if(e instanceof Uint8Array)return`${this.prefix}${this.baseEncode(e)}`;throw Error("Unknown type, must be binary type")}}class NO{constructor(e,r,n){if(this.name=e,this.prefix=r,r.codePointAt(0)===void 0)throw new Error("Invalid prefix character");this.prefixCodePoint=r.codePointAt(0),this.baseDecode=n}decode(e){if(typeof e=="string"){if(e.codePointAt(0)!==this.prefixCodePoint)throw Error(`Unable to decode multibase string ${JSON.stringify(e)}, ${this.name} decoder only supports inputs prefixed with ${this.prefix}`);return this.baseDecode(e.slice(this.prefix.length))}else throw Error("Can only multibase decode strings")}or(e){return Lf(this,e)}}class RO{constructor(e){this.decoders=e}or(e){return Lf(this,e)}decode(e){const r=e[0],n=this.decoders[r];if(n)return n.decode(e);throw RangeError(`Unable to decode multibase string ${JSON.stringify(e)}, only inputs prefixed with ${Object.keys(this.decoders)} are supported`)}}const Lf=(t,e)=>new RO(He(He({},t.decoders||{[t.prefix]:t}),e.decoders||{[e.prefix]:e}));class TO{constructor(e,r,n,i){this.name=e,this.prefix=r,this.baseEncode=n,this.baseDecode=i,this.encoder=new CO(e,r,n),this.decoder=new NO(e,r,i)}encode(e){return this.encoder.encode(e)}decode(e){return this.decoder.decode(e)}}const Bs=({name:t,prefix:e,encode:r,decode:n})=>new TO(t,e,r,n),$i=({prefix:t,name:e,alphabet:r})=>{const{encode:n,decode:i}=$O(r,e);return Bs({prefix:t,name:e,encode:n,decode:s=>Ff(i(s))})},BO=(t,e,r,n)=>{const i={};for(let l=0;l<e.length;++l)i[e[l]]=l;let s=t.length;for(;t[s-1]==="=";)--s;const o=new Uint8Array(s*r/8|0);let a=0,c=0,u=0;for(let l=0;l<s;++l){const h=i[t[l]];if(h===void 0)throw new SyntaxError(`Non-${n} character`);c=c<<r|h,a+=r,a>=8&&(a-=8,o[u++]=255&c>>a)}if(a>=r||255&c<<8-a)throw new SyntaxError("Unexpected end of data");return o},FO=(t,e,r)=>{const n=e[e.length-1]==="=",i=(1<<r)-1;let s="",o=0,a=0;for(let c=0;c<t.length;++c)for(a=a<<8|t[c],o+=8;o>r;)o-=r,s+=e[i&a>>o];if(o&&(s+=e[i&a<<r-o]),n)for(;s.length*r&7;)s+="=";return s},Be=({name:t,prefix:e,bitsPerChar:r,alphabet:n})=>Bs({prefix:e,name:t,encode(i){return FO(i,n,r)},decode(i){return BO(i,n,r,t)}}),LO=Bs({prefix:"\0",name:"identity",encode:t=>PO(t),decode:t=>AO(t)});var UO=Object.freeze({__proto__:null,identity:LO});const jO=Be({prefix:"0",name:"base2",alphabet:"01",bitsPerChar:1});var qO=Object.freeze({__proto__:null,base2:jO});const kO=Be({prefix:"7",name:"base8",alphabet:"01234567",bitsPerChar:3});var MO=Object.freeze({__proto__:null,base8:kO});const zO=$i({prefix:"9",name:"base10",alphabet:"0123456789"});var HO=Object.freeze({__proto__:null,base10:zO});const VO=Be({prefix:"f",name:"base16",alphabet:"0123456789abcdef",bitsPerChar:4}),KO=Be({prefix:"F",name:"base16upper",alphabet:"0123456789ABCDEF",bitsPerChar:4});var WO=Object.freeze({__proto__:null,base16:VO,base16upper:KO});const GO=Be({prefix:"b",name:"base32",alphabet:"abcdefghijklmnopqrstuvwxyz234567",bitsPerChar:5}),YO=Be({prefix:"B",name:"base32upper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",bitsPerChar:5}),ZO=Be({prefix:"c",name:"base32pad",alphabet:"abcdefghijklmnopqrstuvwxyz234567=",bitsPerChar:5}),JO=Be({prefix:"C",name:"base32padupper",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=",bitsPerChar:5}),XO=Be({prefix:"v",name:"base32hex",alphabet:"0123456789abcdefghijklmnopqrstuv",bitsPerChar:5}),QO=Be({prefix:"V",name:"base32hexupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV",bitsPerChar:5}),e3=Be({prefix:"t",name:"base32hexpad",alphabet:"0123456789abcdefghijklmnopqrstuv=",bitsPerChar:5}),t3=Be({prefix:"T",name:"base32hexpadupper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUV=",bitsPerChar:5}),r3=Be({prefix:"h",name:"base32z",alphabet:"ybndrfg8ejkmcpqxot1uwisza345h769",bitsPerChar:5});var n3=Object.freeze({__proto__:null,base32:GO,base32upper:YO,base32pad:ZO,base32padupper:JO,base32hex:XO,base32hexupper:QO,base32hexpad:e3,base32hexpadupper:t3,base32z:r3});const i3=$i({prefix:"k",name:"base36",alphabet:"0123456789abcdefghijklmnopqrstuvwxyz"}),s3=$i({prefix:"K",name:"base36upper",alphabet:"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"});var o3=Object.freeze({__proto__:null,base36:i3,base36upper:s3});const a3=$i({name:"base58btc",prefix:"z",alphabet:"**********************************************************"}),c3=$i({name:"base58flickr",prefix:"Z",alphabet:"**********************************************************"});var u3=Object.freeze({__proto__:null,base58btc:a3,base58flickr:c3});const l3=Be({prefix:"m",name:"base64",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",bitsPerChar:6}),h3=Be({prefix:"M",name:"base64pad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",bitsPerChar:6}),d3=Be({prefix:"u",name:"base64url",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bitsPerChar:6}),f3=Be({prefix:"U",name:"base64urlpad",alphabet:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=",bitsPerChar:6});var p3=Object.freeze({__proto__:null,base64:l3,base64pad:h3,base64url:d3,base64urlpad:f3});const Uf=Array.from("\u{1F680}\u{1FA90}\u2604\u{1F6F0}\u{1F30C}\u{1F311}\u{1F312}\u{1F313}\u{1F314}\u{1F315}\u{1F316}\u{1F317}\u{1F318}\u{1F30D}\u{1F30F}\u{1F30E}\u{1F409}\u2600\u{1F4BB}\u{1F5A5}\u{1F4BE}\u{1F4BF}\u{1F602}\u2764\u{1F60D}\u{1F923}\u{1F60A}\u{1F64F}\u{1F495}\u{1F62D}\u{1F618}\u{1F44D}\u{1F605}\u{1F44F}\u{1F601}\u{1F525}\u{1F970}\u{1F494}\u{1F496}\u{1F499}\u{1F622}\u{1F914}\u{1F606}\u{1F644}\u{1F4AA}\u{1F609}\u263A\u{1F44C}\u{1F917}\u{1F49C}\u{1F614}\u{1F60E}\u{1F607}\u{1F339}\u{1F926}\u{1F389}\u{1F49E}\u270C\u2728\u{1F937}\u{1F631}\u{1F60C}\u{1F338}\u{1F64C}\u{1F60B}\u{1F497}\u{1F49A}\u{1F60F}\u{1F49B}\u{1F642}\u{1F493}\u{1F929}\u{1F604}\u{1F600}\u{1F5A4}\u{1F603}\u{1F4AF}\u{1F648}\u{1F447}\u{1F3B6}\u{1F612}\u{1F92D}\u2763\u{1F61C}\u{1F48B}\u{1F440}\u{1F62A}\u{1F611}\u{1F4A5}\u{1F64B}\u{1F61E}\u{1F629}\u{1F621}\u{1F92A}\u{1F44A}\u{1F973}\u{1F625}\u{1F924}\u{1F449}\u{1F483}\u{1F633}\u270B\u{1F61A}\u{1F61D}\u{1F634}\u{1F31F}\u{1F62C}\u{1F643}\u{1F340}\u{1F337}\u{1F63B}\u{1F613}\u2B50\u2705\u{1F97A}\u{1F308}\u{1F608}\u{1F918}\u{1F4A6}\u2714\u{1F623}\u{1F3C3}\u{1F490}\u2639\u{1F38A}\u{1F498}\u{1F620}\u261D\u{1F615}\u{1F33A}\u{1F382}\u{1F33B}\u{1F610}\u{1F595}\u{1F49D}\u{1F64A}\u{1F639}\u{1F5E3}\u{1F4AB}\u{1F480}\u{1F451}\u{1F3B5}\u{1F91E}\u{1F61B}\u{1F534}\u{1F624}\u{1F33C}\u{1F62B}\u26BD\u{1F919}\u2615\u{1F3C6}\u{1F92B}\u{1F448}\u{1F62E}\u{1F646}\u{1F37B}\u{1F343}\u{1F436}\u{1F481}\u{1F632}\u{1F33F}\u{1F9E1}\u{1F381}\u26A1\u{1F31E}\u{1F388}\u274C\u270A\u{1F44B}\u{1F630}\u{1F928}\u{1F636}\u{1F91D}\u{1F6B6}\u{1F4B0}\u{1F353}\u{1F4A2}\u{1F91F}\u{1F641}\u{1F6A8}\u{1F4A8}\u{1F92C}\u2708\u{1F380}\u{1F37A}\u{1F913}\u{1F619}\u{1F49F}\u{1F331}\u{1F616}\u{1F476}\u{1F974}\u25B6\u27A1\u2753\u{1F48E}\u{1F4B8}\u2B07\u{1F628}\u{1F31A}\u{1F98B}\u{1F637}\u{1F57A}\u26A0\u{1F645}\u{1F61F}\u{1F635}\u{1F44E}\u{1F932}\u{1F920}\u{1F927}\u{1F4CC}\u{1F535}\u{1F485}\u{1F9D0}\u{1F43E}\u{1F352}\u{1F617}\u{1F911}\u{1F30A}\u{1F92F}\u{1F437}\u260E\u{1F4A7}\u{1F62F}\u{1F486}\u{1F446}\u{1F3A4}\u{1F647}\u{1F351}\u2744\u{1F334}\u{1F4A3}\u{1F438}\u{1F48C}\u{1F4CD}\u{1F940}\u{1F922}\u{1F445}\u{1F4A1}\u{1F4A9}\u{1F450}\u{1F4F8}\u{1F47B}\u{1F910}\u{1F92E}\u{1F3BC}\u{1F975}\u{1F6A9}\u{1F34E}\u{1F34A}\u{1F47C}\u{1F48D}\u{1F4E3}\u{1F942}"),g3=Uf.reduce((t,e,r)=>(t[r]=e,t),[]),y3=Uf.reduce((t,e,r)=>(t[e.codePointAt(0)]=r,t),[]);function m3(t){return t.reduce((e,r)=>(e+=g3[r],e),"")}function w3(t){const e=[];for(const r of t){const n=y3[r.codePointAt(0)];if(n===void 0)throw new Error(`Non-base256emoji character: ${r}`);e.push(n)}return new Uint8Array(e)}const b3=Bs({prefix:"\u{1F680}",name:"base256emoji",encode:m3,decode:w3});var v3=Object.freeze({__proto__:null,base256emoji:b3}),E3=qf,jf=128,_3=127,I3=~_3,S3=Math.pow(2,31);function qf(t,e,r){e=e||[],r=r||0;for(var n=r;t>=S3;)e[r++]=t&255|jf,t/=128;for(;t&I3;)e[r++]=t&255|jf,t>>>=7;return e[r]=t|0,qf.bytes=r-n+1,e}var x3=Ga,O3=128,kf=127;function Ga(t,n){var r=0,n=n||0,i=0,s=n,o,a=t.length;do{if(s>=a)throw Ga.bytes=0,new RangeError("Could not decode varint");o=t[s++],r+=i<28?(o&kf)<<i:(o&kf)*Math.pow(2,i),i+=7}while(o>=O3);return Ga.bytes=s-n,r}var D3=Math.pow(2,7),$3=Math.pow(2,14),A3=Math.pow(2,21),P3=Math.pow(2,28),C3=Math.pow(2,35),N3=Math.pow(2,42),R3=Math.pow(2,49),T3=Math.pow(2,56),B3=Math.pow(2,63),F3=function(t){return t<D3?1:t<$3?2:t<A3?3:t<P3?4:t<C3?5:t<N3?6:t<R3?7:t<T3?8:t<B3?9:10},L3={encode:E3,decode:x3,encodingLength:F3},Mf=L3;const zf=(t,e,r=0)=>(Mf.encode(t,e,r),e),Hf=t=>Mf.encodingLength(t),Ya=(t,e)=>{const r=e.byteLength,n=Hf(t),i=n+Hf(r),s=new Uint8Array(i+r);return zf(t,s,0),zf(r,s,n),s.set(e,i),new U3(t,r,e,s)};class U3{constructor(e,r,n,i){this.code=e,this.size=r,this.digest=n,this.bytes=i}}const Vf=({name:t,code:e,encode:r})=>new j3(t,e,r);class j3{constructor(e,r,n){this.name=e,this.code=r,this.encode=n}digest(e){if(e instanceof Uint8Array){const r=this.encode(e);return r instanceof Uint8Array?Ya(this.code,r):r.then(n=>Ya(this.code,n))}else throw Error("Unknown type, must be binary type")}}const Kf=t=>async e=>new Uint8Array(await crypto.subtle.digest(t,e)),q3=Vf({name:"sha2-256",code:18,encode:Kf("SHA-256")}),k3=Vf({name:"sha2-512",code:19,encode:Kf("SHA-512")});var M3=Object.freeze({__proto__:null,sha256:q3,sha512:k3});const Wf=0,z3="identity",Gf=Ff;var H3=Object.freeze({__proto__:null,identity:{code:Wf,name:z3,encode:Gf,digest:t=>Ya(Wf,Gf(t))}});new TextEncoder,new TextDecoder;const Yf=He(He(He(He(He(He(He(He(He(He({},UO),qO),MO),HO),WO),n3),o3),u3),p3),v3);He(He({},M3),H3);function V3(t=0){return globalThis.Buffer!=null&&globalThis.Buffer.allocUnsafe!=null?globalThis.Buffer.allocUnsafe(t):new Uint8Array(t)}function Zf(t,e,r,n){return{name:t,prefix:e,encoder:{name:t,prefix:e,encode:r},decoder:{decode:n}}}const Jf=Zf("utf8","u",t=>"u"+new TextDecoder("utf8").decode(t),t=>new TextEncoder().encode(t.substring(1))),Za=Zf("ascii","a",t=>{let e="a";for(let r=0;r<t.length;r++)e+=String.fromCharCode(t[r]);return e},t=>{t=t.substring(1);const e=V3(t.length);for(let r=0;r<t.length;r++)e[r]=t.charCodeAt(r);return e}),K3=He({utf8:Jf,"utf-8":Jf,hex:Yf.base16,latin1:Za,ascii:Za,binary:Za},Yf);function W3(t,e="utf8"){const r=K3[e];if(!r)throw new Error(`Unsupported encoding "${e}"`);return(e==="utf8"||e==="utf-8")&&globalThis.Buffer!=null&&globalThis.Buffer.from!=null?globalThis.Buffer.from(t,"utf8"):r.decoder.decode(`${r.prefix}${t}`)}var G3=Object.defineProperty,Y3=(t,e,r)=>e in t?G3(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ft=(t,e,r)=>Y3(t,typeof e!="symbol"?e+"":e,r);class Z3{constructor(e,r){this.core=e,this.logger=r,Ft(this,"keychain",new Map),Ft(this,"name",Jx),Ft(this,"version",Xx),Ft(this,"initialized",!1),Ft(this,"storagePrefix",Tt),Ft(this,"init",async()=>{if(!this.initialized){const n=await this.getKeyChain();typeof n<"u"&&(this.keychain=n),this.initialized=!0}}),Ft(this,"has",n=>(this.isInitialized(),this.keychain.has(n))),Ft(this,"set",async(n,i)=>{this.isInitialized(),this.keychain.set(n,i),await this.persist()}),Ft(this,"get",n=>{this.isInitialized();const i=this.keychain.get(n);if(typeof i>"u"){const{message:s}=U("NO_MATCHING_KEY",`${this.name}: ${n}`);throw new Error(s)}return i}),Ft(this,"del",async n=>{this.isInitialized(),this.keychain.delete(n),await this.persist()}),this.core=e,this.logger=ke(r,this.name)}get context(){return Ge(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}async setKeyChain(e){await this.core.storage.setItem(this.storageKey,ua(e))}async getKeyChain(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?la(e):void 0}async persist(){await this.setKeyChain(this.keychain)}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}}var J3=Object.defineProperty,X3=(t,e,r)=>e in t?J3(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Fe=(t,e,r)=>X3(t,typeof e!="symbol"?e+"":e,r);class Q3{constructor(e,r,n){this.core=e,this.logger=r,Fe(this,"name",Yx),Fe(this,"keychain"),Fe(this,"randomSessionIdentifier",Ua()),Fe(this,"initialized",!1),Fe(this,"init",async()=>{this.initialized||(await this.keychain.init(),this.initialized=!0)}),Fe(this,"hasKeys",i=>(this.isInitialized(),this.keychain.has(i))),Fe(this,"getClientId",async()=>{this.isInitialized();const i=await this.getClientSeed(),s=Qu(i);return Xu(s.publicKey)}),Fe(this,"generateKeyPair",()=>{this.isInitialized();const i=QI();return this.setPrivateKey(i.publicKey,i.privateKey)}),Fe(this,"signJWT",async i=>{this.isInitialized();const s=await this.getClientSeed(),o=Qu(s),a=this.randomSessionIdentifier;return await r0(a,i,Zx,o)}),Fe(this,"generateSharedKey",(i,s,o)=>{this.isInitialized();const a=this.getPrivateKey(i),c=eS(a,s);return this.setSymKey(c,o)}),Fe(this,"setSymKey",async(i,s)=>{this.isInitialized();const o=s||xs(i);return await this.keychain.set(o,i),o}),Fe(this,"deleteKeyPair",async i=>{this.isInitialized(),await this.keychain.del(i)}),Fe(this,"deleteSymKey",async i=>{this.isInitialized(),await this.keychain.del(i)}),Fe(this,"encode",async(i,s,o)=>{this.isInitialized();const a=Yd(o),c=Ut(s);if(Jd(a))return nS(c,o?.encoding);if(Zd(a)){const d=a.senderPublicKey,f=a.receiverPublicKey;i=await this.generateSharedKey(d,f)}const u=this.getSymKey(i),{type:l,senderPublicKey:h}=a;return tS({type:l,symKey:u,message:c,senderPublicKey:h,encoding:o?.encoding})}),Fe(this,"decode",async(i,s,o)=>{this.isInitialized();const a=sS(s,o);if(Jd(a)){const c=iS(s,o?.encoding);return Or(c)}if(Zd(a)){const c=a.receiverPublicKey,u=a.senderPublicKey;i=await this.generateSharedKey(c,u)}try{const c=this.getSymKey(i),u=rS({symKey:c,encoded:s,encoding:o?.encoding});return Or(u)}catch(c){this.logger.error(`Failed to decode message from topic: '${i}', clientId: '${await this.getClientId()}'`),this.logger.error(c)}}),Fe(this,"getPayloadType",(i,s=wt)=>{const o=vi({encoded:i,encoding:s});return jr(o.type)}),Fe(this,"getPayloadSenderPublicKey",(i,s=wt)=>{const o=vi({encoded:i,encoding:s});return o.senderPublicKey?Ze(o.senderPublicKey,ze):void 0}),this.core=e,this.logger=ke(r,this.name),this.keychain=n||new Z3(this.core,this.logger)}get context(){return Ge(this.logger)}async setPrivateKey(e,r){return await this.keychain.set(e,r),e}getPrivateKey(e){return this.keychain.get(e)}async getClientSeed(){let e="";try{e=this.keychain.get(Pf)}catch{e=Ua(),await this.keychain.set(Pf,e)}return W3(e,"base16")}getSymKey(e){return this.keychain.get(e)}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}}var eD=Object.defineProperty,tD=Object.defineProperties,rD=Object.getOwnPropertyDescriptors,Xf=Object.getOwnPropertySymbols,nD=Object.prototype.hasOwnProperty,iD=Object.prototype.propertyIsEnumerable,Ja=(t,e,r)=>e in t?eD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,sD=(t,e)=>{for(var r in e||(e={}))nD.call(e,r)&&Ja(t,r,e[r]);if(Xf)for(var r of Xf(e))iD.call(e,r)&&Ja(t,r,e[r]);return t},oD=(t,e)=>tD(t,rD(e)),st=(t,e,r)=>Ja(t,typeof e!="symbol"?e+"":e,r);class aD extends tm{constructor(e,r){super(e,r),this.logger=e,this.core=r,st(this,"messages",new Map),st(this,"messagesWithoutClientAck",new Map),st(this,"name",Qx),st(this,"version",eO),st(this,"initialized",!1),st(this,"storagePrefix",Tt),st(this,"init",async()=>{if(!this.initialized){this.logger.trace("Initialized");try{const n=await this.getRelayerMessages();typeof n<"u"&&(this.messages=n);const i=await this.getRelayerMessagesWithoutClientAck();typeof i<"u"&&(this.messagesWithoutClientAck=i),this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",size:this.messages.size})}catch(n){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(n)}finally{this.initialized=!0}}}),st(this,"set",async(n,i,s)=>{this.isInitialized();const o=Ct(i);let a=this.messages.get(n);if(typeof a>"u"&&(a={}),typeof a[o]<"u")return o;if(a[o]=i,this.messages.set(n,a),s===Ts.inbound){const c=this.messagesWithoutClientAck.get(n)||{};this.messagesWithoutClientAck.set(n,oD(sD({},c),{[o]:i}))}return await this.persist(),o}),st(this,"get",n=>{this.isInitialized();let i=this.messages.get(n);return typeof i>"u"&&(i={}),i}),st(this,"getWithoutAck",n=>{this.isInitialized();const i={};for(const s of n){const o=this.messagesWithoutClientAck.get(s)||{};i[s]=Object.values(o)}return i}),st(this,"has",(n,i)=>{this.isInitialized();const s=this.get(n),o=Ct(i);return typeof s[o]<"u"}),st(this,"ack",async(n,i)=>{this.isInitialized();const s=this.messagesWithoutClientAck.get(n);if(typeof s>"u")return;const o=Ct(i);delete s[o],Object.keys(s).length===0?this.messagesWithoutClientAck.delete(n):this.messagesWithoutClientAck.set(n,s),await this.persist()}),st(this,"del",async n=>{this.isInitialized(),this.messages.delete(n),this.messagesWithoutClientAck.delete(n),await this.persist()}),this.logger=ke(e,this.name),this.core=r}get context(){return Ge(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get storageKeyWithoutClientAck(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name+"_withoutClientAck"}async setRelayerMessages(e){await this.core.storage.setItem(this.storageKey,ua(e))}async setRelayerMessagesWithoutClientAck(e){await this.core.storage.setItem(this.storageKeyWithoutClientAck,ua(e))}async getRelayerMessages(){const e=await this.core.storage.getItem(this.storageKey);return typeof e<"u"?la(e):void 0}async getRelayerMessagesWithoutClientAck(){const e=await this.core.storage.getItem(this.storageKeyWithoutClientAck);return typeof e<"u"?la(e):void 0}async persist(){await this.setRelayerMessages(this.messages),await this.setRelayerMessagesWithoutClientAck(this.messagesWithoutClientAck)}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}}var cD=Object.defineProperty,uD=Object.defineProperties,lD=Object.getOwnPropertyDescriptors,Qf=Object.getOwnPropertySymbols,hD=Object.prototype.hasOwnProperty,dD=Object.prototype.propertyIsEnumerable,Xa=(t,e,r)=>e in t?cD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Fs=(t,e)=>{for(var r in e||(e={}))hD.call(e,r)&&Xa(t,r,e[r]);if(Qf)for(var r of Qf(e))dD.call(e,r)&&Xa(t,r,e[r]);return t},Qa=(t,e)=>uD(t,lD(e)),Et=(t,e,r)=>Xa(t,typeof e!="symbol"?e+"":e,r);class fD extends rm{constructor(e,r){super(e,r),this.relayer=e,this.logger=r,Et(this,"events",new Ue.exports.EventEmitter),Et(this,"name",tO),Et(this,"queue",new Map),Et(this,"publishTimeout",L.toMiliseconds(L.ONE_MINUTE)),Et(this,"initialPublishTimeout",L.toMiliseconds(L.ONE_SECOND*15)),Et(this,"needsTransportRestart",!1),Et(this,"publish",async(n,i,s)=>{var o;this.logger.debug("Publishing Payload"),this.logger.trace({type:"method",method:"publish",params:{topic:n,message:i,opts:s}});const a=s?.ttl||Cf,c=Os(s),u=s?.prompt||!1,l=s?.tag||0,h=s?.id||qr().toString(),d={topic:n,message:i,opts:{ttl:a,relay:c,prompt:u,tag:l,id:h,attestation:s?.attestation,tvf:s?.tvf}},f=`Failed to publish payload, please try again. id:${h} tag:${l}`;try{const p=new Promise(async y=>{const g=({id:b})=>{d.opts.id===b&&(this.removeRequestFromQueue(b),this.relayer.events.removeListener(De.publish,g),y(d))};this.relayer.events.on(De.publish,g);const v=hr(new Promise((b,E)=>{this.rpcPublish({topic:n,message:i,ttl:a,prompt:u,tag:l,id:h,attestation:s?.attestation,tvf:s?.tvf}).then(b).catch(_=>{this.logger.warn(_,_?.message),E(_)})}),this.initialPublishTimeout,`Failed initial publish, retrying.... id:${h} tag:${l}`);try{await v,this.events.removeListener(De.publish,g)}catch(b){this.queue.set(h,Qa(Fs({},d),{attempt:1})),this.logger.warn(b,b?.message)}});this.logger.trace({type:"method",method:"publish",params:{id:h,topic:n,message:i,opts:s}}),await hr(p,this.publishTimeout,f)}catch(p){if(this.logger.debug("Failed to Publish Payload"),this.logger.error(p),(o=s?.internal)!=null&&o.throwOnFailedPublish)throw p}finally{this.queue.delete(h)}}),Et(this,"on",(n,i)=>{this.events.on(n,i)}),Et(this,"once",(n,i)=>{this.events.once(n,i)}),Et(this,"off",(n,i)=>{this.events.off(n,i)}),Et(this,"removeListener",(n,i)=>{this.events.removeListener(n,i)}),this.relayer=e,this.logger=ke(r,this.name),this.registerEventListeners()}get context(){return Ge(this.logger)}async rpcPublish(e){var r,n,i,s;const{topic:o,message:a,ttl:c=Cf,prompt:u,tag:l,id:h,attestation:d,tvf:f}=e,p={method:Ei(Os().protocol).publish,params:Fs({topic:o,message:a,ttl:c,prompt:u,tag:l,attestation:d},f),id:h};Te((r=p.params)==null?void 0:r.prompt)&&((n=p.params)==null||delete n.prompt),Te((i=p.params)==null?void 0:i.tag)&&((s=p.params)==null||delete s.tag),this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"message",direction:"outgoing",request:p});const y=await this.relayer.request(p);return this.relayer.events.emit(De.publish,e),this.logger.debug("Successfully Published Payload"),y}removeRequestFromQueue(e){this.queue.delete(e)}checkQueue(){this.queue.forEach(async(e,r)=>{const n=e.attempt+1;this.queue.set(r,Qa(Fs({},e),{attempt:n}));const{topic:i,message:s,opts:o,attestation:a}=e;this.logger.warn({},`Publisher: queue->publishing: ${e.opts.id}, tag: ${e.opts.tag}, attempt: ${n}`),await this.rpcPublish(Qa(Fs({},e),{topic:i,message:s,ttl:o.ttl,prompt:o.prompt,tag:o.tag,id:o.id,attestation:a,tvf:o.tvf})),this.logger.warn({},`Publisher: queue->published: ${e.opts.id}`)})}registerEventListeners(){this.relayer.core.heartbeat.on(Sr.pulse,()=>{if(this.needsTransportRestart){this.needsTransportRestart=!1,this.relayer.events.emit(De.connection_stalled);return}this.checkQueue()}),this.relayer.on(De.message_ack,e=>{this.removeRequestFromQueue(e.id.toString())})}}var pD=Object.defineProperty,gD=(t,e,r)=>e in t?pD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,$n=(t,e,r)=>gD(t,typeof e!="symbol"?e+"":e,r);class yD{constructor(){$n(this,"map",new Map),$n(this,"set",(e,r)=>{const n=this.get(e);this.exists(e,r)||this.map.set(e,[...n,r])}),$n(this,"get",e=>this.map.get(e)||[]),$n(this,"exists",(e,r)=>this.get(e).includes(r)),$n(this,"delete",(e,r)=>{if(typeof r>"u"){this.map.delete(e);return}if(!this.map.has(e))return;const n=this.get(e);if(!this.exists(e,r))return;const i=n.filter(s=>s!==r);if(!i.length){this.map.delete(e);return}this.map.set(e,i)}),$n(this,"clear",()=>{this.map.clear()})}get topics(){return Array.from(this.map.keys())}}var mD=Object.defineProperty,wD=Object.defineProperties,bD=Object.getOwnPropertyDescriptors,ep=Object.getOwnPropertySymbols,vD=Object.prototype.hasOwnProperty,ED=Object.prototype.propertyIsEnumerable,ec=(t,e,r)=>e in t?mD(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ai=(t,e)=>{for(var r in e||(e={}))vD.call(e,r)&&ec(t,r,e[r]);if(ep)for(var r of ep(e))ED.call(e,r)&&ec(t,r,e[r]);return t},tc=(t,e)=>wD(t,bD(e)),se=(t,e,r)=>ec(t,typeof e!="symbol"?e+"":e,r);class _D extends sm{constructor(e,r){super(e,r),this.relayer=e,this.logger=r,se(this,"subscriptions",new Map),se(this,"topicMap",new yD),se(this,"events",new Ue.exports.EventEmitter),se(this,"name",cO),se(this,"version",uO),se(this,"pending",new Map),se(this,"cached",[]),se(this,"initialized",!1),se(this,"storagePrefix",Tt),se(this,"subscribeTimeout",L.toMiliseconds(L.ONE_MINUTE)),se(this,"initialSubscribeTimeout",L.toMiliseconds(L.ONE_SECOND*15)),se(this,"clientId"),se(this,"batchSubscribeTopicsLimit",500),se(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),this.registerEventListeners(),await this.restore()),this.initialized=!0}),se(this,"subscribe",async(n,i)=>{this.isInitialized(),this.logger.debug("Subscribing Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:n,opts:i}});try{const s=Os(i),o={topic:n,relay:s,transportType:i?.transportType};this.pending.set(n,o);const a=await this.rpcSubscribe(n,s,i);return typeof a=="string"&&(this.onSubscribe(a,o),this.logger.debug("Successfully Subscribed Topic"),this.logger.trace({type:"method",method:"subscribe",params:{topic:n,opts:i}})),a}catch(s){throw this.logger.debug("Failed to Subscribe Topic"),this.logger.error(s),s}}),se(this,"unsubscribe",async(n,i)=>{this.isInitialized(),typeof i?.id<"u"?await this.unsubscribeById(n,i.id,i):await this.unsubscribeByTopic(n,i)}),se(this,"isSubscribed",n=>new Promise(i=>{i(this.topicMap.topics.includes(n))})),se(this,"isKnownTopic",n=>new Promise(i=>{i(this.topicMap.topics.includes(n)||this.pending.has(n)||this.cached.some(s=>s.topic===n))})),se(this,"on",(n,i)=>{this.events.on(n,i)}),se(this,"once",(n,i)=>{this.events.once(n,i)}),se(this,"off",(n,i)=>{this.events.off(n,i)}),se(this,"removeListener",(n,i)=>{this.events.removeListener(n,i)}),se(this,"start",async()=>{await this.onConnect()}),se(this,"stop",async()=>{await this.onDisconnect()}),se(this,"restart",async()=>{await this.restore(),await this.onRestart()}),se(this,"checkPending",async()=>{if(this.pending.size===0&&(!this.initialized||!this.relayer.connected))return;const n=[];this.pending.forEach(i=>{n.push(i)}),await this.batchSubscribe(n)}),se(this,"registerEventListeners",()=>{this.relayer.core.heartbeat.on(Sr.pulse,async()=>{await this.checkPending()}),this.events.on(it.created,async n=>{const i=it.created;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:n}),await this.persist()}),this.events.on(it.deleted,async n=>{const i=it.deleted;this.logger.info(`Emitting ${i}`),this.logger.debug({type:"event",event:i,data:n}),await this.persist()})}),this.relayer=e,this.logger=ke(r,this.name),this.clientId=""}get context(){return Ge(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.relayer.core.customStoragePrefix+"//"+this.name}get length(){return this.subscriptions.size}get ids(){return Array.from(this.subscriptions.keys())}get values(){return Array.from(this.subscriptions.values())}get topics(){return this.topicMap.topics}get hasAnyTopics(){return this.topicMap.topics.length>0||this.pending.size>0||this.cached.length>0||this.subscriptions.size>0}hasSubscription(e,r){let n=!1;try{n=this.getSubscription(e).topic===r}catch{}return n}reset(){this.cached=[],this.initialized=!0}onDisable(){this.values.length>0&&(this.cached=this.values),this.subscriptions.clear(),this.topicMap.clear()}async unsubscribeByTopic(e,r){const n=this.topicMap.get(e);await Promise.all(n.map(async i=>await this.unsubscribeById(e,i,r)))}async unsubscribeById(e,r,n){this.logger.debug("Unsubscribing Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:r,opts:n}});try{const i=Os(n);await this.restartToComplete({topic:e,id:r,relay:i}),await this.rpcUnsubscribe(e,r,i);const s=ie("USER_DISCONNECTED",`${this.name}, ${e}`);await this.onUnsubscribe(e,r,s),this.logger.debug("Successfully Unsubscribed Topic"),this.logger.trace({type:"method",method:"unsubscribe",params:{topic:e,id:r,opts:n}})}catch(i){throw this.logger.debug("Failed to Unsubscribe Topic"),this.logger.error(i),i}}async rpcSubscribe(e,r,n){var i;(!n||n?.transportType===ue.relay)&&await this.restartToComplete({topic:e,id:e,relay:r});const s={method:Ei(r.protocol).subscribe,params:{topic:e}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:s});const o=(i=n?.internal)==null?void 0:i.throwOnFailedPublish;try{const a=await this.getSubscriptionId(e);if(n?.transportType===ue.link_mode)return setTimeout(()=>{(this.relayer.connected||this.relayer.connecting)&&this.relayer.request(s).catch(l=>this.logger.warn(l))},L.toMiliseconds(L.ONE_SECOND)),a;const c=new Promise(async l=>{const h=d=>{d.topic===e&&(this.events.removeListener(it.created,h),l(d.id))};this.events.on(it.created,h);try{const d=await hr(new Promise((f,p)=>{this.relayer.request(s).catch(y=>{this.logger.warn(y,y?.message),p(y)}).then(f)}),this.initialSubscribeTimeout,`Subscribing to ${e} failed, please try again`);this.events.removeListener(it.created,h),l(d)}catch{}}),u=await hr(c,this.subscribeTimeout,`Subscribing to ${e} failed, please try again`);if(!u&&o)throw new Error(`Subscribing to ${e} failed, please try again`);return u?a:null}catch(a){if(this.logger.debug("Outgoing Relay Subscribe Payload stalled"),this.relayer.events.emit(De.connection_stalled),o)throw a}return null}async rpcBatchSubscribe(e){if(!e.length)return;const r=e[0].relay,n={method:Ei(r.protocol).batchSubscribe,params:{topics:e.map(i=>i.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:n});try{await await hr(new Promise(i=>{this.relayer.request(n).catch(s=>this.logger.warn(s)).then(i)}),this.subscribeTimeout,"rpcBatchSubscribe failed, please try again")}catch{this.relayer.events.emit(De.connection_stalled)}}async rpcBatchFetchMessages(e){if(!e.length)return;const r=e[0].relay,n={method:Ei(r.protocol).batchFetchMessages,params:{topics:e.map(s=>s.topic)}};this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:n});let i;try{i=await await hr(new Promise((s,o)=>{this.relayer.request(n).catch(a=>{this.logger.warn(a),o(a)}).then(s)}),this.subscribeTimeout,"rpcBatchFetchMessages failed, please try again")}catch{this.relayer.events.emit(De.connection_stalled)}return i}rpcUnsubscribe(e,r,n){const i={method:Ei(n.protocol).unsubscribe,params:{topic:e,id:r}};return this.logger.debug("Outgoing Relay Payload"),this.logger.trace({type:"payload",direction:"outgoing",request:i}),this.relayer.request(i)}onSubscribe(e,r){this.setSubscription(e,tc(Ai({},r),{id:e})),this.pending.delete(r.topic)}onBatchSubscribe(e){e.length&&e.forEach(r=>{this.setSubscription(r.id,Ai({},r)),this.pending.delete(r.topic)})}async onUnsubscribe(e,r,n){this.events.removeAllListeners(r),this.hasSubscription(r,e)&&this.deleteSubscription(r,n),await this.relayer.messages.del(e)}async setRelayerSubscriptions(e){await this.relayer.core.storage.setItem(this.storageKey,e)}async getRelayerSubscriptions(){return await this.relayer.core.storage.getItem(this.storageKey)}setSubscription(e,r){this.logger.debug("Setting subscription"),this.logger.trace({type:"method",method:"setSubscription",id:e,subscription:r}),this.addSubscription(e,r)}addSubscription(e,r){this.subscriptions.set(e,Ai({},r)),this.topicMap.set(r.topic,e),this.events.emit(it.created,r)}getSubscription(e){this.logger.debug("Getting subscription"),this.logger.trace({type:"method",method:"getSubscription",id:e});const r=this.subscriptions.get(e);if(!r){const{message:n}=U("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(n)}return r}deleteSubscription(e,r){this.logger.debug("Deleting subscription"),this.logger.trace({type:"method",method:"deleteSubscription",id:e,reason:r});const n=this.getSubscription(e);this.subscriptions.delete(e),this.topicMap.delete(n.topic,e),this.events.emit(it.deleted,tc(Ai({},n),{reason:r}))}async persist(){await this.setRelayerSubscriptions(this.values),this.events.emit(it.sync)}async onRestart(){if(this.cached.length){const e=[...this.cached],r=Math.ceil(this.cached.length/this.batchSubscribeTopicsLimit);for(let n=0;n<r;n++){const i=e.splice(0,this.batchSubscribeTopicsLimit);await this.batchSubscribe(i)}}this.events.emit(it.resubscribed)}async restore(){try{const e=await this.getRelayerSubscriptions();if(typeof e>"u"||!e.length)return;if(this.subscriptions.size){const{message:r}=U("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),this.logger.error(`${this.name}: ${JSON.stringify(this.values)}`),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored subscriptions for ${this.name}`),this.logger.trace({type:"method",method:"restore",subscriptions:this.values})}catch(e){this.logger.debug(`Failed to Restore subscriptions for ${this.name}`),this.logger.error(e)}}async batchSubscribe(e){e.length&&(await this.rpcBatchSubscribe(e),this.onBatchSubscribe(await Promise.all(e.map(async r=>tc(Ai({},r),{id:await this.getSubscriptionId(r.topic)})))))}async batchFetchMessages(e){if(!e.length)return;this.logger.trace(`Fetching batch messages for ${e.length} subscriptions`);const r=await this.rpcBatchFetchMessages(e);r&&r.messages&&(await M2(L.toMiliseconds(L.ONE_SECOND)),await this.relayer.handleBatchMessageEvents(r.messages))}async onConnect(){await this.restart(),this.reset()}onDisconnect(){this.onDisable()}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}async restartToComplete(e){!this.relayer.connected&&!this.relayer.connecting&&(this.cached.push(e),await this.relayer.transportOpen())}async getClientId(){return this.clientId||(this.clientId=await this.relayer.core.crypto.getClientId()),this.clientId}async getSubscriptionId(e){return Ct(e+await this.getClientId())}}var ID=Object.defineProperty,tp=Object.getOwnPropertySymbols,SD=Object.prototype.hasOwnProperty,xD=Object.prototype.propertyIsEnumerable,rc=(t,e,r)=>e in t?ID(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,rp=(t,e)=>{for(var r in e||(e={}))SD.call(e,r)&&rc(t,r,e[r]);if(tp)for(var r of tp(e))xD.call(e,r)&&rc(t,r,e[r]);return t},X=(t,e,r)=>rc(t,typeof e!="symbol"?e+"":e,r);class OD extends nm{constructor(e){super(e),X(this,"protocol","wc"),X(this,"version",2),X(this,"core"),X(this,"logger"),X(this,"events",new Ue.exports.EventEmitter),X(this,"provider"),X(this,"messages"),X(this,"subscriber"),X(this,"publisher"),X(this,"name",nO),X(this,"transportExplicitlyClosed",!1),X(this,"initialized",!1),X(this,"connectionAttemptInProgress",!1),X(this,"relayUrl"),X(this,"projectId"),X(this,"packageName"),X(this,"bundleId"),X(this,"hasExperiencedNetworkDisruption",!1),X(this,"pingTimeout"),X(this,"heartBeatTimeout",L.toMiliseconds(L.THIRTY_SECONDS+L.FIVE_SECONDS)),X(this,"reconnectTimeout"),X(this,"connectPromise"),X(this,"reconnectInProgress",!1),X(this,"requestsInFlight",[]),X(this,"connectTimeout",L.toMiliseconds(L.ONE_SECOND*15)),X(this,"request",async r=>{var n,i;this.logger.debug("Publishing Request Payload");const s=r.id||qr().toString();await this.toEstablishConnection();try{this.logger.trace({id:s,method:r.method,topic:(n=r.params)==null?void 0:n.topic},"relayer.request - publishing...");const o=`${s}:${((i=r.params)==null?void 0:i.tag)||""}`;this.requestsInFlight.push(o);const a=await this.provider.request(r);return this.requestsInFlight=this.requestsInFlight.filter(c=>c!==o),a}catch(o){throw this.logger.debug(`Failed to Publish Request: ${s}`),o}}),X(this,"resetPingTimeout",()=>{ps()&&(clearTimeout(this.pingTimeout),this.pingTimeout=setTimeout(()=>{var r,n,i,s;try{this.logger.debug({},"pingTimeout: Connection stalled, terminating..."),(s=(i=(n=(r=this.provider)==null?void 0:r.connection)==null?void 0:n.socket)==null?void 0:i.terminate)==null||s.call(i)}catch(o){this.logger.warn(o,o?.message)}},this.heartBeatTimeout))}),X(this,"onPayloadHandler",r=>{this.onProviderPayload(r),this.resetPingTimeout()}),X(this,"onConnectHandler",()=>{this.logger.warn({},"Relayer connected \u{1F6DC}"),this.startPingTimeout(),this.events.emit(De.connect)}),X(this,"onDisconnectHandler",()=>{this.logger.warn({},"Relayer disconnected \u{1F6D1}"),this.requestsInFlight=[],this.onProviderDisconnect()}),X(this,"onProviderErrorHandler",r=>{this.logger.fatal(`Fatal socket error: ${r.message}`),this.events.emit(De.error,r),this.logger.fatal("Fatal socket error received, closing transport"),this.transportClose()}),X(this,"registerProviderListeners",()=>{this.provider.on(ht.payload,this.onPayloadHandler),this.provider.on(ht.connect,this.onConnectHandler),this.provider.on(ht.disconnect,this.onDisconnectHandler),this.provider.on(ht.error,this.onProviderErrorHandler)}),this.core=e.core,this.logger=typeof e.logger<"u"&&typeof e.logger!="string"?ke(e.logger,this.name):It(Hi({level:e.logger||rO})),this.messages=new aD(this.logger,e.core),this.subscriber=new _D(this,this.logger),this.publisher=new fD(this,this.logger),this.relayUrl=e?.relayUrl||Rf,this.projectId=e.projectId,O2()?this.packageName=Uh():D2()&&(this.bundleId=Uh()),this.provider={}}async init(){if(this.logger.trace("Initialized"),this.registerEventListeners(),await Promise.all([this.messages.init(),this.subscriber.init()]),this.initialized=!0,this.subscriber.hasAnyTopics)try{await this.transportOpen()}catch(e){this.logger.warn(e,e?.message)}}get context(){return Ge(this.logger)}get connected(){var e,r,n;return((n=(r=(e=this.provider)==null?void 0:e.connection)==null?void 0:r.socket)==null?void 0:n.readyState)===1||!1}get connecting(){var e,r,n;return((n=(r=(e=this.provider)==null?void 0:e.connection)==null?void 0:r.socket)==null?void 0:n.readyState)===0||this.connectPromise!==void 0||!1}async publish(e,r,n){this.isInitialized(),await this.publisher.publish(e,r,n),await this.recordMessageEvent({topic:e,message:r,publishedAt:Date.now(),transportType:ue.relay},Ts.outbound)}async subscribe(e,r){var n,i,s;this.isInitialized(),(!(r!=null&&r.transportType)||r?.transportType==="relay")&&await this.toEstablishConnection();const o=typeof((n=r?.internal)==null?void 0:n.throwOnFailedPublish)>"u"?!0:(i=r?.internal)==null?void 0:i.throwOnFailedPublish;let a=((s=this.subscriber.topicMap.get(e))==null?void 0:s[0])||"",c;const u=l=>{l.topic===e&&(this.subscriber.off(it.created,u),c())};return await Promise.all([new Promise(l=>{c=l,this.subscriber.on(it.created,u)}),new Promise(async(l,h)=>{a=await this.subscriber.subscribe(e,rp({internal:{throwOnFailedPublish:o}},r)).catch(d=>{o&&h(d)})||a,l()})]),a}async unsubscribe(e,r){this.isInitialized(),await this.subscriber.unsubscribe(e,r)}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async transportDisconnect(){this.provider.disconnect&&(this.hasExperiencedNetworkDisruption||this.connected)?await hr(this.provider.disconnect(),2e3,"provider.disconnect()").catch(()=>this.onProviderDisconnect()):this.onProviderDisconnect()}async transportClose(){this.transportExplicitlyClosed=!0,await this.transportDisconnect()}async transportOpen(e){if(!this.subscriber.hasAnyTopics){this.logger.warn("Starting WS connection skipped because the client has no topics to work with.");return}if(this.connectPromise?(this.logger.debug({},"Waiting for existing connection attempt to resolve..."),await this.connectPromise,this.logger.debug({},"Existing connection attempt resolved")):(this.connectPromise=new Promise(async(r,n)=>{await this.connect(e).then(r).catch(n).finally(()=>{this.connectPromise=void 0})}),await this.connectPromise),!this.connected)throw new Error(`Couldn't establish socket connection to the relay server: ${this.relayUrl}`)}async restartTransport(e){this.logger.debug({},"Restarting transport..."),!this.connectionAttemptInProgress&&(this.relayUrl=e||this.relayUrl,await this.confirmOnlineStateOrThrow(),await this.transportClose(),await this.transportOpen())}async confirmOnlineStateOrThrow(){if(!await hf())throw new Error("No internet connection detected. Please restart your network and try again.")}async handleBatchMessageEvents(e){if(e?.length===0){this.logger.trace("Batch message events is empty. Ignoring...");return}const r=e.sort((n,i)=>n.publishedAt-i.publishedAt);this.logger.debug(`Batch of ${r.length} message events sorted`);for(const n of r)try{await this.onMessageEvent(n)}catch(i){this.logger.warn(i,"Error while processing batch message event: "+i?.message)}this.logger.trace(`Batch of ${r.length} message events processed`)}async onLinkMessageEvent(e,r){const{topic:n}=e;if(!r.sessionExists){const i=ve(L.FIVE_MINUTES),s={topic:n,expiry:i,relay:{protocol:"irn"},active:!1};await this.core.pairing.pairings.set(n,s)}this.events.emit(De.message,e),await this.recordMessageEvent(e,Ts.inbound)}async connect(e){await this.confirmOnlineStateOrThrow(),e&&e!==this.relayUrl&&(this.relayUrl=e,await this.transportDisconnect()),this.connectionAttemptInProgress=!0,this.transportExplicitlyClosed=!1;let r=1;for(;r<6;){try{if(this.transportExplicitlyClosed)break;this.logger.debug({},`Connecting to ${this.relayUrl}, attempt: ${r}...`),await this.createProvider(),await new Promise(async(n,i)=>{const s=()=>{i(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(ht.disconnect,s),await hr(new Promise((o,a)=>{this.provider.connect().then(o).catch(a)}),this.connectTimeout,`Socket stalled when trying to connect to ${this.relayUrl}`).catch(o=>{i(o)}).finally(()=>{this.provider.off(ht.disconnect,s),clearTimeout(this.reconnectTimeout)}),await new Promise(async(o,a)=>{const c=()=>{a(new Error("Connection interrupted while trying to subscribe"))};this.provider.once(ht.disconnect,c),await this.subscriber.start().then(o).catch(a).finally(()=>{this.provider.off(ht.disconnect,c)})}),this.hasExperiencedNetworkDisruption=!1,n()})}catch(n){await this.subscriber.stop();const i=n;this.logger.warn({},i.message),this.hasExperiencedNetworkDisruption=!0}finally{this.connectionAttemptInProgress=!1}if(this.connected){this.logger.debug({},`Connected to ${this.relayUrl} successfully on attempt: ${r}`);break}await new Promise(n=>setTimeout(n,L.toMiliseconds(r*1))),r++}}startPingTimeout(){var e,r,n,i,s;if(ps())try{(r=(e=this.provider)==null?void 0:e.connection)!=null&&r.socket&&((s=(i=(n=this.provider)==null?void 0:n.connection)==null?void 0:i.socket)==null||s.on("ping",()=>{this.resetPingTimeout()})),this.resetPingTimeout()}catch(o){this.logger.warn(o,o?.message)}}async createProvider(){this.provider.connection&&this.unregisterProviderListeners();const e=await this.core.crypto.signJWT(this.relayUrl);this.provider=new lt(new zx(N2({sdkVersion:Wa,protocol:this.protocol,version:this.version,relayUrl:this.relayUrl,projectId:this.projectId,auth:e,useOnCloseEvent:!0,bundleId:this.bundleId,packageName:this.packageName}))),this.registerProviderListeners()}async recordMessageEvent(e,r){const{topic:n,message:i}=e;await this.messages.set(n,i,r)}async shouldIgnoreMessageEvent(e){const{topic:r,message:n}=e;if(!n||n.length===0)return this.logger.warn(`Ignoring invalid/empty message: ${n}`),!0;if(!await this.subscriber.isKnownTopic(r))return this.logger.warn(`Ignoring message for unknown topic ${r}`),!0;const i=this.messages.has(r,n);return i&&this.logger.warn(`Ignoring duplicate message: ${n}`),i}async onProviderPayload(e){if(this.logger.debug("Incoming Relay Payload"),this.logger.trace({type:"payload",direction:"incoming",payload:e}),Ka(e)){if(!e.method.endsWith(iO))return;const r=e.params,{topic:n,message:i,publishedAt:s,attestation:o}=r.data,a={topic:n,message:i,publishedAt:s,transportType:ue.relay,attestation:o};this.logger.debug("Emitting Relayer Payload"),this.logger.trace(rp({type:"event",event:r.id},a)),this.events.emit(r.id,a),await this.acknowledgePayload(e),await this.onMessageEvent(a)}else Ns(e)&&this.events.emit(De.message_ack,e)}async onMessageEvent(e){await this.shouldIgnoreMessageEvent(e)||(await this.recordMessageEvent(e,Ts.inbound),this.events.emit(De.message,e))}async acknowledgePayload(e){const r=Ps(e.id,!0);await this.provider.connection.send(r)}unregisterProviderListeners(){this.provider.off(ht.payload,this.onPayloadHandler),this.provider.off(ht.connect,this.onConnectHandler),this.provider.off(ht.disconnect,this.onDisconnectHandler),this.provider.off(ht.error,this.onProviderErrorHandler),clearTimeout(this.pingTimeout)}async registerEventListeners(){let e=await hf();XS(async r=>{e!==r&&(e=r,r?await this.transportOpen().catch(n=>this.logger.error(n,n?.message)):(this.hasExperiencedNetworkDisruption=!0,await this.transportDisconnect(),this.transportExplicitlyClosed=!1))}),this.core.heartbeat.on(Sr.pulse,async()=>{if(!this.transportExplicitlyClosed&&!this.connected&&tx())try{await this.confirmOnlineStateOrThrow(),await this.transportOpen()}catch(r){this.logger.warn(r,r?.message)}})}async onProviderDisconnect(){clearTimeout(this.pingTimeout),this.events.emit(De.disconnect),this.connectionAttemptInProgress=!1,!this.reconnectInProgress&&(this.reconnectInProgress=!0,await this.subscriber.stop(),this.subscriber.hasAnyTopics&&(this.transportExplicitlyClosed||(this.reconnectTimeout=setTimeout(async()=>{await this.transportOpen().catch(e=>this.logger.error(e,e?.message)),this.reconnectTimeout=void 0,this.reconnectInProgress=!1},L.toMiliseconds(sO)))))}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}async toEstablishConnection(){if(await this.confirmOnlineStateOrThrow(),!this.connected){if(this.connectPromise){await this.connectPromise;return}await this.connect()}}}function DD(){}function np(t){if(!t||typeof t!="object")return!1;const e=Object.getPrototypeOf(t);return e===null||e===Object.prototype||Object.getPrototypeOf(e)===null?Object.prototype.toString.call(t)==="[object Object]":!1}function ip(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}function sp(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const $D="[object RegExp]",AD="[object String]",PD="[object Number]",CD="[object Boolean]",op="[object Arguments]",ND="[object Symbol]",RD="[object Date]",TD="[object Map]",BD="[object Set]",FD="[object Array]",LD="[object Function]",UD="[object ArrayBuffer]",nc="[object Object]",jD="[object Error]",qD="[object DataView]",kD="[object Uint8Array]",MD="[object Uint8ClampedArray]",zD="[object Uint16Array]",HD="[object Uint32Array]",VD="[object BigUint64Array]",KD="[object Int8Array]",WD="[object Int16Array]",GD="[object Int32Array]",YD="[object BigInt64Array]",ZD="[object Float32Array]",JD="[object Float64Array]";function XD(t,e){return t===e||Number.isNaN(t)&&Number.isNaN(e)}function QD(t,e,r){return Pi(t,e,void 0,void 0,void 0,void 0,r)}function Pi(t,e,r,n,i,s,o){const a=o(t,e,r,n,i,s);if(a!==void 0)return a;if(typeof t==typeof e)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":return t===e;case"number":return t===e||Object.is(t,e);case"function":return t===e;case"object":return Ci(t,e,s,o)}return Ci(t,e,s,o)}function Ci(t,e,r,n){if(Object.is(t,e))return!0;let i=sp(t),s=sp(e);if(i===op&&(i=nc),s===op&&(s=nc),i!==s)return!1;switch(i){case AD:return t.toString()===e.toString();case PD:{const c=t.valueOf(),u=e.valueOf();return XD(c,u)}case CD:case RD:case ND:return Object.is(t.valueOf(),e.valueOf());case $D:return t.source===e.source&&t.flags===e.flags;case LD:return t===e}r=r??new Map;const o=r.get(t),a=r.get(e);if(o!=null&&a!=null)return o===e;r.set(t,e),r.set(e,t);try{switch(i){case TD:{if(t.size!==e.size)return!1;for(const[c,u]of t.entries())if(!e.has(c)||!Pi(u,e.get(c),c,t,e,r,n))return!1;return!0}case BD:{if(t.size!==e.size)return!1;const c=Array.from(t.values()),u=Array.from(e.values());for(let l=0;l<c.length;l++){const h=c[l],d=u.findIndex(f=>Pi(h,f,void 0,t,e,r,n));if(d===-1)return!1;u.splice(d,1)}return!0}case FD:case kD:case MD:case zD:case HD:case VD:case KD:case WD:case GD:case YD:case ZD:case JD:{if(typeof Buffer<"u"&&Buffer.isBuffer(t)!==Buffer.isBuffer(e)||t.length!==e.length)return!1;for(let c=0;c<t.length;c++)if(!Pi(t[c],e[c],c,t,e,r,n))return!1;return!0}case UD:return t.byteLength!==e.byteLength?!1:Ci(new Uint8Array(t),new Uint8Array(e),r,n);case qD:return t.byteLength!==e.byteLength||t.byteOffset!==e.byteOffset?!1:Ci(new Uint8Array(t),new Uint8Array(e),r,n);case jD:return t.name===e.name&&t.message===e.message;case nc:{if(!(Ci(t.constructor,e.constructor,r,n)||np(t)&&np(e)))return!1;const c=[...Object.keys(t),...ip(t)],u=[...Object.keys(e),...ip(e)];if(c.length!==u.length)return!1;for(let l=0;l<c.length;l++){const h=c[l],d=t[h];if(!Object.hasOwn(e,h))return!1;const f=e[h];if(!Pi(d,f,h,t,e,r,n))return!1}return!0}default:return!1}}finally{r.delete(t),r.delete(e)}}function e$(t,e){return QD(t,e,DD)}var t$=Object.defineProperty,ap=Object.getOwnPropertySymbols,r$=Object.prototype.hasOwnProperty,n$=Object.prototype.propertyIsEnumerable,ic=(t,e,r)=>e in t?t$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,cp=(t,e)=>{for(var r in e||(e={}))r$.call(e,r)&&ic(t,r,e[r]);if(ap)for(var r of ap(e))n$.call(e,r)&&ic(t,r,e[r]);return t},Qe=(t,e,r)=>ic(t,typeof e!="symbol"?e+"":e,r);class Hr extends im{constructor(e,r,n,i=Tt,s=void 0){super(e,r,n,i),this.core=e,this.logger=r,this.name=n,Qe(this,"map",new Map),Qe(this,"version",oO),Qe(this,"cached",[]),Qe(this,"initialized",!1),Qe(this,"getKey"),Qe(this,"storagePrefix",Tt),Qe(this,"recentlyDeleted",[]),Qe(this,"recentlyDeletedLimit",200),Qe(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(o=>{this.getKey&&o!==null&&!Te(o)?this.map.set(this.getKey(o),o):AS(o)?this.map.set(o.id,o):PS(o)&&this.map.set(o.topic,o)}),this.cached=[],this.initialized=!0)}),Qe(this,"set",async(o,a)=>{this.isInitialized(),this.map.has(o)?await this.update(o,a):(this.logger.debug("Setting value"),this.logger.trace({type:"method",method:"set",key:o,value:a}),this.map.set(o,a),await this.persist())}),Qe(this,"get",o=>(this.isInitialized(),this.logger.debug("Getting value"),this.logger.trace({type:"method",method:"get",key:o}),this.getData(o))),Qe(this,"getAll",o=>(this.isInitialized(),o?this.values.filter(a=>Object.keys(o).every(c=>e$(a[c],o[c]))):this.values)),Qe(this,"update",async(o,a)=>{this.isInitialized(),this.logger.debug("Updating value"),this.logger.trace({type:"method",method:"update",key:o,update:a});const c=cp(cp({},this.getData(o)),a);this.map.set(o,c),await this.persist()}),Qe(this,"delete",async(o,a)=>{this.isInitialized(),this.map.has(o)&&(this.logger.debug("Deleting value"),this.logger.trace({type:"method",method:"delete",key:o,reason:a}),this.map.delete(o),this.addToRecentlyDeleted(o),await this.persist())}),this.logger=ke(r,this.name),this.storagePrefix=i,this.getKey=s}get context(){return Ge(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.map.size}get keys(){return Array.from(this.map.keys())}get values(){return Array.from(this.map.values())}addToRecentlyDeleted(e){this.recentlyDeleted.push(e),this.recentlyDeleted.length>=this.recentlyDeletedLimit&&this.recentlyDeleted.splice(0,this.recentlyDeletedLimit/2)}async setDataStore(e){await this.core.storage.setItem(this.storageKey,e)}async getDataStore(){return await this.core.storage.getItem(this.storageKey)}getData(e){const r=this.map.get(e);if(!r){if(this.recentlyDeleted.includes(e)){const{message:i}=U("MISSING_OR_INVALID",`Record was recently deleted - ${this.name}: ${e}`);throw this.logger.error(i),new Error(i)}const{message:n}=U("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.error(n),new Error(n)}return r}async persist(){await this.setDataStore(this.values)}async restore(){try{const e=await this.getDataStore();if(typeof e>"u"||!e.length)return;if(this.map.size){const{message:r}=U("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored value for ${this.name}`),this.logger.trace({type:"method",method:"restore",value:this.values})}catch(e){this.logger.debug(`Failed to Restore value for ${this.name}`),this.logger.error(e)}}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}}var i$=Object.defineProperty,s$=(t,e,r)=>e in t?i$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Y=(t,e,r)=>s$(t,typeof e!="symbol"?e+"":e,r);class o${constructor(e,r){this.core=e,this.logger=r,Y(this,"name",lO),Y(this,"version",hO),Y(this,"events",new Ue.exports),Y(this,"pairings"),Y(this,"initialized",!1),Y(this,"storagePrefix",Tt),Y(this,"ignoredPayloadTypes",[Zt]),Y(this,"registeredMethods",[]),Y(this,"init",async()=>{this.initialized||(await this.pairings.init(),await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.initialized=!0,this.logger.trace("Initialized"))}),Y(this,"register",({methods:n})=>{this.isInitialized(),this.registeredMethods=[...new Set([...this.registeredMethods,...n])]}),Y(this,"create",async n=>{this.isInitialized();const i=Ua(),s=await this.core.crypto.setSymKey(i),o=ve(L.FIVE_MINUTES),a={protocol:Nf},c={topic:s,expiry:o,relay:a,active:!1,methods:n?.methods},u=Qd({protocol:this.core.protocol,version:this.core.version,topic:s,symKey:i,relay:a,expiryTimestamp:o,methods:n?.methods});return this.events.emit(kr.create,c),this.core.expirer.set(s,o),await this.pairings.set(s,c),await this.core.relayer.subscribe(s,{transportType:n?.transportType}),{topic:s,uri:u}}),Y(this,"pair",async n=>{this.isInitialized();const i=this.core.eventClient.createEvent({properties:{topic:n?.uri,trace:[Bt.pairing_started]}});this.isValidPair(n,i);const{topic:s,symKey:o,relay:a,expiryTimestamp:c,methods:u}=Xd(n.uri);i.props.properties.topic=s,i.addTrace(Bt.pairing_uri_validation_success),i.addTrace(Bt.pairing_uri_not_expired);let l;if(this.pairings.keys.includes(s)){if(l=this.pairings.get(s),i.addTrace(Bt.existing_pairing),l.active)throw i.setError(Qt.active_pairing_already_exists),new Error(`Pairing already exists: ${s}. Please try again with a new connection URI.`);i.addTrace(Bt.pairing_not_expired)}const h=c||ve(L.FIVE_MINUTES),d={topic:s,relay:a,expiry:h,active:!1,methods:u};this.core.expirer.set(s,h),await this.pairings.set(s,d),i.addTrace(Bt.store_new_pairing),n.activatePairing&&await this.activate({topic:s}),this.events.emit(kr.create,d),i.addTrace(Bt.emit_inactive_pairing),this.core.crypto.keychain.has(s)||await this.core.crypto.setSymKey(o,s),i.addTrace(Bt.subscribing_pairing_topic);try{await this.core.relayer.confirmOnlineStateOrThrow()}catch{i.setError(Qt.no_internet_connection)}try{await this.core.relayer.subscribe(s,{relay:a})}catch(f){throw i.setError(Qt.subscribe_pairing_topic_failure),f}return i.addTrace(Bt.subscribe_pairing_topic_success),d}),Y(this,"activate",async({topic:n})=>{this.isInitialized();const i=ve(L.FIVE_MINUTES);this.core.expirer.set(n,i),await this.pairings.update(n,{active:!0,expiry:i})}),Y(this,"ping",async n=>{this.isInitialized(),await this.isValidPing(n),this.logger.warn("ping() is deprecated and will be removed in the next major release.");const{topic:i}=n;if(this.pairings.keys.includes(i)){const s=await this.sendRequest(i,"wc_pairingPing",{}),{done:o,resolve:a,reject:c}=Tr();this.events.once(te("pairing_ping",s),({error:u})=>{u?c(u):a()}),await o()}}),Y(this,"updateExpiry",async({topic:n,expiry:i})=>{this.isInitialized(),await this.pairings.update(n,{expiry:i})}),Y(this,"updateMetadata",async({topic:n,metadata:i})=>{this.isInitialized(),await this.pairings.update(n,{peerMetadata:i})}),Y(this,"getPairings",()=>(this.isInitialized(),this.pairings.values)),Y(this,"disconnect",async n=>{this.isInitialized(),await this.isValidDisconnect(n);const{topic:i}=n;this.pairings.keys.includes(i)&&(await this.sendRequest(i,"wc_pairingDelete",ie("USER_DISCONNECTED")),await this.deletePairing(i))}),Y(this,"formatUriFromPairing",n=>{this.isInitialized();const{topic:i,relay:s,expiry:o,methods:a}=n,c=this.core.crypto.keychain.get(i);return Qd({protocol:this.core.protocol,version:this.core.version,topic:i,symKey:c,relay:s,expiryTimestamp:o,methods:a})}),Y(this,"sendRequest",async(n,i,s)=>{const o=br(i,s),a=await this.core.crypto.encode(n,o),c=xi[i].req;return this.core.history.set(n,o),this.core.relayer.publish(n,a,c),o.id}),Y(this,"sendResult",async(n,i,s)=>{const o=Ps(n,s),a=await this.core.crypto.encode(i,o),c=(await this.core.history.get(i,n)).request.method,u=xi[c].res;await this.core.relayer.publish(i,a,u),await this.core.history.resolve(o)}),Y(this,"sendError",async(n,i,s)=>{const o=Cs(n,s),a=await this.core.crypto.encode(i,o),c=(await this.core.history.get(i,n)).request.method,u=xi[c]?xi[c].res:xi.unregistered_method.res;await this.core.relayer.publish(i,a,u),await this.core.history.resolve(o)}),Y(this,"deletePairing",async(n,i)=>{await this.core.relayer.unsubscribe(n),await Promise.all([this.pairings.delete(n,ie("USER_DISCONNECTED")),this.core.crypto.deleteSymKey(n),i?Promise.resolve():this.core.expirer.del(n)])}),Y(this,"cleanup",async()=>{const n=this.pairings.getAll().filter(i=>dr(i.expiry));await Promise.all(n.map(i=>this.deletePairing(i.topic)))}),Y(this,"onRelayEventRequest",async n=>{const{topic:i,payload:s}=n;switch(s.method){case"wc_pairingPing":return await this.onPairingPingRequest(i,s);case"wc_pairingDelete":return await this.onPairingDeleteRequest(i,s);default:return await this.onUnknownRpcMethodRequest(i,s)}}),Y(this,"onRelayEventResponse",async n=>{const{topic:i,payload:s}=n,o=(await this.core.history.get(i,s.id)).request.method;switch(o){case"wc_pairingPing":return this.onPairingPingResponse(i,s);default:return this.onUnknownRpcMethodResponse(o)}}),Y(this,"onPairingPingRequest",async(n,i)=>{const{id:s}=i;try{this.isValidPing({topic:n}),await this.sendResult(s,n,!0),this.events.emit(kr.ping,{id:s,topic:n})}catch(o){await this.sendError(s,n,o),this.logger.error(o)}}),Y(this,"onPairingPingResponse",(n,i)=>{const{id:s}=i;setTimeout(()=>{Rt(i)?this.events.emit(te("pairing_ping",s),{}):ut(i)&&this.events.emit(te("pairing_ping",s),{error:i.error})},500)}),Y(this,"onPairingDeleteRequest",async(n,i)=>{const{id:s}=i;try{this.isValidDisconnect({topic:n}),await this.deletePairing(n),this.events.emit(kr.delete,{id:s,topic:n})}catch(o){await this.sendError(s,n,o),this.logger.error(o)}}),Y(this,"onUnknownRpcMethodRequest",async(n,i)=>{const{id:s,method:o}=i;try{if(this.registeredMethods.includes(o))return;const a=ie("WC_METHOD_UNSUPPORTED",o);await this.sendError(s,n,a),this.logger.error(a)}catch(a){await this.sendError(s,n,a),this.logger.error(a)}}),Y(this,"onUnknownRpcMethodResponse",n=>{this.registeredMethods.includes(n)||this.logger.error(ie("WC_METHOD_UNSUPPORTED",n))}),Y(this,"isValidPair",(n,i)=>{var s;if(!Xe(n)){const{message:a}=U("MISSING_OR_INVALID",`pair() params: ${n}`);throw i.setError(Qt.malformed_pairing_uri),new Error(a)}if(!$S(n.uri)){const{message:a}=U("MISSING_OR_INVALID",`pair() uri: ${n.uri}`);throw i.setError(Qt.malformed_pairing_uri),new Error(a)}const o=Xd(n?.uri);if(!((s=o?.relay)!=null&&s.protocol)){const{message:a}=U("MISSING_OR_INVALID","pair() uri#relay-protocol");throw i.setError(Qt.malformed_pairing_uri),new Error(a)}if(!(o!=null&&o.symKey)){const{message:a}=U("MISSING_OR_INVALID","pair() uri#symKey");throw i.setError(Qt.malformed_pairing_uri),new Error(a)}if(o!=null&&o.expiryTimestamp&&L.toMiliseconds(o?.expiryTimestamp)<Date.now()){i.setError(Qt.pairing_expired);const{message:a}=U("EXPIRED","pair() URI has expired. Please try again with a new connection URI.");throw new Error(a)}}),Y(this,"isValidPing",async n=>{if(!Xe(n)){const{message:s}=U("MISSING_OR_INVALID",`ping() params: ${n}`);throw new Error(s)}const{topic:i}=n;await this.isValidPairingTopic(i)}),Y(this,"isValidDisconnect",async n=>{if(!Xe(n)){const{message:s}=U("MISSING_OR_INVALID",`disconnect() params: ${n}`);throw new Error(s)}const{topic:i}=n;await this.isValidPairingTopic(i)}),Y(this,"isValidPairingTopic",async n=>{if(!ye(n,!1)){const{message:i}=U("MISSING_OR_INVALID",`pairing topic should be a string: ${n}`);throw new Error(i)}if(!this.pairings.keys.includes(n)){const{message:i}=U("NO_MATCHING_KEY",`pairing topic doesn't exist: ${n}`);throw new Error(i)}if(dr(this.pairings.get(n).expiry)){await this.deletePairing(n);const{message:i}=U("EXPIRED",`pairing topic: ${n}`);throw new Error(i)}}),this.core=e,this.logger=ke(r,this.name),this.pairings=new Hr(this.core,this.logger,this.name,this.storagePrefix)}get context(){return Ge(this.logger)}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}registerRelayerEvents(){this.core.relayer.on(De.message,async e=>{const{topic:r,message:n,transportType:i}=e;if(this.pairings.keys.includes(r)&&i!==ue.link_mode&&!this.ignoredPayloadTypes.includes(this.core.crypto.getPayloadType(n)))try{const s=await this.core.crypto.decode(r,n);Ka(s)?(this.core.history.set(r,s),await this.onRelayEventRequest({topic:r,payload:s})):Ns(s)&&(await this.core.history.resolve(s),await this.onRelayEventResponse({topic:r,payload:s}),this.core.history.delete(r,s.id)),await this.core.relayer.messages.ack(r,n)}catch(s){this.logger.error(s)}})}registerExpirerEvents(){this.core.expirer.on(dt.expired,async e=>{const{topic:r}=Mh(e.target);r&&this.pairings.keys.includes(r)&&(await this.deletePairing(r,!0),this.events.emit(kr.expire,{topic:r}))})}}var a$=Object.defineProperty,c$=(t,e,r)=>e in t?a$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Le=(t,e,r)=>c$(t,typeof e!="symbol"?e+"":e,r);class u$ extends em{constructor(e,r){super(e,r),this.core=e,this.logger=r,Le(this,"records",new Map),Le(this,"events",new Ue.exports.EventEmitter),Le(this,"name",dO),Le(this,"version",fO),Le(this,"cached",[]),Le(this,"initialized",!1),Le(this,"storagePrefix",Tt),Le(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(n=>this.records.set(n.id,n)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),Le(this,"set",(n,i,s)=>{if(this.isInitialized(),this.logger.debug("Setting JSON-RPC request history record"),this.logger.trace({type:"method",method:"set",topic:n,request:i,chainId:s}),this.records.has(i.id))return;const o={id:i.id,topic:n,request:{method:i.method,params:i.params||null},chainId:s,expiry:ve(L.THIRTY_DAYS)};this.records.set(o.id,o),this.persist(),this.events.emit(bt.created,o)}),Le(this,"resolve",async n=>{if(this.isInitialized(),this.logger.debug("Updating JSON-RPC response history record"),this.logger.trace({type:"method",method:"update",response:n}),!this.records.has(n.id))return;const i=await this.getRecord(n.id);typeof i.response>"u"&&(i.response=ut(n)?{error:n.error}:{result:n.result},this.records.set(i.id,i),this.persist(),this.events.emit(bt.updated,i))}),Le(this,"get",async(n,i)=>(this.isInitialized(),this.logger.debug("Getting record"),this.logger.trace({type:"method",method:"get",topic:n,id:i}),await this.getRecord(i))),Le(this,"delete",(n,i)=>{this.isInitialized(),this.logger.debug("Deleting record"),this.logger.trace({type:"method",method:"delete",id:i}),this.values.forEach(s=>{if(s.topic===n){if(typeof i<"u"&&s.id!==i)return;this.records.delete(s.id),this.events.emit(bt.deleted,s)}}),this.persist()}),Le(this,"exists",async(n,i)=>(this.isInitialized(),this.records.has(i)?(await this.getRecord(i)).topic===n:!1)),Le(this,"on",(n,i)=>{this.events.on(n,i)}),Le(this,"once",(n,i)=>{this.events.once(n,i)}),Le(this,"off",(n,i)=>{this.events.off(n,i)}),Le(this,"removeListener",(n,i)=>{this.events.removeListener(n,i)}),this.logger=ke(r,this.name)}get context(){return Ge(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get size(){return this.records.size}get keys(){return Array.from(this.records.keys())}get values(){return Array.from(this.records.values())}get pending(){const e=[];return this.values.forEach(r=>{if(typeof r.response<"u")return;const n={topic:r.topic,request:br(r.request.method,r.request.params,r.id),chainId:r.chainId};return e.push(n)}),e}async setJsonRpcRecords(e){await this.core.storage.setItem(this.storageKey,e)}async getJsonRpcRecords(){return await this.core.storage.getItem(this.storageKey)}getRecord(e){this.isInitialized();const r=this.records.get(e);if(!r){const{message:n}=U("NO_MATCHING_KEY",`${this.name}: ${e}`);throw new Error(n)}return r}async persist(){await this.setJsonRpcRecords(this.values),this.events.emit(bt.sync)}async restore(){try{const e=await this.getJsonRpcRecords();if(typeof e>"u"||!e.length)return;if(this.records.size){const{message:r}=U("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored records for ${this.name}`),this.logger.trace({type:"method",method:"restore",records:this.values})}catch(e){this.logger.debug(`Failed to Restore records for ${this.name}`),this.logger.error(e)}}registerEventListeners(){this.events.on(bt.created,e=>{const r=bt.created;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.events.on(bt.updated,e=>{const r=bt.updated;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.events.on(bt.deleted,e=>{const r=bt.deleted;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,record:e})}),this.core.heartbeat.on(Sr.pulse,()=>{this.cleanup()})}cleanup(){try{this.isInitialized();let e=!1;this.records.forEach(r=>{L.toMiliseconds(r.expiry||0)-Date.now()<=0&&(this.logger.info(`Deleting expired history log: ${r.id}`),this.records.delete(r.id),this.events.emit(bt.deleted,r,!1),e=!0)}),e&&this.persist()}catch(e){this.logger.warn(e)}}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}}var l$=Object.defineProperty,h$=(t,e,r)=>e in t?l$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,qe=(t,e,r)=>h$(t,typeof e!="symbol"?e+"":e,r);class d$ extends om{constructor(e,r){super(e,r),this.core=e,this.logger=r,qe(this,"expirations",new Map),qe(this,"events",new Ue.exports.EventEmitter),qe(this,"name",pO),qe(this,"version",gO),qe(this,"cached",[]),qe(this,"initialized",!1),qe(this,"storagePrefix",Tt),qe(this,"init",async()=>{this.initialized||(this.logger.trace("Initialized"),await this.restore(),this.cached.forEach(n=>this.expirations.set(n.target,n)),this.cached=[],this.registerEventListeners(),this.initialized=!0)}),qe(this,"has",n=>{try{const i=this.formatTarget(n);return typeof this.getExpiration(i)<"u"}catch{return!1}}),qe(this,"set",(n,i)=>{this.isInitialized();const s=this.formatTarget(n),o={target:s,expiry:i};this.expirations.set(s,o),this.checkExpiry(s,o),this.events.emit(dt.created,{target:s,expiration:o})}),qe(this,"get",n=>{this.isInitialized();const i=this.formatTarget(n);return this.getExpiration(i)}),qe(this,"del",n=>{if(this.isInitialized(),this.has(n)){const i=this.formatTarget(n),s=this.getExpiration(i);this.expirations.delete(i),this.events.emit(dt.deleted,{target:i,expiration:s})}}),qe(this,"on",(n,i)=>{this.events.on(n,i)}),qe(this,"once",(n,i)=>{this.events.once(n,i)}),qe(this,"off",(n,i)=>{this.events.off(n,i)}),qe(this,"removeListener",(n,i)=>{this.events.removeListener(n,i)}),this.logger=ke(r,this.name)}get context(){return Ge(this.logger)}get storageKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//"+this.name}get length(){return this.expirations.size}get keys(){return Array.from(this.expirations.keys())}get values(){return Array.from(this.expirations.values())}formatTarget(e){if(typeof e=="string")return R2(e);if(typeof e=="number")return T2(e);const{message:r}=U("UNKNOWN_TYPE",`Target type: ${typeof e}`);throw new Error(r)}async setExpirations(e){await this.core.storage.setItem(this.storageKey,e)}async getExpirations(){return await this.core.storage.getItem(this.storageKey)}async persist(){await this.setExpirations(this.values),this.events.emit(dt.sync)}async restore(){try{const e=await this.getExpirations();if(typeof e>"u"||!e.length)return;if(this.expirations.size){const{message:r}=U("RESTORE_WILL_OVERRIDE",this.name);throw this.logger.error(r),new Error(r)}this.cached=e,this.logger.debug(`Successfully Restored expirations for ${this.name}`),this.logger.trace({type:"method",method:"restore",expirations:this.values})}catch(e){this.logger.debug(`Failed to Restore expirations for ${this.name}`),this.logger.error(e)}}getExpiration(e){const r=this.expirations.get(e);if(!r){const{message:n}=U("NO_MATCHING_KEY",`${this.name}: ${e}`);throw this.logger.warn(n),new Error(n)}return r}checkExpiry(e,r){const{expiry:n}=r;L.toMiliseconds(n)-Date.now()<=0&&this.expire(e,r)}expire(e,r){this.expirations.delete(e),this.events.emit(dt.expired,{target:e,expiration:r})}checkExpirations(){this.core.relayer.connected&&this.expirations.forEach((e,r)=>this.checkExpiry(r,e))}registerEventListeners(){this.core.heartbeat.on(Sr.pulse,()=>this.checkExpirations()),this.events.on(dt.created,e=>{const r=dt.created;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()}),this.events.on(dt.expired,e=>{const r=dt.expired;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()}),this.events.on(dt.deleted,e=>{const r=dt.deleted;this.logger.info(`Emitting ${r}`),this.logger.debug({type:"event",event:r,data:e}),this.persist()})}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}}var f$=Object.defineProperty,p$=(t,e,r)=>e in t?f$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,me=(t,e,r)=>p$(t,typeof e!="symbol"?e+"":e,r);class g$ extends am{constructor(e,r,n){super(e,r,n),this.core=e,this.logger=r,this.store=n,me(this,"name",yO),me(this,"abortController"),me(this,"isDevEnv"),me(this,"verifyUrlV3",wO),me(this,"storagePrefix",Tt),me(this,"version",Af),me(this,"publicKey"),me(this,"fetchPromise"),me(this,"init",async()=>{var i;this.isDevEnv||(this.publicKey=await this.store.getItem(this.storeKey),this.publicKey&&L.toMiliseconds((i=this.publicKey)==null?void 0:i.expiresAt)<Date.now()&&(this.logger.debug("verify v2 public key expired"),await this.removePublicKey()))}),me(this,"register",async i=>{if(!fn()||this.isDevEnv)return;const s=window.location.origin,{id:o,decryptedId:a}=i,c=`${this.verifyUrlV3}/attestation?projectId=${this.core.projectId}&origin=${s}&id=${o}&decryptedId=${a}`;try{const u=Ar(),l=this.startAbortTimer(L.ONE_SECOND*5),h=await new Promise((d,f)=>{const p=()=>{window.removeEventListener("message",g),u.body.removeChild(y),f("attestation aborted")};this.abortController.signal.addEventListener("abort",p);const y=u.createElement("iframe");y.src=c,y.style.display="none",y.addEventListener("error",p,{signal:this.abortController.signal});const g=v=>{if(v.data&&typeof v.data=="string")try{const b=JSON.parse(v.data);if(b.type==="verify_attestation"){if(Oo(b.attestation).payload.id!==o)return;clearInterval(l),u.body.removeChild(y),this.abortController.signal.removeEventListener("abort",p),window.removeEventListener("message",g),d(b.attestation===null?"":b.attestation)}}catch(b){this.logger.warn(b)}};u.body.appendChild(y),window.addEventListener("message",g,{signal:this.abortController.signal})});return this.logger.debug("jwt attestation",h),h}catch(u){this.logger.warn(u)}return""}),me(this,"resolve",async i=>{if(this.isDevEnv)return"";const{attestationId:s,hash:o,encryptedId:a}=i;if(s===""){this.logger.debug("resolve: attestationId is empty, skipping");return}if(s){if(Oo(s).payload.id!==a)return;const u=await this.isValidJwtAttestation(s);if(u){if(!u.isVerified){this.logger.warn("resolve: jwt attestation: origin url not verified");return}return u}}if(!o)return;const c=this.getVerifyUrl(i?.verifyUrl);return this.fetchAttestation(o,c)}),me(this,"fetchAttestation",async(i,s)=>{this.logger.debug(`resolving attestation: ${i} from url: ${s}`);const o=this.startAbortTimer(L.ONE_SECOND*5),a=await fetch(`${s}/attestation/${i}?v2Supported=true`,{signal:this.abortController.signal});return clearTimeout(o),a.status===200?await a.json():void 0}),me(this,"getVerifyUrl",i=>{let s=i||Oi;return bO.includes(s)||(this.logger.info(`verify url: ${s}, not included in trusted list, assigning default: ${Oi}`),s=Oi),s}),me(this,"fetchPublicKey",async()=>{try{this.logger.debug(`fetching public key from: ${this.verifyUrlV3}`);const i=this.startAbortTimer(L.FIVE_SECONDS),s=await fetch(`${this.verifyUrlV3}/public-key`,{signal:this.abortController.signal});return clearTimeout(i),await s.json()}catch(i){this.logger.warn(i)}}),me(this,"persistPublicKey",async i=>{this.logger.debug("persisting public key to local storage",i),await this.store.setItem(this.storeKey,i),this.publicKey=i}),me(this,"removePublicKey",async()=>{this.logger.debug("removing verify v2 public key from storage"),await this.store.removeItem(this.storeKey),this.publicKey=void 0}),me(this,"isValidJwtAttestation",async i=>{const s=await this.getPublicKey();try{if(s)return this.validateAttestation(i,s)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}const o=await this.fetchAndPersistPublicKey();try{if(o)return this.validateAttestation(i,o)}catch(a){this.logger.error(a),this.logger.warn("error validating attestation")}}),me(this,"getPublicKey",async()=>this.publicKey?this.publicKey:await this.fetchAndPersistPublicKey()),me(this,"fetchAndPersistPublicKey",async()=>{if(this.fetchPromise)return await this.fetchPromise,this.publicKey;this.fetchPromise=new Promise(async s=>{const o=await this.fetchPublicKey();o&&(await this.persistPublicKey(o),s(o))});const i=await this.fetchPromise;return this.fetchPromise=void 0,i}),me(this,"validateAttestation",(i,s)=>{const o=aS(i,s.publicKey),a={hasExpired:L.toMiliseconds(o.exp)<Date.now(),payload:o};if(a.hasExpired)throw this.logger.warn("resolve: jwt attestation expired"),new Error("JWT attestation expired");return{origin:a.payload.origin,isScam:a.payload.isScam,isVerified:a.payload.isVerified}}),this.logger=ke(r,this.name),this.abortController=new AbortController,this.isDevEnv=ha(),this.init()}get storeKey(){return this.storagePrefix+this.version+this.core.customStoragePrefix+"//verify:public:key"}get context(){return Ge(this.logger)}startAbortTimer(e){return this.abortController=new AbortController,setTimeout(()=>this.abortController.abort(),L.toMiliseconds(e))}}var y$=Object.defineProperty,m$=(t,e,r)=>e in t?y$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,up=(t,e,r)=>m$(t,typeof e!="symbol"?e+"":e,r);class w$ extends cm{constructor(e,r){super(e,r),this.projectId=e,this.logger=r,up(this,"context",vO),up(this,"registerDeviceToken",async n=>{const{clientId:i,token:s,notificationType:o,enableEncrypted:a=!1}=n,c=`${EO}/${this.projectId}/clients`;await fetch(c,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({client_id:i,type:o,token:s,always_raw:a})})}),this.logger=ke(r,this.context)}}var b$=Object.defineProperty,lp=Object.getOwnPropertySymbols,v$=Object.prototype.hasOwnProperty,E$=Object.prototype.propertyIsEnumerable,sc=(t,e,r)=>e in t?b$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ni=(t,e)=>{for(var r in e||(e={}))v$.call(e,r)&&sc(t,r,e[r]);if(lp)for(var r of lp(e))E$.call(e,r)&&sc(t,r,e[r]);return t},$e=(t,e,r)=>sc(t,typeof e!="symbol"?e+"":e,r);class _$ extends um{constructor(e,r,n=!0){super(e,r,n),this.core=e,this.logger=r,$e(this,"context",IO),$e(this,"storagePrefix",Tt),$e(this,"storageVersion",_O),$e(this,"events",new Map),$e(this,"shouldPersist",!1),$e(this,"init",async()=>{if(!ha())try{const i={eventId:Hh(),timestamp:Date.now(),domain:this.getAppDomain(),props:{event:"INIT",type:"",properties:{client_id:await this.core.crypto.getClientId(),user_agent:qh(this.core.relayer.protocol,this.core.relayer.version,Wa)}}};await this.sendEvent([i])}catch(i){this.logger.warn(i)}}),$e(this,"createEvent",i=>{const{event:s="ERROR",type:o="",properties:{topic:a,trace:c}}=i,u=Hh(),l=this.core.projectId||"",h=Date.now(),d=Ni({eventId:u,timestamp:h,props:{event:s,type:o,properties:{topic:a,trace:c}},bundleId:l,domain:this.getAppDomain()},this.setMethods(u));return this.telemetryEnabled&&(this.events.set(u,d),this.shouldPersist=!0),d}),$e(this,"getEvent",i=>{const{eventId:s,topic:o}=i;if(s)return this.events.get(s);const a=Array.from(this.events.values()).find(c=>c.props.properties.topic===o);if(a)return Ni(Ni({},a),this.setMethods(a.eventId))}),$e(this,"deleteEvent",i=>{const{eventId:s}=i;this.events.delete(s),this.shouldPersist=!0}),$e(this,"setEventListeners",()=>{this.core.heartbeat.on(Sr.pulse,async()=>{this.shouldPersist&&await this.persist(),this.events.forEach(i=>{L.fromMiliseconds(Date.now())-L.fromMiliseconds(i.timestamp)>SO&&(this.events.delete(i.eventId),this.shouldPersist=!0)})})}),$e(this,"setMethods",i=>({addTrace:s=>this.addTrace(i,s),setError:s=>this.setError(i,s)})),$e(this,"addTrace",(i,s)=>{const o=this.events.get(i);o&&(o.props.properties.trace.push(s),this.events.set(i,o),this.shouldPersist=!0)}),$e(this,"setError",(i,s)=>{const o=this.events.get(i);o&&(o.props.type=s,o.timestamp=Date.now(),this.events.set(i,o),this.shouldPersist=!0)}),$e(this,"persist",async()=>{await this.core.storage.setItem(this.storageKey,Array.from(this.events.values())),this.shouldPersist=!1}),$e(this,"restore",async()=>{try{const i=await this.core.storage.getItem(this.storageKey)||[];if(!i.length)return;i.forEach(s=>{this.events.set(s.eventId,Ni(Ni({},s),this.setMethods(s.eventId)))})}catch(i){this.logger.warn(i)}}),$e(this,"submit",async()=>{if(!this.telemetryEnabled||this.events.size===0)return;const i=[];for(const[s,o]of this.events)o.props.type&&i.push(o);if(i.length!==0)try{if((await this.sendEvent(i)).ok)for(const s of i)this.events.delete(s.eventId),this.shouldPersist=!0}catch(s){this.logger.warn(s)}}),$e(this,"sendEvent",async i=>{const s=this.getAppDomain()?"":"&sp=desktop";return await fetch(`${xO}?projectId=${this.core.projectId}&st=events_sdk&sv=js-${Wa}${s}`,{method:"POST",body:JSON.stringify(i)})}),$e(this,"getAppDomain",()=>jh().url),this.logger=ke(r,this.context),this.telemetryEnabled=n,n?this.restore().then(async()=>{await this.submit(),this.setEventListeners()}):this.persist()}get storageKey(){return this.storagePrefix+this.storageVersion+this.core.customStoragePrefix+"//"+this.context}}var I$=Object.defineProperty,hp=Object.getOwnPropertySymbols,S$=Object.prototype.hasOwnProperty,x$=Object.prototype.propertyIsEnumerable,oc=(t,e,r)=>e in t?I$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,dp=(t,e)=>{for(var r in e||(e={}))S$.call(e,r)&&oc(t,r,e[r]);if(hp)for(var r of hp(e))x$.call(e,r)&&oc(t,r,e[r]);return t},ce=(t,e,r)=>oc(t,typeof e!="symbol"?e+"":e,r);class ac extends Zy{constructor(e){var r;super(e),ce(this,"protocol",$f),ce(this,"version",Af),ce(this,"name",Rs),ce(this,"relayUrl"),ce(this,"projectId"),ce(this,"customStoragePrefix"),ce(this,"events",new Ue.exports.EventEmitter),ce(this,"logger"),ce(this,"heartbeat"),ce(this,"relayer"),ce(this,"crypto"),ce(this,"storage"),ce(this,"history"),ce(this,"expirer"),ce(this,"pairing"),ce(this,"verify"),ce(this,"echoClient"),ce(this,"linkModeSupportedApps"),ce(this,"eventClient"),ce(this,"initialized",!1),ce(this,"logChunkController"),ce(this,"on",(a,c)=>this.events.on(a,c)),ce(this,"once",(a,c)=>this.events.once(a,c)),ce(this,"off",(a,c)=>this.events.off(a,c)),ce(this,"removeListener",(a,c)=>this.events.removeListener(a,c)),ce(this,"dispatchEnvelope",({topic:a,message:c,sessionExists:u})=>{if(!a||!c)return;const l={topic:a,message:c,publishedAt:Date.now(),transportType:ue.link_mode};this.relayer.onLinkMessageEvent(l,{sessionExists:u})});const n=this.getGlobalCore(e?.customStoragePrefix);if(n)try{return this.customStoragePrefix=n.customStoragePrefix,this.logger=n.logger,this.heartbeat=n.heartbeat,this.crypto=n.crypto,this.history=n.history,this.expirer=n.expirer,this.storage=n.storage,this.relayer=n.relayer,this.pairing=n.pairing,this.verify=n.verify,this.echoClient=n.echoClient,this.linkModeSupportedApps=n.linkModeSupportedApps,this.eventClient=n.eventClient,this.initialized=n.initialized,this.logChunkController=n.logChunkController,n}catch(a){console.warn("Failed to copy global core",a)}this.projectId=e?.projectId,this.relayUrl=e?.relayUrl||Rf,this.customStoragePrefix=e!=null&&e.customStoragePrefix?`:${e.customStoragePrefix}`:"";const i=Hi({level:typeof e?.logger=="string"&&e.logger?e.logger:Wx.logger,name:Rs}),{logger:s,chunkLoggerController:o}=Wy({opts:i,maxSizeInBytes:e?.maxLogBlobSizeInBytes,loggerOverride:e?.logger});this.logChunkController=o,(r=this.logChunkController)!=null&&r.downloadLogsBlobInBrowser&&(window.downloadLogsBlobInBrowser=async()=>{var a,c;(a=this.logChunkController)!=null&&a.downloadLogsBlobInBrowser&&((c=this.logChunkController)==null||c.downloadLogsBlobInBrowser({clientId:await this.crypto.getClientId()}))}),this.logger=ke(s,this.name),this.heartbeat=new Xs,this.crypto=new Q3(this,this.logger,e?.keychain),this.history=new u$(this,this.logger),this.expirer=new d$(this,this.logger),this.storage=e!=null&&e.storage?e.storage:new by(dp(dp({},Gx),e?.storageOptions)),this.relayer=new OD({core:this,logger:this.logger,relayUrl:this.relayUrl,projectId:this.projectId}),this.pairing=new o$(this,this.logger),this.verify=new g$(this,this.logger,this.storage),this.echoClient=new w$(this.projectId||"",this.logger),this.linkModeSupportedApps=[],this.eventClient=new _$(this,this.logger,e?.telemetryEnabled),this.setGlobalCore(this)}static async init(e){const r=new ac(e);await r.initialize();const n=await r.crypto.getClientId();return await r.storage.setItem(aO,n),r}get context(){return Ge(this.logger)}async start(){this.initialized||await this.initialize()}async getLogsBlob(){var e;return(e=this.logChunkController)==null?void 0:e.logsToBlob({clientId:await this.crypto.getClientId()})}async addLinkModeSupportedApp(e){this.linkModeSupportedApps.includes(e)||(this.linkModeSupportedApps.push(e),await this.storage.setItem(Tf,this.linkModeSupportedApps))}async initialize(){this.logger.trace("Initialized");try{await this.crypto.init(),await this.history.init(),await this.expirer.init(),await this.relayer.init(),await this.heartbeat.init(),await this.pairing.init(),this.linkModeSupportedApps=await this.storage.getItem(Tf)||[],this.initialized=!0,this.logger.info("Core Initialization Success")}catch(e){throw this.logger.warn(`Core Initialization Failure at epoch ${Date.now()}`,e),this.logger.error(e.message),e}}getGlobalCore(e=""){try{if(this.isGlobalCoreDisabled())return;const r=`_walletConnectCore_${e}`,n=`${r}_count`;return globalThis[n]=(globalThis[n]||0)+1,globalThis[n]>1&&console.warn(`WalletConnect Core is already initialized. This is probably a mistake and can lead to unexpected behavior. Init() was called ${globalThis[n]} times.`),globalThis[r]}catch(r){console.warn("Failed to get global WalletConnect core",r);return}}setGlobalCore(e){var r;try{if(this.isGlobalCoreDisabled())return;const n=`_walletConnectCore_${((r=e.opts)==null?void 0:r.customStoragePrefix)||""}`;globalThis[n]=e}catch(n){console.warn("Failed to set global WalletConnect core",n)}}isGlobalCoreDisabled(){try{return typeof process<"u"&&process.env.DISABLE_GLOBAL_CORE==="true"}catch{return!0}}}const O$=ac,fp="wc",pp=2,gp="client",cc=`${fp}@${pp}:${gp}:`,uc={name:gp,logger:"error",controller:!1,relayUrl:"wss://relay.walletconnect.org"},yp="WALLETCONNECT_DEEPLINK_CHOICE",D$="proposal",mp="Proposal expired",$$="session",An=L.SEVEN_DAYS,A$="engine",Ae={wc_sessionPropose:{req:{ttl:L.FIVE_MINUTES,prompt:!0,tag:1100},res:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1101},reject:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1120},autoReject:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1121}},wc_sessionSettle:{req:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1102},res:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1103}},wc_sessionUpdate:{req:{ttl:L.ONE_DAY,prompt:!1,tag:1104},res:{ttl:L.ONE_DAY,prompt:!1,tag:1105}},wc_sessionExtend:{req:{ttl:L.ONE_DAY,prompt:!1,tag:1106},res:{ttl:L.ONE_DAY,prompt:!1,tag:1107}},wc_sessionRequest:{req:{ttl:L.FIVE_MINUTES,prompt:!0,tag:1108},res:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1109}},wc_sessionEvent:{req:{ttl:L.FIVE_MINUTES,prompt:!0,tag:1110},res:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1111}},wc_sessionDelete:{req:{ttl:L.ONE_DAY,prompt:!1,tag:1112},res:{ttl:L.ONE_DAY,prompt:!1,tag:1113}},wc_sessionPing:{req:{ttl:L.ONE_DAY,prompt:!1,tag:1114},res:{ttl:L.ONE_DAY,prompt:!1,tag:1115}},wc_sessionAuthenticate:{req:{ttl:L.ONE_HOUR,prompt:!0,tag:1116},res:{ttl:L.ONE_HOUR,prompt:!1,tag:1117},reject:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1118},autoReject:{ttl:L.FIVE_MINUTES,prompt:!1,tag:1119}}},lc={min:L.FIVE_MINUTES,max:L.SEVEN_DAYS},Lt={idle:"IDLE",active:"ACTIVE"},wp={eth_sendTransaction:{key:""},eth_sendRawTransaction:{key:""},wallet_sendCalls:{key:""},solana_signTransaction:{key:"signature"},solana_signAllTransactions:{key:"transactions"},solana_signAndSendTransaction:{key:"signature"}},P$="request",C$=["wc_sessionPropose","wc_sessionRequest","wc_authRequest","wc_sessionAuthenticate"],N$="wc",R$="auth",T$="authKeys",B$="pairingTopics",F$="requests",Ls=`${N$}@${1.5}:${R$}:`,Us=`${Ls}:PUB_KEY`;var L$=Object.defineProperty,U$=Object.defineProperties,j$=Object.getOwnPropertyDescriptors,bp=Object.getOwnPropertySymbols,q$=Object.prototype.hasOwnProperty,k$=Object.prototype.propertyIsEnumerable,hc=(t,e,r)=>e in t?L$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,oe=(t,e)=>{for(var r in e||(e={}))q$.call(e,r)&&hc(t,r,e[r]);if(bp)for(var r of bp(e))k$.call(e,r)&&hc(t,r,e[r]);return t},Ve=(t,e)=>U$(t,j$(e)),B=(t,e,r)=>hc(t,typeof e!="symbol"?e+"":e,r);class M$ extends fm{constructor(e){super(e),B(this,"name",A$),B(this,"events",new Ue.exports),B(this,"initialized",!1),B(this,"requestQueue",{state:Lt.idle,queue:[]}),B(this,"sessionRequestQueue",{state:Lt.idle,queue:[]}),B(this,"requestQueueDelay",L.ONE_SECOND),B(this,"expectedPairingMethodMap",new Map),B(this,"recentlyDeletedMap",new Map),B(this,"recentlyDeletedLimit",200),B(this,"relayMessageCache",[]),B(this,"pendingSessions",new Map),B(this,"init",async()=>{this.initialized||(await this.cleanup(),this.registerRelayerEvents(),this.registerExpirerEvents(),this.registerPairingEvents(),await this.registerLinkModeListeners(),this.client.core.pairing.register({methods:Object.keys(Ae)}),this.initialized=!0,setTimeout(async()=>{await this.processPendingMessageEvents(),this.sessionRequestQueue.queue=this.getPendingSessionRequests(),this.processSessionRequestQueue()},L.toMiliseconds(this.requestQueueDelay)))}),B(this,"connect",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();const n=Ve(oe({},r),{requiredNamespaces:r.requiredNamespaces||{},optionalNamespaces:r.optionalNamespaces||{}});await this.isValidConnect(n),n.optionalNamespaces=IS(n.requiredNamespaces,n.optionalNamespaces),n.requiredNamespaces={};const{pairingTopic:i,requiredNamespaces:s,optionalNamespaces:o,sessionProperties:a,scopedProperties:c,relays:u}=n;let l=i,h,d=!1;try{if(l){const $=this.client.core.pairing.pairings.get(l);this.client.logger.warn("connect() with existing pairing topic is deprecated and will be removed in the next major release."),d=$.active}}catch($){throw this.client.logger.error(`connect() -> pairing.get(${l}) failed`),$}if(!l||!d){const{topic:$,uri:O}=await this.client.core.pairing.create();l=$,h=O}if(!l){const{message:$}=U("NO_MATCHING_KEY",`connect() pairing topic: ${l}`);throw new Error($)}const f=await this.client.core.crypto.generateKeyPair(),p=Ae.wc_sessionPropose.req.ttl||L.FIVE_MINUTES,y=ve(p),g=Ve(oe(oe({requiredNamespaces:s,optionalNamespaces:o,relays:u??[{protocol:Nf}],proposer:{publicKey:f,metadata:this.client.metadata},expiryTimestamp:y,pairingTopic:l},a&&{sessionProperties:a}),c&&{scopedProperties:c}),{id:Nt()}),v=te("session_connect",g.id),{reject:b,resolve:E,done:_}=Tr(p,mp),P=({id:$})=>{$===g.id&&(this.client.events.off("proposal_expire",P),this.pendingSessions.delete(g.id),this.events.emit(v,{error:{message:mp,code:0}}))};return this.client.events.on("proposal_expire",P),this.events.once(v,({error:$,session:O})=>{this.client.events.off("proposal_expire",P),$?b($):O&&E(O)}),await this.sendRequest({topic:l,method:"wc_sessionPropose",params:g,throwOnFailedPublish:!0,clientRpcId:g.id}),await this.setProposal(g.id,g),{uri:h,approval:_}}),B(this,"pair",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{return await this.client.core.pairing.pair(r)}catch(n){throw this.client.logger.error("pair() failed"),n}}),B(this,"approve",async r=>{var n,i,s;const o=this.client.core.eventClient.createEvent({properties:{topic:(n=r?.id)==null?void 0:n.toString(),trace:[vt.session_approve_started]}});try{this.isInitialized(),await this.confirmOnlineStateOrThrow()}catch(S){throw o.setError(Mr.no_internet_connection),S}try{await this.isValidProposalId(r?.id)}catch(S){throw this.client.logger.error(`approve() -> proposal.get(${r?.id}) failed`),o.setError(Mr.proposal_not_found),S}try{await this.isValidApprove(r)}catch(S){throw this.client.logger.error("approve() -> isValidApprove() failed"),o.setError(Mr.session_approve_namespace_validation_failure),S}const{id:a,relayProtocol:c,namespaces:u,sessionProperties:l,scopedProperties:h,sessionConfig:d}=r,f=this.client.proposal.get(a);this.client.core.eventClient.deleteEvent({eventId:o.eventId});const{pairingTopic:p,proposer:y,requiredNamespaces:g,optionalNamespaces:v}=f;let b=(i=this.client.core.eventClient)==null?void 0:i.getEvent({topic:p});b||(b=(s=this.client.core.eventClient)==null?void 0:s.createEvent({type:vt.session_approve_started,properties:{topic:p,trace:[vt.session_approve_started,vt.session_namespaces_validation_success]}}));const E=await this.client.core.crypto.generateKeyPair(),_=y.publicKey,P=await this.client.core.crypto.generateSharedKey(E,_),$=oe(oe(oe({relay:{protocol:c??"irn"},namespaces:u,controller:{publicKey:E,metadata:this.client.metadata},expiry:ve(An)},l&&{sessionProperties:l}),h&&{scopedProperties:h}),d&&{sessionConfig:d}),O=ue.relay;b.addTrace(vt.subscribing_session_topic);try{await this.client.core.relayer.subscribe(P,{transportType:O})}catch(S){throw b.setError(Mr.subscribe_session_topic_failure),S}b.addTrace(vt.subscribe_session_topic_success);const C=Ve(oe({},$),{topic:P,requiredNamespaces:g,optionalNamespaces:v,pairingTopic:p,acknowledged:!1,self:$.controller,peer:{publicKey:y.publicKey,metadata:y.metadata},controller:E,transportType:ue.relay});await this.client.session.set(P,C),b.addTrace(vt.store_session);try{b.addTrace(vt.publishing_session_settle),await this.sendRequest({topic:P,method:"wc_sessionSettle",params:$,throwOnFailedPublish:!0}).catch(S=>{throw b?.setError(Mr.session_settle_publish_failure),S}),b.addTrace(vt.session_settle_publish_success),b.addTrace(vt.publishing_session_approve),await this.sendResult({id:a,topic:p,result:{relay:{protocol:c??"irn"},responderPublicKey:E},throwOnFailedPublish:!0}).catch(S=>{throw b?.setError(Mr.session_approve_publish_failure),S}),b.addTrace(vt.session_approve_publish_success)}catch(S){throw this.client.logger.error(S),this.client.session.delete(P,ie("USER_DISCONNECTED")),await this.client.core.relayer.unsubscribe(P),S}return this.client.core.eventClient.deleteEvent({eventId:b.eventId}),await this.client.core.pairing.updateMetadata({topic:p,metadata:y.metadata}),await this.client.proposal.delete(a,ie("USER_DISCONNECTED")),await this.client.core.pairing.activate({topic:p}),await this.setExpiry(P,ve(An)),{topic:P,acknowledged:()=>Promise.resolve(this.client.session.get(P))}}),B(this,"reject",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidReject(r)}catch(o){throw this.client.logger.error("reject() -> isValidReject() failed"),o}const{id:n,reason:i}=r;let s;try{s=this.client.proposal.get(n).pairingTopic}catch(o){throw this.client.logger.error(`reject() -> proposal.get(${n}) failed`),o}s&&(await this.sendError({id:n,topic:s,error:i,rpcOpts:Ae.wc_sessionPropose.reject}),await this.client.proposal.delete(n,ie("USER_DISCONNECTED")))}),B(this,"update",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidUpdate(r)}catch(h){throw this.client.logger.error("update() -> isValidUpdate() failed"),h}const{topic:n,namespaces:i}=r,{done:s,resolve:o,reject:a}=Tr(),c=Nt(),u=qr().toString(),l=this.client.session.get(n).namespaces;return this.events.once(te("session_update",c),({error:h})=>{h?a(h):o()}),await this.client.session.update(n,{namespaces:i}),await this.sendRequest({topic:n,method:"wc_sessionUpdate",params:{namespaces:i},throwOnFailedPublish:!0,clientRpcId:c,relayRpcId:u}).catch(h=>{this.client.logger.error(h),this.client.session.update(n,{namespaces:l}),a(h)}),{acknowledged:s}}),B(this,"extend",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidExtend(r)}catch(c){throw this.client.logger.error("extend() -> isValidExtend() failed"),c}const{topic:n}=r,i=Nt(),{done:s,resolve:o,reject:a}=Tr();return this.events.once(te("session_extend",i),({error:c})=>{c?a(c):o()}),await this.setExpiry(n,ve(An)),this.sendRequest({topic:n,method:"wc_sessionExtend",params:{},clientRpcId:i,throwOnFailedPublish:!0}).catch(c=>{a(c)}),{acknowledged:s}}),B(this,"request",async r=>{this.isInitialized();try{await this.isValidRequest(r)}catch(v){throw this.client.logger.error("request() -> isValidRequest() failed"),v}const{chainId:n,request:i,topic:s,expiry:o=Ae.wc_sessionRequest.req.ttl}=r,a=this.client.session.get(s);a?.transportType===ue.relay&&await this.confirmOnlineStateOrThrow();const c=Nt(),u=qr().toString(),{done:l,resolve:h,reject:d}=Tr(o,"Request expired. Please try again.");this.events.once(te("session_request",c),({error:v,result:b})=>{v?d(v):h(b)});const f="wc_sessionRequest",p=this.getAppLinkIfEnabled(a.peer.metadata,a.transportType);if(p)return await this.sendRequest({clientRpcId:c,relayRpcId:u,topic:s,method:f,params:{request:Ve(oe({},i),{expiryTimestamp:ve(o)}),chainId:n},expiry:o,throwOnFailedPublish:!0,appLink:p}).catch(v=>d(v)),this.client.events.emit("session_request_sent",{topic:s,request:i,chainId:n,id:c}),await l();const y={request:Ve(oe({},i),{expiryTimestamp:ve(o)}),chainId:n},g=this.shouldSetTVF(f,y);return await Promise.all([new Promise(async v=>{await this.sendRequest(oe({clientRpcId:c,relayRpcId:u,topic:s,method:f,params:y,expiry:o,throwOnFailedPublish:!0},g&&{tvf:this.getTVFParams(c,y)})).catch(b=>d(b)),this.client.events.emit("session_request_sent",{topic:s,request:i,chainId:n,id:c}),v()}),new Promise(async v=>{var b;if(!((b=a.sessionConfig)!=null&&b.disableDeepLink)){const E=await U2(this.client.core.storage,yp);await B2({id:c,topic:s,wcDeepLink:E})}v()}),l()]).then(v=>v[2])}),B(this,"respond",async r=>{this.isInitialized(),await this.isValidRespond(r);const{topic:n,response:i}=r,{id:s}=i,o=this.client.session.get(n);o.transportType===ue.relay&&await this.confirmOnlineStateOrThrow();const a=this.getAppLinkIfEnabled(o.peer.metadata,o.transportType);Rt(i)?await this.sendResult({id:s,topic:n,result:i.result,throwOnFailedPublish:!0,appLink:a}):ut(i)&&await this.sendError({id:s,topic:n,error:i.error,appLink:a}),this.cleanupAfterResponse(r)}),B(this,"ping",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow();try{await this.isValidPing(r)}catch(i){throw this.client.logger.error("ping() -> isValidPing() failed"),i}const{topic:n}=r;if(this.client.session.keys.includes(n)){const i=Nt(),s=qr().toString(),{done:o,resolve:a,reject:c}=Tr();this.events.once(te("session_ping",i),({error:u})=>{u?c(u):a()}),await Promise.all([this.sendRequest({topic:n,method:"wc_sessionPing",params:{},throwOnFailedPublish:!0,clientRpcId:i,relayRpcId:s}),o()])}else this.client.core.pairing.pairings.keys.includes(n)&&(this.client.logger.warn("ping() on pairing topic is deprecated and will be removed in the next major release."),await this.client.core.pairing.ping({topic:n}))}),B(this,"emit",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow(),await this.isValidEmit(r);const{topic:n,event:i,chainId:s}=r,o=qr().toString(),a=Nt();await this.sendRequest({topic:n,method:"wc_sessionEvent",params:{event:i,chainId:s},throwOnFailedPublish:!0,relayRpcId:o,clientRpcId:a})}),B(this,"disconnect",async r=>{this.isInitialized(),await this.confirmOnlineStateOrThrow(),await this.isValidDisconnect(r);const{topic:n}=r;if(this.client.session.keys.includes(n))await this.sendRequest({topic:n,method:"wc_sessionDelete",params:ie("USER_DISCONNECTED"),throwOnFailedPublish:!0}),await this.deleteSession({topic:n,emitEvent:!1});else if(this.client.core.pairing.pairings.keys.includes(n))await this.client.core.pairing.disconnect({topic:n});else{const{message:i}=U("MISMATCHED_TOPIC",`Session or pairing topic not found: ${n}`);throw new Error(i)}}),B(this,"find",r=>(this.isInitialized(),this.client.session.getAll().filter(n=>OS(n,r)))),B(this,"getPendingSessionRequests",()=>this.client.pendingRequest.getAll()),B(this,"authenticate",async(r,n)=>{var i;this.isInitialized(),this.isValidAuthenticate(r);const s=n&&this.client.core.linkModeSupportedApps.includes(n)&&((i=this.client.metadata.redirect)==null?void 0:i.linkMode),o=s?ue.link_mode:ue.relay;o===ue.relay&&await this.confirmOnlineStateOrThrow();const{chains:a,statement:c="",uri:u,domain:l,nonce:h,type:d,exp:f,nbf:p,methods:y=[],expiry:g}=r,v=[...r.resources||[]],{topic:b,uri:E}=await this.client.core.pairing.create({methods:["wc_sessionAuthenticate"],transportType:o});this.client.logger.info({message:"Generated new pairing",pairing:{topic:b,uri:E}});const _=await this.client.core.crypto.generateKeyPair(),P=xs(_);if(await Promise.all([this.client.auth.authKeys.set(Us,{responseTopic:P,publicKey:_}),this.client.auth.pairingTopics.set(P,{topic:P,pairingTopic:b})]),await this.client.core.relayer.subscribe(P,{transportType:o}),this.client.logger.info(`sending request to new pairing topic: ${b}`),y.length>0){const{namespace:x}=dn(a[0]);let N=A_(x,"request",y);ms(v)&&(N=C_(N,v.pop())),v.push(N)}const $=g&&g>Ae.wc_sessionAuthenticate.req.ttl?g:Ae.wc_sessionAuthenticate.req.ttl,O={authPayload:{type:d??"caip122",chains:a,statement:c,aud:u,domain:l,version:"1",nonce:h,iat:new Date().toISOString(),exp:f,nbf:p,resources:v},requester:{publicKey:_,metadata:this.client.metadata},expiryTimestamp:ve($)},C={eip155:{chains:a,methods:[...new Set(["personal_sign",...y])],events:["chainChanged","accountsChanged"]}},S={requiredNamespaces:{},optionalNamespaces:C,relays:[{protocol:"irn"}],pairingTopic:b,proposer:{publicKey:_,metadata:this.client.metadata},expiryTimestamp:ve(Ae.wc_sessionPropose.req.ttl),id:Nt()},{done:k,resolve:T,reject:R}=Tr($,"Request expired"),M=Nt(),D=te("session_connect",S.id),m=te("session_request",M),w=async({error:x,session:N})=>{this.events.off(m,I),x?R(x):N&&T({session:N})},I=async x=>{var N,F,j;if(await this.deletePendingAuthRequest(M,{message:"fulfilled",code:0}),x.error){const W=ie("WC_METHOD_UNSUPPORTED","wc_sessionAuthenticate");return x.error.code===W.code?void 0:(this.events.off(D,w),R(x.error.message))}await this.deleteProposal(S.id),this.events.off(D,w);const{cacaos:z,responder:q}=x.result,H=[],V=[];for(const W of z){await sd({cacao:W,projectId:this.client.core.projectId})||(this.client.logger.error(W,"Signature verification failed"),R(ie("SESSION_SETTLEMENT_FAILED","Signature verification failed")));const{p:ge}=W,le=ms(ge.resources),he=[ma(ge.iss)],Pe=ys(ge.iss);if(le){const we=cd(le),er=ud(le);H.push(...we),he.push(...er)}for(const we of he)V.push(`${we}:${Pe}`)}const ee=await this.client.core.crypto.generateSharedKey(_,q.publicKey);let G;H.length>0&&(G={topic:ee,acknowledged:!0,self:{publicKey:_,metadata:this.client.metadata},peer:q,controller:q.publicKey,expiry:ve(An),requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:b,namespaces:nf([...new Set(H)],[...new Set(V)]),transportType:o},await this.client.core.relayer.subscribe(ee,{transportType:o}),await this.client.session.set(ee,G),b&&await this.client.core.pairing.updateMetadata({topic:b,metadata:q.metadata}),G=this.client.session.get(ee)),(N=this.client.metadata.redirect)!=null&&N.linkMode&&(F=q.metadata.redirect)!=null&&F.linkMode&&(j=q.metadata.redirect)!=null&&j.universal&&n&&(this.client.core.addLinkModeSupportedApp(q.metadata.redirect.universal),this.client.session.update(ee,{transportType:ue.link_mode})),T({auths:z,session:G})};this.events.once(D,w),this.events.once(m,I);let A;try{if(s){const x=br("wc_sessionAuthenticate",O,M);this.client.core.history.set(b,x);const N=await this.client.core.crypto.encode("",x,{type:wi,encoding:yr});A=Ds(n,b,N)}else await Promise.all([this.sendRequest({topic:b,method:"wc_sessionAuthenticate",params:O,expiry:r.expiry,throwOnFailedPublish:!0,clientRpcId:M}),this.sendRequest({topic:b,method:"wc_sessionPropose",params:S,expiry:Ae.wc_sessionPropose.req.ttl,throwOnFailedPublish:!0,clientRpcId:S.id})])}catch(x){throw this.events.off(D,w),this.events.off(m,I),x}return await this.setProposal(S.id,S),await this.setAuthRequest(M,{request:Ve(oe({},O),{verifyContext:{}}),pairingTopic:b,transportType:o}),{uri:A??E,response:k}}),B(this,"approveSessionAuthenticate",async r=>{const{id:n,auths:i}=r,s=this.client.core.eventClient.createEvent({properties:{topic:n.toString(),trace:[zr.authenticated_session_approve_started]}});try{this.isInitialized()}catch(g){throw s.setError(Di.no_internet_connection),g}const o=this.getPendingAuthRequest(n);if(!o)throw s.setError(Di.authenticated_session_pending_request_not_found),new Error(`Could not find pending auth request with id ${n}`);const a=o.transportType||ue.relay;a===ue.relay&&await this.confirmOnlineStateOrThrow();const c=o.requester.publicKey,u=await this.client.core.crypto.generateKeyPair(),l=xs(c),h={type:Zt,receiverPublicKey:c,senderPublicKey:u},d=[],f=[];for(const g of i){if(!await sd({cacao:g,projectId:this.client.core.projectId})){s.setError(Di.invalid_cacao);const P=ie("SESSION_SETTLEMENT_FAILED","Signature verification failed");throw await this.sendError({id:n,topic:l,error:P,encodeOpts:h}),new Error(P.message)}s.addTrace(zr.cacaos_verified);const{p:v}=g,b=ms(v.resources),E=[ma(v.iss)],_=ys(v.iss);if(b){const P=cd(b),$=ud(b);d.push(...P),E.push(...$)}for(const P of E)f.push(`${P}:${_}`)}const p=await this.client.core.crypto.generateSharedKey(u,c);s.addTrace(zr.create_authenticated_session_topic);let y;if(d?.length>0){y={topic:p,acknowledged:!0,self:{publicKey:u,metadata:this.client.metadata},peer:{publicKey:c,metadata:o.requester.metadata},controller:c,expiry:ve(An),authentication:i,requiredNamespaces:{},optionalNamespaces:{},relay:{protocol:"irn"},pairingTopic:o.pairingTopic,namespaces:nf([...new Set(d)],[...new Set(f)]),transportType:a},s.addTrace(zr.subscribing_authenticated_session_topic);try{await this.client.core.relayer.subscribe(p,{transportType:a})}catch(g){throw s.setError(Di.subscribe_authenticated_session_topic_failure),g}s.addTrace(zr.subscribe_authenticated_session_topic_success),await this.client.session.set(p,y),s.addTrace(zr.store_authenticated_session),await this.client.core.pairing.updateMetadata({topic:o.pairingTopic,metadata:o.requester.metadata})}s.addTrace(zr.publishing_authenticated_session_approve);try{await this.sendResult({topic:l,id:n,result:{cacaos:i,responder:{publicKey:u,metadata:this.client.metadata}},encodeOpts:h,throwOnFailedPublish:!0,appLink:this.getAppLinkIfEnabled(o.requester.metadata,a)})}catch(g){throw s.setError(Di.authenticated_session_approve_publish_failure),g}return await this.client.auth.requests.delete(n,{message:"fulfilled",code:0}),await this.client.core.pairing.activate({topic:o.pairingTopic}),this.client.core.eventClient.deleteEvent({eventId:s.eventId}),{session:y}}),B(this,"rejectSessionAuthenticate",async r=>{this.isInitialized();const{id:n,reason:i}=r,s=this.getPendingAuthRequest(n);if(!s)throw new Error(`Could not find pending auth request with id ${n}`);s.transportType===ue.relay&&await this.confirmOnlineStateOrThrow();const o=s.requester.publicKey,a=await this.client.core.crypto.generateKeyPair(),c=xs(o),u={type:Zt,receiverPublicKey:o,senderPublicKey:a};await this.sendError({id:n,topic:c,error:i,encodeOpts:u,rpcOpts:Ae.wc_sessionAuthenticate.reject,appLink:this.getAppLinkIfEnabled(s.requester.metadata,s.transportType)}),await this.client.auth.requests.delete(n,{message:"rejected",code:0}),await this.client.proposal.delete(n,ie("USER_DISCONNECTED"))}),B(this,"formatAuthMessage",r=>{this.isInitialized();const{request:n,iss:i}=r;return od(n,i)}),B(this,"processRelayMessageCache",()=>{setTimeout(async()=>{if(this.relayMessageCache.length!==0)for(;this.relayMessageCache.length>0;)try{const r=this.relayMessageCache.shift();r&&await this.onRelayMessage(r)}catch(r){this.client.logger.error(r)}},50)}),B(this,"cleanupDuplicatePairings",async r=>{if(r.pairingTopic)try{const n=this.client.core.pairing.pairings.get(r.pairingTopic),i=this.client.core.pairing.pairings.getAll().filter(s=>{var o,a;return((o=s.peerMetadata)==null?void 0:o.url)&&((a=s.peerMetadata)==null?void 0:a.url)===r.peer.metadata.url&&s.topic&&s.topic!==n.topic});if(i.length===0)return;this.client.logger.info(`Cleaning up ${i.length} duplicate pairing(s)`),await Promise.all(i.map(s=>this.client.core.pairing.disconnect({topic:s.topic}))),this.client.logger.info("Duplicate pairings clean up finished")}catch(n){this.client.logger.error(n)}}),B(this,"deleteSession",async r=>{var n;const{topic:i,expirerHasDeleted:s=!1,emitEvent:o=!0,id:a=0}=r,{self:c}=this.client.session.get(i);await this.client.core.relayer.unsubscribe(i),await this.client.session.delete(i,ie("USER_DISCONNECTED")),this.addToRecentlyDeleted(i,"session"),this.client.core.crypto.keychain.has(c.publicKey)&&await this.client.core.crypto.deleteKeyPair(c.publicKey),this.client.core.crypto.keychain.has(i)&&await this.client.core.crypto.deleteSymKey(i),s||this.client.core.expirer.del(i),this.client.core.storage.removeItem(yp).catch(u=>this.client.logger.warn(u)),this.getPendingSessionRequests().forEach(u=>{u.topic===i&&this.deletePendingSessionRequest(u.id,ie("USER_DISCONNECTED"))}),i===((n=this.sessionRequestQueue.queue[0])==null?void 0:n.topic)&&(this.sessionRequestQueue.state=Lt.idle),o&&this.client.events.emit("session_delete",{id:a,topic:i})}),B(this,"deleteProposal",async(r,n)=>{if(n)try{const i=this.client.proposal.get(r),s=this.client.core.eventClient.getEvent({topic:i.pairingTopic});s?.setError(Mr.proposal_expired)}catch{}await Promise.all([this.client.proposal.delete(r,ie("USER_DISCONNECTED")),n?Promise.resolve():this.client.core.expirer.del(r)]),this.addToRecentlyDeleted(r,"proposal")}),B(this,"deletePendingSessionRequest",async(r,n,i=!1)=>{await Promise.all([this.client.pendingRequest.delete(r,n),i?Promise.resolve():this.client.core.expirer.del(r)]),this.addToRecentlyDeleted(r,"request"),this.sessionRequestQueue.queue=this.sessionRequestQueue.queue.filter(s=>s.id!==r),i&&(this.sessionRequestQueue.state=Lt.idle,this.client.events.emit("session_request_expire",{id:r}))}),B(this,"deletePendingAuthRequest",async(r,n,i=!1)=>{await Promise.all([this.client.auth.requests.delete(r,n),i?Promise.resolve():this.client.core.expirer.del(r)])}),B(this,"setExpiry",async(r,n)=>{this.client.session.keys.includes(r)&&(this.client.core.expirer.set(r,n),await this.client.session.update(r,{expiry:n}))}),B(this,"setProposal",async(r,n)=>{this.client.core.expirer.set(r,ve(Ae.wc_sessionPropose.req.ttl)),await this.client.proposal.set(r,n)}),B(this,"setAuthRequest",async(r,n)=>{const{request:i,pairingTopic:s,transportType:o=ue.relay}=n;this.client.core.expirer.set(r,i.expiryTimestamp),await this.client.auth.requests.set(r,{authPayload:i.authPayload,requester:i.requester,expiryTimestamp:i.expiryTimestamp,id:r,pairingTopic:s,verifyContext:i.verifyContext,transportType:o})}),B(this,"setPendingSessionRequest",async r=>{const{id:n,topic:i,params:s,verifyContext:o}=r,a=s.request.expiryTimestamp||ve(Ae.wc_sessionRequest.req.ttl);this.client.core.expirer.set(n,a),await this.client.pendingRequest.set(n,{id:n,topic:i,params:s,verifyContext:o})}),B(this,"sendRequest",async r=>{const{topic:n,method:i,params:s,expiry:o,relayRpcId:a,clientRpcId:c,throwOnFailedPublish:u,appLink:l,tvf:h}=r,d=br(i,s,c);let f;const p=!!l;try{const v=p?yr:wt;f=await this.client.core.crypto.encode(n,d,{encoding:v})}catch(v){throw await this.cleanup(),this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${n} failed`),v}let y;if(C$.includes(i)){const v=Ct(JSON.stringify(d)),b=Ct(f);y=await this.client.core.verify.register({id:b,decryptedId:v})}const g=Ae[i].req;if(g.attestation=y,o&&(g.ttl=o),a&&(g.id=a),this.client.core.history.set(n,d),p){const v=Ds(l,n,f);await global.Linking.openURL(v,this.client.name)}else{const v=Ae[i].req;o&&(v.ttl=o),a&&(v.id=a),v.tvf=Ve(oe({},h),{correlationId:d.id}),u?(v.internal=Ve(oe({},v.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(n,f,v)):this.client.core.relayer.publish(n,f,v).catch(b=>this.client.logger.error(b))}return d.id}),B(this,"sendResult",async r=>{const{id:n,topic:i,result:s,throwOnFailedPublish:o,encodeOpts:a,appLink:c}=r,u=Ps(n,s);let l;const h=c&&typeof(global==null?void 0:global.Linking)<"u";try{const p=h?yr:wt;l=await this.client.core.crypto.encode(i,u,Ve(oe({},a||{}),{encoding:p}))}catch(p){throw await this.cleanup(),this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${i} failed`),p}let d,f;try{d=await this.client.core.history.get(i,n);const p=d.request;try{this.shouldSetTVF(p.method,p.params)&&(f=this.getTVFParams(n,p.params,s))}catch(y){this.client.logger.warn("sendResult() -> getTVFParams() failed",y)}}catch(p){throw this.client.logger.error(`sendResult() -> history.get(${i}, ${n}) failed`),p}if(h){const p=Ds(c,i,l);await global.Linking.openURL(p,this.client.name)}else{const p=d.request.method,y=Ae[p].res;y.tvf=Ve(oe({},f),{correlationId:n}),o?(y.internal=Ve(oe({},y.internal),{throwOnFailedPublish:!0}),await this.client.core.relayer.publish(i,l,y)):this.client.core.relayer.publish(i,l,y).catch(g=>this.client.logger.error(g))}await this.client.core.history.resolve(u)}),B(this,"sendError",async r=>{const{id:n,topic:i,error:s,encodeOpts:o,rpcOpts:a,appLink:c}=r,u=Cs(n,s);let l;const h=c&&typeof(global==null?void 0:global.Linking)<"u";try{const f=h?yr:wt;l=await this.client.core.crypto.encode(i,u,Ve(oe({},o||{}),{encoding:f}))}catch(f){throw await this.cleanup(),this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${i} failed`),f}let d;try{d=await this.client.core.history.get(i,n)}catch(f){throw this.client.logger.error(`sendError() -> history.get(${i}, ${n}) failed`),f}if(h){const f=Ds(c,i,l);await global.Linking.openURL(f,this.client.name)}else{const f=d.request.method,p=a||Ae[f].res;this.client.core.relayer.publish(i,l,p)}await this.client.core.history.resolve(u)}),B(this,"cleanup",async()=>{const r=[],n=[];this.client.session.getAll().forEach(i=>{let s=!1;dr(i.expiry)&&(s=!0),this.client.core.crypto.keychain.has(i.topic)||(s=!0),s&&r.push(i.topic)}),this.client.proposal.getAll().forEach(i=>{dr(i.expiryTimestamp)&&n.push(i.id)}),await Promise.all([...r.map(i=>this.deleteSession({topic:i})),...n.map(i=>this.deleteProposal(i))])}),B(this,"onProviderMessageEvent",async r=>{!this.initialized||this.relayMessageCache.length>0?this.relayMessageCache.push(r):await this.onRelayMessage(r)}),B(this,"onRelayEventRequest",async r=>{this.requestQueue.queue.push(r),await this.processRequestsQueue()}),B(this,"processRequestsQueue",async()=>{if(this.requestQueue.state===Lt.active){this.client.logger.info("Request queue already active, skipping...");return}for(this.client.logger.info(`Request queue starting with ${this.requestQueue.queue.length} requests`);this.requestQueue.queue.length>0;){this.requestQueue.state=Lt.active;const r=this.requestQueue.queue.shift();if(r)try{await this.processRequest(r)}catch(n){this.client.logger.warn(n)}}this.requestQueue.state=Lt.idle}),B(this,"processRequest",async r=>{const{topic:n,payload:i,attestation:s,transportType:o,encryptedId:a}=r,c=i.method;if(!this.shouldIgnorePairingRequest({topic:n,requestMethod:c}))switch(c){case"wc_sessionPropose":return await this.onSessionProposeRequest({topic:n,payload:i,attestation:s,encryptedId:a});case"wc_sessionSettle":return await this.onSessionSettleRequest(n,i);case"wc_sessionUpdate":return await this.onSessionUpdateRequest(n,i);case"wc_sessionExtend":return await this.onSessionExtendRequest(n,i);case"wc_sessionPing":return await this.onSessionPingRequest(n,i);case"wc_sessionDelete":return await this.onSessionDeleteRequest(n,i);case"wc_sessionRequest":return await this.onSessionRequest({topic:n,payload:i,attestation:s,encryptedId:a,transportType:o});case"wc_sessionEvent":return await this.onSessionEventRequest(n,i);case"wc_sessionAuthenticate":return await this.onSessionAuthenticateRequest({topic:n,payload:i,attestation:s,encryptedId:a,transportType:o});default:return this.client.logger.info(`Unsupported request method ${c}`)}}),B(this,"onRelayEventResponse",async r=>{const{topic:n,payload:i,transportType:s}=r,o=(await this.client.core.history.get(n,i.id)).request.method;switch(o){case"wc_sessionPropose":return this.onSessionProposeResponse(n,i,s);case"wc_sessionSettle":return this.onSessionSettleResponse(n,i);case"wc_sessionUpdate":return this.onSessionUpdateResponse(n,i);case"wc_sessionExtend":return this.onSessionExtendResponse(n,i);case"wc_sessionPing":return this.onSessionPingResponse(n,i);case"wc_sessionRequest":return this.onSessionRequestResponse(n,i);case"wc_sessionAuthenticate":return this.onSessionAuthenticateResponse(n,i);default:return this.client.logger.info(`Unsupported response method ${o}`)}}),B(this,"onRelayEventUnknownPayload",r=>{const{topic:n}=r,{message:i}=U("MISSING_OR_INVALID",`Decoded payload on topic ${n} is not identifiable as a JSON-RPC request or a response.`);throw new Error(i)}),B(this,"shouldIgnorePairingRequest",r=>{const{topic:n,requestMethod:i}=r,s=this.expectedPairingMethodMap.get(n);return!s||s.includes(i)?!1:!!(s.includes("wc_sessionAuthenticate")&&this.client.events.listenerCount("session_authenticate")>0)}),B(this,"onSessionProposeRequest",async r=>{const{topic:n,payload:i,attestation:s,encryptedId:o}=r,{params:a,id:c}=i;try{const u=this.client.core.eventClient.getEvent({topic:n});this.client.events.listenerCount("session_proposal")===0&&(console.warn("No listener for session_proposal event"),u?.setError(Qt.proposal_listener_not_found)),this.isValidConnect(oe({},i.params));const l=a.expiryTimestamp||ve(Ae.wc_sessionPropose.req.ttl),h=oe({id:c,pairingTopic:n,expiryTimestamp:l},a);await this.setProposal(c,h);const d=await this.getVerifyContext({attestationId:s,hash:Ct(JSON.stringify(i)),encryptedId:o,metadata:h.proposer.metadata});u?.addTrace(Bt.emit_session_proposal),this.client.events.emit("session_proposal",{id:c,params:h,verifyContext:d})}catch(u){await this.sendError({id:c,topic:n,error:u,rpcOpts:Ae.wc_sessionPropose.autoReject}),this.client.logger.error(u)}}),B(this,"onSessionProposeResponse",async(r,n,i)=>{const{id:s}=n;if(Rt(n)){const{result:o}=n;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",result:o});const a=this.client.proposal.get(s);this.client.logger.trace({type:"method",method:"onSessionProposeResponse",proposal:a});const c=a.proposer.publicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",selfPublicKey:c});const u=o.responderPublicKey;this.client.logger.trace({type:"method",method:"onSessionProposeResponse",peerPublicKey:u});const l=await this.client.core.crypto.generateSharedKey(c,u);this.pendingSessions.set(s,{sessionTopic:l,pairingTopic:r,proposalId:s,publicKey:c});const h=await this.client.core.relayer.subscribe(l,{transportType:i});this.client.logger.trace({type:"method",method:"onSessionProposeResponse",subscriptionId:h}),await this.client.core.pairing.activate({topic:r})}else if(ut(n)){await this.client.proposal.delete(s,ie("USER_DISCONNECTED"));const o=te("session_connect",s);if(this.events.listenerCount(o)===0)throw new Error(`emitting ${o} without any listeners, 954`);this.events.emit(o,{error:n.error})}}),B(this,"onSessionSettleRequest",async(r,n)=>{const{id:i,params:s}=n;try{this.isValidSessionSettleRequest(s);const{relay:o,controller:a,expiry:c,namespaces:u,sessionProperties:l,scopedProperties:h,sessionConfig:d}=n.params,f=[...this.pendingSessions.values()].find(g=>g.sessionTopic===r);if(!f)return this.client.logger.error(`Pending session not found for topic ${r}`);const p=this.client.proposal.get(f.proposalId),y=Ve(oe(oe(oe({topic:r,relay:o,expiry:c,namespaces:u,acknowledged:!0,pairingTopic:f.pairingTopic,requiredNamespaces:p.requiredNamespaces,optionalNamespaces:p.optionalNamespaces,controller:a.publicKey,self:{publicKey:f.publicKey,metadata:this.client.metadata},peer:{publicKey:a.publicKey,metadata:a.metadata}},l&&{sessionProperties:l}),h&&{scopedProperties:h}),d&&{sessionConfig:d}),{transportType:ue.relay});await this.client.session.set(y.topic,y),await this.setExpiry(y.topic,y.expiry),await this.client.core.pairing.updateMetadata({topic:f.pairingTopic,metadata:y.peer.metadata}),this.client.events.emit("session_connect",{session:y}),this.events.emit(te("session_connect",f.proposalId),{session:y}),this.pendingSessions.delete(f.proposalId),this.deleteProposal(f.proposalId,!1),this.cleanupDuplicatePairings(y),await this.sendResult({id:n.id,topic:r,result:!0,throwOnFailedPublish:!0})}catch(o){await this.sendError({id:i,topic:r,error:o}),this.client.logger.error(o)}}),B(this,"onSessionSettleResponse",async(r,n)=>{const{id:i}=n;Rt(n)?(await this.client.session.update(r,{acknowledged:!0}),this.events.emit(te("session_approve",i),{})):ut(n)&&(await this.client.session.delete(r,ie("USER_DISCONNECTED")),this.events.emit(te("session_approve",i),{error:n.error}))}),B(this,"onSessionUpdateRequest",async(r,n)=>{const{params:i,id:s}=n;try{const o=`${r}_session_update`,a=_i.get(o);if(a&&this.isRequestOutOfSync(a,s)){this.client.logger.warn(`Discarding out of sync request - ${s}`),this.sendError({id:s,topic:r,error:ie("INVALID_UPDATE_REQUEST")});return}this.isValidUpdate(oe({topic:r},i));try{_i.set(o,s),await this.client.session.update(r,{namespaces:i.namespaces}),await this.sendResult({id:s,topic:r,result:!0,throwOnFailedPublish:!0})}catch(c){throw _i.delete(o),c}this.client.events.emit("session_update",{id:s,topic:r,params:i})}catch(o){await this.sendError({id:s,topic:r,error:o}),this.client.logger.error(o)}}),B(this,"isRequestOutOfSync",(r,n)=>n.toString().slice(0,-3)<r.toString().slice(0,-3)),B(this,"onSessionUpdateResponse",(r,n)=>{const{id:i}=n,s=te("session_update",i);if(this.events.listenerCount(s)===0)throw new Error(`emitting ${s} without any listeners`);Rt(n)?this.events.emit(te("session_update",i),{}):ut(n)&&this.events.emit(te("session_update",i),{error:n.error})}),B(this,"onSessionExtendRequest",async(r,n)=>{const{id:i}=n;try{this.isValidExtend({topic:r}),await this.setExpiry(r,ve(An)),await this.sendResult({id:i,topic:r,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_extend",{id:i,topic:r})}catch(s){await this.sendError({id:i,topic:r,error:s}),this.client.logger.error(s)}}),B(this,"onSessionExtendResponse",(r,n)=>{const{id:i}=n,s=te("session_extend",i);if(this.events.listenerCount(s)===0)throw new Error(`emitting ${s} without any listeners`);Rt(n)?this.events.emit(te("session_extend",i),{}):ut(n)&&this.events.emit(te("session_extend",i),{error:n.error})}),B(this,"onSessionPingRequest",async(r,n)=>{const{id:i}=n;try{this.isValidPing({topic:r}),await this.sendResult({id:i,topic:r,result:!0,throwOnFailedPublish:!0}),this.client.events.emit("session_ping",{id:i,topic:r})}catch(s){await this.sendError({id:i,topic:r,error:s}),this.client.logger.error(s)}}),B(this,"onSessionPingResponse",(r,n)=>{const{id:i}=n,s=te("session_ping",i);setTimeout(()=>{if(this.events.listenerCount(s)===0)throw new Error(`emitting ${s} without any listeners 2176`);Rt(n)?this.events.emit(te("session_ping",i),{}):ut(n)&&this.events.emit(te("session_ping",i),{error:n.error})},500)}),B(this,"onSessionDeleteRequest",async(r,n)=>{const{id:i}=n;try{this.isValidDisconnect({topic:r,reason:n.params}),Promise.all([new Promise(s=>{this.client.core.relayer.once(De.publish,async()=>{s(await this.deleteSession({topic:r,id:i}))})}),this.sendResult({id:i,topic:r,result:!0,throwOnFailedPublish:!0}),this.cleanupPendingSentRequestsForTopic({topic:r,error:ie("USER_DISCONNECTED")})]).catch(s=>this.client.logger.error(s))}catch(s){this.client.logger.error(s)}}),B(this,"onSessionRequest",async r=>{var n,i,s;const{topic:o,payload:a,attestation:c,encryptedId:u,transportType:l}=r,{id:h,params:d}=a;try{await this.isValidRequest(oe({topic:o},d));const f=this.client.session.get(o),p=await this.getVerifyContext({attestationId:c,hash:Ct(JSON.stringify(br("wc_sessionRequest",d,h))),encryptedId:u,metadata:f.peer.metadata,transportType:l}),y={id:h,topic:o,params:d,verifyContext:p};await this.setPendingSessionRequest(y),l===ue.link_mode&&(n=f.peer.metadata.redirect)!=null&&n.universal&&this.client.core.addLinkModeSupportedApp((i=f.peer.metadata.redirect)==null?void 0:i.universal),(s=this.client.signConfig)!=null&&s.disableRequestQueue?this.emitSessionRequest(y):(this.addSessionRequestToSessionRequestQueue(y),this.processSessionRequestQueue())}catch(f){await this.sendError({id:h,topic:o,error:f}),this.client.logger.error(f)}}),B(this,"onSessionRequestResponse",(r,n)=>{const{id:i}=n,s=te("session_request",i);if(this.events.listenerCount(s)===0)throw new Error(`emitting ${s} without any listeners`);Rt(n)?this.events.emit(te("session_request",i),{result:n.result}):ut(n)&&this.events.emit(te("session_request",i),{error:n.error})}),B(this,"onSessionEventRequest",async(r,n)=>{const{id:i,params:s}=n;try{const o=`${r}_session_event_${s.event.name}`,a=_i.get(o);if(a&&this.isRequestOutOfSync(a,i)){this.client.logger.info(`Discarding out of sync request - ${i}`);return}this.isValidEmit(oe({topic:r},s)),this.client.events.emit("session_event",{id:i,topic:r,params:s}),_i.set(o,i)}catch(o){await this.sendError({id:i,topic:r,error:o}),this.client.logger.error(o)}}),B(this,"onSessionAuthenticateResponse",(r,n)=>{const{id:i}=n;this.client.logger.trace({type:"method",method:"onSessionAuthenticateResponse",topic:r,payload:n}),Rt(n)?this.events.emit(te("session_request",i),{result:n.result}):ut(n)&&this.events.emit(te("session_request",i),{error:n.error})}),B(this,"onSessionAuthenticateRequest",async r=>{var n;const{topic:i,payload:s,attestation:o,encryptedId:a,transportType:c}=r;try{const{requester:u,authPayload:l,expiryTimestamp:h}=s.params,d=await this.getVerifyContext({attestationId:o,hash:Ct(JSON.stringify(s)),encryptedId:a,metadata:u.metadata,transportType:c}),f={requester:u,pairingTopic:i,id:s.id,authPayload:l,verifyContext:d,expiryTimestamp:h};await this.setAuthRequest(s.id,{request:f,pairingTopic:i,transportType:c}),c===ue.link_mode&&(n=u.metadata.redirect)!=null&&n.universal&&this.client.core.addLinkModeSupportedApp(u.metadata.redirect.universal),this.client.events.emit("session_authenticate",{topic:i,params:s.params,id:s.id,verifyContext:d})}catch(u){this.client.logger.error(u);const l=s.params.requester.publicKey,h=await this.client.core.crypto.generateKeyPair(),d=this.getAppLinkIfEnabled(s.params.requester.metadata,c),f={type:Zt,receiverPublicKey:l,senderPublicKey:h};await this.sendError({id:s.id,topic:i,error:u,encodeOpts:f,rpcOpts:Ae.wc_sessionAuthenticate.autoReject,appLink:d})}}),B(this,"addSessionRequestToSessionRequestQueue",r=>{this.sessionRequestQueue.queue.push(r)}),B(this,"cleanupAfterResponse",r=>{this.deletePendingSessionRequest(r.response.id,{message:"fulfilled",code:0}),setTimeout(()=>{this.sessionRequestQueue.state=Lt.idle,this.processSessionRequestQueue()},L.toMiliseconds(this.requestQueueDelay))}),B(this,"cleanupPendingSentRequestsForTopic",({topic:r,error:n})=>{const i=this.client.core.history.pending;i.length>0&&i.filter(s=>s.topic===r&&s.request.method==="wc_sessionRequest").forEach(s=>{const o=s.request.id,a=te("session_request",o);if(this.events.listenerCount(a)===0)throw new Error(`emitting ${a} without any listeners`);this.events.emit(te("session_request",s.request.id),{error:n})})}),B(this,"processSessionRequestQueue",()=>{if(this.sessionRequestQueue.state===Lt.active){this.client.logger.info("session request queue is already active.");return}const r=this.sessionRequestQueue.queue[0];if(!r){this.client.logger.info("session request queue is empty.");return}try{this.sessionRequestQueue.state=Lt.active,this.emitSessionRequest(r)}catch(n){this.client.logger.error(n)}}),B(this,"emitSessionRequest",r=>{this.client.events.emit("session_request",r)}),B(this,"onPairingCreated",r=>{if(r.methods&&this.expectedPairingMethodMap.set(r.topic,r.methods),r.active)return;const n=this.client.proposal.getAll().find(i=>i.pairingTopic===r.topic);n&&this.onSessionProposeRequest({topic:r.topic,payload:br("wc_sessionPropose",Ve(oe({},n),{requiredNamespaces:n.requiredNamespaces,optionalNamespaces:n.optionalNamespaces,relays:n.relays,proposer:n.proposer,sessionProperties:n.sessionProperties,scopedProperties:n.scopedProperties}),n.id)})}),B(this,"isValidConnect",async r=>{if(!Xe(r)){const{message:u}=U("MISSING_OR_INVALID",`connect() params: ${JSON.stringify(r)}`);throw new Error(u)}const{pairingTopic:n,requiredNamespaces:i,optionalNamespaces:s,sessionProperties:o,scopedProperties:a,relays:c}=r;if(Te(n)||await this.isValidPairingTopic(n),!US(c,!0)){const{message:u}=U("MISSING_OR_INVALID",`connect() relays: ${c}`);throw new Error(u)}if(!Te(i)&&wr(i)!==0){const u="requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces";["fatal","error","silent"].includes(this.client.logger.level)?console.warn(u):this.client.logger.warn(u),this.validateNamespaces(i,"requiredNamespaces")}if(!Te(s)&&wr(s)!==0&&this.validateNamespaces(s,"optionalNamespaces"),Te(o)||this.validateSessionProps(o,"sessionProperties"),!Te(a)){this.validateSessionProps(a,"scopedProperties");const u=Object.keys(i||{}).concat(Object.keys(s||{}));if(!Object.keys(a).every(l=>u.includes(l)))throw new Error(`Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(a)}, required/optional namespaces: ${JSON.stringify(u)}`)}}),B(this,"validateNamespaces",(r,n)=>{const i=LS(r,"connect()",n);if(i)throw new Error(i.message)}),B(this,"isValidApprove",async r=>{if(!Xe(r))throw new Error(U("MISSING_OR_INVALID",`approve() params: ${r}`).message);const{id:n,namespaces:i,relayProtocol:s,sessionProperties:o,scopedProperties:a}=r;this.checkRecentlyDeleted(n),await this.isValidProposalId(n);const c=this.client.proposal.get(n),u=qa(i,"approve()");if(u)throw new Error(u.message);const l=uf(c.requiredNamespaces,i,"approve()");if(l)throw new Error(l.message);if(!ye(s,!0)){const{message:h}=U("MISSING_OR_INVALID",`approve() relayProtocol: ${s}`);throw new Error(h)}if(Te(o)||this.validateSessionProps(o,"sessionProperties"),!Te(a)){this.validateSessionProps(a,"scopedProperties");const h=new Set(Object.keys(i));if(!Object.keys(a).every(d=>h.has(d)))throw new Error(`Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(a)}, approved namespaces: ${Array.from(h).join(", ")}`)}}),B(this,"isValidReject",async r=>{if(!Xe(r)){const{message:s}=U("MISSING_OR_INVALID",`reject() params: ${r}`);throw new Error(s)}const{id:n,reason:i}=r;if(this.checkRecentlyDeleted(n),await this.isValidProposalId(n),!qS(i)){const{message:s}=U("MISSING_OR_INVALID",`reject() reason: ${JSON.stringify(i)}`);throw new Error(s)}}),B(this,"isValidSessionSettleRequest",r=>{if(!Xe(r)){const{message:u}=U("MISSING_OR_INVALID",`onSessionSettleRequest() params: ${r}`);throw new Error(u)}const{relay:n,controller:i,namespaces:s,expiry:o}=r;if(!af(n)){const{message:u}=U("MISSING_OR_INVALID","onSessionSettleRequest() relay protocol should be a string");throw new Error(u)}const a=CS(i,"onSessionSettleRequest()");if(a)throw new Error(a.message);const c=qa(s,"onSessionSettleRequest()");if(c)throw new Error(c.message);if(dr(o)){const{message:u}=U("EXPIRED","onSessionSettleRequest()");throw new Error(u)}}),B(this,"isValidUpdate",async r=>{if(!Xe(r)){const{message:c}=U("MISSING_OR_INVALID",`update() params: ${r}`);throw new Error(c)}const{topic:n,namespaces:i}=r;this.checkRecentlyDeleted(n),await this.isValidSessionTopic(n);const s=this.client.session.get(n),o=qa(i,"update()");if(o)throw new Error(o.message);const a=uf(s.requiredNamespaces,i,"update()");if(a)throw new Error(a.message)}),B(this,"isValidExtend",async r=>{if(!Xe(r)){const{message:i}=U("MISSING_OR_INVALID",`extend() params: ${r}`);throw new Error(i)}const{topic:n}=r;this.checkRecentlyDeleted(n),await this.isValidSessionTopic(n)}),B(this,"isValidRequest",async r=>{if(!Xe(r)){const{message:c}=U("MISSING_OR_INVALID",`request() params: ${r}`);throw new Error(c)}const{topic:n,request:i,chainId:s,expiry:o}=r;this.checkRecentlyDeleted(n),await this.isValidSessionTopic(n);const{namespaces:a}=this.client.session.get(n);if(!cf(a,s)){const{message:c}=U("MISSING_OR_INVALID",`request() chainId: ${s}`);throw new Error(c)}if(!kS(i)){const{message:c}=U("MISSING_OR_INVALID",`request() ${JSON.stringify(i)}`);throw new Error(c)}if(!HS(a,s,i.method)){const{message:c}=U("MISSING_OR_INVALID",`request() method: ${i.method}`);throw new Error(c)}if(o&&!GS(o,lc)){const{message:c}=U("MISSING_OR_INVALID",`request() expiry: ${o}. Expiry must be a number (in seconds) between ${lc.min} and ${lc.max}`);throw new Error(c)}}),B(this,"isValidRespond",async r=>{var n;if(!Xe(r)){const{message:o}=U("MISSING_OR_INVALID",`respond() params: ${r}`);throw new Error(o)}const{topic:i,response:s}=r;try{await this.isValidSessionTopic(i)}catch(o){throw(n=r?.response)!=null&&n.id&&this.cleanupAfterResponse(r),o}if(!MS(s)){const{message:o}=U("MISSING_OR_INVALID",`respond() response: ${JSON.stringify(s)}`);throw new Error(o)}}),B(this,"isValidPing",async r=>{if(!Xe(r)){const{message:i}=U("MISSING_OR_INVALID",`ping() params: ${r}`);throw new Error(i)}const{topic:n}=r;await this.isValidSessionOrPairingTopic(n)}),B(this,"isValidEmit",async r=>{if(!Xe(r)){const{message:a}=U("MISSING_OR_INVALID",`emit() params: ${r}`);throw new Error(a)}const{topic:n,event:i,chainId:s}=r;await this.isValidSessionTopic(n);const{namespaces:o}=this.client.session.get(n);if(!cf(o,s)){const{message:a}=U("MISSING_OR_INVALID",`emit() chainId: ${s}`);throw new Error(a)}if(!zS(i)){const{message:a}=U("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(i)}`);throw new Error(a)}if(!VS(o,s,i.name)){const{message:a}=U("MISSING_OR_INVALID",`emit() event: ${JSON.stringify(i)}`);throw new Error(a)}}),B(this,"isValidDisconnect",async r=>{if(!Xe(r)){const{message:i}=U("MISSING_OR_INVALID",`disconnect() params: ${r}`);throw new Error(i)}const{topic:n}=r;await this.isValidSessionOrPairingTopic(n)}),B(this,"isValidAuthenticate",r=>{const{chains:n,uri:i,domain:s,nonce:o}=r;if(!Array.isArray(n)||n.length===0)throw new Error("chains is required and must be a non-empty array");if(!ye(i,!1))throw new Error("uri is required parameter");if(!ye(s,!1))throw new Error("domain is required parameter");if(!ye(o,!1))throw new Error("nonce is required parameter");if([...new Set(n.map(c=>dn(c).namespace))].length>1)throw new Error("Multi-namespace requests are not supported. Please request single namespace only.");const{namespace:a}=dn(n[0]);if(a!=="eip155")throw new Error("Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.")}),B(this,"getVerifyContext",async r=>{const{attestationId:n,hash:i,encryptedId:s,metadata:o,transportType:a}=r,c={verified:{verifyUrl:o.verifyUrl||Oi,validation:"UNKNOWN",origin:o.url||""}};try{if(a===ue.link_mode){const l=this.getAppLinkIfEnabled(o,a);return c.verified.validation=l&&new URL(l).origin===new URL(o.url).origin?"VALID":"INVALID",c}const u=await this.client.core.verify.resolve({attestationId:n,hash:i,encryptedId:s,verifyUrl:o.verifyUrl});u&&(c.verified.origin=u.origin,c.verified.isScam=u.isScam,c.verified.validation=u.origin===new URL(o.url).origin?"VALID":"INVALID")}catch(u){this.client.logger.warn(u)}return this.client.logger.debug(`Verify context: ${JSON.stringify(c)}`),c}),B(this,"validateSessionProps",(r,n)=>{Object.values(r).forEach((i,s)=>{if(i==null){const{message:o}=U("MISSING_OR_INVALID",`${n} must contain an existing value for each key. Received: ${i} for key ${Object.keys(r)[s]}`);throw new Error(o)}})}),B(this,"getPendingAuthRequest",r=>{const n=this.client.auth.requests.get(r);return typeof n=="object"?n:void 0}),B(this,"addToRecentlyDeleted",(r,n)=>{if(this.recentlyDeletedMap.set(r,n),this.recentlyDeletedMap.size>=this.recentlyDeletedLimit){let i=0;const s=this.recentlyDeletedLimit/2;for(const o of this.recentlyDeletedMap.keys()){if(i++>=s)break;this.recentlyDeletedMap.delete(o)}}}),B(this,"checkRecentlyDeleted",r=>{const n=this.recentlyDeletedMap.get(r);if(n){const{message:i}=U("MISSING_OR_INVALID",`Record was recently deleted - ${n}: ${r}`);throw new Error(i)}}),B(this,"isLinkModeEnabled",(r,n)=>{var i,s,o,a,c,u,l,h,d;return!r||n!==ue.link_mode?!1:((s=(i=this.client.metadata)==null?void 0:i.redirect)==null?void 0:s.linkMode)===!0&&((a=(o=this.client.metadata)==null?void 0:o.redirect)==null?void 0:a.universal)!==void 0&&((u=(c=this.client.metadata)==null?void 0:c.redirect)==null?void 0:u.universal)!==""&&((l=r?.redirect)==null?void 0:l.universal)!==void 0&&((h=r?.redirect)==null?void 0:h.universal)!==""&&((d=r?.redirect)==null?void 0:d.linkMode)===!0&&this.client.core.linkModeSupportedApps.includes(r.redirect.universal)&&typeof(global==null?void 0:global.Linking)<"u"}),B(this,"getAppLinkIfEnabled",(r,n)=>{var i;return this.isLinkModeEnabled(r,n)?(i=r?.redirect)==null?void 0:i.universal:void 0}),B(this,"handleLinkModeMessage",({url:r})=>{if(!r||!r.includes("wc_ev")||!r.includes("topic"))return;const n=zh(r,"topic")||"",i=decodeURIComponent(zh(r,"wc_ev")||""),s=this.client.session.keys.includes(n);s&&this.client.session.update(n,{transportType:ue.link_mode}),this.client.core.dispatchEnvelope({topic:n,message:i,sessionExists:s})}),B(this,"registerLinkModeListeners",async()=>{var r;if(ha()||lr()&&(r=this.client.metadata.redirect)!=null&&r.linkMode){const n=global==null?void 0:global.Linking;if(typeof n<"u"){n.addEventListener("url",this.handleLinkModeMessage,this.client.name);const i=await n.getInitialURL();i&&setTimeout(()=>{this.handleLinkModeMessage({url:i})},50)}}}),B(this,"shouldSetTVF",(r,n)=>{if(!n||r!=="wc_sessionRequest")return!1;const{request:i}=n;return Object.keys(wp).includes(i.method)}),B(this,"getTVFParams",(r,n,i)=>{var s,o;try{const a=n.request.method,c=this.extractTxHashesFromResult(a,i);return Ve(oe({correlationId:r,rpcMethods:[a],chainId:n.chainId},this.isValidContractData(n.request.params)&&{contractAddresses:[(o=(s=n.request.params)==null?void 0:s[0])==null?void 0:o.to]}),{txHashes:c})}catch(a){this.client.logger.warn("Error getting TVF params",a)}return{}}),B(this,"isValidContractData",r=>{var n;if(!r)return!1;try{const i=r?.data||((n=r?.[0])==null?void 0:n.data);if(!i.startsWith("0x"))return!1;const s=i.slice(2);return/^[0-9a-fA-F]*$/.test(s)?s.length%2===0:!1}catch{}return!1}),B(this,"extractTxHashesFromResult",(r,n)=>{try{const i=wp[r];if(typeof n=="string")return[n];const s=n[i.key];if(mr(s))return r==="solana_signAllTransactions"?s.map(o=>g_(o)):s;if(typeof s=="string")return[s]}catch(i){this.client.logger.warn("Error extracting tx hashes from result",i)}return[]})}async processPendingMessageEvents(){try{const e=this.client.session.keys,r=this.client.core.relayer.messages.getWithoutAck(e);for(const[n,i]of Object.entries(r))for(const s of i)try{await this.onProviderMessageEvent({topic:n,message:s,publishedAt:Date.now()})}catch{this.client.logger.warn(`Error processing pending message event for topic: ${n}, message: ${s}`)}}catch(e){this.client.logger.warn("processPendingMessageEvents failed",e)}}isInitialized(){if(!this.initialized){const{message:e}=U("NOT_INITIALIZED",this.name);throw new Error(e)}}async confirmOnlineStateOrThrow(){await this.client.core.relayer.confirmOnlineStateOrThrow()}registerRelayerEvents(){this.client.core.relayer.on(De.message,e=>{this.onProviderMessageEvent(e)})}async onRelayMessage(e){const{topic:r,message:n,attestation:i,transportType:s}=e,{publicKey:o}=this.client.auth.authKeys.keys.includes(Us)?this.client.auth.authKeys.get(Us):{responseTopic:void 0,publicKey:void 0};try{const a=await this.client.core.crypto.decode(r,n,{receiverPublicKey:o,encoding:s===ue.link_mode?yr:wt});Ka(a)?(this.client.core.history.set(r,a),await this.onRelayEventRequest({topic:r,payload:a,attestation:i,transportType:s,encryptedId:Ct(n)})):Ns(a)?(await this.client.core.history.resolve(a),await this.onRelayEventResponse({topic:r,payload:a,transportType:s}),this.client.core.history.delete(r,a.id)):await this.onRelayEventUnknownPayload({topic:r,payload:a,transportType:s}),await this.client.core.relayer.messages.ack(r,n)}catch(a){this.client.logger.error(a)}}registerExpirerEvents(){this.client.core.expirer.on(dt.expired,async e=>{const{topic:r,id:n}=Mh(e.target);if(n&&this.client.pendingRequest.keys.includes(n))return await this.deletePendingSessionRequest(n,U("EXPIRED"),!0);if(n&&this.client.auth.requests.keys.includes(n))return await this.deletePendingAuthRequest(n,U("EXPIRED"),!0);r?this.client.session.keys.includes(r)&&(await this.deleteSession({topic:r,expirerHasDeleted:!0}),this.client.events.emit("session_expire",{topic:r})):n&&(await this.deleteProposal(n,!0),this.client.events.emit("proposal_expire",{id:n}))})}registerPairingEvents(){this.client.core.pairing.events.on(kr.create,e=>this.onPairingCreated(e)),this.client.core.pairing.events.on(kr.delete,e=>{this.addToRecentlyDeleted(e.topic,"pairing")})}isValidPairingTopic(e){if(!ye(e,!1)){const{message:r}=U("MISSING_OR_INVALID",`pairing topic should be a string: ${e}`);throw new Error(r)}if(!this.client.core.pairing.pairings.keys.includes(e)){const{message:r}=U("NO_MATCHING_KEY",`pairing topic doesn't exist: ${e}`);throw new Error(r)}if(dr(this.client.core.pairing.pairings.get(e).expiry)){const{message:r}=U("EXPIRED",`pairing topic: ${e}`);throw new Error(r)}}async isValidSessionTopic(e){if(!ye(e,!1)){const{message:r}=U("MISSING_OR_INVALID",`session topic should be a string: ${e}`);throw new Error(r)}if(this.checkRecentlyDeleted(e),!this.client.session.keys.includes(e)){const{message:r}=U("NO_MATCHING_KEY",`session topic doesn't exist: ${e}`);throw new Error(r)}if(dr(this.client.session.get(e).expiry)){await this.deleteSession({topic:e});const{message:r}=U("EXPIRED",`session topic: ${e}`);throw new Error(r)}if(!this.client.core.crypto.keychain.has(e)){const{message:r}=U("MISSING_OR_INVALID",`session topic does not exist in keychain: ${e}`);throw await this.deleteSession({topic:e}),new Error(r)}}async isValidSessionOrPairingTopic(e){if(this.checkRecentlyDeleted(e),this.client.session.keys.includes(e))await this.isValidSessionTopic(e);else if(this.client.core.pairing.pairings.keys.includes(e))this.isValidPairingTopic(e);else if(ye(e,!1)){const{message:r}=U("NO_MATCHING_KEY",`session or pairing topic doesn't exist: ${e}`);throw new Error(r)}else{const{message:r}=U("MISSING_OR_INVALID",`session or pairing topic should be a string: ${e}`);throw new Error(r)}}async isValidProposalId(e){if(!jS(e)){const{message:r}=U("MISSING_OR_INVALID",`proposal id should be a number: ${e}`);throw new Error(r)}if(!this.client.proposal.keys.includes(e)){const{message:r}=U("NO_MATCHING_KEY",`proposal id doesn't exist: ${e}`);throw new Error(r)}if(dr(this.client.proposal.get(e).expiryTimestamp)){await this.deleteProposal(e);const{message:r}=U("EXPIRED",`proposal id: ${e}`);throw new Error(r)}}}class z$ extends Hr{constructor(e,r){super(e,r,D$,cc),this.core=e,this.logger=r}}class H$ extends Hr{constructor(e,r){super(e,r,$$,cc),this.core=e,this.logger=r}}class V$ extends Hr{constructor(e,r){super(e,r,P$,cc,n=>n.id),this.core=e,this.logger=r}}class K$ extends Hr{constructor(e,r){super(e,r,T$,Ls,()=>Us),this.core=e,this.logger=r}}class W$ extends Hr{constructor(e,r){super(e,r,B$,Ls),this.core=e,this.logger=r}}class G$ extends Hr{constructor(e,r){super(e,r,F$,Ls,n=>n.id),this.core=e,this.logger=r}}var Y$=Object.defineProperty,Z$=(t,e,r)=>e in t?Y$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,dc=(t,e,r)=>Z$(t,typeof e!="symbol"?e+"":e,r);class J${constructor(e,r){this.core=e,this.logger=r,dc(this,"authKeys"),dc(this,"pairingTopics"),dc(this,"requests"),this.authKeys=new K$(this.core,this.logger),this.pairingTopics=new W$(this.core,this.logger),this.requests=new G$(this.core,this.logger)}async init(){await this.authKeys.init(),await this.pairingTopics.init(),await this.requests.init()}}var X$=Object.defineProperty,Q$=(t,e,r)=>e in t?X$(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Z=(t,e,r)=>Q$(t,typeof e!="symbol"?e+"":e,r);class fc extends dm{constructor(e){super(e),Z(this,"protocol",fp),Z(this,"version",pp),Z(this,"name",uc.name),Z(this,"metadata"),Z(this,"core"),Z(this,"logger"),Z(this,"events",new Ue.exports.EventEmitter),Z(this,"engine"),Z(this,"session"),Z(this,"proposal"),Z(this,"pendingRequest"),Z(this,"auth"),Z(this,"signConfig"),Z(this,"on",(n,i)=>this.events.on(n,i)),Z(this,"once",(n,i)=>this.events.once(n,i)),Z(this,"off",(n,i)=>this.events.off(n,i)),Z(this,"removeListener",(n,i)=>this.events.removeListener(n,i)),Z(this,"removeAllListeners",n=>this.events.removeAllListeners(n)),Z(this,"connect",async n=>{try{return await this.engine.connect(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"pair",async n=>{try{return await this.engine.pair(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"approve",async n=>{try{return await this.engine.approve(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"reject",async n=>{try{return await this.engine.reject(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"update",async n=>{try{return await this.engine.update(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"extend",async n=>{try{return await this.engine.extend(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"request",async n=>{try{return await this.engine.request(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"respond",async n=>{try{return await this.engine.respond(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"ping",async n=>{try{return await this.engine.ping(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"emit",async n=>{try{return await this.engine.emit(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"disconnect",async n=>{try{return await this.engine.disconnect(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"find",n=>{try{return this.engine.find(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"getPendingSessionRequests",()=>{try{return this.engine.getPendingSessionRequests()}catch(n){throw this.logger.error(n.message),n}}),Z(this,"authenticate",async(n,i)=>{try{return await this.engine.authenticate(n,i)}catch(s){throw this.logger.error(s.message),s}}),Z(this,"formatAuthMessage",n=>{try{return this.engine.formatAuthMessage(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"approveSessionAuthenticate",async n=>{try{return await this.engine.approveSessionAuthenticate(n)}catch(i){throw this.logger.error(i.message),i}}),Z(this,"rejectSessionAuthenticate",async n=>{try{return await this.engine.rejectSessionAuthenticate(n)}catch(i){throw this.logger.error(i.message),i}}),this.name=e?.name||uc.name,this.metadata=A2(e?.metadata),this.signConfig=e?.signConfig;const r=typeof e?.logger<"u"&&typeof e?.logger!="string"?e.logger:It(Hi({level:e?.logger||uc.logger}));this.core=e?.core||new O$(e),this.logger=ke(r,this.name),this.session=new H$(this.core,this.logger),this.proposal=new z$(this.core,this.logger),this.pendingRequest=new V$(this.core,this.logger),this.engine=new M$(this),this.auth=new J$(this.core,this.logger)}static async init(e){const r=new fc(e);return await r.initialize(),r}get context(){return Ge(this.logger)}get pairing(){return this.core.pairing.pairings}async initialize(){this.logger.trace("Initialized");try{await this.core.start(),await this.session.init(),await this.proposal.init(),await this.pendingRequest.init(),await this.auth.init(),await this.engine.init(),this.logger.info("SignClient Initialization Success"),setTimeout(()=>{this.engine.processRelayMessageCache()},L.toMiliseconds(L.ONE_SECOND))}catch(e){throw this.logger.info("SignClient Initialization Failure"),this.logger.error(e.message),e}}}const vp="error",eA="wss://relay.walletconnect.org",tA="wc",rA="universal_provider",js=`${tA}@2:${rA}:`,Ep="https://rpc.walletconnect.org/v1/",Pn="generic",nA=`${Ep}bundler`,ft={DEFAULT_CHAIN_CHANGED:"default_chain_changed"};function iA(){}function pc(t){return t==null||typeof t!="object"&&typeof t!="function"}function gc(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function sA(t){if(pc(t))return t;if(Array.isArray(t)||gc(t)||t instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&t instanceof SharedArrayBuffer)return t.slice(0);const e=Object.getPrototypeOf(t),r=e.constructor;if(t instanceof Date||t instanceof Map||t instanceof Set)return new r(t);if(t instanceof RegExp){const n=new r(t);return n.lastIndex=t.lastIndex,n}if(t instanceof DataView)return new r(t.buffer.slice(0));if(t instanceof Error){const n=new r(t.message);return n.stack=t.stack,n.name=t.name,n.cause=t.cause,n}if(typeof File<"u"&&t instanceof File)return new r([t],t.name,{type:t.type,lastModified:t.lastModified});if(typeof t=="object"){const n=Object.create(e);return Object.assign(n,t)}return t}function _p(t){return typeof t=="object"&&t!==null}function Ip(t){return Object.getOwnPropertySymbols(t).filter(e=>Object.prototype.propertyIsEnumerable.call(t,e))}function Sp(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const oA="[object RegExp]",xp="[object String]",Op="[object Number]",Dp="[object Boolean]",$p="[object Arguments]",aA="[object Symbol]",cA="[object Date]",uA="[object Map]",lA="[object Set]",hA="[object Array]",dA="[object ArrayBuffer]",fA="[object Object]",pA="[object DataView]",gA="[object Uint8Array]",yA="[object Uint8ClampedArray]",mA="[object Uint16Array]",wA="[object Uint32Array]",bA="[object Int8Array]",vA="[object Int16Array]",EA="[object Int32Array]",_A="[object Float32Array]",IA="[object Float64Array]";function SA(t,e){return Cn(t,void 0,t,new Map,e)}function Cn(t,e,r,n=new Map,i=void 0){const s=i?.(t,e,r,n);if(s!=null)return s;if(pc(t))return t;if(n.has(t))return n.get(t);if(Array.isArray(t)){const o=new Array(t.length);n.set(t,o);for(let a=0;a<t.length;a++)o[a]=Cn(t[a],a,r,n,i);return Object.hasOwn(t,"index")&&(o.index=t.index),Object.hasOwn(t,"input")&&(o.input=t.input),o}if(t instanceof Date)return new Date(t.getTime());if(t instanceof RegExp){const o=new RegExp(t.source,t.flags);return o.lastIndex=t.lastIndex,o}if(t instanceof Map){const o=new Map;n.set(t,o);for(const[a,c]of t)o.set(a,Cn(c,a,r,n,i));return o}if(t instanceof Set){const o=new Set;n.set(t,o);for(const a of t)o.add(Cn(a,void 0,r,n,i));return o}if(typeof Buffer<"u"&&Buffer.isBuffer(t))return t.subarray();if(gc(t)){const o=new(Object.getPrototypeOf(t)).constructor(t.length);n.set(t,o);for(let a=0;a<t.length;a++)o[a]=Cn(t[a],a,r,n,i);return o}if(t instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&t instanceof SharedArrayBuffer)return t.slice(0);if(t instanceof DataView){const o=new DataView(t.buffer.slice(0),t.byteOffset,t.byteLength);return n.set(t,o),Vr(o,t,r,n,i),o}if(typeof File<"u"&&t instanceof File){const o=new File([t],t.name,{type:t.type});return n.set(t,o),Vr(o,t,r,n,i),o}if(t instanceof Blob){const o=new Blob([t],{type:t.type});return n.set(t,o),Vr(o,t,r,n,i),o}if(t instanceof Error){const o=new t.constructor;return n.set(t,o),o.message=t.message,o.name=t.name,o.stack=t.stack,o.cause=t.cause,Vr(o,t,r,n,i),o}if(typeof t=="object"&&xA(t)){const o=Object.create(Object.getPrototypeOf(t));return n.set(t,o),Vr(o,t,r,n,i),o}return t}function Vr(t,e,r=t,n,i){const s=[...Object.keys(e),...Ip(e)];for(let o=0;o<s.length;o++){const a=s[o],c=Object.getOwnPropertyDescriptor(t,a);(c==null||c.writable)&&(t[a]=Cn(e[a],a,r,n,i))}}function xA(t){switch(Sp(t)){case $p:case hA:case dA:case pA:case Dp:case cA:case _A:case IA:case bA:case vA:case EA:case uA:case Op:case fA:case oA:case lA:case xp:case aA:case gA:case yA:case mA:case wA:return!0;default:return!1}}function OA(t,e){return SA(t,(r,n,i,s)=>{const o=e?.(r,n,i,s);if(o!=null)return o;if(typeof t=="object")switch(Object.prototype.toString.call(t)){case Op:case xp:case Dp:{const a=new t.constructor(t?.valueOf());return Vr(a,t),a}case $p:{const a={};return Vr(a,t),a.length=t.length,a[Symbol.iterator]=t[Symbol.iterator],a}default:return}})}function Ap(t){return OA(t)}function Pp(t){return t!==null&&typeof t=="object"&&Sp(t)==="[object Arguments]"}function DA(t){return gc(t)}function $A(t){if(typeof t!="object"||t==null)return!1;if(Object.getPrototypeOf(t)===null)return!0;if(Object.prototype.toString.call(t)!=="[object Object]"){const r=t[Symbol.toStringTag];return r==null||!Object.getOwnPropertyDescriptor(t,Symbol.toStringTag)?.writable?!1:t.toString()===`[object ${r}]`}let e=t;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}function AA(t,...e){const r=e.slice(0,-1),n=e[e.length-1];let i=t;for(let s=0;s<r.length;s++){const o=r[s];i=qs(i,o,n,new Map)}return i}function qs(t,e,r,n){if(pc(t)&&(t=Object(t)),e==null||typeof e!="object")return t;if(n.has(e))return sA(n.get(e));if(n.set(e,t),Array.isArray(e)){e=e.slice();for(let s=0;s<e.length;s++)e[s]=e[s]??void 0}const i=[...Object.keys(e),...Ip(e)];for(let s=0;s<i.length;s++){const o=i[s];let a=e[o],c=t[o];if(Pp(a)&&(a={...a}),Pp(c)&&(c={...c}),typeof Buffer<"u"&&Buffer.isBuffer(a)&&(a=Ap(a)),Array.isArray(a))if(typeof c=="object"&&c!=null){const l=[],h=Reflect.ownKeys(c);for(let d=0;d<h.length;d++){const f=h[d];l[f]=c[f]}c=l}else c=[];const u=r(c,a,o,t,e,n);u!=null?t[o]=u:Array.isArray(a)||_p(c)&&_p(a)?t[o]=qs(c,a,r,n):c==null&&$A(a)?t[o]=qs({},a,r,n):c==null&&DA(a)?t[o]=Ap(a):(c===void 0||a!==void 0)&&(t[o]=a)}return t}function PA(t,...e){return AA(t,...e,iA)}var CA=Object.defineProperty,NA=Object.defineProperties,RA=Object.getOwnPropertyDescriptors,Cp=Object.getOwnPropertySymbols,TA=Object.prototype.hasOwnProperty,BA=Object.prototype.propertyIsEnumerable,Np=(t,e,r)=>e in t?CA(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ks=(t,e)=>{for(var r in e||(e={}))TA.call(e,r)&&Np(t,r,e[r]);if(Cp)for(var r of Cp(e))BA.call(e,r)&&Np(t,r,e[r]);return t},FA=(t,e)=>NA(t,RA(e));function ot(t,e,r){var n;const i=dn(t);return((n=e.rpcMap)==null?void 0:n[i.reference])||`${Ep}?chainId=${i.namespace}:${i.reference}&projectId=${r}`}function Kr(t){return t.includes(":")?t.split(":")[1]:t}function Rp(t){return t.map(e=>`${e.split(":")[0]}:${e.split(":")[1]}`)}function LA(t,e){const r=Object.keys(e.namespaces).filter(i=>i.includes(t));if(!r.length)return[];const n=[];return r.forEach(i=>{const s=e.namespaces[i].accounts;n.push(...s)}),n}function Ms(t={},e={}){const r=Tp(t),n=Tp(e);return PA(r,n)}function Tp(t){var e,r,n,i,s;const o={};if(!wr(t))return o;for(const[a,c]of Object.entries(t)){const u=$s(a)?[a]:c.chains,l=c.methods||[],h=c.events||[],d=c.rpcMap||{},f=Dn(a);o[f]=FA(ks(ks({},o[f]),c),{chains:At(u,(e=o[f])==null?void 0:e.chains),methods:At(l,(r=o[f])==null?void 0:r.methods),events:At(h,(n=o[f])==null?void 0:n.events)}),(wr(d)||wr(((i=o[f])==null?void 0:i.rpcMap)||{}))&&(o[f].rpcMap=ks(ks({},d),(s=o[f])==null?void 0:s.rpcMap))}return o}function Bp(t){return t.includes(":")?t.split(":")[2]:t}function Fp(t){const e={};for(const[r,n]of Object.entries(t)){const i=n.methods||[],s=n.events||[],o=n.accounts||[],a=$s(r)?[r]:n.chains?n.chains:Rp(n.accounts);e[r]={chains:a,methods:i,events:s,accounts:o}}return e}function yc(t){return typeof t=="number"?t:t.includes("0x")?parseInt(t,16):(t=t.includes(":")?t.split(":")[1]:t,isNaN(Number(t))?t:Number(t))}const Lp={},Q=t=>Lp[t],mc=(t,e)=>{Lp[t]=e};var wc={exports:{}};(function(t,e){var r=typeof globalThis<"u"&&globalThis||typeof self<"u"&&self||typeof Ke<"u"&&Ke,n=function(){function s(){this.fetch=!1,this.DOMException=r.DOMException}return s.prototype=r,new s}();(function(s){(function(o){var a=typeof s<"u"&&s||typeof self<"u"&&self||typeof Ke<"u"&&Ke||{},c={searchParams:"URLSearchParams"in a,iterable:"Symbol"in a&&"iterator"in Symbol,blob:"FileReader"in a&&"Blob"in a&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in a,arrayBuffer:"ArrayBuffer"in a};function u(m){return m&&DataView.prototype.isPrototypeOf(m)}if(c.arrayBuffer)var l=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],h=ArrayBuffer.isView||function(m){return m&&l.indexOf(Object.prototype.toString.call(m))>-1};function d(m){if(typeof m!="string"&&(m=String(m)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(m)||m==="")throw new TypeError('Invalid character in header field name: "'+m+'"');return m.toLowerCase()}function f(m){return typeof m!="string"&&(m=String(m)),m}function p(m){var w={next:function(){var I=m.shift();return{done:I===void 0,value:I}}};return c.iterable&&(w[Symbol.iterator]=function(){return w}),w}function y(m){this.map={},m instanceof y?m.forEach(function(w,I){this.append(I,w)},this):Array.isArray(m)?m.forEach(function(w){if(w.length!=2)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+w.length);this.append(w[0],w[1])},this):m&&Object.getOwnPropertyNames(m).forEach(function(w){this.append(w,m[w])},this)}y.prototype.append=function(m,w){m=d(m),w=f(w);var I=this.map[m];this.map[m]=I?I+", "+w:w},y.prototype.delete=function(m){delete this.map[d(m)]},y.prototype.get=function(m){return m=d(m),this.has(m)?this.map[m]:null},y.prototype.has=function(m){return this.map.hasOwnProperty(d(m))},y.prototype.set=function(m,w){this.map[d(m)]=f(w)},y.prototype.forEach=function(m,w){for(var I in this.map)this.map.hasOwnProperty(I)&&m.call(w,this.map[I],I,this)},y.prototype.keys=function(){var m=[];return this.forEach(function(w,I){m.push(I)}),p(m)},y.prototype.values=function(){var m=[];return this.forEach(function(w){m.push(w)}),p(m)},y.prototype.entries=function(){var m=[];return this.forEach(function(w,I){m.push([I,w])}),p(m)},c.iterable&&(y.prototype[Symbol.iterator]=y.prototype.entries);function g(m){if(!m._noBody){if(m.bodyUsed)return Promise.reject(new TypeError("Already read"));m.bodyUsed=!0}}function v(m){return new Promise(function(w,I){m.onload=function(){w(m.result)},m.onerror=function(){I(m.error)}})}function b(m){var w=new FileReader,I=v(w);return w.readAsArrayBuffer(m),I}function E(m){var w=new FileReader,I=v(w),A=/charset=([A-Za-z0-9_-]+)/.exec(m.type),x=A?A[1]:"utf-8";return w.readAsText(m,x),I}function _(m){for(var w=new Uint8Array(m),I=new Array(w.length),A=0;A<w.length;A++)I[A]=String.fromCharCode(w[A]);return I.join("")}function P(m){if(m.slice)return m.slice(0);var w=new Uint8Array(m.byteLength);return w.set(new Uint8Array(m)),w.buffer}function $(){return this.bodyUsed=!1,this._initBody=function(m){this.bodyUsed=this.bodyUsed,this._bodyInit=m,m?typeof m=="string"?this._bodyText=m:c.blob&&Blob.prototype.isPrototypeOf(m)?this._bodyBlob=m:c.formData&&FormData.prototype.isPrototypeOf(m)?this._bodyFormData=m:c.searchParams&&URLSearchParams.prototype.isPrototypeOf(m)?this._bodyText=m.toString():c.arrayBuffer&&c.blob&&u(m)?(this._bodyArrayBuffer=P(m.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):c.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(m)||h(m))?this._bodyArrayBuffer=P(m):this._bodyText=m=Object.prototype.toString.call(m):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||(typeof m=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):c.searchParams&&URLSearchParams.prototype.isPrototypeOf(m)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},c.blob&&(this.blob=function(){var m=g(this);if(m)return m;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer){var m=g(this);return m||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else{if(c.blob)return this.blob().then(b);throw new Error("could not read as ArrayBuffer")}},this.text=function(){var m=g(this);if(m)return m;if(this._bodyBlob)return E(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(_(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},c.formData&&(this.formData=function(){return this.text().then(k)}),this.json=function(){return this.text().then(JSON.parse)},this}var O=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function C(m){var w=m.toUpperCase();return O.indexOf(w)>-1?w:m}function S(m,w){if(!(this instanceof S))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');w=w||{};var I=w.body;if(m instanceof S){if(m.bodyUsed)throw new TypeError("Already read");this.url=m.url,this.credentials=m.credentials,w.headers||(this.headers=new y(m.headers)),this.method=m.method,this.mode=m.mode,this.signal=m.signal,!I&&m._bodyInit!=null&&(I=m._bodyInit,m.bodyUsed=!0)}else this.url=String(m);if(this.credentials=w.credentials||this.credentials||"same-origin",(w.headers||!this.headers)&&(this.headers=new y(w.headers)),this.method=C(w.method||this.method||"GET"),this.mode=w.mode||this.mode||null,this.signal=w.signal||this.signal||function(){if("AbortController"in a){var N=new AbortController;return N.signal}}(),this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&I)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(I),(this.method==="GET"||this.method==="HEAD")&&(w.cache==="no-store"||w.cache==="no-cache")){var A=/([?&])_=[^&]*/;if(A.test(this.url))this.url=this.url.replace(A,"$1_="+new Date().getTime());else{var x=/\?/;this.url+=(x.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}S.prototype.clone=function(){return new S(this,{body:this._bodyInit})};function k(m){var w=new FormData;return m.trim().split("&").forEach(function(I){if(I){var A=I.split("="),x=A.shift().replace(/\+/g," "),N=A.join("=").replace(/\+/g," ");w.append(decodeURIComponent(x),decodeURIComponent(N))}}),w}function T(m){var w=new y,I=m.replace(/\r?\n[\t ]+/g," ");return I.split("\r").map(function(A){return A.indexOf(`
`)===0?A.substr(1,A.length):A}).forEach(function(A){var x=A.split(":"),N=x.shift().trim();if(N){var F=x.join(":").trim();try{w.append(N,F)}catch(j){console.warn("Response "+j.message)}}}),w}$.call(S.prototype);function R(m,w){if(!(this instanceof R))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(w||(w={}),this.type="default",this.status=w.status===void 0?200:w.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=w.statusText===void 0?"":""+w.statusText,this.headers=new y(w.headers),this.url=w.url||"",this._initBody(m)}$.call(R.prototype),R.prototype.clone=function(){return new R(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new y(this.headers),url:this.url})},R.error=function(){var m=new R(null,{status:200,statusText:""});return m.ok=!1,m.status=0,m.type="error",m};var M=[301,302,303,307,308];R.redirect=function(m,w){if(M.indexOf(w)===-1)throw new RangeError("Invalid status code");return new R(null,{status:w,headers:{location:m}})},o.DOMException=a.DOMException;try{new o.DOMException}catch{o.DOMException=function(w,I){this.message=w,this.name=I;var A=Error(w);this.stack=A.stack},o.DOMException.prototype=Object.create(Error.prototype),o.DOMException.prototype.constructor=o.DOMException}function D(m,w){return new Promise(function(I,A){var x=new S(m,w);if(x.signal&&x.signal.aborted)return A(new o.DOMException("Aborted","AbortError"));var N=new XMLHttpRequest;function F(){N.abort()}N.onload=function(){var q={statusText:N.statusText,headers:T(N.getAllResponseHeaders()||"")};x.url.indexOf("file://")===0&&(N.status<200||N.status>599)?q.status=200:q.status=N.status,q.url="responseURL"in N?N.responseURL:q.headers.get("X-Request-URL");var H="response"in N?N.response:N.responseText;setTimeout(function(){I(new R(H,q))},0)},N.onerror=function(){setTimeout(function(){A(new TypeError("Network request failed"))},0)},N.ontimeout=function(){setTimeout(function(){A(new TypeError("Network request timed out"))},0)},N.onabort=function(){setTimeout(function(){A(new o.DOMException("Aborted","AbortError"))},0)};function j(q){try{return q===""&&a.location.href?a.location.href:q}catch{return q}}if(N.open(x.method,j(x.url),!0),x.credentials==="include"?N.withCredentials=!0:x.credentials==="omit"&&(N.withCredentials=!1),"responseType"in N&&(c.blob?N.responseType="blob":c.arrayBuffer&&(N.responseType="arraybuffer")),w&&typeof w.headers=="object"&&!(w.headers instanceof y||a.Headers&&w.headers instanceof a.Headers)){var z=[];Object.getOwnPropertyNames(w.headers).forEach(function(q){z.push(d(q)),N.setRequestHeader(q,f(w.headers[q]))}),x.headers.forEach(function(q,H){z.indexOf(H)===-1&&N.setRequestHeader(H,q)})}else x.headers.forEach(function(q,H){N.setRequestHeader(H,q)});x.signal&&(x.signal.addEventListener("abort",F),N.onreadystatechange=function(){N.readyState===4&&x.signal.removeEventListener("abort",F)}),N.send(typeof x._bodyInit>"u"?null:x._bodyInit)})}return D.polyfill=!0,a.fetch||(a.fetch=D,a.Headers=y,a.Request=S,a.Response=R),o.Headers=y,o.Request=S,o.Response=R,o.fetch=D,Object.defineProperty(o,"__esModule",{value:!0}),o})({})})(n),n.fetch.ponyfill=!0,delete n.fetch.polyfill;var i=r.fetch?r:n;e=i.fetch,e.default=i.fetch,e.fetch=i.fetch,e.Headers=i.Headers,e.Request=i.Request,e.Response=i.Response,t.exports=e})(wc,wc.exports);var Up=rg(wc.exports),UA=Object.defineProperty,jA=Object.defineProperties,qA=Object.getOwnPropertyDescriptors,jp=Object.getOwnPropertySymbols,kA=Object.prototype.hasOwnProperty,MA=Object.prototype.propertyIsEnumerable,qp=(t,e,r)=>e in t?UA(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,kp=(t,e)=>{for(var r in e||(e={}))kA.call(e,r)&&qp(t,r,e[r]);if(jp)for(var r of jp(e))MA.call(e,r)&&qp(t,r,e[r]);return t},Mp=(t,e)=>jA(t,qA(e));const zA={Accept:"application/json","Content-Type":"application/json"},HA="POST",zp={headers:zA,method:HA},Hp=10;class _t{constructor(e,r=!1){if(this.url=e,this.disableProviderPing=r,this.events=new Ue.exports.EventEmitter,this.isAvailable=!1,this.registering=!1,!Ef(e))throw new Error(`Provided URL is not compatible with HTTP connection: ${e}`);this.url=e,this.disableProviderPing=r}get connected(){return this.isAvailable}get connecting(){return this.registering}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}off(e,r){this.events.off(e,r)}removeListener(e,r){this.events.removeListener(e,r)}async open(e=this.url){await this.register(e)}async close(){if(!this.isAvailable)throw new Error("Connection already closed");this.onClose()}async send(e){this.isAvailable||await this.register();try{const r=Ut(e),n=await(await Up(this.url,Mp(kp({},zp),{body:r}))).json();this.onPayload({data:n})}catch(r){this.onError(e.id,r)}}async register(e=this.url){if(!Ef(e))throw new Error(`Provided URL is not compatible with HTTP connection: ${e}`);if(this.registering){const r=this.events.getMaxListeners();return(this.events.listenerCount("register_error")>=r||this.events.listenerCount("open")>=r)&&this.events.setMaxListeners(r+1),new Promise((n,i)=>{this.events.once("register_error",s=>{this.resetMaxListeners(),i(s)}),this.events.once("open",()=>{if(this.resetMaxListeners(),typeof this.isAvailable>"u")return i(new Error("HTTP connection is missing or invalid"));n()})})}this.url=e,this.registering=!0;try{if(!this.disableProviderPing){const r=Ut({id:1,jsonrpc:"2.0",method:"test",params:[]});await Up(e,Mp(kp({},zp),{body:r}))}this.onOpen()}catch(r){const n=this.parseError(r);throw this.events.emit("register_error",n),this.onClose(),n}}onOpen(){this.isAvailable=!0,this.registering=!1,this.events.emit("open")}onClose(){this.isAvailable=!1,this.registering=!1,this.events.emit("close")}onPayload(e){if(typeof e.data>"u")return;const r=typeof e.data=="string"?Or(e.data):e.data;this.events.emit("payload",r)}onError(e,r){const n=this.parseError(r),i=n.message||n.toString(),s=Cs(e,i);this.events.emit("payload",s)}parseError(e,r=this.url){return gf(e,r,"HTTP")}resetMaxListeners(){this.events.getMaxListeners()>Hp&&this.events.setMaxListeners(Hp)}}var VA=Object.defineProperty,KA=(t,e,r)=>e in t?VA(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Nn=(t,e,r)=>KA(t,typeof e!="symbol"?e+"":e,r);class WA{constructor(e){Nn(this,"name","polkadot"),Nn(this,"client"),Nn(this,"httpProviders"),Nn(this,"events"),Nn(this,"namespace"),Nn(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var n;const i=Kr(r);e[i]=this.createHttpProvider(i,(n=this.namespace.rpcMap)==null?void 0:n[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace,this.client.core.projectId);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);return new lt(new _t(n,Q("disableProviderPing")))}}var GA=Object.defineProperty,YA=Object.defineProperties,ZA=Object.getOwnPropertyDescriptors,Vp=Object.getOwnPropertySymbols,JA=Object.prototype.hasOwnProperty,XA=Object.prototype.propertyIsEnumerable,bc=(t,e,r)=>e in t?GA(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Kp=(t,e)=>{for(var r in e||(e={}))JA.call(e,r)&&bc(t,r,e[r]);if(Vp)for(var r of Vp(e))XA.call(e,r)&&bc(t,r,e[r]);return t},Wp=(t,e)=>YA(t,ZA(e)),Rn=(t,e,r)=>bc(t,typeof e!="symbol"?e+"":e,r);class QA{constructor(e){Rn(this,"name","eip155"),Rn(this,"client"),Rn(this,"chainId"),Rn(this,"namespace"),Rn(this,"httpProviders"),Rn(this,"events"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.httpProviders=this.createHttpProviders(),this.chainId=parseInt(this.getDefaultChain())}async request(e){switch(e.request.method){case"eth_requestAccounts":return this.getAccounts();case"eth_accounts":return this.getAccounts();case"wallet_switchEthereumChain":return await this.handleSwitchChain(e);case"eth_chainId":return parseInt(this.getDefaultChain());case"wallet_getCapabilities":return await this.getCapabilities(e);case"wallet_getCallsStatus":return await this.getCallStatus(e)}return this.namespace.methods.includes(e.request.method)?await this.client.request(e):this.getHttpProvider().request(e.request)}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(parseInt(e),r),this.chainId=parseInt(e),this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId.toString();if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}createHttpProvider(e,r){const n=r||ot(`${this.name}:${e}`,this.namespace,this.client.core.projectId);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);return new lt(new _t(n,Q("disableProviderPing")))}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var n;const i=parseInt(Kr(r));e[i]=this.createHttpProvider(i,(n=this.namespace.rpcMap)==null?void 0:n[r])}),e}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}getHttpProvider(){const e=this.chainId,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}async handleSwitchChain(e){var r,n;let i=e.request.params?(r=e.request.params[0])==null?void 0:r.chainId:"0x0";i=i.startsWith("0x")?i:`0x${i}`;const s=parseInt(i,16);if(this.isChainApproved(s))this.setDefaultChain(`${s}`);else if(this.namespace.methods.includes("wallet_switchEthereumChain"))await this.client.request({topic:e.topic,request:{method:e.request.method,params:[{chainId:i}]},chainId:(n=this.namespace.chains)==null?void 0:n[0]}),this.setDefaultChain(`${s}`);else throw new Error(`Failed to switch to chain 'eip155:${s}'. The chain is not approved or the wallet does not support 'wallet_switchEthereumChain' method.`);return null}isChainApproved(e){return this.namespace.chains.includes(`${this.name}:${e}`)}async getCapabilities(e){var r,n,i,s,o;const a=(n=(r=e.request)==null?void 0:r.params)==null?void 0:n[0],c=((s=(i=e.request)==null?void 0:i.params)==null?void 0:s[1])||[],u=`${a}${c.join(",")}`;if(!a)throw new Error("Missing address parameter in `wallet_getCapabilities` request");const l=this.client.session.get(e.topic),h=((o=l?.sessionProperties)==null?void 0:o.capabilities)||{};if(h!=null&&h[u])return h?.[u];const d=await this.client.request(e);try{await this.client.session.update(e.topic,{sessionProperties:Wp(Kp({},l.sessionProperties||{}),{capabilities:Wp(Kp({},h||{}),{[u]:d})})})}catch(f){console.warn("Failed to update session with capabilities",f)}return d}async getCallStatus(e){var r,n;const i=this.client.session.get(e.topic),s=(r=i.sessionProperties)==null?void 0:r.bundler_name;if(s){const a=this.getBundlerUrl(e.chainId,s);try{return await this.getUserOperationReceipt(a,e)}catch(c){console.warn("Failed to fetch call status from bundler",c,a)}}const o=(n=i.sessionProperties)==null?void 0:n.bundler_url;if(o)try{return await this.getUserOperationReceipt(o,e)}catch(a){console.warn("Failed to fetch call status from custom bundler",a,o)}if(this.namespace.methods.includes(e.request.method))return await this.client.request(e);throw new Error("Fetching call status not approved by the wallet.")}async getUserOperationReceipt(e,r){var n;const i=new URL(e),s=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(br("eth_getUserOperationReceipt",[(n=r.request.params)==null?void 0:n[0]]))});if(!s.ok)throw new Error(`Failed to fetch user operation receipt - ${s.status}`);return await s.json()}getBundlerUrl(e,r){return`${nA}?projectId=${this.client.core.projectId}&chainId=${e}&bundler=${r}`}}var eP=Object.defineProperty,tP=(t,e,r)=>e in t?eP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Tn=(t,e,r)=>tP(t,typeof e!="symbol"?e+"":e,r);class rP{constructor(e){Tn(this,"name","solana"),Tn(this,"client"),Tn(this,"httpProviders"),Tn(this,"events"),Tn(this,"namespace"),Tn(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var n;const i=Kr(r);e[i]=this.createHttpProvider(i,(n=this.namespace.rpcMap)==null?void 0:n[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace,this.client.core.projectId);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);return new lt(new _t(n,Q("disableProviderPing")))}}var nP=Object.defineProperty,iP=(t,e,r)=>e in t?nP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Bn=(t,e,r)=>iP(t,typeof e!="symbol"?e+"":e,r);class sP{constructor(e){Bn(this,"name","cosmos"),Bn(this,"client"),Bn(this,"httpProviders"),Bn(this,"events"),Bn(this,"namespace"),Bn(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var n;const i=Kr(r);e[i]=this.createHttpProvider(i,(n=this.namespace.rpcMap)==null?void 0:n[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace,this.client.core.projectId);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);return new lt(new _t(n,Q("disableProviderPing")))}}var oP=Object.defineProperty,aP=(t,e,r)=>e in t?oP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Fn=(t,e,r)=>aP(t,typeof e!="symbol"?e+"":e,r);class cP{constructor(e){Fn(this,"name","algorand"),Fn(this,"client"),Fn(this,"httpProviders"),Fn(this,"events"),Fn(this,"namespace"),Fn(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){if(!this.httpProviders[e]){const n=r||ot(`${this.name}:${e}`,this.namespace,this.client.core.projectId);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,n)}this.chainId=e,this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var n;e[r]=this.createHttpProvider(r,(n=this.namespace.rpcMap)==null?void 0:n[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace,this.client.core.projectId);return typeof n>"u"?void 0:new lt(new _t(n,Q("disableProviderPing")))}}var uP=Object.defineProperty,lP=(t,e,r)=>e in t?uP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Ln=(t,e,r)=>lP(t,typeof e!="symbol"?e+"":e,r);class hP{constructor(e){Ln(this,"name","cip34"),Ln(this,"client"),Ln(this,"httpProviders"),Ln(this,"events"),Ln(this,"namespace"),Ln(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{const n=this.getCardanoRPCUrl(r),i=Kr(r);e[i]=this.createHttpProvider(i,n)}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}getCardanoRPCUrl(e){const r=this.namespace.rpcMap;if(r)return r[e]}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||this.getCardanoRPCUrl(e);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);return new lt(new _t(n,Q("disableProviderPing")))}}var dP=Object.defineProperty,fP=(t,e,r)=>e in t?dP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Un=(t,e,r)=>fP(t,typeof e!="symbol"?e+"":e,r);class pP{constructor(e){Un(this,"name","elrond"),Un(this,"client"),Un(this,"httpProviders"),Un(this,"events"),Un(this,"namespace"),Un(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var n;const i=Kr(r);e[i]=this.createHttpProvider(i,(n=this.namespace.rpcMap)==null?void 0:n[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace,this.client.core.projectId);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);return new lt(new _t(n,Q("disableProviderPing")))}}var gP=Object.defineProperty,yP=(t,e,r)=>e in t?gP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,jn=(t,e,r)=>yP(t,typeof e!="symbol"?e+"":e,r);class mP{constructor(e){jn(this,"name","multiversx"),jn(this,"client"),jn(this,"httpProviders"),jn(this,"events"),jn(this,"namespace"),jn(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var n;const i=Kr(r);e[i]=this.createHttpProvider(i,(n=this.namespace.rpcMap)==null?void 0:n[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace,this.client.core.projectId);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);return new lt(new _t(n,Q("disableProviderPing")))}}var wP=Object.defineProperty,bP=(t,e,r)=>e in t?wP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,qn=(t,e,r)=>bP(t,typeof e!="symbol"?e+"":e,r);class vP{constructor(e){qn(this,"name","near"),qn(this,"client"),qn(this,"httpProviders"),qn(this,"events"),qn(this,"namespace"),qn(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){if(this.chainId=e,!this.httpProviders[e]){const n=r||ot(`${this.name}:${e}`,this.namespace);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,n)}this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{var n;e[r]=this.createHttpProvider(r,(n=this.namespace.rpcMap)==null?void 0:n[r])}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace);return typeof n>"u"?void 0:new lt(new _t(n,Q("disableProviderPing")))}}var EP=Object.defineProperty,_P=(t,e,r)=>e in t?EP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,kn=(t,e,r)=>_P(t,typeof e!="symbol"?e+"":e,r);class IP{constructor(e){kn(this,"name","tezos"),kn(this,"client"),kn(this,"httpProviders"),kn(this,"events"),kn(this,"namespace"),kn(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace=Object.assign(this.namespace,e)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider().request(e.request)}setDefaultChain(e,r){if(this.chainId=e,!this.httpProviders[e]){const n=r||ot(`${this.name}:${e}`,this.namespace);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);this.setHttpProvider(e,n)}this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const e=this.namespace.accounts;return e?e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2])||[]:[]}createHttpProviders(){const e={};return this.namespace.chains.forEach(r=>{e[r]=this.createHttpProvider(r)}),e}getHttpProvider(){const e=`${this.name}:${this.chainId}`,r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace);return typeof n>"u"?void 0:new lt(new _t(n))}}var SP=Object.defineProperty,xP=(t,e,r)=>e in t?SP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Mn=(t,e,r)=>xP(t,typeof e!="symbol"?e+"":e,r);class OP{constructor(e){Mn(this,"name",Pn),Mn(this,"client"),Mn(this,"httpProviders"),Mn(this,"events"),Mn(this,"namespace"),Mn(this,"chainId"),this.namespace=e.namespace,this.events=Q("events"),this.client=Q("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(e){this.namespace.chains=[...new Set((this.namespace.chains||[]).concat(e.chains||[]))],this.namespace.accounts=[...new Set((this.namespace.accounts||[]).concat(e.accounts||[]))],this.namespace.methods=[...new Set((this.namespace.methods||[]).concat(e.methods||[]))],this.namespace.events=[...new Set((this.namespace.events||[]).concat(e.events||[]))],this.httpProviders=this.createHttpProviders()}requestAccounts(){return this.getAccounts()}request(e){return this.namespace.methods.includes(e.request.method)?this.client.request(e):this.getHttpProvider(e.chainId).request(e.request)}setDefaultChain(e,r){this.httpProviders[e]||this.setHttpProvider(e,r),this.chainId=e,this.events.emit(ft.DEFAULT_CHAIN_CHANGED,`${this.name}:${e}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const e=this.namespace.chains[0];if(!e)throw new Error("ChainId not found");return e.split(":")[1]}getAccounts(){const e=this.namespace.accounts;return e?[...new Set(e.filter(r=>r.split(":")[1]===this.chainId.toString()).map(r=>r.split(":")[2]))]:[]}createHttpProviders(){var e,r;const n={};return(r=(e=this.namespace)==null?void 0:e.accounts)==null||r.forEach(i=>{const s=dn(i);n[`${s.namespace}:${s.reference}`]=this.createHttpProvider(i)}),n}getHttpProvider(e){const r=this.httpProviders[e];if(typeof r>"u")throw new Error(`JSON-RPC provider for ${e} not found`);return r}setHttpProvider(e,r){const n=this.createHttpProvider(e,r);n&&(this.httpProviders[e]=n)}createHttpProvider(e,r){const n=r||ot(e,this.namespace,this.client.core.projectId);if(!n)throw new Error(`No RPC url provided for chainId: ${e}`);return new lt(new _t(n,Q("disableProviderPing")))}}var DP=Object.defineProperty,$P=Object.defineProperties,AP=Object.getOwnPropertyDescriptors,Gp=Object.getOwnPropertySymbols,PP=Object.prototype.hasOwnProperty,CP=Object.prototype.propertyIsEnumerable,vc=(t,e,r)=>e in t?DP(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,zs=(t,e)=>{for(var r in e||(e={}))PP.call(e,r)&&vc(t,r,e[r]);if(Gp)for(var r of Gp(e))CP.call(e,r)&&vc(t,r,e[r]);return t},Ec=(t,e)=>$P(t,AP(e)),pt=(t,e,r)=>vc(t,typeof e!="symbol"?e+"":e,r);class Hs{constructor(e){pt(this,"client"),pt(this,"namespaces"),pt(this,"optionalNamespaces"),pt(this,"sessionProperties"),pt(this,"scopedProperties"),pt(this,"events",new Ue.exports),pt(this,"rpcProviders",{}),pt(this,"session"),pt(this,"providerOpts"),pt(this,"logger"),pt(this,"uri"),pt(this,"disableProviderPing",!1),this.providerOpts=e,this.logger=typeof e?.logger<"u"&&typeof e?.logger!="string"?e.logger:It(Hi({level:e?.logger||vp})),this.disableProviderPing=e?.disableProviderPing||!1}static async init(e){const r=new Hs(e);return await r.initialize(),r}async request(e,r,n){const[i,s]=this.validateChain(r);if(!this.session)throw new Error("Please call connect() before request()");return await this.getProvider(i).request({request:zs({},e),chainId:`${i}:${s}`,topic:this.session.topic,expiry:n})}sendAsync(e,r,n,i){const s=new Date().getTime();this.request(e,n,i).then(o=>r(null,Ps(s,o))).catch(o=>r(o,void 0))}async enable(){if(!this.client)throw new Error("Sign Client not initialized");return this.session||await this.connect({namespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties,scopedProperties:this.scopedProperties}),await this.requestAccounts()}async disconnect(){var e;if(!this.session)throw new Error("Please call connect() before enable()");await this.client.disconnect({topic:(e=this.session)==null?void 0:e.topic,reason:ie("USER_DISCONNECTED")}),await this.cleanup()}async connect(e){if(!this.client)throw new Error("Sign Client not initialized");if(this.setNamespaces(e),await this.cleanupPendingPairings(),!e.skipPairing)return await this.pair(e.pairingTopic)}async authenticate(e,r){if(!this.client)throw new Error("Sign Client not initialized");this.setNamespaces(e),await this.cleanupPendingPairings();const{uri:n,response:i}=await this.client.authenticate(e,r);n&&(this.uri=n,this.events.emit("display_uri",n));const s=await i();if(this.session=s.session,this.session){const o=Fp(this.session.namespaces);this.namespaces=Ms(this.namespaces,o),await this.persist("namespaces",this.namespaces),this.onConnect()}return s}on(e,r){this.events.on(e,r)}once(e,r){this.events.once(e,r)}removeListener(e,r){this.events.removeListener(e,r)}off(e,r){this.events.off(e,r)}get isWalletConnect(){return!0}async pair(e){const{uri:r,approval:n}=await this.client.connect({pairingTopic:e,requiredNamespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties,scopedProperties:this.scopedProperties});r&&(this.uri=r,this.events.emit("display_uri",r));const i=await n();this.session=i;const s=Fp(i.namespaces);return this.namespaces=Ms(this.namespaces,s),await this.persist("namespaces",this.namespaces),await this.persist("optionalNamespaces",this.optionalNamespaces),this.onConnect(),this.session}setDefaultChain(e,r){try{if(!this.session)return;const[n,i]=this.validateChain(e),s=this.getProvider(n);s.name===Pn?s.setDefaultChain(`${n}:${i}`,r):s.setDefaultChain(i,r)}catch(n){if(!/Please call connect/.test(n.message))throw n}}async cleanupPendingPairings(e={}){this.logger.info("Cleaning up inactive pairings...");const r=this.client.pairing.getAll();if(mr(r)){for(const n of r)e.deletePairings?this.client.core.expirer.set(n.topic,0):await this.client.core.relayer.subscriber.unsubscribe(n.topic);this.logger.info(`Inactive pairings cleared: ${r.length}`)}}abortPairingAttempt(){this.logger.warn("abortPairingAttempt is deprecated. This is now a no-op.")}async checkStorage(){this.namespaces=await this.getFromStore("namespaces")||{},this.optionalNamespaces=await this.getFromStore("optionalNamespaces")||{},this.session&&this.createProviders()}async initialize(){this.logger.trace("Initialized"),await this.createClient(),await this.checkStorage(),this.registerEventListeners()}async createClient(){var e,r;if(this.client=this.providerOpts.client||await fc.init({core:this.providerOpts.core,logger:this.providerOpts.logger||vp,relayUrl:this.providerOpts.relayUrl||eA,projectId:this.providerOpts.projectId,metadata:this.providerOpts.metadata,storageOptions:this.providerOpts.storageOptions,storage:this.providerOpts.storage,name:this.providerOpts.name,customStoragePrefix:this.providerOpts.customStoragePrefix,telemetryEnabled:this.providerOpts.telemetryEnabled}),this.providerOpts.session)try{this.session=this.client.session.get(this.providerOpts.session.topic)}catch(n){throw this.logger.error("Failed to get session",n),new Error(`The provided session: ${(r=(e=this.providerOpts)==null?void 0:e.session)==null?void 0:r.topic} doesn't exist in the Sign client`)}else{const n=this.client.session.getAll();this.session=n[0]}this.logger.trace("SignClient Initialized")}createProviders(){if(!this.client)throw new Error("Sign Client not initialized");if(!this.session)throw new Error("Session not initialized. Please call connect() before enable()");const e=[...new Set(Object.keys(this.session.namespaces).map(r=>Dn(r)))];mc("client",this.client),mc("events",this.events),mc("disableProviderPing",this.disableProviderPing),e.forEach(r=>{if(!this.session)return;const n=LA(r,this.session),i=Rp(n),s=Ms(this.namespaces,this.optionalNamespaces),o=Ec(zs({},s[r]),{accounts:n,chains:i});switch(r){case"eip155":this.rpcProviders[r]=new QA({namespace:o});break;case"algorand":this.rpcProviders[r]=new cP({namespace:o});break;case"solana":this.rpcProviders[r]=new rP({namespace:o});break;case"cosmos":this.rpcProviders[r]=new sP({namespace:o});break;case"polkadot":this.rpcProviders[r]=new WA({namespace:o});break;case"cip34":this.rpcProviders[r]=new hP({namespace:o});break;case"elrond":this.rpcProviders[r]=new pP({namespace:o});break;case"multiversx":this.rpcProviders[r]=new mP({namespace:o});break;case"near":this.rpcProviders[r]=new vP({namespace:o});break;case"tezos":this.rpcProviders[r]=new IP({namespace:o});break;default:this.rpcProviders[Pn]?this.rpcProviders[Pn].updateNamespace(o):this.rpcProviders[Pn]=new OP({namespace:o})}})}registerEventListeners(){if(typeof this.client>"u")throw new Error("Sign Client is not initialized");this.client.on("session_ping",e=>{var r;const{topic:n}=e;n===((r=this.session)==null?void 0:r.topic)&&this.events.emit("session_ping",e)}),this.client.on("session_event",e=>{var r;const{params:n,topic:i}=e;if(i!==((r=this.session)==null?void 0:r.topic))return;const{event:s}=n;if(s.name==="accountsChanged"){const o=s.data;o&&mr(o)&&this.events.emit("accountsChanged",o.map(Bp))}else if(s.name==="chainChanged"){const o=n.chainId,a=n.event.data,c=Dn(o),u=yc(o)!==yc(a)?`${c}:${yc(a)}`:o;this.onChainChanged(u)}else this.events.emit(s.name,s.data);this.events.emit("session_event",e)}),this.client.on("session_update",({topic:e,params:r})=>{var n,i;if(e!==((n=this.session)==null?void 0:n.topic))return;const{namespaces:s}=r,o=(i=this.client)==null?void 0:i.session.get(e);this.session=Ec(zs({},o),{namespaces:s}),this.onSessionUpdate(),this.events.emit("session_update",{topic:e,params:r})}),this.client.on("session_delete",async e=>{var r;e.topic===((r=this.session)==null?void 0:r.topic)&&(await this.cleanup(),this.events.emit("session_delete",e),this.events.emit("disconnect",Ec(zs({},ie("USER_DISCONNECTED")),{data:e.topic})))}),this.on(ft.DEFAULT_CHAIN_CHANGED,e=>{this.onChainChanged(e,!0)})}getProvider(e){return this.rpcProviders[e]||this.rpcProviders[Pn]}onSessionUpdate(){Object.keys(this.rpcProviders).forEach(e=>{var r;this.getProvider(e).updateNamespace((r=this.session)==null?void 0:r.namespaces[e])})}setNamespaces(e){const{namespaces:r={},optionalNamespaces:n={},sessionProperties:i,scopedProperties:s}=e;this.optionalNamespaces=Ms(r,n),this.sessionProperties=i,this.scopedProperties=s}validateChain(e){const[r,n]=e?.split(":")||["",""];if(!this.namespaces||!Object.keys(this.namespaces).length)return[r,n];if(r&&!Object.keys(this.namespaces||{}).map(o=>Dn(o)).includes(r))throw new Error(`Namespace '${r}' is not configured. Please call connect() first with namespace config.`);if(r&&n)return[r,n];const i=Dn(Object.keys(this.namespaces)[0]),s=this.rpcProviders[i].getDefaultChain();return[i,s]}async requestAccounts(){const[e]=this.validateChain();return await this.getProvider(e).requestAccounts()}async onChainChanged(e,r=!1){if(!this.namespaces)return;const[n,i]=this.validateChain(e);if(!i)return;this.updateNamespaceChain(n,i),this.events.emit("chainChanged",i);const s=this.getProvider(n).getDefaultChain();r||this.getProvider(n).setDefaultChain(i),this.emitAccountsChangedOnChainChange({namespace:n,previousChainId:s,newChainId:e}),await this.persist("namespaces",this.namespaces)}emitAccountsChangedOnChainChange({namespace:e,previousChainId:r,newChainId:n}){var i,s;try{if(r===n)return;const o=(s=(i=this.session)==null?void 0:i.namespaces[e])==null?void 0:s.accounts;if(!o)return;const a=o.filter(c=>c.includes(`${n}:`)).map(Bp);if(!mr(a))return;this.events.emit("accountsChanged",a)}catch(o){this.logger.warn("Failed to emit accountsChanged on chain change",o)}}updateNamespaceChain(e,r){if(!this.namespaces)return;const n=this.namespaces[e]?e:`${e}:${r}`,i={chains:[],methods:[],events:[],defaultChain:r};this.namespaces[n]?this.namespaces[n]&&(this.namespaces[n].defaultChain=r):this.namespaces[n]=i}onConnect(){this.createProviders(),this.events.emit("connect",{session:this.session})}async cleanup(){this.namespaces=void 0,this.optionalNamespaces=void 0,this.sessionProperties=void 0,await this.deleteFromStore("namespaces"),await this.deleteFromStore("optionalNamespaces"),await this.deleteFromStore("sessionProperties"),this.session=void 0,await this.cleanupPendingPairings({deletePairings:!0}),await this.cleanupStorage()}async persist(e,r){var n;const i=((n=this.session)==null?void 0:n.topic)||"";await this.client.core.storage.setItem(`${js}/${e}${i}`,r)}async getFromStore(e){var r;const n=((r=this.session)==null?void 0:r.topic)||"";return await this.client.core.storage.getItem(`${js}/${e}${n}`)}async deleteFromStore(e){var r;const n=((r=this.session)==null?void 0:r.topic)||"";await this.client.core.storage.removeItem(`${js}/${e}${n}`)}async cleanupStorage(){var e;try{if(((e=this.client)==null?void 0:e.session.length)>0)return;const r=await this.client.core.storage.getKeys();for(const n of r)n.startsWith(js)&&await this.client.core.storage.removeItem(n)}catch(r){this.logger.warn("Failed to cleanup storage",r)}}}const NP=Hs;vr.UniversalProvider=NP,vr.default=Hs,Object.defineProperty(vr,"__esModule",{value:!0})});
//# sourceMappingURL=index.umd.js.map
