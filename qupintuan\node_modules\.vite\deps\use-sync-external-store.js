import {
  require_react
} from "./chunk-VT65UQWK.js";
import {
  __commonJS
} from "./chunk-ONY6HBPH.js";

// node_modules/use-sync-external-store/cjs/use-sync-external-store.development.js
var require_use_sync_external_store_development = __commonJS({
  "node_modules/use-sync-external-store/cjs/use-sync-external-store.development.js"(exports) {
    "use strict";
    if (true) {
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
      useSyncExternalStore$jscomp$inline_1 = require_react().useSyncExternalStore;
      console.error(
        "The main 'use-sync-external-store' entry point is not supported; all it does is re-export useSyncExternalStore from the 'react' package, so it only works with React 18+.\n\nIf you wish to support React 16 and 17, import from 'use-sync-external-store/shim' instead. It will fall back to a shimmed implementation when the native one is not available.\n\nIf you only support React 18+, you can import directly from 'react'."
      );
      exports.useSyncExternalStore = useSyncExternalStore$jscomp$inline_1;
      "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
    }
    var useSyncExternalStore$jscomp$inline_1;
  }
});

// node_modules/use-sync-external-store/index.js
var require_use_sync_external_store = __commonJS({
  "node_modules/use-sync-external-store/index.js"(exports, module) {
    if (false) {
      module.exports = null;
    } else {
      module.exports = require_use_sync_external_store_development();
    }
  }
});
export default require_use_sync_external_store();
/*! Bundled license information:

use-sync-external-store/cjs/use-sync-external-store.development.js:
  (**
   * @license React
   * use-sync-external-store.development.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=use-sync-external-store.js.map
