{"version": 3, "sources": ["../../@walletconnect/utils/node_modules/@noble/hashes/src/_md.ts", "../../@walletconnect/utils/node_modules/@noble/hashes/src/sha256.ts", "../../@walletconnect/utils/node_modules/@noble/hashes/src/hmac.ts", "../../@walletconnect/utils/node_modules/@noble/curves/src/abstract/utils.ts", "../../@walletconnect/utils/node_modules/@noble/curves/src/abstract/modular.ts", "../../@walletconnect/utils/node_modules/@noble/curves/src/abstract/curve.ts", "../../@walletconnect/utils/node_modules/@noble/curves/src/abstract/weierstrass.ts", "../../@walletconnect/utils/node_modules/@noble/curves/src/_shortw_utils.ts", "../../@walletconnect/utils/node_modules/@noble/curves/src/abstract/hash-to-curve.ts", "../../@walletconnect/utils/node_modules/@noble/curves/src/secp256k1.ts"], "sourcesContent": ["/**\n * Internal Merkle-<PERSON>gard hash utils.\n * @module\n */\nimport { aexists, aoutput } from './_assert.js';\nimport { type Input, Hash, createView, toBytes } from './utils.js';\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nexport function setBigUint64(\n  view: DataView,\n  byteOffset: number,\n  value: bigint,\n  isLE: boolean\n): void {\n  if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);\n  const _32n = BigInt(32);\n  const _u32_max = BigInt(0xffffffff);\n  const wh = Number((value >> _32n) & _u32_max);\n  const wl = Number(value & _u32_max);\n  const h = isLE ? 4 : 0;\n  const l = isLE ? 0 : 4;\n  view.setUint32(byteOffset + h, wh, isLE);\n  view.setUint32(byteOffset + l, wl, isLE);\n}\n\n/** Choice: a ? b : c */\nexport function Chi(a: number, b: number, c: number): number {\n  return (a & b) ^ (~a & c);\n}\n\n/** Majority function, true if any two inputs is true. */\nexport function Maj(a: number, b: number, c: number): number {\n  return (a & b) ^ (a & c) ^ (b & c);\n}\n\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nexport abstract class HashMD<T extends HashMD<T>> extends Hash<T> {\n  protected abstract process(buf: DataView, offset: number): void;\n  protected abstract get(): number[];\n  protected abstract set(...args: number[]): void;\n  abstract destroy(): void;\n  protected abstract roundClean(): void;\n  // For partial updates less than block size\n  protected buffer: Uint8Array;\n  protected view: DataView;\n  protected finished = false;\n  protected length = 0;\n  protected pos = 0;\n  protected destroyed = false;\n\n  constructor(\n    readonly blockLen: number,\n    public outputLen: number,\n    readonly padOffset: number,\n    readonly isLE: boolean\n  ) {\n    super();\n    this.buffer = new Uint8Array(blockLen);\n    this.view = createView(this.buffer);\n  }\n  update(data: Input): this {\n    aexists(this);\n    const { view, buffer, blockLen } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      // Fast path: we have at least one block in input, cast it to view and process\n      if (take === blockLen) {\n        const dataView = createView(data);\n        for (; blockLen <= len - pos; pos += blockLen) this.process(dataView, pos);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      pos += take;\n      if (this.pos === blockLen) {\n        this.process(view, 0);\n        this.pos = 0;\n      }\n    }\n    this.length += data.length;\n    this.roundClean();\n    return this;\n  }\n  digestInto(out: Uint8Array): void {\n    aexists(this);\n    aoutput(out, this);\n    this.finished = true;\n    // Padding\n    // We can avoid allocation of buffer for padding completely if it\n    // was previously not allocated here. But it won't change performance.\n    const { buffer, view, blockLen, isLE } = this;\n    let { pos } = this;\n    // append the bit '1' to the message\n    buffer[pos++] = 0b10000000;\n    this.buffer.subarray(pos).fill(0);\n    // we have less than padOffset left in buffer, so we cannot put length in\n    // current block, need process it and pad again\n    if (this.padOffset > blockLen - pos) {\n      this.process(view, 0);\n      pos = 0;\n    }\n    // Pad until full block byte with zeros\n    for (let i = pos; i < blockLen; i++) buffer[i] = 0;\n    // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n    // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n    // So we just write lowest 64 bits of that value.\n    setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n    this.process(view, 0);\n    const oview = createView(out);\n    const len = this.outputLen;\n    // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n    if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');\n    const outLen = len / 4;\n    const state = this.get();\n    if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');\n    for (let i = 0; i < outLen; i++) oview.setUint32(4 * i, state[i], isLE);\n  }\n  digest(): Uint8Array {\n    const { buffer, outputLen } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to?: T): T {\n    to ||= new (this.constructor as any)() as T;\n    to.set(...this.get());\n    const { blockLen, buffer, length, finished, destroyed, pos } = this;\n    to.length = length;\n    to.pos = pos;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    if (length % blockLen) to.buffer.set(buffer);\n    return to;\n  }\n}\n", "/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake<PERSON>.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nimport { Chi, HashMD, Maj } from './_md.js';\nimport { type CHash, rotr, wrapConstructor } from './utils.js';\n\n/** Round constants: first 32 bits of fractional parts of the cube roots of the first 64 primes 2..311). */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n\n/** Initial state: first 32 bits of fractional parts of the square roots of the first 8 primes 2..19. */\n// prettier-ignore\nconst SHA256_IV = /* @__PURE__ */ new Uint32Array([\n  0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n\n/**\n * Temporary buffer, not used to store anything between runs.\n * Named this way because it matches specification.\n */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nexport class SHA256 extends HashMD<SHA256> {\n  // We cannot use array here since array allows indexing by variable\n  // which means optimizer/compiler cannot use registers.\n  protected A: number = SHA256_IV[0] | 0;\n  protected B: number = SHA256_IV[1] | 0;\n  protected C: number = SHA256_IV[2] | 0;\n  protected D: number = SHA256_IV[3] | 0;\n  protected E: number = SHA256_IV[4] | 0;\n  protected F: number = SHA256_IV[5] | 0;\n  protected G: number = SHA256_IV[6] | 0;\n  protected H: number = SHA256_IV[7] | 0;\n\n  constructor() {\n    super(64, 32, 8, false);\n  }\n  protected get(): [number, number, number, number, number, number, number, number] {\n    const { A, B, C, D, E, F, G, H } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  protected set(\n    A: number, B: number, C: number, D: number, E: number, F: number, G: number, H: number\n  ): void {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n      SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let { A, B, C, D, E, F, G, H } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = (sigma0 + Maj(A, B, C)) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = (D + T1) | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = (T1 + T2) | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = (A + this.A) | 0;\n    B = (B + this.B) | 0;\n    C = (C + this.C) | 0;\n    D = (D + this.D) | 0;\n    E = (E + this.E) | 0;\n    F = (F + this.F) | 0;\n    G = (G + this.G) | 0;\n    H = (H + this.H) | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  protected roundClean(): void {\n    SHA256_W.fill(0);\n  }\n  destroy(): void {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    this.buffer.fill(0);\n  }\n}\n\n/**\n * Constants taken from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf.\n */\nclass SHA224 extends SHA256 {\n  protected A = 0xc1059ed8 | 0;\n  protected B = 0x367cd507 | 0;\n  protected C = 0x3070dd17 | 0;\n  protected D = 0xf70e5939 | 0;\n  protected E = 0xffc00b31 | 0;\n  protected F = 0x68581511 | 0;\n  protected G = 0x64f98fa7 | 0;\n  protected H = 0xbefa4fa4 | 0;\n  constructor() {\n    super();\n    this.outputLen = 28;\n  }\n}\n\n/** SHA2-256 hash function */\nexport const sha256: CHash = /* @__PURE__ */ wrapConstructor(() => new SHA256());\n/** SHA2-224 hash function */\nexport const sha224: CHash = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n", "/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\nimport { abytes, aexists, ahash } from './_assert.js';\nimport { Hash, toBytes, type CHash, type Input } from './utils.js';\n\nexport class HMAC<T extends Hash<T>> extends Hash<HMAC<T>> {\n  oHash: T;\n  iHash: T;\n  blockLen: number;\n  outputLen: number;\n  private finished = false;\n  private destroyed = false;\n\n  constructor(hash: CHash, _key: Input) {\n    super();\n    ahash(hash);\n    const key = toBytes(_key);\n    this.iHash = hash.create() as T;\n    if (typeof this.iHash.update !== 'function')\n      throw new Error('Expected instance of class which extends utils.Hash');\n    this.blockLen = this.iHash.blockLen;\n    this.outputLen = this.iHash.outputLen;\n    const blockLen = this.blockLen;\n    const pad = new Uint8Array(blockLen);\n    // blockLen can be bigger than outputLen\n    pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36;\n    this.iHash.update(pad);\n    // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n    this.oHash = hash.create() as T;\n    // Undo internal XOR && apply outer XOR\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36 ^ 0x5c;\n    this.oHash.update(pad);\n    pad.fill(0);\n  }\n  update(buf: Input): this {\n    aexists(this);\n    this.iHash.update(buf);\n    return this;\n  }\n  digestInto(out: Uint8Array): void {\n    aexists(this);\n    abytes(out, this.outputLen);\n    this.finished = true;\n    this.iHash.digestInto(out);\n    this.oHash.update(out);\n    this.oHash.digestInto(out);\n    this.destroy();\n  }\n  digest(): Uint8Array {\n    const out = new Uint8Array(this.oHash.outputLen);\n    this.digestInto(out);\n    return out;\n  }\n  _cloneInto(to?: HMAC<T>): HMAC<T> {\n    // Create new instance without calling constructor since key already in state and we don't know it.\n    to ||= Object.create(Object.getPrototypeOf(this), {});\n    const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n    to = to as this;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    to.blockLen = blockLen;\n    to.outputLen = outputLen;\n    to.oHash = oHash._cloneInto(to.oHash);\n    to.iHash = iHash._cloneInto(to.iHash);\n    return to;\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.oHash.destroy();\n    this.iHash.destroy();\n  }\n}\n\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nexport const hmac: {\n  (hash: CHash, key: Input, message: Input): Uint8Array;\n  create(hash: CHash, key: Input): HMAC<any>;\n} = (hash: CHash, key: Input, message: Input): Uint8Array =>\n  new HMAC<any>(hash, key).update(message).digest();\nhmac.create = (hash: CHash, key: Input) => new HMAC<any>(hash, key);\n", "/**\n * Hex, bytes and number utilities.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nexport type Hex = Uint8Array | string; // hex strings are accepted for simplicity\nexport type PrivKey = Hex | bigint; // bigints are accepted to ease learning curve\nexport type CHash = {\n  (message: Uint8Array | string): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create(opts?: { dkLen?: number }): any; // For shake\n};\nexport type FHash = (message: Uint8Array | string) => Uint8Array;\n\nexport function isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n\nexport function abytes(item: unknown): void {\n  if (!isBytes(item)) throw new Error('Uint8Array expected');\n}\n\nexport function abool(title: string, value: boolean): void {\n  if (typeof value !== 'boolean') throw new Error(title + ' boolean expected, got ' + value);\n}\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  abytes(bytes);\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\nexport function numberToHexUnpadded(num: number | bigint): string {\n  const hex = num.toString(16);\n  return hex.length & 1 ? '0' + hex : hex;\n}\n\nexport function hexToNumber(hex: string): bigint {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  return hex === '' ? _0n : BigInt('0x' + hex); // Big Endian\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 } as const;\nfunction asciiToBase16(ch: number): number | undefined {\n  if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48\n  if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n  if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n  return;\n}\n\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n  }\n  return array;\n}\n\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes: Uint8Array): bigint {\n  return hexToNumber(bytesToHex(bytes));\n}\nexport function bytesToNumberLE(bytes: Uint8Array): bigint {\n  abytes(bytes);\n  return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\n\nexport function numberToBytesBE(n: number | bigint, len: number): Uint8Array {\n  return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n: number | bigint, len: number): Uint8Array {\n  return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n: number | bigint): Uint8Array {\n  return hexToBytes(numberToHexUnpadded(n));\n}\n\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title: string, hex: Hex, expectedLength?: number): Uint8Array {\n  let res: Uint8Array;\n  if (typeof hex === 'string') {\n    try {\n      res = hexToBytes(hex);\n    } catch (e) {\n      throw new Error(title + ' must be hex string or Uint8Array, cause: ' + e);\n    }\n  } else if (isBytes(hex)) {\n    // Uint8Array.from() instead of hash.slice() because node.js Buffer\n    // is instance of Uint8Array, and its slice() creates **mutable** copy\n    res = Uint8Array.from(hex);\n  } else {\n    throw new Error(title + ' must be hex string or Uint8Array');\n  }\n  const len = res.length;\n  if (typeof expectedLength === 'number' && len !== expectedLength)\n    throw new Error(title + ' of length ' + expectedLength + ' expected, got ' + len);\n  return res;\n}\n\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\n// Compares 2 u8a-s in kinda constant time\nexport function equalBytes(a: Uint8Array, b: Uint8Array): boolean {\n  if (a.length !== b.length) return false;\n  let diff = 0;\n  for (let i = 0; i < a.length; i++) diff |= a[i] ^ b[i];\n  return diff === 0;\n}\n\n// Global symbols in both browsers and Node.js since v11\n// See https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\n\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error('string expected');\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\n// Is positive bigint\nconst isPosBig = (n: bigint) => typeof n === 'bigint' && _0n <= n;\n\nexport function inRange(n: bigint, min: bigint, max: bigint): boolean {\n  return isPosBig(n) && isPosBig(min) && isPosBig(max) && min <= n && n < max;\n}\n\n/**\n * Asserts min <= n < max. NOTE: It's < max and not <= max.\n * @example\n * aInRange('x', x, 1n, 256n); // would assume x is in (1n..255n)\n */\nexport function aInRange(title: string, n: bigint, min: bigint, max: bigint): void {\n  // Why min <= n < max and not a (min < n < max) OR b (min <= n <= max)?\n  // consider P=256n, min=0n, max=P\n  // - a for min=0 would require -1:          `inRange('x', x, -1n, P)`\n  // - b would commonly require subtraction:  `inRange('x', x, 0n, P - 1n)`\n  // - our way is the cleanest:               `inRange('x', x, 0n, P)\n  if (!inRange(n, min, max))\n    throw new Error('expected valid ' + title + ': ' + min + ' <= n < ' + max + ', got ' + n);\n}\n\n// Bit operations\n\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nexport function bitLen(n: bigint): number {\n  let len;\n  for (len = 0; n > _0n; n >>= _1n, len += 1);\n  return len;\n}\n\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n: bigint, pos: number): bigint {\n  return (n >> BigInt(pos)) & _1n;\n}\n\n/**\n * Sets single bit at position.\n */\nexport function bitSet(n: bigint, pos: number, value: boolean): bigint {\n  return n | ((value ? _1n : _0n) << BigInt(pos));\n}\n\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n: number): bigint => (_2n << BigInt(n - 1)) - _1n;\n\n// DRBG\n\nconst u8n = (data?: any) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr: any) => Uint8Array.from(arr); // another shortcut\ntype Pred<T> = (v: Uint8Array) => T | undefined;\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg<T>(\n  hashLen: number,\n  qByteLen: number,\n  hmacFn: (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array\n): (seed: Uint8Array, predicate: Pred<T>) => T {\n  if (typeof hashLen !== 'number' || hashLen < 2) throw new Error('hashLen must be a number');\n  if (typeof qByteLen !== 'number' || qByteLen < 2) throw new Error('qByteLen must be a number');\n  if (typeof hmacFn !== 'function') throw new Error('hmacFn must be a function');\n  // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n  let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n  let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n  let i = 0; // Iterations counter, will throw when over 1000\n  const reset = () => {\n    v.fill(1);\n    k.fill(0);\n    i = 0;\n  };\n  const h = (...b: Uint8Array[]) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n  const reseed = (seed = u8n()) => {\n    // HMAC-DRBG reseed() function. Steps D-G\n    k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n    v = h(); // v = hmac(k || v)\n    if (seed.length === 0) return;\n    k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n    v = h(); // v = hmac(k || v)\n  };\n  const gen = () => {\n    // HMAC-DRBG generate() function\n    if (i++ >= 1000) throw new Error('drbg: tried 1000 values');\n    let len = 0;\n    const out: Uint8Array[] = [];\n    while (len < qByteLen) {\n      v = h();\n      const sl = v.slice();\n      out.push(sl);\n      len += v.length;\n    }\n    return concatBytes(...out);\n  };\n  const genUntil = (seed: Uint8Array, pred: Pred<T>): T => {\n    reset();\n    reseed(seed); // Steps D-G\n    let res: T | undefined = undefined; // Step H: grind until k is in [1..n-1]\n    while (!(res = pred(gen()))) reseed();\n    reset();\n    return res;\n  };\n  return genUntil;\n}\n\n// Validating curves and fields\n\nconst validatorFns = {\n  bigint: (val: any): boolean => typeof val === 'bigint',\n  function: (val: any): boolean => typeof val === 'function',\n  boolean: (val: any): boolean => typeof val === 'boolean',\n  string: (val: any): boolean => typeof val === 'string',\n  stringOrUint8Array: (val: any): boolean => typeof val === 'string' || isBytes(val),\n  isSafeInteger: (val: any): boolean => Number.isSafeInteger(val),\n  array: (val: any): boolean => Array.isArray(val),\n  field: (val: any, object: any): any => (object as any).Fp.isValid(val),\n  hash: (val: any): boolean => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n} as const;\ntype Validator = keyof typeof validatorFns;\ntype ValMap<T extends Record<string, any>> = { [K in keyof T]?: Validator };\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\n\nexport function validateObject<T extends Record<string, any>>(\n  object: T,\n  validators: ValMap<T>,\n  optValidators: ValMap<T> = {}\n): T {\n  const checkField = (fieldName: keyof T, type: Validator, isOptional: boolean) => {\n    const checkVal = validatorFns[type];\n    if (typeof checkVal !== 'function') throw new Error('invalid validator function');\n\n    const val = object[fieldName as keyof typeof object];\n    if (isOptional && val === undefined) return;\n    if (!checkVal(val, object)) {\n      throw new Error(\n        'param ' + String(fieldName) + ' is invalid. Expected ' + type + ', got ' + val\n      );\n    }\n  };\n  for (const [fieldName, type] of Object.entries(validators)) checkField(fieldName, type!, false);\n  for (const [fieldName, type] of Object.entries(optValidators)) checkField(fieldName, type!, true);\n  return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n\n/**\n * throws not implemented error\n */\nexport const notImplemented = (): never => {\n  throw new Error('not implemented');\n};\n\n/**\n * Memoizes (caches) computation result.\n * Uses WeakMap: the value is going auto-cleaned by GC after last reference is removed.\n */\nexport function memoized<T extends object, R, O extends any[]>(\n  fn: (arg: T, ...args: O) => R\n): (arg: T, ...args: O) => R {\n  const map = new WeakMap<T, R>();\n  return (arg: T, ...args: O): R => {\n    const val = map.get(arg);\n    if (val !== undefined) return val;\n    const computed = fn(arg, ...args);\n    map.set(arg, computed);\n    return computed;\n  };\n}\n", "/**\n * Utils for modular division and finite fields.\n * A finite field over 11 is integer number operations `mod 11`.\n * There is no division: it is replaced by modular multiplicative inverse.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport {\n  bitMask,\n  bytesToNumberBE,\n  bytesToNumberLE,\n  ensureBytes,\n  numberToBytesBE,\n  numberToBytesLE,\n  validateObject,\n} from './utils.js';\n\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = /* @__PURE__ */ BigInt(2), _3n = /* @__PURE__ */ BigInt(3);\n// prettier-ignore\nconst _4n = /* @__PURE__ */ BigInt(4), _5n = /* @__PURE__ */ BigInt(5), _8n = /* @__PURE__ */ BigInt(8);\n// prettier-ignore\nconst _9n =/* @__PURE__ */ BigInt(9), _16n = /* @__PURE__ */ BigInt(16);\n\n// Calculates a modulo b\nexport function mod(a: bigint, b: bigint): bigint {\n  const result = a % b;\n  return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @todo use field version && remove\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\nexport function pow(num: bigint, power: bigint, modulo: bigint): bigint {\n  if (power < _0n) throw new Error('invalid exponent, negatives unsupported');\n  if (modulo <= _0n) throw new Error('invalid modulus');\n  if (modulo === _1n) return _0n;\n  let res = _1n;\n  while (power > _0n) {\n    if (power & _1n) res = (res * num) % modulo;\n    num = (num * num) % modulo;\n    power >>= _1n;\n  }\n  return res;\n}\n\n/** Does `x^(2^power)` mod p. `pow2(30, 4)` == `30^(2^4)` */\nexport function pow2(x: bigint, power: bigint, modulo: bigint): bigint {\n  let res = x;\n  while (power-- > _0n) {\n    res *= res;\n    res %= modulo;\n  }\n  return res;\n}\n\n/**\n * Inverses number over modulo.\n * Implemented using [Euclidean GCD](https://brilliant.org/wiki/extended-euclidean-algorithm/).\n */\nexport function invert(number: bigint, modulo: bigint): bigint {\n  if (number === _0n) throw new Error('invert: expected non-zero number');\n  if (modulo <= _0n) throw new Error('invert: expected positive modulus, got ' + modulo);\n  // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n  let a = mod(number, modulo);\n  let b = modulo;\n  // prettier-ignore\n  let x = _0n, y = _1n, u = _1n, v = _0n;\n  while (a !== _0n) {\n    // JIT applies optimization if those two lines follow each other\n    const q = b / a;\n    const r = b % a;\n    const m = x - u * q;\n    const n = y - v * q;\n    // prettier-ignore\n    b = a, a = r, x = u, y = v, u = m, v = n;\n  }\n  const gcd = b;\n  if (gcd !== _1n) throw new Error('invert: does not exist');\n  return mod(x, modulo);\n}\n\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P: bigint): <T>(Fp: IField<T>, n: T) => T {\n  // Legendre constant: used to calculate Legendre symbol (a | p),\n  // which denotes the value of a^((p-1)/2) (mod p).\n  // (a | p) ≡ 1    if a is a square (mod p)\n  // (a | p) ≡ -1   if a is not a square (mod p)\n  // (a | p) ≡ 0    if a ≡ 0 (mod p)\n  const legendreC = (P - _1n) / _2n;\n\n  let Q: bigint, S: number, Z: bigint;\n  // Step 1: By factoring out powers of 2 from p - 1,\n  // find q and s such that p - 1 = q*(2^s) with q odd\n  for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++);\n\n  // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n  for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++) {\n    // Crash instead of infinity loop, we cannot reasonable count until P.\n    if (Z > 1000) throw new Error('Cannot find square root: likely non-prime P');\n  }\n\n  // Fast-path\n  if (S === 1) {\n    const p1div4 = (P + _1n) / _4n;\n    return function tonelliFast<T>(Fp: IField<T>, n: T) {\n      const root = Fp.pow(n, p1div4);\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n\n  // Slow-path\n  const Q1div2 = (Q + _1n) / _2n;\n  return function tonelliSlow<T>(Fp: IField<T>, n: T): T {\n    // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n    if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE)) throw new Error('Cannot find square root');\n    let r = S;\n    // TODO: will fail at Fp2/etc\n    let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n    let x = Fp.pow(n, Q1div2); // first guess at the square root\n    let b = Fp.pow(n, Q); // first guess at the fudge factor\n\n    while (!Fp.eql(b, Fp.ONE)) {\n      if (Fp.eql(b, Fp.ZERO)) return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n      // Find m such b^(2^m)==1\n      let m = 1;\n      for (let t2 = Fp.sqr(b); m < r; m++) {\n        if (Fp.eql(t2, Fp.ONE)) break;\n        t2 = Fp.sqr(t2); // t2 *= t2\n      }\n      // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n      const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n      g = Fp.sqr(ge); // g = ge * ge\n      x = Fp.mul(x, ge); // x *= ge\n      b = Fp.mul(b, g); // b *= g\n      r = m;\n    }\n    return x;\n  };\n}\n\n/**\n * Square root for a finite field. It will try to check if optimizations are applicable and fall back to 4:\n *\n * 1. P ≡ 3 (mod 4)\n * 2. P ≡ 5 (mod 8)\n * 3. P ≡ 9 (mod 16)\n * 4. Tonelli-Shanks algorithm\n *\n * Different algorithms can give different roots, it is up to user to decide which one they want.\n * For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n */\nexport function FpSqrt(P: bigint): <T>(Fp: IField<T>, n: T) => T {\n  // P ≡ 3 (mod 4)\n  // √n = n^((P+1)/4)\n  if (P % _4n === _3n) {\n    // Not all roots possible!\n    // const ORDER =\n    //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n    // const NUM = 72057594037927816n;\n    const p1div4 = (P + _1n) / _4n;\n    return function sqrt3mod4<T>(Fp: IField<T>, n: T) {\n      const root = Fp.pow(n, p1div4);\n      // Throw if root**2 != n\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n\n  // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n  if (P % _8n === _5n) {\n    const c1 = (P - _5n) / _8n;\n    return function sqrt5mod8<T>(Fp: IField<T>, n: T) {\n      const n2 = Fp.mul(n, _2n);\n      const v = Fp.pow(n2, c1);\n      const nv = Fp.mul(n, v);\n      const i = Fp.mul(Fp.mul(nv, _2n), v);\n      const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n\n  // P ≡ 9 (mod 16)\n  if (P % _16n === _9n) {\n    // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n    // Means we cannot use sqrt for constants at all!\n    //\n    // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n    // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n    // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n    // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n    // sqrt = (x) => {\n    //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n    //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n    //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n    //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n    //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n    //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n    //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n    //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n    //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n    //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n    // }\n  }\n  // Other cases: Tonelli-Shanks algorithm\n  return tonelliShanks(P);\n}\n\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num: bigint, modulo: bigint): boolean =>\n  (mod(num, modulo) & _1n) === _1n;\n\n/** Field is not always over prime: for example, Fp2 has ORDER(q)=p^m. */\nexport interface IField<T> {\n  ORDER: bigint;\n  isLE: boolean;\n  BYTES: number;\n  BITS: number;\n  MASK: bigint;\n  ZERO: T;\n  ONE: T;\n  // 1-arg\n  create: (num: T) => T;\n  isValid: (num: T) => boolean;\n  is0: (num: T) => boolean;\n  neg(num: T): T;\n  inv(num: T): T;\n  sqrt(num: T): T;\n  sqr(num: T): T;\n  // 2-args\n  eql(lhs: T, rhs: T): boolean;\n  add(lhs: T, rhs: T): T;\n  sub(lhs: T, rhs: T): T;\n  mul(lhs: T, rhs: T | bigint): T;\n  pow(lhs: T, power: bigint): T;\n  div(lhs: T, rhs: T | bigint): T;\n  // N for NonNormalized (for now)\n  addN(lhs: T, rhs: T): T;\n  subN(lhs: T, rhs: T): T;\n  mulN(lhs: T, rhs: T | bigint): T;\n  sqrN(num: T): T;\n\n  // Optional\n  // Should be same as sgn0 function in\n  // [RFC9380](https://www.rfc-editor.org/rfc/rfc9380#section-4.1).\n  // NOTE: sgn0 is 'negative in LE', which is same as odd. And negative in LE is kinda strange definition anyway.\n  isOdd?(num: T): boolean; // Odd instead of even since we have it for Fp2\n  // legendre?(num: T): T;\n  pow(lhs: T, power: bigint): T;\n  invertBatch: (lst: T[]) => T[];\n  toBytes(num: T): Uint8Array;\n  fromBytes(bytes: Uint8Array): T;\n  // If c is False, CMOV returns a, otherwise it returns b.\n  cmov(a: T, b: T, c: boolean): T;\n}\n// prettier-ignore\nconst FIELD_FIELDS = [\n  'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n  'eql', 'add', 'sub', 'mul', 'pow', 'div',\n  'addN', 'subN', 'mulN', 'sqrN'\n] as const;\nexport function validateField<T>(field: IField<T>): IField<T> {\n  const initial = {\n    ORDER: 'bigint',\n    MASK: 'bigint',\n    BYTES: 'isSafeInteger',\n    BITS: 'isSafeInteger',\n  } as Record<string, string>;\n  const opts = FIELD_FIELDS.reduce((map, val: string) => {\n    map[val] = 'function';\n    return map;\n  }, initial);\n  return validateObject(field, opts);\n}\n\n// Generic field functions\n\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow<T>(f: IField<T>, num: T, power: bigint): T {\n  // Should have same speed as pow for bigints\n  // TODO: benchmark!\n  if (power < _0n) throw new Error('invalid exponent, negatives unsupported');\n  if (power === _0n) return f.ONE;\n  if (power === _1n) return num;\n  let p = f.ONE;\n  let d = num;\n  while (power > _0n) {\n    if (power & _1n) p = f.mul(p, d);\n    d = f.sqr(d);\n    power >>= _1n;\n  }\n  return p;\n}\n\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nexport function FpInvertBatch<T>(f: IField<T>, nums: T[]): T[] {\n  const tmp = new Array(nums.length);\n  // Walk from first to last, multiply them by each other MOD p\n  const lastMultiplied = nums.reduce((acc, num, i) => {\n    if (f.is0(num)) return acc;\n    tmp[i] = acc;\n    return f.mul(acc, num);\n  }, f.ONE);\n  // Invert last element\n  const inverted = f.inv(lastMultiplied);\n  // Walk from last to first, multiply them by inverted each other MOD p\n  nums.reduceRight((acc, num, i) => {\n    if (f.is0(num)) return acc;\n    tmp[i] = f.mul(acc, tmp[i]);\n    return f.mul(acc, num);\n  }, inverted);\n  return tmp;\n}\n\nexport function FpDiv<T>(f: IField<T>, lhs: T, rhs: T | bigint): T {\n  return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n\n/**\n * Legendre symbol.\n * * (a | p) ≡ 1    if a is a square (mod p), quadratic residue\n * * (a | p) ≡ -1   if a is not a square (mod p), quadratic non residue\n * * (a | p) ≡ 0    if a ≡ 0 (mod p)\n */\nexport function FpLegendre(order: bigint): <T>(f: IField<T>, x: T) => T {\n  const legendreConst = (order - _1n) / _2n; // Integer arithmetic\n  return <T>(f: IField<T>, x: T): T => f.pow(x, legendreConst);\n}\n\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare<T>(f: IField<T>): (x: T) => boolean {\n  const legendre = FpLegendre(f.ORDER);\n  return (x: T): boolean => {\n    const p = legendre(f, x);\n    return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n  };\n}\n\n// CURVE.n lengths\nexport function nLength(\n  n: bigint,\n  nBitLength?: number\n): {\n  nBitLength: number;\n  nByteLength: number;\n} {\n  // Bit size, byte size of CURVE.n\n  const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n  const nByteLength = Math.ceil(_nBitLength / 8);\n  return { nBitLength: _nBitLength, nByteLength };\n}\n\ntype FpField = IField<bigint> & Required<Pick<IField<bigint>, 'isOdd'>>;\n/**\n * Initializes a finite field over prime.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * Fragile: always run a benchmark on a change.\n * Security note: operations don't check 'isValid' for all elements for performance reasons,\n * it is caller responsibility to check this.\n * This is low-level code, please make sure you know what you're doing.\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(\n  ORDER: bigint,\n  bitLen?: number,\n  isLE = false,\n  redef: Partial<IField<bigint>> = {}\n): Readonly<FpField> {\n  if (ORDER <= _0n) throw new Error('invalid field: expected ORDER > 0, got ' + ORDER);\n  const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n  if (BYTES > 2048) throw new Error('invalid field: expected ORDER of <= 2048 bytes');\n  let sqrtP: ReturnType<typeof FpSqrt>; // cached sqrtP\n  const f: Readonly<FpField> = Object.freeze({\n    ORDER,\n    isLE,\n    BITS,\n    BYTES,\n    MASK: bitMask(BITS),\n    ZERO: _0n,\n    ONE: _1n,\n    create: (num) => mod(num, ORDER),\n    isValid: (num) => {\n      if (typeof num !== 'bigint')\n        throw new Error('invalid field element: expected bigint, got ' + typeof num);\n      return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n    },\n    is0: (num) => num === _0n,\n    isOdd: (num) => (num & _1n) === _1n,\n    neg: (num) => mod(-num, ORDER),\n    eql: (lhs, rhs) => lhs === rhs,\n\n    sqr: (num) => mod(num * num, ORDER),\n    add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n    sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n    mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n    pow: (num, power) => FpPow(f, num, power),\n    div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n\n    // Same as above, but doesn't normalize\n    sqrN: (num) => num * num,\n    addN: (lhs, rhs) => lhs + rhs,\n    subN: (lhs, rhs) => lhs - rhs,\n    mulN: (lhs, rhs) => lhs * rhs,\n\n    inv: (num) => invert(num, ORDER),\n    sqrt:\n      redef.sqrt ||\n      ((n) => {\n        if (!sqrtP) sqrtP = FpSqrt(ORDER);\n        return sqrtP(f, n);\n      }),\n    invertBatch: (lst) => FpInvertBatch(f, lst),\n    // TODO: do we really need constant cmov?\n    // We don't have const-time bigints anyway, so probably will be not very useful\n    cmov: (a, b, c) => (c ? b : a),\n    toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n    fromBytes: (bytes) => {\n      if (bytes.length !== BYTES)\n        throw new Error('Field.fromBytes: expected ' + BYTES + ' bytes, got ' + bytes.length);\n      return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n    },\n  } as FpField);\n  return Object.freeze(f);\n}\n\nexport function FpSqrtOdd<T>(Fp: IField<T>, elm: T): T {\n  if (!Fp.isOdd) throw new Error(\"Field doesn't have isOdd\");\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? root : Fp.neg(root);\n}\n\nexport function FpSqrtEven<T>(Fp: IField<T>, elm: T): T {\n  if (!Fp.isOdd) throw new Error(\"Field doesn't have isOdd\");\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use `mapKeyToField` instead\n */\nexport function hashToPrivateScalar(\n  hash: string | Uint8Array,\n  groupOrder: bigint,\n  isLE = false\n): bigint {\n  hash = ensureBytes('privateHash', hash);\n  const hashLen = hash.length;\n  const minLen = nLength(groupOrder).nByteLength + 8;\n  if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n    throw new Error(\n      'hashToPrivateScalar: expected ' + minLen + '-1024 bytes of input, got ' + hashLen\n    );\n  const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n  return mod(num, groupOrder - _1n) + _1n;\n}\n\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder: bigint): number {\n  if (typeof fieldOrder !== 'bigint') throw new Error('field order must be bigint');\n  const bitLength = fieldOrder.toString(2).length;\n  return Math.ceil(bitLength / 8);\n}\n\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder: bigint): number {\n  const length = getFieldBytesLength(fieldOrder);\n  return length + Math.ceil(length / 2);\n}\n\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key: Uint8Array, fieldOrder: bigint, isLE = false): Uint8Array {\n  const len = key.length;\n  const fieldLen = getFieldBytesLength(fieldOrder);\n  const minLen = getMinHashLength(fieldOrder);\n  // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n  if (len < 16 || len < minLen || len > 1024)\n    throw new Error('expected ' + minLen + '-1024 bytes of input, got ' + len);\n  const num = isLE ? bytesToNumberLE(key) : bytesToNumberBE(key);\n  // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n  const reduced = mod(num, fieldOrder - _1n) + _1n;\n  return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n", "/**\n * Methods for elliptic curve multiplication by scalars.\n * Contains wNAF, pippenger\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { type IField, nLength, validateField } from './modular.js';\nimport { bitLen, validateObject } from './utils.js';\n\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n\nexport type AffinePoint<T> = {\n  x: T;\n  y: T;\n} & { z?: never; t?: never };\n\nexport interface Group<T extends Group<T>> {\n  double(): T;\n  negate(): T;\n  add(other: T): T;\n  subtract(other: T): T;\n  equals(other: T): boolean;\n  multiply(scalar: bigint): T;\n}\n\nexport type GroupConstructor<T> = {\n  BASE: T;\n  ZERO: T;\n};\nexport type Mapper<T> = (i: T[]) => T[];\n\nfunction constTimeNegate<T extends Group<T>>(condition: boolean, item: T): T {\n  const neg = item.negate();\n  return condition ? neg : item;\n}\n\nfunction validateW(W: number, bits: number) {\n  if (!Number.isSafeInteger(W) || W <= 0 || W > bits)\n    throw new Error('invalid window size, expected [1..' + bits + '], got W=' + W);\n}\n\nfunction calcWOpts(W: number, bits: number) {\n  validateW(W, bits);\n  const windows = Math.ceil(bits / W) + 1; // +1, because\n  const windowSize = 2 ** (W - 1); // -1 because we skip zero\n  return { windows, windowSize };\n}\n\nfunction validateMSMPoints(points: any[], c: any) {\n  if (!Array.isArray(points)) throw new Error('array expected');\n  points.forEach((p, i) => {\n    if (!(p instanceof c)) throw new Error('invalid point at index ' + i);\n  });\n}\nfunction validateMSMScalars(scalars: any[], field: any) {\n  if (!Array.isArray(scalars)) throw new Error('array of scalars expected');\n  scalars.forEach((s, i) => {\n    if (!field.isValid(s)) throw new Error('invalid scalar at index ' + i);\n  });\n}\n\n// Since points in different groups cannot be equal (different object constructor),\n// we can have single place to store precomputes\nconst pointPrecomputes = new WeakMap<any, any[]>();\nconst pointWindowSizes = new WeakMap<any, number>(); // This allows use make points immutable (nothing changes inside)\n\nfunction getW(P: any): number {\n  return pointWindowSizes.get(P) || 1;\n}\n\nexport type IWNAF<T extends Group<T>> = {\n  constTimeNegate: <T extends Group<T>>(condition: boolean, item: T) => T;\n  hasPrecomputes(elm: T): boolean;\n  unsafeLadder(elm: T, n: bigint, p?: T): T;\n  precomputeWindow(elm: T, W: number): Group<T>[];\n  wNAF(W: number, precomputes: T[], n: bigint): { p: T; f: T };\n  wNAFUnsafe(W: number, precomputes: T[], n: bigint, acc?: T): T;\n  getPrecomputes(W: number, P: T, transform: Mapper<T>): T[];\n  wNAFCached(P: T, n: bigint, transform: Mapper<T>): { p: T; f: T };\n  wNAFCachedUnsafe(P: T, n: bigint, transform: Mapper<T>, prev?: T): T;\n  setWindowSize(P: T, W: number): void;\n};\n\n/**\n * Elliptic curve multiplication of Point by scalar. Fragile.\n * Scalars should always be less than curve order: this should be checked inside of a curve itself.\n * Creates precomputation tables for fast multiplication:\n * - private scalar is split by fixed size windows of W bits\n * - every window point is collected from window's table & added to accumulator\n * - since windows are different, same point inside tables won't be accessed more than once per calc\n * - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n * - +1 window is neccessary for wNAF\n * - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n *\n * @todo Research returning 2d JS array of windows, instead of a single window.\n * This would allow windows to be in different memory locations\n */\nexport function wNAF<T extends Group<T>>(c: GroupConstructor<T>, bits: number): IWNAF<T> {\n  return {\n    constTimeNegate,\n\n    hasPrecomputes(elm: T) {\n      return getW(elm) !== 1;\n    },\n\n    // non-const time multiplication ladder\n    unsafeLadder(elm: T, n: bigint, p = c.ZERO) {\n      let d: T = elm;\n      while (n > _0n) {\n        if (n & _1n) p = p.add(d);\n        d = d.double();\n        n >>= _1n;\n      }\n      return p;\n    },\n\n    /**\n     * Creates a wNAF precomputation window. Used for caching.\n     * Default window size is set by `utils.precompute()` and is equal to 8.\n     * Number of precomputed points depends on the curve size:\n     * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n     * - 𝑊 is the window size\n     * - 𝑛 is the bitlength of the curve order.\n     * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n     * @param elm Point instance\n     * @param W window size\n     * @returns precomputed point tables flattened to a single array\n     */\n    precomputeWindow(elm: T, W: number): Group<T>[] {\n      const { windows, windowSize } = calcWOpts(W, bits);\n      const points: T[] = [];\n      let p: T = elm;\n      let base = p;\n      for (let window = 0; window < windows; window++) {\n        base = p;\n        points.push(base);\n        // =1, because we skip zero\n        for (let i = 1; i < windowSize; i++) {\n          base = base.add(p);\n          points.push(base);\n        }\n        p = base.double();\n      }\n      return points;\n    },\n\n    /**\n     * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n     * @param W window size\n     * @param precomputes precomputed tables\n     * @param n scalar (we don't check here, but should be less than curve order)\n     * @returns real and fake (for const-time) points\n     */\n    wNAF(W: number, precomputes: T[], n: bigint): { p: T; f: T } {\n      // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n      // But need to carefully remove other checks before wNAF. ORDER == bits here\n      const { windows, windowSize } = calcWOpts(W, bits);\n\n      let p = c.ZERO;\n      let f = c.BASE;\n\n      const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n      const maxNumber = 2 ** W;\n      const shiftBy = BigInt(W);\n\n      for (let window = 0; window < windows; window++) {\n        const offset = window * windowSize;\n        // Extract W bits.\n        let wbits = Number(n & mask);\n\n        // Shift number by W bits.\n        n >>= shiftBy;\n\n        // If the bits are bigger than max size, we'll split those.\n        // +224 => 256 - 32\n        if (wbits > windowSize) {\n          wbits -= maxNumber;\n          n += _1n;\n        }\n\n        // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n        // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n        // there is negate now: it is possible that negated element from low value\n        // would be the same as high element, which will create carry into next window.\n        // It's not obvious how this can fail, but still worth investigating later.\n\n        // Check if we're onto Zero point.\n        // Add random point inside current window to f.\n        const offset1 = offset;\n        const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n        const cond1 = window % 2 !== 0;\n        const cond2 = wbits < 0;\n        if (wbits === 0) {\n          // The most important part for const-time getPublicKey\n          f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n        } else {\n          p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n        }\n      }\n      // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n      // Even if the variable is still unused, there are some checks which will\n      // throw an exception, so compiler needs to prove they won't happen, which is hard.\n      // At this point there is a way to F be infinity-point even if p is not,\n      // which makes it less const-time: around 1 bigint multiply.\n      return { p, f };\n    },\n\n    /**\n     * Implements ec unsafe (non const-time) multiplication using precomputed tables and w-ary non-adjacent form.\n     * @param W window size\n     * @param precomputes precomputed tables\n     * @param n scalar (we don't check here, but should be less than curve order)\n     * @param acc accumulator point to add result of multiplication\n     * @returns point\n     */\n    wNAFUnsafe(W: number, precomputes: T[], n: bigint, acc: T = c.ZERO): T {\n      const { windows, windowSize } = calcWOpts(W, bits);\n      const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n      const maxNumber = 2 ** W;\n      const shiftBy = BigInt(W);\n      for (let window = 0; window < windows; window++) {\n        const offset = window * windowSize;\n        if (n === _0n) break; // No need to go over empty scalar\n        // Extract W bits.\n        let wbits = Number(n & mask);\n        // Shift number by W bits.\n        n >>= shiftBy;\n        // If the bits are bigger than max size, we'll split those.\n        // +224 => 256 - 32\n        if (wbits > windowSize) {\n          wbits -= maxNumber;\n          n += _1n;\n        }\n        if (wbits === 0) continue;\n        let curr = precomputes[offset + Math.abs(wbits) - 1]; // -1 because we skip zero\n        if (wbits < 0) curr = curr.negate();\n        // NOTE: by re-using acc, we can save a lot of additions in case of MSM\n        acc = acc.add(curr);\n      }\n      return acc;\n    },\n\n    getPrecomputes(W: number, P: T, transform: Mapper<T>): T[] {\n      // Calculate precomputes on a first run, reuse them after\n      let comp = pointPrecomputes.get(P);\n      if (!comp) {\n        comp = this.precomputeWindow(P, W) as T[];\n        if (W !== 1) pointPrecomputes.set(P, transform(comp));\n      }\n      return comp;\n    },\n\n    wNAFCached(P: T, n: bigint, transform: Mapper<T>): { p: T; f: T } {\n      const W = getW(P);\n      return this.wNAF(W, this.getPrecomputes(W, P, transform), n);\n    },\n\n    wNAFCachedUnsafe(P: T, n: bigint, transform: Mapper<T>, prev?: T): T {\n      const W = getW(P);\n      if (W === 1) return this.unsafeLadder(P, n, prev); // For W=1 ladder is ~x2 faster\n      return this.wNAFUnsafe(W, this.getPrecomputes(W, P, transform), n, prev);\n    },\n\n    // We calculate precomputes for elliptic curve point multiplication\n    // using windowed method. This specifies window size and\n    // stores precomputed values. Usually only base point would be precomputed.\n\n    setWindowSize(P: T, W: number) {\n      validateW(W, bits);\n      pointWindowSizes.set(P, W);\n      pointPrecomputes.delete(P);\n    },\n  };\n}\n\n/**\n * Pippenger algorithm for multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * 30x faster vs naive addition on L=4096, 10x faster with precomputes.\n * For N=254bit, L=1, it does: 1024 ADD + 254 DBL. For L=5: 1536 ADD + 254 DBL.\n * Algorithmically constant-time (for same L), even when 1 point + scalar, or when scalar = 0.\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @param scalars array of L scalars (aka private keys / bigints)\n */\nexport function pippenger<T extends Group<T>>(\n  c: GroupConstructor<T>,\n  fieldN: IField<bigint>,\n  points: T[],\n  scalars: bigint[]\n): T {\n  // If we split scalars by some window (let's say 8 bits), every chunk will only\n  // take 256 buckets even if there are 4096 scalars, also re-uses double.\n  // TODO:\n  // - https://eprint.iacr.org/2024/750.pdf\n  // - https://tches.iacr.org/index.php/TCHES/article/view/10287\n  // 0 is accepted in scalars\n  validateMSMPoints(points, c);\n  validateMSMScalars(scalars, fieldN);\n  if (points.length !== scalars.length)\n    throw new Error('arrays of points and scalars must have equal length');\n  const zero = c.ZERO;\n  const wbits = bitLen(BigInt(points.length));\n  const windowSize = wbits > 12 ? wbits - 3 : wbits > 4 ? wbits - 2 : wbits ? 2 : 1; // in bits\n  const MASK = (1 << windowSize) - 1;\n  const buckets = new Array(MASK + 1).fill(zero); // +1 for zero array\n  const lastBits = Math.floor((fieldN.BITS - 1) / windowSize) * windowSize;\n  let sum = zero;\n  for (let i = lastBits; i >= 0; i -= windowSize) {\n    buckets.fill(zero);\n    for (let j = 0; j < scalars.length; j++) {\n      const scalar = scalars[j];\n      const wbits = Number((scalar >> BigInt(i)) & BigInt(MASK));\n      buckets[wbits] = buckets[wbits].add(points[j]);\n    }\n    let resI = zero; // not using this will do small speed-up, but will lose ct\n    // Skip first bucket, because it is zero\n    for (let j = buckets.length - 1, sumI = zero; j > 0; j--) {\n      sumI = sumI.add(buckets[j]);\n      resI = resI.add(sumI);\n    }\n    sum = sum.add(resI);\n    if (i !== 0) for (let j = 0; j < windowSize; j++) sum = sum.double();\n  }\n  return sum as T;\n}\n/**\n * Precomputed multi-scalar multiplication (MSM, Pa + Qb + Rc + ...).\n * @param c Curve Point constructor\n * @param fieldN field over CURVE.N - important that it's not over CURVE.P\n * @param points array of L curve points\n * @returns function which multiplies points with scaars\n */\nexport function precomputeMSMUnsafe<T extends Group<T>>(\n  c: GroupConstructor<T>,\n  fieldN: IField<bigint>,\n  points: T[],\n  windowSize: number\n): (scalars: bigint[]) => T {\n  /**\n   * Performance Analysis of Window-based Precomputation\n   *\n   * Base Case (256-bit scalar, 8-bit window):\n   * - Standard precomputation requires:\n   *   - 31 additions per scalar × 256 scalars = 7,936 ops\n   *   - Plus 255 summary additions = 8,191 total ops\n   *   Note: Summary additions can be optimized via accumulator\n   *\n   * Chunked Precomputation Analysis:\n   * - Using 32 chunks requires:\n   *   - 255 additions per chunk\n   *   - 256 doublings\n   *   - Total: (255 × 32) + 256 = 8,416 ops\n   *\n   * Memory Usage Comparison:\n   * Window Size | Standard Points | Chunked Points\n   * ------------|-----------------|---------------\n   *     4-bit   |     520         |      15\n   *     8-bit   |    4,224        |     255\n   *    10-bit   |   13,824        |   1,023\n   *    16-bit   |  557,056        |  65,535\n   *\n   * Key Advantages:\n   * 1. Enables larger window sizes due to reduced memory overhead\n   * 2. More efficient for smaller scalar counts:\n   *    - 16 chunks: (16 × 255) + 256 = 4,336 ops\n   *    - ~2x faster than standard 8,191 ops\n   *\n   * Limitations:\n   * - Not suitable for plain precomputes (requires 256 constant doublings)\n   * - Performance degrades with larger scalar counts:\n   *   - Optimal for ~256 scalars\n   *   - Less efficient for 4096+ scalars (Pippenger preferred)\n   */\n  validateW(windowSize, fieldN.BITS);\n  validateMSMPoints(points, c);\n  const zero = c.ZERO;\n  const tableSize = 2 ** windowSize - 1; // table size (without zero)\n  const chunks = Math.ceil(fieldN.BITS / windowSize); // chunks of item\n  const MASK = BigInt((1 << windowSize) - 1);\n  const tables = points.map((p: T) => {\n    const res = [];\n    for (let i = 0, acc = p; i < tableSize; i++) {\n      res.push(acc);\n      acc = acc.add(p);\n    }\n    return res;\n  });\n  return (scalars: bigint[]): T => {\n    validateMSMScalars(scalars, fieldN);\n    if (scalars.length > points.length)\n      throw new Error('array of scalars must be smaller than array of points');\n    let res = zero;\n    for (let i = 0; i < chunks; i++) {\n      // No need to double if accumulator is still zero.\n      if (res !== zero) for (let j = 0; j < windowSize; j++) res = res.double();\n      const shiftBy = BigInt(chunks * windowSize - (i + 1) * windowSize);\n      for (let j = 0; j < scalars.length; j++) {\n        const n = scalars[j];\n        const curr = Number((n >> shiftBy) & MASK);\n        if (!curr) continue; // skip zero scalars chunks\n        res = res.add(tables[j][curr - 1]);\n      }\n    }\n    return res;\n  };\n}\n\n/**\n * Generic BasicCurve interface: works even for polynomial fields (BLS): P, n, h would be ok.\n * Though generator can be different (Fp2 / Fp6 for BLS).\n */\nexport type BasicCurve<T> = {\n  Fp: IField<T>; // Field over which we'll do calculations (Fp)\n  n: bigint; // Curve order, total count of valid points in the field\n  nBitLength?: number; // bit length of curve order\n  nByteLength?: number; // byte length of curve order\n  h: bigint; // cofactor. we can assign default=1, but users will just ignore it w/o validation\n  hEff?: bigint; // Number to multiply to clear cofactor\n  Gx: T; // base point X coordinate\n  Gy: T; // base point Y coordinate\n  allowInfinityPoint?: boolean; // bls12-381 requires it. ZERO point is valid, but invalid pubkey\n};\n\nexport function validateBasic<FP, T>(\n  curve: BasicCurve<FP> & T\n): Readonly<\n  {\n    readonly nBitLength: number;\n    readonly nByteLength: number;\n  } & BasicCurve<FP> &\n    T & {\n      p: bigint;\n    }\n> {\n  validateField(curve.Fp);\n  validateObject(\n    curve,\n    {\n      n: 'bigint',\n      h: 'bigint',\n      Gx: 'field',\n      Gy: 'field',\n    },\n    {\n      nBitLength: 'isSafeInteger',\n      nByteLength: 'isSafeInteger',\n    }\n  );\n  // Set defaults\n  return Object.freeze({\n    ...nLength(curve.n, curve.nBitLength),\n    ...curve,\n    ...{ p: curve.Fp.ORDER },\n  } as const);\n}\n", "/**\n * Short <PERSON> curve methods. The formula is: y² = x³ + ax + b.\n *\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance\n * of nominative types in TypeScript and interfaces only check for shape, so it's hard to create\n * unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * @todo https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport {\n  type AffinePoint,\n  type BasicCurve,\n  type Group,\n  type GroupConstructor,\n  pippenger,\n  validateBasic,\n  wNAF,\n} from './curve.js';\nimport {\n  Field,\n  type IField,\n  getMinHashLength,\n  invert,\n  mapHashToField,\n  mod,\n  validateField,\n} from './modular.js';\nimport * as ut from './utils.js';\nimport { type CHash, type Hex, type PrivKey, abool, ensureBytes, memoized } from './utils.js';\n\nexport type { AffinePoint };\ntype HmacFnSync = (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array;\ntype EndomorphismOpts = {\n  beta: bigint;\n  splitScalar: (k: bigint) => { k1neg: boolean; k1: bigint; k2neg: boolean; k2: bigint };\n};\nexport type BasicWCurve<T> = BasicCurve<T> & {\n  // Params: a, b\n  a: T;\n  b: T;\n\n  // Optional params\n  allowedPrivateKeyLengths?: readonly number[]; // for P521\n  wrapPrivateKey?: boolean; // bls12-381 requires mod(n) instead of rejecting keys >= n\n  endo?: EndomorphismOpts; // Endomorphism options for Koblitz curves\n  // When a cofactor != 1, there can be an effective methods to:\n  // 1. Determine whether a point is torsion-free\n  isTorsionFree?: (c: ProjConstructor<T>, point: ProjPointType<T>) => boolean;\n  // 2. Clear torsion component\n  clearCofactor?: (c: ProjConstructor<T>, point: ProjPointType<T>) => ProjPointType<T>;\n};\n\ntype Entropy = Hex | boolean;\nexport type SignOpts = { lowS?: boolean; extraEntropy?: Entropy; prehash?: boolean };\nexport type VerOpts = { lowS?: boolean; prehash?: boolean; format?: 'compact' | 'der' | undefined };\n\nfunction validateSigVerOpts(opts: SignOpts | VerOpts) {\n  if (opts.lowS !== undefined) abool('lowS', opts.lowS);\n  if (opts.prehash !== undefined) abool('prehash', opts.prehash);\n}\n\n// Instance for 3d XYZ points\nexport interface ProjPointType<T> extends Group<ProjPointType<T>> {\n  readonly px: T;\n  readonly py: T;\n  readonly pz: T;\n  get x(): T;\n  get y(): T;\n  multiply(scalar: bigint): ProjPointType<T>;\n  toAffine(iz?: T): AffinePoint<T>;\n  isTorsionFree(): boolean;\n  clearCofactor(): ProjPointType<T>;\n  assertValidity(): void;\n  hasEvenY(): boolean;\n  toRawBytes(isCompressed?: boolean): Uint8Array;\n  toHex(isCompressed?: boolean): string;\n\n  multiplyUnsafe(scalar: bigint): ProjPointType<T>;\n  multiplyAndAddUnsafe(Q: ProjPointType<T>, a: bigint, b: bigint): ProjPointType<T> | undefined;\n  _setWindowSize(windowSize: number): void;\n}\n// Static methods for 3d XYZ points\nexport interface ProjConstructor<T> extends GroupConstructor<ProjPointType<T>> {\n  new (x: T, y: T, z: T): ProjPointType<T>;\n  fromAffine(p: AffinePoint<T>): ProjPointType<T>;\n  fromHex(hex: Hex): ProjPointType<T>;\n  fromPrivateKey(privateKey: PrivKey): ProjPointType<T>;\n  normalizeZ(points: ProjPointType<T>[]): ProjPointType<T>[];\n  msm(points: ProjPointType<T>[], scalars: bigint[]): ProjPointType<T>;\n}\n\nexport type CurvePointsType<T> = BasicWCurve<T> & {\n  // Bytes\n  fromBytes?: (bytes: Uint8Array) => AffinePoint<T>;\n  toBytes?: (c: ProjConstructor<T>, point: ProjPointType<T>, isCompressed: boolean) => Uint8Array;\n};\n\nexport type CurvePointsTypeWithLength<T> = Readonly<\n  CurvePointsType<T> & { nByteLength: number; nBitLength: number }\n>;\n\nfunction validatePointOpts<T>(curve: CurvePointsType<T>): CurvePointsTypeWithLength<T> {\n  const opts = validateBasic(curve);\n  ut.validateObject(\n    opts,\n    {\n      a: 'field',\n      b: 'field',\n    },\n    {\n      allowedPrivateKeyLengths: 'array',\n      wrapPrivateKey: 'boolean',\n      isTorsionFree: 'function',\n      clearCofactor: 'function',\n      allowInfinityPoint: 'boolean',\n      fromBytes: 'function',\n      toBytes: 'function',\n    }\n  );\n  const { endo, Fp, a } = opts;\n  if (endo) {\n    if (!Fp.eql(a, Fp.ZERO)) {\n      throw new Error('invalid endomorphism, can only be defined for Koblitz curves that have a=0');\n    }\n    if (\n      typeof endo !== 'object' ||\n      typeof endo.beta !== 'bigint' ||\n      typeof endo.splitScalar !== 'function'\n    ) {\n      throw new Error('invalid endomorphism, expected beta: bigint and splitScalar: function');\n    }\n  }\n  return Object.freeze({ ...opts } as const);\n}\n\nexport type CurvePointsRes<T> = {\n  CURVE: ReturnType<typeof validatePointOpts<T>>;\n  ProjectivePoint: ProjConstructor<T>;\n  normPrivateKeyToScalar: (key: PrivKey) => bigint;\n  weierstrassEquation: (x: T) => T;\n  isWithinCurveOrder: (num: bigint) => boolean;\n};\n\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = ut;\n\nexport class DERErr extends Error {\n  constructor(m = '') {\n    super(m);\n  }\n}\nexport type IDER = {\n  // asn.1 DER encoding utils\n  Err: typeof DERErr;\n  // Basic building block is TLV (Tag-Length-Value)\n  _tlv: {\n    encode: (tag: number, data: string) => string;\n    // v - value, l - left bytes (unparsed)\n    decode(tag: number, data: Uint8Array): { v: Uint8Array; l: Uint8Array };\n  };\n  // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n  // since we always use positive integers here. It must always be empty:\n  // - add zero byte if exists\n  // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n  _int: {\n    encode(num: bigint): string;\n    decode(data: Uint8Array): bigint;\n  };\n  toSig(hex: string | Uint8Array): { r: bigint; s: bigint };\n  hexFromSig(sig: { r: bigint; s: bigint }): string;\n};\n/**\n * ASN.1 DER encoding utilities. ASN is very complex & fragile. Format:\n *\n *     [0x30 (SEQUENCE), bytelength, 0x02 (INTEGER), intLength, R, 0x02 (INTEGER), intLength, S]\n *\n * Docs: https://letsencrypt.org/docs/a-warm-welcome-to-asn1-and-der/, https://luca.ntop.org/Teaching/Appunti/asn1.html\n */\nexport const DER: IDER = {\n  // asn.1 DER encoding utils\n  Err: DERErr,\n  // Basic building block is TLV (Tag-Length-Value)\n  _tlv: {\n    encode: (tag: number, data: string): string => {\n      const { Err: E } = DER;\n      if (tag < 0 || tag > 256) throw new E('tlv.encode: wrong tag');\n      if (data.length & 1) throw new E('tlv.encode: unpadded data');\n      const dataLen = data.length / 2;\n      const len = ut.numberToHexUnpadded(dataLen);\n      if ((len.length / 2) & 0b1000_0000) throw new E('tlv.encode: long form length too big');\n      // length of length with long form flag\n      const lenLen = dataLen > 127 ? ut.numberToHexUnpadded((len.length / 2) | 0b1000_0000) : '';\n      const t = ut.numberToHexUnpadded(tag);\n      return t + lenLen + len + data;\n    },\n    // v - value, l - left bytes (unparsed)\n    decode(tag: number, data: Uint8Array): { v: Uint8Array; l: Uint8Array } {\n      const { Err: E } = DER;\n      let pos = 0;\n      if (tag < 0 || tag > 256) throw new E('tlv.encode: wrong tag');\n      if (data.length < 2 || data[pos++] !== tag) throw new E('tlv.decode: wrong tlv');\n      const first = data[pos++];\n      const isLong = !!(first & 0b1000_0000); // First bit of first length byte is flag for short/long form\n      let length = 0;\n      if (!isLong) length = first;\n      else {\n        // Long form: [longFlag(1bit), lengthLength(7bit), length (BE)]\n        const lenLen = first & 0b0111_1111;\n        if (!lenLen) throw new E('tlv.decode(long): indefinite length not supported');\n        if (lenLen > 4) throw new E('tlv.decode(long): byte length is too big'); // this will overflow u32 in js\n        const lengthBytes = data.subarray(pos, pos + lenLen);\n        if (lengthBytes.length !== lenLen) throw new E('tlv.decode: length bytes not complete');\n        if (lengthBytes[0] === 0) throw new E('tlv.decode(long): zero leftmost byte');\n        for (const b of lengthBytes) length = (length << 8) | b;\n        pos += lenLen;\n        if (length < 128) throw new E('tlv.decode(long): not minimal encoding');\n      }\n      const v = data.subarray(pos, pos + length);\n      if (v.length !== length) throw new E('tlv.decode: wrong value length');\n      return { v, l: data.subarray(pos + length) };\n    },\n  },\n  // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n  // since we always use positive integers here. It must always be empty:\n  // - add zero byte if exists\n  // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n  _int: {\n    encode(num: bigint): string {\n      const { Err: E } = DER;\n      if (num < _0n) throw new E('integer: negative integers are not allowed');\n      let hex = ut.numberToHexUnpadded(num);\n      // Pad with zero byte if negative flag is present\n      if (Number.parseInt(hex[0], 16) & 0b1000) hex = '00' + hex;\n      if (hex.length & 1) throw new E('unexpected DER parsing assertion: unpadded hex');\n      return hex;\n    },\n    decode(data: Uint8Array): bigint {\n      const { Err: E } = DER;\n      if (data[0] & 0b1000_0000) throw new E('invalid signature integer: negative');\n      if (data[0] === 0x00 && !(data[1] & 0b1000_0000))\n        throw new E('invalid signature integer: unnecessary leading zero');\n      return b2n(data);\n    },\n  },\n  toSig(hex: string | Uint8Array): { r: bigint; s: bigint } {\n    // parse DER signature\n    const { Err: E, _int: int, _tlv: tlv } = DER;\n    const data = typeof hex === 'string' ? h2b(hex) : hex;\n    ut.abytes(data);\n    const { v: seqBytes, l: seqLeftBytes } = tlv.decode(0x30, data);\n    if (seqLeftBytes.length) throw new E('invalid signature: left bytes after parsing');\n    const { v: rBytes, l: rLeftBytes } = tlv.decode(0x02, seqBytes);\n    const { v: sBytes, l: sLeftBytes } = tlv.decode(0x02, rLeftBytes);\n    if (sLeftBytes.length) throw new E('invalid signature: left bytes after parsing');\n    return { r: int.decode(rBytes), s: int.decode(sBytes) };\n  },\n  hexFromSig(sig: { r: bigint; s: bigint }): string {\n    const { _tlv: tlv, _int: int } = DER;\n    const rs = tlv.encode(0x02, int.encode(sig.r));\n    const ss = tlv.encode(0x02, int.encode(sig.s));\n    const seq = rs + ss;\n    return tlv.encode(0x30, seq);\n  },\n};\n\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\n\nexport function weierstrassPoints<T>(opts: CurvePointsType<T>): CurvePointsRes<T> {\n  const CURVE = validatePointOpts(opts);\n  const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n  const Fn = Field(CURVE.n, CURVE.nBitLength);\n\n  const toBytes =\n    CURVE.toBytes ||\n    ((_c: ProjConstructor<T>, point: ProjPointType<T>, _isCompressed: boolean) => {\n      const a = point.toAffine();\n      return ut.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n    });\n  const fromBytes =\n    CURVE.fromBytes ||\n    ((bytes: Uint8Array) => {\n      // const head = bytes[0];\n      const tail = bytes.subarray(1);\n      // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n      const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n      const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n      return { x, y };\n    });\n\n  /**\n   * y² = x³ + ax + b: Short weierstrass curve formula\n   * @returns y²\n   */\n  function weierstrassEquation(x: T): T {\n    const { a, b } = CURVE;\n    const x2 = Fp.sqr(x); // x * x\n    const x3 = Fp.mul(x2, x); // x2 * x\n    return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n  }\n  // Validate whether the passed curve params are valid.\n  // We check if curve equation works for generator point.\n  // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n  // ProjectivePoint class has not been initialized yet.\n  if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n    throw new Error('bad generator point: equation left != right');\n\n  // Valid group elements reside in range 1..n-1\n  function isWithinCurveOrder(num: bigint): boolean {\n    return ut.inRange(num, _1n, CURVE.n);\n  }\n  // Validates if priv key is valid and converts it to bigint.\n  // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n  function normPrivateKeyToScalar(key: PrivKey): bigint {\n    const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n: N } = CURVE;\n    if (lengths && typeof key !== 'bigint') {\n      if (ut.isBytes(key)) key = ut.bytesToHex(key);\n      // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n      if (typeof key !== 'string' || !lengths.includes(key.length))\n        throw new Error('invalid private key');\n      key = key.padStart(nByteLength * 2, '0');\n    }\n    let num: bigint;\n    try {\n      num =\n        typeof key === 'bigint'\n          ? key\n          : ut.bytesToNumberBE(ensureBytes('private key', key, nByteLength));\n    } catch (error) {\n      throw new Error(\n        'invalid private key, expected hex or ' + nByteLength + ' bytes, got ' + typeof key\n      );\n    }\n    if (wrapPrivateKey) num = mod(num, N); // disabled by default, enabled for BLS\n    ut.aInRange('private key', num, _1n, N); // num in range [1..N-1]\n    return num;\n  }\n\n  function assertPrjPoint(other: unknown) {\n    if (!(other instanceof Point)) throw new Error('ProjectivePoint expected');\n  }\n\n  // Memoized toAffine / validity check. They are heavy. Points are immutable.\n\n  // Converts Projective point to affine (x, y) coordinates.\n  // Can accept precomputed Z^-1 - for example, from invertBatch.\n  // (x, y, z) ∋ (x=x/z, y=y/z)\n  const toAffineMemo = memoized((p: Point, iz?: T): AffinePoint<T> => {\n    const { px: x, py: y, pz: z } = p;\n    // Fast-path for normalized points\n    if (Fp.eql(z, Fp.ONE)) return { x, y };\n    const is0 = p.is0();\n    // If invZ was 0, we return zero point. However we still want to execute\n    // all operations, so we replace invZ with a random number, 1.\n    if (iz == null) iz = is0 ? Fp.ONE : Fp.inv(z);\n    const ax = Fp.mul(x, iz);\n    const ay = Fp.mul(y, iz);\n    const zz = Fp.mul(z, iz);\n    if (is0) return { x: Fp.ZERO, y: Fp.ZERO };\n    if (!Fp.eql(zz, Fp.ONE)) throw new Error('invZ was invalid');\n    return { x: ax, y: ay };\n  });\n  // NOTE: on exception this will crash 'cached' and no value will be set.\n  // Otherwise true will be return\n  const assertValidMemo = memoized((p: Point) => {\n    if (p.is0()) {\n      // (0, 1, 0) aka ZERO is invalid in most contexts.\n      // In BLS, ZERO can be serialized, so we allow it.\n      // (0, 0, 0) is invalid representation of ZERO.\n      if (CURVE.allowInfinityPoint && !Fp.is0(p.py)) return;\n      throw new Error('bad point: ZERO');\n    }\n    // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n    const { x, y } = p.toAffine();\n    // Check if x, y are valid field elements\n    if (!Fp.isValid(x) || !Fp.isValid(y)) throw new Error('bad point: x or y not FE');\n    const left = Fp.sqr(y); // y²\n    const right = weierstrassEquation(x); // x³ + ax + b\n    if (!Fp.eql(left, right)) throw new Error('bad point: equation left != right');\n    if (!p.isTorsionFree()) throw new Error('bad point: not in prime-order subgroup');\n    return true;\n  });\n\n  /**\n   * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n   * Default Point works in 2d / affine coordinates: (x, y)\n   * We're doing calculations in projective, because its operations don't require costly inversion.\n   */\n  class Point implements ProjPointType<T> {\n    static readonly BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    static readonly ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n\n    constructor(\n      readonly px: T,\n      readonly py: T,\n      readonly pz: T\n    ) {\n      if (px == null || !Fp.isValid(px)) throw new Error('x required');\n      if (py == null || !Fp.isValid(py)) throw new Error('y required');\n      if (pz == null || !Fp.isValid(pz)) throw new Error('z required');\n      Object.freeze(this);\n    }\n\n    // Does not validate if the point is on-curve.\n    // Use fromHex instead, or call assertValidity() later.\n    static fromAffine(p: AffinePoint<T>): Point {\n      const { x, y } = p || {};\n      if (!p || !Fp.isValid(x) || !Fp.isValid(y)) throw new Error('invalid affine point');\n      if (p instanceof Point) throw new Error('projective point not allowed');\n      const is0 = (i: T) => Fp.eql(i, Fp.ZERO);\n      // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n      if (is0(x) && is0(y)) return Point.ZERO;\n      return new Point(x, y, Fp.ONE);\n    }\n\n    get x(): T {\n      return this.toAffine().x;\n    }\n    get y(): T {\n      return this.toAffine().y;\n    }\n\n    /**\n     * Takes a bunch of Projective Points but executes only one\n     * inversion on all of them. Inversion is very slow operation,\n     * so this improves performance massively.\n     * Optimization: converts a list of projective points to a list of identical points with Z=1.\n     */\n    static normalizeZ(points: Point[]): Point[] {\n      const toInv = Fp.invertBatch(points.map((p) => p.pz));\n      return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n    }\n\n    /**\n     * Converts hash string or Uint8Array to Point.\n     * @param hex short/long ECDSA hex\n     */\n    static fromHex(hex: Hex): Point {\n      const P = Point.fromAffine(fromBytes(ensureBytes('pointHex', hex)));\n      P.assertValidity();\n      return P;\n    }\n\n    // Multiplies generator point by privateKey.\n    static fromPrivateKey(privateKey: PrivKey) {\n      return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n    }\n\n    // Multiscalar Multiplication\n    static msm(points: Point[], scalars: bigint[]): Point {\n      return pippenger(Point, Fn, points, scalars);\n    }\n\n    // \"Private method\", don't use it directly\n    _setWindowSize(windowSize: number) {\n      wnaf.setWindowSize(this, windowSize);\n    }\n\n    // A point on curve is valid if it conforms to equation.\n    assertValidity(): void {\n      assertValidMemo(this);\n    }\n\n    hasEvenY(): boolean {\n      const { y } = this.toAffine();\n      if (Fp.isOdd) return !Fp.isOdd(y);\n      throw new Error(\"Field doesn't support isOdd\");\n    }\n\n    /**\n     * Compare one point to another.\n     */\n    equals(other: Point): boolean {\n      assertPrjPoint(other);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      const { px: X2, py: Y2, pz: Z2 } = other;\n      const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n      const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n      return U1 && U2;\n    }\n\n    /**\n     * Flips point to one corresponding to (x, -y) in Affine coordinates.\n     */\n    negate(): Point {\n      return new Point(this.px, Fp.neg(this.py), this.pz);\n    }\n\n    // Renes-Costello-Batina exception-free doubling formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 3\n    // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n    double() {\n      const { a, b } = CURVE;\n      const b3 = Fp.mul(b, _3n);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n      let t0 = Fp.mul(X1, X1); // step 1\n      let t1 = Fp.mul(Y1, Y1);\n      let t2 = Fp.mul(Z1, Z1);\n      let t3 = Fp.mul(X1, Y1);\n      t3 = Fp.add(t3, t3); // step 5\n      Z3 = Fp.mul(X1, Z1);\n      Z3 = Fp.add(Z3, Z3);\n      X3 = Fp.mul(a, Z3);\n      Y3 = Fp.mul(b3, t2);\n      Y3 = Fp.add(X3, Y3); // step 10\n      X3 = Fp.sub(t1, Y3);\n      Y3 = Fp.add(t1, Y3);\n      Y3 = Fp.mul(X3, Y3);\n      X3 = Fp.mul(t3, X3);\n      Z3 = Fp.mul(b3, Z3); // step 15\n      t2 = Fp.mul(a, t2);\n      t3 = Fp.sub(t0, t2);\n      t3 = Fp.mul(a, t3);\n      t3 = Fp.add(t3, Z3);\n      Z3 = Fp.add(t0, t0); // step 20\n      t0 = Fp.add(Z3, t0);\n      t0 = Fp.add(t0, t2);\n      t0 = Fp.mul(t0, t3);\n      Y3 = Fp.add(Y3, t0);\n      t2 = Fp.mul(Y1, Z1); // step 25\n      t2 = Fp.add(t2, t2);\n      t0 = Fp.mul(t2, t3);\n      X3 = Fp.sub(X3, t0);\n      Z3 = Fp.mul(t2, t1);\n      Z3 = Fp.add(Z3, Z3); // step 30\n      Z3 = Fp.add(Z3, Z3);\n      return new Point(X3, Y3, Z3);\n    }\n\n    // Renes-Costello-Batina exception-free addition formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 1\n    // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n    add(other: Point): Point {\n      assertPrjPoint(other);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      const { px: X2, py: Y2, pz: Z2 } = other;\n      let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n      const a = CURVE.a;\n      const b3 = Fp.mul(CURVE.b, _3n);\n      let t0 = Fp.mul(X1, X2); // step 1\n      let t1 = Fp.mul(Y1, Y2);\n      let t2 = Fp.mul(Z1, Z2);\n      let t3 = Fp.add(X1, Y1);\n      let t4 = Fp.add(X2, Y2); // step 5\n      t3 = Fp.mul(t3, t4);\n      t4 = Fp.add(t0, t1);\n      t3 = Fp.sub(t3, t4);\n      t4 = Fp.add(X1, Z1);\n      let t5 = Fp.add(X2, Z2); // step 10\n      t4 = Fp.mul(t4, t5);\n      t5 = Fp.add(t0, t2);\n      t4 = Fp.sub(t4, t5);\n      t5 = Fp.add(Y1, Z1);\n      X3 = Fp.add(Y2, Z2); // step 15\n      t5 = Fp.mul(t5, X3);\n      X3 = Fp.add(t1, t2);\n      t5 = Fp.sub(t5, X3);\n      Z3 = Fp.mul(a, t4);\n      X3 = Fp.mul(b3, t2); // step 20\n      Z3 = Fp.add(X3, Z3);\n      X3 = Fp.sub(t1, Z3);\n      Z3 = Fp.add(t1, Z3);\n      Y3 = Fp.mul(X3, Z3);\n      t1 = Fp.add(t0, t0); // step 25\n      t1 = Fp.add(t1, t0);\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.mul(b3, t4);\n      t1 = Fp.add(t1, t2);\n      t2 = Fp.sub(t0, t2); // step 30\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.add(t4, t2);\n      t0 = Fp.mul(t1, t4);\n      Y3 = Fp.add(Y3, t0);\n      t0 = Fp.mul(t5, t4); // step 35\n      X3 = Fp.mul(t3, X3);\n      X3 = Fp.sub(X3, t0);\n      t0 = Fp.mul(t3, t1);\n      Z3 = Fp.mul(t5, Z3);\n      Z3 = Fp.add(Z3, t0); // step 40\n      return new Point(X3, Y3, Z3);\n    }\n\n    subtract(other: Point) {\n      return this.add(other.negate());\n    }\n\n    is0() {\n      return this.equals(Point.ZERO);\n    }\n    private wNAF(n: bigint): { p: Point; f: Point } {\n      return wnaf.wNAFCached(this, n, Point.normalizeZ);\n    }\n\n    /**\n     * Non-constant-time multiplication. Uses double-and-add algorithm.\n     * It's faster, but should only be used when you don't care about\n     * an exposed private key e.g. sig verification, which works over *public* keys.\n     */\n    multiplyUnsafe(sc: bigint): Point {\n      const { endo, n: N } = CURVE;\n      ut.aInRange('scalar', sc, _0n, N);\n      const I = Point.ZERO;\n      if (sc === _0n) return I;\n      if (this.is0() || sc === _1n) return this;\n\n      // Case a: no endomorphism. Case b: has precomputes.\n      if (!endo || wnaf.hasPrecomputes(this))\n        return wnaf.wNAFCachedUnsafe(this, sc, Point.normalizeZ);\n\n      // Case c: endomorphism\n      let { k1neg, k1, k2neg, k2 } = endo.splitScalar(sc);\n      let k1p = I;\n      let k2p = I;\n      let d: Point = this;\n      while (k1 > _0n || k2 > _0n) {\n        if (k1 & _1n) k1p = k1p.add(d);\n        if (k2 & _1n) k2p = k2p.add(d);\n        d = d.double();\n        k1 >>= _1n;\n        k2 >>= _1n;\n      }\n      if (k1neg) k1p = k1p.negate();\n      if (k2neg) k2p = k2p.negate();\n      k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n      return k1p.add(k2p);\n    }\n\n    /**\n     * Constant time multiplication.\n     * Uses wNAF method. Windowed method may be 10% faster,\n     * but takes 2x longer to generate and consumes 2x memory.\n     * Uses precomputes when available.\n     * Uses endomorphism for Koblitz curves.\n     * @param scalar by which the point would be multiplied\n     * @returns New point\n     */\n    multiply(scalar: bigint): Point {\n      const { endo, n: N } = CURVE;\n      ut.aInRange('scalar', scalar, _1n, N);\n      let point: Point, fake: Point; // Fake point is used to const-time mult\n      if (endo) {\n        const { k1neg, k1, k2neg, k2 } = endo.splitScalar(scalar);\n        let { p: k1p, f: f1p } = this.wNAF(k1);\n        let { p: k2p, f: f2p } = this.wNAF(k2);\n        k1p = wnaf.constTimeNegate(k1neg, k1p);\n        k2p = wnaf.constTimeNegate(k2neg, k2p);\n        k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n        point = k1p.add(k2p);\n        fake = f1p.add(f2p);\n      } else {\n        const { p, f } = this.wNAF(scalar);\n        point = p;\n        fake = f;\n      }\n      // Normalize `z` for both points, but return only real one\n      return Point.normalizeZ([point, fake])[0];\n    }\n\n    /**\n     * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n     * Not using Strauss-Shamir trick: precomputation tables are faster.\n     * The trick could be useful if both P and Q are not G (not in our case).\n     * @returns non-zero affine point\n     */\n    multiplyAndAddUnsafe(Q: Point, a: bigint, b: bigint): Point | undefined {\n      const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n      const mul = (\n        P: Point,\n        a: bigint // Select faster multiply() method\n      ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n      const sum = mul(this, a).add(mul(Q, b));\n      return sum.is0() ? undefined : sum;\n    }\n\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (x, y, z) ∋ (x=x/z, y=y/z)\n    toAffine(iz?: T): AffinePoint<T> {\n      return toAffineMemo(this, iz);\n    }\n    isTorsionFree(): boolean {\n      const { h: cofactor, isTorsionFree } = CURVE;\n      if (cofactor === _1n) return true; // No subgroups, always torsion-free\n      if (isTorsionFree) return isTorsionFree(Point, this);\n      throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n    }\n    clearCofactor(): Point {\n      const { h: cofactor, clearCofactor } = CURVE;\n      if (cofactor === _1n) return this; // Fast-path\n      if (clearCofactor) return clearCofactor(Point, this) as Point;\n      return this.multiplyUnsafe(CURVE.h);\n    }\n\n    toRawBytes(isCompressed = true): Uint8Array {\n      abool('isCompressed', isCompressed);\n      this.assertValidity();\n      return toBytes(Point, this, isCompressed);\n    }\n\n    toHex(isCompressed = true): string {\n      abool('isCompressed', isCompressed);\n      return ut.bytesToHex(this.toRawBytes(isCompressed));\n    }\n  }\n  const _bits = CURVE.nBitLength;\n  const wnaf = wNAF(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n  // Validate if generator point is on curve\n  return {\n    CURVE,\n    ProjectivePoint: Point as ProjConstructor<T>,\n    normPrivateKeyToScalar,\n    weierstrassEquation,\n    isWithinCurveOrder,\n  };\n}\n\n// Instance\nexport interface SignatureType {\n  readonly r: bigint;\n  readonly s: bigint;\n  readonly recovery?: number;\n  assertValidity(): void;\n  addRecoveryBit(recovery: number): RecoveredSignatureType;\n  hasHighS(): boolean;\n  normalizeS(): SignatureType;\n  recoverPublicKey(msgHash: Hex): ProjPointType<bigint>;\n  toCompactRawBytes(): Uint8Array;\n  toCompactHex(): string;\n  // DER-encoded\n  toDERRawBytes(isCompressed?: boolean): Uint8Array;\n  toDERHex(isCompressed?: boolean): string;\n}\nexport type RecoveredSignatureType = SignatureType & {\n  readonly recovery: number;\n};\n// Static methods\nexport type SignatureConstructor = {\n  new (r: bigint, s: bigint): SignatureType;\n  fromCompact(hex: Hex): SignatureType;\n  fromDER(hex: Hex): SignatureType;\n};\ntype SignatureLike = { r: bigint; s: bigint };\n\nexport type PubKey = Hex | ProjPointType<bigint>;\n\nexport type CurveType = BasicWCurve<bigint> & {\n  hash: CHash; // CHash not FHash because we need outputLen for DRBG\n  hmac: HmacFnSync;\n  randomBytes: (bytesLength?: number) => Uint8Array;\n  lowS?: boolean;\n  bits2int?: (bytes: Uint8Array) => bigint;\n  bits2int_modN?: (bytes: Uint8Array) => bigint;\n};\n\nfunction validateOpts(\n  curve: CurveType\n): Readonly<CurveType & { nByteLength: number; nBitLength: number }> {\n  const opts = validateBasic(curve);\n  ut.validateObject(\n    opts,\n    {\n      hash: 'hash',\n      hmac: 'function',\n      randomBytes: 'function',\n    },\n    {\n      bits2int: 'function',\n      bits2int_modN: 'function',\n      lowS: 'boolean',\n    }\n  );\n  return Object.freeze({ lowS: true, ...opts } as const);\n}\n\nexport type CurveFn = {\n  CURVE: ReturnType<typeof validateOpts>;\n  getPublicKey: (privateKey: PrivKey, isCompressed?: boolean) => Uint8Array;\n  getSharedSecret: (privateA: PrivKey, publicB: Hex, isCompressed?: boolean) => Uint8Array;\n  sign: (msgHash: Hex, privKey: PrivKey, opts?: SignOpts) => RecoveredSignatureType;\n  verify: (signature: Hex | SignatureLike, msgHash: Hex, publicKey: Hex, opts?: VerOpts) => boolean;\n  ProjectivePoint: ProjConstructor<bigint>;\n  Signature: SignatureConstructor;\n  utils: {\n    normPrivateKeyToScalar: (key: PrivKey) => bigint;\n    isValidPrivateKey(privateKey: PrivKey): boolean;\n    randomPrivateKey: () => Uint8Array;\n    precompute: (windowSize?: number, point?: ProjPointType<bigint>) => ProjPointType<bigint>;\n  };\n};\n\n/**\n * Creates short weierstrass curve and ECDSA signature methods for it.\n * @example\n * import { Field } from '@noble/curves/abstract/modular';\n * // Before that, define BigInt-s: a, b, p, n, Gx, Gy\n * const curve = weierstrass({ a, b, Fp: Field(p), n, Gx, Gy, h: 1n })\n */\nexport function weierstrass(curveDef: CurveType): CurveFn {\n  const CURVE = validateOpts(curveDef) as ReturnType<typeof validateOpts>;\n  const { Fp, n: CURVE_ORDER } = CURVE;\n  const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n  const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n\n  function modN(a: bigint) {\n    return mod(a, CURVE_ORDER);\n  }\n  function invN(a: bigint) {\n    return invert(a, CURVE_ORDER);\n  }\n\n  const {\n    ProjectivePoint: Point,\n    normPrivateKeyToScalar,\n    weierstrassEquation,\n    isWithinCurveOrder,\n  } = weierstrassPoints({\n    ...CURVE,\n    toBytes(_c, point, isCompressed: boolean): Uint8Array {\n      const a = point.toAffine();\n      const x = Fp.toBytes(a.x);\n      const cat = ut.concatBytes;\n      abool('isCompressed', isCompressed);\n      if (isCompressed) {\n        return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n      } else {\n        return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n      }\n    },\n    fromBytes(bytes: Uint8Array) {\n      const len = bytes.length;\n      const head = bytes[0];\n      const tail = bytes.subarray(1);\n      // this.assertValidity() is done inside of fromHex\n      if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n        const x = ut.bytesToNumberBE(tail);\n        if (!ut.inRange(x, _1n, Fp.ORDER)) throw new Error('Point is not on curve');\n        const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n        let y: bigint;\n        try {\n          y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n        } catch (sqrtError) {\n          const suffix = sqrtError instanceof Error ? ': ' + sqrtError.message : '';\n          throw new Error('Point is not on curve' + suffix);\n        }\n        const isYOdd = (y & _1n) === _1n;\n        // ECDSA\n        const isHeadOdd = (head & 1) === 1;\n        if (isHeadOdd !== isYOdd) y = Fp.neg(y);\n        return { x, y };\n      } else if (len === uncompressedLen && head === 0x04) {\n        const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n        const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n        return { x, y };\n      } else {\n        const cl = compressedLen;\n        const ul = uncompressedLen;\n        throw new Error(\n          'invalid Point, expected length of ' + cl + ', or uncompressed ' + ul + ', got ' + len\n        );\n      }\n    },\n  });\n  const numToNByteStr = (num: bigint): string =>\n    ut.bytesToHex(ut.numberToBytesBE(num, CURVE.nByteLength));\n\n  function isBiggerThanHalfOrder(number: bigint) {\n    const HALF = CURVE_ORDER >> _1n;\n    return number > HALF;\n  }\n\n  function normalizeS(s: bigint) {\n    return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n  }\n  // slice bytes num\n  const slcNum = (b: Uint8Array, from: number, to: number) => ut.bytesToNumberBE(b.slice(from, to));\n\n  /**\n   * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n   */\n  class Signature implements SignatureType {\n    constructor(\n      readonly r: bigint,\n      readonly s: bigint,\n      readonly recovery?: number\n    ) {\n      this.assertValidity();\n    }\n\n    // pair (bytes of r, bytes of s)\n    static fromCompact(hex: Hex) {\n      const l = CURVE.nByteLength;\n      hex = ensureBytes('compactSignature', hex, l * 2);\n      return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n    }\n\n    // DER encoded ECDSA signature\n    // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n    static fromDER(hex: Hex) {\n      const { r, s } = DER.toSig(ensureBytes('DER', hex));\n      return new Signature(r, s);\n    }\n\n    assertValidity(): void {\n      ut.aInRange('r', this.r, _1n, CURVE_ORDER); // r in [1..N]\n      ut.aInRange('s', this.s, _1n, CURVE_ORDER); // s in [1..N]\n    }\n\n    addRecoveryBit(recovery: number): RecoveredSignature {\n      return new Signature(this.r, this.s, recovery) as RecoveredSignature;\n    }\n\n    recoverPublicKey(msgHash: Hex): typeof Point.BASE {\n      const { r, s, recovery: rec } = this;\n      const h = bits2int_modN(ensureBytes('msgHash', msgHash)); // Truncate hash\n      if (rec == null || ![0, 1, 2, 3].includes(rec)) throw new Error('recovery id invalid');\n      const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n      if (radj >= Fp.ORDER) throw new Error('recovery id 2 or 3 invalid');\n      const prefix = (rec & 1) === 0 ? '02' : '03';\n      const R = Point.fromHex(prefix + numToNByteStr(radj));\n      const ir = invN(radj); // r^-1\n      const u1 = modN(-h * ir); // -hr^-1\n      const u2 = modN(s * ir); // sr^-1\n      const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n      if (!Q) throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n      Q.assertValidity();\n      return Q;\n    }\n\n    // Signatures should be low-s, to prevent malleability.\n    hasHighS(): boolean {\n      return isBiggerThanHalfOrder(this.s);\n    }\n\n    normalizeS() {\n      return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n    }\n\n    // DER-encoded\n    toDERRawBytes() {\n      return ut.hexToBytes(this.toDERHex());\n    }\n    toDERHex() {\n      return DER.hexFromSig({ r: this.r, s: this.s });\n    }\n\n    // padded bytes of r, then padded bytes of s\n    toCompactRawBytes() {\n      return ut.hexToBytes(this.toCompactHex());\n    }\n    toCompactHex() {\n      return numToNByteStr(this.r) + numToNByteStr(this.s);\n    }\n  }\n  type RecoveredSignature = Signature & { recovery: number };\n\n  const utils = {\n    isValidPrivateKey(privateKey: PrivKey) {\n      try {\n        normPrivateKeyToScalar(privateKey);\n        return true;\n      } catch (error) {\n        return false;\n      }\n    },\n    normPrivateKeyToScalar: normPrivateKeyToScalar,\n\n    /**\n     * Produces cryptographically secure private key from random of size\n     * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n     */\n    randomPrivateKey: (): Uint8Array => {\n      const length = getMinHashLength(CURVE.n);\n      return mapHashToField(CURVE.randomBytes(length), CURVE.n);\n    },\n\n    /**\n     * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n     * Allows to massively speed-up `point.multiply(scalar)`.\n     * @returns cached point\n     * @example\n     * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n     * fast.multiply(privKey); // much faster ECDH now\n     */\n    precompute(windowSize = 8, point = Point.BASE): typeof Point.BASE {\n      point._setWindowSize(windowSize);\n      point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n      return point;\n    },\n  };\n\n  /**\n   * Computes public key for a private key. Checks for validity of the private key.\n   * @param privateKey private key\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns Public key, full when isCompressed=false; short when isCompressed=true\n   */\n  function getPublicKey(privateKey: PrivKey, isCompressed = true): Uint8Array {\n    return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n  }\n\n  /**\n   * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n   */\n  function isProbPub(item: PrivKey | PubKey): boolean {\n    const arr = ut.isBytes(item);\n    const str = typeof item === 'string';\n    const len = (arr || str) && (item as Hex).length;\n    if (arr) return len === compressedLen || len === uncompressedLen;\n    if (str) return len === 2 * compressedLen || len === 2 * uncompressedLen;\n    if (item instanceof Point) return true;\n    return false;\n  }\n\n  /**\n   * ECDH (Elliptic Curve Diffie Hellman).\n   * Computes shared public key from private key and public key.\n   * Checks: 1) private key validity 2) shared key is on-curve.\n   * Does NOT hash the result.\n   * @param privateA private key\n   * @param publicB different public key\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns shared public key\n   */\n  function getSharedSecret(privateA: PrivKey, publicB: Hex, isCompressed = true): Uint8Array {\n    if (isProbPub(privateA)) throw new Error('first arg must be private key');\n    if (!isProbPub(publicB)) throw new Error('second arg must be public key');\n    const b = Point.fromHex(publicB); // check for being on-curve\n    return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n  }\n\n  // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n  // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n  // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n  // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n  const bits2int =\n    CURVE.bits2int ||\n    function (bytes: Uint8Array): bigint {\n      // Our custom check \"just in case\"\n      if (bytes.length > 8192) throw new Error('input is too large');\n      // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n      // for some cases, since bytes.length * 8 is not actual bitLength.\n      const num = ut.bytesToNumberBE(bytes); // check for == u8 done here\n      const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n      return delta > 0 ? num >> BigInt(delta) : num;\n    };\n  const bits2int_modN =\n    CURVE.bits2int_modN ||\n    function (bytes: Uint8Array): bigint {\n      return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n    };\n  // NOTE: pads output with zero as per spec\n  const ORDER_MASK = ut.bitMask(CURVE.nBitLength);\n  /**\n   * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n   */\n  function int2octets(num: bigint): Uint8Array {\n    ut.aInRange('num < 2^' + CURVE.nBitLength, num, _0n, ORDER_MASK);\n    // works with order, can have different size than numToField!\n    return ut.numberToBytesBE(num, CURVE.nByteLength);\n  }\n\n  // Steps A, D of RFC6979 3.2\n  // Creates RFC6979 seed; converts msg/privKey to numbers.\n  // Used only in sign, not in verify.\n  // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order,\n  // this will be invalid at least for P521. Also it can be bigger for P224 + SHA256\n  function prepSig(msgHash: Hex, privateKey: PrivKey, opts = defaultSigOpts) {\n    if (['recovered', 'canonical'].some((k) => k in opts))\n      throw new Error('sign() legacy options not supported');\n    const { hash, randomBytes } = CURVE;\n    let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n    if (lowS == null) lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n    msgHash = ensureBytes('msgHash', msgHash);\n    validateSigVerOpts(opts);\n    if (prehash) msgHash = ensureBytes('prehashed msgHash', hash(msgHash));\n\n    // We can't later call bits2octets, since nested bits2int is broken for curves\n    // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n    // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n    const h1int = bits2int_modN(msgHash);\n    const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n    const seedArgs = [int2octets(d), int2octets(h1int)];\n    // extraEntropy. RFC6979 3.6: additional k' (optional).\n    if (ent != null && ent !== false) {\n      // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n      const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n      seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n    }\n    const seed = ut.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n    const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n    // Converts signature params into point w r/s, checks result for validity.\n    function k2sig(kBytes: Uint8Array): RecoveredSignature | undefined {\n      // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n      const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n      if (!isWithinCurveOrder(k)) return; // Important: all mod() calls here must be done over N\n      const ik = invN(k); // k^-1 mod n\n      const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n      const r = modN(q.x); // r = q.x mod n\n      if (r === _0n) return;\n      // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n      // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n      // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n      const s = modN(ik * modN(m + r * d)); // Not using blinding here\n      if (s === _0n) return;\n      let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n      let normS = s;\n      if (lowS && isBiggerThanHalfOrder(s)) {\n        normS = normalizeS(s); // if lowS was passed, ensure s is always\n        recovery ^= 1; // // in the bottom half of N\n      }\n      return new Signature(r, normS, recovery) as RecoveredSignature; // use normS, not s\n    }\n    return { seed, k2sig };\n  }\n  const defaultSigOpts: SignOpts = { lowS: CURVE.lowS, prehash: false };\n  const defaultVerOpts: VerOpts = { lowS: CURVE.lowS, prehash: false };\n\n  /**\n   * Signs message hash with a private key.\n   * ```\n   * sign(m, d, k) where\n   *   (x, y) = G × k\n   *   r = x mod n\n   *   s = (m + dr)/k mod n\n   * ```\n   * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n   * @param privKey private key\n   * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n   * @returns signature with recovery param\n   */\n  function sign(msgHash: Hex, privKey: PrivKey, opts = defaultSigOpts): RecoveredSignature {\n    const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n    const C = CURVE;\n    const drbg = ut.createHmacDrbg<RecoveredSignature>(C.hash.outputLen, C.nByteLength, C.hmac);\n    return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n  }\n\n  // Enable precomputes. Slows down first publicKey computation by 20ms.\n  Point.BASE._setWindowSize(8);\n  // utils.precompute(8, ProjectivePoint.BASE)\n\n  /**\n   * Verifies a signature against message hash and public key.\n   * Rejects lowS signatures by default: to override,\n   * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n   *\n   * ```\n   * verify(r, s, h, P) where\n   *   U1 = hs^-1 mod n\n   *   U2 = rs^-1 mod n\n   *   R = U1⋅G - U2⋅P\n   *   mod(R.x, n) == r\n   * ```\n   */\n  function verify(\n    signature: Hex | SignatureLike,\n    msgHash: Hex,\n    publicKey: Hex,\n    opts = defaultVerOpts\n  ): boolean {\n    const sg = signature;\n    msgHash = ensureBytes('msgHash', msgHash);\n    publicKey = ensureBytes('publicKey', publicKey);\n    const { lowS, prehash, format } = opts;\n\n    // Verify opts, deduce signature format\n    validateSigVerOpts(opts);\n    if ('strict' in opts) throw new Error('options.strict was renamed to lowS');\n    if (format !== undefined && format !== 'compact' && format !== 'der')\n      throw new Error('format must be compact or der');\n    const isHex = typeof sg === 'string' || ut.isBytes(sg);\n    const isObj =\n      !isHex &&\n      !format &&\n      typeof sg === 'object' &&\n      sg !== null &&\n      typeof sg.r === 'bigint' &&\n      typeof sg.s === 'bigint';\n    if (!isHex && !isObj)\n      throw new Error('invalid signature, expected Uint8Array, hex string or Signature instance');\n\n    let _sig: Signature | undefined = undefined;\n    let P: ProjPointType<bigint>;\n    try {\n      if (isObj) _sig = new Signature(sg.r, sg.s);\n      if (isHex) {\n        // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n        // Since DER can also be 2*nByteLength bytes, we check for it first.\n        try {\n          if (format !== 'compact') _sig = Signature.fromDER(sg);\n        } catch (derError) {\n          if (!(derError instanceof DER.Err)) throw derError;\n        }\n        if (!_sig && format !== 'der') _sig = Signature.fromCompact(sg);\n      }\n      P = Point.fromHex(publicKey);\n    } catch (error) {\n      return false;\n    }\n    if (!_sig) return false;\n    if (lowS && _sig.hasHighS()) return false;\n    if (prehash) msgHash = CURVE.hash(msgHash);\n    const { r, s } = _sig;\n    const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n    const is = invN(s); // s^-1\n    const u1 = modN(h * is); // u1 = hs^-1 mod n\n    const u2 = modN(r * is); // u2 = rs^-1 mod n\n    const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n    if (!R) return false;\n    const v = modN(R.x);\n    return v === r;\n  }\n  return {\n    CURVE,\n    getPublicKey,\n    getSharedSecret,\n    sign,\n    verify,\n    ProjectivePoint: Point,\n    Signature,\n    utils,\n  };\n}\n\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio<T>(\n  Fp: IField<T>,\n  Z: T\n): (u: T, v: T) => { isValid: boolean; value: T } {\n  // Generic implementation\n  const q = Fp.ORDER;\n  let l = _0n;\n  for (let o = q - _1n; o % _2n === _0n; o /= _2n) l += _1n;\n  const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n  // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n  // 2n ** c1 == 2n << (c1-1)\n  const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n  const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n  const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n  const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n  const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n  const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n  const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n  const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n  let sqrtRatio = (u: T, v: T): { isValid: boolean; value: T } => {\n    let tv1 = c6; // 1. tv1 = c6\n    let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n    let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n    tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n    let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n    tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n    tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n    tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n    tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n    let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n    tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n    let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n    tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n    tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n    tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n    tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n    // 17. for i in (c1, c1 - 1, ..., 2):\n    for (let i = c1; i > _1n; i--) {\n      let tv5 = i - _2n; // 18.    tv5 = i - 2\n      tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n      let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n      const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n      tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n      tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n      tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n      tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n      tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n    }\n    return { isValid: isQR, value: tv3 };\n  };\n  if (Fp.ORDER % _4n === _3n) {\n    // sqrt_ratio_3mod4(u, v)\n    const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n    const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n    sqrtRatio = (u: T, v: T) => {\n      let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n      const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n      tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n      let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n      y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n      const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n      const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n      const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n      let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n      return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n    };\n  }\n  // No curves uses that\n  // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n  return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU<T>(\n  Fp: IField<T>,\n  opts: {\n    A: T;\n    B: T;\n    Z: T;\n  }\n): (u: T) => { x: T; y: T } {\n  validateField(Fp);\n  if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n    throw new Error('mapToCurveSimpleSWU: invalid opts');\n  const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n  if (!Fp.isOdd) throw new Error('Fp.isOdd is not implemented!');\n  // Input: u, an element of F.\n  // Output: (x, y), a point on E.\n  return (u: T): { x: T; y: T } => {\n    // prettier-ignore\n    let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n    tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n    tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n    tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n    tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n    tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n    tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n    tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n    tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n    tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n    tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n    tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n    tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n    tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n    tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n    tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n    tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n    x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n    const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n    y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n    y = Fp.mul(y, value); // 20.   y = y * y1\n    x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n    y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n    const e1 = Fp.isOdd!(u) === Fp.isOdd!(y); // 23.  e1 = sgn0(u) == sgn0(y)\n    y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n    x = Fp.div(x, tv4); // 25.   x = x / tv4\n    return { x, y };\n  };\n}\n", "/**\n * Utilities for short weierstrass curves, combined with noble-hashes.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac } from '@noble/hashes/hmac';\nimport { concatBytes, randomBytes } from '@noble/hashes/utils';\nimport type { CHash } from './abstract/utils.js';\nimport { type CurveFn, type CurveType, weierstrass } from './abstract/weierstrass.js';\n\n/** connects noble-curves to noble-hashes */\nexport function getHash(hash: CHash): {\n  hash: CHash;\n  hmac: (key: Uint8Array, ...msgs: Uint8Array[]) => Uint8Array;\n  randomBytes: typeof randomBytes;\n} {\n  return {\n    hash,\n    hmac: (key: Uint8Array, ...msgs: Uint8Array[]) => hmac(hash, key, concatBytes(...msgs)),\n    randomBytes,\n  };\n}\n/** Same API as @noble/hashes, with ability to create curve with custom hash */\nexport type CurveDef = Readonly<Omit<CurveType, 'hash' | 'hmac' | 'randomBytes'>>;\nexport type CurveFnWithCreate = CurveFn & { create: (hash: CHash) => CurveFn };\n\nexport function createCurve(curveDef: CurveDef, defHash: CHash): CurveFnWithCreate {\n  const create = (hash: CHash): CurveFn => weierstrass({ ...curveDef, ...getHash(hash) });\n  return { ...create(defHash), create };\n}\n", "/**\n * hash-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380).\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport type { AffinePoint, Group, GroupConstructor } from './curve.js';\nimport { type IField, mod } from './modular.js';\nimport type { CHash } from './utils.js';\nimport { abytes, bytesToNumberBE, concatBytes, utf8ToBytes, validateObject } from './utils.js';\n\nexport type UnicodeOrBytes = string | Uint8Array;\n\n/**\n * * `DST` is a domain separation tag, defined in section 2.2.5\n * * `p` characteristic of F, where F is a finite field of characteristic p and order q = p^m\n * * `m` is extension degree (1 for prime fields)\n * * `k` is the target security target in bits (e.g. 128), from section 5.1\n * * `expand` is `xmd` (SHA2, SHA3, BLAKE) or `xof` (SHAKE, BLAKE-XOF)\n * * `hash` conforming to `utils.CHash` interface, with `outputLen` / `blockLen` props\n */\nexport type Opts = {\n  DST: UnicodeOrBytes;\n  p: bigint;\n  m: number;\n  k: number;\n  expand: 'xmd' | 'xof';\n  hash: CHash;\n};\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = bytesToNumberBE;\n\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value: number, length: number): Uint8Array {\n  anum(value);\n  anum(length);\n  if (value < 0 || value >= 1 << (8 * length)) throw new Error('invalid I2OSP input: ' + value);\n  const res = Array.from({ length }).fill(0) as number[];\n  for (let i = length - 1; i >= 0; i--) {\n    res[i] = value & 0xff;\n    value >>>= 8;\n  }\n  return new Uint8Array(res);\n}\n\nfunction strxor(a: Uint8Array, b: Uint8Array): Uint8Array {\n  const arr = new Uint8Array(a.length);\n  for (let i = 0; i < a.length; i++) {\n    arr[i] = a[i] ^ b[i];\n  }\n  return arr;\n}\n\nfunction anum(item: unknown): void {\n  if (!Number.isSafeInteger(item)) throw new Error('number expected');\n}\n\n/**\n * Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits.\n * [RFC 9380 5.3.1](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1).\n */\nexport function expand_message_xmd(\n  msg: Uint8Array,\n  DST: Uint8Array,\n  lenInBytes: number,\n  H: CHash\n): Uint8Array {\n  abytes(msg);\n  abytes(DST);\n  anum(lenInBytes);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  if (DST.length > 255) DST = H(concatBytes(utf8ToBytes('H2C-OVERSIZE-DST-'), DST));\n  const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n  const ell = Math.ceil(lenInBytes / b_in_bytes);\n  if (lenInBytes > 65535 || ell > 255) throw new Error('expand_message_xmd: invalid lenInBytes');\n  const DST_prime = concatBytes(DST, i2osp(DST.length, 1));\n  const Z_pad = i2osp(0, r_in_bytes);\n  const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n  const b = new Array<Uint8Array>(ell);\n  const b_0 = H(concatBytes(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n  b[0] = H(concatBytes(b_0, i2osp(1, 1), DST_prime));\n  for (let i = 1; i <= ell; i++) {\n    const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n    b[i] = H(concatBytes(...args));\n  }\n  const pseudo_random_bytes = concatBytes(...b);\n  return pseudo_random_bytes.slice(0, lenInBytes);\n}\n\n/**\n * Produces a uniformly random byte string using an extendable-output function (XOF) H.\n * 1. The collision resistance of H MUST be at least k bits.\n * 2. H MUST be an XOF that has been proved indifferentiable from\n *    a random oracle under a reasonable cryptographic assumption.\n * [RFC 9380 5.3.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2).\n */\nexport function expand_message_xof(\n  msg: Uint8Array,\n  DST: Uint8Array,\n  lenInBytes: number,\n  k: number,\n  H: CHash\n): Uint8Array {\n  abytes(msg);\n  abytes(DST);\n  anum(lenInBytes);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n  if (DST.length > 255) {\n    const dkLen = Math.ceil((2 * k) / 8);\n    DST = H.create({ dkLen }).update(utf8ToBytes('H2C-OVERSIZE-DST-')).update(DST).digest();\n  }\n  if (lenInBytes > 65535 || DST.length > 255)\n    throw new Error('expand_message_xof: invalid lenInBytes');\n  return (\n    H.create({ dkLen: lenInBytes })\n      .update(msg)\n      .update(i2osp(lenInBytes, 2))\n      // 2. DST_prime = DST || I2OSP(len(DST), 1)\n      .update(DST)\n      .update(i2osp(DST.length, 1))\n      .digest()\n  );\n}\n\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F.\n * [RFC 9380 5.2](https://www.rfc-editor.org/rfc/rfc9380#section-5.2).\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nexport function hash_to_field(msg: Uint8Array, count: number, options: Opts): bigint[][] {\n  validateObject(options, {\n    DST: 'stringOrUint8Array',\n    p: 'bigint',\n    m: 'isSafeInteger',\n    k: 'isSafeInteger',\n    hash: 'hash',\n  });\n  const { p, k, m, hash, expand, DST: _DST } = options;\n  abytes(msg);\n  anum(count);\n  const DST = typeof _DST === 'string' ? utf8ToBytes(_DST) : _DST;\n  const log2p = p.toString(2).length;\n  const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n  const len_in_bytes = count * m * L;\n  let prb; // pseudo_random_bytes\n  if (expand === 'xmd') {\n    prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n  } else if (expand === 'xof') {\n    prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n  } else if (expand === '_internal_pass') {\n    // for internal tests only\n    prb = msg;\n  } else {\n    throw new Error('expand must be \"xmd\" or \"xof\"');\n  }\n  const u = new Array(count);\n  for (let i = 0; i < count; i++) {\n    const e = new Array(m);\n    for (let j = 0; j < m; j++) {\n      const elm_offset = L * (j + i * m);\n      const tv = prb.subarray(elm_offset, elm_offset + L);\n      e[j] = mod(os2ip(tv), p);\n    }\n    u[i] = e;\n  }\n  return u;\n}\n\nexport type XY<T> = (\n  x: T,\n  y: T\n) => {\n  x: T;\n  y: T;\n};\nexport function isogenyMap<T, F extends IField<T>>(field: F, map: [T[], T[], T[], T[]]): XY<T> {\n  // Make same order as in spec\n  const COEFF = map.map((i) => Array.from(i).reverse());\n  return (x: T, y: T) => {\n    const [xNum, xDen, yNum, yDen] = COEFF.map((val) =>\n      val.reduce((acc, i) => field.add(field.mul(acc, x), i))\n    );\n    x = field.div(xNum, xDen); // xNum / xDen\n    y = field.mul(y, field.div(yNum, yDen)); // y * (yNum / yDev)\n    return { x: x, y: y };\n  };\n}\n\n/** Point interface, which curves must implement to work correctly with the module. */\nexport interface H2CPoint<T> extends Group<H2CPoint<T>> {\n  add(rhs: H2CPoint<T>): H2CPoint<T>;\n  toAffine(iz?: bigint): AffinePoint<T>;\n  clearCofactor(): H2CPoint<T>;\n  assertValidity(): void;\n}\n\nexport interface H2CPointConstructor<T> extends GroupConstructor<H2CPoint<T>> {\n  fromAffine(ap: AffinePoint<T>): H2CPoint<T>;\n}\n\nexport type MapToCurve<T> = (scalar: bigint[]) => AffinePoint<T>;\n\n// Separated from initialization opts, so users won't accidentally change per-curve parameters\n// (changing DST is ok!)\nexport type htfBasicOpts = { DST: UnicodeOrBytes };\nexport type HTFMethod<T> = (msg: Uint8Array, options?: htfBasicOpts) => H2CPoint<T>;\nexport type MapMethod<T> = (scalars: bigint[]) => H2CPoint<T>;\n\n/** Creates hash-to-curve methods from EC Point and mapToCurve function. */\nexport function createHasher<T>(\n  Point: H2CPointConstructor<T>,\n  mapToCurve: MapToCurve<T>,\n  def: Opts & { encodeDST?: UnicodeOrBytes }\n): {\n  hashToCurve: HTFMethod<T>;\n  encodeToCurve: HTFMethod<T>;\n  mapToCurve: MapMethod<T>;\n} {\n  if (typeof mapToCurve !== 'function') throw new Error('mapToCurve() must be defined');\n  return {\n    // Encodes byte string to elliptic curve.\n    // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n    hashToCurve(msg: Uint8Array, options?: htfBasicOpts): H2CPoint<T> {\n      const u = hash_to_field(msg, 2, { ...def, DST: def.DST, ...options } as Opts);\n      const u0 = Point.fromAffine(mapToCurve(u[0]));\n      const u1 = Point.fromAffine(mapToCurve(u[1]));\n      const P = u0.add(u1).clearCofactor();\n      P.assertValidity();\n      return P;\n    },\n\n    // Encodes byte string to elliptic curve.\n    // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n    encodeToCurve(msg: Uint8Array, options?: htfBasicOpts): H2CPoint<T> {\n      const u = hash_to_field(msg, 1, { ...def, DST: def.encodeDST, ...options } as Opts);\n      const P = Point.fromAffine(mapToCurve(u[0])).clearCofactor();\n      P.assertValidity();\n      return P;\n    },\n    // Same as encodeToCurve, but without hash\n    mapToCurve(scalars: bigint[]): H2CPoint<T> {\n      if (!Array.isArray(scalars)) throw new Error('mapToCurve: expected array of bigints');\n      for (const i of scalars)\n        if (typeof i !== 'bigint') throw new Error('mapToCurve: expected array of bigints');\n      const P = Point.fromAffine(mapToCurve(scalars)).clearCofactor();\n      P.assertValidity();\n      return P;\n    },\n  };\n}\n", "/**\n * NIST secp256k1. See [pdf](https://www.secg.org/sec2-v2.pdf).\n *\n * Seems to be rigid (not backdoored)\n * [as per discussion](https://bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975).\n *\n * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n * [See explanation](https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066).\n * @module\n */\n/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha256';\nimport { randomBytes } from '@noble/hashes/utils';\nimport { createCurve, type CurveFnWithCreate } from './_shortw_utils.js';\nimport { createHasher, type HTFMethod, isogenyMap } from './abstract/hash-to-curve.js';\nimport { Field, mod, pow2 } from './abstract/modular.js';\nimport type { Hex, PrivKey } from './abstract/utils.js';\nimport {\n  aInRange,\n  bytesToNumberBE,\n  concatBytes,\n  ensureBytes,\n  inRange,\n  numberToBytesBE,\n} from './abstract/utils.js';\nimport { mapToCurveSimpleSWU, type ProjPointType as PointType } from './abstract/weierstrass.js';\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a: bigint, b: bigint) => (a + b / _2n) / b;\n\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y: bigint): bigint {\n  const P = secp256k1P;\n  // prettier-ignore\n  const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n  // prettier-ignore\n  const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n  const b2 = (y * y * y) % P; // x^3, 11\n  const b3 = (b2 * b2 * y) % P; // x^7\n  const b6 = (pow2(b3, _3n, P) * b3) % P;\n  const b9 = (pow2(b6, _3n, P) * b3) % P;\n  const b11 = (pow2(b9, _2n, P) * b2) % P;\n  const b22 = (pow2(b11, _11n, P) * b11) % P;\n  const b44 = (pow2(b22, _22n, P) * b22) % P;\n  const b88 = (pow2(b44, _44n, P) * b44) % P;\n  const b176 = (pow2(b88, _88n, P) * b88) % P;\n  const b220 = (pow2(b176, _44n, P) * b44) % P;\n  const b223 = (pow2(b220, _3n, P) * b3) % P;\n  const t1 = (pow2(b223, _23n, P) * b22) % P;\n  const t2 = (pow2(t1, _6n, P) * b2) % P;\n  const root = pow2(t2, _2n, P);\n  if (!Fpk1.eql(Fpk1.sqr(root), y)) throw new Error('Cannot find square root');\n  return root;\n}\n\nconst Fpk1 = Field(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n\n/**\n * secp256k1 short weierstrass curve and ECDSA signatures over it.\n *\n * @example\n * import { secp256k1 } from '@noble/curves/secp256k1';\n *\n * const priv = secp256k1.utils.randomPrivateKey();\n * const pub = secp256k1.getPublicKey(priv);\n * const msg = new Uint8Array(32).fill(1); // message hash (not message) in ecdsa\n * const sig = secp256k1.sign(msg, priv); // `{prehash: true}` option is available\n * const isValid = secp256k1.verify(sig, msg, pub) === true;\n */\nexport const secp256k1: CurveFnWithCreate = createCurve(\n  {\n    a: BigInt(0), // equation params: a, b\n    b: BigInt(7),\n    Fp: Fpk1, // Field's prime: 2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n\n    n: secp256k1N, // Curve order, total count of valid points in the field\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1), // Cofactor\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    endo: {\n      // Endomorphism, see above\n      beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n      splitScalar: (k: bigint) => {\n        const n = secp256k1N;\n        const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n        const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n        const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n        const b2 = a1;\n        const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n\n        const c1 = divNearest(b2 * k, n);\n        const c2 = divNearest(-b1 * k, n);\n        let k1 = mod(k - c1 * a1 - c2 * a2, n);\n        let k2 = mod(-c1 * b1 - c2 * b2, n);\n        const k1neg = k1 > POW_2_128;\n        const k2neg = k2 > POW_2_128;\n        if (k1neg) k1 = n - k1;\n        if (k2neg) k2 = n - k2;\n        if (k1 > POW_2_128 || k2 > POW_2_128) {\n          throw new Error('splitScalar: Endomorphism failed, k=' + k);\n        }\n        return { k1neg, k1, k2neg, k2 };\n      },\n    },\n  },\n  sha256\n);\n\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES: { [tag: string]: Uint8Array } = {};\nfunction taggedHash(tag: string, ...messages: Uint8Array[]): Uint8Array {\n  let tagP = TAGGED_HASH_PREFIXES[tag];\n  if (tagP === undefined) {\n    const tagH = sha256(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n    tagP = concatBytes(tagH, tagH);\n    TAGGED_HASH_PREFIXES[tag] = tagP;\n  }\n  return sha256(concatBytes(tagP, ...messages));\n}\n\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point: PointType<bigint>) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n: bigint) => numberToBytesBE(n, 32);\nconst modP = (x: bigint) => mod(x, secp256k1P);\nconst modN = (x: bigint) => mod(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q: PointType<bigint>, a: bigint, b: bigint) =>\n  Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv: PrivKey) {\n  let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n  let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n  const scalar = p.hasEvenY() ? d_ : modN(-d_);\n  return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x: bigint): PointType<bigint> {\n  aInRange('x', x, _1n, secp256k1P); // Fail if x ≥ p.\n  const xx = modP(x * x);\n  const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n  let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n  if (y % _2n !== _0n) y = modP(-y); // Return the unique point P such that x(P) = x and\n  const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n  p.assertValidity();\n  return p;\n}\nconst num = bytesToNumberBE;\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args: Uint8Array[]): bigint {\n  return modN(num(taggedHash('BIP0340/challenge', ...args)));\n}\n\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey: Hex): Uint8Array {\n  return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(\n  message: Hex,\n  privateKey: PrivKey,\n  auxRand: Hex = randomBytes(32)\n): Uint8Array {\n  const m = ensureBytes('message', message);\n  const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n  const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n  const t = numTo32b(d ^ num(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n  const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n  const k_ = modN(num(rand)); // Let k' = int(rand) mod n\n  if (k_ === _0n) throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n  const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n  const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n  const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n  sig.set(rx, 0);\n  sig.set(numTo32b(modN(k + e * d)), 32);\n  // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n  if (!schnorrVerify(sig, m, px)) throw new Error('sign: Invalid signature produced');\n  return sig;\n}\n\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature: Hex, message: Hex, publicKey: Hex): boolean {\n  const sig = ensureBytes('signature', signature, 64);\n  const m = ensureBytes('message', message);\n  const pub = ensureBytes('publicKey', publicKey, 32);\n  try {\n    const P = lift_x(num(pub)); // P = lift_x(int(pk)); fail if that fails\n    const r = num(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n    if (!inRange(r, _1n, secp256k1P)) return false;\n    const s = num(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n    if (!inRange(s, _1n, secp256k1N)) return false;\n    const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n    const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n    if (!R || !R.hasEvenY() || R.toAffine().x !== r) return false; // -eP == (n-e)P\n    return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n  } catch (error) {\n    return false;\n  }\n}\n\nexport type SecpSchnorr = {\n  getPublicKey: typeof schnorrGetPublicKey;\n  sign: typeof schnorrSign;\n  verify: typeof schnorrVerify;\n  utils: {\n    randomPrivateKey: () => Uint8Array;\n    lift_x: typeof lift_x;\n    pointToBytes: (point: PointType<bigint>) => Uint8Array;\n    numberToBytesBE: typeof numberToBytesBE;\n    bytesToNumberBE: typeof bytesToNumberBE;\n    taggedHash: typeof taggedHash;\n    mod: typeof mod;\n  };\n};\n/**\n * Schnorr signatures over secp256k1.\n * https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\n * @example\n * import { schnorr } from '@noble/curves/secp256k1';\n * const priv = schnorr.utils.randomPrivateKey();\n * const pub = schnorr.getPublicKey(priv);\n * const msg = new TextEncoder().encode('hello');\n * const sig = schnorr.sign(msg, priv);\n * const isValid = schnorr.verify(sig, msg, pub);\n */\nexport const schnorr: SecpSchnorr = /* @__PURE__ */ (() => ({\n  getPublicKey: schnorrGetPublicKey,\n  sign: schnorrSign,\n  verify: schnorrVerify,\n  utils: {\n    randomPrivateKey: secp256k1.utils.randomPrivateKey,\n    lift_x,\n    pointToBytes,\n    numberToBytesBE,\n    bytesToNumberBE,\n    taggedHash,\n    mod,\n  },\n}))();\n\nconst isoMap = /* @__PURE__ */ (() =>\n  isogenyMap(\n    Fpk1,\n    [\n      // xNum\n      [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n      ],\n      // xDen\n      [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n      ],\n      // yNum\n      [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n      ],\n      // yDen\n      [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n      ],\n    ].map((i) => i.map((j) => BigInt(j))) as [bigint[], bigint[], bigint[], bigint[]]\n  ))();\nconst mapSWU = /* @__PURE__ */ (() =>\n  mapToCurveSimpleSWU(Fpk1, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fpk1.create(BigInt('-11')),\n  }))();\nconst htf = /* @__PURE__ */ (() =>\n  createHasher(\n    secp256k1.ProjectivePoint,\n    (scalars: bigint[]) => {\n      const { x, y } = mapSWU(Fpk1.create(scalars[0]));\n      return isoMap(x, y);\n    },\n    {\n      DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n      encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n      p: Fpk1.ORDER,\n      m: 1,\n      k: 128,\n      expand: 'xmd',\n      hash: sha256,\n    }\n  ))();\n\n/** secp256k1 hash-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nexport const hashToCurve: HTFMethod<bigint> = /* @__PURE__ */ (() => htf.hashToCurve)();\n\n/** secp256k1 encode-to-curve from [RFC 9380](https://www.rfc-editor.org/rfc/rfc9380). */\nexport const encodeToCurve: HTFMethod<bigint> = /* @__PURE__ */ (() => htf.encodeToCurve)();\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAQM,SAAU,aACd,MACA,YACA,OACA,MAAa;AAEb,MAAI,OAAO,KAAK,iBAAiB;AAAY,WAAO,KAAK,aAAa,YAAY,OAAO,IAAI;AAC7F,QAAM,OAAO,OAAO,EAAE;AACtB,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,KAAK,OAAQ,SAAS,OAAQ,QAAQ;AAC5C,QAAM,KAAK,OAAO,QAAQ,QAAQ;AAClC,QAAM,IAAI,OAAO,IAAI;AACrB,QAAM,IAAI,OAAO,IAAI;AACrB,OAAK,UAAU,aAAa,GAAG,IAAI,IAAI;AACvC,OAAK,UAAU,aAAa,GAAG,IAAI,IAAI;AACzC;AAGM,SAAU,IAAI,GAAW,GAAW,GAAS;AACjD,SAAQ,IAAI,IAAM,CAAC,IAAI;AACzB;AAGM,SAAU,IAAI,GAAW,GAAW,GAAS;AACjD,SAAQ,IAAI,IAAM,IAAI,IAAM,IAAI;AAClC;AAMM,IAAgB,SAAhB,cAAoD,KAAO;EAc/D,YACW,UACF,WACE,WACA,MAAa;AAEtB,UAAK;AALI,SAAA,WAAA;AACF,SAAA,YAAA;AACE,SAAA,YAAA;AACA,SAAA,OAAA;AATD,SAAA,WAAW;AACX,SAAA,SAAS;AACT,SAAA,MAAM;AACN,SAAA,YAAY;AASpB,SAAK,SAAS,IAAI,WAAW,QAAQ;AACrC,SAAK,OAAO,WAAW,KAAK,MAAM;EACpC;EACA,OAAO,MAAW;AAChB,YAAQ,IAAI;AACZ,UAAM,EAAE,MAAM,QAAQ,SAAQ,IAAK;AACnC,WAAO,QAAQ,IAAI;AACnB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AAEpD,UAAI,SAAS,UAAU;AACrB,cAAM,WAAW,WAAW,IAAI;AAChC,eAAO,YAAY,MAAM,KAAK,OAAO;AAAU,eAAK,QAAQ,UAAU,GAAG;AACzE;MACF;AACA,aAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,GAAG;AACnD,WAAK,OAAO;AACZ,aAAO;AACP,UAAI,KAAK,QAAQ,UAAU;AACzB,aAAK,QAAQ,MAAM,CAAC;AACpB,aAAK,MAAM;MACb;IACF;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,WAAU;AACf,WAAO;EACT;EACA,WAAW,KAAe;AACxB,YAAQ,IAAI;AACZ,YAAQ,KAAK,IAAI;AACjB,SAAK,WAAW;AAIhB,UAAM,EAAE,QAAQ,MAAM,UAAU,KAAI,IAAK;AACzC,QAAI,EAAE,IAAG,IAAK;AAEd,WAAO,KAAK,IAAI;AAChB,SAAK,OAAO,SAAS,GAAG,EAAE,KAAK,CAAC;AAGhC,QAAI,KAAK,YAAY,WAAW,KAAK;AACnC,WAAK,QAAQ,MAAM,CAAC;AACpB,YAAM;IACR;AAEA,aAAS,IAAI,KAAK,IAAI,UAAU;AAAK,aAAO,CAAC,IAAI;AAIjD,iBAAa,MAAM,WAAW,GAAG,OAAO,KAAK,SAAS,CAAC,GAAG,IAAI;AAC9D,SAAK,QAAQ,MAAM,CAAC;AACpB,UAAM,QAAQ,WAAW,GAAG;AAC5B,UAAM,MAAM,KAAK;AAEjB,QAAI,MAAM;AAAG,YAAM,IAAI,MAAM,6CAA6C;AAC1E,UAAM,SAAS,MAAM;AACrB,UAAM,QAAQ,KAAK,IAAG;AACtB,QAAI,SAAS,MAAM;AAAQ,YAAM,IAAI,MAAM,oCAAoC;AAC/E,aAAS,IAAI,GAAG,IAAI,QAAQ;AAAK,YAAM,UAAU,IAAI,GAAG,MAAM,CAAC,GAAG,IAAI;EACxE;EACA,SAAM;AACJ,UAAM,EAAE,QAAQ,UAAS,IAAK;AAC9B,SAAK,WAAW,MAAM;AACtB,UAAM,MAAM,OAAO,MAAM,GAAG,SAAS;AACrC,SAAK,QAAO;AACZ,WAAO;EACT;EACA,WAAW,IAAM;AACf,WAAA,KAAO,IAAK,KAAK,YAAmB;AACpC,OAAG,IAAI,GAAG,KAAK,IAAG,CAAE;AACpB,UAAM,EAAE,UAAU,QAAQ,QAAQ,UAAU,WAAW,IAAG,IAAK;AAC/D,OAAG,SAAS;AACZ,OAAG,MAAM;AACT,OAAG,WAAW;AACd,OAAG,YAAY;AACf,QAAI,SAAS;AAAU,SAAG,OAAO,IAAI,MAAM;AAC3C,WAAO;EACT;;;;AC7HF,IAAM,WAA2B,IAAI,YAAY;EAC/C;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAID,IAAM,YAA4B,IAAI,YAAY;EAChD;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAMD,IAAM,WAA2B,IAAI,YAAY,EAAE;AAC7C,IAAO,SAAP,cAAsB,OAAc;EAYxC,cAAA;AACE,UAAM,IAAI,IAAI,GAAG,KAAK;AAVd,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;EAIrC;EACU,MAAG;AACX,UAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC,IAAK;AACnC,WAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAChC;;EAEU,IACR,GAAW,GAAW,GAAW,GAAW,GAAW,GAAW,GAAW,GAAS;AAEtF,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;EACf;EACU,QAAQ,MAAgB,QAAc;AAE9C,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU;AAAG,eAAS,CAAC,IAAI,KAAK,UAAU,QAAQ,KAAK;AACpF,aAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAM,MAAM,SAAS,IAAI,EAAE;AAC3B,YAAM,KAAK,SAAS,IAAI,CAAC;AACzB,YAAM,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,IAAK,QAAQ;AACnD,YAAM,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAK,OAAO;AACjD,eAAS,CAAC,IAAK,KAAK,SAAS,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,IAAK;IACjE;AAEA,QAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC,IAAK;AACjC,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AACpD,YAAM,KAAM,IAAI,SAAS,IAAI,GAAG,GAAG,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC,IAAK;AACrE,YAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AACpD,YAAM,KAAM,SAAS,IAAI,GAAG,GAAG,CAAC,IAAK;AACrC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAK,IAAI,KAAM;AACf,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAK,KAAK,KAAM;IAClB;AAEA,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACjC;EACU,aAAU;AAClB,aAAS,KAAK,CAAC;EACjB;EACA,UAAO;AACL,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,SAAK,OAAO,KAAK,CAAC;EACpB;;AAMF,IAAM,SAAN,cAAqB,OAAM;EASzB,cAAA;AACE,UAAK;AATG,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,YAAa;AACjB,SAAA,IAAI,YAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AAGzB,SAAK,YAAY;EACnB;;AAIK,IAAM,SAAgC,gBAAgB,MAAM,IAAI,OAAM,CAAE;AAExE,IAAM,SAAgC,gBAAgB,MAAM,IAAI,OAAM,CAAE;;;AChIzE,IAAO,OAAP,cAAuC,KAAa;EAQxD,YAAY,MAAa,MAAW;AAClC,UAAK;AAJC,SAAA,WAAW;AACX,SAAA,YAAY;AAIlB,UAAM,IAAI;AACV,UAAM,MAAM,QAAQ,IAAI;AACxB,SAAK,QAAQ,KAAK,OAAM;AACxB,QAAI,OAAO,KAAK,MAAM,WAAW;AAC/B,YAAM,IAAI,MAAM,qDAAqD;AACvE,SAAK,WAAW,KAAK,MAAM;AAC3B,SAAK,YAAY,KAAK,MAAM;AAC5B,UAAM,WAAW,KAAK;AACtB,UAAM,MAAM,IAAI,WAAW,QAAQ;AAEnC,QAAI,IAAI,IAAI,SAAS,WAAW,KAAK,OAAM,EAAG,OAAO,GAAG,EAAE,OAAM,IAAK,GAAG;AACxE,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAAK,UAAI,CAAC,KAAK;AAC/C,SAAK,MAAM,OAAO,GAAG;AAErB,SAAK,QAAQ,KAAK,OAAM;AAExB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAAK,UAAI,CAAC,KAAK,KAAO;AACtD,SAAK,MAAM,OAAO,GAAG;AACrB,QAAI,KAAK,CAAC;EACZ;EACA,OAAO,KAAU;AACf,YAAQ,IAAI;AACZ,SAAK,MAAM,OAAO,GAAG;AACrB,WAAO;EACT;EACA,WAAW,KAAe;AACxB,YAAQ,IAAI;AACZ,WAAO,KAAK,KAAK,SAAS;AAC1B,SAAK,WAAW;AAChB,SAAK,MAAM,WAAW,GAAG;AACzB,SAAK,MAAM,OAAO,GAAG;AACrB,SAAK,MAAM,WAAW,GAAG;AACzB,SAAK,QAAO;EACd;EACA,SAAM;AACJ,UAAM,MAAM,IAAI,WAAW,KAAK,MAAM,SAAS;AAC/C,SAAK,WAAW,GAAG;AACnB,WAAO;EACT;EACA,WAAW,IAAY;AAErB,WAAA,KAAO,OAAO,OAAO,OAAO,eAAe,IAAI,GAAG,CAAA,CAAE;AACpD,UAAM,EAAE,OAAO,OAAO,UAAU,WAAW,UAAU,UAAS,IAAK;AACnE,SAAK;AACL,OAAG,WAAW;AACd,OAAG,YAAY;AACf,OAAG,WAAW;AACd,OAAG,YAAY;AACf,OAAG,QAAQ,MAAM,WAAW,GAAG,KAAK;AACpC,OAAG,QAAQ,MAAM,WAAW,GAAG,KAAK;AACpC,WAAO;EACT;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,MAAM,QAAO;AAClB,SAAK,MAAM,QAAO;EACpB;;AAaK,IAAM,OAGT,CAAC,MAAa,KAAY,YAC5B,IAAI,KAAU,MAAM,GAAG,EAAE,OAAO,OAAO,EAAE,OAAM;AACjD,KAAK,SAAS,CAAC,MAAa,QAAe,IAAI,KAAU,MAAM,GAAG;;;AC3FlE;;;;gBAAAA;EAAA;;;;;;;qBAAAC;EAAA;;;;;;;;;;;;;;;;AAUA,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AAW9B,SAAU,QAAQ,GAAU;AAChC,SAAO,aAAa,cAAe,YAAY,OAAO,CAAC,KAAK,EAAE,YAAY,SAAS;AACrF;AAEM,SAAUD,QAAO,MAAa;AAClC,MAAI,CAAC,QAAQ,IAAI;AAAG,UAAM,IAAI,MAAM,qBAAqB;AAC3D;AAEM,SAAU,MAAM,OAAe,OAAc;AACjD,MAAI,OAAO,UAAU;AAAW,UAAM,IAAI,MAAM,QAAQ,4BAA4B,KAAK;AAC3F;AAGA,IAAM,QAAwB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,GAAG,MAC5D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAK3B,SAAU,WAAW,OAAiB;AAC1C,EAAAA,QAAO,KAAK;AAEZ,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,WAAO,MAAM,MAAM,CAAC,CAAC;EACvB;AACA,SAAO;AACT;AAEM,SAAU,oBAAoBE,MAAoB;AACtD,QAAM,MAAMA,KAAI,SAAS,EAAE;AAC3B,SAAO,IAAI,SAAS,IAAI,MAAM,MAAM;AACtC;AAEM,SAAU,YAAY,KAAW;AACrC,MAAI,OAAO,QAAQ;AAAU,UAAM,IAAI,MAAM,8BAA8B,OAAO,GAAG;AACrF,SAAO,QAAQ,KAAK,MAAM,OAAO,OAAO,GAAG;AAC7C;AAGA,IAAM,SAAS,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG;AAC5D,SAAS,cAAc,IAAU;AAC/B,MAAI,MAAM,OAAO,MAAM,MAAM,OAAO;AAAI,WAAO,KAAK,OAAO;AAC3D,MAAI,MAAM,OAAO,KAAK,MAAM,OAAO;AAAG,WAAO,MAAM,OAAO,IAAI;AAC9D,MAAI,MAAM,OAAO,KAAK,MAAM,OAAO;AAAG,WAAO,MAAM,OAAO,IAAI;AAC9D;AACF;AAKM,SAAU,WAAW,KAAW;AACpC,MAAI,OAAO,QAAQ;AAAU,UAAM,IAAI,MAAM,8BAA8B,OAAO,GAAG;AACrF,QAAM,KAAK,IAAI;AACf,QAAM,KAAK,KAAK;AAChB,MAAI,KAAK;AAAG,UAAM,IAAI,MAAM,qDAAqD,EAAE;AACnF,QAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,WAAS,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,MAAM,MAAM,GAAG;AAC/C,UAAM,KAAK,cAAc,IAAI,WAAW,EAAE,CAAC;AAC3C,UAAM,KAAK,cAAc,IAAI,WAAW,KAAK,CAAC,CAAC;AAC/C,QAAI,OAAO,UAAa,OAAO,QAAW;AACxC,YAAM,OAAO,IAAI,EAAE,IAAI,IAAI,KAAK,CAAC;AACjC,YAAM,IAAI,MAAM,iDAAiD,OAAO,gBAAgB,EAAE;IAC5F;AACA,UAAM,EAAE,IAAI,KAAK,KAAK;EACxB;AACA,SAAO;AACT;AAGM,SAAU,gBAAgB,OAAiB;AAC/C,SAAO,YAAY,WAAW,KAAK,CAAC;AACtC;AACM,SAAU,gBAAgB,OAAiB;AAC/C,EAAAF,QAAO,KAAK;AACZ,SAAO,YAAY,WAAW,WAAW,KAAK,KAAK,EAAE,QAAO,CAAE,CAAC;AACjE;AAEM,SAAU,gBAAgB,GAAoB,KAAW;AAC7D,SAAO,WAAW,EAAE,SAAS,EAAE,EAAE,SAAS,MAAM,GAAG,GAAG,CAAC;AACzD;AACM,SAAU,gBAAgB,GAAoB,KAAW;AAC7D,SAAO,gBAAgB,GAAG,GAAG,EAAE,QAAO;AACxC;AAEM,SAAU,mBAAmB,GAAkB;AACnD,SAAO,WAAW,oBAAoB,CAAC,CAAC;AAC1C;AAWM,SAAU,YAAY,OAAe,KAAU,gBAAuB;AAC1E,MAAI;AACJ,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI;AACF,YAAM,WAAW,GAAG;IACtB,SAAS,GAAG;AACV,YAAM,IAAI,MAAM,QAAQ,+CAA+C,CAAC;IAC1E;EACF,WAAW,QAAQ,GAAG,GAAG;AAGvB,UAAM,WAAW,KAAK,GAAG;EAC3B,OAAO;AACL,UAAM,IAAI,MAAM,QAAQ,mCAAmC;EAC7D;AACA,QAAM,MAAM,IAAI;AAChB,MAAI,OAAO,mBAAmB,YAAY,QAAQ;AAChD,UAAM,IAAI,MAAM,QAAQ,gBAAgB,iBAAiB,oBAAoB,GAAG;AAClF,SAAO;AACT;AAKM,SAAUC,gBAAe,QAAoB;AACjD,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,IAAI,OAAO,CAAC;AAClB,IAAAD,QAAO,CAAC;AACR,WAAO,EAAE;EACX;AACA,QAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,WAAS,IAAI,GAAG,MAAM,GAAG,IAAI,OAAO,QAAQ,KAAK;AAC/C,UAAM,IAAI,OAAO,CAAC;AAClB,QAAI,IAAI,GAAG,GAAG;AACd,WAAO,EAAE;EACX;AACA,SAAO;AACT;AAGM,SAAU,WAAW,GAAe,GAAa;AACrD,MAAI,EAAE,WAAW,EAAE;AAAQ,WAAO;AAClC,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAAK,YAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;AACrD,SAAO,SAAS;AAClB;AASM,SAAU,YAAY,KAAW;AACrC,MAAI,OAAO,QAAQ;AAAU,UAAM,IAAI,MAAM,iBAAiB;AAC9D,SAAO,IAAI,WAAW,IAAI,YAAW,EAAG,OAAO,GAAG,CAAC;AACrD;AAGA,IAAM,WAAW,CAAC,MAAc,OAAO,MAAM,YAAY,OAAO;AAE1D,SAAU,QAAQ,GAAW,KAAa,KAAW;AACzD,SAAO,SAAS,CAAC,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,OAAO,KAAK,IAAI;AAC1E;AAOM,SAAU,SAAS,OAAe,GAAW,KAAa,KAAW;AAMzE,MAAI,CAAC,QAAQ,GAAG,KAAK,GAAG;AACtB,UAAM,IAAI,MAAM,oBAAoB,QAAQ,OAAO,MAAM,aAAa,MAAM,WAAW,CAAC;AAC5F;AAQM,SAAU,OAAO,GAAS;AAC9B,MAAI;AACJ,OAAK,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,OAAO;AAAE;AAC3C,SAAO;AACT;AAOM,SAAU,OAAO,GAAW,KAAW;AAC3C,SAAQ,KAAK,OAAO,GAAG,IAAK;AAC9B;AAKM,SAAU,OAAO,GAAW,KAAa,OAAc;AAC3D,SAAO,KAAM,QAAQ,MAAM,QAAQ,OAAO,GAAG;AAC/C;AAMO,IAAM,UAAU,CAAC,OAAuB,OAAO,OAAO,IAAI,CAAC,KAAK;AAIvE,IAAM,MAAM,CAAC,SAAe,IAAI,WAAW,IAAI;AAC/C,IAAM,OAAO,CAAC,QAAa,WAAW,KAAK,GAAG;AASxC,SAAU,eACd,SACA,UACA,QAAkE;AAElE,MAAI,OAAO,YAAY,YAAY,UAAU;AAAG,UAAM,IAAI,MAAM,0BAA0B;AAC1F,MAAI,OAAO,aAAa,YAAY,WAAW;AAAG,UAAM,IAAI,MAAM,2BAA2B;AAC7F,MAAI,OAAO,WAAW;AAAY,UAAM,IAAI,MAAM,2BAA2B;AAE7E,MAAI,IAAI,IAAI,OAAO;AACnB,MAAI,IAAI,IAAI,OAAO;AACnB,MAAI,IAAI;AACR,QAAM,QAAQ,MAAK;AACjB,MAAE,KAAK,CAAC;AACR,MAAE,KAAK,CAAC;AACR,QAAI;EACN;AACA,QAAM,IAAI,IAAI,MAAoB,OAAO,GAAG,GAAG,GAAG,CAAC;AACnD,QAAM,SAAS,CAAC,OAAO,IAAG,MAAM;AAE9B,QAAI,EAAE,KAAK,CAAC,CAAI,CAAC,GAAG,IAAI;AACxB,QAAI,EAAC;AACL,QAAI,KAAK,WAAW;AAAG;AACvB,QAAI,EAAE,KAAK,CAAC,CAAI,CAAC,GAAG,IAAI;AACxB,QAAI,EAAC;EACP;AACA,QAAM,MAAM,MAAK;AAEf,QAAI,OAAO;AAAM,YAAM,IAAI,MAAM,yBAAyB;AAC1D,QAAI,MAAM;AACV,UAAM,MAAoB,CAAA;AAC1B,WAAO,MAAM,UAAU;AACrB,UAAI,EAAC;AACL,YAAM,KAAK,EAAE,MAAK;AAClB,UAAI,KAAK,EAAE;AACX,aAAO,EAAE;IACX;AACA,WAAOC,aAAY,GAAG,GAAG;EAC3B;AACA,QAAM,WAAW,CAAC,MAAkB,SAAoB;AACtD,UAAK;AACL,WAAO,IAAI;AACX,QAAI,MAAqB;AACzB,WAAO,EAAE,MAAM,KAAK,IAAG,CAAE;AAAI,aAAM;AACnC,UAAK;AACL,WAAO;EACT;AACA,SAAO;AACT;AAIA,IAAM,eAAe;EACnB,QAAQ,CAAC,QAAsB,OAAO,QAAQ;EAC9C,UAAU,CAAC,QAAsB,OAAO,QAAQ;EAChD,SAAS,CAAC,QAAsB,OAAO,QAAQ;EAC/C,QAAQ,CAAC,QAAsB,OAAO,QAAQ;EAC9C,oBAAoB,CAAC,QAAsB,OAAO,QAAQ,YAAY,QAAQ,GAAG;EACjF,eAAe,CAAC,QAAsB,OAAO,cAAc,GAAG;EAC9D,OAAO,CAAC,QAAsB,MAAM,QAAQ,GAAG;EAC/C,OAAO,CAAC,KAAU,WAAsB,OAAe,GAAG,QAAQ,GAAG;EACrE,MAAM,CAAC,QAAsB,OAAO,QAAQ,cAAc,OAAO,cAAc,IAAI,SAAS;;AAMxF,SAAU,eACd,QACA,YACA,gBAA2B,CAAA,GAAE;AAE7B,QAAM,aAAa,CAAC,WAAoB,MAAiB,eAAuB;AAC9E,UAAM,WAAW,aAAa,IAAI;AAClC,QAAI,OAAO,aAAa;AAAY,YAAM,IAAI,MAAM,4BAA4B;AAEhF,UAAM,MAAM,OAAO,SAAgC;AACnD,QAAI,cAAc,QAAQ;AAAW;AACrC,QAAI,CAAC,SAAS,KAAK,MAAM,GAAG;AAC1B,YAAM,IAAI,MACR,WAAW,OAAO,SAAS,IAAI,2BAA2B,OAAO,WAAW,GAAG;IAEnF;EACF;AACA,aAAW,CAAC,WAAW,IAAI,KAAK,OAAO,QAAQ,UAAU;AAAG,eAAW,WAAW,MAAO,KAAK;AAC9F,aAAW,CAAC,WAAW,IAAI,KAAK,OAAO,QAAQ,aAAa;AAAG,eAAW,WAAW,MAAO,IAAI;AAChG,SAAO;AACT;AAaO,IAAM,iBAAiB,MAAY;AACxC,QAAM,IAAI,MAAM,iBAAiB;AACnC;AAMM,SAAU,SACd,IAA6B;AAE7B,QAAM,MAAM,oBAAI,QAAO;AACvB,SAAO,CAAC,QAAW,SAAc;AAC/B,UAAM,MAAM,IAAI,IAAI,GAAG;AACvB,QAAI,QAAQ;AAAW,aAAO;AAC9B,UAAM,WAAW,GAAG,KAAK,GAAG,IAAI;AAChC,QAAI,IAAI,KAAK,QAAQ;AACrB,WAAO;EACT;AACF;;;AC9VA,IAAME,OAAM,OAAO,CAAC;AAApB,IAAuBC,OAAM,OAAO,CAAC;AAArC,IAAwCC,OAAsB,OAAO,CAAC;AAAtE,IAAyE,MAAsB,OAAO,CAAC;AAEvG,IAAM,MAAsB,OAAO,CAAC;AAApC,IAAuC,MAAsB,OAAO,CAAC;AAArE,IAAwE,MAAsB,OAAO,CAAC;AAEtG,IAAM,MAAqB,OAAO,CAAC;AAAnC,IAAsC,OAAuB,OAAO,EAAE;AAGhE,SAAU,IAAI,GAAW,GAAS;AACtC,QAAM,SAAS,IAAI;AACnB,SAAO,UAAUF,OAAM,SAAS,IAAI;AACtC;AAQM,SAAU,IAAIG,MAAa,OAAe,QAAc;AAC5D,MAAI,QAAQH;AAAK,UAAM,IAAI,MAAM,yCAAyC;AAC1E,MAAI,UAAUA;AAAK,UAAM,IAAI,MAAM,iBAAiB;AACpD,MAAI,WAAWC;AAAK,WAAOD;AAC3B,MAAI,MAAMC;AACV,SAAO,QAAQD,MAAK;AAClB,QAAI,QAAQC;AAAK,YAAO,MAAME,OAAO;AACrC,IAAAA,OAAOA,OAAMA,OAAO;AACpB,cAAUF;EACZ;AACA,SAAO;AACT;AAGM,SAAU,KAAK,GAAW,OAAe,QAAc;AAC3D,MAAI,MAAM;AACV,SAAO,UAAUD,MAAK;AACpB,WAAO;AACP,WAAO;EACT;AACA,SAAO;AACT;AAMM,SAAU,OAAO,QAAgB,QAAc;AACnD,MAAI,WAAWA;AAAK,UAAM,IAAI,MAAM,kCAAkC;AACtE,MAAI,UAAUA;AAAK,UAAM,IAAI,MAAM,4CAA4C,MAAM;AAErF,MAAI,IAAI,IAAI,QAAQ,MAAM;AAC1B,MAAI,IAAI;AAER,MAAI,IAAIA,MAAK,IAAIC,MAAK,IAAIA,MAAK,IAAID;AACnC,SAAO,MAAMA,MAAK;AAEhB,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI,IAAI;AAClB,UAAM,IAAI,IAAI,IAAI;AAElB,QAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;EACzC;AACA,QAAM,MAAM;AACZ,MAAI,QAAQC;AAAK,UAAM,IAAI,MAAM,wBAAwB;AACzD,SAAO,IAAI,GAAG,MAAM;AACtB;AAUM,SAAU,cAAc,GAAS;AAMrC,QAAM,aAAa,IAAIA,QAAOC;AAE9B,MAAI,GAAW,GAAW;AAG1B,OAAK,IAAI,IAAID,MAAK,IAAI,GAAG,IAAIC,SAAQF,MAAK,KAAKE,MAAK;AAAI;AAGxD,OAAK,IAAIA,MAAK,IAAI,KAAK,IAAI,GAAG,WAAW,CAAC,MAAM,IAAID,MAAK,KAAK;AAE5D,QAAI,IAAI;AAAM,YAAM,IAAI,MAAM,6CAA6C;EAC7E;AAGA,MAAI,MAAM,GAAG;AACX,UAAM,UAAU,IAAIA,QAAO;AAC3B,WAAO,SAAS,YAAe,IAAe,GAAI;AAChD,YAAM,OAAO,GAAG,IAAI,GAAG,MAAM;AAC7B,UAAI,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC;AAAG,cAAM,IAAI,MAAM,yBAAyB;AACvE,aAAO;IACT;EACF;AAGA,QAAM,UAAU,IAAIA,QAAOC;AAC3B,SAAO,SAAS,YAAe,IAAe,GAAI;AAEhD,QAAI,GAAG,IAAI,GAAG,SAAS,MAAM,GAAG,IAAI,GAAG,GAAG;AAAG,YAAM,IAAI,MAAM,yBAAyB;AACtF,QAAI,IAAI;AAER,QAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;AACnC,QAAI,IAAI,GAAG,IAAI,GAAG,MAAM;AACxB,QAAI,IAAI,GAAG,IAAI,GAAG,CAAC;AAEnB,WAAO,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG;AACzB,UAAI,GAAG,IAAI,GAAG,GAAG,IAAI;AAAG,eAAO,GAAG;AAElC,UAAI,IAAI;AACR,eAAS,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK;AACnC,YAAI,GAAG,IAAI,IAAI,GAAG,GAAG;AAAG;AACxB,aAAK,GAAG,IAAI,EAAE;MAChB;AAEA,YAAM,KAAK,GAAG,IAAI,GAAGD,QAAO,OAAO,IAAI,IAAI,CAAC,CAAC;AAC7C,UAAI,GAAG,IAAI,EAAE;AACb,UAAI,GAAG,IAAI,GAAG,EAAE;AAChB,UAAI,GAAG,IAAI,GAAG,CAAC;AACf,UAAI;IACN;AACA,WAAO;EACT;AACF;AAaM,SAAU,OAAO,GAAS;AAG9B,MAAI,IAAI,QAAQ,KAAK;AAKnB,UAAM,UAAU,IAAIA,QAAO;AAC3B,WAAO,SAAS,UAAa,IAAe,GAAI;AAC9C,YAAM,OAAO,GAAG,IAAI,GAAG,MAAM;AAE7B,UAAI,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC;AAAG,cAAM,IAAI,MAAM,yBAAyB;AACvE,aAAO;IACT;EACF;AAGA,MAAI,IAAI,QAAQ,KAAK;AACnB,UAAM,MAAM,IAAI,OAAO;AACvB,WAAO,SAAS,UAAa,IAAe,GAAI;AAC9C,YAAM,KAAK,GAAG,IAAI,GAAGC,IAAG;AACxB,YAAM,IAAI,GAAG,IAAI,IAAI,EAAE;AACvB,YAAM,KAAK,GAAG,IAAI,GAAG,CAAC;AACtB,YAAM,IAAI,GAAG,IAAI,GAAG,IAAI,IAAIA,IAAG,GAAG,CAAC;AACnC,YAAM,OAAO,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AACzC,UAAI,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC;AAAG,cAAM,IAAI,MAAM,yBAAyB;AACvE,aAAO;IACT;EACF;AAGA,MAAI,IAAI,SAAS,KAAK;EAoBtB;AAEA,SAAO,cAAc,CAAC;AACxB;AAkDA,IAAM,eAAe;EACnB;EAAU;EAAW;EAAO;EAAO;EAAO;EAAQ;EAClD;EAAO;EAAO;EAAO;EAAO;EAAO;EACnC;EAAQ;EAAQ;EAAQ;;AAEpB,SAAU,cAAiB,OAAgB;AAC/C,QAAM,UAAU;IACd,OAAO;IACP,MAAM;IACN,OAAO;IACP,MAAM;;AAER,QAAM,OAAO,aAAa,OAAO,CAAC,KAAK,QAAe;AACpD,QAAI,GAAG,IAAI;AACX,WAAO;EACT,GAAG,OAAO;AACV,SAAO,eAAe,OAAO,IAAI;AACnC;AAQM,SAAU,MAAS,GAAcE,MAAQ,OAAa;AAG1D,MAAI,QAAQC;AAAK,UAAM,IAAI,MAAM,yCAAyC;AAC1E,MAAI,UAAUA;AAAK,WAAO,EAAE;AAC5B,MAAI,UAAUC;AAAK,WAAOF;AAC1B,MAAI,IAAI,EAAE;AACV,MAAI,IAAIA;AACR,SAAO,QAAQC,MAAK;AAClB,QAAI,QAAQC;AAAK,UAAI,EAAE,IAAI,GAAG,CAAC;AAC/B,QAAI,EAAE,IAAI,CAAC;AACX,cAAUA;EACZ;AACA,SAAO;AACT;AAMM,SAAU,cAAiB,GAAc,MAAS;AACtD,QAAM,MAAM,IAAI,MAAM,KAAK,MAAM;AAEjC,QAAM,iBAAiB,KAAK,OAAO,CAAC,KAAKF,MAAK,MAAK;AACjD,QAAI,EAAE,IAAIA,IAAG;AAAG,aAAO;AACvB,QAAI,CAAC,IAAI;AACT,WAAO,EAAE,IAAI,KAAKA,IAAG;EACvB,GAAG,EAAE,GAAG;AAER,QAAM,WAAW,EAAE,IAAI,cAAc;AAErC,OAAK,YAAY,CAAC,KAAKA,MAAK,MAAK;AAC/B,QAAI,EAAE,IAAIA,IAAG;AAAG,aAAO;AACvB,QAAI,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC;AAC1B,WAAO,EAAE,IAAI,KAAKA,IAAG;EACvB,GAAG,QAAQ;AACX,SAAO;AACT;AA2BM,SAAU,QACd,GACA,YAAmB;AAMnB,QAAM,cAAc,eAAe,SAAY,aAAa,EAAE,SAAS,CAAC,EAAE;AAC1E,QAAM,cAAc,KAAK,KAAK,cAAc,CAAC;AAC7C,SAAO,EAAE,YAAY,aAAa,YAAW;AAC/C;AAkBM,SAAU,MACd,OACAG,SACA,OAAO,OACP,QAAiC,CAAA,GAAE;AAEnC,MAAI,SAASC;AAAK,UAAM,IAAI,MAAM,4CAA4C,KAAK;AACnF,QAAM,EAAE,YAAY,MAAM,aAAa,MAAK,IAAK,QAAQ,OAAOD,OAAM;AACtE,MAAI,QAAQ;AAAM,UAAM,IAAI,MAAM,gDAAgD;AAClF,MAAI;AACJ,QAAM,IAAuB,OAAO,OAAO;IACzC;IACA;IACA;IACA;IACA,MAAM,QAAQ,IAAI;IAClB,MAAMC;IACN,KAAKC;IACL,QAAQ,CAACC,SAAQ,IAAIA,MAAK,KAAK;IAC/B,SAAS,CAACA,SAAO;AACf,UAAI,OAAOA,SAAQ;AACjB,cAAM,IAAI,MAAM,iDAAiD,OAAOA,IAAG;AAC7E,aAAOF,QAAOE,QAAOA,OAAM;IAC7B;IACA,KAAK,CAACA,SAAQA,SAAQF;IACtB,OAAO,CAACE,UAASA,OAAMD,UAASA;IAChC,KAAK,CAACC,SAAQ,IAAI,CAACA,MAAK,KAAK;IAC7B,KAAK,CAAC,KAAK,QAAQ,QAAQ;IAE3B,KAAK,CAACA,SAAQ,IAAIA,OAAMA,MAAK,KAAK;IAClC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,KAAK,KAAK;IACvC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,KAAK,KAAK;IACvC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,KAAK,KAAK;IACvC,KAAK,CAACA,MAAK,UAAU,MAAM,GAAGA,MAAK,KAAK;IACxC,KAAK,CAAC,KAAK,QAAQ,IAAI,MAAM,OAAO,KAAK,KAAK,GAAG,KAAK;;IAGtD,MAAM,CAACA,SAAQA,OAAMA;IACrB,MAAM,CAAC,KAAK,QAAQ,MAAM;IAC1B,MAAM,CAAC,KAAK,QAAQ,MAAM;IAC1B,MAAM,CAAC,KAAK,QAAQ,MAAM;IAE1B,KAAK,CAACA,SAAQ,OAAOA,MAAK,KAAK;IAC/B,MACE,MAAM,SACL,CAAC,MAAK;AACL,UAAI,CAAC;AAAO,gBAAQ,OAAO,KAAK;AAChC,aAAO,MAAM,GAAG,CAAC;IACnB;IACF,aAAa,CAAC,QAAQ,cAAc,GAAG,GAAG;;;IAG1C,MAAM,CAAC,GAAG,GAAG,MAAO,IAAI,IAAI;IAC5B,SAAS,CAACA,SAAS,OAAO,gBAAgBA,MAAK,KAAK,IAAI,gBAAgBA,MAAK,KAAK;IAClF,WAAW,CAAC,UAAS;AACnB,UAAI,MAAM,WAAW;AACnB,cAAM,IAAI,MAAM,+BAA+B,QAAQ,iBAAiB,MAAM,MAAM;AACtF,aAAO,OAAO,gBAAgB,KAAK,IAAI,gBAAgB,KAAK;IAC9D;GACU;AACZ,SAAO,OAAO,OAAO,CAAC;AACxB;AA0CM,SAAU,oBAAoB,YAAkB;AACpD,MAAI,OAAO,eAAe;AAAU,UAAM,IAAI,MAAM,4BAA4B;AAChF,QAAM,YAAY,WAAW,SAAS,CAAC,EAAE;AACzC,SAAO,KAAK,KAAK,YAAY,CAAC;AAChC;AASM,SAAU,iBAAiB,YAAkB;AACjD,QAAM,SAAS,oBAAoB,UAAU;AAC7C,SAAO,SAAS,KAAK,KAAK,SAAS,CAAC;AACtC;AAeM,SAAU,eAAe,KAAiB,YAAoB,OAAO,OAAK;AAC9E,QAAM,MAAM,IAAI;AAChB,QAAM,WAAW,oBAAoB,UAAU;AAC/C,QAAM,SAAS,iBAAiB,UAAU;AAE1C,MAAI,MAAM,MAAM,MAAM,UAAU,MAAM;AACpC,UAAM,IAAI,MAAM,cAAc,SAAS,+BAA+B,GAAG;AAC3E,QAAMC,OAAM,OAAO,gBAAgB,GAAG,IAAI,gBAAgB,GAAG;AAE7D,QAAM,UAAU,IAAIA,MAAK,aAAaC,IAAG,IAAIA;AAC7C,SAAO,OAAO,gBAAgB,SAAS,QAAQ,IAAI,gBAAgB,SAAS,QAAQ;AACtF;;;AC1gBA,IAAMC,OAAM,OAAO,CAAC;AACpB,IAAMC,OAAM,OAAO,CAAC;AAsBpB,SAAS,gBAAoC,WAAoB,MAAO;AACtE,QAAM,MAAM,KAAK,OAAM;AACvB,SAAO,YAAY,MAAM;AAC3B;AAEA,SAAS,UAAU,GAAW,MAAY;AACxC,MAAI,CAAC,OAAO,cAAc,CAAC,KAAK,KAAK,KAAK,IAAI;AAC5C,UAAM,IAAI,MAAM,uCAAuC,OAAO,cAAc,CAAC;AACjF;AAEA,SAAS,UAAU,GAAW,MAAY;AACxC,YAAU,GAAG,IAAI;AACjB,QAAM,UAAU,KAAK,KAAK,OAAO,CAAC,IAAI;AACtC,QAAM,aAAa,MAAM,IAAI;AAC7B,SAAO,EAAE,SAAS,WAAU;AAC9B;AAEA,SAAS,kBAAkB,QAAe,GAAM;AAC9C,MAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,UAAM,IAAI,MAAM,gBAAgB;AAC5D,SAAO,QAAQ,CAAC,GAAG,MAAK;AACtB,QAAI,EAAE,aAAa;AAAI,YAAM,IAAI,MAAM,4BAA4B,CAAC;EACtE,CAAC;AACH;AACA,SAAS,mBAAmB,SAAgB,OAAU;AACpD,MAAI,CAAC,MAAM,QAAQ,OAAO;AAAG,UAAM,IAAI,MAAM,2BAA2B;AACxE,UAAQ,QAAQ,CAAC,GAAG,MAAK;AACvB,QAAI,CAAC,MAAM,QAAQ,CAAC;AAAG,YAAM,IAAI,MAAM,6BAA6B,CAAC;EACvE,CAAC;AACH;AAIA,IAAM,mBAAmB,oBAAI,QAAO;AACpC,IAAM,mBAAmB,oBAAI,QAAO;AAEpC,SAAS,KAAK,GAAM;AAClB,SAAO,iBAAiB,IAAI,CAAC,KAAK;AACpC;AA6BM,SAAU,KAAyB,GAAwB,MAAY;AAC3E,SAAO;IACL;IAEA,eAAe,KAAM;AACnB,aAAO,KAAK,GAAG,MAAM;IACvB;;IAGA,aAAa,KAAQ,GAAW,IAAI,EAAE,MAAI;AACxC,UAAI,IAAO;AACX,aAAO,IAAID,MAAK;AACd,YAAI,IAAIC;AAAK,cAAI,EAAE,IAAI,CAAC;AACxB,YAAI,EAAE,OAAM;AACZ,cAAMA;MACR;AACA,aAAO;IACT;;;;;;;;;;;;;IAcA,iBAAiB,KAAQ,GAAS;AAChC,YAAM,EAAE,SAAS,WAAU,IAAK,UAAU,GAAG,IAAI;AACjD,YAAM,SAAc,CAAA;AACpB,UAAI,IAAO;AACX,UAAI,OAAO;AACX,eAAS,SAAS,GAAG,SAAS,SAAS,UAAU;AAC/C,eAAO;AACP,eAAO,KAAK,IAAI;AAEhB,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,iBAAO,KAAK,IAAI,CAAC;AACjB,iBAAO,KAAK,IAAI;QAClB;AACA,YAAI,KAAK,OAAM;MACjB;AACA,aAAO;IACT;;;;;;;;IASA,KAAK,GAAW,aAAkB,GAAS;AAGzC,YAAM,EAAE,SAAS,WAAU,IAAK,UAAU,GAAG,IAAI;AAEjD,UAAI,IAAI,EAAE;AACV,UAAI,IAAI,EAAE;AAEV,YAAM,OAAO,OAAO,KAAK,IAAI,CAAC;AAC9B,YAAM,YAAY,KAAK;AACvB,YAAM,UAAU,OAAO,CAAC;AAExB,eAAS,SAAS,GAAG,SAAS,SAAS,UAAU;AAC/C,cAAM,SAAS,SAAS;AAExB,YAAI,QAAQ,OAAO,IAAI,IAAI;AAG3B,cAAM;AAIN,YAAI,QAAQ,YAAY;AACtB,mBAAS;AACT,eAAKA;QACP;AAUA,cAAM,UAAU;AAChB,cAAM,UAAU,SAAS,KAAK,IAAI,KAAK,IAAI;AAC3C,cAAM,QAAQ,SAAS,MAAM;AAC7B,cAAM,QAAQ,QAAQ;AACtB,YAAI,UAAU,GAAG;AAEf,cAAI,EAAE,IAAI,gBAAgB,OAAO,YAAY,OAAO,CAAC,CAAC;QACxD,OAAO;AACL,cAAI,EAAE,IAAI,gBAAgB,OAAO,YAAY,OAAO,CAAC,CAAC;QACxD;MACF;AAMA,aAAO,EAAE,GAAG,EAAC;IACf;;;;;;;;;IAUA,WAAW,GAAW,aAAkB,GAAW,MAAS,EAAE,MAAI;AAChE,YAAM,EAAE,SAAS,WAAU,IAAK,UAAU,GAAG,IAAI;AACjD,YAAM,OAAO,OAAO,KAAK,IAAI,CAAC;AAC9B,YAAM,YAAY,KAAK;AACvB,YAAM,UAAU,OAAO,CAAC;AACxB,eAAS,SAAS,GAAG,SAAS,SAAS,UAAU;AAC/C,cAAM,SAAS,SAAS;AACxB,YAAI,MAAMD;AAAK;AAEf,YAAI,QAAQ,OAAO,IAAI,IAAI;AAE3B,cAAM;AAGN,YAAI,QAAQ,YAAY;AACtB,mBAAS;AACT,eAAKC;QACP;AACA,YAAI,UAAU;AAAG;AACjB,YAAI,OAAO,YAAY,SAAS,KAAK,IAAI,KAAK,IAAI,CAAC;AACnD,YAAI,QAAQ;AAAG,iBAAO,KAAK,OAAM;AAEjC,cAAM,IAAI,IAAI,IAAI;MACpB;AACA,aAAO;IACT;IAEA,eAAe,GAAW,GAAM,WAAoB;AAElD,UAAI,OAAO,iBAAiB,IAAI,CAAC;AACjC,UAAI,CAAC,MAAM;AACT,eAAO,KAAK,iBAAiB,GAAG,CAAC;AACjC,YAAI,MAAM;AAAG,2BAAiB,IAAI,GAAG,UAAU,IAAI,CAAC;MACtD;AACA,aAAO;IACT;IAEA,WAAW,GAAM,GAAW,WAAoB;AAC9C,YAAM,IAAI,KAAK,CAAC;AAChB,aAAO,KAAK,KAAK,GAAG,KAAK,eAAe,GAAG,GAAG,SAAS,GAAG,CAAC;IAC7D;IAEA,iBAAiB,GAAM,GAAW,WAAsB,MAAQ;AAC9D,YAAM,IAAI,KAAK,CAAC;AAChB,UAAI,MAAM;AAAG,eAAO,KAAK,aAAa,GAAG,GAAG,IAAI;AAChD,aAAO,KAAK,WAAW,GAAG,KAAK,eAAe,GAAG,GAAG,SAAS,GAAG,GAAG,IAAI;IACzE;;;;IAMA,cAAc,GAAM,GAAS;AAC3B,gBAAU,GAAG,IAAI;AACjB,uBAAiB,IAAI,GAAG,CAAC;AACzB,uBAAiB,OAAO,CAAC;IAC3B;;AAEJ;AAYM,SAAU,UACd,GACA,QACA,QACA,SAAiB;AAQjB,oBAAkB,QAAQ,CAAC;AAC3B,qBAAmB,SAAS,MAAM;AAClC,MAAI,OAAO,WAAW,QAAQ;AAC5B,UAAM,IAAI,MAAM,qDAAqD;AACvE,QAAM,OAAO,EAAE;AACf,QAAM,QAAQ,OAAO,OAAO,OAAO,MAAM,CAAC;AAC1C,QAAM,aAAa,QAAQ,KAAK,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI;AAChF,QAAM,QAAQ,KAAK,cAAc;AACjC,QAAM,UAAU,IAAI,MAAM,OAAO,CAAC,EAAE,KAAK,IAAI;AAC7C,QAAM,WAAW,KAAK,OAAO,OAAO,OAAO,KAAK,UAAU,IAAI;AAC9D,MAAI,MAAM;AACV,WAAS,IAAI,UAAU,KAAK,GAAG,KAAK,YAAY;AAC9C,YAAQ,KAAK,IAAI;AACjB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAM,SAAS,QAAQ,CAAC;AACxB,YAAMC,SAAQ,OAAQ,UAAU,OAAO,CAAC,IAAK,OAAO,IAAI,CAAC;AACzD,cAAQA,MAAK,IAAI,QAAQA,MAAK,EAAE,IAAI,OAAO,CAAC,CAAC;IAC/C;AACA,QAAI,OAAO;AAEX,aAAS,IAAI,QAAQ,SAAS,GAAG,OAAO,MAAM,IAAI,GAAG,KAAK;AACxD,aAAO,KAAK,IAAI,QAAQ,CAAC,CAAC;AAC1B,aAAO,KAAK,IAAI,IAAI;IACtB;AACA,UAAM,IAAI,IAAI,IAAI;AAClB,QAAI,MAAM;AAAG,eAAS,IAAI,GAAG,IAAI,YAAY;AAAK,cAAM,IAAI,OAAM;EACpE;AACA,SAAO;AACT;AAmGM,SAAU,cACd,OAAyB;AAUzB,gBAAc,MAAM,EAAE;AACtB,iBACE,OACA;IACE,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;KAEN;IACE,YAAY;IACZ,aAAa;GACd;AAGH,SAAO,OAAO,OAAO;IACnB,GAAG,QAAQ,MAAM,GAAG,MAAM,UAAU;IACpC,GAAG;IACH,GAAG,EAAE,GAAG,MAAM,GAAG,MAAK;GACd;AACZ;;;AC9XA,SAAS,mBAAmB,MAAwB;AAClD,MAAI,KAAK,SAAS;AAAW,UAAM,QAAQ,KAAK,IAAI;AACpD,MAAI,KAAK,YAAY;AAAW,UAAM,WAAW,KAAK,OAAO;AAC/D;AA0CA,SAAS,kBAAqB,OAAyB;AACrD,QAAM,OAAO,cAAc,KAAK;AAChC,EAAG,eACD,MACA;IACE,GAAG;IACH,GAAG;KAEL;IACE,0BAA0B;IAC1B,gBAAgB;IAChB,eAAe;IACf,eAAe;IACf,oBAAoB;IACpB,WAAW;IACX,SAAS;GACV;AAEH,QAAM,EAAE,MAAM,IAAI,EAAC,IAAK;AACxB,MAAI,MAAM;AACR,QAAI,CAAC,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG;AACvB,YAAM,IAAI,MAAM,4EAA4E;IAC9F;AACA,QACE,OAAO,SAAS,YAChB,OAAO,KAAK,SAAS,YACrB,OAAO,KAAK,gBAAgB,YAC5B;AACA,YAAM,IAAI,MAAM,uEAAuE;IACzF;EACF;AACA,SAAO,OAAO,OAAO,EAAE,GAAG,KAAI,CAAW;AAC3C;AAUA,IAAM,EAAE,iBAAiB,KAAK,YAAY,IAAG,IAAK;AAE5C,IAAO,SAAP,cAAsB,MAAK;EAC/B,YAAY,IAAI,IAAE;AAChB,UAAM,CAAC;EACT;;AA6BK,IAAM,MAAY;;EAEvB,KAAK;;EAEL,MAAM;IACJ,QAAQ,CAAC,KAAa,SAAwB;AAC5C,YAAM,EAAE,KAAK,EAAC,IAAK;AACnB,UAAI,MAAM,KAAK,MAAM;AAAK,cAAM,IAAI,EAAE,uBAAuB;AAC7D,UAAI,KAAK,SAAS;AAAG,cAAM,IAAI,EAAE,2BAA2B;AAC5D,YAAM,UAAU,KAAK,SAAS;AAC9B,YAAM,MAAS,oBAAoB,OAAO;AAC1C,UAAK,IAAI,SAAS,IAAK;AAAa,cAAM,IAAI,EAAE,sCAAsC;AAEtF,YAAM,SAAS,UAAU,MAAS,oBAAqB,IAAI,SAAS,IAAK,GAAW,IAAI;AACxF,YAAM,IAAO,oBAAoB,GAAG;AACpC,aAAO,IAAI,SAAS,MAAM;IAC5B;;IAEA,OAAO,KAAa,MAAgB;AAClC,YAAM,EAAE,KAAK,EAAC,IAAK;AACnB,UAAI,MAAM;AACV,UAAI,MAAM,KAAK,MAAM;AAAK,cAAM,IAAI,EAAE,uBAAuB;AAC7D,UAAI,KAAK,SAAS,KAAK,KAAK,KAAK,MAAM;AAAK,cAAM,IAAI,EAAE,uBAAuB;AAC/E,YAAM,QAAQ,KAAK,KAAK;AACxB,YAAM,SAAS,CAAC,EAAE,QAAQ;AAC1B,UAAI,SAAS;AACb,UAAI,CAAC;AAAQ,iBAAS;WACjB;AAEH,cAAM,SAAS,QAAQ;AACvB,YAAI,CAAC;AAAQ,gBAAM,IAAI,EAAE,mDAAmD;AAC5E,YAAI,SAAS;AAAG,gBAAM,IAAI,EAAE,0CAA0C;AACtE,cAAM,cAAc,KAAK,SAAS,KAAK,MAAM,MAAM;AACnD,YAAI,YAAY,WAAW;AAAQ,gBAAM,IAAI,EAAE,uCAAuC;AACtF,YAAI,YAAY,CAAC,MAAM;AAAG,gBAAM,IAAI,EAAE,sCAAsC;AAC5E,mBAAW,KAAK;AAAa,mBAAU,UAAU,IAAK;AACtD,eAAO;AACP,YAAI,SAAS;AAAK,gBAAM,IAAI,EAAE,wCAAwC;MACxE;AACA,YAAM,IAAI,KAAK,SAAS,KAAK,MAAM,MAAM;AACzC,UAAI,EAAE,WAAW;AAAQ,cAAM,IAAI,EAAE,gCAAgC;AACrE,aAAO,EAAE,GAAG,GAAG,KAAK,SAAS,MAAM,MAAM,EAAC;IAC5C;;;;;;EAMF,MAAM;IACJ,OAAOC,MAAW;AAChB,YAAM,EAAE,KAAK,EAAC,IAAK;AACnB,UAAIA,OAAMC;AAAK,cAAM,IAAI,EAAE,4CAA4C;AACvE,UAAI,MAAS,oBAAoBD,IAAG;AAEpC,UAAI,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI;AAAQ,cAAM,OAAO;AACvD,UAAI,IAAI,SAAS;AAAG,cAAM,IAAI,EAAE,gDAAgD;AAChF,aAAO;IACT;IACA,OAAO,MAAgB;AACrB,YAAM,EAAE,KAAK,EAAC,IAAK;AACnB,UAAI,KAAK,CAAC,IAAI;AAAa,cAAM,IAAI,EAAE,qCAAqC;AAC5E,UAAI,KAAK,CAAC,MAAM,KAAQ,EAAE,KAAK,CAAC,IAAI;AAClC,cAAM,IAAI,EAAE,qDAAqD;AACnE,aAAO,IAAI,IAAI;IACjB;;EAEF,MAAM,KAAwB;AAE5B,UAAM,EAAE,KAAK,GAAG,MAAM,KAAK,MAAM,IAAG,IAAK;AACzC,UAAM,OAAO,OAAO,QAAQ,WAAW,IAAI,GAAG,IAAI;AAClD,IAAGE,QAAO,IAAI;AACd,UAAM,EAAE,GAAG,UAAU,GAAG,aAAY,IAAK,IAAI,OAAO,IAAM,IAAI;AAC9D,QAAI,aAAa;AAAQ,YAAM,IAAI,EAAE,6CAA6C;AAClF,UAAM,EAAE,GAAG,QAAQ,GAAG,WAAU,IAAK,IAAI,OAAO,GAAM,QAAQ;AAC9D,UAAM,EAAE,GAAG,QAAQ,GAAG,WAAU,IAAK,IAAI,OAAO,GAAM,UAAU;AAChE,QAAI,WAAW;AAAQ,YAAM,IAAI,EAAE,6CAA6C;AAChF,WAAO,EAAE,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,IAAI,OAAO,MAAM,EAAC;EACvD;EACA,WAAW,KAA6B;AACtC,UAAM,EAAE,MAAM,KAAK,MAAM,IAAG,IAAK;AACjC,UAAM,KAAK,IAAI,OAAO,GAAM,IAAI,OAAO,IAAI,CAAC,CAAC;AAC7C,UAAM,KAAK,IAAI,OAAO,GAAM,IAAI,OAAO,IAAI,CAAC,CAAC;AAC7C,UAAM,MAAM,KAAK;AACjB,WAAO,IAAI,OAAO,IAAM,GAAG;EAC7B;;AAKF,IAAMD,OAAM,OAAO,CAAC;AAApB,IAAuBE,OAAM,OAAO,CAAC;AAArC,IAAwCC,OAAM,OAAO,CAAC;AAAtD,IAAyDC,OAAM,OAAO,CAAC;AAAvE,IAA0EC,OAAM,OAAO,CAAC;AAElF,SAAU,kBAAqB,MAAwB;AAC3D,QAAM,QAAQ,kBAAkB,IAAI;AACpC,QAAM,EAAE,GAAE,IAAK;AACf,QAAM,KAAK,MAAM,MAAM,GAAG,MAAM,UAAU;AAE1C,QAAMC,WACJ,MAAM,YACL,CAAC,IAAwB,OAAyB,kBAA0B;AAC3E,UAAM,IAAI,MAAM,SAAQ;AACxB,WAAUC,aAAY,WAAW,KAAK,CAAC,CAAI,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;EACjF;AACF,QAAM,YACJ,MAAM,cACL,CAAC,UAAqB;AAErB,UAAM,OAAO,MAAM,SAAS,CAAC;AAE7B,UAAM,IAAI,GAAG,UAAU,KAAK,SAAS,GAAG,GAAG,KAAK,CAAC;AACjD,UAAM,IAAI,GAAG,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAC5D,WAAO,EAAE,GAAG,EAAC;EACf;AAMF,WAAS,oBAAoB,GAAI;AAC/B,UAAM,EAAE,GAAG,EAAC,IAAK;AACjB,UAAM,KAAK,GAAG,IAAI,CAAC;AACnB,UAAM,KAAK,GAAG,IAAI,IAAI,CAAC;AACvB,WAAO,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EAC3C;AAKA,MAAI,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG,oBAAoB,MAAM,EAAE,CAAC;AACzD,UAAM,IAAI,MAAM,6CAA6C;AAG/D,WAAS,mBAAmBR,MAAW;AACrC,WAAU,QAAQA,MAAKG,MAAK,MAAM,CAAC;EACrC;AAGA,WAAS,uBAAuB,KAAY;AAC1C,UAAM,EAAE,0BAA0B,SAAS,aAAa,gBAAgB,GAAG,EAAC,IAAK;AACjF,QAAI,WAAW,OAAO,QAAQ,UAAU;AACtC,UAAO,QAAQ,GAAG;AAAG,cAAS,WAAW,GAAG;AAE5C,UAAI,OAAO,QAAQ,YAAY,CAAC,QAAQ,SAAS,IAAI,MAAM;AACzD,cAAM,IAAI,MAAM,qBAAqB;AACvC,YAAM,IAAI,SAAS,cAAc,GAAG,GAAG;IACzC;AACA,QAAIH;AACJ,QAAI;AACF,MAAAA,OACE,OAAO,QAAQ,WACX,MACG,gBAAgB,YAAY,eAAe,KAAK,WAAW,CAAC;IACvE,SAAS,OAAO;AACd,YAAM,IAAI,MACR,0CAA0C,cAAc,iBAAiB,OAAO,GAAG;IAEvF;AACA,QAAI;AAAgB,MAAAA,OAAM,IAAIA,MAAK,CAAC;AACpC,IAAG,SAAS,eAAeA,MAAKG,MAAK,CAAC;AACtC,WAAOH;EACT;AAEA,WAAS,eAAe,OAAc;AACpC,QAAI,EAAE,iBAAiBS;AAAQ,YAAM,IAAI,MAAM,0BAA0B;EAC3E;AAOA,QAAM,eAAe,SAAS,CAAC,GAAU,OAA0B;AACjE,UAAM,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAC,IAAK;AAEhC,QAAI,GAAG,IAAI,GAAG,GAAG,GAAG;AAAG,aAAO,EAAE,GAAG,EAAC;AACpC,UAAM,MAAM,EAAE,IAAG;AAGjB,QAAI,MAAM;AAAM,WAAK,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;AAC5C,UAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,UAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,UAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,QAAI;AAAK,aAAO,EAAE,GAAG,GAAG,MAAM,GAAG,GAAG,KAAI;AACxC,QAAI,CAAC,GAAG,IAAI,IAAI,GAAG,GAAG;AAAG,YAAM,IAAI,MAAM,kBAAkB;AAC3D,WAAO,EAAE,GAAG,IAAI,GAAG,GAAE;EACvB,CAAC;AAGD,QAAM,kBAAkB,SAAS,CAAC,MAAY;AAC5C,QAAI,EAAE,IAAG,GAAI;AAIX,UAAI,MAAM,sBAAsB,CAAC,GAAG,IAAI,EAAE,EAAE;AAAG;AAC/C,YAAM,IAAI,MAAM,iBAAiB;IACnC;AAEA,UAAM,EAAE,GAAG,EAAC,IAAK,EAAE,SAAQ;AAE3B,QAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AAAG,YAAM,IAAI,MAAM,0BAA0B;AAChF,UAAM,OAAO,GAAG,IAAI,CAAC;AACrB,UAAM,QAAQ,oBAAoB,CAAC;AACnC,QAAI,CAAC,GAAG,IAAI,MAAM,KAAK;AAAG,YAAM,IAAI,MAAM,mCAAmC;AAC7E,QAAI,CAAC,EAAE,cAAa;AAAI,YAAM,IAAI,MAAM,wCAAwC;AAChF,WAAO;EACT,CAAC;EAOD,MAAMA,OAAK;IAIT,YACW,IACA,IACA,IAAK;AAFL,WAAA,KAAA;AACA,WAAA,KAAA;AACA,WAAA,KAAA;AAET,UAAI,MAAM,QAAQ,CAAC,GAAG,QAAQ,EAAE;AAAG,cAAM,IAAI,MAAM,YAAY;AAC/D,UAAI,MAAM,QAAQ,CAAC,GAAG,QAAQ,EAAE;AAAG,cAAM,IAAI,MAAM,YAAY;AAC/D,UAAI,MAAM,QAAQ,CAAC,GAAG,QAAQ,EAAE;AAAG,cAAM,IAAI,MAAM,YAAY;AAC/D,aAAO,OAAO,IAAI;IACpB;;;IAIA,OAAO,WAAW,GAAiB;AACjC,YAAM,EAAE,GAAG,EAAC,IAAK,KAAK,CAAA;AACtB,UAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AAAG,cAAM,IAAI,MAAM,sBAAsB;AAClF,UAAI,aAAaA;AAAO,cAAM,IAAI,MAAM,8BAA8B;AACtE,YAAM,MAAM,CAAC,MAAS,GAAG,IAAI,GAAG,GAAG,IAAI;AAEvC,UAAI,IAAI,CAAC,KAAK,IAAI,CAAC;AAAG,eAAOA,OAAM;AACnC,aAAO,IAAIA,OAAM,GAAG,GAAG,GAAG,GAAG;IAC/B;IAEA,IAAI,IAAC;AACH,aAAO,KAAK,SAAQ,EAAG;IACzB;IACA,IAAI,IAAC;AACH,aAAO,KAAK,SAAQ,EAAG;IACzB;;;;;;;IAQA,OAAO,WAAW,QAAe;AAC/B,YAAM,QAAQ,GAAG,YAAY,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;AACpD,aAAO,OAAO,IAAI,CAAC,GAAG,MAAM,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC,EAAE,IAAIA,OAAM,UAAU;IACxE;;;;;IAMA,OAAO,QAAQ,KAAQ;AACrB,YAAM,IAAIA,OAAM,WAAW,UAAU,YAAY,YAAY,GAAG,CAAC,CAAC;AAClE,QAAE,eAAc;AAChB,aAAO;IACT;;IAGA,OAAO,eAAe,YAAmB;AACvC,aAAOA,OAAM,KAAK,SAAS,uBAAuB,UAAU,CAAC;IAC/D;;IAGA,OAAO,IAAI,QAAiB,SAAiB;AAC3C,aAAO,UAAUA,QAAO,IAAI,QAAQ,OAAO;IAC7C;;IAGA,eAAe,YAAkB;AAC/B,WAAK,cAAc,MAAM,UAAU;IACrC;;IAGA,iBAAc;AACZ,sBAAgB,IAAI;IACtB;IAEA,WAAQ;AACN,YAAM,EAAE,EAAC,IAAK,KAAK,SAAQ;AAC3B,UAAI,GAAG;AAAO,eAAO,CAAC,GAAG,MAAM,CAAC;AAChC,YAAM,IAAI,MAAM,6BAA6B;IAC/C;;;;IAKA,OAAO,OAAY;AACjB,qBAAe,KAAK;AACpB,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,YAAM,KAAK,GAAG,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;AAChD,YAAM,KAAK,GAAG,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;AAChD,aAAO,MAAM;IACf;;;;IAKA,SAAM;AACJ,aAAO,IAAIA,OAAM,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG,KAAK,EAAE;IACpD;;;;;IAMA,SAAM;AACJ,YAAM,EAAE,GAAG,EAAC,IAAK;AACjB,YAAM,KAAK,GAAG,IAAI,GAAGJ,IAAG;AACxB,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,UAAI,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AACxC,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,aAAO,IAAII,OAAM,IAAI,IAAI,EAAE;IAC7B;;;;;IAMA,IAAI,OAAY;AACd,qBAAe,KAAK;AACpB,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,YAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AACnC,UAAI,KAAK,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AACxC,YAAM,IAAI,MAAM;AAChB,YAAM,KAAK,GAAG,IAAI,MAAM,GAAGJ,IAAG;AAC9B,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,UAAI,KAAK,GAAG,IAAI,IAAI,EAAE;AACtB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,GAAG,EAAE;AACjB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,WAAK,GAAG,IAAI,IAAI,EAAE;AAClB,aAAO,IAAII,OAAM,IAAI,IAAI,EAAE;IAC7B;IAEA,SAAS,OAAY;AACnB,aAAO,KAAK,IAAI,MAAM,OAAM,CAAE;IAChC;IAEA,MAAG;AACD,aAAO,KAAK,OAAOA,OAAM,IAAI;IAC/B;IACQ,KAAK,GAAS;AACpB,aAAO,KAAK,WAAW,MAAM,GAAGA,OAAM,UAAU;IAClD;;;;;;IAOA,eAAe,IAAU;AACvB,YAAM,EAAE,MAAM,GAAG,EAAC,IAAK;AACvB,MAAG,SAAS,UAAU,IAAIR,MAAK,CAAC;AAChC,YAAM,IAAIQ,OAAM;AAChB,UAAI,OAAOR;AAAK,eAAO;AACvB,UAAI,KAAK,IAAG,KAAM,OAAOE;AAAK,eAAO;AAGrC,UAAI,CAAC,QAAQ,KAAK,eAAe,IAAI;AACnC,eAAO,KAAK,iBAAiB,MAAM,IAAIM,OAAM,UAAU;AAGzD,UAAI,EAAE,OAAO,IAAI,OAAO,GAAE,IAAK,KAAK,YAAY,EAAE;AAClD,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,IAAW;AACf,aAAO,KAAKR,QAAO,KAAKA,MAAK;AAC3B,YAAI,KAAKE;AAAK,gBAAM,IAAI,IAAI,CAAC;AAC7B,YAAI,KAAKA;AAAK,gBAAM,IAAI,IAAI,CAAC;AAC7B,YAAI,EAAE,OAAM;AACZ,eAAOA;AACP,eAAOA;MACT;AACA,UAAI;AAAO,cAAM,IAAI,OAAM;AAC3B,UAAI;AAAO,cAAM,IAAI,OAAM;AAC3B,YAAM,IAAIM,OAAM,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE;AACzD,aAAO,IAAI,IAAI,GAAG;IACpB;;;;;;;;;;IAWA,SAAS,QAAc;AACrB,YAAM,EAAE,MAAM,GAAG,EAAC,IAAK;AACvB,MAAG,SAAS,UAAU,QAAQN,MAAK,CAAC;AACpC,UAAI,OAAc;AAClB,UAAI,MAAM;AACR,cAAM,EAAE,OAAO,IAAI,OAAO,GAAE,IAAK,KAAK,YAAY,MAAM;AACxD,YAAI,EAAE,GAAG,KAAK,GAAG,IAAG,IAAK,KAAK,KAAK,EAAE;AACrC,YAAI,EAAE,GAAG,KAAK,GAAG,IAAG,IAAK,KAAK,KAAK,EAAE;AACrC,cAAM,KAAK,gBAAgB,OAAO,GAAG;AACrC,cAAM,KAAK,gBAAgB,OAAO,GAAG;AACrC,cAAM,IAAIM,OAAM,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE;AACzD,gBAAQ,IAAI,IAAI,GAAG;AACnB,eAAO,IAAI,IAAI,GAAG;MACpB,OAAO;AACL,cAAM,EAAE,GAAG,EAAC,IAAK,KAAK,KAAK,MAAM;AACjC,gBAAQ;AACR,eAAO;MACT;AAEA,aAAOA,OAAM,WAAW,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;IAC1C;;;;;;;IAQA,qBAAqB,GAAU,GAAW,GAAS;AACjD,YAAM,IAAIA,OAAM;AAChB,YAAM,MAAM,CACV,GACAC,OACIA,OAAMT,QAAOS,OAAMP,QAAO,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,eAAeO,EAAC,IAAI,EAAE,SAASA,EAAC;AACjF,YAAM,MAAM,IAAI,MAAM,CAAC,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACtC,aAAO,IAAI,IAAG,IAAK,SAAY;IACjC;;;;IAKA,SAAS,IAAM;AACb,aAAO,aAAa,MAAM,EAAE;IAC9B;IACA,gBAAa;AACX,YAAM,EAAE,GAAG,UAAU,cAAa,IAAK;AACvC,UAAI,aAAaP;AAAK,eAAO;AAC7B,UAAI;AAAe,eAAO,cAAcM,QAAO,IAAI;AACnD,YAAM,IAAI,MAAM,8DAA8D;IAChF;IACA,gBAAa;AACX,YAAM,EAAE,GAAG,UAAU,cAAa,IAAK;AACvC,UAAI,aAAaN;AAAK,eAAO;AAC7B,UAAI;AAAe,eAAO,cAAcM,QAAO,IAAI;AACnD,aAAO,KAAK,eAAe,MAAM,CAAC;IACpC;IAEA,WAAW,eAAe,MAAI;AAC5B,YAAM,gBAAgB,YAAY;AAClC,WAAK,eAAc;AACnB,aAAOF,SAAQE,QAAO,MAAM,YAAY;IAC1C;IAEA,MAAM,eAAe,MAAI;AACvB,YAAM,gBAAgB,YAAY;AAClC,aAAU,WAAW,KAAK,WAAW,YAAY,CAAC;IACpD;;AA5TgB,EAAAA,OAAA,OAAO,IAAIA,OAAM,MAAM,IAAI,MAAM,IAAI,GAAG,GAAG;AAC3C,EAAAA,OAAA,OAAO,IAAIA,OAAM,GAAG,MAAM,GAAG,KAAK,GAAG,IAAI;AA6T3D,QAAM,QAAQ,MAAM;AACpB,QAAM,OAAO,KAAKA,QAAO,MAAM,OAAO,KAAK,KAAK,QAAQ,CAAC,IAAI,KAAK;AAElE,SAAO;IACL;IACA,iBAAiBA;IACjB;IACA;IACA;;AAEJ;AAwCA,SAAS,aACP,OAAgB;AAEhB,QAAM,OAAO,cAAc,KAAK;AAChC,EAAG,eACD,MACA;IACE,MAAM;IACN,MAAM;IACN,aAAa;KAEf;IACE,UAAU;IACV,eAAe;IACf,MAAM;GACP;AAEH,SAAO,OAAO,OAAO,EAAE,MAAM,MAAM,GAAG,KAAI,CAAW;AACvD;AAyBM,SAAU,YAAY,UAAmB;AAC7C,QAAM,QAAQ,aAAa,QAAQ;AACnC,QAAM,EAAE,IAAI,GAAG,YAAW,IAAK;AAC/B,QAAM,gBAAgB,GAAG,QAAQ;AACjC,QAAM,kBAAkB,IAAI,GAAG,QAAQ;AAEvC,WAASE,MAAK,GAAS;AACrB,WAAO,IAAI,GAAG,WAAW;EAC3B;AACA,WAAS,KAAK,GAAS;AACrB,WAAO,OAAO,GAAG,WAAW;EAC9B;AAEA,QAAM,EACJ,iBAAiBF,QACjB,wBACA,qBACA,mBAAkB,IAChB,kBAAkB;IACpB,GAAG;IACH,QAAQ,IAAI,OAAO,cAAqB;AACtC,YAAM,IAAI,MAAM,SAAQ;AACxB,YAAM,IAAI,GAAG,QAAQ,EAAE,CAAC;AACxB,YAAM,MAASD;AACf,YAAM,gBAAgB,YAAY;AAClC,UAAI,cAAc;AAChB,eAAO,IAAI,WAAW,KAAK,CAAC,MAAM,SAAQ,IAAK,IAAO,CAAI,CAAC,GAAG,CAAC;MACjE,OAAO;AACL,eAAO,IAAI,WAAW,KAAK,CAAC,CAAI,CAAC,GAAG,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;MACxD;IACF;IACA,UAAU,OAAiB;AACzB,YAAM,MAAM,MAAM;AAClB,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,OAAO,MAAM,SAAS,CAAC;AAE7B,UAAI,QAAQ,kBAAkB,SAAS,KAAQ,SAAS,IAAO;AAC7D,cAAM,IAAO,gBAAgB,IAAI;AACjC,YAAI,CAAI,QAAQ,GAAGL,MAAK,GAAG,KAAK;AAAG,gBAAM,IAAI,MAAM,uBAAuB;AAC1E,cAAM,KAAK,oBAAoB,CAAC;AAChC,YAAI;AACJ,YAAI;AACF,cAAI,GAAG,KAAK,EAAE;QAChB,SAAS,WAAW;AAClB,gBAAM,SAAS,qBAAqB,QAAQ,OAAO,UAAU,UAAU;AACvE,gBAAM,IAAI,MAAM,0BAA0B,MAAM;QAClD;AACA,cAAM,UAAU,IAAIA,UAASA;AAE7B,cAAM,aAAa,OAAO,OAAO;AACjC,YAAI,cAAc;AAAQ,cAAI,GAAG,IAAI,CAAC;AACtC,eAAO,EAAE,GAAG,EAAC;MACf,WAAW,QAAQ,mBAAmB,SAAS,GAAM;AACnD,cAAM,IAAI,GAAG,UAAU,KAAK,SAAS,GAAG,GAAG,KAAK,CAAC;AACjD,cAAM,IAAI,GAAG,UAAU,KAAK,SAAS,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;AAC5D,eAAO,EAAE,GAAG,EAAC;MACf,OAAO;AACL,cAAM,KAAK;AACX,cAAM,KAAK;AACX,cAAM,IAAI,MACR,uCAAuC,KAAK,uBAAuB,KAAK,WAAW,GAAG;MAE1F;IACF;GACD;AACD,QAAM,gBAAgB,CAACH,SAClB,WAAc,gBAAgBA,MAAK,MAAM,WAAW,CAAC;AAE1D,WAAS,sBAAsB,QAAc;AAC3C,UAAM,OAAO,eAAeG;AAC5B,WAAO,SAAS;EAClB;AAEA,WAAS,WAAW,GAAS;AAC3B,WAAO,sBAAsB,CAAC,IAAIQ,MAAK,CAAC,CAAC,IAAI;EAC/C;AAEA,QAAM,SAAS,CAAC,GAAe,MAAc,OAAkB,gBAAgB,EAAE,MAAM,MAAM,EAAE,CAAC;EAKhG,MAAM,UAAS;IACb,YACW,GACA,GACA,UAAiB;AAFjB,WAAA,IAAA;AACA,WAAA,IAAA;AACA,WAAA,WAAA;AAET,WAAK,eAAc;IACrB;;IAGA,OAAO,YAAY,KAAQ;AACzB,YAAM,IAAI,MAAM;AAChB,YAAM,YAAY,oBAAoB,KAAK,IAAI,CAAC;AAChD,aAAO,IAAI,UAAU,OAAO,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,GAAG,IAAI,CAAC,CAAC;IAC/D;;;IAIA,OAAO,QAAQ,KAAQ;AACrB,YAAM,EAAE,GAAG,EAAC,IAAK,IAAI,MAAM,YAAY,OAAO,GAAG,CAAC;AAClD,aAAO,IAAI,UAAU,GAAG,CAAC;IAC3B;IAEA,iBAAc;AACZ,MAAG,SAAS,KAAK,KAAK,GAAGR,MAAK,WAAW;AACzC,MAAG,SAAS,KAAK,KAAK,GAAGA,MAAK,WAAW;IAC3C;IAEA,eAAe,UAAgB;AAC7B,aAAO,IAAI,UAAU,KAAK,GAAG,KAAK,GAAG,QAAQ;IAC/C;IAEA,iBAAiB,SAAY;AAC3B,YAAM,EAAE,GAAG,GAAG,UAAU,IAAG,IAAK;AAChC,YAAM,IAAI,cAAc,YAAY,WAAW,OAAO,CAAC;AACvD,UAAI,OAAO,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,SAAS,GAAG;AAAG,cAAM,IAAI,MAAM,qBAAqB;AACrF,YAAM,OAAO,QAAQ,KAAK,QAAQ,IAAI,IAAI,MAAM,IAAI;AACpD,UAAI,QAAQ,GAAG;AAAO,cAAM,IAAI,MAAM,4BAA4B;AAClE,YAAM,UAAU,MAAM,OAAO,IAAI,OAAO;AACxC,YAAM,IAAIM,OAAM,QAAQ,SAAS,cAAc,IAAI,CAAC;AACpD,YAAM,KAAK,KAAK,IAAI;AACpB,YAAM,KAAKE,MAAK,CAAC,IAAI,EAAE;AACvB,YAAM,KAAKA,MAAK,IAAI,EAAE;AACtB,YAAM,IAAIF,OAAM,KAAK,qBAAqB,GAAG,IAAI,EAAE;AACnD,UAAI,CAAC;AAAG,cAAM,IAAI,MAAM,mBAAmB;AAC3C,QAAE,eAAc;AAChB,aAAO;IACT;;IAGA,WAAQ;AACN,aAAO,sBAAsB,KAAK,CAAC;IACrC;IAEA,aAAU;AACR,aAAO,KAAK,SAAQ,IAAK,IAAI,UAAU,KAAK,GAAGE,MAAK,CAAC,KAAK,CAAC,GAAG,KAAK,QAAQ,IAAI;IACjF;;IAGA,gBAAa;AACX,aAAU,WAAW,KAAK,SAAQ,CAAE;IACtC;IACA,WAAQ;AACN,aAAO,IAAI,WAAW,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAC,CAAE;IAChD;;IAGA,oBAAiB;AACf,aAAU,WAAW,KAAK,aAAY,CAAE;IAC1C;IACA,eAAY;AACV,aAAO,cAAc,KAAK,CAAC,IAAI,cAAc,KAAK,CAAC;IACrD;;AAIF,QAAM,QAAQ;IACZ,kBAAkB,YAAmB;AACnC,UAAI;AACF,+BAAuB,UAAU;AACjC,eAAO;MACT,SAAS,OAAO;AACd,eAAO;MACT;IACF;IACA;;;;;IAMA,kBAAkB,MAAiB;AACjC,YAAM,SAAS,iBAAiB,MAAM,CAAC;AACvC,aAAO,eAAe,MAAM,YAAY,MAAM,GAAG,MAAM,CAAC;IAC1D;;;;;;;;;IAUA,WAAW,aAAa,GAAG,QAAQF,OAAM,MAAI;AAC3C,YAAM,eAAe,UAAU;AAC/B,YAAM,SAAS,OAAO,CAAC,CAAC;AACxB,aAAO;IACT;;AASF,WAAS,aAAa,YAAqB,eAAe,MAAI;AAC5D,WAAOA,OAAM,eAAe,UAAU,EAAE,WAAW,YAAY;EACjE;AAKA,WAAS,UAAU,MAAsB;AACvC,UAAM,MAAS,QAAQ,IAAI;AAC3B,UAAM,MAAM,OAAO,SAAS;AAC5B,UAAM,OAAO,OAAO,QAAS,KAAa;AAC1C,QAAI;AAAK,aAAO,QAAQ,iBAAiB,QAAQ;AACjD,QAAI;AAAK,aAAO,QAAQ,IAAI,iBAAiB,QAAQ,IAAI;AACzD,QAAI,gBAAgBA;AAAO,aAAO;AAClC,WAAO;EACT;AAYA,WAAS,gBAAgB,UAAmB,SAAc,eAAe,MAAI;AAC3E,QAAI,UAAU,QAAQ;AAAG,YAAM,IAAI,MAAM,+BAA+B;AACxE,QAAI,CAAC,UAAU,OAAO;AAAG,YAAM,IAAI,MAAM,+BAA+B;AACxE,UAAM,IAAIA,OAAM,QAAQ,OAAO;AAC/B,WAAO,EAAE,SAAS,uBAAuB,QAAQ,CAAC,EAAE,WAAW,YAAY;EAC7E;AAMA,QAAM,WACJ,MAAM,YACN,SAAU,OAAiB;AAEzB,QAAI,MAAM,SAAS;AAAM,YAAM,IAAI,MAAM,oBAAoB;AAG7D,UAAMT,OAAS,gBAAgB,KAAK;AACpC,UAAM,QAAQ,MAAM,SAAS,IAAI,MAAM;AACvC,WAAO,QAAQ,IAAIA,QAAO,OAAO,KAAK,IAAIA;EAC5C;AACF,QAAM,gBACJ,MAAM,iBACN,SAAU,OAAiB;AACzB,WAAOW,MAAK,SAAS,KAAK,CAAC;EAC7B;AAEF,QAAM,aAAgB,QAAQ,MAAM,UAAU;AAI9C,WAAS,WAAWX,MAAW;AAC7B,IAAG,SAAS,aAAa,MAAM,YAAYA,MAAKC,MAAK,UAAU;AAE/D,WAAU,gBAAgBD,MAAK,MAAM,WAAW;EAClD;AAOA,WAAS,QAAQ,SAAc,YAAqB,OAAO,gBAAc;AACvE,QAAI,CAAC,aAAa,WAAW,EAAE,KAAK,CAAC,MAAM,KAAK,IAAI;AAClD,YAAM,IAAI,MAAM,qCAAqC;AACvD,UAAM,EAAE,MAAM,aAAAY,aAAW,IAAK;AAC9B,QAAI,EAAE,MAAM,SAAS,cAAc,IAAG,IAAK;AAC3C,QAAI,QAAQ;AAAM,aAAO;AACzB,cAAU,YAAY,WAAW,OAAO;AACxC,uBAAmB,IAAI;AACvB,QAAI;AAAS,gBAAU,YAAY,qBAAqB,KAAK,OAAO,CAAC;AAKrE,UAAM,QAAQ,cAAc,OAAO;AACnC,UAAM,IAAI,uBAAuB,UAAU;AAC3C,UAAM,WAAW,CAAC,WAAW,CAAC,GAAG,WAAW,KAAK,CAAC;AAElD,QAAI,OAAO,QAAQ,QAAQ,OAAO;AAEhC,YAAM,IAAI,QAAQ,OAAOA,aAAY,GAAG,KAAK,IAAI;AACjD,eAAS,KAAK,YAAY,gBAAgB,CAAC,CAAC;IAC9C;AACA,UAAM,OAAUJ,aAAY,GAAG,QAAQ;AACvC,UAAM,IAAI;AAEV,aAAS,MAAM,QAAkB;AAE/B,YAAM,IAAI,SAAS,MAAM;AACzB,UAAI,CAAC,mBAAmB,CAAC;AAAG;AAC5B,YAAM,KAAK,KAAK,CAAC;AACjB,YAAM,IAAIC,OAAM,KAAK,SAAS,CAAC,EAAE,SAAQ;AACzC,YAAM,IAAIE,MAAK,EAAE,CAAC;AAClB,UAAI,MAAMV;AAAK;AAIf,YAAM,IAAIU,MAAK,KAAKA,MAAK,IAAI,IAAI,CAAC,CAAC;AACnC,UAAI,MAAMV;AAAK;AACf,UAAI,YAAY,EAAE,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE,IAAIE,IAAG;AACrD,UAAI,QAAQ;AACZ,UAAI,QAAQ,sBAAsB,CAAC,GAAG;AACpC,gBAAQ,WAAW,CAAC;AACpB,oBAAY;MACd;AACA,aAAO,IAAI,UAAU,GAAG,OAAO,QAAQ;IACzC;AACA,WAAO,EAAE,MAAM,MAAK;EACtB;AACA,QAAM,iBAA2B,EAAE,MAAM,MAAM,MAAM,SAAS,MAAK;AACnE,QAAM,iBAA0B,EAAE,MAAM,MAAM,MAAM,SAAS,MAAK;AAelE,WAAS,KAAK,SAAc,SAAkB,OAAO,gBAAc;AACjE,UAAM,EAAE,MAAM,MAAK,IAAK,QAAQ,SAAS,SAAS,IAAI;AACtD,UAAM,IAAI;AACV,UAAM,OAAU,eAAmC,EAAE,KAAK,WAAW,EAAE,aAAa,EAAE,IAAI;AAC1F,WAAO,KAAK,MAAM,KAAK;EACzB;AAGA,EAAAM,OAAM,KAAK,eAAe,CAAC;AAgB3B,WAAS,OACP,WACA,SACA,WACA,OAAO,gBAAc;AAErB,UAAM,KAAK;AACX,cAAU,YAAY,WAAW,OAAO;AACxC,gBAAY,YAAY,aAAa,SAAS;AAC9C,UAAM,EAAE,MAAM,SAAS,OAAM,IAAK;AAGlC,uBAAmB,IAAI;AACvB,QAAI,YAAY;AAAM,YAAM,IAAI,MAAM,oCAAoC;AAC1E,QAAI,WAAW,UAAa,WAAW,aAAa,WAAW;AAC7D,YAAM,IAAI,MAAM,+BAA+B;AACjD,UAAM,QAAQ,OAAO,OAAO,YAAe,QAAQ,EAAE;AACrD,UAAM,QACJ,CAAC,SACD,CAAC,UACD,OAAO,OAAO,YACd,OAAO,QACP,OAAO,GAAG,MAAM,YAChB,OAAO,GAAG,MAAM;AAClB,QAAI,CAAC,SAAS,CAAC;AACb,YAAM,IAAI,MAAM,0EAA0E;AAE5F,QAAI,OAA8B;AAClC,QAAI;AACJ,QAAI;AACF,UAAI;AAAO,eAAO,IAAI,UAAU,GAAG,GAAG,GAAG,CAAC;AAC1C,UAAI,OAAO;AAGT,YAAI;AACF,cAAI,WAAW;AAAW,mBAAO,UAAU,QAAQ,EAAE;QACvD,SAAS,UAAU;AACjB,cAAI,EAAE,oBAAoB,IAAI;AAAM,kBAAM;QAC5C;AACA,YAAI,CAAC,QAAQ,WAAW;AAAO,iBAAO,UAAU,YAAY,EAAE;MAChE;AACA,UAAIA,OAAM,QAAQ,SAAS;IAC7B,SAAS,OAAO;AACd,aAAO;IACT;AACA,QAAI,CAAC;AAAM,aAAO;AAClB,QAAI,QAAQ,KAAK,SAAQ;AAAI,aAAO;AACpC,QAAI;AAAS,gBAAU,MAAM,KAAK,OAAO;AACzC,UAAM,EAAE,GAAG,EAAC,IAAK;AACjB,UAAM,IAAI,cAAc,OAAO;AAC/B,UAAM,KAAK,KAAK,CAAC;AACjB,UAAM,KAAKE,MAAK,IAAI,EAAE;AACtB,UAAM,KAAKA,MAAK,IAAI,EAAE;AACtB,UAAM,IAAIF,OAAM,KAAK,qBAAqB,GAAG,IAAI,EAAE,GAAG,SAAQ;AAC9D,QAAI,CAAC;AAAG,aAAO;AACf,UAAM,IAAIE,MAAK,EAAE,CAAC;AAClB,WAAO,MAAM;EACf;AACA,SAAO;IACL;IACA;IACA;IACA;IACA;IACA,iBAAiBF;IACjB;IACA;;AAEJ;AAWM,SAAU,eACd,IACA,GAAI;AAGJ,QAAM,IAAI,GAAG;AACb,MAAI,IAAIR;AACR,WAAS,IAAI,IAAIE,MAAK,IAAIC,SAAQH,MAAK,KAAKG;AAAK,SAAKD;AACtD,QAAM,KAAK;AAGX,QAAM,eAAeC,QAAQ,KAAKD,OAAMA;AACxC,QAAM,aAAa,eAAeC;AAClC,QAAM,MAAM,IAAID,QAAO;AACvB,QAAM,MAAM,KAAKA,QAAOC;AACxB,QAAM,KAAK,aAAaD;AACxB,QAAM,KAAK;AACX,QAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACvB,QAAM,KAAK,GAAG,IAAI,IAAI,KAAKA,QAAOC,IAAG;AACrC,MAAI,YAAY,CAAC,GAAM,MAAwC;AAC7D,QAAI,MAAM;AACV,QAAI,MAAM,GAAG,IAAI,GAAG,EAAE;AACtB,QAAI,MAAM,GAAG,IAAI,GAAG;AACpB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,QAAI,MAAM,GAAG,IAAI,GAAG,GAAG;AACvB,UAAM,GAAG,IAAI,KAAK,EAAE;AACpB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,UAAM,GAAG,IAAI,KAAK,CAAC;AACnB,QAAI,MAAM,GAAG,IAAI,KAAK,GAAG;AACzB,UAAM,GAAG,IAAI,KAAK,EAAE;AACpB,QAAI,OAAO,GAAG,IAAI,KAAK,GAAG,GAAG;AAC7B,UAAM,GAAG,IAAI,KAAK,EAAE;AACpB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,KAAK,KAAK,KAAK,IAAI;AAC5B,UAAM,GAAG,KAAK,KAAK,KAAK,IAAI;AAE5B,aAAS,IAAI,IAAI,IAAID,MAAK,KAAK;AAC7B,UAAIU,OAAM,IAAIT;AACd,MAAAS,OAAMT,QAAQS,OAAMV;AACpB,UAAI,OAAO,GAAG,IAAI,KAAKU,IAAG;AAC1B,YAAM,KAAK,GAAG,IAAI,MAAM,GAAG,GAAG;AAC9B,YAAM,GAAG,IAAI,KAAK,GAAG;AACrB,YAAM,GAAG,IAAI,KAAK,GAAG;AACrB,aAAO,GAAG,IAAI,KAAK,GAAG;AACtB,YAAM,GAAG,KAAK,KAAK,KAAK,EAAE;AAC1B,YAAM,GAAG,KAAK,MAAM,KAAK,EAAE;IAC7B;AACA,WAAO,EAAE,SAAS,MAAM,OAAO,IAAG;EACpC;AACA,MAAI,GAAG,QAAQP,SAAQD,MAAK;AAE1B,UAAMS,OAAM,GAAG,QAAQT,QAAOC;AAC9B,UAAMS,MAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;AAC5B,gBAAY,CAAC,GAAM,MAAQ;AACzB,UAAI,MAAM,GAAG,IAAI,CAAC;AAClB,YAAM,MAAM,GAAG,IAAI,GAAG,CAAC;AACvB,YAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAI,KAAK,GAAG,IAAI,KAAKD,GAAE;AACvB,WAAK,GAAG,IAAI,IAAI,GAAG;AACnB,YAAM,KAAK,GAAG,IAAI,IAAIC,GAAE;AACxB,YAAM,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE,GAAG,CAAC;AAChC,YAAM,OAAO,GAAG,IAAI,KAAK,CAAC;AAC1B,UAAI,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI;AAC5B,aAAO,EAAE,SAAS,MAAM,OAAO,EAAC;IAClC;EACF;AAGA,SAAO;AACT;AAKM,SAAU,oBACd,IACA,MAIC;AAED,gBAAc,EAAE;AAChB,MAAI,CAAC,GAAG,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,KAAK,CAAC;AAClE,UAAM,IAAI,MAAM,mCAAmC;AACrD,QAAM,YAAY,eAAe,IAAI,KAAK,CAAC;AAC3C,MAAI,CAAC,GAAG;AAAO,UAAM,IAAI,MAAM,8BAA8B;AAG7D,SAAO,CAAC,MAAwB;AAE9B,QAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACrC,UAAM,GAAG,IAAI,CAAC;AACd,UAAM,GAAG,IAAI,KAAK,KAAK,CAAC;AACxB,UAAM,GAAG,IAAI,GAAG;AAChB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,GAAG,GAAG;AACxB,UAAM,GAAG,IAAI,KAAK,KAAK,CAAC;AACxB,UAAM,GAAG,KAAK,KAAK,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,KAAK,GAAG,IAAI,CAAC;AACxD,UAAM,GAAG,IAAI,KAAK,KAAK,CAAC;AACxB,UAAM,GAAG,IAAI,GAAG;AAChB,UAAM,GAAG,IAAI,GAAG;AAChB,UAAM,GAAG,IAAI,KAAK,KAAK,CAAC;AACxB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,UAAM,GAAG,IAAI,KAAK,KAAK,CAAC;AACxB,UAAM,GAAG,IAAI,KAAK,GAAG;AACrB,QAAI,GAAG,IAAI,KAAK,GAAG;AACnB,UAAM,EAAE,SAAS,MAAK,IAAK,UAAU,KAAK,GAAG;AAC7C,QAAI,GAAG,IAAI,KAAK,CAAC;AACjB,QAAI,GAAG,IAAI,GAAG,KAAK;AACnB,QAAI,GAAG,KAAK,GAAG,KAAK,OAAO;AAC3B,QAAI,GAAG,KAAK,GAAG,OAAO,OAAO;AAC7B,UAAM,KAAK,GAAG,MAAO,CAAC,MAAM,GAAG,MAAO,CAAC;AACvC,QAAI,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE;AAC5B,QAAI,GAAG,IAAI,GAAG,GAAG;AACjB,WAAO,EAAE,GAAG,EAAC;EACf;AACF;;;ACn1CM,SAAU,QAAQ,MAAW;AAKjC,SAAO;IACL;IACA,MAAM,CAAC,QAAoB,SAAuB,KAAK,MAAM,KAAK,YAAY,GAAG,IAAI,CAAC;IACtF;;AAEJ;AAKM,SAAU,YAAY,UAAoB,SAAc;AAC5D,QAAM,SAAS,CAAC,SAAyB,YAAY,EAAE,GAAG,UAAU,GAAG,QAAQ,IAAI,EAAC,CAAE;AACtF,SAAO,EAAE,GAAG,OAAO,OAAO,GAAG,OAAM;AACrC;;;ACEA,IAAM,QAAQ;AAGd,SAAS,MAAM,OAAe,QAAc;AAC1C,OAAK,KAAK;AACV,OAAK,MAAM;AACX,MAAI,QAAQ,KAAK,SAAS,KAAM,IAAI;AAAS,UAAM,IAAI,MAAM,0BAA0B,KAAK;AAC5F,QAAM,MAAM,MAAM,KAAK,EAAE,OAAM,CAAE,EAAE,KAAK,CAAC;AACzC,WAAS,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,QAAI,CAAC,IAAI,QAAQ;AACjB,eAAW;EACb;AACA,SAAO,IAAI,WAAW,GAAG;AAC3B;AAEA,SAAS,OAAO,GAAe,GAAa;AAC1C,QAAM,MAAM,IAAI,WAAW,EAAE,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;EACrB;AACA,SAAO;AACT;AAEA,SAAS,KAAK,MAAa;AACzB,MAAI,CAAC,OAAO,cAAc,IAAI;AAAG,UAAM,IAAI,MAAM,iBAAiB;AACpE;AAMM,SAAU,mBACd,KACA,KACA,YACA,GAAQ;AAER,EAAAC,QAAO,GAAG;AACV,EAAAA,QAAO,GAAG;AACV,OAAK,UAAU;AAEf,MAAI,IAAI,SAAS;AAAK,UAAM,EAAEC,aAAY,YAAY,mBAAmB,GAAG,GAAG,CAAC;AAChF,QAAM,EAAE,WAAW,YAAY,UAAU,WAAU,IAAK;AACxD,QAAM,MAAM,KAAK,KAAK,aAAa,UAAU;AAC7C,MAAI,aAAa,SAAS,MAAM;AAAK,UAAM,IAAI,MAAM,wCAAwC;AAC7F,QAAM,YAAYA,aAAY,KAAK,MAAM,IAAI,QAAQ,CAAC,CAAC;AACvD,QAAM,QAAQ,MAAM,GAAG,UAAU;AACjC,QAAM,YAAY,MAAM,YAAY,CAAC;AACrC,QAAM,IAAI,IAAI,MAAkB,GAAG;AACnC,QAAM,MAAM,EAAEA,aAAY,OAAO,KAAK,WAAW,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;AACxE,IAAE,CAAC,IAAI,EAAEA,aAAY,KAAK,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;AACjD,WAAS,IAAI,GAAG,KAAK,KAAK,KAAK;AAC7B,UAAM,OAAO,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC,GAAG,SAAS;AAC/D,MAAE,CAAC,IAAI,EAAEA,aAAY,GAAG,IAAI,CAAC;EAC/B;AACA,QAAM,sBAAsBA,aAAY,GAAG,CAAC;AAC5C,SAAO,oBAAoB,MAAM,GAAG,UAAU;AAChD;AASM,SAAU,mBACd,KACA,KACA,YACA,GACA,GAAQ;AAER,EAAAD,QAAO,GAAG;AACV,EAAAA,QAAO,GAAG;AACV,OAAK,UAAU;AAGf,MAAI,IAAI,SAAS,KAAK;AACpB,UAAM,QAAQ,KAAK,KAAM,IAAI,IAAK,CAAC;AACnC,UAAM,EAAE,OAAO,EAAE,MAAK,CAAE,EAAE,OAAO,YAAY,mBAAmB,CAAC,EAAE,OAAO,GAAG,EAAE,OAAM;EACvF;AACA,MAAI,aAAa,SAAS,IAAI,SAAS;AACrC,UAAM,IAAI,MAAM,wCAAwC;AAC1D,SACE,EAAE,OAAO,EAAE,OAAO,WAAU,CAAE,EAC3B,OAAO,GAAG,EACV,OAAO,MAAM,YAAY,CAAC,CAAC,EAE3B,OAAO,GAAG,EACV,OAAO,MAAM,IAAI,QAAQ,CAAC,CAAC,EAC3B,OAAM;AAEb;AAUM,SAAU,cAAc,KAAiB,OAAe,SAAa;AACzE,iBAAe,SAAS;IACtB,KAAK;IACL,GAAG;IACH,GAAG;IACH,GAAG;IACH,MAAM;GACP;AACD,QAAM,EAAE,GAAG,GAAG,GAAG,MAAM,QAAQ,KAAK,KAAI,IAAK;AAC7C,EAAAA,QAAO,GAAG;AACV,OAAK,KAAK;AACV,QAAM,MAAM,OAAO,SAAS,WAAW,YAAY,IAAI,IAAI;AAC3D,QAAM,QAAQ,EAAE,SAAS,CAAC,EAAE;AAC5B,QAAM,IAAI,KAAK,MAAM,QAAQ,KAAK,CAAC;AACnC,QAAM,eAAe,QAAQ,IAAI;AACjC,MAAI;AACJ,MAAI,WAAW,OAAO;AACpB,UAAM,mBAAmB,KAAK,KAAK,cAAc,IAAI;EACvD,WAAW,WAAW,OAAO;AAC3B,UAAM,mBAAmB,KAAK,KAAK,cAAc,GAAG,IAAI;EAC1D,WAAW,WAAW,kBAAkB;AAEtC,UAAM;EACR,OAAO;AACL,UAAM,IAAI,MAAM,+BAA+B;EACjD;AACA,QAAM,IAAI,IAAI,MAAM,KAAK;AACzB,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,UAAM,IAAI,IAAI,MAAM,CAAC;AACrB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,aAAa,KAAK,IAAI,IAAI;AAChC,YAAM,KAAK,IAAI,SAAS,YAAY,aAAa,CAAC;AAClD,QAAE,CAAC,IAAI,IAAI,MAAM,EAAE,GAAG,CAAC;IACzB;AACA,MAAE,CAAC,IAAI;EACT;AACA,SAAO;AACT;AASM,SAAU,WAAmC,OAAU,KAAyB;AAEpF,QAAM,QAAQ,IAAI,IAAI,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE,QAAO,CAAE;AACpD,SAAO,CAAC,GAAM,MAAQ;AACpB,UAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,QAC1C,IAAI,OAAO,CAAC,KAAK,MAAM,MAAM,IAAI,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAEzD,QAAI,MAAM,IAAI,MAAM,IAAI;AACxB,QAAI,MAAM,IAAI,GAAG,MAAM,IAAI,MAAM,IAAI,CAAC;AACtC,WAAO,EAAE,GAAM,EAAI;EACrB;AACF;AAuBM,SAAU,aACdE,QACA,YACA,KAA0C;AAM1C,MAAI,OAAO,eAAe;AAAY,UAAM,IAAI,MAAM,8BAA8B;AACpF,SAAO;;;IAGL,YAAY,KAAiB,SAAsB;AACjD,YAAM,IAAI,cAAc,KAAK,GAAG,EAAE,GAAG,KAAK,KAAK,IAAI,KAAK,GAAG,QAAO,CAAU;AAC5E,YAAM,KAAKA,OAAM,WAAW,WAAW,EAAE,CAAC,CAAC,CAAC;AAC5C,YAAM,KAAKA,OAAM,WAAW,WAAW,EAAE,CAAC,CAAC,CAAC;AAC5C,YAAM,IAAI,GAAG,IAAI,EAAE,EAAE,cAAa;AAClC,QAAE,eAAc;AAChB,aAAO;IACT;;;IAIA,cAAc,KAAiB,SAAsB;AACnD,YAAM,IAAI,cAAc,KAAK,GAAG,EAAE,GAAG,KAAK,KAAK,IAAI,WAAW,GAAG,QAAO,CAAU;AAClF,YAAM,IAAIA,OAAM,WAAW,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,cAAa;AAC1D,QAAE,eAAc;AAChB,aAAO;IACT;;IAEA,WAAW,SAAiB;AAC1B,UAAI,CAAC,MAAM,QAAQ,OAAO;AAAG,cAAM,IAAI,MAAM,uCAAuC;AACpF,iBAAW,KAAK;AACd,YAAI,OAAO,MAAM;AAAU,gBAAM,IAAI,MAAM,uCAAuC;AACpF,YAAM,IAAIA,OAAM,WAAW,WAAW,OAAO,CAAC,EAAE,cAAa;AAC7D,QAAE,eAAc;AAChB,aAAO;IACT;;AAEJ;;;ACjOA,IAAM,aAAa,OAAO,oEAAoE;AAC9F,IAAM,aAAa,OAAO,oEAAoE;AAC9F,IAAMC,OAAM,OAAO,CAAC;AACpB,IAAMC,OAAM,OAAO,CAAC;AACpB,IAAM,aAAa,CAAC,GAAW,OAAe,IAAI,IAAIA,QAAO;AAM7D,SAAS,QAAQ,GAAS;AACxB,QAAM,IAAI;AAEV,QAAMC,OAAM,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,OAAO,OAAO,EAAE,GAAG,OAAO,OAAO,EAAE;AAE3E,QAAM,OAAO,OAAO,EAAE,GAAG,OAAO,OAAO,EAAE,GAAG,OAAO,OAAO,EAAE;AAC5D,QAAM,KAAM,IAAI,IAAI,IAAK;AACzB,QAAM,KAAM,KAAK,KAAK,IAAK;AAC3B,QAAM,KAAM,KAAK,IAAIA,MAAK,CAAC,IAAI,KAAM;AACrC,QAAM,KAAM,KAAK,IAAIA,MAAK,CAAC,IAAI,KAAM;AACrC,QAAM,MAAO,KAAK,IAAID,MAAK,CAAC,IAAI,KAAM;AACtC,QAAM,MAAO,KAAK,KAAK,MAAM,CAAC,IAAI,MAAO;AACzC,QAAM,MAAO,KAAK,KAAK,MAAM,CAAC,IAAI,MAAO;AACzC,QAAM,MAAO,KAAK,KAAK,MAAM,CAAC,IAAI,MAAO;AACzC,QAAM,OAAQ,KAAK,KAAK,MAAM,CAAC,IAAI,MAAO;AAC1C,QAAM,OAAQ,KAAK,MAAM,MAAM,CAAC,IAAI,MAAO;AAC3C,QAAM,OAAQ,KAAK,MAAMC,MAAK,CAAC,IAAI,KAAM;AACzC,QAAM,KAAM,KAAK,MAAM,MAAM,CAAC,IAAI,MAAO;AACzC,QAAM,KAAM,KAAK,IAAI,KAAK,CAAC,IAAI,KAAM;AACrC,QAAM,OAAO,KAAK,IAAID,MAAK,CAAC;AAC5B,MAAI,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC;AAAG,UAAM,IAAI,MAAM,yBAAyB;AAC3E,SAAO;AACT;AAEA,IAAM,OAAO,MAAM,YAAY,QAAW,QAAW,EAAE,MAAM,QAAO,CAAE;AAc/D,IAAM,YAA+B,YAC1C;EACE,GAAG,OAAO,CAAC;;EACX,GAAG,OAAO,CAAC;EACX,IAAI;;EACJ,GAAG;;;EAEH,IAAI,OAAO,+EAA+E;EAC1F,IAAI,OAAO,+EAA+E;EAC1F,GAAG,OAAO,CAAC;;EACX,MAAM;;EACN,MAAM;;IAEJ,MAAM,OAAO,oEAAoE;IACjF,aAAa,CAAC,MAAa;AACzB,YAAM,IAAI;AACV,YAAM,KAAK,OAAO,oCAAoC;AACtD,YAAM,KAAK,CAACD,OAAM,OAAO,oCAAoC;AAC7D,YAAM,KAAK,OAAO,qCAAqC;AACvD,YAAM,KAAK;AACX,YAAM,YAAY,OAAO,qCAAqC;AAE9D,YAAM,KAAK,WAAW,KAAK,GAAG,CAAC;AAC/B,YAAM,KAAK,WAAW,CAAC,KAAK,GAAG,CAAC;AAChC,UAAI,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,CAAC;AACrC,UAAI,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,CAAC;AAClC,YAAM,QAAQ,KAAK;AACnB,YAAM,QAAQ,KAAK;AACnB,UAAI;AAAO,aAAK,IAAI;AACpB,UAAI;AAAO,aAAK,IAAI;AACpB,UAAI,KAAK,aAAa,KAAK,WAAW;AACpC,cAAM,IAAI,MAAM,yCAAyC,CAAC;MAC5D;AACA,aAAO,EAAE,OAAO,IAAI,OAAO,GAAE;IAC/B;;GAGJ,MAAM;AAKR,IAAMG,OAAM,OAAO,CAAC;AAEpB,IAAM,uBAAsD,CAAA;AAC5D,SAAS,WAAW,QAAgB,UAAsB;AACxD,MAAI,OAAO,qBAAqB,GAAG;AACnC,MAAI,SAAS,QAAW;AACtB,UAAM,OAAO,OAAO,WAAW,KAAK,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;AAChE,WAAOC,aAAY,MAAM,IAAI;AAC7B,yBAAqB,GAAG,IAAI;EAC9B;AACA,SAAO,OAAOA,aAAY,MAAM,GAAG,QAAQ,CAAC;AAC9C;AAGA,IAAM,eAAe,CAAC,UAA6B,MAAM,WAAW,IAAI,EAAE,MAAM,CAAC;AACjF,IAAM,WAAW,CAAC,MAAc,gBAAgB,GAAG,EAAE;AACrD,IAAM,OAAO,CAAC,MAAc,IAAI,GAAG,UAAU;AAC7C,IAAM,OAAO,CAAC,MAAc,IAAI,GAAG,UAAU;AAC7C,IAAM,QAAQ,UAAU;AACxB,IAAM,UAAU,CAAC,GAAsB,GAAW,MAChD,MAAM,KAAK,qBAAqB,GAAG,GAAG,CAAC;AAGzC,SAAS,oBAAoB,MAAa;AACxC,MAAI,KAAK,UAAU,MAAM,uBAAuB,IAAI;AACpD,MAAI,IAAI,MAAM,eAAe,EAAE;AAC/B,QAAM,SAAS,EAAE,SAAQ,IAAK,KAAK,KAAK,CAAC,EAAE;AAC3C,SAAO,EAAE,QAAgB,OAAO,aAAa,CAAC,EAAC;AACjD;AAKA,SAAS,OAAO,GAAS;AACvB,WAAS,KAAK,GAAGJ,MAAK,UAAU;AAChC,QAAM,KAAK,KAAK,IAAI,CAAC;AACrB,QAAM,IAAI,KAAK,KAAK,IAAI,OAAO,CAAC,CAAC;AACjC,MAAI,IAAI,QAAQ,CAAC;AACjB,MAAI,IAAIC,SAAQE;AAAK,QAAI,KAAK,CAAC,CAAC;AAChC,QAAM,IAAI,IAAI,MAAM,GAAG,GAAGH,IAAG;AAC7B,IAAE,eAAc;AAChB,SAAO;AACT;AACA,IAAM,MAAM;AAIZ,SAAS,aAAa,MAAkB;AACtC,SAAO,KAAK,IAAI,WAAW,qBAAqB,GAAG,IAAI,CAAC,CAAC;AAC3D;AAKA,SAAS,oBAAoB,YAAe;AAC1C,SAAO,oBAAoB,UAAU,EAAE;AACzC;AAMA,SAAS,YACP,SACA,YACA,UAAe,YAAY,EAAE,GAAC;AAE9B,QAAM,IAAI,YAAY,WAAW,OAAO;AACxC,QAAM,EAAE,OAAO,IAAI,QAAQ,EAAC,IAAK,oBAAoB,UAAU;AAC/D,QAAM,IAAI,YAAY,WAAW,SAAS,EAAE;AAC5C,QAAM,IAAI,SAAS,IAAI,IAAI,WAAW,eAAe,CAAC,CAAC,CAAC;AACxD,QAAM,OAAO,WAAW,iBAAiB,GAAG,IAAI,CAAC;AACjD,QAAM,KAAK,KAAK,IAAI,IAAI,CAAC;AACzB,MAAI,OAAOG;AAAK,UAAM,IAAI,MAAM,wBAAwB;AACxD,QAAM,EAAE,OAAO,IAAI,QAAQ,EAAC,IAAK,oBAAoB,EAAE;AACvD,QAAM,IAAI,UAAU,IAAI,IAAI,CAAC;AAC7B,QAAM,MAAM,IAAI,WAAW,EAAE;AAC7B,MAAI,IAAI,IAAI,CAAC;AACb,MAAI,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,EAAE;AAErC,MAAI,CAAC,cAAc,KAAK,GAAG,EAAE;AAAG,UAAM,IAAI,MAAM,kCAAkC;AAClF,SAAO;AACT;AAMA,SAAS,cAAc,WAAgB,SAAc,WAAc;AACjE,QAAM,MAAM,YAAY,aAAa,WAAW,EAAE;AAClD,QAAM,IAAI,YAAY,WAAW,OAAO;AACxC,QAAM,MAAM,YAAY,aAAa,WAAW,EAAE;AAClD,MAAI;AACF,UAAM,IAAI,OAAO,IAAI,GAAG,CAAC;AACzB,UAAM,IAAI,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACjC,QAAI,CAAC,QAAQ,GAAGH,MAAK,UAAU;AAAG,aAAO;AACzC,UAAM,IAAI,IAAI,IAAI,SAAS,IAAI,EAAE,CAAC;AAClC,QAAI,CAAC,QAAQ,GAAGA,MAAK,UAAU;AAAG,aAAO;AACzC,UAAM,IAAI,UAAU,SAAS,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC;AACnD,UAAM,IAAI,QAAQ,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;AAChC,QAAI,CAAC,KAAK,CAAC,EAAE,SAAQ,KAAM,EAAE,SAAQ,EAAG,MAAM;AAAG,aAAO;AACxD,WAAO;EACT,SAAS,OAAO;AACd,WAAO;EACT;AACF;AA2BO,IAAM,WAAwC,OAAO;EAC1D,cAAc;EACd,MAAM;EACN,QAAQ;EACR,OAAO;IACL,kBAAkB,UAAU,MAAM;IAClC;IACA;IACA;IACA;IACA;IACA;;IAED;AAEH,IAAM,UAA0B,MAC9B,WACE,MACA;;EAEE;IACE;IACA;IACA;IACA;;;EAGF;IACE;IACA;IACA;;;;EAGF;IACE;IACA;IACA;IACA;;;EAGF;IACE;IACA;IACA;IACA;;;EAEF,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,CAA6C,GACjF;AACJ,IAAM,UAA0B,MAC9B,oBAAoB,MAAM;EACxB,GAAG,OAAO,oEAAoE;EAC9E,GAAG,OAAO,MAAM;EAChB,GAAG,KAAK,OAAO,OAAO,KAAK,CAAC;CAC7B,GAAE;AACL,IAAM,OAAuB,MAC3B,aACE,UAAU,iBACV,CAAC,YAAqB;AACpB,QAAM,EAAE,GAAG,EAAC,IAAK,OAAO,KAAK,OAAO,QAAQ,CAAC,CAAC,CAAC;AAC/C,SAAO,OAAO,GAAG,CAAC;AACpB,GACA;EACE,KAAK;EACL,WAAW;EACX,GAAG,KAAK;EACR,GAAG;EACH,GAAG;EACH,QAAQ;EACR,MAAM;CACP,GACD;AAGG,IAAM,eAAkD,MAAM,IAAI,aAAY;AAG9E,IAAM,iBAAoD,MAAM,IAAI,eAAc;", "names": ["abytes", "concatBytes", "num", "_0n", "_1n", "_2n", "num", "num", "_0n", "_1n", "bitLen", "_0n", "_1n", "num", "num", "_1n", "_0n", "_1n", "wbits", "num", "_0n", "abytes", "_1n", "_2n", "_3n", "_4n", "toBytes", "concatBytes", "Point", "a", "modN", "randomBytes", "tv5", "c1", "c2", "abytes", "concatBytes", "Point", "_1n", "_2n", "_3n", "_0n", "concatBytes"]}