import{e as t}from"./index-C8294aMB-1754367538540-wb8rvug16.js";import{E as k,R as E,h as L,j as U,i as g,f as v,r as x}from"./index-C8294aMB-1754367538540-wb8rvug16.js";import{createAndLock as r,fetchTotalRooms as e,fetchRoom as c,joinRoom as n,claimReward as m}from"./core-BaMphMzk-1754367538540-7ns772q62.js";import"./web3-uglCpOhK-1754367538540-7ns772q62.js";import"./vendor-CgHzTxSQ-1754367538540-7ns772q62.js";import"./ui-CbWGv3YI-1754367538540-7ns772q62.js";import"./basicOperations-DvCcvMnJ-1754367538540-7ns772q62.js";import"./roomManagement-ki4T0DWy-1754367538540-7ns772q62.js";import"./rewardOperations-2DEVGtSb-1754367538540-7ns772q62.js";import"./transaction-Cr7qC6sv-1754367538540-7ns772q62.js";async function y(o,a){return r(o,a)}async function S(o){return e(o)}async function T(o,a){return c(o,a)}async function w(o){return n(o)}async function $(o){return m(o)}async function j(o){return t.fetchRooms(o)}export{k as ERROR_CODES,E as ROOM_STATUS,L as calculateRemainingTime,U as canUserClaimReward,g as canUserJoinRoom,$ as claimReward,y as createAndLock,t as default,T as fetchRoom,j as fetchRooms,S as fetchTotalRooms,v as formatRoomStatus,w as joinRoom,t as newGroupBuyService,x as recalculateRoomStatus};
