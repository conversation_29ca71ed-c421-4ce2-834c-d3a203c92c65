(function(c,a){typeof exports=="object"&&typeof module<"u"?a(exports):typeof define=="function"&&define.amd?define(["exports"],a):(c=typeof globalThis<"u"?globalThis:c||self,a(c["@walletconnect/types"]={}))})(this,function(c){"use strict";class a{}var P=Object.defineProperty,R=(t,e,n)=>e in t?P(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,y=(t,e,n)=>R(t,typeof e!="symbol"?e+"":e,n);class x extends a{constructor(e){super(),this.opts=e,y(this,"protocol","wc"),y(this,"version",2)}}class N{constructor(e,n,r){this.core=e,this.logger=n}}var S=Object.defineProperty,T=(t,e,n)=>e in t?S(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,A=(t,e,n)=>T(t,typeof e!="symbol"?e+"":e,n);class K extends a{constructor(e,n){super(),this.core=e,this.logger=n,A(this,"records",new Map)}}class F{constructor(e,n){this.logger=e,this.core=n}}class W extends a{constructor(e,n){super(),this.relayer=e,this.logger=n}}class $ extends a{constructor(e){super()}}class k{constructor(e,n,r,s){this.core=e,this.logger=n,this.name=r}}var H=Object.defineProperty,J=(t,e,n)=>e in t?H(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,U=(t,e,n)=>J(t,typeof e!="symbol"?e+"":e,n);class V{constructor(){U(this,"map",new Map)}}class q extends a{constructor(e,n){super(),this.relayer=e,this.logger=n}}class z{constructor(e,n){this.core=e,this.logger=n}}class B extends a{constructor(e,n){super(),this.core=e,this.logger=n}}class D{constructor(e,n){this.logger=e,this.core=n}}class G{constructor(e,n,r){this.core=e,this.logger=n,this.store=r}}class Q{constructor(e,n){this.projectId=e,this.logger=n}}class X{constructor(e,n,r){this.core=e,this.logger=n,this.telemetryEnabled=r}}var v={exports:{}},l=typeof Reflect=="object"?Reflect:null,g=l&&typeof l.apply=="function"?l.apply:function(e,n,r){return Function.prototype.apply.call(e,n,r)},p;l&&typeof l.ownKeys=="function"?p=l.ownKeys:Object.getOwnPropertySymbols?p=function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:p=function(e){return Object.getOwnPropertyNames(e)};function Y(t){console&&console.warn&&console.warn(t)}var m=Number.isNaN||function(e){return e!==e};function o(){o.init.call(this)}v.exports=o,v.exports.once=ne,o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var b=10;function d(t){if(typeof t!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return b},set:function(t){if(typeof t!="number"||t<0||m(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");b=t}}),o.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(e){if(typeof e!="number"||e<0||m(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this};function L(t){return t._maxListeners===void 0?o.defaultMaxListeners:t._maxListeners}o.prototype.getMaxListeners=function(){return L(this)},o.prototype.emit=function(e){for(var n=[],r=1;r<arguments.length;r++)n.push(arguments[r]);var s=e==="error",u=this._events;if(u!==void 0)s=s&&u.error===void 0;else if(!s)return!1;if(s){var i;if(n.length>0&&(i=n[0]),i instanceof Error)throw i;var f=new Error("Unhandled error."+(i?" ("+i.message+")":""));throw f.context=i,f}var h=u[e];if(h===void 0)return!1;if(typeof h=="function")g(h,this,n);else for(var M=h.length,ae=C(h,M),r=0;r<M;++r)g(ae[r],this,n);return!0};function I(t,e,n,r){var s,u,i;if(d(n),u=t._events,u===void 0?(u=t._events=Object.create(null),t._eventsCount=0):(u.newListener!==void 0&&(t.emit("newListener",e,n.listener?n.listener:n),u=t._events),i=u[e]),i===void 0)i=u[e]=n,++t._eventsCount;else if(typeof i=="function"?i=u[e]=r?[n,i]:[i,n]:r?i.unshift(n):i.push(n),s=L(t),s>0&&i.length>s&&!i.warned){i.warned=!0;var f=new Error("Possible EventEmitter memory leak detected. "+i.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");f.name="MaxListenersExceededWarning",f.emitter=t,f.type=e,f.count=i.length,Y(f)}return t}o.prototype.addListener=function(e,n){return I(this,e,n,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(e,n){return I(this,e,n,!0)};function Z(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function w(t,e,n){var r={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},s=Z.bind(r);return s.listener=n,r.wrapFn=s,s}o.prototype.once=function(e,n){return d(n),this.on(e,w(this,e,n)),this},o.prototype.prependOnceListener=function(e,n){return d(n),this.prependListener(e,w(this,e,n)),this},o.prototype.removeListener=function(e,n){var r,s,u,i,f;if(d(n),s=this._events,s===void 0)return this;if(r=s[e],r===void 0)return this;if(r===n||r.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete s[e],s.removeListener&&this.emit("removeListener",e,r.listener||n));else if(typeof r!="function"){for(u=-1,i=r.length-1;i>=0;i--)if(r[i]===n||r[i].listener===n){f=r[i].listener,u=i;break}if(u<0)return this;u===0?r.shift():ee(r,u),r.length===1&&(s[e]=r[0]),s.removeListener!==void 0&&this.emit("removeListener",e,f||n)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(e){var n,r,s;if(r=this._events,r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete r[e]),this;if(arguments.length===0){var u=Object.keys(r),i;for(s=0;s<u.length;++s)i=u[s],i!=="removeListener"&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=r[e],typeof n=="function")this.removeListener(e,n);else if(n!==void 0)for(s=n.length-1;s>=0;s--)this.removeListener(e,n[s]);return this};function E(t,e,n){var r=t._events;if(r===void 0)return[];var s=r[e];return s===void 0?[]:typeof s=="function"?n?[s.listener||s]:[s]:n?te(s):C(s,s.length)}o.prototype.listeners=function(e){return E(this,e,!0)},o.prototype.rawListeners=function(e){return E(this,e,!1)},o.listenerCount=function(t,e){return typeof t.listenerCount=="function"?t.listenerCount(e):_.call(t,e)},o.prototype.listenerCount=_;function _(t){var e=this._events;if(e!==void 0){var n=e[t];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}o.prototype.eventNames=function(){return this._eventsCount>0?p(this._events):[]};function C(t,e){for(var n=new Array(e),r=0;r<e;++r)n[r]=t[r];return n}function ee(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}function te(t){for(var e=new Array(t.length),n=0;n<e.length;++n)e[n]=t[n].listener||t[n];return e}function ne(t,e){return new Promise(function(n,r){function s(i){t.removeListener(e,u),r(i)}function u(){typeof t.removeListener=="function"&&t.removeListener("error",s),n([].slice.call(arguments))}O(t,e,u,{once:!0}),e!=="error"&&re(t,s,{once:!0})})}function re(t,e,n){typeof t.on=="function"&&O(t,"error",e,n)}function O(t,e,n,r){if(typeof t.on=="function")r.once?t.once(e,n):t.on(e,n);else if(typeof t.addEventListener=="function")t.addEventListener(e,function s(u){r.once&&t.removeEventListener(e,s),n(u)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t)}var se=Object.defineProperty,ie=(t,e,n)=>e in t?se(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,j=(t,e,n)=>ie(t,typeof e!="symbol"?e+"":e,n);class oe extends v.exports{constructor(){super()}}class ue{constructor(e){this.opts=e,j(this,"protocol","wc"),j(this,"version",2)}}class ce extends v.exports.EventEmitter{constructor(){super()}}class fe{constructor(e){this.client=e}}c.ICore=x,c.ICrypto=N,c.IEchoClient=Q,c.IEngine=fe,c.IEngineEvents=ce,c.IEventClient=X,c.IExpirer=B,c.IJsonRpcHistory=K,c.IKeyChain=z,c.IMessageTracker=F,c.IPairing=D,c.IPublisher=W,c.IRelayer=$,c.ISignClient=ue,c.ISignClientEvents=oe,c.IStore=k,c.ISubscriber=q,c.ISubscriberTopicMap=V,c.IVerify=G,Object.defineProperty(c,"__esModule",{value:!0})});
//# sourceMappingURL=index.umd.js.map
