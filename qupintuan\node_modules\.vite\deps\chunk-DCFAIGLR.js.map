{"version": 3, "sources": ["../../preact/src/constants.js", "../../preact/src/util.js", "../../preact/src/options.js", "../../preact/src/create-element.js", "../../preact/src/component.js", "../../preact/src/diff/props.js", "../../preact/src/create-context.js", "../../preact/src/diff/children.js", "../../preact/src/diff/index.js", "../../preact/src/render.js", "../../preact/src/clone-element.js", "../../preact/src/diff/catch-error.js", "../../preact/hooks/src/index.js"], "sourcesContent": ["/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 16;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 17;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { EMPTY_ARR } from './constants';\n\nexport const isArray = Array.isArray;\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-expect-error We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {preact.ContainerNode} node The node to remove\n */\nexport function removeNode(node) {\n\tif (node && node.parentNode) node.parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {VNode[\"type\"]} type The node name or Component constructor for this\n * virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the\n * virtual node\n * @returns {VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != null) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === undefined) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, null);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\t/** @type {VNode} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\n\t\t// be set to dom.nextSibling which can return `null` and it is important\n\t\t// to be able to distinguish between an uninitialized _nextDom and\n\t\t// a _nextDom that has been set to `null`\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\tconstructor: undefined,\n\t\t_original: original == null ? ++vnodeId : original,\n\t\t_index: -1,\n\t\t_flags: 0\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == null && options.vnode != null) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: null };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != null && vnode.constructor == undefined;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\nimport { MODE_HYDRATE } from './constants';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function BaseComponent(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nBaseComponent.prototype.setState = function (update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != null && this._nextState !== this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == null) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nBaseComponent.prototype.forceUpdate = function (callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {ComponentChildren | void}\n */\nBaseComponent.prototype.render = Fragment;\n\n/**\n * @param {VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == null) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._index + 1)\n\t\t\t: null;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != null && sibling._dom != null) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet oldVNode = component._vnode,\n\t\toldDom = oldVNode._dom,\n\t\tcommitQueue = [],\n\t\trefQueue = [];\n\n\tif (component._parentDom) {\n\t\tconst newVNode = assign({}, oldVNode);\n\t\tnewVNode._original = oldVNode._original + 1;\n\t\tif (options.vnode) options.vnode(newVNode);\n\n\t\tdiff(\n\t\t\tcomponent._parentDom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tcomponent._parentDom.namespaceURI,\n\t\t\toldVNode._flags & MODE_HYDRATE ? [oldDom] : null,\n\t\t\tcommitQueue,\n\t\t\toldDom == null ? getDomSibling(oldVNode) : oldDom,\n\t\t\t!!(oldVNode._flags & MODE_HYDRATE),\n\t\t\trefQueue\n\t\t);\n\n\t\tnewVNode._original = oldVNode._original;\n\t\tnewVNode._parent._children[newVNode._index] = newVNode;\n\t\tcommitRoot(commitQueue, newVNode, refQueue);\n\n\t\tif (newVNode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(newVNode);\n\t\t}\n\t}\n}\n\n/**\n * @param {VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\n\t\tvnode._dom = vnode._component.base = null;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != null && child._dom != null) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/**\n * Enqueue a rerender of a component\n * @param {Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce !== options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/**\n * @param {Component} a\n * @param {Component} b\n */\nconst depthSort = (a, b) => a._vnode._depth - b._vnode._depth;\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet c;\n\trerenderQueue.sort(depthSort);\n\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t// process() calls from getting scheduled while `queue` is still being consumed.\n\twhile ((c = rerenderQueue.shift())) {\n\t\tif (c._dirty) {\n\t\t\tlet renderQueueLength = rerenderQueue.length;\n\t\t\trenderComponent(c);\n\t\t\tif (rerenderQueue.length > renderQueueLength) {\n\t\t\t\t// When i.e. rerendering a provider additional new items can be injected, we want to\n\t\t\t\t// keep the order from top to bottom with those new items so we can handle them in a\n\t\t\t\t// single pass\n\t\t\t\trerenderQueue.sort(depthSort);\n\t\t\t}\n\t\t}\n\t}\n\tprocess._rerenderCount = 0;\n}\n\nprocess._rerenderCount = 0;\n", "import { IS_NON_DIMENSIONAL } from '../constants';\nimport options from '../options';\n\nfunction setStyle(style, key, value) {\n\tif (key[0] === '-') {\n\t\tstyle.setProperty(key, value == null ? '' : value);\n\t} else if (value == null) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\n// A logical clock to solve issues like https://github.com/preactjs/preact/issues/3927.\n// When the DOM performs an event it leaves micro-ticks in between bubbling up which means that\n// an event can trigger on a newly reated DOM-node while the event bubbles up.\n//\n// Originally inspired by Vue\n// (https://github.com/vuejs/core/blob/caeb8a68811a1b0f79/packages/runtime-dom/src/modules/events.ts#L90-L101),\n// but modified to use a logical clock instead of Date.now() in case event handlers get attached\n// and events get dispatched during the same millisecond.\n//\n// The clock is incremented after each new event dispatch. This allows 1 000 000 new events\n// per second for over 280 years before the value reaches Number.MAX_SAFE_INTEGER (2**53 - 1).\nlet eventClock = 0;\n\n/**\n * Set a property value on a DOM node\n * @param {PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {string} namespace Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, namespace) {\n\tlet useCapture;\n\n\to: if (name === 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] !== oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] === 'o' && name[1] === 'n') {\n\t\tuseCapture =\n\t\t\tname !== (name = name.replace(/(PointerCapture)$|Capture$/i, '$1'));\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (\n\t\t\tname.toLowerCase() in dom ||\n\t\t\tname === 'onFocusOut' ||\n\t\t\tname === 'onFocusIn'\n\t\t)\n\t\t\tname = name.toLowerCase().slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tvalue._attached = eventClock;\n\t\t\t\tdom.addEventListener(\n\t\t\t\t\tname,\n\t\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\t\tuseCapture\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tvalue._attached = oldValue._attached;\n\t\t\t}\n\t\t} else {\n\t\t\tdom.removeEventListener(\n\t\t\t\tname,\n\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\tuseCapture\n\t\t\t);\n\t\t}\n\t} else {\n\t\tif (namespace == 'http://www.w3.org/2000/svg') {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname != 'width' &&\n\t\t\tname != 'height' &&\n\t\t\tname != 'href' &&\n\t\t\tname != 'list' &&\n\t\t\tname != 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname != 'tabIndex' &&\n\t\t\tname != 'download' &&\n\t\t\tname != 'rowSpan' &&\n\t\t\tname != 'colSpan' &&\n\t\t\tname != 'role' &&\n\t\t\tname != 'popover' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == null ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// aria- and data- attributes have no boolean representation.\n\t\t// A `false` value is different from the attribute not being\n\t\t// present, so we can't remove it. For non-boolean aria\n\t\t// attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost too many bytes. On top of\n\t\t// that other frameworks generally stringify `false`.\n\n\t\tif (typeof value == 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != null && (value !== false || name[4] === '-')) {\n\t\t\tdom.setAttribute(name, name == 'popover' && value == true ? '' : value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Create an event proxy function.\n * @param {boolean} useCapture Is the event handler for the capture phase.\n * @private\n */\nfunction createEventProxy(useCapture) {\n\t/**\n\t * Proxy an event to hooked event handlers\n\t * @param {PreactEvent} e The event object from the browser\n\t * @private\n\t */\n\treturn function (e) {\n\t\tif (this._listeners) {\n\t\t\tconst eventHandler = this._listeners[e.type + useCapture];\n\t\t\tif (e._dispatched == null) {\n\t\t\t\te._dispatched = eventClock++;\n\n\t\t\t\t// When `e._dispatched` is smaller than the time when the targeted event\n\t\t\t\t// handler was attached we know we have bubbled up to an element that was added\n\t\t\t\t// during patching the DOM.\n\t\t\t} else if (e._dispatched < eventHandler._attached) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\treturn eventHandler(options.event ? options.event(e) : e);\n\t\t}\n\t};\n}\n\nconst eventProxy = createEventProxy(false);\nconst eventProxyCapture = createEventProxy(true);\n", "import { enqueueRender } from './component';\n\nexport let i = 0;\n\nexport function createContext(defaultValue, contextId) {\n\tcontextId = '__cC' + i++;\n\n\tconst context = {\n\t\t_id: contextId,\n\t\t_defaultValue: defaultValue,\n\t\t/** @type {FunctionComponent} */\n\t\tConsumer(props, contextValue) {\n\t\t\t// return props.children(\n\t\t\t// \tcontext[contextId] ? context[contextId].props.value : defaultValue\n\t\t\t// );\n\t\t\treturn props.children(contextValue);\n\t\t},\n\t\t/** @type {FunctionComponent} */\n\t\tProvider(props) {\n\t\t\tif (!this.getChildContext) {\n\t\t\t\t/** @type {Component[] | null} */\n\t\t\t\tlet subs = [];\n\t\t\t\tlet ctx = {};\n\t\t\t\tctx[contextId] = this;\n\n\t\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\t\tthis.componentWillUnmount = () => {\n\t\t\t\t\tsubs = null;\n\t\t\t\t};\n\n\t\t\t\tthis.shouldComponentUpdate = function (_props) {\n\t\t\t\t\tif (this.props.value !== _props.value) {\n\t\t\t\t\t\tsubs.some(c => {\n\t\t\t\t\t\t\tc._force = true;\n\t\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tthis.sub = c => {\n\t\t\t\t\tsubs.push(c);\n\t\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\t\tif (subs) {\n\t\t\t\t\t\t\tsubs.splice(subs.indexOf(c), 1);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn props.children;\n\t\t}\n\t};\n\n\t// Devtools needs access to the context object when it\n\t// encounters a Provider. This is necessary to support\n\t// setting `displayName` on the context object instead\n\t// of on the component itself. See:\n\t// https://reactjs.org/docs/context.html#contextdisplayname\n\n\treturn (context.Provider._contextRef = context.Consumer.contextType =\n\t\tcontext);\n}\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport { EMPTY_OBJ, EMPTY_ARR, INSERT_VNODE, MATCHED } from '../constants';\nimport { isArray } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * Diff the children of a virtual node\n * @param {PreactElement} parentDom The DOM element whose children are being\n * diffed\n * @param {ComponentChildren[]} renderResult\n * @param {VNode} newParentVNode The new virtual node whose children should be\n * diff'ed against oldParentVNode\n * @param {VNode} oldParentVNode The old virtual node whose children should be\n * diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\tlet i,\n\t\t/** @type {VNode} */\n\t\toldVNode,\n\t\t/** @type {VNode} */\n\t\tchildVNode,\n\t\t/** @type {PreactElement} */\n\t\tnewDom,\n\t\t/** @type {PreactElement} */\n\t\tfirstChildDom;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\t/** @type {VNode[]} */\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet newChildrenLength = renderResult.length;\n\n\tnewParentVNode._nextDom = oldDom;\n\tconstructNewChildrenArray(newParentVNode, renderResult, oldChildren);\n\toldDom = newParentVNode._nextDom;\n\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\tchildVNode = newParentVNode._children[i];\n\t\tif (childVNode == null) continue;\n\n\t\t// At this point, constructNewChildrenArray has assigned _index to be the\n\t\t// matchingIndex for this VNode's oldVNode (or -1 if there is no oldVNode).\n\t\tif (childVNode._index === -1) {\n\t\t\toldVNode = EMPTY_OBJ;\n\t\t} else {\n\t\t\toldVNode = oldChildren[childVNode._index] || EMPTY_OBJ;\n\t\t}\n\n\t\t// Update childVNode._index to its final index\n\t\tchildVNode._index = i;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tdiff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\n\t\t// Adjust DOM nodes\n\t\tnewDom = childVNode._dom;\n\t\tif (childVNode.ref && oldVNode.ref != childVNode.ref) {\n\t\t\tif (oldVNode.ref) {\n\t\t\t\tapplyRef(oldVNode.ref, null, childVNode);\n\t\t\t}\n\t\t\trefQueue.push(\n\t\t\t\tchildVNode.ref,\n\t\t\t\tchildVNode._component || newDom,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t}\n\n\t\tif (firstChildDom == null && newDom != null) {\n\t\t\tfirstChildDom = newDom;\n\t\t}\n\n\t\tif (\n\t\t\tchildVNode._flags & INSERT_VNODE ||\n\t\t\toldVNode._children === childVNode._children\n\t\t) {\n\t\t\toldDom = insert(childVNode, oldDom, parentDom);\n\t\t} else if (\n\t\t\ttypeof childVNode.type == 'function' &&\n\t\t\tchildVNode._nextDom !== undefined\n\t\t) {\n\t\t\t// Since Fragments or components that return Fragment like VNodes can\n\t\t\t// contain multiple DOM nodes as the same level, continue the diff from\n\t\t\t// the sibling of last DOM child of this child VNode\n\t\t\toldDom = childVNode._nextDom;\n\t\t} else if (newDom) {\n\t\t\toldDom = newDom.nextSibling;\n\t\t}\n\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because it\n\t\t// is only used by `diffChildren` to determine where to resume the diff\n\t\t// after diffing Components and Fragments. Once we store it the nextDOM\n\t\t// local var, we can clean up the property. Also prevents us hanging on to\n\t\t// DOM nodes that may have been unmounted.\n\t\tchildVNode._nextDom = undefined;\n\n\t\t// Unset diffing flags\n\t\tchildVNode._flags &= ~(INSERT_VNODE | MATCHED);\n\t}\n\n\t// TODO: With new child diffing algo, consider alt ways to diff Fragments.\n\t// Such as dropping oldDom and moving fragments in place\n\t//\n\t// Because the newParentVNode is Fragment-like, we need to set it's\n\t// _nextDom property to the nextSibling of its last child DOM node.\n\t//\n\t// `oldDom` contains the correct value here because if the last child\n\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\n\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\n\t// node's nextSibling.\n\tnewParentVNode._nextDom = oldDom;\n\tnewParentVNode._dom = firstChildDom;\n}\n\n/**\n * @param {VNode} newParentVNode\n * @param {ComponentChildren[]} renderResult\n * @param {VNode[]} oldChildren\n */\nfunction constructNewChildrenArray(newParentVNode, renderResult, oldChildren) {\n\t/** @type {number} */\n\tlet i;\n\t/** @type {VNode} */\n\tlet childVNode;\n\t/** @type {VNode} */\n\tlet oldVNode;\n\n\tconst newChildrenLength = renderResult.length;\n\tlet oldChildrenLength = oldChildren.length,\n\t\tremainingOldChildren = oldChildrenLength;\n\n\tlet skew = 0;\n\n\tnewParentVNode._children = [];\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\t// @ts-expect-error We are reusing the childVNode variable to hold both the\n\t\t// pre and post normalized childVNode\n\t\tchildVNode = renderResult[i];\n\n\t\tif (\n\t\t\tchildVNode == null ||\n\t\t\ttypeof childVNode == 'boolean' ||\n\t\t\ttypeof childVNode == 'function'\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = null;\n\t\t\tcontinue;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint' ||\n\t\t\tchildVNode.constructor == String\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tnull,\n\t\t\t\tchildVNode,\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (childVNode.constructor === undefined && childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : null,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\tconst skewedIndex = i + skew;\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Temporarily store the matchingIndex on the _index property so we can pull\n\t\t// out the oldVNode in diffChildren. We'll override this to the VNode's\n\t\t// final index after using this property to get the oldVNode\n\t\tconst matchingIndex = (childVNode._index = findMatchingIndex(\n\t\t\tchildVNode,\n\t\t\toldChildren,\n\t\t\tskewedIndex,\n\t\t\tremainingOldChildren\n\t\t));\n\n\t\toldVNode = null;\n\t\tif (matchingIndex !== -1) {\n\t\t\toldVNode = oldChildren[matchingIndex];\n\t\t\tremainingOldChildren--;\n\t\t\tif (oldVNode) {\n\t\t\t\toldVNode._flags |= MATCHED;\n\t\t\t}\n\t\t}\n\n\t\t// Here, we define isMounting for the purposes of the skew diffing\n\t\t// algorithm. Nodes that are unsuspending are considered mounting and we detect\n\t\t// this by checking if oldVNode._original === null\n\t\tconst isMounting = oldVNode == null || oldVNode._original === null;\n\n\t\tif (isMounting) {\n\t\t\tif (matchingIndex == -1) {\n\t\t\t\tskew--;\n\t\t\t}\n\n\t\t\t// If we are mounting a DOM VNode, mark it for insertion\n\t\t\tif (typeof childVNode.type != 'function') {\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t} else if (matchingIndex !== skewedIndex) {\n\t\t\t// When we move elements around i.e. [0, 1, 2] --> [1, 0, 2]\n\t\t\t// --> we diff 1, we find it at position 1 while our skewed index is 0 and our skew is 0\n\t\t\t//     we set the skew to 1 as we found an offset.\n\t\t\t// --> we diff 0, we find it at position 0 while our skewed index is at 2 and our skew is 1\n\t\t\t//     this makes us increase the skew again.\n\t\t\t// --> we diff 2, we find it at position 2 while our skewed index is at 4 and our skew is 2\n\t\t\t//\n\t\t\t// this becomes an optimization question where currently we see a 1 element offset as an insertion\n\t\t\t// or deletion i.e. we optimize for [0, 1, 2] --> [9, 0, 1, 2]\n\t\t\t// while a more than 1 offset we see as a swap.\n\t\t\t// We could probably build heuristics for having an optimized course of action here as well, but\n\t\t\t// might go at the cost of some bytes.\n\t\t\t//\n\t\t\t// If we wanted to optimize for i.e. only swaps we'd just do the last two code-branches and have\n\t\t\t// only the first item be a re-scouting and all the others fall in their skewed counter-part.\n\t\t\t// We could also further optimize for swaps\n\t\t\tif (matchingIndex == skewedIndex - 1) {\n\t\t\t\tskew--;\n\t\t\t} else if (matchingIndex == skewedIndex + 1) {\n\t\t\t\tskew++;\n\t\t\t} else {\n\t\t\t\tif (matchingIndex > skewedIndex) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\n\t\t\t\t// Move this VNode's DOM if the original index (matchingIndex) doesn't\n\t\t\t\t// match the new skew index (i + new skew)\n\t\t\t\t// In the former two branches we know that it matches after skewing\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any. Loop forwards so that as we\n\t// unmount DOM from the beginning of the oldChildren, we can adjust oldDom to\n\t// point to the next child, which needs to be the first DOM node that won't be\n\t// unmounted.\n\tif (remainingOldChildren) {\n\t\tfor (i = 0; i < oldChildrenLength; i++) {\n\t\t\toldVNode = oldChildren[i];\n\t\t\tif (oldVNode != null && (oldVNode._flags & MATCHED) === 0) {\n\t\t\t\tif (oldVNode._dom == newParentVNode._nextDom) {\n\t\t\t\t\tnewParentVNode._nextDom = getDomSibling(oldVNode);\n\t\t\t\t}\n\n\t\t\t\tunmount(oldVNode, oldVNode);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * @param {VNode} parentVNode\n * @param {PreactElement} oldDom\n * @param {PreactElement} parentDom\n * @returns {PreactElement}\n */\nfunction insert(parentVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\n\tif (typeof parentVNode.type == 'function') {\n\t\tlet children = parentVNode._children;\n\t\tfor (let i = 0; children && i < children.length; i++) {\n\t\t\tif (children[i]) {\n\t\t\t\t// If we enter this code path on sCU bailout, where we copy\n\t\t\t\t// oldVNode._children to newVNode._children, we need to update the old\n\t\t\t\t// children's _parent pointer to point to the newVNode (parentVNode\n\t\t\t\t// here).\n\t\t\t\tchildren[i]._parent = parentVNode;\n\t\t\t\toldDom = insert(children[i], oldDom, parentDom);\n\t\t\t}\n\t\t}\n\n\t\treturn oldDom;\n\t} else if (parentVNode._dom != oldDom) {\n\t\tif (oldDom && parentVNode.type && !parentDom.contains(oldDom)) {\n\t\t\toldDom = getDomSibling(parentVNode);\n\t\t}\n\t\tparentDom.insertBefore(parentVNode._dom, oldDom || null);\n\t\toldDom = parentVNode._dom;\n\t}\n\n\tdo {\n\t\toldDom = oldDom && oldDom.nextSibling;\n\t} while (oldDom != null && oldDom.nodeType === 8);\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {ComponentChildren} children The unflattened children of a virtual\n * node\n * @returns {VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == null || typeof children == 'boolean') {\n\t} else if (isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\n/**\n * @param {VNode} childVNode\n * @param {VNode[]} oldChildren\n * @param {number} skewedIndex\n * @param {number} remainingOldChildren\n * @returns {number}\n */\nfunction findMatchingIndex(\n\tchildVNode,\n\toldChildren,\n\tskewedIndex,\n\tremainingOldChildren\n) {\n\tconst key = childVNode.key;\n\tconst type = childVNode.type;\n\tlet x = skewedIndex - 1;\n\tlet y = skewedIndex + 1;\n\tlet oldVNode = oldChildren[skewedIndex];\n\n\t// We only need to perform a search if there are more children\n\t// (remainingOldChildren) to search. However, if the oldVNode we just looked\n\t// at skewedIndex was not already used in this diff, then there must be at\n\t// least 1 other (so greater than 1) remainingOldChildren to attempt to match\n\t// against. So the following condition checks that ensuring\n\t// remainingOldChildren > 1 if the oldVNode is not already used/matched. Else\n\t// if the oldVNode was null or matched, then there could needs to be at least\n\t// 1 (aka `remainingOldChildren > 0`) children to find and compare against.\n\tlet shouldSearch =\n\t\tremainingOldChildren >\n\t\t(oldVNode != null && (oldVNode._flags & MATCHED) === 0 ? 1 : 0);\n\n\tif (\n\t\toldVNode === null ||\n\t\t(oldVNode &&\n\t\t\tkey == oldVNode.key &&\n\t\t\ttype === oldVNode.type &&\n\t\t\t(oldVNode._flags & MATCHED) === 0)\n\t) {\n\t\treturn skewedIndex;\n\t} else if (shouldSearch) {\n\t\twhile (x >= 0 || y < oldChildren.length) {\n\t\t\tif (x >= 0) {\n\t\t\t\toldVNode = oldChildren[x];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) === 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn x;\n\t\t\t\t}\n\t\t\t\tx--;\n\t\t\t}\n\n\t\t\tif (y < oldChildren.length) {\n\t\t\t\toldVNode = oldChildren[y];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) === 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn y;\n\t\t\t\t}\n\t\t\t\ty++;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn -1;\n}\n", "import {\n\tEMPTY_OBJ,\n\tMODE_HYDRATE,\n\tMODE_SUSPENDED,\n\tRESET_MODE\n} from '../constants';\nimport { BaseComponent, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { setProperty } from './props';\nimport { assign, isArray, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {PreactElement} parentDom The parent of the DOM element\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\t/** @type {any} */\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor !== undefined) return null;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._flags & MODE_SUSPENDED) {\n\t\tisHydrating = !!(oldVNode._flags & MODE_HYDRATE);\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\touter: if (typeof newType == 'function') {\n\t\ttry {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\t\t\tconst isClassComponent =\n\t\t\t\t'prototype' in newType && newType.prototype.render;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif (isClassComponent) {\n\t\t\t\t\t// @ts-expect-error The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-expect-error Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new BaseComponent(\n\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (isClassComponent && c._nextState == null) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (isClassComponent && newType.getDerivedStateFromProps != null) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\t\t\tc._vnode = newVNode;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tc.componentWillMount != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidMount != null) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t!c._force &&\n\t\t\t\t\t((c.shouldComponentUpdate != null &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\t\tnewVNode._original === oldVNode._original)\n\t\t\t\t) {\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) {\n\t\t\t\t\t\t// When we are dealing with a bail because of sCU we have to update\n\t\t\t\t\t\t// the props, state and dirty-state.\n\t\t\t\t\t\t// when we are dealing with strict-equality we don't as the child could still\n\t\t\t\t\t\t// be dirtied see #3883\n\t\t\t\t\t\tc.props = newProps;\n\t\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t\tc._dirty = false;\n\t\t\t\t\t}\n\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.some(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != null) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidUpdate != null) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._parentDom = parentDom;\n\t\t\tc._force = false;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif (isClassComponent) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != null) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (isClassComponent && !isNew && c.getSnapshotBeforeUpdate != null) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != null && tmp.type === Fragment && tmp.key == null;\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\n\n\t\t\tdiffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tisArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnamespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._flags &= RESET_MODE;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = null;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tnewVNode._original = null;\n\t\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\t\tif (isHydrating || excessDomChildren != null) {\n\t\t\t\tnewVNode._flags |= isHydrating\n\t\t\t\t\t? MODE_HYDRATE | MODE_SUSPENDED\n\t\t\t\t\t: MODE_HYDRATE;\n\n\t\t\t\twhile (oldDom && oldDom.nodeType === 8 && oldDom.nextSibling) {\n\t\t\t\t\toldDom = oldDom.nextSibling;\n\t\t\t\t}\n\t\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\n\t\t\t\tnewVNode._dom = oldDom;\n\t\t\t} else {\n\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t}\n\t\t\toptions._catchError(e, newVNode, oldVNode);\n\t\t}\n\t} else if (\n\t\texcessDomChildren == null &&\n\t\tnewVNode._original === oldVNode._original\n\t) {\n\t\tnewVNode._children = oldVNode._children;\n\t\tnewVNode._dom = oldVNode._dom;\n\t} else {\n\t\tnewVNode._dom = diffElementNodes(\n\t\t\toldVNode._dom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\t}\n\n\tif ((tmp = options.diffed)) tmp(newVNode);\n}\n\n/**\n * @param {Array<Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {VNode} root\n */\nexport function commitRoot(commitQueue, root, refQueue) {\n\troot._nextDom = undefined;\n\n\tfor (let i = 0; i < refQueue.length; i++) {\n\t\tapplyRef(refQueue[i], refQueue[++i], refQueue[++i]);\n\t}\n\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-expect-error Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-expect-error See above comment on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {PreactElement} dom The DOM element representing the virtual nodes\n * being diffed\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n * @returns {PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating,\n\trefQueue\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = /** @type {string} */ (newVNode.type);\n\t/** @type {any} */\n\tlet i;\n\t/** @type {{ __html?: string }} */\n\tlet newHtml;\n\t/** @type {{ __html?: string }} */\n\tlet oldHtml;\n\t/** @type {ComponentChildren} */\n\tlet newChildren;\n\tlet value;\n\tlet inputValue;\n\tlet checked;\n\n\t// Tracks entering and exiting namespaces when descending through the tree.\n\tif (nodeType === 'svg') namespace = 'http://www.w3.org/2000/svg';\n\telse if (nodeType === 'math')\n\t\tnamespace = 'http://www.w3.org/1998/Math/MathML';\n\telse if (!namespace) namespace = 'http://www.w3.org/1999/xhtml';\n\n\tif (excessDomChildren != null) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tvalue = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tvalue &&\n\t\t\t\t'setAttribute' in value === !!nodeType &&\n\t\t\t\t(nodeType ? value.localName === nodeType : value.nodeType === 3)\n\t\t\t) {\n\t\t\t\tdom = value;\n\t\t\t\texcessDomChildren[i] = null;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == null) {\n\t\tif (nodeType === null) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = document.createElementNS(\n\t\t\tnamespace,\n\t\t\tnodeType,\n\t\t\tnewProps.is && newProps\n\t\t);\n\n\t\t// we are creating a new node, so we can assume this is a new subtree (in\n\t\t// case we are hydrating), this deopts the hydrate\n\t\tif (isHydrating) {\n\t\t\tif (options._hydrationMismatch)\n\t\t\t\toptions._hydrationMismatch(newVNode, excessDomChildren);\n\t\t\tisHydrating = false;\n\t\t}\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = null;\n\t}\n\n\tif (nodeType === null) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\t// If we are in a situation where we are not hydrating but are using\n\t\t// existing DOM (e.g. replaceNode) we should read the existing DOM\n\t\t// attributes to diff them\n\t\tif (!isHydrating && excessDomChildren != null) {\n\t\t\toldProps = {};\n\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\tvalue = dom.attributes[i];\n\t\t\t\toldProps[value.name] = value.value;\n\t\t\t}\n\t\t}\n\n\t\tfor (i in oldProps) {\n\t\t\tvalue = oldProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\toldHtml = value;\n\t\t\t} else if (!(i in newProps)) {\n\t\t\t\tif (\n\t\t\t\t\t(i == 'value' && 'defaultValue' in newProps) ||\n\t\t\t\t\t(i == 'checked' && 'defaultChecked' in newProps)\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tsetProperty(dom, i, null, value, namespace);\n\t\t\t}\n\t\t}\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tfor (i in newProps) {\n\t\t\tvalue = newProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t\tnewChildren = value;\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\tnewHtml = value;\n\t\t\t} else if (i == 'value') {\n\t\t\t\tinputValue = value;\n\t\t\t} else if (i == 'checked') {\n\t\t\t\tchecked = value;\n\t\t\t} else if (\n\t\t\t\t(!isHydrating || typeof value == 'function') &&\n\t\t\t\toldProps[i] !== value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, value, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\tif (\n\t\t\t\t!isHydrating &&\n\t\t\t\t(!oldHtml ||\n\t\t\t\t\t(newHtml.__html !== oldHtml.__html &&\n\t\t\t\t\t\tnewHtml.__html !== dom.innerHTML))\n\t\t\t) {\n\t\t\t\tdom.innerHTML = newHtml.__html;\n\t\t\t}\n\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\tif (oldHtml) dom.innerHTML = '';\n\n\t\t\tdiffChildren(\n\t\t\t\tdom,\n\t\t\t\tisArray(newChildren) ? newChildren : [newChildren],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnodeType === 'foreignObject'\n\t\t\t\t\t? 'http://www.w3.org/1999/xhtml'\n\t\t\t\t\t: namespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != null) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// As above, don't diff props during hydration\n\t\tif (!isHydrating) {\n\t\t\ti = 'value';\n\t\t\tif (nodeType === 'progress' && inputValue == null) {\n\t\t\t\tdom.removeAttribute('value');\n\t\t\t} else if (\n\t\t\t\tinputValue !== undefined &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(inputValue !== dom[i] ||\n\t\t\t\t\t(nodeType === 'progress' && !inputValue) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType === 'option' && inputValue !== oldProps[i]))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, inputValue, oldProps[i], namespace);\n\t\t\t}\n\n\t\t\ti = 'checked';\n\t\t\tif (checked !== undefined && checked !== dom[i]) {\n\t\t\t\tsetProperty(dom, i, checked, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {Ref<any> & { _unmount?: unknown }} ref\n * @param {any} value\n * @param {VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') {\n\t\t\tlet hasRefUnmount = typeof ref._unmount == 'function';\n\t\t\tif (hasRefUnmount) {\n\t\t\t\t// @ts-ignore TS doesn't like moving narrowing checks into variables\n\t\t\t\tref._unmount();\n\t\t\t}\n\n\t\t\tif (!hasRefUnmount || value != null) {\n\t\t\t\t// Store the cleanup function on the function\n\t\t\t\t// instance object itself to avoid shape\n\t\t\t\t// transitioning vnode\n\t\t\t\tref._unmount = ref(value);\n\t\t\t}\n\t\t} else ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {VNode} vnode The virtual node to unmount\n * @param {VNode} parentVNode The parent of the VNode that initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current === vnode._dom) {\n\t\t\tapplyRef(r, null, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != null) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = null;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type != 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\t// Must be set to `undefined` to properly clean up `_nextDom`\n\t// for which `null` is a valid value. See comment in `create-element.js`\n\tvnode._component = vnode._parent = vnode._dom = vnode._nextDom = undefined;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {ComponentChild} vnode The virtual node to render\n * @param {PreactElement} parentDom The DOM element to render into\n * @param {PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode == 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? null\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = ((!isHydrating && replaceNode) || parentDom)._children =\n\t\tcreateElement(Fragment, null, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [],\n\t\trefQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.namespaceURI,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t\t? null\n\t\t\t\t: parentDom.firstChild\n\t\t\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t\t\t: null,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t\t? oldVNode._dom\n\t\t\t\t: parentDom.firstChild,\n\t\tisHydrating,\n\t\trefQueue\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode, refQueue);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {ComponentChild} vnode The virtual node to render\n * @param {PreactElement} parentDom The DOM element to update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its\n * children.\n * @param {VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<ComponentChildren>} rest Any additional arguments will be used\n * as replacement children.\n * @returns {VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\n\tlet defaultProps;\n\n\tif (vnode.type && vnode.type.defaultProps) {\n\t\tdefaultProps = vnode.type.defaultProps;\n\t}\n\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse if (props[i] === undefined && defaultProps !== undefined) {\n\t\t\tnormalizedProps[i] = defaultProps[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tnull\n\t);\n}\n", "/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {VNode} vnode The vnode that threw the error that was caught (except\n * for unmounting when this parameter is the highest parent that was being\n * unmounted)\n * @param {VNode} [oldVNode]\n * @param {ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {Component} */\n\tlet component,\n\t\t/** @type {ComponentType} */\n\t\tctor,\n\t\t/** @type {boolean} */\n\t\thandled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != null) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != null) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n", "import { options as _options } from 'preact';\n\n/** @type {number} */\nlet currentIndex;\n\n/** @type {import('./internal').Component} */\nlet currentComponent;\n\n/** @type {import('./internal').Component} */\nlet previousComponent;\n\n/** @type {number} */\nlet currentHook = 0;\n\n/** @type {Array<import('./internal').Component>} */\nlet afterPaintEffects = [];\n\n// Cast to use internal Options type\nconst options = /** @type {import('./internal').Options} */ (_options);\n\nlet oldBeforeDiff = options._diff;\nlet oldBeforeRender = options._render;\nlet oldAfterDiff = options.diffed;\nlet oldCommit = options._commit;\nlet oldBeforeUnmount = options.unmount;\nlet oldRoot = options._root;\n\nconst RAF_TIMEOUT = 100;\nlet prevRaf;\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions._diff = vnode => {\n\tcurrentComponent = null;\n\tif (oldBeforeDiff) oldBeforeDiff(vnode);\n};\n\noptions._root = (vnode, parentDom) => {\n\tif (vnode && parentDom._children && parentDom._children._mask) {\n\t\tvnode._mask = parentDom._children._mask;\n\t}\n\n\tif (oldRoot) oldRoot(vnode, parentDom);\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions._render = vnode => {\n\tif (oldBeforeRender) oldBeforeRender(vnode);\n\n\tcurrentComponent = vnode._component;\n\tcurrentIndex = 0;\n\n\tconst hooks = currentComponent.__hooks;\n\tif (hooks) {\n\t\tif (previousComponent === currentComponent) {\n\t\t\thooks._pendingEffects = [];\n\t\t\tcurrentComponent._renderCallbacks = [];\n\t\t\thooks._list.forEach(hookItem => {\n\t\t\t\tif (hookItem._nextValue) {\n\t\t\t\t\thookItem._value = hookItem._nextValue;\n\t\t\t\t}\n\t\t\t\thookItem._pendingArgs = hookItem._nextValue = undefined;\n\t\t\t});\n\t\t} else {\n\t\t\thooks._pendingEffects.forEach(invokeCleanup);\n\t\t\thooks._pendingEffects.forEach(invokeEffect);\n\t\t\thooks._pendingEffects = [];\n\t\t\tcurrentIndex = 0;\n\t\t}\n\t}\n\tpreviousComponent = currentComponent;\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.diffed = vnode => {\n\tif (oldAfterDiff) oldAfterDiff(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\tif (c.__hooks._pendingEffects.length) afterPaint(afterPaintEffects.push(c));\n\t\tc.__hooks._list.forEach(hookItem => {\n\t\t\tif (hookItem._pendingArgs) {\n\t\t\t\thookItem._args = hookItem._pendingArgs;\n\t\t\t}\n\t\t\thookItem._pendingArgs = undefined;\n\t\t});\n\t}\n\tpreviousComponent = currentComponent = null;\n};\n\n// TODO: Improve typing of commitQueue parameter\n/** @type {(vnode: import('./internal').VNode, commitQueue: any) => void} */\noptions._commit = (vnode, commitQueue) => {\n\tcommitQueue.some(component => {\n\t\ttry {\n\t\t\tcomponent._renderCallbacks.forEach(invokeCleanup);\n\t\t\tcomponent._renderCallbacks = component._renderCallbacks.filter(cb =>\n\t\t\t\tcb._value ? invokeEffect(cb) : true\n\t\t\t);\n\t\t} catch (e) {\n\t\t\tcommitQueue.some(c => {\n\t\t\t\tif (c._renderCallbacks) c._renderCallbacks = [];\n\t\t\t});\n\t\t\tcommitQueue = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t});\n\n\tif (oldCommit) oldCommit(vnode, commitQueue);\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.unmount = vnode => {\n\tif (oldBeforeUnmount) oldBeforeUnmount(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\tlet hasErrored;\n\t\tc.__hooks._list.forEach(s => {\n\t\t\ttry {\n\t\t\t\tinvokeCleanup(s);\n\t\t\t} catch (e) {\n\t\t\t\thasErrored = e;\n\t\t\t}\n\t\t});\n\t\tc.__hooks = undefined;\n\t\tif (hasErrored) options._catchError(hasErrored, c._vnode);\n\t}\n};\n\n/**\n * Get a hook's state from the currentComponent\n * @param {number} index The index of the hook to get\n * @param {number} type The index of the hook to get\n * @returns {any}\n */\nfunction getHookState(index, type) {\n\tif (options._hook) {\n\t\toptions._hook(currentComponent, index, currentHook || type);\n\t}\n\tcurrentHook = 0;\n\n\t// Largely inspired by:\n\t// * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\n\t// * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\n\t// Other implementations to look at:\n\t// * https://codesandbox.io/s/mnox05qp8\n\tconst hooks =\n\t\tcurrentComponent.__hooks ||\n\t\t(currentComponent.__hooks = {\n\t\t\t_list: [],\n\t\t\t_pendingEffects: []\n\t\t});\n\n\tif (index >= hooks._list.length) {\n\t\thooks._list.push({});\n\t}\n\n\treturn hooks._list[index];\n}\n\n/**\n * @template {unknown} S\n * @param {import('./index').Dispatch<import('./index').StateUpdater<S>>} [initialState]\n * @returns {[S, (state: S) => void]}\n */\nexport function useState(initialState) {\n\tcurrentHook = 1;\n\treturn useReducer(invokeOrReturn, initialState);\n}\n\n/**\n * @template {unknown} S\n * @template {unknown} A\n * @param {import('./index').Reducer<S, A>} reducer\n * @param {import('./index').Dispatch<import('./index').StateUpdater<S>>} initialState\n * @param {(initialState: any) => void} [init]\n * @returns {[ S, (state: S) => void ]}\n */\nexport function useReducer(reducer, initialState, init) {\n\t/** @type {import('./internal').ReducerHookState} */\n\tconst hookState = getHookState(currentIndex++, 2);\n\thookState._reducer = reducer;\n\tif (!hookState._component) {\n\t\thookState._value = [\n\t\t\t!init ? invokeOrReturn(undefined, initialState) : init(initialState),\n\n\t\t\taction => {\n\t\t\t\tconst currentValue = hookState._nextValue\n\t\t\t\t\t? hookState._nextValue[0]\n\t\t\t\t\t: hookState._value[0];\n\t\t\t\tconst nextValue = hookState._reducer(currentValue, action);\n\n\t\t\t\tif (currentValue !== nextValue) {\n\t\t\t\t\thookState._nextValue = [nextValue, hookState._value[1]];\n\t\t\t\t\thookState._component.setState({});\n\t\t\t\t}\n\t\t\t}\n\t\t];\n\n\t\thookState._component = currentComponent;\n\n\t\tif (!currentComponent._hasScuFromHooks) {\n\t\t\tcurrentComponent._hasScuFromHooks = true;\n\t\t\tlet prevScu = currentComponent.shouldComponentUpdate;\n\t\t\tconst prevCWU = currentComponent.componentWillUpdate;\n\n\t\t\t// If we're dealing with a forced update `shouldComponentUpdate` will\n\t\t\t// not be called. But we use that to update the hook values, so we\n\t\t\t// need to call it.\n\t\t\tcurrentComponent.componentWillUpdate = function (p, s, c) {\n\t\t\t\tif (this._force) {\n\t\t\t\t\tlet tmp = prevScu;\n\t\t\t\t\t// Clear to avoid other sCU hooks from being called\n\t\t\t\t\tprevScu = undefined;\n\t\t\t\t\tupdateHookState(p, s, c);\n\t\t\t\t\tprevScu = tmp;\n\t\t\t\t}\n\n\t\t\t\tif (prevCWU) prevCWU.call(this, p, s, c);\n\t\t\t};\n\n\t\t\t// This SCU has the purpose of bailing out after repeated updates\n\t\t\t// to stateful hooks.\n\t\t\t// we store the next value in _nextValue[0] and keep doing that for all\n\t\t\t// state setters, if we have next states and\n\t\t\t// all next states within a component end up being equal to their original state\n\t\t\t// we are safe to bail out for this specific component.\n\t\t\t/**\n\t\t\t *\n\t\t\t * @type {import('./internal').Component[\"shouldComponentUpdate\"]}\n\t\t\t */\n\t\t\t// @ts-ignore - We don't use TS to downtranspile\n\t\t\t// eslint-disable-next-line no-inner-declarations\n\t\t\tfunction updateHookState(p, s, c) {\n\t\t\t\tif (!hookState._component.__hooks) return true;\n\n\t\t\t\t/** @type {(x: import('./internal').HookState) => x is import('./internal').ReducerHookState} */\n\t\t\t\tconst isStateHook = x => !!x._component;\n\t\t\t\tconst stateHooks =\n\t\t\t\t\thookState._component.__hooks._list.filter(isStateHook);\n\n\t\t\t\tconst allHooksEmpty = stateHooks.every(x => !x._nextValue);\n\t\t\t\t// When we have no updated hooks in the component we invoke the previous SCU or\n\t\t\t\t// traverse the VDOM tree further.\n\t\t\t\tif (allHooksEmpty) {\n\t\t\t\t\treturn prevScu ? prevScu.call(this, p, s, c) : true;\n\t\t\t\t}\n\n\t\t\t\t// We check whether we have components with a nextValue set that\n\t\t\t\t// have values that aren't equal to one another this pushes\n\t\t\t\t// us to update further down the tree\n\t\t\t\tlet shouldUpdate = false;\n\t\t\t\tstateHooks.forEach(hookItem => {\n\t\t\t\t\tif (hookItem._nextValue) {\n\t\t\t\t\t\tconst currentValue = hookItem._value[0];\n\t\t\t\t\t\thookItem._value = hookItem._nextValue;\n\t\t\t\t\t\thookItem._nextValue = undefined;\n\t\t\t\t\t\tif (currentValue !== hookItem._value[0]) shouldUpdate = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn shouldUpdate || hookState._component.props !== p\n\t\t\t\t\t? prevScu\n\t\t\t\t\t\t? prevScu.call(this, p, s, c)\n\t\t\t\t\t\t: true\n\t\t\t\t\t: false;\n\t\t\t}\n\n\t\t\tcurrentComponent.shouldComponentUpdate = updateHookState;\n\t\t}\n\t}\n\n\treturn hookState._nextValue || hookState._value;\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 3);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._pendingArgs = args;\n\n\t\tcurrentComponent.__hooks._pendingEffects.push(state);\n\t}\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useLayoutEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 4);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._pendingArgs = args;\n\n\t\tcurrentComponent._renderCallbacks.push(state);\n\t}\n}\n\n/** @type {(initialValue: unknown) => unknown} */\nexport function useRef(initialValue) {\n\tcurrentHook = 5;\n\treturn useMemo(() => ({ current: initialValue }), []);\n}\n\n/**\n * @param {object} ref\n * @param {() => object} createHandle\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useImperativeHandle(ref, createHandle, args) {\n\tcurrentHook = 6;\n\tuseLayoutEffect(\n\t\t() => {\n\t\t\tif (typeof ref == 'function') {\n\t\t\t\tref(createHandle());\n\t\t\t\treturn () => ref(null);\n\t\t\t} else if (ref) {\n\t\t\t\tref.current = createHandle();\n\t\t\t\treturn () => (ref.current = null);\n\t\t\t}\n\t\t},\n\t\targs == null ? args : args.concat(ref)\n\t);\n}\n\n/**\n * @template {unknown} T\n * @param {() => T} factory\n * @param {unknown[]} args\n * @returns {T}\n */\nexport function useMemo(factory, args) {\n\t/** @type {import('./internal').MemoHookState<T>} */\n\tconst state = getHookState(currentIndex++, 7);\n\tif (argsChanged(state._args, args)) {\n\t\tstate._value = factory();\n\t\tstate._args = args;\n\t\tstate._factory = factory;\n\t}\n\n\treturn state._value;\n}\n\n/**\n * @param {() => void} callback\n * @param {unknown[]} args\n * @returns {() => void}\n */\nexport function useCallback(callback, args) {\n\tcurrentHook = 8;\n\treturn useMemo(() => callback, args);\n}\n\n/**\n * @param {import('./internal').PreactContext} context\n */\nexport function useContext(context) {\n\tconst provider = currentComponent.context[context._id];\n\t// We could skip this call here, but than we'd not call\n\t// `options._hook`. We need to do that in order to make\n\t// the devtools aware of this hook.\n\t/** @type {import('./internal').ContextHookState} */\n\tconst state = getHookState(currentIndex++, 9);\n\t// The devtools needs access to the context object to\n\t// be able to pull of the default value when no provider\n\t// is present in the tree.\n\tstate._context = context;\n\tif (!provider) return context._defaultValue;\n\t// This is probably not safe to convert to \"!\"\n\tif (state._value == null) {\n\t\tstate._value = true;\n\t\tprovider.sub(currentComponent);\n\t}\n\treturn provider.props.value;\n}\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\n */\nexport function useDebugValue(value, formatter) {\n\tif (options.useDebugValue) {\n\t\toptions.useDebugValue(\n\t\t\tformatter ? formatter(value) : /** @type {any}*/ (value)\n\t\t);\n\t}\n}\n\n/**\n * @param {(error: unknown, errorInfo: import('preact').ErrorInfo) => void} cb\n * @returns {[unknown, () => void]}\n */\nexport function useErrorBoundary(cb) {\n\t/** @type {import('./internal').ErrorBoundaryHookState} */\n\tconst state = getHookState(currentIndex++, 10);\n\tconst errState = useState();\n\tstate._value = cb;\n\tif (!currentComponent.componentDidCatch) {\n\t\tcurrentComponent.componentDidCatch = (err, errorInfo) => {\n\t\t\tif (state._value) state._value(err, errorInfo);\n\t\t\terrState[1](err);\n\t\t};\n\t}\n\treturn [\n\t\terrState[0],\n\t\t() => {\n\t\t\terrState[1](undefined);\n\t\t}\n\t];\n}\n\n/** @type {() => string} */\nexport function useId() {\n\t/** @type {import('./internal').IdHookState} */\n\tconst state = getHookState(currentIndex++, 11);\n\tif (!state._value) {\n\t\t// Grab either the root node or the nearest async boundary node.\n\t\t/** @type {import('./internal.d').VNode} */\n\t\tlet root = currentComponent._vnode;\n\t\twhile (root !== null && !root._mask && root._parent !== null) {\n\t\t\troot = root._parent;\n\t\t}\n\n\t\tlet mask = root._mask || (root._mask = [0, 0]);\n\t\tstate._value = 'P' + mask[0] + '-' + mask[1]++;\n\t}\n\n\treturn state._value;\n}\n\n/**\n * After paint effects consumer.\n */\nfunction flushAfterPaintEffects() {\n\tlet component;\n\twhile ((component = afterPaintEffects.shift())) {\n\t\tif (!component._parentDom || !component.__hooks) continue;\n\t\ttry {\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeCleanup);\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeEffect);\n\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t} catch (e) {\n\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t}\n}\n\nlet HAS_RAF = typeof requestAnimationFrame == 'function';\n\n/**\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\n * the next browser frame.\n *\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\n * even if RAF doesn't fire (for example if the browser tab is not visible)\n *\n * @param {() => void} callback\n */\nfunction afterNextFrame(callback) {\n\tconst done = () => {\n\t\tclearTimeout(timeout);\n\t\tif (HAS_RAF) cancelAnimationFrame(raf);\n\t\tsetTimeout(callback);\n\t};\n\tconst timeout = setTimeout(done, RAF_TIMEOUT);\n\n\tlet raf;\n\tif (HAS_RAF) {\n\t\traf = requestAnimationFrame(done);\n\t}\n}\n\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\n// Perhaps this is not such a big deal.\n/**\n * Schedule afterPaintEffects flush after the browser paints\n * @param {number} newQueueLength\n * @returns {void}\n */\nfunction afterPaint(newQueueLength) {\n\tif (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\n\t\tprevRaf = options.requestAnimationFrame;\n\t\t(prevRaf || afterNextFrame)(flushAfterPaintEffects);\n\t}\n}\n\n/**\n * @param {import('./internal').HookState} hook\n * @returns {void}\n */\nfunction invokeCleanup(hook) {\n\t// A hook cleanup can introduce a call to render which creates a new root, this will call options.vnode\n\t// and move the currentComponent away.\n\tconst comp = currentComponent;\n\tlet cleanup = hook._cleanup;\n\tif (typeof cleanup == 'function') {\n\t\thook._cleanup = undefined;\n\t\tcleanup();\n\t}\n\n\tcurrentComponent = comp;\n}\n\n/**\n * Invoke a Hook's effect\n * @param {import('./internal').EffectHookState} hook\n * @returns {void}\n */\nfunction invokeEffect(hook) {\n\t// A hook call can introduce a call to render which creates a new root, this will call options.vnode\n\t// and move the currentComponent away.\n\tconst comp = currentComponent;\n\thook._cleanup = hook._value();\n\tcurrentComponent = comp;\n}\n\n/**\n * @param {unknown[]} oldArgs\n * @param {unknown[]} newArgs\n * @returns {boolean}\n */\nfunction argsChanged(oldArgs, newArgs) {\n\treturn (\n\t\t!oldArgs ||\n\t\toldArgs.length !== newArgs.length ||\n\t\tnewArgs.some((arg, index) => arg !== oldArgs[index])\n\t);\n}\n\n/**\n * @template Arg\n * @param {Arg} arg\n * @param {(arg: Arg) => any} f\n * @returns {any}\n */\nfunction invokeOrReturn(arg, f) {\n\treturn typeof f == 'function' ? f(arg) : f;\n}\n"], "mappings": ";;;;;;AACa;;;;;;;;;;;;;;;ACUN,SAASA,EAAOC,IAAKC,IAAAA;AAE3B,WAASC,MAAKD,GAAOD,CAAAA,GAAIE,EAAAA,IAAKD,GAAMC,EAAAA;AACpC,SAA6BF;AAC9B;AAQgB,SAAAG,EAAWC,IAAAA;AACtBA,EAAAA,MAAQA,GAAKC,cAAYD,GAAKC,WAAWC,YAAYF,EAAAA;AAC1D;AEXO,SAASG,EAAcC,IAAMP,IAAOQ,IAAAA;AAC1C,MACCC,IACAC,IACAT,IAHGU,KAAkB,CAAA;AAItB,OAAKV,MAAKD,GACA,UAALC,KAAYQ,KAAMT,GAAMC,EAAAA,IACd,SAALA,KAAYS,KAAMV,GAAMC,EAAAA,IAC5BU,GAAgBV,EAAAA,IAAKD,GAAMC,EAAAA;AAUjC,MAPIW,UAAUC,SAAS,MACtBF,GAAgBH,WACfI,UAAUC,SAAS,IAAIC,EAAMC,KAAKH,WAAW,CAAA,IAAKJ,KAKjC,cAAA,OAARD,MAA2C,QAArBA,GAAKS,aACrC,MAAKf,MAAKM,GAAKS,aAAAA,YACVL,GAAgBV,EAAAA,MACnBU,GAAgBV,EAAAA,IAAKM,GAAKS,aAAaf,EAAAA;AAK1C,SAAOgB,EAAYV,IAAMI,IAAiBF,IAAKC,IAAK,IAAA;AACrD;AAcO,SAASO,EAAYV,IAAMP,IAAOS,IAAKC,IAAKQ,IAAAA;AAIlD,MAAMC,KAAQ,EACbZ,MAAAA,IACAP,OAAAA,IACAS,KAAAA,IACAC,KAAAA,IACAU,KAAW,MACXC,IAAS,MACTC,KAAQ,GACRC,KAAM,MAKNC,KAAAA,QACAC,KAAY,MACZC,aAAAA,QACAC,KAAuB,QAAZT,KAAAA,EAAqBU,IAAUV,IAC1CW,KAAAA,IACAC,KAAQ,EAAA;AAMT,SAFgB,QAAZZ,MAAqC,QAAjBa,EAAQZ,SAAeY,EAAQZ,MAAMA,EAAAA,GAEtDA;AACR;AAEO,SAASa,IAAAA;AACf,SAAO,EAAEC,SAAS,KAAA;AACnB;AAAA,SAEgBC,EAASlC,IAAAA;AACxB,SAAOA,GAAMQ;AACd;AAAA,SC/EgB2B,EAAcnC,IAAOoC,IAAAA;AACpCC,OAAKrC,QAAQA,IACbqC,KAAKD,UAAUA;AAChB;AA0EgB,SAAAE,EAAcnB,IAAOoB,IAAAA;AACpC,MAAkB,QAAdA,GAEH,QAAOpB,GAAKE,KACTiB,EAAcnB,GAAKE,IAAUF,GAAKU,MAAU,CAAA,IAC5C;AAIJ,WADIW,IACGD,KAAapB,GAAKC,IAAWP,QAAQ0B,KAG3C,KAAe,SAFfC,KAAUrB,GAAKC,IAAWmB,EAAAA,MAEa,QAAhBC,GAAOjB,IAI7B,QAAOiB,GAAOjB;AAShB,SAA4B,cAAA,OAAdJ,GAAMZ,OAAqB+B,EAAcnB,EAAAA,IAAS;AACjE;AA2CA,SAASsB,EAAwBtB,IAAAA;AAAjC,MAGWlB,IACJyC;AAHN,MAA+B,SAA1BvB,KAAQA,GAAKE,OAAyC,QAApBF,GAAKM,KAAqB;AAEhE,SADAN,GAAKI,MAAQJ,GAAKM,IAAYkB,OAAO,MAC5B1C,KAAI,GAAGA,KAAIkB,GAAKC,IAAWP,QAAQZ,KAE3C,KAAa,SADTyC,KAAQvB,GAAKC,IAAWnB,EAAAA,MACO,QAAdyC,GAAKnB,KAAe;AACxCJ,MAAAA,GAAKI,MAAQJ,GAAKM,IAAYkB,OAAOD,GAAKnB;AAC1C;IACD;AAGD,WAAOkB,EAAwBtB,EAAAA;EAChC;AACD;AA4BgB,SAAAyB,EAAcC,IAAAA;AAAAA,GAAAA,CAE1BA,GAACrB,QACDqB,GAACrB,MAAAA,SACFsB,EAAcC,KAAKF,EAAAA,KAAAA,CAClBG,EAAOC,SACTC,MAAiBnB,EAAQoB,wBAEzBD,IAAenB,EAAQoB,sBACNC,GAAOJ,CAAAA;AAE1B;AASA,SAASA,IAAAA;AAAT,MACKH,IAMEQ,IAzGkBC,IAOjBC,IANHC,IACHC,IACAC,IACAC;AAmGD,OAHAb,EAAcc,KAAKC,CAAAA,GAGXhB,KAAIC,EAAcgB,MAAAA,IACrBjB,CAAAA,GAACrB,QACA6B,KAAoBP,EAAcjC,QAlGjC0C,KAAAA,QALNE,MADGD,MADoBF,KA0GNT,IAzGMlB,KACNJ,KACjBmC,KAAc,CAAA,GACdC,KAAW,CAAA,GAERL,GAASS,SACNR,KAAWzD,EAAO,CAAA,GAAI0D,EAAAA,GACpB7B,MAAa6B,GAAQ7B,MAAa,GACtCI,EAAQZ,SAAOY,EAAQZ,MAAMoC,EAAAA,GAEjCS,EACCV,GAASS,KACTR,IACAC,IACAF,GAASW,KACTX,GAASS,IAAYG,cJzII,KI0IzBV,GAAQ1B,MAAyB,CAAC2B,EAAAA,IAAU,MAC5CC,IACU,QAAVD,KAAiBnB,EAAckB,EAAAA,IAAYC,IAAAA,CAAAA,EJ5IlB,KI6ItBD,GAAQ1B,MACX6B,EAAAA,GAGDJ,GAAQ5B,MAAa6B,GAAQ7B,KAC7B4B,GAAQlC,GAAAD,IAAmBmC,GAAQ1B,GAAAA,IAAW0B,IAC9CY,EAAWT,IAAaH,IAAUI,EAAAA,GAE9BJ,GAAQhC,OAASkC,MACpBhB,EAAwBc,EAAAA,IA8EpBT,EAAcjC,SAASwC,MAI1BP,EAAcc,KAAKC,CAAAA;AAItBb,IAAOC,MAAkB;AAC1B;AGlNO,SAASmB,EACfC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAjB,IACAD,IACAmB,IACAjB,IAAAA;AAXM,MAaF1D,IAEHuD,IAEAqB,IAEAC,IAEAC,IAKGC,KAAeR,MAAkBA,GAAcpD,OAAe6D,GAE9DC,KAAoBZ,GAAazD;AAMrC,OAJA0D,GAAc/C,MAAYiC,IAC1B0B,EAA0BZ,IAAgBD,IAAcU,EAAAA,GACxDvB,KAASc,GAAc/C,KAElBvB,KAAI,GAAGA,KAAIiF,IAAmBjF,KAEhB,UADlB4E,KAAaN,GAAcnD,IAAWnB,EAAAA,OAMrCuD,KAAAA,OADGqB,GAAUhD,MACFuD,IAEAJ,GAAYH,GAAUhD,GAAAA,KAAYuD,GAI9CP,GAAUhD,MAAU5B,IAGpB+D,EACCK,IACAQ,IACArB,IACAiB,IACAC,IACAC,IACAjB,IACAD,IACAmB,IACAjB,EAAAA,GAIDmB,KAASD,GAAUtD,KACfsD,GAAWnE,OAAO8C,GAAS9C,OAAOmE,GAAWnE,QAC5C8C,GAAS9C,OACZ2E,EAAS7B,GAAS9C,KAAK,MAAMmE,EAAAA,GAE9BlB,GAASZ,KACR8B,GAAWnE,KACXmE,GAAUpD,OAAeqD,IACzBD,EAAAA,IAImB,QAAjBE,MAAmC,QAAVD,OAC5BC,KAAgBD,KPpGS,QOwGzBD,GAAU/C,OACV0B,GAAQpC,QAAeyD,GAAUzD,MAEjCqC,KAAS6B,EAAOT,IAAYpB,IAAQY,EAAAA,IAEV,cAAA,OAAnBQ,GAAWtE,QAAAA,WAClBsE,GAAUrD,MAKViC,KAASoB,GAAUrD,MACTsD,OACVrB,KAASqB,GAAOS,cAQjBV,GAAUrD,MAAAA,QAGVqD,GAAU/C,OAAAA;AAaXyC,EAAAA,GAAc/C,MAAYiC,IAC1Bc,GAAchD,MAAQwD;AACvB;AAOA,SAASI,EAA0BZ,IAAgBD,IAAcU,IAAAA;AAAjE,MAEK/E,IAEA4E,IAEArB,IA+DGgC,IAOAC,IApEDP,KAAoBZ,GAAazD,QACnC6E,KAAoBV,GAAYnE,QACnC8E,KAAuBD,IAEpBE,KAAO;AAGX,OADArB,GAAcnD,MAAa,CAAA,GACtBnB,KAAI,GAAGA,KAAIiF,IAAmBjF,KAMnB,UAHf4E,KAAaP,GAAarE,EAAAA,MAIJ,aAAA,OAAd4E,MACc,cAAA,OAAdA,MA8CFW,KAAcvF,KAAI2F,KA/BvBf,KAAaN,GAAcnD,IAAWnB,EAAAA,IANjB,YAAA,OAAd4E,MACc,YAAA,OAAdA,MAEc,YAAA,OAAdA,MACPA,GAAWnD,eAAemE,SAEiB5E,EAC1C,MACA4D,IACA,MACA,MACA,IAAA,IAESiB,EAAQjB,EAAAA,IACyB5D,EAC1CiB,GACA,EAAE1B,UAAUqE,GAAAA,GACZ,MACA,MACA,IAAA,IAAA,WAESA,GAAWnD,eAA6BmD,GAAUvD,MAAU,IAK3BL,EAC1C4D,GAAWtE,MACXsE,GAAW7E,OACX6E,GAAWpE,KACXoE,GAAWnE,MAAMmE,GAAWnE,MAAM,MAClCmE,GAAUlD,GAAAA,IAGgCkD,IAIlCxD,KAAWkD,IACrBM,GAAUvD,MAAUiD,GAAcjD,MAAU,GAY5CkC,KAAW,MAAA,QAPLiC,KAAiBZ,GAAUhD,MAAUkE,EAC1ClB,IACAG,IACAQ,IACAG,EAAAA,OAMAA,OADAnC,KAAWwB,GAAYS,EAAAA,OAGtBjC,GAAQ1B,OP5OW,UOmPU,QAAZ0B,MAA2C,SAAvBA,GAAQ7B,OAAAA,MAG1C8D,MACHG,MAI6B,cAAA,OAAnBf,GAAWtE,SACrBsE,GAAU/C,OP9Pc,UOgQf2D,OAAkBD,OAiBxBC,MAAiBD,KAAc,IAClCI,OACUH,MAAiBD,KAAc,IACzCI,QAEIH,KAAgBD,KACnBI,OAEAA,MAMDf,GAAU/C,OP/Rc,WO+KzB+C,KAAaN,GAAcnD,IAAWnB,EAAAA,IAAK;AAyH7C,MAAI0F,GACH,MAAK1F,KAAI,GAAGA,KAAIyF,IAAmBzF,KAElB,UADhBuD,KAAWwB,GAAY/E,EAAAA,MACiC,MPzSpC,SOySKuD,GAAQ1B,SAC5B0B,GAAQjC,OAASgD,GAAc/C,QAClC+C,GAAc/C,MAAYc,EAAckB,EAAAA,IAGzCwC,EAAQxC,IAAUA,EAAAA;AAItB;AAQA,SAAS8B,EAAOW,IAAaxC,IAAQY,IAAAA;AAArC,MAIM7D,IACKP;AAFV,MAA+B,cAAA,OAApBgG,GAAY1F,MAAoB;AAE1C,SADIC,KAAWyF,GAAW7E,KACjBnB,KAAI,GAAGO,MAAYP,KAAIO,GAASK,QAAQZ,KAC5CO,CAAAA,GAASP,EAAAA,MAKZO,GAASP,EAAAA,EAAEoB,KAAW4E,IACtBxC,KAAS6B,EAAO9E,GAASP,EAAAA,GAAIwD,IAAQY,EAAAA;AAIvC,WAAOZ;EACR;AAAWwC,EAAAA,GAAW1E,OAASkC,OAC1BA,MAAUwC,GAAY1F,QAAAA,CAAS8D,GAAU6B,SAASzC,EAAAA,MACrDA,KAASnB,EAAc2D,EAAAA,IAExB5B,GAAU8B,aAAaF,GAAW1E,KAAOkC,MAAU,IAAA,GACnDA,KAASwC,GAAW1E;AAGrB,KAAA;AACCkC,IAAAA,KAASA,MAAUA,GAAO8B;EAAAA,SACR,QAAV9B,MAAsC,MAApBA,GAAO2C;AAElC,SAAO3C;AACR;AAQgB,SAAA4C,EAAa7F,IAAU8F,IAAAA;AAUtC,SATAA,KAAMA,MAAO,CAAA,GACG,QAAZ9F,MAAuC,aAAA,OAAZA,OACpBsF,EAAQtF,EAAAA,IAClBA,GAAS+F,KAAK,SAAA7D,IAAAA;AACb2D,MAAa3D,IAAO4D,EAAAA;EACrB,CAAA,IAEAA,GAAIvD,KAAKvC,EAAAA,IAEH8F;AACR;AASA,SAASP,EACRlB,IACAG,IACAQ,IACAG,IAAAA;AAJD,MAMOlF,KAAMoE,GAAWpE,KACjBF,KAAOsE,GAAWtE,MACpBiG,KAAIhB,KAAc,GAClBiB,KAAIjB,KAAc,GAClBhC,KAAWwB,GAAYQ,EAAAA;AAc3B,MACc,SAAbhC,MACCA,MACA/C,MAAO+C,GAAS/C,OAChBF,OAASiD,GAASjD,QACc,MPjZZ,SOiZnBiD,GAAQ1B,KAEV,QAAO0D;AACD,MAXNG,MACa,QAAZnC,MAAoD,MP1YhC,SO0YCA,GAAQ1B,OAA2B,IAAI,GAW7D,QAAO0E,MAAK,KAAKC,KAAIzB,GAAYnE,UAAQ;AACxC,QAAI2F,MAAK,GAAG;AAEX,WADAhD,KAAWwB,GAAYwB,EAAAA,MAGU,MP1Zd,SO0ZjBhD,GAAQ1B,QACTrB,MAAO+C,GAAS/C,OAChBF,OAASiD,GAASjD,KAElB,QAAOiG;AAERA,MAAAA;IACD;AAEA,QAAIC,KAAIzB,GAAYnE,QAAQ;AAE3B,WADA2C,KAAWwB,GAAYyB,EAAAA,MAGU,MPvad,SOuajBjD,GAAQ1B,QACTrB,MAAO+C,GAAS/C,OAChBF,OAASiD,GAASjD,KAElB,QAAOkG;AAERA,MAAAA;IACD;EACD;AAGD,SAAA;AACD;AFvbA,SAASC,EAASC,IAAOlG,IAAKmG,IAAAA;AACd,UAAXnG,GAAI,CAAA,IACPkG,GAAME,YAAYpG,IAAc,QAATmG,KAAgB,KAAKA,EAAAA,IAE5CD,GAAMlG,EAAAA,IADa,QAATmG,KACG,KACa,YAAA,OAATA,MAAqBE,EAAmBC,KAAKtG,EAAAA,IACjDmG,KAEAA,KAAQ;AAEvB;AAuBO,SAASC,EAAYG,IAAKC,IAAML,IAAOM,IAAUxC,IAAAA;AACvD,MAAIyC;AAEJC,IAAG,KAAa,YAATH,GACN,KAAoB,YAAA,OAATL,GACVI,CAAAA,GAAIL,MAAMU,UAAUT;OACd;AAKN,QAJuB,YAAA,OAAZM,OACVF,GAAIL,MAAMU,UAAUH,KAAW,KAG5BA,GACH,MAAKD,MAAQC,GACNN,CAAAA,MAASK,MAAQL,MACtBF,EAASM,GAAIL,OAAOM,IAAM,EAAA;AAK7B,QAAIL,GACH,MAAKK,MAAQL,GACPM,CAAAA,MAAYN,GAAMK,EAAAA,MAAUC,GAASD,EAAAA,KACzCP,EAASM,GAAIL,OAAOM,IAAML,GAAMK,EAAAA,CAAAA;EAIpC;WAGoB,QAAZA,GAAK,CAAA,KAA0B,QAAZA,GAAK,CAAA,EAChCE,CAAAA,KACCF,QAAUA,KAAOA,GAAKK,QAAQ,+BAA+B,IAAA,IAQ7DL,KAJAA,GAAKM,YAAAA,KAAiBP,MACb,iBAATC,MACS,gBAATA,KAEOA,GAAKM,YAAAA,EAAczG,MAAM,CAAA,IACrBmG,GAAKnG,MAAM,CAAA,GAElBkG,GAAGQ,MAAaR,GAAGQ,IAAc,CAAA,IACtCR,GAAGQ,EAAYP,KAAOE,EAAAA,IAAcP,IAEhCA,KACEM,KAQJN,GAAMa,IAAYP,GAASO,KAP3Bb,GAAMa,IAAYC,GAClBV,GAAIW,iBACHV,IACAE,KAAaS,IAAoBC,GACjCV,EAAAA,KAMFH,GAAIc,oBACHb,IACAE,KAAaS,IAAoBC,GACjCV,EAAAA;OAGI;AACN,QAAiB,gCAAbzC,GAIHuC,CAAAA,KAAOA,GAAKK,QAAQ,eAAe,GAAA,EAAKA,QAAQ,UAAU,GAAA;aAElD,WAARL,MACQ,YAARA,MACQ,UAARA,MACQ,UAARA,MACQ,UAARA,MAGQ,cAARA,MACQ,cAARA,MACQ,aAARA,MACQ,aAARA,MACQ,UAARA,MACQ,aAARA,MACAA,MAAQD,GAER,KAAA;AACCA,MAAAA,GAAIC,EAAAA,IAAiB,QAATL,KAAgB,KAAKA;AAEjC,YAAMQ;IACK,SAAHW,IAAAA;IAAG;AAUO,kBAAA,OAATnB,OAES,QAATA,MAAAA,UAAkBA,MAA+B,QAAZK,GAAK,CAAA,IAGpDD,GAAIgB,gBAAgBf,EAAAA,IAFpBD,GAAIiB,aAAahB,IAAc,aAARA,MAA8B,KAATL,KAAgB,KAAKA,EAAAA;EAInE;AACD;AAOA,SAASsB,EAAiBf,IAAAA;AAMzB,SAAiBY,SAAAA,IAAAA;AAChB,QAAI1F,KAAImF,GAAa;AACpB,UAAMW,KAAe9F,KAAImF,EAAYO,GAAExH,OAAO4G,EAAAA;AAC9C,UAAqB,QAAjBY,GAAEK,EACLL,CAAAA,GAAEK,IAAcV;eAKNK,GAAEK,IAAcD,GAAaV,EACvC;AAED,aAAOU,GAAapG,EAAQsG,QAAQtG,EAAQsG,MAAMN,EAAAA,IAAKA,EAAAA;IACxD;EACD;AACD;AG5IgB,SAAA/D,EACfK,IACAd,IACAC,IACAiB,IACAC,IACAC,IACAjB,IACAD,IACAmB,IACAjB,IAAAA;AAVe,MAaX2E,IAkBEzF,IAAG0F,IAAOC,IAAUC,IAAUC,IAAUC,IACxCC,IACEC,IAMFC,IACAC,IAyGO9I,IA4BP+I,IACHC,IASShJ,IA6BNqE,IAtML4E,KAAU3F,GAAShD;AAIpB,MAAA,WAAIgD,GAAS7B,YAA2B,QAAW;AR9CtB,QQiDzB8B,GAAQ1B,QACX8C,KAAAA,CAAAA,ERpD0B,KQoDTpB,GAAQ1B,MAEzB6C,KAAoB,CADpBlB,KAASF,GAAQhC,MAAQiC,GAAQjC,GAAAA,KAI7B+G,KAAMvG,EAAOT,QAASgH,GAAI/E,EAAAA;AAE/B4F,IAAO,KAAsB,cAAA,OAAXD,GACjB,KAAA;AAkEC,QAhEIN,KAAWrF,GAASvD,OAClB6I,KACL,eAAeK,MAAWA,GAAQE,UAAUC,QAKzCP,MADJR,KAAMY,GAAQI,gBACQ7E,GAAc6D,GAAG7G,GAAAA,GACnCsH,KAAmBT,KACpBQ,KACCA,GAAS9I,MAAM4G,QACf0B,GAAGjH,KACJoD,IAGCjB,GAAQ/B,MAEXkH,MADA9F,KAAIU,GAAQ9B,MAAc+B,GAAQ/B,KACNJ,KAAwBwB,GAAC0G,OAGjDV,KAEHtF,GAAQ9B,MAAcoB,KAAI,IAAIqG,GAAQN,IAAUG,EAAAA,KAGhDxF,GAAQ9B,MAAcoB,KAAI,IAAIV,EAC7ByG,IACAG,EAAAA,GAEDlG,GAAEnB,cAAcwH,IAChBrG,GAAEwG,SAASG,IAERV,MAAUA,GAASW,IAAI5G,EAAAA,GAE3BA,GAAE7C,QAAQ4I,IACL/F,GAAE6G,UAAO7G,GAAE6G,QAAQ,CAAE,IAC1B7G,GAAET,UAAU2G,IACZlG,GAACoB,MAAkBQ,IACnB8D,KAAQ1F,GAACrB,MAAAA,MACTqB,GAAC8G,MAAoB,CAAA,GACrB9G,GAAC+G,MAAmB,CAAA,IAIjBf,MAAoC,QAAhBhG,GAACgH,QACxBhH,GAACgH,MAAchH,GAAE6G,QAGdb,MAAwD,QAApCK,GAAQY,6BAC3BjH,GAACgH,OAAehH,GAAE6G,UACrB7G,GAACgH,MAAc/J,EAAO,CAAA,GAAI+C,GAACgH,GAAAA,IAG5B/J,EACC+C,GAACgH,KACDX,GAAQY,yBAAyBlB,IAAU/F,GAACgH,GAAAA,CAAAA,IAI9CrB,KAAW3F,GAAE7C,OACbyI,KAAW5F,GAAE6G,OACb7G,GAAClB,MAAU4B,IAGPgF,GAEFM,CAAAA,MACoC,QAApCK,GAAQY,4BACgB,QAAxBjH,GAAEkH,sBAEFlH,GAAEkH,mBAAAA,GAGClB,MAA2C,QAAvBhG,GAAEmH,qBACzBnH,GAAC8G,IAAkB5G,KAAKF,GAAEmH,iBAAAA;SAErB;AAUN,UARCnB,MACoC,QAApCK,GAAQY,4BACRlB,OAAaJ,MACkB,QAA/B3F,GAAEoH,6BAEFpH,GAAEoH,0BAA0BrB,IAAUG,EAAAA,GAAAA,CAIrClG,GAACtB,QAC2B,QAA3BsB,GAAEqH,yBAAAA,UACHrH,GAAEqH,sBACDtB,IACA/F,GAACgH,KACDd,EAAAA,KAEDxF,GAAQ5B,QAAe6B,GAAQ7B,MAC/B;AAkBD,aAhBI4B,GAAQ5B,QAAe6B,GAAQ7B,QAKlCkB,GAAE7C,QAAQ4I,IACV/F,GAAE6G,QAAQ7G,GAACgH,KACXhH,GAACrB,MAAAA,QAGF+B,GAAQhC,MAAQiC,GAAQjC,KACxBgC,GAAQnC,MAAaoC,GAAQpC,KAC7BmC,GAAQnC,IAAWmF,KAAK,SAAApF,IAAAA;AACnBA,UAAAA,OAAOA,GAAKE,KAAWkC;QAC5B,CAAA,GAEStD,KAAI,GAAGA,KAAI4C,GAAC+G,IAAiB/I,QAAQZ,KAC7C4C,CAAAA,GAAC8G,IAAkB5G,KAAKF,GAAC+G,IAAiB3J,EAAAA,CAAAA;AAE3C4C,QAAAA,GAAC+G,MAAmB,CAAA,GAEhB/G,GAAC8G,IAAkB9I,UACtB6C,GAAYX,KAAKF,EAAAA;AAGlB,cAAMsG;MACP;AAE6B,cAAzBtG,GAAEsH,uBACLtH,GAAEsH,oBAAoBvB,IAAU/F,GAACgH,KAAad,EAAAA,GAG3CF,MAA4C,QAAxBhG,GAAEuH,sBACzBvH,GAAC8G,IAAkB5G,KAAK,WAAA;AACvBF,QAAAA,GAAEuH,mBAAmB5B,IAAUC,IAAUC,EAAAA;MAC1C,CAAA;IAEF;AASA,QAPA7F,GAAET,UAAU2G,IACZlG,GAAE7C,QAAQ4I,IACV/F,GAACkB,MAAcM,IACfxB,GAACtB,MAAAA,OAEGyH,KAAajH,EAAOkB,KACvBgG,KAAQ,GACLJ,IAAkB;AAQrB,WAPAhG,GAAE6G,QAAQ7G,GAACgH,KACXhH,GAACrB,MAAAA,OAEGwH,MAAYA,GAAWzF,EAAAA,GAE3B+E,KAAMzF,GAAEwG,OAAOxG,GAAE7C,OAAO6C,GAAE6G,OAAO7G,GAAET,OAAAA,GAE1BnC,KAAI,GAAGA,KAAI4C,GAAC+G,IAAiB/I,QAAQZ,KAC7C4C,CAAAA,GAAC8G,IAAkB5G,KAAKF,GAAC+G,IAAiB3J,EAAAA,CAAAA;AAE3C4C,MAAAA,GAAC+G,MAAmB,CAAA;IACrB,MACC,IAAA;AACC/G,MAAAA,GAACrB,MAAAA,OACGwH,MAAYA,GAAWzF,EAAAA,GAE3B+E,KAAMzF,GAAEwG,OAAOxG,GAAE7C,OAAO6C,GAAE6G,OAAO7G,GAAET,OAAAA,GAGnCS,GAAE6G,QAAQ7G,GAACgH;IAAAA,SACHhH,GAACrB,OAAAA,EAAayH,KAAQ;AAIhCpG,IAAAA,GAAE6G,QAAQ7G,GAACgH,KAEc,QAArBhH,GAAEwH,oBACL5F,KAAgB3E,EAAOA,EAAO,CAAA,GAAI2E,EAAAA,GAAgB5B,GAAEwH,gBAAAA,CAAAA,IAGjDxB,MAAAA,CAAqBN,MAAsC,QAA7B1F,GAAEyH,4BACnC5B,KAAW7F,GAAEyH,wBAAwB9B,IAAUC,EAAAA,IAOhDrE,EACCC,IACAyB,EAJGxB,KADI,QAAPgE,MAAeA,GAAI/H,SAAS2B,KAAuB,QAAXoG,GAAI7H,MACL6H,GAAItI,MAAMQ,WAAW8H,EAAAA,IAIpChE,KAAe,CAACA,EAAAA,GACxCf,IACAC,IACAiB,IACAC,IACAC,IACAjB,IACAD,IACAmB,IACAjB,EAAAA,GAGDd,GAAEF,OAAOY,GAAQhC,KAGjBgC,GAAQzB,OAAAA,MAEJe,GAAC8G,IAAkB9I,UACtB6C,GAAYX,KAAKF,EAAAA,GAGd8F,OACH9F,GAAC0G,MAAiB1G,GAACxB,KAAwB;EAoB7C,SAlBS0G,IAAAA;AAGR,QAFAxE,GAAQ5B,MAAa,MAEjBiD,MAAoC,QAArBD,IAA2B;AAK7C,WAJApB,GAAQzB,OAAW8C,KAChB2F,MRnRqB,IQsRjB9G,MAA8B,MAApBA,GAAO2C,YAAkB3C,GAAO8B,cAChD9B,CAAAA,KAASA,GAAO8B;AAEjBZ,MAAAA,GAAkBA,GAAkB6F,QAAQ/G,EAAAA,CAAAA,IAAW,MACvDF,GAAQhC,MAAQkC;IACjB,MACCF,CAAAA,GAAQhC,MAAQiC,GAAQjC,KACxBgC,GAAQnC,MAAaoC,GAAQpC;AAE9BW,MAAOR,IAAawG,IAAGxE,IAAUC,EAAAA;EAClC;MAEqB,SAArBmB,MACApB,GAAQ5B,QAAe6B,GAAQ7B,OAE/B4B,GAAQnC,MAAaoC,GAAQpC,KAC7BmC,GAAQhC,MAAQiC,GAAQjC,OAExBgC,GAAQhC,MAAQkJ,EACfjH,GAAQjC,KACRgC,IACAC,IACAiB,IACAC,IACAC,IACAjB,IACAkB,IACAjB,EAAAA;AAAAA,GAIG2E,KAAMvG,EAAQ2I,WAASpC,GAAI/E,EAAAA;AACjC;AAOgB,SAAAY,EAAWT,IAAaiH,IAAMhH,IAAAA;AAC7CgH,EAAAA,GAAInJ,MAAAA;AAEJ,WAASvB,KAAI,GAAGA,KAAI0D,GAAS9C,QAAQZ,KACpCoF,GAAS1B,GAAS1D,EAAAA,GAAI0D,GAAAA,EAAW1D,EAAAA,GAAI0D,GAAAA,EAAW1D,EAAAA,CAAAA;AAG7C8B,IAAON,OAAUM,EAAON,IAASkJ,IAAMjH,EAAAA,GAE3CA,GAAY6C,KAAK,SAAA1D,IAAAA;AAChB,QAAA;AAECa,MAAAA,KAAcb,GAAC8G,KACf9G,GAAC8G,MAAoB,CAAA,GACrBjG,GAAY6C,KAAK,SAAAqE,IAAAA;AAEhBA,QAAAA,GAAG7J,KAAK8B,EAAAA;MACT,CAAA;IAGD,SAFSkF,IAAAA;AACRhG,QAAOR,IAAawG,IAAGlF,GAAClB,GAAAA;IACzB;EACD,CAAA;AACD;AAiBA,SAAS8I,EACRzD,IACAzD,IACAC,IACAiB,IACAC,IACAC,IACAjB,IACAkB,IACAjB,IAAAA;AATD,MAeK1D,IAEA4K,IAEAC,IAEAC,IACAnE,IACAoE,IACAC,IAbAzC,KAAWhF,GAASxD,OACpB4I,KAAWrF,GAASvD,OACpBoG,KAAkC7C,GAAShD;AAmB/C,MALiB,UAAb6F,KAAoB1B,KAAY,+BACd,WAAb0B,KACR1B,KAAY,uCACHA,OAAWA,KAAY,iCAER,QAArBC;AACH,SAAK1E,KAAI,GAAGA,KAAI0E,GAAkB9D,QAAQZ,KAMzC,MALA2G,KAAQjC,GAAkB1E,EAAAA,MAOzB,kBAAkB2G,MAAAA,CAAAA,CAAYR,OAC7BA,KAAWQ,GAAMsE,cAAc9E,KAA8B,MAAnBQ,GAAMR,WAChD;AACDY,MAAAA,KAAMJ,IACNjC,GAAkB1E,EAAAA,IAAK;AACvB;IACD;;AAIF,MAAW,QAAP+G,IAAa;AAChB,QAAiB,SAAbZ,GACH,QAAO+E,SAASC,eAAexC,EAAAA;AAGhC5B,IAAAA,KAAMmE,SAASE,gBACd3G,IACA0B,IACAwC,GAAS0C,MAAM1C,EAAAA,GAKZhE,OACC7C,EAAOwJ,OACVxJ,EAAOwJ,IAAoBhI,IAAUoB,EAAAA,GACtCC,KAAAA,QAGDD,KAAoB;EACrB;AAEA,MAAiB,SAAbyB,GAECoC,CAAAA,OAAaI,MAAchE,MAAeoC,GAAIwE,SAAS5C,OAC1D5B,GAAIwE,OAAO5C;OAEN;AASN,QAPAjE,KAAoBA,MAAqB7D,EAAMC,KAAKiG,GAAIyE,UAAAA,GAExDjD,KAAWhF,GAASxD,SAASoF,GAAAA,CAKxBR,MAAoC,QAArBD,GAEnB,MADA6D,KAAW,CAAE,GACRvI,KAAI,GAAGA,KAAI+G,GAAI0E,WAAW7K,QAAQZ,KAEtCuI,CAAAA,IADA5B,KAAQI,GAAI0E,WAAWzL,EAAAA,GACRgH,IAAAA,IAAQL,GAAMA;AAI/B,SAAK3G,MAAKuI,GAET,KADA5B,KAAQ4B,GAASvI,EAAAA,GACR,cAALA,GAAAA;aACY,6BAALA,GACV6K,CAAAA,KAAUlE;aACA,EAAE3G,MAAK2I,KAAW;AAC5B,UACO,WAAL3I,MAAgB,kBAAkB2I,MAC7B,aAAL3I,MAAkB,oBAAoB2I,GAEvC;AAED/B,QAAYG,IAAK/G,IAAG,MAAM2G,IAAOlC,EAAAA;IAClC;AAKD,SAAKzE,MAAK2I,GACThC,CAAAA,KAAQgC,GAAS3I,EAAAA,GACR,cAALA,KACH8K,KAAcnE,KACC,6BAAL3G,KACV4K,KAAUjE,KACK,WAAL3G,KACV+K,KAAapE,KACE,aAAL3G,KACVgL,KAAUrE,KAERhC,MAA+B,cAAA,OAATgC,MACxB4B,GAASvI,EAAAA,MAAO2G,MAEhBC,EAAYG,IAAK/G,IAAG2G,IAAO4B,GAASvI,EAAAA,GAAIyE,EAAAA;AAK1C,QAAImG,GAGDjG,CAAAA,MACCkG,OACAD,GAAOc,WAAYb,GAAOa,UAC1Bd,GAAOc,WAAY3E,GAAI4E,eAEzB5E,GAAI4E,YAAYf,GAAOc,SAGxBpI,GAAQnC,MAAa,CAAA;aAEjB0J,OAAS9D,GAAI4E,YAAY,KAE7BxH,EACC4C,IACAlB,EAAQiF,EAAAA,IAAeA,KAAc,CAACA,EAAAA,GACtCxH,IACAC,IACAiB,IACa,oBAAb2B,KACG,iCACA1B,IACHC,IACAjB,IACAiB,KACGA,GAAkB,CAAA,IAClBnB,GAAQpC,OAAckB,EAAckB,IAAU,CAAA,GACjDoB,IACAjB,EAAAA,GAIwB,QAArBgB,GACH,MAAK1E,KAAI0E,GAAkB9D,QAAQZ,OAClCC,GAAWyE,GAAkB1E,EAAAA,CAAAA;AAM3B2E,IAAAA,OACJ3E,KAAI,SACa,eAAbmG,MAAyC,QAAd4E,KAC9BhE,GAAIgB,gBAAgB,OAAA,IAAA,WAEpBgD,OAKCA,OAAehE,GAAI/G,EAAAA,KACL,eAAbmG,MAAAA,CAA4B4E,MAIf,aAAb5E,MAAyB4E,OAAexC,GAASvI,EAAAA,MAEnD4G,EAAYG,IAAK/G,IAAG+K,IAAYxC,GAASvI,EAAAA,GAAIyE,EAAAA,GAG9CzE,KAAI,WAAA,WACAgL,MAAyBA,OAAYjE,GAAI/G,EAAAA,KAC5C4G,EAAYG,IAAK/G,IAAGgL,IAASzC,GAASvI,EAAAA,GAAIyE,EAAAA;EAG7C;AAEA,SAAOsC;AACR;AAQgB,SAAA3B,EAAS3E,IAAKkG,IAAOzF,IAAAA;AACpC,MAAA;AACC,QAAkB,cAAA,OAAPT,IAAmB;AAC7B,UAAImL,KAAuC,cAAA,OAAhBnL,GAAGoB;AAC1B+J,MAAAA,MAEHnL,GAAGoB,IAAAA,GAGC+J,MAA0B,QAATjF,OAIrBlG,GAAGoB,MAAYpB,GAAIkG,EAAAA;IAErB,MAAOlG,CAAAA,GAAIuB,UAAU2E;EAGtB,SAFSmB,IAAAA;AACRhG,MAAOR,IAAawG,IAAG5G,EAAAA;EACxB;AACD;AASgB,SAAA6E,EAAQ7E,IAAO8E,IAAa6F,IAAAA;AAA5B,MACXC,IAsBM9L;AAbV,MARI8B,EAAQiE,WAASjE,EAAQiE,QAAQ7E,EAAAA,IAEhC4K,KAAI5K,GAAMT,SACTqL,GAAE9J,WAAW8J,GAAE9J,YAAYd,GAAKI,OACpC8D,EAAS0G,IAAG,MAAM9F,EAAAA,IAIU,SAAzB8F,KAAI5K,GAAKM,MAAsB;AACnC,QAAIsK,GAAEC,qBACL,KAAA;AACCD,MAAAA,GAAEC,qBAAAA;IAGH,SAFSjE,IAAAA;AACRhG,QAAOR,IAAawG,IAAG9B,EAAAA;IACxB;AAGD8F,IAAAA,GAAEpJ,OAAOoJ,GAAChI,MAAc;EACzB;AAEA,MAAKgI,KAAI5K,GAAKC,IACb,MAASnB,KAAI,GAAGA,KAAI8L,GAAElL,QAAQZ,KACzB8L,CAAAA,GAAE9L,EAAAA,KACL+F,EACC+F,GAAE9L,EAAAA,GACFgG,IACA6F,MAAmC,cAAA,OAAd3K,GAAMZ,IAAAA;AAM1BuL,EAAAA,MACJ5L,EAAWiB,GAAKI,GAAAA,GAKjBJ,GAAKM,MAAcN,GAAKE,KAAWF,GAAKI,MAAQJ,GAAKK,MAAAA;AACtD;AAGA,SAASgI,EAASxJ,IAAO0J,IAAOtH,IAAAA;AAC/B,SAAOC,KAAKX,YAAY1B,IAAOoC,EAAAA;AAChC;AAAA,SCpnBgBiH,EAAOlI,IAAOkD,IAAW4H,IAAAA;AAAAA,MAMpCrH,IAOApB,IAQAE,IACHC;AArBG5B,IAAOV,MAAQU,EAAOV,GAAOF,IAAOkD,EAAAA,GAYpCb,MAPAoB,KAAoC,cAAA,OAAfqH,MAQtB,OACCA,MAAeA,GAAW7K,OAAeiD,GAASjD,KAMlDsC,KAAc,CAAA,GACjBC,KAAW,CAAA,GACZK,EACCK,IAPDlD,MAAAA,CAAWyD,MAAeqH,MAAgB5H,IAASjD,MAClDd,EAAc4B,GAAU,MAAM,CAACf,EAAAA,CAAAA,GAU/BqC,MAAY4B,GACZA,GACAf,GAAUH,cAAAA,CACTU,MAAeqH,KACb,CAACA,EAAAA,IACDzI,KACC,OACAa,GAAU6H,aACTpL,EAAMC,KAAKsD,GAAUoH,UAAAA,IACrB,MACL/H,IAAAA,CACCkB,MAAeqH,KACbA,KACAzI,KACCA,GAAQjC,MACR8C,GAAU6H,YACdtH,IACAjB,EAAAA,GAIDQ,EAAWT,IAAavC,IAAOwC,EAAAA;AAChC;AAOgB,SAAAwI,EAAQhL,IAAOkD,IAAAA;AAC9BgF,IAAOlI,IAAOkD,IAAW8H,CAAAA;AAC1B;AC5DO,SAASC,EAAajL,IAAOnB,IAAOQ,IAAAA;AAApC,MAELC,IACAC,IACAT,IAEGe,IALAL,KAAkBb,EAAO,CAAE,GAAEqB,GAAMnB,KAAAA;AAWvC,OAAKC,MAJDkB,GAAMZ,QAAQY,GAAMZ,KAAKS,iBAC5BA,KAAeG,GAAMZ,KAAKS,eAGjBhB,GACA,UAALC,KAAYQ,KAAMT,GAAMC,EAAAA,IACd,SAALA,KAAYS,KAAMV,GAAMC,EAAAA,IAEhCU,GAAgBV,EAAAA,IAAAA,WADRD,GAAMC,EAAAA,KAAAA,WAAoBe,KACbA,GAAaf,EAAAA,IAEbD,GAAMC,EAAAA;AAS7B,SALIW,UAAUC,SAAS,MACtBF,GAAgBH,WACfI,UAAUC,SAAS,IAAIC,EAAMC,KAAKH,WAAW,CAAA,IAAKJ,KAG7CS,EACNE,GAAMZ,MACNI,IACAF,MAAOU,GAAMV,KACbC,MAAOS,GAAMT,KACb,IAAA;AAEF;AAAA,SJ1CgB2L,EAAcC,IAAcC,IAAAA;AAG3C,MAAMnK,KAAU,EACfX,KAHD8K,KAAY,SAAStM,KAIpBoB,IAAeiL,IAEfE,UAAAA,SAASxM,IAAOyM,IAAAA;AAIf,WAAOzM,GAAMQ,SAASiM,EAAAA;EACvB,GAEAC,UAAQA,SAAC1M,IAAAA;AAAD0M,QAGFC,IACAC;AA8BL,WAjCKvK,KAAKgI,oBAELsC,KAAO,CAAA,IACPC,KAAM,CAAA,GACNL,EAAAA,IAAalK,MAEjBA,KAAKgI,kBAAkB,WAAA;AAAM,aAAAuC;IAAG,GAEhCvK,KAAK2J,uBAAuB,WAAA;AAC3BW,MAAAA,KAAO;IACR,GAEAtK,KAAK6H,wBAAwB,SAAU2C,IAAAA;AAClCxK,WAAKrC,MAAM4G,UAAUiG,GAAOjG,SAC/B+F,GAAKpG,KAAK,SAAA1D,IAAAA;AACTA,QAAAA,GAACtB,MAAAA,MACDqB,EAAcC,EAAAA;MACf,CAAA;IAEF,GAEAR,KAAKoH,MAAM,SAAA5G,IAAAA;AACV8J,MAAAA,GAAK5J,KAAKF,EAAAA;AACV,UAAIiK,KAAMjK,GAAEmJ;AACZnJ,MAAAA,GAAEmJ,uBAAuB,WAAA;AACpBW,QAAAA,MACHA,GAAKI,OAAOJ,GAAKnC,QAAQ3H,EAAAA,GAAI,CAAA,GAE1BiK,MAAKA,GAAI/L,KAAK8B,EAAAA;MACnB;IACD,IAGM7C,GAAMQ;EACd,EAAA;AASD,SAAQ4B,GAAQsK,SAAQrL,KAAee,GAAQoK,SAASlD,cACvDlH;AACF;AN/Da,IC0BAtB,GChBPiB,GCRFH,GAgGSoL,GC+ETlK,GAWAI,GAEEE,GA0BAS,GC/LF6D,GAmJEG,GACAD,GC5KK3H,GNUEmF,GACAH,GACA6B,GCZAhB;ADDA;;IAWAV,IAAgC,CAAA;AAXhC,IAYAH,IAAY,CAAA;AAZZ,IAaA6B,IACZ;AAdY,ICCAhB,IAAUmH,MAAMnH;AAyBhBhF,QAAQmE,EAAUnE,OChBzBiB,IAAU,EACfR,KSHe,SAAY2L,IAAO/L,IAAOqC,IAAU2J,IAAAA;AAQnD,eANI7J,IAEH8J,IAEAC,IAEOlM,KAAQA,GAAKE,KACpB,MAAKiC,KAAYnC,GAAKM,QAAAA,CAAiB6B,GAASjC,GAC/C,KAAA;AAcC,aAbA+L,KAAO9J,GAAU5B,gBAE4B,QAAjC0L,GAAKE,6BAChBhK,GAAUiK,SAASH,GAAKE,yBAAyBJ,EAAAA,CAAAA,GACjDG,KAAU/J,GAAS9B,MAGe,QAA/B8B,GAAUkK,sBACblK,GAAUkK,kBAAkBN,IAAOC,MAAa,CAAE,CAAA,GAClDE,KAAU/J,GAAS9B,MAIhB6L,GACH,QAAQ/J,GAASiG,MAAiBjG;MAIpC,SAFSyE,IAAAA;AACRmF,QAAAA,KAAQnF;MACT;AAIF,YAAMmF;IACP,EAAA,GRxCItL,IAAU,GAgGDoL,IAAiB,SAAA7L,IAAAA;AAAK,aACzB,QAATA,MAAsCsM,QAArBtM,GAAMO;IAAwB,GCzEhDS,EAAciH,UAAUmE,WAAW,SAAUG,IAAQC,IAAAA;AAEpD,UAAIC;AAEHA,MAAAA,KADsB,QAAnBvL,KAAIwH,OAAuBxH,KAAIwH,QAAgBxH,KAAKqH,QACnDrH,KAAIwH,MAEJxH,KAAIwH,MAAc/J,EAAO,CAAE,GAAEuC,KAAKqH,KAAAA,GAGlB,cAAA,OAAVgE,OAGVA,KAASA,GAAO5N,EAAO,CAAA,GAAI8N,EAAAA,GAAIvL,KAAKrC,KAAAA,IAGjC0N,MACH5N,EAAO8N,IAAGF,EAAAA,GAIG,QAAVA,MAEArL,KAAIV,QACHgM,MACHtL,KAAIuH,IAAiB7G,KAAK4K,EAAAA,GAE3B/K,EAAcP,IAAAA;IAEhB,GAQAF,EAAciH,UAAUyE,cAAc,SAAUF,IAAAA;AAC3CtL,WAAIV,QAIPU,KAAId,MAAAA,MACAoM,MAAUtL,KAAIsH,IAAkB5G,KAAK4K,EAAAA,GACzC/K,EAAcP,IAAAA;IAEhB,GAYAF,EAAciH,UAAUC,SAASnH,GA8F7BY,IAAgB,CAAA,GAadM,IACa,cAAA,OAAX0K,UACJA,QAAQ1E,UAAU2E,KAAKC,KAAKF,QAAQG,QAAAA,CAAAA,IACpCC,YAuBErK,IAAY,SAACsK,IAAGC,IAAAA;AAAM,aAAAD,GAACxM,IAAAL,MAAiB8M,GAACzM,IAAAL;IAAc,GAuB7D0B,EAAOC,MAAkB,GCtNrByE,IAAa,GAmJXG,IAAaK,EAAAA,KAAiB,GAC9BN,IAAoBM,EAAAA,IAAiB,GC5KhCjI,IAAI;;;;;;;;;;;;;;;;;;;;AMqIf,SAASoO,GAAaC,IAAOC,IAAAA;AACxBC,EAAAA,GAAOC,OACVD,GAAOC,IAAOC,IAAkBJ,IAAOK,MAAeJ,EAAAA,GAEvDI,KAAc;AAOd,MAAMC,KACLF,GAAgBG,QACfH,GAAgBG,MAAW,EAC3BC,IAAO,CAAA,GACPL,KAAiB,CAAA,EAAA;AAOnB,SAJIH,MAASM,GAAKE,GAAOC,UACxBH,GAAKE,GAAOE,KAAK,CAAE,CAAA,GAGbJ,GAAKE,GAAOR,EAAAA;AACpB;AAOO,SAASW,GAASC,IAAAA;AAExB,SADAP,KAAc,GACPQ,GAAWC,IAAgBF,EAAAA;AACnC;AAUgB,SAAAC,GAAWE,IAASH,IAAcI,IAAAA;AAEjD,MAAMC,KAAYlB,GAAamB,MAAgB,CAAA;AAE/C,MADAD,GAAUE,IAAWJ,IAAAA,CAChBE,GAASG,QACbH,GAAST,KAAU,CACjBQ,KAAiDA,GAAKJ,EAAAA,IAA/CE,GAAAA,QAA0BF,EAAAA,GAElC,SAAAS,IAAAA;AACC,QAAMC,KAAeL,GAASM,MAC3BN,GAASM,IAAY,CAAA,IACrBN,GAAST,GAAQ,CAAA,GACdgB,KAAYP,GAAUE,EAASG,IAAcD,EAAAA;AAE/CC,IAAAA,OAAiBE,OACpBP,GAASM,MAAc,CAACC,IAAWP,GAAST,GAAQ,CAAA,CAAA,GACpDS,GAASG,IAAYK,SAAS,CAAE,CAAA;EAElC,CAAA,GAGDR,GAASG,MAAchB,IAAAA,CAElBA,GAAiBsB,IAAkB;AAgC9B,QAAAC,KAAT,SAAyBC,IAAGC,IAAGC,IAAAA;AAC9B,UAAA,CAAKb,GAASG,IAAAb,IAAqB,QAAA;AAGnC,UACMwB,KACLd,GAASG,IAAAb,IAAAC,GAA0BwB,OAFhB,SAAAC,IAAAA;AAAC,eAAA,CAAA,CAAMA,GAACb;MAAW,CAAA;AAOvC,UAHsBW,GAAWG,MAAM,SAAAD,IAAAA;AAAC,eAAA,CAAKA,GAACV;MAAW,CAAA,EAIxD,QAAA,CAAOY,MAAUA,GAAQC,KAAKC,MAAMT,IAAGC,IAAGC,EAAAA;AAM3C,UAAIQ,KAAAA;AAUJ,aATAP,GAAWQ,QAAQ,SAAAC,IAAAA;AAClB,YAAIA,GAAQjB,KAAa;AACxB,cAAMD,KAAekB,GAAQhC,GAAQ,CAAA;AACrCgC,UAAAA,GAAQhC,KAAUgC,GAAQjB,KAC1BiB,GAAQjB,MAAAA,QACJD,OAAiBkB,GAAQhC,GAAQ,CAAA,MAAI8B,KAAAA;QAC1C;MACD,CAAA,GAAA,EAAA,CAEOA,MAAgBrB,GAASG,IAAYqB,UAAUb,QAAAA,CACnDO,MACCA,GAAQC,KAAKC,MAAMT,IAAGC,IAAGC,EAAAA;IAG9B;AAhEA1B,IAAAA,GAAiBsB,IAAAA;AACjB,QAAIS,KAAU/B,GAAiBsC,uBACzBC,KAAUvC,GAAiBwC;AAKjCxC,IAAAA,GAAiBwC,sBAAsB,SAAUhB,IAAGC,IAAGC,IAAAA;AACtD,UAAIO,KAAIQ,KAAS;AAChB,YAAIC,KAAMX;AAEVA,QAAAA,KAAAA,QACAR,GAAgBC,IAAGC,IAAGC,EAAAA,GACtBK,KAAUW;MACX;AAEIH,MAAAA,MAASA,GAAQP,KAAKC,MAAMT,IAAGC,IAAGC,EAAAA;IACvC,GAiDA1B,GAAiBsC,wBAAwBf;EAC1C;AAGD,SAAOV,GAASM,OAAeN,GAAST;AACzC;AAOgB,SAAAuC,GAAUC,IAAUC,IAAAA;AAEnC,MAAMC,KAAQnD,GAAamB,MAAgB,CAAA;AAAA,GACtChB,GAAOiD,OAAiBC,GAAYF,GAAK3C,KAAQ0C,EAAAA,MACrDC,GAAK1C,KAAUwC,IACfE,GAAMG,IAAeJ,IAErB7C,GAAgBG,IAAAJ,IAAyBO,KAAKwC,EAAAA;AAEhD;AAOO,SAASI,GAAgBN,IAAUC,IAAAA;AAEzC,MAAMC,KAAQnD,GAAamB,MAAgB,CAAA;AAAA,GACtChB,GAAOiD,OAAiBC,GAAYF,GAAK3C,KAAQ0C,EAAAA,MACrDC,GAAK1C,KAAUwC,IACfE,GAAMG,IAAeJ,IAErB7C,GAAgBD,IAAkBO,KAAKwC,EAAAA;AAEzC;AAGO,SAASK,GAAOC,IAAAA;AAEtB,SADAnD,KAAc,GACPoD,GAAQ,WAAA;AAAO,WAAA,EAAEC,SAASF,GAAAA;EAAc,GAAG,CAAA,CAAA;AACnD;AAQgB,SAAAG,GAAoBC,IAAKC,IAAcZ,IAAAA;AACtD5C,EAAAA,KAAc,GACdiD,GACC,WAAA;AACC,WAAkB,cAAA,OAAPM,MACVA,GAAIC,GAAAA,CAAAA,GACS,WAAA;AAAA,aAAAD,GAAI,IAAA;IAAK,KACZA,MACVA,GAAIF,UAAUG,GAAAA,GACA,WAAA;AAAA,aAAAD,GAAIF,UAAU;IAAI,KAAA;EAElC,GACQ,QAART,KAAeA,KAAOA,GAAKa,OAAOF,EAAAA,CAAAA;AAEpC;AAQgB,SAAAH,GAAQM,IAASd,IAAAA;AAEhC,MAAMC,KAAQnD,GAAamB,MAAgB,CAAA;AAO3C,SANIkC,GAAYF,GAAK3C,KAAQ0C,EAAAA,MAC5BC,GAAK1C,KAAUuD,GAAAA,GACfb,GAAK3C,MAAS0C,IACdC,GAAK/C,MAAY4D,KAGXb,GAAK1C;AACb;AAOO,SAASwD,GAAYhB,IAAUC,IAAAA;AAErC,SADA5C,KAAc,GACPoD,GAAQ,WAAA;AAAA,WAAMT;EAAQ,GAAEC,EAAAA;AAChC;AAKO,SAASgB,GAAWC,IAAAA;AAC1B,MAAMC,KAAW/D,GAAiB8D,QAAQA,GAAO9C,GAAAA,GAK3C8B,KAAQnD,GAAamB,MAAgB,CAAA;AAK3C,SADAgC,GAAKpB,IAAYoC,IACZC,MAEe,QAAhBjB,GAAK1C,OACR0C,GAAK1C,KAAAA,MACL2D,GAASC,IAAIhE,EAAAA,IAEP+D,GAAS1B,MAAM4B,SANAH,GAAO1D;AAO9B;AAMO,SAAS8D,GAAcD,IAAOE,IAAAA;AAChCrE,EAAAA,GAAQoE,iBACXpE,GAAQoE,cACPC,KAAYA,GAAUF,EAAAA,IAAMG,EAAA;AAG/B;AAMO,SAASC,GAAiBC,IAAAA;AAEhC,MAAMxB,KAAQnD,GAAamB,MAAgB,EAAA,GACrCyD,KAAWhE,GAAAA;AAQjB,SAPAuC,GAAK1C,KAAUkE,IACVtE,GAAiBwE,sBACrBxE,GAAiBwE,oBAAoB,SAACC,IAAKC,IAAAA;AACtC5B,IAAAA,GAAK1C,MAAS0C,GAAK1C,GAAQqE,IAAKC,EAAAA,GACpCH,GAAS,CAAA,EAAGE,EAAAA;EACb,IAEM,CACNF,GAAS,CAAA,GACT,WAAA;AACCA,IAAAA,GAAS,CAAA,EAAA,MAAGI;EACb,CAAA;AAEF;AAGO,SAASC,KAAAA;AAEf,MAAM9B,KAAQnD,GAAamB,MAAgB,EAAA;AAC3C,MAAA,CAAKgC,GAAK1C,IAAS;AAIlB,aADIyE,KAAO7E,GAAgB8E,KACX,SAATD,MAAAA,CAAkBA,GAAIE,OAA2B,SAAjBF,GAAIzE,KAC1CyE,CAAAA,KAAOA,GAAIzE;AAGZ,QAAI4E,KAAOH,GAAIE,QAAWF,GAAIE,MAAS,CAAC,GAAG,CAAA;AAC3CjC,IAAAA,GAAK1C,KAAU,MAAM4E,GAAK,CAAA,IAAK,MAAMA,GAAK,CAAA;EAC3C;AAEA,SAAOlC,GAAK1C;AACb;AAKA,SAAS6E,KAAAA;AAER,WADIC,IACIA,KAAYC,GAAkBC,MAAAA,IACrC,KAAKF,GAASG,OAAgBH,GAAS/E,IACvC,KAAA;AACC+E,IAAAA,GAAS/E,IAAAJ,IAAyBoC,QAAQmD,EAAAA,GAC1CJ,GAAS/E,IAAAJ,IAAyBoC,QAAQoD,EAAAA,GAC1CL,GAAS/E,IAAAJ,MAA2B,CAAA;EAIrC,SAHSyF,IAAAA;AACRN,IAAAA,GAAS/E,IAAAJ,MAA2B,CAAA,GACpCD,GAAO2C,IAAa+C,IAAGN,GAASJ,GAAAA;EACjC;AAEF;AAcA,SAASW,GAAe7C,IAAAA;AACvB,MAOI8C,IAPEC,KAAO,WAAA;AACZC,iBAAaC,EAAAA,GACTC,MAASC,qBAAqBL,EAAAA,GAClCM,WAAWpD,EAAAA;EACZ,GACMiD,KAAUG,WAAWL,IAjcR,GAAA;AAocfG,EAAAA,OACHJ,KAAMO,sBAAsBN,EAAAA;AAE9B;AAqBA,SAASL,GAAcY,IAAAA;AAGtB,MAAMC,KAAOnG,IACToG,KAAUF,GAAIlF;AACI,gBAAA,OAAXoF,OACVF,GAAIlF,MAAAA,QACJoF,GAAAA,IAGDpG,KAAmBmG;AACpB;AAOA,SAASZ,GAAaW,IAAAA;AAGrB,MAAMC,KAAOnG;AACbkG,EAAAA,GAAIlF,MAAYkF,GAAI9F,GAAAA,GACpBJ,KAAmBmG;AACpB;AAOA,SAASnD,GAAYqD,IAASC,IAAAA;AAC7B,SAAA,CACED,MACDA,GAAQhG,WAAWiG,GAAQjG,UAC3BiG,GAAQC,KAAK,SAACC,IAAK5G,IAAAA;AAAU,WAAA4G,OAAQH,GAAQzG,EAAAA;EAAM,CAAA;AAErD;AAQA,SAASc,GAAe8F,IAAKC,IAAAA;AAC5B,SAAmB,cAAA,OAALA,KAAkBA,GAAED,EAAAA,IAAOC;AAC1C;IAniBI3F,IAGAd,IAGA0G,IAmBAC,IAhBA1G,IAGAkF,IAGErF,IAEF8G,IACAC,IACAC,IACAC,IACAC,IACAC,IAibAnB;;;;AAvcJ,IASI7F,KAAc;AATlB,IAYIkF,KAAoB,CAAA;AAZxB,IAeMrF,KAAuDoH;AAf7D,IAiBIN,KAAgB9G,GAAOqH;AAjB3B,IAkBIN,KAAkB/G,GAAOsH;AAlB7B,IAmBIN,KAAehH,GAAQuH;AAnB3B,IAoBIN,KAAYjH,GAAOkB;AApBvB,IAqBIgG,KAAmBlH,GAAQwH;AArB/B,IAsBIL,KAAUnH,GAAOM;AAMrBN,IAAAA,GAAOqH,MAAS,SAAAI,IAAAA;AACfvH,MAAAA,KAAmB,MACf4G,MAAeA,GAAcW,EAAAA;IAClC,GAEAzH,GAAOM,KAAS,SAACmH,IAAOC,IAAAA;AACnBD,MAAAA,MAASC,GAASC,OAAcD,GAASC,IAAA1C,QAC5CwC,GAAKxC,MAASyC,GAASC,IAAA1C,MAGpBkC,MAASA,GAAQM,IAAOC,EAAAA;IAC7B,GAGA1H,GAAOsH,MAAW,SAAAG,IAAAA;AACbV,MAAAA,MAAiBA,GAAgBU,EAAAA,GAGrCzG,KAAe;AAEf,UAAMZ,MAHNF,KAAmBuH,GAAKvG,KAGMb;AAC1BD,MAAAA,OACCwG,OAAsB1G,MACzBE,GAAKH,MAAmB,CAAA,GACxBC,GAAgBD,MAAoB,CAAA,GACpCG,GAAKE,GAAO+B,QAAQ,SAAAC,IAAAA;AACfA,QAAAA,GAAQjB,QACXiB,GAAQhC,KAAUgC,GAAQjB,MAE3BiB,GAASa,IAAeb,GAAQjB,MAAAA;MACjC,CAAA,MAEAjB,GAAKH,IAAiBoC,QAAQmD,EAAAA,GAC9BpF,GAAKH,IAAiBoC,QAAQoD,EAAAA,GAC9BrF,GAAKH,MAAmB,CAAA,GACxBe,KAAe,KAGjB4F,KAAoB1G;IACrB,GAGAF,GAAQuH,SAAS,SAAAE,IAAAA;AACZT,MAAAA,MAAcA,GAAaS,EAAAA;AAE/B,UAAM7F,KAAI6F,GAAKvG;AACXU,MAAAA,MAAKA,GAACvB,QACLuB,GAACvB,IAAAJ,IAAyBM,WA+ZR,MA/Z2B8E,GAAkB7E,KAAKoB,EAAAA,KA+Z7CiF,OAAY7G,GAAQmG,2BAC/CU,KAAU7G,GAAQmG,0BACNR,IAAgBR,EAAAA,IAha5BvD,GAACvB,IAAAC,GAAe+B,QAAQ,SAAAC,IAAAA;AACnBA,QAAAA,GAASa,MACZb,GAAQjC,MAASiC,GAASa,IAE3Bb,GAASa,IAAAA;MACV,CAAA,IAEDyD,KAAoB1G,KAAmB;IACxC,GAIAF,GAAOkB,MAAW,SAACuG,IAAOG,IAAAA;AACzBA,MAAAA,GAAYnB,KAAK,SAAArB,IAAAA;AAChB,YAAA;AACCA,UAAAA,GAASnF,IAAkBoC,QAAQmD,EAAAA,GACnCJ,GAASnF,MAAoBmF,GAASnF,IAAkB6B,OAAO,SAAA0C,IAAAA;AAAE,mBAAA,CAChEA,GAAElE,MAAUmF,GAAajB,EAAAA;UAAU,CAAA;QAQrC,SANSkB,IAAAA;AACRkC,UAAAA,GAAYnB,KAAK,SAAA7E,IAAAA;AACZA,YAAAA,GAAC3B,QAAmB2B,GAAC3B,MAAoB,CAAA;UAC9C,CAAA,GACA2H,KAAc,CAAA,GACd5H,GAAO2C,IAAa+C,IAAGN,GAASJ,GAAAA;QACjC;MACD,CAAA,GAEIiC,MAAWA,GAAUQ,IAAOG,EAAAA;IACjC,GAGA5H,GAAQwH,UAAU,SAAAC,IAAAA;AACbP,MAAAA,MAAkBA,GAAiBO,EAAAA;AAEvC,UAEKI,IAFCjG,KAAI6F,GAAKvG;AACXU,MAAAA,MAAKA,GAACvB,QAETuB,GAACvB,IAAAC,GAAe+B,QAAQ,SAAAV,IAAAA;AACvB,YAAA;AACC6D,UAAAA,GAAc7D,EAAAA;QAGf,SAFS+D,IAAAA;AACRmC,UAAAA,KAAanC;QACd;MACD,CAAA,GACA9D,GAACvB,MAAAA,QACGwH,MAAY7H,GAAO2C,IAAakF,IAAYjG,GAACoD,GAAAA;IAEnD;AA2UA,IAAIgB,KAA0C,cAAA,OAAzBG;;;", "names": ["assign", "obj", "props", "i", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "slice", "call", "defaultProps", "createVNode", "original", "vnode", "__k", "__", "__b", "__e", "__d", "__c", "constructor", "__v", "vnodeId", "__i", "__u", "options", "createRef", "current", "Fragment", "BaseComponent", "context", "this", "getDomSibling", "childIndex", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "rerenderQueue", "push", "process", "__r", "prevDebounce", "debounceRendering", "defer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "component", "newVNode", "oldVNode", "oldDom", "commitQueue", "refQueue", "sort", "depthSort", "shift", "__P", "diff", "__n", "namespaceURI", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "parentDom", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "namespace", "excessDomChildren", "isHydrating", "childVNode", "newDom", "firstChildDom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EMPTY_ARR", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructNewChildrenArray", "EMPTY_OBJ", "applyRef", "insert", "nextS<PERSON>ling", "skewedIndex", "matchingIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingOldChildren", "skew", "String", "isArray", "findMatchingIndex", "unmount", "parentVNode", "contains", "insertBefore", "nodeType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "some", "x", "y", "setStyle", "style", "value", "setProperty", "IS_NON_DIMENSIONAL", "test", "dom", "name", "oldValue", "useCapture", "o", "cssText", "replace", "toLowerCase", "l", "_attached", "eventClock", "addEventListener", "eventProxyCapture", "eventProxy", "removeEventListener", "e", "removeAttribute", "setAttribute", "createEventProxy", "<PERSON><PERSON><PERSON><PERSON>", "_dispatched", "event", "tmp", "isNew", "oldProps", "oldState", "snapshot", "clearProcessingException", "newProps", "isClassComponent", "provider", "componentContext", "renderHook", "count", "newType", "outer", "prototype", "render", "contextType", "__E", "doR<PERSON>", "sub", "state", "__h", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "MODE_HYDRATE", "indexOf", "diffElementNodes", "diffed", "root", "cb", "newHtml", "oldHtml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputValue", "checked", "localName", "document", "createTextNode", "createElementNS", "is", "__m", "data", "childNodes", "attributes", "__html", "innerHTML", "hasRefUnmount", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "<PERSON><PERSON><PERSON><PERSON>", "hydrate", "cloneElement", "createContext", "defaultValue", "contextId", "Consumer", "contextValue", "Provider", "subs", "ctx", "_props", "old", "splice", "isValidElement", "Array", "error", "errorInfo", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "undefined", "update", "callback", "s", "forceUpdate", "Promise", "then", "bind", "resolve", "setTimeout", "a", "b", "getHookState", "index", "type", "options", "__h", "currentComponent", "currentHook", "hooks", "__H", "__", "length", "push", "useState", "initialState", "useReducer", "invokeOrReturn", "reducer", "init", "hookState", "currentIndex", "_reducer", "__c", "action", "currentValue", "__N", "nextValue", "setState", "_hasScuFromHooks", "updateHookState", "p", "s", "c", "stateHooks", "filter", "x", "every", "prevScu", "call", "this", "shouldUpdate", "for<PERSON>ach", "hookItem", "props", "shouldComponentUpdate", "prevCWU", "componentWillUpdate", "__e", "tmp", "useEffect", "callback", "args", "state", "__s", "args<PERSON><PERSON><PERSON>", "_pendingArgs", "useLayoutEffect", "useRef", "initialValue", "useMemo", "current", "useImperativeHandle", "ref", "createHandle", "concat", "factory", "useCallback", "useContext", "context", "provider", "sub", "value", "useDebugValue", "formatter", "n", "useErrorBoundary", "cb", "errState", "componentDidCatch", "err", "errorInfo", "undefined", "useId", "root", "__v", "__m", "mask", "flushAfterPaintEffects", "component", "afterPaintEffects", "shift", "__P", "invokeCleanup", "invokeEffect", "e", "afterNextFrame", "raf", "done", "clearTimeout", "timeout", "HAS_RAF", "cancelAnimationFrame", "setTimeout", "requestAnimationFrame", "hook", "comp", "cleanup", "oldArgs", "newArgs", "some", "arg", "f", "previousComponent", "prevRaf", "oldBeforeDiff", "oldBeforeRender", "oldAfterDiff", "old<PERSON><PERSON><PERSON>", "oldBeforeUnmount", "oldRoot", "_options", "__b", "__r", "diffed", "unmount", "vnode", "parentDom", "__k", "commitQueue", "hasErrored"]}