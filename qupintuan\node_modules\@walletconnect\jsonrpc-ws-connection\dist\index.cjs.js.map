{"version": 3, "file": "index.cjs.js", "sources": ["../src/utils.ts", "../src/ws.ts"], "sourcesContent": ["export const resolveWebSocketImplementation = () => {\n  if (typeof WebSocket !== \"undefined\") {\n    return WebSocket;\n  } else if (typeof global !== \"undefined\" && typeof global.WebSocket !== \"undefined\") {\n    return global.WebSocket;\n  } else if (typeof window !== \"undefined\" && typeof window.WebSocket !== \"undefined\") {\n    return window.WebSocket;\n  } else if (typeof self !== \"undefined\" && typeof self.WebSocket !== \"undefined\") {\n    return self.WebSocket;\n  }\n\n  return require(\"ws\");\n};\n\nexport const hasBuiltInWebSocket = () =>\n  typeof WebSocket !== \"undefined\" ||\n  (typeof global !== \"undefined\" && typeof global.WebSocket !== \"undefined\") ||\n  (typeof window !== \"undefined\" && typeof window.WebSocket !== \"undefined\") ||\n  (typeof self !== \"undefined\" && typeof self.WebSocket !== \"undefined\");\n\nexport const isBrowser = () => typeof window !== \"undefined\";\n\nexport const truncateQuery = (wssUrl: string) => wssUrl.split(\"?\")[0];\n", "import { EventEmitter } from \"events\";\nimport { safeJsonParse, safeJsonStringify } from \"@walletconnect/safe-json\";\nimport {\n  formatJsonRpcError,\n  IJsonRpcConnection,\n  JsonRpcPayload,\n  isReactNative,\n  isWsUrl,\n  isLocalhostUrl,\n  parseConnectionError,\n} from \"@walletconnect/jsonrpc-utils\";\nimport { truncateQuery, resolveWebSocketImplementation, hasBuiltInWebSocket } from \"./utils\";\n\n// Source: https://nodejs.org/api/events.html#emittersetmaxlistenersn\nconst EVENT_EMITTER_MAX_LISTENERS_DEFAULT = 10;\n\nconst WS = resolveWebSocketImplementation();\n\nexport class WsConnection implements IJsonRpcConnection {\n  public events = new EventEmitter();\n\n  private socket: WebSocket | undefined;\n\n  private registering = false;\n\n  constructor(public url: string) {\n    if (!isWsUrl(url)) {\n      throw new Error(`Provided URL is not compatible with WebSocket connection: ${url}`);\n    }\n    this.url = url;\n  }\n\n  get connected(): boolean {\n    return typeof this.socket !== \"undefined\";\n  }\n\n  get connecting(): boolean {\n    return this.registering;\n  }\n\n  public on(event: string, listener: any): void {\n    this.events.on(event, listener);\n  }\n\n  public once(event: string, listener: any): void {\n    this.events.once(event, listener);\n  }\n\n  public off(event: string, listener: any): void {\n    this.events.off(event, listener);\n  }\n\n  public removeListener(event: string, listener: any): void {\n    this.events.removeListener(event, listener);\n  }\n\n  public async open(url: string = this.url): Promise<void> {\n    await this.register(url);\n  }\n\n  public async close(): Promise<void> {\n    return new Promise<void>((resolve, reject) => {\n      if (typeof this.socket === \"undefined\") {\n        reject(new Error(\"Connection already closed\"));\n        return;\n      }\n\n      this.socket.onclose = (event) => {\n        this.onClose(event);\n        resolve();\n      };\n\n      this.socket.close();\n    });\n  }\n\n  public async send(payload: JsonRpcPayload): Promise<void> {\n    if (typeof this.socket === \"undefined\") {\n      this.socket = await this.register();\n    }\n    try {\n      this.socket.send(safeJsonStringify(payload));\n    } catch (e) {\n      this.onError(payload.id, e as Error);\n    }\n  }\n\n  // ---------- Private ----------------------------------------------- //\n\n  private register(url = this.url): Promise<WebSocket> {\n    if (!isWsUrl(url)) {\n      throw new Error(`Provided URL is not compatible with WebSocket connection: ${url}`);\n    }\n    if (this.registering) {\n      const currentMaxListeners = this.events.getMaxListeners();\n      if (\n        this.events.listenerCount(\"register_error\") >= currentMaxListeners ||\n        this.events.listenerCount(\"open\") >= currentMaxListeners\n      ) {\n        this.events.setMaxListeners(currentMaxListeners + 1);\n      }\n      return new Promise((resolve, reject) => {\n        this.events.once(\"register_error\", (error) => {\n          this.resetMaxListeners();\n          reject(error);\n        });\n        this.events.once(\"open\", () => {\n          this.resetMaxListeners();\n          if (typeof this.socket === \"undefined\") {\n            return reject(new Error(\"WebSocket connection is missing or invalid\"));\n          }\n          resolve(this.socket);\n        });\n      });\n    }\n    this.url = url;\n    this.registering = true;\n\n    return new Promise((resolve, reject) => {\n      const opts = !isReactNative() ? { rejectUnauthorized: !isLocalhostUrl(url) } : undefined;\n      const socket: WebSocket = new WS(url, [], opts);\n      if (hasBuiltInWebSocket()) {\n        socket.onerror = (event: Event) => {\n          const errorEvent = event as ErrorEvent;\n          reject(this.emitError(errorEvent.error));\n        };\n      } else {\n        (socket as any).on(\"error\", (errorEvent: any) => {\n          reject(this.emitError(errorEvent));\n        });\n      }\n      socket.onopen = () => {\n        this.onOpen(socket);\n        resolve(socket);\n      };\n    });\n  }\n\n  private onOpen(socket: WebSocket) {\n    socket.onmessage = (event: MessageEvent) => this.onPayload(event);\n    socket.onclose = (event) => this.onClose(event);\n    this.socket = socket;\n    this.registering = false;\n    this.events.emit(\"open\");\n  }\n\n  private onClose(event: CloseEvent) {\n    this.socket = undefined;\n    this.registering = false;\n    this.events.emit(\"close\", event);\n  }\n\n  private onPayload(e: { data: any }) {\n    if (typeof e.data === \"undefined\") return;\n    const payload: JsonRpcPayload = typeof e.data === \"string\" ? safeJsonParse(e.data) : e.data;\n    this.events.emit(\"payload\", payload);\n  }\n\n  private onError(id: number, e: Error) {\n    const error = this.parseError(e);\n    const message = error.message || error.toString();\n    const payload = formatJsonRpcError(id, message);\n    this.events.emit(\"payload\", payload);\n  }\n\n  private parseError(e: Error, url = this.url) {\n    return parseConnectionError(e, truncateQuery(url), \"WS\");\n  }\n\n  private resetMaxListeners() {\n    if (this.events.getMaxListeners() > EVENT_EMITTER_MAX_LISTENERS_DEFAULT) {\n      this.events.setMaxListeners(EVENT_EMITTER_MAX_LISTENERS_DEFAULT);\n    }\n  }\n\n  private emitError(errorEvent: Error) {\n    const error = this.parseError(\n      new Error(\n        errorEvent?.message || `WebSocket connection failed for host: ${truncateQuery(this.url)}`,\n      ),\n    );\n    this.events.emit(\"register_error\", error);\n    return error;\n  }\n}\n\nexport default WsConnection;\n"], "names": ["wssUrl", "EVENT_EMITTER_MAX_LISTENERS_DEFAULT", "WS", "resolveWebSocketImplementation", "url", "EventEmitter", "isWsUrl", "event", "listener", "resolve", "reject", "payload", "safeJsonStringify", "e", "currentMaxListeners", "error", "opts", "isReactNative", "isLocalhostUrl", "socket", "hasBuiltInWebSocket", "errorEvent", "safeJsonParse", "id", "message", "formatJsonRpcError", "parseConnectionError", "truncate<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;AAAO,MAAM,8BAAiC,CAAA,IACxC,OAAO,SAAA,EAAc,YAChB,SACE,CAAA,OAAO,MAAW,EAAA,WAAA,EAAe,OAAO,MAAO,CAAA,SAAA,EAAc,WAC/D,CAAA,MAAA,CAAO,UACL,OAAO,MAAA,EAAW,WAAe,EAAA,OAAO,MAAO,CAAA,SAAA,EAAc,WAC/D,CAAA,MAAA,CAAO,UACL,OAAO,IAAA,EAAS,WAAe,EAAA,OAAO,KAAK,SAAc,EAAA,WAAA,CAC3D,IAAK,CAAA,SAAA,CAGP,QAAQ,IAAI,CAAA,CAGR,mBAAsB,CAAA,IACjC,OAAO,SAAA,EAAc,WACpB,EAAA,OAAO,QAAW,WAAe,EAAA,OAAO,MAAO,CAAA,SAAA,EAAc,aAC7D,OAAO,MAAA,EAAW,WAAe,EAAA,OAAO,OAAO,SAAc,EAAA,WAAA,EAC7D,OAAO,IAAA,EAAS,aAAe,OAAO,IAAA,CAAK,SAAc,EAAA,WAAA,CAI/C,cAAiBA,CAAmBA,EAAAA,CAAAA,CAAO,KAAM,CAAA,GAAG,EAAE,CAAC;;ACRpE,MAAMC,CAAsC,CAAA,EAAA,CAEtCC,CAAKC,CAAAA,8BAAAA,EAEJ,CAAA,MAAM,YAA2C,CAOtD,WAAmBC,CAAAA,CAAAA,CAAa,CAAb,IAAA,CAAA,GAAA,CAAAA,CANnB,CAAA,IAAA,CAAO,MAAS,CAAA,IAAIC,oBAIpB,IAAQ,CAAA,WAAA,CAAc,CAGpB,CAAA,CAAA,GAAI,CAACC,oBAAAA,CAAQF,CAAG,CAAA,CACd,MAAM,IAAI,KAAM,CAAA,CAAA,0DAAA,EAA6DA,CAAK,CAAA,CAAA,CAAA,CAEpF,IAAK,CAAA,GAAA,CAAMA,EACb,CAEA,IAAI,SAAqB,EAAA,CACvB,OAAO,OAAO,IAAK,CAAA,MAAA,EAAW,WAChC,CAEA,IAAI,UAAA,EAAsB,CACxB,OAAO,IAAK,CAAA,WACd,CAEO,EAAGG,CAAAA,CAAAA,CAAeC,CAAqB,CAAA,CAC5C,IAAK,CAAA,MAAA,CAAO,EAAGD,CAAAA,CAAAA,CAAOC,CAAQ,EAChC,CAEO,IAAA,CAAKD,CAAeC,CAAAA,CAAAA,CAAqB,CAC9C,IAAA,CAAK,OAAO,IAAKD,CAAAA,CAAAA,CAAOC,CAAQ,EAClC,CAEO,GAAA,CAAID,CAAeC,CAAAA,CAAAA,CAAqB,CAC7C,IAAA,CAAK,MAAO,CAAA,GAAA,CAAID,CAAOC,CAAAA,CAAQ,EACjC,CAEO,eAAeD,CAAeC,CAAAA,CAAAA,CAAqB,CACxD,IAAA,CAAK,MAAO,CAAA,cAAA,CAAeD,CAAOC,CAAAA,CAAQ,EAC5C,CAEA,MAAa,IAAA,CAAKJ,CAAc,CAAA,IAAA,CAAK,GAAoB,CAAA,CACvD,MAAM,IAAK,CAAA,QAAA,CAASA,CAAG,EACzB,CAEA,MAAa,KAAuB,EAAA,CAClC,OAAO,IAAI,OAAc,CAAA,CAACK,CAASC,CAAAA,CAAAA,GAAW,CAC5C,GAAI,OAAO,IAAK,CAAA,MAAA,EAAW,WAAa,CAAA,CACtCA,CAAO,CAAA,IAAI,KAAM,CAAA,2BAA2B,CAAC,CAAA,CAC7C,MAGF,CAAA,IAAA,CAAK,MAAO,CAAA,OAAA,CAAWH,CAAU,EAAA,CAC/B,KAAK,OAAQA,CAAAA,CAAK,CAClBE,CAAAA,CAAAA,GACF,CAAA,CAEA,IAAK,CAAA,MAAA,CAAO,KAAM,GACpB,CAAC,CACH,CAEA,MAAa,IAAKE,CAAAA,CAAAA,CAAwC,CACpD,OAAO,IAAA,CAAK,MAAW,EAAA,WAAA,GACzB,IAAK,CAAA,MAAA,CAAS,MAAM,IAAA,CAAK,QAAS,EAAA,CAAA,CAEpC,GAAI,CACF,IAAK,CAAA,MAAA,CAAO,IAAKC,CAAAA,0BAAAA,CAAkBD,CAAO,CAAC,EAC7C,CAASE,MAAAA,CAAAA,CAAP,CACA,IAAA,CAAK,OAAQF,CAAAA,CAAAA,CAAQ,EAAIE,CAAAA,CAAU,EACrC,CACF,CAIQ,QAAA,CAAST,CAAM,CAAA,IAAA,CAAK,IAAyB,CACnD,GAAI,CAACE,oBAAAA,CAAQF,CAAG,CAAA,CACd,MAAM,IAAI,KAAM,CAAA,CAAA,0DAAA,EAA6DA,CAAK,CAAA,CAAA,CAAA,CAEpF,GAAI,IAAA,CAAK,WAAa,CAAA,CACpB,MAAMU,CAAsB,CAAA,IAAA,CAAK,MAAO,CAAA,eAAA,EACxC,CAAA,OAAA,CACE,IAAK,CAAA,MAAA,CAAO,aAAc,CAAA,gBAAgB,CAAKA,EAAAA,CAAAA,EAC/C,IAAK,CAAA,MAAA,CAAO,aAAc,CAAA,MAAM,GAAKA,CAErC,GAAA,IAAA,CAAK,MAAO,CAAA,eAAA,CAAgBA,CAAsB,CAAA,CAAC,CAE9C,CAAA,IAAI,QAAQ,CAACL,CAAAA,CAASC,CAAW,GAAA,CACtC,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,gBAAA,CAAmBK,GAAU,CAC5C,IAAA,CAAK,iBAAkB,EAAA,CACvBL,CAAOK,CAAAA,CAAK,EACd,CAAC,CACD,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,MAAQ,CAAA,IAAM,CAE7B,GADA,KAAK,iBAAkB,EAAA,CACnB,OAAO,IAAA,CAAK,MAAW,EAAA,WAAA,CACzB,OAAOL,CAAAA,CAAO,IAAI,KAAA,CAAM,4CAA4C,CAAC,CAEvED,CAAAA,CAAAA,CAAQ,IAAK,CAAA,MAAM,EACrB,CAAC,EACH,CAAC,CAAA,CAEH,OAAK,IAAA,CAAA,GAAA,CAAML,CACX,CAAA,IAAA,CAAK,WAAc,CAAA,CAAA,CAAA,CAEZ,IAAI,OAAA,CAAQ,CAACK,CAAAA,CAASC,CAAW,GAAA,CACtC,MAAMM,CAAQC,CAAAA,0BAAAA,EAAiE,CAAA,KAAA,CAAA,CAA/C,CAAE,kBAAA,CAAoB,CAACC,2BAAAA,CAAed,CAAG,CAAE,CACrEe,CAAAA,CAAAA,CAAoB,IAAIjB,CAAAA,CAAGE,CAAK,CAAA,GAAIY,CAAI,CAAA,CAC1CI,mBAAoB,EAAA,CACtBD,CAAO,CAAA,OAAA,CAAWZ,CAAiB,EAAA,CACjC,MAAMc,CAAAA,CAAad,CACnBG,CAAAA,CAAAA,CAAO,IAAK,CAAA,SAAA,CAAUW,CAAW,CAAA,KAAK,CAAC,EACzC,CAAA,CAECF,CAAe,CAAA,EAAA,CAAG,OAAUE,CAAAA,CAAAA,EAAoB,CAC/CX,CAAAA,CAAO,IAAK,CAAA,SAAA,CAAUW,CAAU,CAAC,EACnC,CAAC,CAEHF,CAAAA,CAAAA,CAAO,OAAS,IAAM,CACpB,IAAK,CAAA,MAAA,CAAOA,CAAM,CAAA,CAClBV,CAAQU,CAAAA,CAAM,EAChB,EACF,CAAC,CACH,CAEQ,MAAA,CAAOA,CAAmB,CAAA,CAChCA,EAAO,SAAaZ,CAAAA,CAAAA,EAAwB,IAAK,CAAA,SAAA,CAAUA,CAAK,CAAA,CAChEY,CAAO,CAAA,OAAA,CAAWZ,CAAU,EAAA,IAAA,CAAK,OAAQA,CAAAA,CAAK,CAC9C,CAAA,IAAA,CAAK,MAASY,CAAAA,CAAAA,CACd,KAAK,WAAc,CAAA,CAAA,CAAA,CACnB,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,MAAM,EACzB,CAEQ,OAAQZ,CAAAA,CAAAA,CAAmB,CACjC,IAAA,CAAK,MAAS,CAAA,KAAA,CAAA,CACd,IAAK,CAAA,WAAA,CAAc,GACnB,IAAK,CAAA,MAAA,CAAO,IAAK,CAAA,OAAA,CAASA,CAAK,EACjC,CAEQ,SAAA,CAAU,CAAkB,CAAA,CAClC,GAAI,OAAO,CAAE,CAAA,IAAA,EAAS,WAAa,CAAA,OACnC,MAAMI,CAA0B,CAAA,OAAO,CAAE,CAAA,IAAA,EAAS,QAAWW,CAAAA,sBAAAA,CAAc,CAAE,CAAA,IAAI,CAAI,CAAA,CAAA,CAAE,IACvF,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,SAAWX,CAAAA,CAAO,EACrC,CAEQ,OAAA,CAAQY,CAAYV,CAAAA,CAAAA,CAAU,CACpC,MAAME,CAAQ,CAAA,IAAA,CAAK,UAAWF,CAAAA,CAAC,CACzBW,CAAAA,CAAAA,CAAUT,CAAM,CAAA,OAAA,EAAWA,CAAM,CAAA,QAAA,GACjCJ,CAAUc,CAAAA,+BAAAA,CAAmBF,CAAIC,CAAAA,CAAO,CAC9C,CAAA,IAAA,CAAK,MAAO,CAAA,IAAA,CAAK,SAAWb,CAAAA,CAAO,EACrC,CAEQ,UAAW,CAAA,CAAA,CAAUP,CAAM,CAAA,IAAA,CAAK,IAAK,CAC3C,OAAOsB,iCAAqB,CAAA,CAAA,CAAGC,aAAcvB,CAAAA,CAAG,CAAG,CAAA,IAAI,CACzD,CAEQ,iBAAoB,EAAA,CACtB,IAAK,CAAA,MAAA,CAAO,eAAgB,EAAA,CAAIH,GAClC,IAAK,CAAA,MAAA,CAAO,eAAgBA,CAAAA,CAAmC,EAEnE,CAEQ,SAAUoB,CAAAA,CAAAA,CAAmB,CACnC,MAAMN,CAAQ,CAAA,IAAA,CAAK,UACjB,CAAA,IAAI,KACFM,CAAAA,CAAAA,CAAAA,EAAA,YAAAA,CAAY,CAAA,OAAA,GAAW,CAAyCM,sCAAAA,EAAAA,aAAAA,CAAc,IAAK,CAAA,GAAG,CACxF,CAAA,CAAA,CACF,EACA,OAAK,IAAA,CAAA,MAAA,CAAO,IAAK,CAAA,gBAAA,CAAkBZ,CAAK,CAAA,CACjCA,CACT,CACF;;;;;"}