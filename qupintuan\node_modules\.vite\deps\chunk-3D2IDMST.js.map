{"version": 3, "sources": ["../../@wagmi/core/src/utils/getAction.ts", "../../@wagmi/core/src/actions/call.ts", "../../@wagmi/core/src/version.ts", "../../@wagmi/core/src/utils/getVersion.ts", "../../@wagmi/core/src/errors/base.ts", "../../@wagmi/core/src/errors/config.ts", "../../@wagmi/core/src/actions/connect.ts", "../../@wagmi/core/src/actions/getConnectorClient.ts", "../../@wagmi/core/src/actions/deployContract.ts", "../../@wagmi/core/src/actions/disconnect.ts", "../../@wagmi/core/src/utils/getUnit.ts", "../../@wagmi/core/src/actions/estimateFeesPerGas.ts", "../../@wagmi/core/src/actions/estimateGas.ts", "../../@wagmi/core/src/actions/estimateMaxPriorityFeePerGas.ts", "../../@wagmi/core/src/actions/getAccount.ts", "../../@wagmi/core/src/actions/multicall.ts", "../../@wagmi/core/src/actions/readContract.ts", "../../@wagmi/core/src/actions/readContracts.ts", "../../@wagmi/core/src/actions/getBalance.ts", "../../@wagmi/core/src/actions/getBlock.ts", "../../@wagmi/core/src/actions/getBlockNumber.ts", "../../@wagmi/core/src/actions/getBlockTransactionCount.ts", "../../@wagmi/core/src/actions/getBytecode.ts", "../../@wagmi/core/src/actions/getCallsStatus.ts", "../../@wagmi/core/src/actions/getCapabilities.ts", "../../@wagmi/core/src/actions/getChainId.ts", "../../@wagmi/core/src/utils/deepEqual.ts", "../../@wagmi/core/src/actions/getChains.ts", "../../@wagmi/core/src/actions/getClient.ts", "../../@wagmi/core/src/actions/getConnections.ts", "../../@wagmi/core/src/actions/getConnectors.ts", "../../@wagmi/core/src/actions/getEnsAddress.ts", "../../@wagmi/core/src/actions/getEnsAvatar.ts", "../../@wagmi/core/src/actions/getEnsName.ts", "../../@wagmi/core/src/actions/getEnsResolver.ts", "../../@wagmi/core/src/actions/getEnsText.ts", "../../@wagmi/core/src/actions/getFeeHistory.ts", "../../@wagmi/core/src/actions/getGasPrice.ts", "../../@wagmi/core/src/actions/getProof.ts", "../../@wagmi/core/src/actions/getPublicClient.ts", "../../@wagmi/core/src/actions/getStorageAt.ts", "../../@wagmi/core/src/actions/getToken.ts", "../../@wagmi/core/src/actions/getTransaction.ts", "../../@wagmi/core/src/actions/getTransactionConfirmations.ts", "../../@wagmi/core/src/actions/getTransactionCount.ts", "../../@wagmi/core/src/actions/getTransactionReceipt.ts", "../../@wagmi/core/src/actions/getWalletClient.ts", "../../@wagmi/core/src/actions/prepareTransactionRequest.ts", "../../@wagmi/core/src/actions/reconnect.ts", "../../@wagmi/core/src/actions/sendCalls.ts", "../../@wagmi/core/src/actions/sendTransaction.ts", "../../@wagmi/core/src/actions/showCallsStatus.ts", "../../@wagmi/core/src/actions/signMessage.ts", "../../@wagmi/core/src/actions/signTypedData.ts", "../../@wagmi/core/src/actions/simulateContract.ts", "../../@wagmi/core/src/actions/switchAccount.ts", "../../@wagmi/core/src/errors/connector.ts", "../../@wagmi/core/src/actions/switchChain.ts", "../../@wagmi/core/src/actions/verifyMessage.ts", "../../@wagmi/core/src/actions/verifyTypedData.ts", "../../@wagmi/core/src/actions/waitForCallsStatus.ts", "../../@wagmi/core/src/actions/waitForTransactionReceipt.ts", "../../@wagmi/core/src/actions/watchAccount.ts", "../../@wagmi/core/src/actions/watchAsset.ts", "../../@wagmi/core/src/actions/watchBlockNumber.ts", "../../@wagmi/core/src/actions/watchBlocks.ts", "../../@wagmi/core/src/actions/watchChainId.ts", "../../@wagmi/core/src/actions/watchClient.ts", "../../@wagmi/core/src/actions/watchConnections.ts", "../../@wagmi/core/src/actions/watchConnectors.ts", "../../@wagmi/core/src/actions/watchContractEvent.ts", "../../@wagmi/core/src/actions/watchPendingTransactions.ts", "../../@wagmi/core/src/actions/watchPublicClient.ts", "../../@wagmi/core/src/actions/writeContract.ts"], "sourcesContent": ["import type {\n  Account,\n  Chain,\n  Client,\n  PublicActions,\n  RpcSchema,\n  Transport,\n  WalletActions,\n} from 'viem'\n\n/**\n * Retrieves and returns an action from the client (if exists), and falls\n * back to the tree-shakable action.\n *\n * Useful for extracting overridden actions from a client (ie. if a consumer\n * wants to override the `sendTransaction` implementation).\n */\nexport function getAction<\n  transport extends Transport,\n  chain extends Chain | undefined,\n  account extends Account | undefined,\n  rpcSchema extends RpcSchema | undefined,\n  extended extends { [key: string]: unknown },\n  client extends Client<transport, chain, account, rpcSchema, extended>,\n  parameters,\n  returnType,\n>(\n  client: client,\n  actionFn: (_: any, parameters: parameters) => returnType,\n  // Some minifiers drop `Function.prototype.name`, or replace it with short letters,\n  // meaning that `actionFn.name` will not always work. For that case, the consumer\n  // needs to pass the name explicitly.\n  name: keyof PublicActions | keyof WalletActions,\n): (parameters: parameters) => returnType {\n  const action_implicit = client[actionFn.name]\n  if (typeof action_implicit === 'function')\n    return action_implicit as (params: parameters) => returnType\n\n  const action_explicit = client[name]\n  if (typeof action_explicit === 'function')\n    return action_explicit as (params: parameters) => returnType\n\n  return (params) => actionFn(client, params)\n}\n", "import type {\n  CallErrorType as viem_CallErrorType,\n  CallParameters as viem_CallParameters,\n  CallReturnType as viem_CallReturnType,\n} from 'viem'\nimport { call as viem_call } from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type CallParameters<config extends Config = Config> =\n  viem_CallParameters & ChainIdParameter<config>\n\nexport type CallReturnType = viem_CallReturnType\n\nexport type CallErrorType = viem_CallErrorType\n\nexport async function call<config extends Config>(\n  config: config,\n  parameters: CallParameters<config>,\n): Promise<CallReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_call, 'call')\n  return action(rest)\n}\n", "export const version = '2.18.1'\n", "import { version } from '../version.js'\n\nexport const getVersion = () => `@wagmi/core@${version}`\n", "import type { Compute, OneOf } from '../types/utils.js'\nimport { getVersion } from '../utils/getVersion.js'\n\nexport type ErrorType<name extends string = 'Error'> = Error & { name: name }\n\ntype BaseErrorOptions = Compute<\n  OneOf<{ details?: string | undefined } | { cause: BaseError | Error }> & {\n    docsPath?: string | undefined\n    docsSlug?: string | undefined\n    metaMessages?: string[] | undefined\n  }\n>\n\nexport type BaseErrorType = BaseError & { name: 'WagmiCoreError' }\nexport class BaseError extends Error {\n  details: string\n  docsPath?: string | undefined\n  metaMessages?: string[] | undefined\n  shortMessage: string\n\n  override name = 'WagmiCoreError'\n  get docsBaseUrl() {\n    return 'https://wagmi.sh/core'\n  }\n  get version() {\n    return getVersion()\n  }\n\n  constructor(shortMessage: string, options: BaseErrorOptions = {}) {\n    super()\n\n    const details =\n      options.cause instanceof BaseError\n        ? options.cause.details\n        : options.cause?.message\n          ? options.cause.message\n          : options.details!\n    const docsPath =\n      options.cause instanceof BaseError\n        ? options.cause.docsPath || options.docsPath\n        : options.docsPath\n\n    this.message = [\n      shortMessage || 'An error occurred.',\n      '',\n      ...(options.metaMessages ? [...options.metaMessages, ''] : []),\n      ...(docsPath\n        ? [\n            `Docs: ${this.docsBaseUrl}${docsPath}.html${\n              options.docsSlug ? `#${options.docsSlug}` : ''\n            }`,\n          ]\n        : []),\n      ...(details ? [`Details: ${details}`] : []),\n      `Version: ${this.version}`,\n    ].join('\\n')\n\n    if (options.cause) this.cause = options.cause\n    this.details = details\n    this.docsPath = docsPath\n    this.metaMessages = options.metaMessages\n    this.shortMessage = shortMessage\n  }\n\n  walk(fn?: (err: unknown) => boolean) {\n    return this.#walk(this, fn)\n  }\n\n  #walk(err: unknown, fn?: (err: unknown) => boolean): unknown {\n    if (fn?.(err)) return err\n    if ((err as Error).cause) return this.#walk((err as Error).cause, fn)\n    return err\n  }\n}\n", "import type { Address } from 'viem'\n\nimport type { Connector } from '../createConfig.js'\nimport { BaseError } from './base.js'\n\nexport type ChainNotConfiguredErrorType = ChainNotConfiguredError & {\n  name: 'ChainNotConfiguredError'\n}\nexport class ChainNotConfiguredError extends BaseError {\n  override name = 'ChainNotConfiguredError'\n  constructor() {\n    super('Chain not configured.')\n  }\n}\n\nexport type ConnectorAlreadyConnectedErrorType =\n  ConnectorAlreadyConnectedError & {\n    name: 'ConnectorAlreadyConnectedError'\n  }\nexport class ConnectorAlreadyConnectedError extends BaseError {\n  override name = 'ConnectorAlreadyConnectedError'\n  constructor() {\n    super('Connector already connected.')\n  }\n}\n\nexport type ConnectorNotConnectedErrorType = ConnectorNotConnectedError & {\n  name: 'ConnectorNotConnectedError'\n}\nexport class ConnectorNotConnectedError extends BaseError {\n  override name = 'ConnectorNotConnectedError'\n  constructor() {\n    super('Connector not connected.')\n  }\n}\n\nexport type ConnectorNotFoundErrorType = ConnectorNotFoundError & {\n  name: 'ConnectorNotFoundError'\n}\nexport class ConnectorNotFoundError extends BaseError {\n  override name = 'ConnectorNotFoundError'\n  constructor() {\n    super('Connector not found.')\n  }\n}\n\nexport type ConnectorAccountNotFoundErrorType =\n  ConnectorAccountNotFoundError & {\n    name: 'ConnectorAccountNotFoundError'\n  }\nexport class ConnectorAccountNotFoundError extends BaseError {\n  override name = 'ConnectorAccountNotFoundError'\n  constructor({\n    address,\n    connector,\n  }: {\n    address: Address\n    connector: Connector\n  }) {\n    super(`Account \"${address}\" not found for connector \"${connector.name}\".`)\n  }\n}\n\nexport type ConnectorChainMismatchErrorType = ConnectorAccountNotFoundError & {\n  name: 'ConnectorChainMismatchError'\n}\nexport class ConnectorChainMismatchError extends BaseError {\n  override name = 'ConnectorChainMismatchError'\n  constructor({\n    connectionChainId,\n    connectorChainId,\n  }: {\n    connectionChainId: number\n    connectorChainId: number\n  }) {\n    super(\n      `The current chain of the connector (id: ${connectorChainId}) does not match the connection's chain (id: ${connectionChainId}).`,\n      {\n        metaMessages: [\n          `Current Chain ID:  ${connectorChainId}`,\n          `Expected Chain ID: ${connectionChainId}`,\n        ],\n      },\n    )\n  }\n}\n\nexport type ConnectorUnavailableReconnectingErrorType =\n  ConnectorUnavailableReconnectingError & {\n    name: 'ConnectorUnavailableReconnectingError'\n  }\nexport class ConnectorUnavailableReconnectingError extends BaseError {\n  override name = 'ConnectorUnavailableReconnectingError'\n  constructor({ connector }: { connector: { name: string } }) {\n    super(`Connector \"${connector.name}\" unavailable while reconnecting.`, {\n      details: [\n        'During the reconnection step, the only connector methods guaranteed to be available are: `id`, `name`, `type`, `uid`.',\n        'All other methods are not guaranteed to be available until reconnection completes and connectors are fully restored.',\n        'This error commonly occurs for connectors that asynchronously inject after reconnection has already started.',\n      ].join(' '),\n    })\n  }\n}\n", "import type {\n  Address,\n  ResourceUnavailableRpcErrorType,\n  UserRejectedRequestErrorType,\n} from 'viem'\n\nimport type { CreateConnectorFn } from '../connectors/createConnector.js'\nimport type { Config, Connector } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport {\n  ConnectorAlreadyConnectedError,\n  type ConnectorAlreadyConnectedErrorType,\n} from '../errors/config.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\n\nexport type ConnectParameters<\n  config extends Config = Config,\n  connector extends Connector | CreateConnectorFn =\n    | Connector\n    | CreateConnectorFn,\n  ///\n  parameters extends unknown | undefined =\n    | (connector extends CreateConnectorFn\n        ? Omit<\n            NonNullable<Parameters<ReturnType<connector>['connect']>[0]>,\n            'isReconnecting'\n          >\n        : never)\n    | (connector extends Connector\n        ? Omit<\n            NonNullable<Parameters<connector['connect']>[0]>,\n            'isReconnecting'\n          >\n        : never),\n> = Compute<\n  ChainIdParameter<config> & {\n    connector: connector | CreateConnectorFn\n  }\n> &\n  parameters\n\nexport type ConnectReturnType<config extends Config = Config> = {\n  accounts: readonly [Address, ...Address[]]\n  chainId:\n    | config['chains'][number]['id']\n    | (number extends config['chains'][number]['id'] ? number : number & {})\n}\n\nexport type ConnectErrorType =\n  | ConnectorAlreadyConnectedErrorType\n  // connector.connect()\n  | UserRejectedRequestErrorType\n  | ResourceUnavailableRpcErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n\n/** https://wagmi.sh/core/api/actions/connect */\nexport async function connect<\n  config extends Config,\n  connector extends Connector | CreateConnectorFn,\n>(\n  config: config,\n  parameters: ConnectParameters<config, connector>,\n): Promise<ConnectReturnType<config>> {\n  // \"Register\" connector if not already created\n  let connector: Connector\n  if (typeof parameters.connector === 'function') {\n    connector = config._internal.connectors.setup(parameters.connector)\n  } else connector = parameters.connector\n\n  // Check if connector is already connected\n  if (connector.uid === config.state.current)\n    throw new ConnectorAlreadyConnectedError()\n\n  try {\n    config.setState((x) => ({ ...x, status: 'connecting' }))\n    connector.emitter.emit('message', { type: 'connecting' })\n\n    const { connector: _, ...rest } = parameters\n    const data = await connector.connect(rest)\n    const accounts = data.accounts as readonly [Address, ...Address[]]\n\n    connector.emitter.off('connect', config._internal.events.connect)\n    connector.emitter.on('change', config._internal.events.change)\n    connector.emitter.on('disconnect', config._internal.events.disconnect)\n\n    await config.storage?.setItem('recentConnectorId', connector.id)\n    config.setState((x) => ({\n      ...x,\n      connections: new Map(x.connections).set(connector.uid, {\n        accounts,\n        chainId: data.chainId,\n        connector: connector,\n      }),\n      current: connector.uid,\n      status: 'connected',\n    }))\n\n    return { accounts, chainId: data.chainId }\n  } catch (error) {\n    config.setState((x) => ({\n      ...x,\n      // Keep existing connector connected in case of error\n      status: x.current ? 'connected' : 'disconnected',\n    }))\n    throw error\n  }\n}\n", "import {\n  type Account,\n  type Address,\n  type BaseErrorType,\n  type Client,\n  createClient,\n  custom,\n} from 'viem'\nimport { getAddress, parseAccount } from 'viem/utils'\n\nimport type { Config, Connection } from '../createConfig.js'\nimport type { ErrorType } from '../errors/base.js'\nimport {\n  ConnectorAccountNotFoundError,\n  type ConnectorAccountNotFoundErrorType,\n  ConnectorChainMismatchError,\n  type ConnectorChainMismatchErrorType,\n  ConnectorNotConnectedError,\n  type ConnectorNotConnectedErrorType,\n  ConnectorUnavailableReconnectingError,\n  type ConnectorUnavailableReconnectingErrorType,\n} from '../errors/config.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\n\nexport type GetConnectorClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  ChainIdParameter<config, chainId> &\n    ConnectorParameter & {\n      /**\n       * Account to use for the client.\n       *\n       * - `Account | Address`: An Account MUST exist on the connector.\n       * - `null`: Account MAY NOT exist on the connector. This is useful for\n       *   actions that can infer the account from the connector (e.g. sending a\n       *   call without a connected account – the user will be prompted to select\n       *   an account within the wallet).\n       */\n      account?: Address | Account | null | undefined\n    }\n>\n\nexport type GetConnectorClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  Client<\n    config['_internal']['transports'][chainId],\n    Extract<config['chains'][number], { id: chainId }>,\n    Account\n  >\n>\n\nexport type GetConnectorClientErrorType =\n  | ConnectorAccountNotFoundErrorType\n  | ConnectorChainMismatchErrorType\n  | ConnectorNotConnectedErrorType\n  | ConnectorUnavailableReconnectingErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n\n/** https://wagmi.sh/core/api/actions/getConnectorClient */\nexport async function getConnectorClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetConnectorClientParameters<config, chainId> = {},\n): Promise<GetConnectorClientReturnType<config, chainId>> {\n  // Get connection\n  let connection: Connection | undefined\n  if (parameters.connector) {\n    const { connector } = parameters\n    if (\n      config.state.status === 'reconnecting' &&\n      !connector.getAccounts &&\n      !connector.getChainId\n    )\n      throw new ConnectorUnavailableReconnectingError({ connector })\n\n    const [accounts, chainId] = await Promise.all([\n      connector.getAccounts().catch((e) => {\n        if (parameters.account === null) return []\n        throw e\n      }),\n      connector.getChainId(),\n    ])\n    connection = {\n      accounts: accounts as readonly [Address, ...Address[]],\n      chainId,\n      connector,\n    }\n  } else connection = config.state.connections.get(config.state.current!)\n  if (!connection) throw new ConnectorNotConnectedError()\n\n  const chainId = parameters.chainId ?? connection.chainId\n\n  // Check connector using same chainId as connection\n  const connectorChainId = await connection.connector.getChainId()\n  if (connectorChainId !== connection.chainId)\n    throw new ConnectorChainMismatchError({\n      connectionChainId: connection.chainId,\n      connectorChainId,\n    })\n\n  // If connector has custom `getClient` implementation\n  type Return = GetConnectorClientReturnType<config, chainId>\n  const connector = connection.connector\n  if (connector.getClient)\n    return connector.getClient({ chainId }) as unknown as Return\n\n  // Default using `custom` transport\n  const account = parseAccount(parameters.account ?? connection.accounts[0]!)\n  if (account) account.address = getAddress(account.address) // TODO: Checksum address as part of `parseAccount`?\n\n  // If account was provided, check that it exists on the connector\n  if (\n    parameters.account &&\n    !connection.accounts.some(\n      (x) => x.toLowerCase() === account.address.toLowerCase(),\n    )\n  )\n    throw new ConnectorAccountNotFoundError({\n      address: account.address,\n      connector,\n    })\n\n  const chain = config.chains.find((chain) => chain.id === chainId)\n  const provider = (await connection.connector.getProvider({ chainId })) as {\n    request(...args: any): Promise<any>\n  }\n\n  return createClient({\n    account,\n    chain,\n    name: 'Connector Client',\n    transport: (opts) => custom(provider)({ ...opts, retryCount: 0 }),\n  }) as Return\n}\n", "import type { Abi, Account, Chain, Client, ContractConstructorArgs } from 'viem'\nimport {\n  type DeployContractErrorType as viem_DeployContractErrorType,\n  type DeployContractParameters as viem_DeployContractParameters,\n  type DeployContractReturnType as viem_DeployContractReturnType,\n  deployContract as viem_deployContract,\n} from 'viem/actions'\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type DeployContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  allArgs = ContractConstructorArgs<abi>,\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: Compute<\n    Omit<\n      viem_DeployContractParameters<\n        abi,\n        chains[key],\n        Account,\n        chains[key],\n        allArgs\n      >,\n      'chain'\n    > &\n      ChainIdParameter<config, chainId> &\n      ConnectorParameter\n  >\n}[number]\n\nexport type DeployContractReturnType = viem_DeployContractReturnType\n\nexport type DeployContractErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_DeployContractErrorType\n\n/** https://wagmi.sh/core/api/actions/deployContract */\nexport async function deployContract<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: DeployContractParameters<abi, config, chainId>,\n): Promise<DeployContractReturnType> {\n  const { account, chainId, connector, ...rest } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account?.type === 'local')\n    client = config.getClient({ chainId })\n  else\n    client = await getConnectorClient(config, {\n      account: account ?? undefined,\n      chainId,\n      connector,\n    })\n\n  const action = getAction(client, viem_deployContract, 'deployContract')\n  const hash = await action({\n    ...(rest as any),\n    ...(account ? { account } : {}),\n    chain: chainId ? { id: chainId } : null,\n  })\n\n  return hash\n}\n", "import type { Config, Connection, Connector } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type {\n  ConnectorNotConnectedErrorType,\n  ConnectorNotFoundErrorType,\n} from '../errors/config.js'\nimport type { ConnectorParameter } from '../types/properties.js'\n\nexport type DisconnectParameters = ConnectorParameter\n\nexport type DisconnectReturnType = void\n\nexport type DisconnectErrorType =\n  | ConnectorNotFoundErrorType\n  | ConnectorNotConnectedErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n\n/** https://wagmi.sh/core/api/actions/disconnect */\nexport async function disconnect(\n  config: Config,\n  parameters: DisconnectParameters = {},\n): Promise<DisconnectReturnType> {\n  let connector: Connector | undefined\n  if (parameters.connector) connector = parameters.connector\n  else {\n    const { connections, current } = config.state\n    const connection = connections.get(current!)\n    connector = connection?.connector\n  }\n\n  const connections = config.state.connections\n\n  if (connector) {\n    await connector.disconnect()\n    connector.emitter.off('change', config._internal.events.change)\n    connector.emitter.off('disconnect', config._internal.events.disconnect)\n    connector.emitter.on('connect', config._internal.events.connect)\n\n    connections.delete(connector.uid)\n  }\n\n  config.setState((x) => {\n    // if no connections exist, move to disconnected state\n    if (connections.size === 0)\n      return {\n        ...x,\n        connections: new Map(),\n        current: null,\n        status: 'disconnected',\n      }\n\n    // switch over to another connection\n    const nextConnection = connections.values().next().value as Connection\n    return {\n      ...x,\n      connections: new Map(connections),\n      current: nextConnection.connector.uid,\n    }\n  })\n\n  // Set recent connector if exists\n  {\n    const current = config.state.current\n    if (!current) return\n    const connector = config.state.connections.get(current)?.connector\n    if (!connector) return\n    await config.storage?.setItem('recentConnectorId', connector.id)\n  }\n}\n", "import { weiUnits } from 'viem'\n\nimport type { Unit } from '../types/unit.js'\n\nexport function getUnit(unit: Unit) {\n  if (typeof unit === 'number') return unit\n  if (unit === 'wei') return 0\n  return Math.abs(weiUnits[unit])\n}\n", "import {\n  type Chain,\n  type FeeValuesEIP1559,\n  type FeeValuesLegacy,\n  type FeeValuesType,\n  formatUnits,\n} from 'viem'\nimport {\n  type EstimateFeesPerGasErrorType as viem_EstimateFeesPerGasErrorType,\n  type EstimateFeesPerGasParameters as viem_EstimateFeesPerGasParameters,\n  type EstimateFeesPerGasReturnType as viem_EstimateFeesPerGasReturnType,\n  estimateFeesPerGas as viem_estimateFeesPerGas,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Unit } from '../types/unit.js'\nimport type { Compute, UnionCompute, UnionLooseOmit } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport { getUnit } from '../utils/getUnit.js'\n\nexport type EstimateFeesPerGasParameters<\n  type extends FeeValuesType = FeeValuesType,\n  config extends Config = Config,\n> = UnionCompute<\n  UnionLooseOmit<\n    viem_EstimateFeesPerGasParameters<Chain, Chain, type>,\n    'chain'\n  > &\n    ChainIdParameter<config> & {\n      /** @deprecated */\n      formatUnits?: Unit | undefined\n    }\n>\n\nexport type EstimateFeesPerGasReturnType<\n  type extends FeeValuesType = FeeValuesType,\n> = Compute<\n  viem_EstimateFeesPerGasReturnType<type> & {\n    /** @deprecated */\n    formatted: UnionCompute<\n      | (type extends 'legacy' ? FeeValuesLegacy<string> : never)\n      | (type extends 'eip1559' ? FeeValuesEIP1559<string> : never)\n    >\n  }\n>\n\nexport type EstimateFeesPerGasErrorType = viem_EstimateFeesPerGasErrorType\n\nexport async function estimateFeesPerGas<\n  config extends Config,\n  type extends FeeValuesType = 'eip1559',\n>(\n  config: config,\n  parameters: EstimateFeesPerGasParameters<type, config> = {},\n): Promise<EstimateFeesPerGasReturnType<type>> {\n  const { chainId, formatUnits: units = 'gwei', ...rest } = parameters\n\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_estimateFeesPerGas,\n    'estimateFeesPerGas',\n  )\n\n  const { gasPrice, maxFeePerGas, maxPriorityFeePerGas } = await action({\n    ...rest,\n    chain: client.chain,\n  })\n\n  const unit = getUnit(units)\n  const formatted = {\n    gasPrice: gasPrice ? formatUnits(gasPrice, unit) : undefined,\n    maxFeePerGas: maxFeePerGas ? formatUnits(maxFeePerGas, unit) : undefined,\n    maxPriorityFeePerGas: maxPriorityFeePerGas\n      ? formatUnits(maxPriorityFeePerGas, unit)\n      : undefined,\n  }\n\n  return {\n    formatted,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n  } as EstimateFeesPerGasReturnType<type>\n}\n", "import type { Account, Address, Chain } from 'viem'\nimport {\n  type EstimateGasErrorType as viem_EstimateGasErrorType,\n  type EstimateGasParameters as viem_EstimateGasParameters,\n  type EstimateGasReturnType as viem_EstimateGasReturnType,\n  estimateGas as viem_estimateGas,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { UnionCompute, UnionLooseOmit } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type EstimateGasParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    UnionLooseOmit<viem_EstimateGasParameters<chains[key]>, 'chain'> &\n      ChainIdParameter<config, chainId> &\n      ConnectorParameter\n  >\n}[number]\n\nexport type EstimateGasReturnType = viem_EstimateGasReturnType\n\nexport type EstimateGasErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_EstimateGasErrorType\n\n/** https://wagmi.sh/core/api/actions/estimateGas */\nexport async function estimateGas<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(\n  config: config,\n  parameters: EstimateGasParameters<config, chainId>,\n): Promise<EstimateGasReturnType> {\n  const { chainId, connector, ...rest } = parameters\n\n  let account: Address | Account\n  if (parameters.account) account = parameters.account\n  else {\n    const connectorClient = await getConnectorClient(config, {\n      account: parameters.account,\n      chainId,\n      connector,\n    })\n    account = connectorClient.account\n  }\n\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_estimateGas, 'estimateGas')\n  return action({ ...(rest as viem_EstimateGasParameters), account })\n}\n", "import type { Chain } from 'viem'\nimport {\n  type EstimateMaxPriorityFeePerGasErrorType as viem_EstimateMaxPriorityFeePerGasErrorType,\n  type EstimateMaxPriorityFeePerGasParameters as viem_EstimateMaxPriorityFeePerGasParameters,\n  type EstimateMaxPriorityFeePerGasReturnType as viem_EstimateMaxPriorityFeePerGasReturnType,\n  estimateMaxPriorityFeePerGas as viem_estimateMaxPriorityFeePerGas,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, UnionLooseOmit } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type EstimateMaxPriorityFeePerGasParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  UnionLooseOmit<\n    viem_EstimateMaxPriorityFeePerGasParameters<Chain, Chain> &\n      ChainIdParameter<config, chainId>,\n    'chain'\n  >\n>\n\nexport type EstimateMaxPriorityFeePerGasReturnType =\n  viem_EstimateMaxPriorityFeePerGasReturnType\n\nexport type EstimateMaxPriorityFeePerGasErrorType =\n  viem_EstimateMaxPriorityFeePerGasErrorType\n\n/** https://wagmi.sh/core/api/actions/estimateMaxPriorityFeePerGas */\nexport async function estimateMaxPriorityFeePerGas<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: EstimateMaxPriorityFeePerGasParameters<config, chainId> = {},\n): Promise<EstimateMaxPriorityFeePerGasReturnType> {\n  const { chainId } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_estimateMaxPriorityFeePerGas,\n    'estimateMaxPriorityFeePerGas',\n  )\n  return action({ chain: client.chain })\n}\n", "import type { Address, Chain } from 'viem'\n\nimport type { Config, Connector } from '../createConfig.js'\n\nexport type GetAccountReturnType<\n  config extends Config = Config,\n  ///\n  chain = Config extends config ? Chain : config['chains'][number],\n> =\n  | {\n      address: Address\n      addresses: readonly [Address, ...Address[]]\n      chain: chain | undefined\n      chainId: number\n      connector: Connector\n      isConnected: true\n      isConnecting: false\n      isDisconnected: false\n      isReconnecting: false\n      status: 'connected'\n    }\n  | {\n      address: Address | undefined\n      addresses: readonly Address[] | undefined\n      chain: chain | undefined\n      chainId: number | undefined\n      connector: Connector | undefined\n      isConnected: boolean\n      isConnecting: false\n      isDisconnected: false\n      isReconnecting: true\n      status: 'reconnecting'\n    }\n  | {\n      address: Address | undefined\n      addresses: readonly Address[] | undefined\n      chain: chain | undefined\n      chainId: number | undefined\n      connector: Connector | undefined\n      isConnected: false\n      isReconnecting: false\n      isConnecting: true\n      isDisconnected: false\n      status: 'connecting'\n    }\n  | {\n      address: undefined\n      addresses: undefined\n      chain: undefined\n      chainId: undefined\n      connector: undefined\n      isConnected: false\n      isReconnecting: false\n      isConnecting: false\n      isDisconnected: true\n      status: 'disconnected'\n    }\n\n/** https://wagmi.sh/core/api/actions/getAccount */\nexport function getAccount<config extends Config>(\n  config: config,\n): GetAccountReturnType<config> {\n  const uid = config.state.current!\n  const connection = config.state.connections.get(uid)\n  const addresses = connection?.accounts\n  const address = addresses?.[0]\n  const chain = config.chains.find(\n    (chain) => chain.id === connection?.chainId,\n  ) as GetAccountReturnType<config>['chain']\n  const status = config.state.status\n\n  switch (status) {\n    case 'connected':\n      return {\n        address: address!,\n        addresses: addresses!,\n        chain,\n        chainId: connection?.chainId!,\n        connector: connection?.connector!,\n        isConnected: true,\n        isConnecting: false,\n        isDisconnected: false,\n        isReconnecting: false,\n        status,\n      }\n    case 'reconnecting':\n      return {\n        address,\n        addresses,\n        chain,\n        chainId: connection?.chainId,\n        connector: connection?.connector,\n        isConnected: !!address,\n        isConnecting: false,\n        isDisconnected: false,\n        isReconnecting: true,\n        status,\n      }\n    case 'connecting':\n      return {\n        address,\n        addresses,\n        chain,\n        chainId: connection?.chainId,\n        connector: connection?.connector,\n        isConnected: false,\n        isConnecting: true,\n        isDisconnected: false,\n        isReconnecting: false,\n        status,\n      }\n    case 'disconnected':\n      return {\n        address: undefined,\n        addresses: undefined,\n        chain: undefined,\n        chainId: undefined,\n        connector: undefined,\n        isConnected: false,\n        isConnecting: false,\n        isDisconnected: true,\n        isReconnecting: false,\n        status,\n      }\n  }\n}\n", "import type {\n  ContractFunctionParameters,\n  MulticallErrorType as viem_MulticallErrorType,\n  MulticallParameters as viem_MulticallParameters,\n  MulticallReturnType as viem_MulticallReturnType,\n} from 'viem'\nimport { multicall as viem_multicall } from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type MulticallParameters<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  config extends Config = Config,\n> = viem_MulticallParameters<contracts, allowFailure> & ChainIdParameter<config>\n\nexport type MulticallReturnType<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n> = viem_MulticallReturnType<contracts, allowFailure>\n\nexport type MulticallErrorType = viem_MulticallErrorType\n\nexport async function multicall<\n  config extends Config,\n  const contracts extends readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n>(\n  config: config,\n  parameters: MulticallParameters<contracts, allowFailure, config>,\n): Promise<MulticallReturnType<contracts, allowFailure>> {\n  const { allowFailure = true, chainId, contracts, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_multicall, 'multicall')\n  return action({\n    allowFailure,\n    contracts,\n    ...rest,\n  }) as Promise<MulticallReturnType<contracts, allowFailure>>\n}\n", "import type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\nimport {\n  type ReadContractErrorType as viem_ReadContractErrorType,\n  type ReadContractParameters as viem_ReadContractParameters,\n  type ReadContractReturnType as viem_ReadContractReturnType,\n  readContract as viem_readContract,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type ReadContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'pure' | 'view'\n  > = ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'pure' | 'view',\n    functionName\n  > = ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config = Config,\n> = viem_ReadContractParameters<abi, functionName, args> &\n  ChainIdParameter<config>\n\nexport type ReadContractReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'pure' | 'view'\n  > = ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'pure' | 'view',\n    functionName\n  > = ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n> = viem_ReadContractReturnType<abi, functionName, args>\n\nexport type ReadContractErrorType = viem_ReadContractErrorType\n\n/** https://wagmi.sh/core/api/actions/readContract */\nexport function readContract<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n>(\n  config: config,\n  parameters: ReadContractParameters<abi, functionName, args, config>,\n): Promise<ReadContractReturnType<abi, functionName, args>> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_readContract, 'readContract')\n  return action(rest as any)\n}\n", "import type {\n  ContractFunctionParameters,\n  MulticallParameters as viem_MulticallParameters,\n  MulticallReturnType as viem_MulticallReturnType,\n} from 'viem'\nimport { ContractFunctionExecutionError } from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { type MulticallErrorType, multicall } from './multicall.js'\nimport { type ReadContractErrorType, readContract } from './readContract.js'\n\nexport type ReadContractsParameters<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  config extends Config = Config,\n> = viem_MulticallParameters<\n  contracts,\n  allowFailure,\n  { properties: ChainIdParameter<config> }\n>\n\nexport type ReadContractsReturnType<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n> = viem_MulticallReturnType<contracts, allowFailure>\n\nexport type ReadContractsErrorType = MulticallErrorType | ReadContractErrorType\n\nexport async function readContracts<\n  config extends Config,\n  const contracts extends readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n>(\n  config: config,\n  parameters: ReadContractsParameters<contracts, allowFailure, config>,\n): Promise<ReadContractsReturnType<contracts, allowFailure>> {\n  const { allowFailure = true, blockNumber, blockTag, ...rest } = parameters\n  const contracts = parameters.contracts as (ContractFunctionParameters & {\n    chainId?: number | undefined\n  })[]\n\n  try {\n    const contractsByChainId: {\n      [chainId: number]: {\n        contract: ContractFunctionParameters\n        index: number\n      }[]\n    } = {}\n    for (const [index, contract] of contracts.entries()) {\n      const chainId = contract.chainId ?? config.state.chainId\n      if (!contractsByChainId[chainId]) contractsByChainId[chainId] = []\n      contractsByChainId[chainId]?.push({ contract, index })\n    }\n    const promises = () =>\n      Object.entries(contractsByChainId).map(([chainId, contracts]) =>\n        multicall(config, {\n          ...rest,\n          allowFailure,\n          blockNumber,\n          blockTag,\n          chainId: Number.parseInt(chainId),\n          contracts: contracts.map(({ contract }) => contract),\n        }),\n      )\n\n    const multicallResults = (await Promise.all(promises())).flat()\n    // Reorder the contract results back to the order they were\n    // provided in.\n    const resultIndexes = Object.values(contractsByChainId).flatMap(\n      (contracts) => contracts.map(({ index }) => index),\n    )\n    return multicallResults.reduce((results, result, index) => {\n      if (results) (results as unknown[])[resultIndexes[index]!] = result\n      return results\n    }, [] as unknown[]) as ReadContractsReturnType<contracts, allowFailure>\n  } catch (error) {\n    if (error instanceof ContractFunctionExecutionError) throw error\n\n    const promises = () =>\n      contracts.map((contract) =>\n        readContract(config, { ...contract, blockNumber, blockTag }),\n      )\n    if (allowFailure)\n      return (await Promise.allSettled(promises())).map((result) => {\n        if (result.status === 'fulfilled')\n          return { result: result.value, status: 'success' }\n        return { error: result.reason, result: undefined, status: 'failure' }\n      }) as ReadContractsReturnType<contracts, allowFailure>\n\n    return (await Promise.all(promises())) as ReadContractsReturnType<\n      contracts,\n      allowFailure\n    >\n  }\n}\n", "import { type Address, formatUnits, type Hex, hexToString, trim } from 'viem'\nimport {\n  type GetBalanceErrorType as viem_GetBalanceErrorType,\n  type GetBalanceParameters as viem_GetBalanceParameters,\n  getBalance as viem_getBalance,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Unit } from '../types/unit.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport { getUnit } from '../utils/getUnit.js'\nimport { type ReadContractsErrorType, readContracts } from './readContracts.js'\n\nexport type GetBalanceParameters<config extends Config = Config> = Compute<\n  ChainIdParameter<config> &\n    viem_GetBalanceParameters & {\n      /** @deprecated */\n      token?: Address | undefined\n      /** @deprecated */\n      unit?: Unit | undefined\n    }\n>\n\nexport type GetBalanceReturnType = {\n  decimals: number\n  /** @deprecated */\n  formatted: string\n  symbol: string\n  value: bigint\n}\n\nexport type GetBalanceErrorType = viem_GetBalanceErrorType\n\n/** https://wagmi.sh/core/api/actions/getBalance */\nexport async function getBalance<config extends Config>(\n  config: config,\n  parameters: GetBalanceParameters<config>,\n): Promise<GetBalanceReturnType> {\n  const {\n    address,\n    blockNumber,\n    blockTag,\n    chainId,\n    token: tokenAddress,\n    unit = 'ether',\n  } = parameters\n\n  if (tokenAddress) {\n    try {\n      return await getTokenBalance(config, {\n        balanceAddress: address,\n        chainId,\n        symbolType: 'string',\n        tokenAddress,\n      })\n    } catch (error) {\n      // In the chance that there is an error upon decoding the contract result,\n      // it could be likely that the contract data is represented as bytes32 instead\n      // of a string.\n      if (\n        (error as ReadContractsErrorType).name ===\n        'ContractFunctionExecutionError'\n      ) {\n        const balance = await getTokenBalance(config, {\n          balanceAddress: address,\n          chainId,\n          symbolType: 'bytes32',\n          tokenAddress,\n        })\n        const symbol = hexToString(\n          trim(balance.symbol as Hex, { dir: 'right' }),\n        )\n        return { ...balance, symbol }\n      }\n      throw error\n    }\n  }\n\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getBalance, 'getBalance')\n  const value = await action(\n    blockNumber ? { address, blockNumber } : { address, blockTag },\n  )\n  const chain = config.chains.find((x) => x.id === chainId) ?? client.chain!\n  return {\n    decimals: chain.nativeCurrency.decimals,\n    formatted: formatUnits(value, getUnit(unit)),\n    symbol: chain.nativeCurrency.symbol,\n    value,\n  }\n}\n\ntype GetTokenBalanceParameters = {\n  balanceAddress: Address\n  chainId?: number | undefined\n  symbolType: 'bytes32' | 'string'\n  tokenAddress: Address\n  unit?: Unit | undefined\n}\n\nasync function getTokenBalance(\n  config: Config,\n  parameters: GetTokenBalanceParameters,\n) {\n  const { balanceAddress, chainId, symbolType, tokenAddress, unit } = parameters\n  const contract = {\n    abi: [\n      {\n        type: 'function',\n        name: 'balanceOf',\n        stateMutability: 'view',\n        inputs: [{ type: 'address' }],\n        outputs: [{ type: 'uint256' }],\n      },\n      {\n        type: 'function',\n        name: 'decimals',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type: 'uint8' }],\n      },\n      {\n        type: 'function',\n        name: 'symbol',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type: symbolType }],\n      },\n    ],\n    address: tokenAddress,\n  } as const\n  const [value, decimals, symbol] = await readContracts(config, {\n    allowFailure: false,\n    contracts: [\n      {\n        ...contract,\n        functionName: 'balanceOf',\n        args: [balanceAddress],\n        chainId,\n      },\n      { ...contract, functionName: 'decimals', chainId },\n      { ...contract, functionName: 'symbol', chainId },\n    ] as const,\n  })\n  const formatted = formatUnits(value ?? '0', getUnit(unit ?? decimals))\n  return { decimals, formatted, symbol, value }\n}\n", "import type { BlockTag, Chain } from 'viem'\nimport {\n  type GetBlockErrorType as viem_GetBlockErrorType,\n  type GetBlockParameters as viem_GetBlockParameters,\n  type GetBlockReturnType as viem_GetBlockReturnType,\n  getBlock as viem_getBlock,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetBlockParameters<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  viem_GetBlockParameters<includeTransactions, blockTag> &\n    ChainIdParameter<config, chainId>\n>\n\nexport type GetBlockReturnType<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = Compute<\n  {\n    [key in keyof chains]: viem_GetBlockReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined,\n      includeTransactions,\n      blockTag\n    > & { chainId: chains[key]['id'] }\n  }[number]\n>\n\nexport type GetBlockErrorType = viem_GetBlockErrorType\n\n/** https://wagmi.sh/core/actions/getBlock */\nexport async function getBlock<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n>(\n  config: config,\n  parameters: GetBlockParameters<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId\n  > = {},\n): Promise<GetBlockReturnType<includeTransactions, blockTag, config, chainId>> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getBlock, 'getBlock')\n  const block = await action(rest)\n  return {\n    ...(block as unknown as GetBlockReturnType<\n      includeTransactions,\n      blockTag,\n      config,\n      chainId\n    >),\n    chainId: client.chain.id,\n  }\n}\n", "import {\n  type GetBlockNumberErrorType as viem_GetBlockNumberErrorType,\n  type GetBlockNumberParameters as viem_GetBlockNumberParameters,\n  type GetBlockNumberReturnType as viem_GetBlockNumberReturnType,\n  getBlockNumber as viem_getBlockNumber,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetBlockNumberParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<viem_GetBlockNumberParameters & ChainIdParameter<config, chainId>>\n\nexport type GetBlockNumberReturnType = viem_GetBlockNumberReturnType\n\nexport type GetBlockNumberErrorType = viem_GetBlockNumberErrorType\n\n/** https://wagmi.sh/core/api/actions/getBlockNumber */\nexport function getBlockNumber<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetBlockNumberParameters<config, chainId> = {},\n): Promise<GetBlockNumberReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getBlockNumber, 'getBlockNumber')\n  return action(rest)\n}\n", "import {\n  type GetBlockTransactionCountErrorType as viem_GetBlockTransactionCountErrorType,\n  type GetBlockTransactionCountParameters as viem_GetBlockTransactionCountParameters,\n  type GetBlockTransactionCountReturnType as viem_GetBlockTransactionCountReturnType,\n  getBlockTransactionCount as viem_getBlockTransactionCount,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetBlockTransactionCountParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  viem_GetBlockTransactionCountParameters & ChainIdParameter<config, chainId>\n>\n\nexport type GetBlockTransactionCountReturnType =\n  viem_GetBlockTransactionCountReturnType\n\nexport type GetBlockTransactionCountErrorType =\n  viem_GetBlockTransactionCountErrorType\n\n/** https://wagmi.sh/core/api/actions/getBlockTransactionCount */\nexport function getBlockTransactionCount<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetBlockTransactionCountParameters<config, chainId> = {},\n): Promise<GetBlockTransactionCountReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_getBlockTransactionCount,\n    'getBlockTransactionCount',\n  )\n  return action(rest)\n}\n", "import {\n  type GetBytecodeErrorType as viem_GetBytecodeErrorType,\n  type GetBytecodeParameters as viem_GetBytecodeParameters,\n  type GetBytecodeReturnType as viem_GetBytecodeReturnType,\n  getBytecode as viem_getBytecode,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetBytecodeParameters<config extends Config = Config> = Compute<\n  viem_GetBytecodeParameters & ChainIdParameter<config>\n>\n\nexport type GetBytecodeReturnType = viem_GetBytecodeReturnType\n\nexport type GetBytecodeErrorType = viem_GetBytecodeErrorType\n\n/** https://wagmi.sh/core/api/actions/getBytecode */\nexport async function getBytecode<config extends Config>(\n  config: config,\n  parameters: GetBytecodeParameters<config>,\n): Promise<GetBytecodeReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getBytecode, 'getBytecode')\n  return action(rest)\n}\n", "import {\n  type GetCallsStatusErrorType as viem_GetCallsStatusErrorType,\n  type GetCallsStatusParameters as viem_GetCallsStatusParameters,\n  type GetCallsStatusReturnType as viem_GetCallsStatusReturnType,\n  getCallsStatus as viem_getCallsStatus,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport { getConnectorClient } from './getConnectorClient.js'\n\nexport type GetCallsStatusParameters = viem_GetCallsStatusParameters &\n  ConnectorParameter\n\nexport type GetCallsStatusReturnType = viem_GetCallsStatusReturnType\n\nexport type GetCallsStatusErrorType = viem_GetCallsStatusErrorType\n\n/** https://wagmi.sh/core/api/actions/getCallsStatus */\nexport async function getCallsStatus<config extends Config>(\n  config: config,\n  parameters: GetCallsStatusParameters,\n): Promise<GetCallsStatusReturnType> {\n  const { connector, id } = parameters\n  const client = await getConnectorClient(config, { connector })\n  return viem_getCallsStatus(client, { id })\n}\n", "import type { Account } from 'viem'\nimport {\n  type GetCapabilitiesErrorType as viem_GetCapabilitiesErrorType,\n  type GetCapabilitiesParameters as viem_GetCapabilitiesParameters,\n  type GetCapabilitiesReturnType as viem_GetCapabilitiesReturnType,\n  getCapabilities as viem_getCapabilities,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport { getConnectorClient } from './getConnectorClient.js'\n\nexport type GetCapabilitiesParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = viem_GetCapabilitiesParameters<chainId> & ConnectorParameter\n\nexport type GetCapabilitiesReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = viem_GetCapabilitiesReturnType<chainId>\n\nexport type GetCapabilitiesErrorType = viem_GetCapabilitiesErrorType\n\n/** https://wagmi.sh/core/api/actions/getCapabilities */\nexport async function getCapabilities<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(\n  config: config,\n  parameters: GetCapabilitiesParameters<config, chainId> = {},\n): Promise<GetCapabilitiesReturnType<config, chainId>> {\n  const { account, chainId, connector } = parameters\n  const client = await getConnectorClient(config, { account, connector })\n  return viem_getCapabilities(client as any, {\n    account: account as Account,\n    chainId,\n  })\n}\n", "import type { Config } from '../createConfig.js'\n\nexport type GetChainIdReturnType<config extends Config = Config> =\n  config['chains'][number]['id']\n\n/** https://wagmi.sh/core/api/actions/getChainId */\nexport function getChainId<config extends Config>(\n  config: config,\n): GetChainIdReturnType<config> {\n  return config.state.chainId\n}\n", "/** Forked from https://github.com/epoberezkin/fast-deep-equal */\n\nexport function deepEqual(a: any, b: any) {\n  if (a === b) return true\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    if (a.constructor !== b.constructor) return false\n\n    let length: number\n    let i: number\n\n    if (Array.isArray(a) && Array.isArray(b)) {\n      length = a.length\n      if (length !== b.length) return false\n      for (i = length; i-- !== 0; ) if (!deepEqual(a[i], b[i])) return false\n      return true\n    }\n\n    if (a.valueOf !== Object.prototype.valueOf)\n      return a.valueOf() === b.valueOf()\n    if (a.toString !== Object.prototype.toString)\n      return a.toString() === b.toString()\n\n    const keys = Object.keys(a)\n    length = keys.length\n    if (length !== Object.keys(b).length) return false\n\n    for (i = length; i-- !== 0; ) if (!Object.hasOwn(b, keys[i]!)) return false\n\n    for (i = length; i-- !== 0; ) {\n      const key = keys[i]\n\n      if (key && !deepEqual(a[key], b[key])) return false\n    }\n\n    return true\n  }\n\n  // true if both NaN, false otherwise\n  // biome-ignore lint/suspicious/noSelfCompare: using\n  return a !== a && b !== b\n}\n", "import type { Chain } from 'viem'\nimport type { Config } from '../createConfig.js'\nimport { deepEqual } from '../utils/deepEqual.js'\n\nexport type GetChainsReturnType<config extends Config = Config> = readonly [\n  ...config['chains'],\n  ...Chain[],\n]\n\nlet previousChains: readonly Chain[] = []\n\n/** https://wagmi.sh/core/api/actions/getChains */\nexport function getChains<config extends Config>(\n  config: config,\n): GetChainsReturnType<config> {\n  const chains = config.chains\n  if (deepEqual(previousChains, chains))\n    return previousChains as GetChainsReturnType<config>\n  previousChains = chains\n  return chains as unknown as GetChainsReturnType<config>\n}\n", "import type { Client } from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\n\nexport type GetClientParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | number\n    | undefined = config['chains'][number]['id'],\n> = ChainIdParameter<config, chainId>\n\nexport type GetClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  resolvedChainId extends\n    | config['chains'][number]['id']\n    | undefined = IsNarrowable<\n    config['chains'][number]['id'],\n    number\n  > extends true\n    ? IsNarrowable<chainId, number> extends true\n      ? chainId\n      : config['chains'][number]['id']\n    : config['chains'][number]['id'] | undefined,\n> = resolvedChainId extends config['chains'][number]['id']\n  ? Compute<\n      Client<\n        config['_internal']['transports'][resolvedChainId],\n        Extract<config['chains'][number], { id: resolvedChainId }>\n      >\n    >\n  : undefined\n\nexport function getClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | number | undefined,\n>(\n  config: config,\n  parameters: GetClientParameters<config, chainId> = {},\n): GetClientReturnType<config, chainId> {\n  try {\n    return config.getClient(parameters) as GetClientReturnType<config, chainId>\n  } catch {\n    return undefined as never\n  }\n}\n", "import type { Config, Connection } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport { deepEqual } from '../utils/deepEqual.js'\n\nexport type GetConnectionsReturnType = Compute<Connection>[]\n\nlet previousConnections: Connection[] = []\n\n/** https://wagmi.sh/core/api/actions/getConnections */\nexport function getConnections(config: Config): GetConnectionsReturnType {\n  const connections = [...config.state.connections.values()]\n  if (config.state.status === 'reconnecting') return previousConnections\n  if (deepEqual(previousConnections, connections)) return previousConnections\n  previousConnections = connections\n  return connections\n}\n", "import type { Config, Connector } from '../createConfig.js'\nimport { deepEqual } from '../utils/deepEqual.js'\n\nexport type GetConnectorsReturnType<config extends Config = Config> =\n  config['connectors']\n\nlet previousConnectors: readonly Connector[] = []\n\n/** https://wagmi.sh/core/api/actions/getConnectors */\nexport function getConnectors<config extends Config>(\n  config: config,\n): GetConnectorsReturnType<config> {\n  const connectors = config.connectors\n  if (deepEqual(previousConnectors, connectors)) return previousConnectors\n  previousConnectors = connectors\n  return connectors\n}\n", "import {\n  type GetEnsAddressErrorType as viem_GetEnsAddressErrorType,\n  type GetEnsAddressParameters as viem_GetEnsAddressParameters,\n  type GetEnsAddressReturnType as viem_GetEnsAddressReturnType,\n  getEnsAddress as viem_getEnsAddress,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsAddressParameters<config extends Config = Config> = Compute<\n  viem_GetEnsAddressParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsAddressReturnType = viem_GetEnsAddressReturnType\n\nexport type GetEnsAddressErrorType = viem_GetEnsAddressErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsAddress */\nexport function getEnsAddress<config extends Config>(\n  config: config,\n  parameters: GetEnsAddressParameters<config>,\n): Promise<GetEnsAddressReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsAddress, 'getEnsAddress')\n  return action(rest)\n}\n", "import {\n  type GetEnsAvatarErrorType as viem_GetEnsAvatarErrorType,\n  type GetEnsAvatarParameters as viem_GetEnsAvatarParameters,\n  type GetEnsAvatarReturnType as viem_GetEnsAvatarReturnType,\n  getEnsAvatar as viem_getEnsAvatar,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsAvatarParameters<config extends Config = Config> = Compute<\n  viem_GetEnsAvatarParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsAvatarReturnType = viem_GetEnsAvatarReturnType\n\nexport type GetEnsAvatarErrorType = viem_GetEnsAvatarErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsAvatar */\nexport function getEnsAvatar<config extends Config>(\n  config: config,\n  parameters: GetEnsAvatarParameters<config>,\n): Promise<GetEnsAvatarReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsAvatar, 'getEnsAvatar')\n  return action(rest)\n}\n", "import {\n  type GetEnsNameErrorType as viem_GetEnsNameErrorType,\n  type GetEnsNameParameters as viem_GetEnsNameParameters,\n  type GetEnsNameReturnType as viem_GetEnsNameReturnType,\n  getEnsName as viem_getEnsName,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsNameParameters<config extends Config = Config> = Compute<\n  viem_GetEnsNameParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsNameReturnType = viem_GetEnsNameReturnType\n\nexport type GetEnsNameErrorType = viem_GetEnsNameErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsName */\nexport function getEnsName<config extends Config>(\n  config: config,\n  parameters: GetEnsNameParameters<config>,\n): Promise<GetEnsNameReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsName, 'getEnsName')\n  return action(rest)\n}\n", "import {\n  type GetEnsResolverErrorType as viem_GetEnsResolverErrorType,\n  type GetEnsResolverParameters as viem_GetEnsResolverParameters,\n  type GetEnsResolverReturnType as viem_GetEnsResolverReturnType,\n  getEnsResolver as viem_getEnsResolver,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsResolverParameters<config extends Config = Config> = Compute<\n  viem_GetEnsResolverParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsResolverReturnType = viem_GetEnsResolverReturnType\n\nexport type GetEnsResolverErrorType = viem_GetEnsResolverErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsResolver */\nexport function getEnsResolver<config extends Config>(\n  config: config,\n  parameters: GetEnsResolverParameters<config>,\n): Promise<GetEnsResolverReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsResolver, 'getEnsResolver')\n  return action(rest)\n}\n", "import {\n  type GetEnsTextErrorType as viem_GetEnsTextErrorType,\n  type GetEnsTextParameters as viem_GetEnsTextParameters,\n  type GetEnsTextReturnType as viem_GetEnsTextReturnType,\n  getEnsText as viem_getEnsText,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetEnsTextParameters<config extends Config = Config> = Compute<\n  viem_GetEnsTextParameters & ChainIdParameter<config>\n>\n\nexport type GetEnsTextReturnType = viem_GetEnsTextReturnType\n\nexport type GetEnsTextErrorType = viem_GetEnsTextErrorType\n\n/** https://wagmi.sh/core/api/actions/getEnsText */\nexport function getEnsText<config extends Config>(\n  config: config,\n  parameters: GetEnsTextParameters<config>,\n): Promise<GetEnsTextReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getEnsText, 'getEnsText')\n  return action(rest)\n}\n", "import {\n  type GetFeeHistoryErrorType as viem_GetFeeHistoryErrorType,\n  type GetFeeHistoryParameters as viem_GetFeeHistoryParameters,\n  type GetFeeHistoryReturnType as viem_GetFeeHistoryReturnType,\n  getFeeHistory as viem_getFeeHistory,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetFeeHistoryParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<viem_GetFeeHistoryParameters & ChainIdParameter<config, chainId>>\n\nexport type GetFeeHistoryReturnType = viem_GetFeeHistoryReturnType\n\nexport type GetFeeHistoryErrorType = viem_GetFeeHistoryErrorType\n\n/** https://wagmi.sh/core/api/actions/getFeeHistory */\nexport function getFeeHistory<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetFeeHistoryParameters<config, chainId>,\n): Promise<GetFeeHistoryReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getFeeHistory, 'getFeeHistory')\n  return action(rest)\n}\n", "import {\n  type GetGasPriceErrorType as viem_GetGasPriceErrorType,\n  type GetGasPriceReturnType as viem_GetGasPriceReturnType,\n  getGasPrice as viem_getGasPrice,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetGasPriceParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<ChainIdParameter<config, chainId>>\n\nexport type GetGasPriceReturnType = viem_GetGasPriceReturnType\n\nexport type GetGasPriceErrorType = viem_GetGasPriceErrorType\n\n/** https://wagmi.sh/core/api/actions/getGasPrice */\nexport function getGasPrice<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetGasPriceParameters<config, chainId> = {},\n): Promise<GetGasPriceReturnType> {\n  const { chainId } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getGasPrice, 'getGasPrice')\n  return action({})\n}\n", "import {\n  type GetProofErrorType as viem_GetProofErrorType,\n  type GetProofParameters as viem_GetProofParameters,\n  type GetProofReturnType as viem_GetProofReturnType,\n  getProof as viem_getProof,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetProofParameters<config extends Config = Config> = Compute<\n  viem_GetProofParameters & ChainIdParameter<config>\n>\n\nexport type GetProofReturnType = viem_GetProofReturnType\n\nexport type GetProofErrorType = viem_GetProofErrorType\n\n/** https://wagmi.sh/core/api/actions/getProof */\nexport async function getProof<config extends Config>(\n  config: config,\n  parameters: GetProofParameters<config>,\n): Promise<GetProofReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getProof, 'getProof')\n  return action(rest)\n}\n", "import { type Client, type PublicClient, publicActions } from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getClient } from './getClient.js'\n\nexport type GetPublicClientParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n> = ChainIdParameter<config, chainId>\n\nexport type GetPublicClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  resolvedChainId extends\n    | config['chains'][number]['id']\n    | undefined = IsNarrowable<\n    config['chains'][number]['id'],\n    number\n  > extends true\n    ? IsNarrowable<chainId, number> extends true\n      ? chainId\n      : config['chains'][number]['id']\n    : config['chains'][number]['id'] | undefined,\n> = resolvedChainId extends config['chains'][number]['id']\n  ? Compute<\n      PublicClient<\n        config['_internal']['transports'][resolvedChainId],\n        Extract<config['chains'][number], { id: resolvedChainId }>\n      >\n    >\n  : undefined\n\nexport function getPublicClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | number | undefined,\n>(\n  config: config,\n  parameters: GetPublicClientParameters<config, chainId> = {},\n): GetPublicClientReturnType<config, chainId> {\n  const client = getClient(config, parameters)\n  return (client as Client)?.extend(publicActions) as GetPublicClientReturnType<\n    config,\n    chainId\n  >\n}\n", "import {\n  type GetStorageAtErrorType as viem_GetStorageAtErrorType,\n  type GetStorageAtParameters as viem_GetStorageAtParameters,\n  type GetStorageAtReturnType as viem_GetStorageAtReturnType,\n  getStorageAt as viem_getStorageAt,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetStorageAtParameters<config extends Config = Config> = Compute<\n  viem_GetStorageAtParameters & ChainIdParameter<config>\n>\n\nexport type GetStorageAtReturnType = viem_GetStorageAtReturnType\n\nexport type GetStorageAtErrorType = viem_GetStorageAtErrorType\n\n/** https://wagmi.sh/core/api/actions/getStorageAt */\nexport async function getStorageAt<config extends Config>(\n  config: config,\n  parameters: GetStorageAtParameters<config>,\n): Promise<GetStorageAtReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getStorageAt, 'getStorageAt')\n  return action(rest)\n}\n", "import type { Address, Hex } from 'viem'\nimport {\n  ContractFunctionExecutionError,\n  formatUnits,\n  hexToString,\n  trim,\n} from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Unit } from '../types/unit.js'\nimport type { Compute } from '../types/utils.js'\nimport { getUnit } from '../utils/getUnit.js'\nimport { type ReadContractsErrorType, readContracts } from './readContracts.js'\n\nexport type GetTokenParameters<config extends Config = Config> = Compute<\n  ChainIdParameter<config> & {\n    address: Address\n    formatUnits?: Unit | undefined\n  }\n>\n\nexport type GetTokenReturnType = {\n  address: Address\n  decimals: number\n  name: string | undefined\n  symbol: string | undefined\n  totalSupply: {\n    formatted: string\n    value: bigint\n  }\n}\n\nexport type GetTokenErrorType = ReadContractsErrorType\n\n/** @deprecated */\nexport async function getToken<config extends Config>(\n  config: config,\n  parameters: GetTokenParameters<config>,\n): Promise<GetTokenReturnType> {\n  const { address, chainId, formatUnits: unit = 18 } = parameters\n\n  function getAbi<type extends 'bytes32' | 'string'>(type: type) {\n    return [\n      {\n        type: 'function',\n        name: 'decimals',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type: 'uint8' }],\n      },\n      {\n        type: 'function',\n        name: 'name',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type }],\n      },\n      {\n        type: 'function',\n        name: 'symbol',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type }],\n      },\n      {\n        type: 'function',\n        name: 'totalSupply',\n        stateMutability: 'view',\n        inputs: [],\n        outputs: [{ type: 'uint256' }],\n      },\n    ] as const\n  }\n\n  try {\n    const abi = getAbi('string')\n    const contractConfig = { address, abi, chainId } as const\n    const [decimals, name, symbol, totalSupply] = await readContracts(config, {\n      allowFailure: true,\n      contracts: [\n        { ...contractConfig, functionName: 'decimals' },\n        { ...contractConfig, functionName: 'name' },\n        { ...contractConfig, functionName: 'symbol' },\n        { ...contractConfig, functionName: 'totalSupply' },\n      ] as const,\n    })\n\n    // throw if `name` or `symbol` failed\n    if (name.error instanceof ContractFunctionExecutionError) throw name.error\n    if (symbol.error instanceof ContractFunctionExecutionError)\n      throw symbol.error\n\n    // `decimals` and `totalSupply` are required\n    if (decimals.error) throw decimals.error\n    if (totalSupply.error) throw totalSupply.error\n\n    return {\n      address,\n      decimals: decimals.result,\n      name: name.result,\n      symbol: symbol.result,\n      totalSupply: {\n        formatted: formatUnits(totalSupply.result!, getUnit(unit)),\n        value: totalSupply.result,\n      },\n    }\n  } catch (error) {\n    // In the chance that there is an error upon decoding the contract result,\n    // it could be likely that the contract data is represented as bytes32 instead\n    // of a string.\n    if (error instanceof ContractFunctionExecutionError) {\n      const abi = getAbi('bytes32')\n      const contractConfig = { address, abi, chainId } as const\n      const [decimals, name, symbol, totalSupply] = await readContracts(\n        config,\n        {\n          allowFailure: false,\n          contracts: [\n            { ...contractConfig, functionName: 'decimals' },\n            { ...contractConfig, functionName: 'name' },\n            { ...contractConfig, functionName: 'symbol' },\n            { ...contractConfig, functionName: 'totalSupply' },\n          ] as const,\n        },\n      )\n      return {\n        address,\n        decimals,\n        name: hexToString(trim(name as Hex, { dir: 'right' })),\n        symbol: hexToString(trim(symbol as Hex, { dir: 'right' })),\n        totalSupply: {\n          formatted: formatUnits(totalSupply, getUnit(unit)),\n          value: totalSupply,\n        },\n      }\n    }\n\n    throw error\n  }\n}\n", "import type { Chain } from 'viem'\nimport {\n  type GetTransactionErrorType as viem_GetTransactionErrorType,\n  type GetTransactionParameters as viem_GetTransactionParameters,\n  type GetTransactionReturnType as viem_GetTransactionReturnType,\n  getTransaction as viem_getTransaction,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetTransactionParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<viem_GetTransactionParameters & ChainIdParameter<config, chainId>>\n\nexport type GetTransactionReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = Compute<\n  {\n    [key in keyof chains]: viem_GetTransactionReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined\n    > & { chainId: chains[key]['id'] }\n  }[number]\n>\n\nexport type GetTransactionErrorType = viem_GetTransactionErrorType\n\n/** https://wagmi.sh/core/api/actions/getTransaction */\nexport function getTransaction<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetTransactionParameters<config, chainId>,\n): Promise<GetTransactionReturnType<config, chainId>> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_getTransaction, 'getTransaction')\n  return action(rest) as unknown as Promise<\n    GetTransactionReturnType<config, chainId>\n  >\n}\n", "import type { Chain } from 'viem'\nimport {\n  type GetTransactionConfirmationsErrorType as viem_GetTransactionConfirmationsErrorType,\n  type GetTransactionConfirmationsParameters as viem_GetTransactionConfirmationsParameters,\n  type GetTransactionConfirmationsReturnType as viem_GetTransactionConfirmationsReturnType,\n  getTransactionConfirmations as viem_getTransactionConfirmations,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetTransactionConfirmationsParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: viem_GetTransactionConfirmationsParameters<\n    chains[key]\n  > &\n    ChainIdParameter<config, chainId>\n}[number]\n\nexport type GetTransactionConfirmationsReturnType =\n  viem_GetTransactionConfirmationsReturnType\n\nexport type GetTransactionConfirmationsErrorType =\n  viem_GetTransactionConfirmationsErrorType\n\n/** https://wagmi.sh/core/api/actions/getTransactionConfirmations */\nexport function getTransactionConfirmations<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetTransactionConfirmationsParameters<config, chainId>,\n): Promise<GetTransactionConfirmationsReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_getTransactionConfirmations,\n    'getTransactionConfirmations',\n  )\n  return action(rest as viem_GetTransactionConfirmationsParameters)\n}\n", "import {\n  type GetTransactionCountErrorType as viem_GetTransactionCountErrorType,\n  type GetTransactionCountParameters as viem_GetTransactionCountParameters,\n  type GetTransactionCountReturnType as viem_GetTransactionCountReturnType,\n  getTransactionCount as viem_getTransactionCount,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetTransactionCountParameters<config extends Config = Config> =\n  Compute<ChainIdParameter<config> & viem_GetTransactionCountParameters>\n\nexport type GetTransactionCountReturnType = viem_GetTransactionCountReturnType\n\nexport type GetTransactionCountErrorType = viem_GetTransactionCountErrorType\n\n/** https://wagmi.sh/core/api/actions/getTransactionCount */\nexport async function getTransactionCount<config extends Config>(\n  config: config,\n  parameters: GetTransactionCountParameters<config>,\n): Promise<GetTransactionCountReturnType> {\n  const { address, blockNumber, blockTag, chainId } = parameters\n\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_getTransactionCount,\n    'getTransactionCount',\n  )\n  return action(blockNumber ? { address, blockNumber } : { address, blockTag })\n}\n", "import type { Chain } from 'viem'\nimport {\n  type GetTransactionReceiptErrorType as viem_GetTransactionReceiptErrorType,\n  type GetTransactionReceiptParameters as viem_GetTransactionReceiptParameters,\n  type GetTransactionReceiptReturnType as viem_GetTransactionReceiptReturnType,\n  getTransactionReceipt as viem_getTransactionReceipt,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type GetTransactionReceiptParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  viem_GetTransactionReceiptParameters & ChainIdParameter<config, chainId>\n>\n\nexport type GetTransactionReceiptReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = Compute<\n  {\n    [key in keyof chains]: viem_GetTransactionReceiptReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined\n    > & { chainId: chains[key]['id'] }\n  }[number]\n>\n\nexport type GetTransactionReceiptErrorType = viem_GetTransactionReceiptErrorType\n\n/** https://wagmi.sh/core/api/actions/getTransactionReceipt */\nexport async function getTransactionReceipt<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetTransactionReceiptParameters<config>,\n): Promise<GetTransactionReceiptReturnType<config, chainId>> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_getTransactionReceipt,\n    'getTransactionReceipt',\n  )\n  return action(rest) as unknown as Promise<\n    GetTransactionReceiptReturnType<config, chainId>\n  >\n}\n", "import { type Account, type WalletClient, walletActions } from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { Compute } from '../types/utils.js'\nimport {\n  type GetConnectorClientErrorType,\n  type GetConnectorClientParameters,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type GetWalletClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = GetConnectorClientParameters<Config, chainId>\n\nexport type GetWalletClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  WalletClient<\n    config['_internal']['transports'][chainId],\n    Extract<config['chains'][number], { id: chainId }>,\n    Account\n  >\n>\n\nexport type GetWalletClientErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n\nexport async function getWalletClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: GetWalletClientParameters<config, chainId> = {},\n): Promise<GetWalletClientReturnType<config, chainId>> {\n  const client = await getConnectorClient(config, parameters)\n  // @ts-ignore\n  return client.extend(walletActions) as unknown as GetWalletClientReturnType<\n    config,\n    chainId\n  >\n}\n", "import type {\n  Account,\n  Address,\n  Chain,\n  PrepareTransactionRequestErrorType as viem_PrepareTransactionRequestErrorType,\n  PrepareTransactionRequestParameters as viem_PrepareTransactionRequestParameters,\n  PrepareTransactionRequestRequest as viem_PrepareTransactionRequestRequest,\n  PrepareTransactionRequestReturnType as viem_PrepareTransactionRequestReturnType,\n} from 'viem'\nimport { prepareTransactionRequest as viem_prepareTransactionRequest } from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type {\n  Compute,\n  IsNarrowable,\n  UnionCompute,\n  UnionStrictOmit,\n} from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport { getAccount } from './getAccount.js'\n\nexport type PrepareTransactionRequestParameters<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    UnionStrictOmit<\n      viem_PrepareTransactionRequestParameters<\n        chains[key],\n        Account,\n        chains[key],\n        Account | Address,\n        request extends viem_PrepareTransactionRequestRequest<\n          chains[key],\n          chains[key]\n        >\n          ? request\n          : never\n      >,\n      'chain'\n    > &\n      ChainIdParameter<config, chainId> & {\n        to: Address\n      }\n  >\n}[number]\n\nexport type PrepareTransactionRequestReturnType<\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: Compute<\n    viem_PrepareTransactionRequestReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined,\n      Account,\n      chains[key],\n      Account,\n      request extends viem_PrepareTransactionRequestRequest<\n        IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined,\n        chains[key]\n      >\n        ? request\n        : never\n    >\n  > & {\n    chainId: chains[key]['id']\n  }\n}[number]\n\nexport type PrepareTransactionRequestErrorType =\n  viem_PrepareTransactionRequestErrorType\n\n/** https://wagmi.sh/core/api/actions/prepareTransactionRequest */\nexport async function prepareTransactionRequest<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  const request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>['0'],\n    SelectChains<config, chainId>['0']\n  >,\n>(\n  config: config,\n  parameters: PrepareTransactionRequestParameters<config, chainId, request>,\n): Promise<PrepareTransactionRequestReturnType<config, chainId, request>> {\n  const { account: account_, chainId, ...rest } = parameters\n\n  const account = account_ ?? getAccount(config).address\n  const client = config.getClient({ chainId })\n\n  const action = getAction(\n    client,\n    viem_prepareTransactionRequest,\n    'prepareTransactionRequest',\n  )\n  return action({\n    ...rest,\n    ...(account ? { account } : {}),\n  } as unknown as viem_PrepareTransactionRequestParameters) as unknown as Promise<\n    PrepareTransactionRequestReturnType<config, chainId, request>\n  >\n}\n", "import type { Address } from 'viem'\n\nimport type { CreateConnectorFn } from '../connectors/createConnector.js'\nimport type { Config, Connection, Connector } from '../createConfig.js'\nimport type { ErrorType } from '../errors/base.js'\nimport type { Compute } from '../types/utils.js'\n\nexport type ReconnectParameters = {\n  /** Connectors to attempt reconnect with */\n  connectors?: readonly (CreateConnectorFn | Connector)[] | undefined\n}\n\nexport type ReconnectReturnType = Compute<Connection>[]\n\nexport type ReconnectErrorType = ErrorType\n\nlet isReconnecting = false\n\n/** https://wagmi.sh/core/api/actions/reconnect */\nexport async function reconnect(\n  config: Config,\n  parameters: ReconnectParameters = {},\n): Promise<ReconnectReturnType> {\n  // If already reconnecting, do nothing\n  if (isReconnecting) return []\n  isReconnecting = true\n\n  config.setState((x) => ({\n    ...x,\n    status: x.current ? 'reconnecting' : 'connecting',\n  }))\n\n  const connectors: Connector[] = []\n  if (parameters.connectors?.length) {\n    for (const connector_ of parameters.connectors) {\n      let connector: Connector\n      // \"Register\" connector if not already created\n      if (typeof connector_ === 'function')\n        connector = config._internal.connectors.setup(connector_)\n      else connector = connector_\n      connectors.push(connector)\n    }\n  } else connectors.push(...config.connectors)\n\n  // Try recently-used connectors first\n  let recentConnectorId: string | null | undefined\n  try {\n    recentConnectorId = await config.storage?.getItem('recentConnectorId')\n  } catch {}\n  const scores: Record<string, number> = {}\n  for (const [, connection] of config.state.connections) {\n    scores[connection.connector.id] = 1\n  }\n  if (recentConnectorId) scores[recentConnectorId] = 0\n  const sorted =\n    Object.keys(scores).length > 0\n      ? // .toSorted()\n        [...connectors].sort(\n          (a, b) => (scores[a.id] ?? 10) - (scores[b.id] ?? 10),\n        )\n      : connectors\n\n  // Iterate through each connector and try to connect\n  let connected = false\n  const connections: Connection[] = []\n  const providers: unknown[] = []\n  for (const connector of sorted) {\n    const provider = await connector.getProvider().catch(() => undefined)\n    if (!provider) continue\n\n    // If we already have an instance of this connector's provider,\n    // then we have already checked it (ie. injected connectors can\n    // share the same `window.ethereum` instance, so we don't want to\n    // connect to it again).\n    if (providers.some((x) => x === provider)) continue\n\n    const isAuthorized = await connector.isAuthorized()\n    if (!isAuthorized) continue\n\n    const data = await connector\n      .connect({ isReconnecting: true })\n      .catch(() => null)\n    if (!data) continue\n\n    connector.emitter.off('connect', config._internal.events.connect)\n    connector.emitter.on('change', config._internal.events.change)\n    connector.emitter.on('disconnect', config._internal.events.disconnect)\n\n    config.setState((x) => {\n      const connections = new Map(connected ? x.connections : new Map()).set(\n        connector.uid,\n        { accounts: data.accounts, chainId: data.chainId, connector },\n      )\n      return {\n        ...x,\n        current: connected ? x.current : connector.uid,\n        connections,\n      }\n    })\n    connections.push({\n      accounts: data.accounts as readonly [Address, ...Address[]],\n      chainId: data.chainId,\n      connector,\n    })\n    providers.push(provider)\n    connected = true\n  }\n\n  // Prevent overwriting connected status from race condition\n  if (\n    config.state.status === 'reconnecting' ||\n    config.state.status === 'connecting'\n  ) {\n    // If connecting didn't succeed, set to disconnected\n    if (!connected)\n      config.setState((x) => ({\n        ...x,\n        connections: new Map(),\n        current: null,\n        status: 'disconnected',\n      }))\n    else config.setState((x) => ({ ...x, status: 'connected' }))\n  }\n\n  isReconnecting = false\n  return connections\n}\n", "import type { Account, Chain } from 'viem'\nimport {\n  type SendCallsErrorType as viem_SendCallsErrorType,\n  type SendCallsParameters as viem_SendCallsParameters,\n  type SendCallsReturnType as viem_SendCallsReturnType,\n  sendCalls as viem_sendCalls,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SendCallsParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  calls extends readonly unknown[] = readonly unknown[],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: Compute<\n    Omit<\n      viem_SendCallsParameters<chains[key], Account, chains[key], calls>,\n      'chain'\n    > &\n      ChainIdParameter<config, chainId> &\n      ConnectorParameter\n  >\n}[number]\n\nexport type SendCallsReturnType = viem_SendCallsReturnType\n\nexport type SendCallsErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SendCallsErrorType\n\n/** https://wagmi.sh/core/api/actions/sendCalls */\nexport async function sendCalls<\n  const calls extends readonly unknown[],\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: SendCallsParameters<config, chainId, calls>,\n): Promise<SendCallsReturnType> {\n  const { account, chainId, connector, calls, ...rest } = parameters\n\n  const client = await getConnectorClient(config, {\n    account,\n    chainId,\n    connector,\n  })\n\n  return viem_sendCalls(client, {\n    ...(rest as any),\n    ...(typeof account !== 'undefined' ? { account } : {}),\n    calls,\n    chain: chainId ? { id: chainId } : undefined,\n  })\n}\n", "import type {\n  Account,\n  Chain,\n  Client,\n  TransactionRequest,\n  SendTransactionErrorType as viem_SendTransactionErrorType,\n  SendTransactionParameters as viem_SendTransactionParameters,\n  SendTransactionReturnType as viem_SendTransactionReturnType,\n} from 'viem'\nimport { sendTransaction as viem_sendTransaction } from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SendTransactionParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: Compute<\n    Omit<\n      viem_SendTransactionParameters<chains[key], Account, chains[key]>,\n      'chain' | 'gas'\n    > &\n      ChainIdParameter<config, chainId> &\n      ConnectorParameter\n  >\n}[number] & {\n  /** Gas provided for transaction execution. */\n  gas?: TransactionRequest['gas'] | null\n}\n\nexport type SendTransactionReturnType = viem_SendTransactionReturnType\n\nexport type SendTransactionErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SendTransactionErrorType\n\n/** https://wagmi.sh/core/api/actions/sendTransaction */\nexport async function sendTransaction<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: SendTransactionParameters<config, chainId>,\n): Promise<SendTransactionReturnType> {\n  const { account, chainId, connector, ...rest } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account?.type === 'local')\n    client = config.getClient({ chainId })\n  else\n    client = await getConnectorClient(config, {\n      account: account ?? undefined,\n      chainId,\n      connector,\n    })\n\n  const action = getAction(client, viem_sendTransaction, 'sendTransaction')\n  const hash = await action({\n    ...(rest as any),\n    ...(account ? { account } : {}),\n    chain: chainId ? { id: chainId } : null,\n    gas: rest.gas ?? undefined,\n  })\n\n  return hash\n}\n", "import {\n  type ShowCallsStatusErrorType as viem_ShowCallsStatusErrorType,\n  type ShowCallsStatusParameters as viem_ShowCallsStatusParameters,\n  type ShowCallsStatusReturnType as viem_ShowCallsStatusReturnType,\n  showCallsStatus as viem_showCallsStatus,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport { getConnectorClient } from './getConnectorClient.js'\n\nexport type ShowCallsStatusParameters = viem_ShowCallsStatusParameters &\n  ConnectorParameter\n\nexport type ShowCallsStatusReturnType = viem_ShowCallsStatusReturnType\n\nexport type ShowCallsStatusErrorType = viem_ShowCallsStatusErrorType\n\n/** https://wagmi.sh/core/api/actions/showCallsStatus */\nexport async function showCallsStatus<config extends Config>(\n  config: config,\n  parameters: ShowCallsStatusParameters,\n): Promise<ShowCallsStatusReturnType> {\n  const { connector, id } = parameters\n  const client = await getConnectorClient(config, { connector })\n  return viem_showCallsStatus(client, { id })\n}\n", "import type { Account, Client } from 'viem'\nimport {\n  type SignMessageErrorType as viem_SignMessageErrorType,\n  type SignMessageParameters as viem_SignMessageParameters,\n  type SignMessageReturnType as viem_SignMessageReturnType,\n  signMessage as viem_signMessage,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SignMessageParameters = Compute<\n  viem_SignMessageParameters<Account> & ConnectorParameter\n>\n\nexport type SignMessageReturnType = viem_SignMessageReturnType\n\nexport type SignMessageErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SignMessageErrorType\n\n/** https://wagmi.sh/core/api/actions/signMessage */\nexport async function signMessage(\n  config: Config,\n  parameters: SignMessageParameters,\n): Promise<SignMessageReturnType> {\n  const { account, connector, ...rest } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account.type === 'local')\n    client = config.getClient()\n  else client = await getConnectorClient(config, { account, connector })\n\n  const action = getAction(client, viem_signMessage, 'signMessage')\n  return action({\n    ...rest,\n    ...(account ? { account } : {}),\n  } as viem_SignMessageParameters<Account>)\n}\n", "import type { Account, Client, TypedData } from 'viem'\nimport {\n  type SignMessageErrorType as viem_SignMessageErrorType,\n  type SignTypedDataParameters as viem_SignTypedDataParameters,\n  type SignTypedDataReturnType as viem_SignTypedDataReturnType,\n  signTypedData as viem_signTypedData,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SignTypedDataParameters<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  ///\n  primaryTypes = typedData extends TypedData ? keyof typedData : string,\n> = UnionCompute<\n  viem_SignTypedDataParameters<typedData, primaryType, Account, primaryTypes> &\n    ConnectorParameter\n>\n\nexport type SignTypedDataReturnType = viem_SignTypedDataReturnType\n\nexport type SignTypedDataErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SignMessageErrorType\n\n/** https://wagmi.sh/core/api/actions/signTypedData */\nexport async function signTypedData<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  config: Config,\n  parameters: SignTypedDataParameters<typedData, primaryType>,\n): Promise<SignTypedDataReturnType> {\n  const { account, connector, ...rest } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account.type === 'local')\n    client = config.getClient()\n  else client = await getConnectorClient(config, { account, connector })\n\n  const action = getAction(client, viem_signTypedData, 'signTypedData')\n  return action({\n    ...rest,\n    ...(account ? { account } : {}),\n  } as unknown as viem_SignTypedDataParameters)\n}\n", "import type {\n  Abi,\n  Account,\n  Address,\n  Chain,\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from 'viem'\nimport {\n  type SimulateContractErrorType as viem_SimulateContractErrorType,\n  type SimulateContractParameters as viem_SimulateContractParameters,\n  type SimulateContractReturnType as viem_SimulateContractReturnType,\n  simulateContract as viem_simulateContract,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type {\n  Compute,\n  PartialBy,\n  UnionCompute,\n  UnionStrictOmit,\n} from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type SimulateContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    UnionStrictOmit<\n      viem_SimulateContractParameters<\n        abi,\n        functionName,\n        args,\n        chains[key],\n        chains[key],\n        Account | Address\n      >,\n      'chain'\n    >\n  > &\n    ChainIdParameter<config, chainId> &\n    ConnectorParameter\n}[number]\n\nexport type SimulateContractReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: viem_SimulateContractReturnType<\n    abi,\n    functionName,\n    args,\n    chains[key],\n    Account,\n    chains[key]\n  > & {\n    chainId: chains[key]['id']\n    request: Compute<\n      PartialBy<\n        { chainId: chainId; chain: chains[key] },\n        chainId extends config['chains'][number]['id'] ? never : 'chainId'\n      >\n    >\n  }\n}[number]\n\nexport type SimulateContractErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SimulateContractErrorType\n\n/** https://wagmi.sh/core/api/actions/simulateContract */\nexport async function simulateContract<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(\n  config: config,\n  parameters: SimulateContractParameters<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId\n  >,\n): Promise<\n  SimulateContractReturnType<abi, functionName, args, config, chainId>\n> {\n  const { abi, chainId, connector, ...rest } =\n    parameters as SimulateContractParameters\n\n  let account: Address | Account\n  if (parameters.account) account = parameters.account\n  else {\n    const connectorClient = await getConnectorClient(config, {\n      chainId,\n      connector,\n    })\n    account = connectorClient.account\n  }\n\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_simulateContract, 'simulateContract')\n  const { result, request } = await action({ ...rest, abi, account })\n\n  return {\n    chainId: client.chain.id,\n    result,\n    request: { ...request, chainId },\n  } as unknown as SimulateContractReturnType<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId\n  >\n}\n", "import type { Address } from 'viem'\n\nimport type { Config, Connector } from '../createConfig.js'\nimport type { BaseError, ErrorType } from '../errors/base.js'\nimport {\n  ConnectorNotConnectedError,\n  type ConnectorNotConnectedErrorType,\n} from '../errors/config.js'\n\nexport type SwitchAccountParameters = {\n  connector: Connector\n}\n\nexport type SwitchAccountReturnType<config extends Config = Config> = {\n  accounts: readonly [Address, ...Address[]]\n  chainId:\n    | config['chains'][number]['id']\n    | (number extends config['chains'][number]['id'] ? number : number & {})\n}\n\nexport type SwitchAccountErrorType =\n  | ConnectorNotConnectedErrorType\n  | BaseError\n  | ErrorType\n\n/** https://wagmi.sh/core/api/actions/switchAccount */\nexport async function switchAccount<config extends Config>(\n  config: config,\n  parameters: SwitchAccountParameters,\n): Promise<SwitchAccountReturnType<config>> {\n  const { connector } = parameters\n\n  const connection = config.state.connections.get(connector.uid)\n  if (!connection) throw new ConnectorNotConnectedError()\n\n  await config.storage?.setItem('recentConnectorId', connector.id)\n  config.setState((x) => ({\n    ...x,\n    current: connector.uid,\n  }))\n  return {\n    accounts: connection.accounts,\n    chainId: connection.chainId,\n  }\n}\n", "import type { Connector } from '../createConfig.js'\nimport { BaseError } from './base.js'\n\nexport type ProviderNotFoundErrorType = ProviderNotFoundError & {\n  name: 'ProviderNotFoundError'\n}\nexport class ProviderNotFoundError extends BaseError {\n  override name = 'ProviderNotFoundError'\n  constructor() {\n    super('Provider not found.')\n  }\n}\n\nexport type SwitchChainNotSupportedErrorType = SwitchChainNotSupportedError & {\n  name: 'SwitchChainNotSupportedError'\n}\nexport class SwitchChainNotSupportedError extends BaseError {\n  override name = 'SwitchChainNotSupportedError'\n\n  constructor({ connector }: { connector: Connector }) {\n    super(`\"${connector.name}\" does not support programmatic chain switching.`)\n  }\n}\n", "import type {\n  AddEthereumChainParameter,\n  UserRejectedRequestErrorType,\n  SwitchChainErrorType as viem_SwitchChainErrorType,\n} from 'viem'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport {\n  ChainNotConfiguredError,\n  type ChainNotConfiguredErrorType,\n} from '../errors/config.js'\nimport {\n  type ProviderNotFoundErrorType,\n  SwitchChainNotSupportedError,\n  type SwitchChainNotSupportedErrorType,\n} from '../errors/connector.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\n\nexport type SwitchChainParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  ConnectorParameter & {\n    chainId: chainId | config['chains'][number]['id']\n    addEthereumChainParameter?:\n      | Compute<ExactPartial<Omit<AddEthereumChainParameter, 'chainId'>>>\n      | undefined\n  }\n>\n\nexport type SwitchChainReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Extract<\n  config['chains'][number],\n  { id: Config extends config ? number : chainId }\n>\n\nexport type SwitchChainErrorType =\n  | SwitchChainNotSupportedErrorType\n  | ChainNotConfiguredErrorType\n  // connector.switchChain()\n  | ProviderNotFoundErrorType\n  | UserRejectedRequestErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_SwitchChainErrorType\n\n/** https://wagmi.sh/core/api/actions/switchChain */\nexport async function switchChain<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: SwitchChainParameters<config, chainId>,\n): Promise<SwitchChainReturnType<config, chainId>> {\n  const { addEthereumChainParameter, chainId } = parameters\n\n  const connection = config.state.connections.get(\n    parameters.connector?.uid ?? config.state.current!,\n  )\n  if (connection) {\n    const connector = connection.connector\n    if (!connector.switchChain)\n      throw new SwitchChainNotSupportedError({ connector })\n    const chain = await connector.switchChain({\n      addEthereumChainParameter,\n      chainId,\n    })\n    return chain as SwitchChainReturnType<config, chainId>\n  }\n\n  const chain = config.chains.find((x) => x.id === chainId)\n  if (!chain) throw new ChainNotConfiguredError()\n  config.setState((x) => ({ ...x, chainId }))\n  return chain as SwitchChainReturnType<config, chainId>\n}\n", "import {\n  type VerifyMessageErrorType as viem_VerifyMessageErrorType,\n  type VerifyMessageParameters as viem_VerifyMessageParameters,\n  type VerifyMessageReturnType as viem_VerifyMessageReturnType,\n  verifyMessage as viem_verifyMessage,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type VerifyMessageParameters<config extends Config = Config> = Compute<\n  viem_VerifyMessageParameters & ChainIdParameter<config>\n>\n\nexport type VerifyMessageReturnType = viem_VerifyMessageReturnType\n\nexport type VerifyMessageErrorType = viem_VerifyMessageErrorType\n\n/** https://wagmi.sh/core/api/actions/verifyMessage */\nexport async function verifyMessage<config extends Config>(\n  config: config,\n  parameters: VerifyMessageParameters<config>,\n): Promise<VerifyMessageReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_verifyMessage, 'verifyMessage')\n  return action(rest)\n}\n", "import type { TypedData } from 'viem'\nimport {\n  type VerifyTypedDataErrorType as viem_VerifyTypedDataErrorType,\n  type VerifyTypedDataParameters as viem_VerifyTypedDataParameters,\n  type VerifyTypedDataReturnType as viem_VerifyTypedDataReturnType,\n  verifyTypedData as viem_verifyTypedData,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type VerifyTypedDataParameters<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  config extends Config = Config,\n> = Compute<\n  viem_VerifyTypedDataParameters<typedData, primaryType> &\n    ChainIdParameter<config>\n>\n\nexport type VerifyTypedDataReturnType = viem_VerifyTypedDataReturnType\n\nexport type VerifyTypedDataErrorType = viem_VerifyTypedDataErrorType\n\n/** https://wagmi.sh/core/api/actions/verifyTypedData */\nexport async function verifyTypedData<\n  config extends Config,\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  config: config,\n  parameters: VerifyTypedDataParameters<typedData, primaryType, config>,\n): Promise<VerifyTypedDataReturnType> {\n  const { chainId, ...rest } = parameters\n  const client = config.getClient({ chainId })\n  const action = getAction(client, viem_verifyTypedData, 'verifyTypedData')\n  return action(rest as viem_VerifyTypedDataParameters)\n}\n", "import {\n  type WaitForCallsStatusErrorType as viem_WaitForCallsStatusErrorType,\n  type WaitForCallsStatusParameters as viem_WaitForCallsStatusParameters,\n  type WaitForCallsStatusReturnType as viem_WaitForCallsStatusReturnType,\n  waitForCallsStatus as viem_waitForCallsStatus,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport { getConnectorClient } from './getConnectorClient.js'\n\nexport type WaitForCallsStatusParameters = viem_WaitForCallsStatusParameters &\n  ConnectorParameter\n\nexport type WaitForCallsStatusReturnType = viem_WaitForCallsStatusReturnType\n\nexport type WaitForCallsStatusErrorType = viem_WaitForCallsStatusErrorType\n\n/** https://wagmi.sh/core/api/actions/waitForCallsStatus */\nexport async function waitForCallsStatus<config extends Config>(\n  config: config,\n  parameters: WaitForCallsStatusParameters,\n): Promise<WaitForCallsStatusReturnType> {\n  const { connector } = parameters\n  const client = await getConnectorClient(config, { connector })\n  return viem_waitForCallsStatus(client, parameters)\n}\n", "import type { Chain } from 'viem'\nimport { hexToString } from 'viem'\nimport {\n  call,\n  getTransaction,\n  type WaitForTransactionReceiptErrorType as viem_WaitForTransactionReceiptErrorType,\n  type WaitForTransactionReceiptParameters as viem_WaitForTransactionReceiptParameters,\n  type WaitForTransactionReceiptReturnType as viem_WaitForTransactionReceiptReturnType,\n  waitForTransactionReceipt as viem_waitForTransactionReceipt,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { Compute, IsNarrowable } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WaitForTransactionReceiptParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  viem_WaitForTransactionReceiptParameters & ChainIdParameter<config, chainId>\n>\n\nexport type WaitForTransactionReceiptReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = Compute<\n  {\n    [key in keyof chains]: viem_WaitForTransactionReceiptReturnType<\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined\n    > & { chainId: chains[key]['id'] }\n  }[number]\n>\n\nexport type WaitForTransactionReceiptErrorType =\n  viem_WaitForTransactionReceiptErrorType\n\nexport async function waitForTransactionReceipt<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WaitForTransactionReceiptParameters<config, chainId>,\n): Promise<WaitForTransactionReceiptReturnType<config, chainId>> {\n  const { chainId, timeout = 0, ...rest } = parameters\n\n  const client = config.getClient({ chainId })\n  const action = getAction(\n    client,\n    viem_waitForTransactionReceipt,\n    'waitForTransactionReceipt',\n  )\n  const receipt = await action({ ...rest, timeout })\n\n  if (receipt.status === 'reverted') {\n    const action_getTransaction = getAction(\n      client,\n      getTransaction,\n      'getTransaction',\n    )\n    const txn = await action_getTransaction({ hash: receipt.transactionHash })\n    const action_call = getAction(client, call, 'call')\n    const code = await action_call({\n      ...(txn as any),\n      data: txn.input,\n      gasPrice: txn.type !== 'eip1559' ? txn.gasPrice : undefined,\n      maxFeePerGas: txn.type === 'eip1559' ? txn.maxFeePerGas : undefined,\n      maxPriorityFeePerGas:\n        txn.type === 'eip1559' ? txn.maxPriorityFeePerGas : undefined,\n    })\n    const reason = code?.data\n      ? hexToString(`0x${code.data.substring(138)}`)\n      : 'unknown reason'\n    throw new Error(reason)\n  }\n\n  return {\n    ...receipt,\n    chainId: client.chain.id,\n  } as WaitForTransactionReceiptReturnType<config, chainId>\n}\n", "import type { Config } from '../createConfig.js'\nimport { deepEqual } from '../utils/deepEqual.js'\nimport { type GetAccountReturnType, getAccount } from './getAccount.js'\n\nexport type WatchAccountParameters<config extends Config = Config> = {\n  onChange(\n    account: GetAccountReturnType<config>,\n    prevAccount: GetAccountReturnType<config>,\n  ): void\n}\n\nexport type WatchAccountReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchAccount */\nexport function watchAccount<config extends Config>(\n  config: config,\n  parameters: WatchAccountParameters<config>,\n): WatchAccountReturnType {\n  const { onChange } = parameters\n\n  return config.subscribe(() => getAccount(config), onChange, {\n    equalityFn(a, b) {\n      const { connector: aConnector, ...aRest } = a\n      const { connector: bConnector, ...bRest } = b\n      return (\n        deepEqual(aRest, bRest) &&\n        // check connector separately\n        aConnector?.id === bConnector?.id &&\n        aConnector?.uid === bConnector?.uid\n      )\n    },\n  })\n}\n", "import {\n  type WatchAssetErrorType as viem_WatchAssetErrorType,\n  type WatchAssetParameters as viem_WatchAssetParameters,\n  type WatchAssetReturnType as viem_WatchAssetReturnType,\n  watchAsset as viem_watchAsset,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { ConnectorParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type WatchAssetParameters = Compute<\n  viem_WatchAssetParameters & ConnectorParameter\n>\n\nexport type WatchAssetReturnType = viem_WatchAssetReturnType\n\nexport type WatchAssetErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_WatchAssetErrorType\n\n/** https://wagmi.sh/core/api/actions/watchAsset */\nexport async function watchAsset(\n  config: Config,\n  parameters: WatchAssetParameters,\n): Promise<WatchAssetReturnType> {\n  const { connector, ...rest } = parameters\n\n  const client = await getConnectorClient(config, { connector })\n\n  const action = getAction(client, viem_watchAsset, 'watchAsset')\n  return action(rest as viem_WatchAssetParameters)\n}\n", "import type { Chain, Transport, WebSocketTransport } from 'viem'\nimport {\n  type WatchBlockNumberParameters as viem_WatchBlockNumberParameters,\n  type WatchBlockNumberReturnType as viem_WatchBlockNumberReturnType,\n  watchBlockNumber as viem_watchBlockNumber,\n} from 'viem/actions'\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  SyncConnectedChainParameter,\n} from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WatchBlockNumberParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    viem_WatchBlockNumberParameters<\n      config['_internal']['transports'][chains[key]['id']] extends infer transport extends\n        Transport\n        ? Transport extends transport\n          ? WebSocketTransport\n          : transport\n        : WebSocketTransport\n    > &\n      ChainIdParameter<config, chainId> &\n      SyncConnectedChainParameter\n  >\n}[number]\n\nexport type WatchBlockNumberReturnType = viem_WatchBlockNumberReturnType\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/api/actions/watchBlockNumber */\nexport function watchBlockNumber<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WatchBlockNumberParameters<config, chainId>,\n): WatchBlockNumberReturnType {\n  const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } =\n    parameters as WatchBlockNumberParameters\n\n  let unwatch: WatchBlockNumberReturnType | undefined\n  const listener = (chainId: number | undefined) => {\n    if (unwatch) unwatch()\n\n    const client = config.getClient({ chainId })\n    const action = getAction(client, viem_watchBlockNumber, 'watchBlockNumber')\n    unwatch = action(rest as viem_WatchBlockNumberParameters)\n    return unwatch\n  }\n\n  // set up listener for block number changes\n  const unlisten = listener(parameters.chainId)\n\n  // set up subscriber for connected chain changes\n  let unsubscribe: (() => void) | undefined\n  if (syncConnectedChain && !parameters.chainId)\n    unsubscribe = config.subscribe(\n      ({ chainId }) => chainId,\n      async (chainId) => listener(chainId),\n    )\n\n  return () => {\n    unlisten?.()\n    unsubscribe?.()\n  }\n}\n", "import type { BlockTag, Chain, Transport, WebSocketTransport } from 'viem'\nimport {\n  type WatchBlocksParameters as viem_WatchBlocksParameters,\n  type WatchBlocksReturnType as viem_WatchBlocksReturnType,\n  watchBlocks as viem_watchBlocks,\n} from 'viem/actions'\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  SyncConnectedChainParameter,\n} from '../types/properties.js'\nimport type { IsNarrowable, UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WatchBlocksParameters<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    viem_WatchBlocksParameters<\n      config['_internal']['transports'][chains[key]['id']] extends infer transport extends\n        Transport\n        ? Transport extends transport\n          ? WebSocketTransport\n          : transport\n        : WebSocketTransport,\n      IsNarrowable<chains[key], Chain> extends true ? chains[key] : undefined,\n      includeTransactions,\n      blockTag\n    > &\n      ChainIdParameter<config, chainId> &\n      SyncConnectedChainParameter\n  >\n}[number]\n\nexport type WatchBlocksReturnType = viem_WatchBlocksReturnType\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/actions/watchBlocks */\nexport function watchBlocks<\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n>(\n  config: config,\n  parameters: WatchBlocksParameters<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId\n  >,\n): WatchBlocksReturnType {\n  const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } =\n    parameters as WatchBlocksParameters\n\n  let unwatch: WatchBlocksReturnType | undefined\n  const listener = (chainId: number | undefined) => {\n    if (unwatch) unwatch()\n\n    const client = config.getClient({ chainId })\n    const action = getAction(client, viem_watchBlocks, 'watchBlocks')\n    unwatch = action(rest as viem_WatchBlocksParameters)\n    return unwatch\n  }\n\n  // set up listener for block number changes\n  const unlisten = listener(parameters.chainId)\n\n  // set up subscriber for connected chain changes\n  let unsubscribe: (() => void) | undefined\n  if (syncConnectedChain && !parameters.chainId)\n    unsubscribe = config.subscribe(\n      ({ chainId }) => chainId,\n      async (chainId) => listener(chainId),\n    )\n\n  return () => {\n    unlisten?.()\n    unsubscribe?.()\n  }\n}\n", "import type { Config } from '../createConfig.js'\nimport type { GetChainIdReturnType } from './getChainId.js'\n\nexport type WatchChainIdParameters<config extends Config = Config> = {\n  onChange(\n    chainId: GetChainIdReturnType<config>,\n    prevChainId: GetChainIdReturnType<config>,\n  ): void\n}\n\nexport type WatchChainIdReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchChainId */\nexport function watchChainId<config extends Config>(\n  config: config,\n  parameters: WatchChainIdParameters<config>,\n): WatchChainIdReturnType {\n  const { onChange } = parameters\n  return config.subscribe((state) => state.chainId, onChange)\n}\n", "import type { Config } from '../createConfig.js'\nimport { type GetClientReturnType, getClient } from './getClient.js'\n\nexport type WatchClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = {\n  onChange(\n    publicClient: GetClientReturnType<config, chainId>,\n    prevClient: GetClientReturnType<config, chainId>,\n  ): void\n}\n\nexport type WatchClientReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchClient */\nexport function watchClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WatchClientParameters<config, chainId>,\n): WatchClientReturnType {\n  const { onChange } = parameters\n  return config.subscribe(\n    () => getClient(config) as GetClientReturnType<config, chainId>,\n    onChange,\n    {\n      equalityFn(a, b) {\n        return a?.uid === b?.uid\n      },\n    },\n  )\n}\n", "import type { Config } from '../createConfig.js'\nimport { deepEqual } from '../utils/deepEqual.js'\nimport {\n  type GetConnectionsReturnType,\n  getConnections,\n} from './getConnections.js'\n\nexport type WatchConnectionsParameters = {\n  onChange(\n    connections: GetConnectionsReturnType,\n    prevConnections: GetConnectionsReturnType,\n  ): void\n}\n\nexport type WatchConnectionsReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchConnections */\nexport function watchConnections(\n  config: Config,\n  parameters: WatchConnectionsParameters,\n): WatchConnectionsReturnType {\n  const { onChange } = parameters\n  return config.subscribe(() => getConnections(config), onChange, {\n    equalityFn: deepEqual,\n  })\n}\n", "import type { Config } from '../createConfig.js'\nimport type { GetConnectorsReturnType } from './getConnectors.js'\n\nexport type WatchConnectorsParameters<config extends Config = Config> = {\n  onChange(\n    connections: GetConnectorsReturnType<config>,\n    prevConnectors: GetConnectorsReturnType<config>,\n  ): void\n}\n\nexport type WatchConnectorsReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchConnectors */\nexport function watchConnectors<config extends Config>(\n  config: config,\n  parameters: WatchConnectorsParameters<config>,\n): WatchConnectorsReturnType {\n  const { onChange } = parameters\n  return config._internal.connectors.subscribe((connectors, prevConnectors) => {\n    onChange(Object.values(connectors), prevConnectors)\n  })\n}\n", "import type {\n  Abi,\n  Chain,\n  ContractEventName,\n  Transport,\n  WebSocketTransport,\n} from 'viem'\nimport {\n  type WatchContractEventParameters as viem_WatchContractEventParameters,\n  type WatchContractEventReturnType as viem_WatchContractEventReturnType,\n  watchContractEvent as viem_watchContractEvent,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  SyncConnectedChainParameter,\n} from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WatchContractEventParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  eventName extends ContractEventName<abi> | undefined = ContractEventName<abi>,\n  strict extends boolean | undefined = undefined,\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    viem_WatchContractEventParameters<\n      abi,\n      eventName,\n      strict,\n      config['_internal']['transports'][chains[key]['id']] extends infer transport extends\n        Transport\n        ? Transport extends transport\n          ? WebSocketTransport\n          : transport\n        : WebSocketTransport\n    > &\n      ChainIdParameter<config, chainId> &\n      SyncConnectedChainParameter\n  >\n}[number]\n\nexport type WatchContractEventReturnType = viem_WatchContractEventReturnType\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/api/actions/watchContractEvent */\nexport function watchContractEvent<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  const abi extends Abi | readonly unknown[],\n  eventName extends ContractEventName<abi> | undefined,\n  strict extends boolean | undefined = undefined,\n>(\n  config: config,\n  parameters: WatchContractEventParameters<\n    abi,\n    eventName,\n    strict,\n    config,\n    chainId\n  >,\n) {\n  const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } =\n    parameters\n\n  let unwatch: WatchContractEventReturnType | undefined\n  const listener = (chainId: number | undefined) => {\n    if (unwatch) unwatch()\n\n    const client = config.getClient({ chainId })\n    const action = getAction(\n      client,\n      viem_watchContractEvent,\n      'watchContractEvent',\n    )\n    unwatch = action(rest as unknown as viem_WatchContractEventParameters)\n    return unwatch\n  }\n\n  // set up listener for transaction changes\n  const unlisten = listener(parameters.chainId)\n\n  // set up subscriber for connected chain changes\n  let unsubscribe: (() => void) | undefined\n  if (syncConnectedChain && !parameters.chainId)\n    unsubscribe = config.subscribe(\n      ({ chainId }) => chainId,\n      async (chainId) => listener(chainId),\n    )\n\n  return () => {\n    unlisten?.()\n    unsubscribe?.()\n  }\n}\n", "import type { Chain, Transport, WebSocketTransport } from 'viem'\nimport {\n  type WatchPendingTransactionsParameters as viem_WatchPendingTransactionsParameters,\n  type WatchPendingTransactionsReturnType as viem_WatchPendingTransactionsReturnType,\n  watchPendingTransactions as viem_watchPendingTransactions,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  SyncConnectedChainParameter,\n} from '../types/properties.js'\nimport type { UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\n\nexport type WatchPendingTransactionsParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = {\n  [key in keyof chains]: UnionCompute<\n    viem_WatchPendingTransactionsParameters<\n      config['_internal']['transports'][chains[key]['id']] extends infer transport extends\n        Transport\n        ? Transport extends transport\n          ? WebSocketTransport\n          : transport\n        : WebSocketTransport\n    > &\n      ChainIdParameter<config, chainId> &\n      SyncConnectedChainParameter\n  >\n}[number]\n\nexport type WatchPendingTransactionsReturnType =\n  viem_WatchPendingTransactionsReturnType\n\n// TODO: wrap in viem's `observe` to avoid duplicate invocations.\n/** https://wagmi.sh/core/api/actions/watchPendingTransactions */\nexport function watchPendingTransactions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WatchPendingTransactionsParameters<config, chainId>,\n) {\n  const { syncConnectedChain = config._internal.syncConnectedChain, ...rest } =\n    parameters\n\n  let unwatch: WatchPendingTransactionsReturnType | undefined\n  const listener = (chainId: number | undefined) => {\n    if (unwatch) unwatch()\n\n    const client = config.getClient({ chainId })\n    const action = getAction(\n      client,\n      viem_watchPendingTransactions,\n      'watchPendingTransactions',\n    )\n    unwatch = action(rest as viem_WatchPendingTransactionsParameters)\n    return unwatch\n  }\n\n  // set up listener for transaction changes\n  const unlisten = listener(parameters.chainId)\n\n  // set up subscriber for connected chain changes\n  let unsubscribe: (() => void) | undefined\n  if (syncConnectedChain && !parameters.chainId)\n    unsubscribe = config.subscribe(\n      ({ chainId }) => chainId,\n      async (chainId) => listener(chainId),\n    )\n\n  return () => {\n    unlisten?.()\n    unsubscribe?.()\n  }\n}\n", "import type { Config } from '../createConfig.js'\nimport {\n  type GetPublicClientReturnType,\n  getPublicClient,\n} from './getPublicClient.js'\n\nexport type WatchPublicClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = {\n  onChange(\n    publicClient: GetPublicClientReturnType<config, chainId>,\n    prevPublicClient: GetPublicClientReturnType<config, chainId>,\n  ): void\n}\n\nexport type WatchPublicClientReturnType = () => void\n\n/** https://wagmi.sh/core/api/actions/watchPublicClient */\nexport function watchPublicClient<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WatchPublicClientParameters<config, chainId>,\n): WatchPublicClientReturnType {\n  const { onChange } = parameters\n  return config.subscribe(\n    () => getPublicClient(config) as GetPublicClientReturnType<config, chainId>,\n    onChange,\n    {\n      equalityFn(a, b) {\n        return a?.uid === b?.uid\n      },\n    },\n  )\n}\n", "import type {\n  Abi,\n  Account,\n  Chain,\n  Client,\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from 'viem'\nimport {\n  type WriteContractErrorType as viem_WriteContractErrorType,\n  type WriteContractParameters as viem_WriteContractParameters,\n  type WriteContractReturnType as viem_WriteContractReturnType,\n  writeContract as viem_writeContract,\n} from 'viem/actions'\n\nimport type { Config } from '../createConfig.js'\nimport type { BaseErrorType, ErrorType } from '../errors/base.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type {\n  ChainIdParameter,\n  ConnectorParameter,\n} from '../types/properties.js'\nimport type { Compute, UnionCompute } from '../types/utils.js'\nimport { getAction } from '../utils/getAction.js'\nimport {\n  type GetConnectorClientErrorType,\n  getConnectorClient,\n} from './getConnectorClient.js'\n\nexport type WriteContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  ///\n  allFunctionNames = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  chains extends readonly Chain[] = SelectChains<config, chainId>,\n> = UnionCompute<\n  {\n    // TODO: Should use `UnionStrictOmit<..., 'chain'>` on `viem_WriteContractParameters` result instead\n    // temp workaround that doesn't affect runtime behavior for https://github.com/wevm/wagmi/issues/3981\n    [key in keyof chains]: viem_WriteContractParameters<\n      abi,\n      functionName,\n      args,\n      chains[key],\n      Account,\n      chains[key],\n      allFunctionNames\n    >\n  }[number] &\n    Compute<ChainIdParameter<config, chainId>> &\n    ConnectorParameter & {\n      /** @deprecated */\n      __mode?: 'prepared'\n    }\n>\n\nexport type WriteContractReturnType = viem_WriteContractReturnType\n\nexport type WriteContractErrorType =\n  // getConnectorClient()\n  | GetConnectorClientErrorType\n  // base\n  | BaseErrorType\n  | ErrorType\n  // viem\n  | viem_WriteContractErrorType\n\n/** https://wagmi.sh/core/api/actions/writeContract */\nexport async function writeContract<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  parameters: WriteContractParameters<abi, functionName, args, config, chainId>,\n): Promise<WriteContractReturnType> {\n  const { account, chainId, connector, ...request } = parameters\n\n  let client: Client\n  if (typeof account === 'object' && account?.type === 'local')\n    client = config.getClient({ chainId })\n  else\n    client = await getConnectorClient(config, {\n      account: account ?? undefined,\n      chainId,\n      connector,\n    })\n\n  const action = getAction(client, viem_writeContract, 'writeContract')\n  const hash = await action({\n    ...(request as any),\n    ...(account ? { account } : {}),\n    chain: chainId ? { id: chainId } : null,\n  })\n\n  return hash\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBM,SAAU,UAUd,QACA,UAIA,MAA+C;AAE/C,QAAM,kBAAkB,OAAO,SAAS,IAAI;AAC5C,MAAI,OAAO,oBAAoB;AAC7B,WAAO;AAET,QAAM,kBAAkB,OAAO,IAAI;AACnC,MAAI,OAAO,oBAAoB;AAC7B,WAAO;AAET,SAAO,CAAC,WAAW,SAAS,QAAQ,MAAM;AAC5C;;;ACzBA,eAAsBA,MACpB,QACA,YAAkC;AAElC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,MAAW,MAAM;AAClD,SAAO,OAAO,IAAI;AACpB;;;AC1BO,IAAM,UAAU;;;ACEhB,IAAM,aAAa,MAAM,eAAe,OAAO;;;;;;;;;;ACYhD,IAAO,YAAP,MAAO,mBAAkB,MAAK;EAOlC,IAAI,cAAW;AACb,WAAO;EACT;EACA,IAAI,UAAO;AACT,WAAO,WAAU;EACnB;EAEA,YAAY,cAAsB,UAA4B,CAAA,GAAE;AAC9D,UAAK;;AAdP,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAES,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAWd,UAAM,UACJ,QAAQ,iBAAiB,aACrB,QAAQ,MAAM,UACd,QAAQ,OAAO,UACb,QAAQ,MAAM,UACd,QAAQ;AAChB,UAAM,WACJ,QAAQ,iBAAiB,aACrB,QAAQ,MAAM,YAAY,QAAQ,WAClC,QAAQ;AAEd,SAAK,UAAU;MACb,gBAAgB;MAChB;MACA,GAAI,QAAQ,eAAe,CAAC,GAAG,QAAQ,cAAc,EAAE,IAAI,CAAA;MAC3D,GAAI,WACA;QACE,SAAS,KAAK,WAAW,GAAG,QAAQ,QAClC,QAAQ,WAAW,IAAI,QAAQ,QAAQ,KAAK,EAC9C;UAEF,CAAA;MACJ,GAAI,UAAU,CAAC,YAAY,OAAO,EAAE,IAAI,CAAA;MACxC,YAAY,KAAK,OAAO;MACxB,KAAK,IAAI;AAEX,QAAI,QAAQ;AAAO,WAAK,QAAQ,QAAQ;AACxC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,eAAe,QAAQ;AAC5B,SAAK,eAAe;EACtB;EAEA,KAAK,IAA8B;AACjC,WAAO,uBAAA,MAAI,sBAAA,KAAA,eAAA,EAAM,KAAV,MAAW,MAAM,EAAE;EAC5B;;kGAEM,KAAc,IAA8B;AAChD,MAAI,KAAK,GAAG;AAAG,WAAO;AACtB,MAAK,IAAc;AAAO,WAAO,uBAAA,MAAI,sBAAA,KAAAC,gBAAA,EAAM,KAAV,MAAY,IAAc,OAAO,EAAE;AACpE,SAAO;AACT;;;AChEI,IAAO,0BAAP,cAAuC,UAAS;EAEpD,cAAA;AACE,UAAM,uBAAuB;AAFtB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAOI,IAAO,iCAAP,cAA8C,UAAS;EAE3D,cAAA;AACE,UAAM,8BAA8B;AAF7B,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAMI,IAAO,6BAAP,cAA0C,UAAS;EAEvD,cAAA;AACE,UAAM,0BAA0B;AAFzB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAMI,IAAO,yBAAP,cAAsC,UAAS;EAEnD,cAAA;AACE,UAAM,sBAAsB;AAFrB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAOI,IAAO,gCAAP,cAA6C,UAAS;EAE1D,YAAY,EACV,SACA,UAAS,GAIV;AACC,UAAM,YAAY,OAAO,8BAA8B,UAAU,IAAI,IAAI;AARlE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EAExD,YAAY,EACV,mBACA,iBAAgB,GAIjB;AACC,UACE,2CAA2C,gBAAgB,gDAAgD,iBAAiB,MAC5H;MACE,cAAc;QACZ,sBAAsB,gBAAgB;QACtC,sBAAsB,iBAAiB;;KAE1C;AAfI,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAiBhB;;AAOI,IAAO,wCAAP,cAAqD,UAAS;EAElE,YAAY,EAAE,UAAS,GAAmC;AACxD,UAAM,cAAc,UAAU,IAAI,qCAAqC;MACrE,SAAS;QACP;QACA;QACA;QACA,KAAK,GAAG;KACX;AARM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;;;AC1CF,eAAsB,QAIpB,QACA,YAAgD;AAGhD,MAAI;AACJ,MAAI,OAAO,WAAW,cAAc,YAAY;AAC9C,gBAAY,OAAO,UAAU,WAAW,MAAM,WAAW,SAAS;EACpE;AAAO,gBAAY,WAAW;AAG9B,MAAI,UAAU,QAAQ,OAAO,MAAM;AACjC,UAAM,IAAI,+BAA8B;AAE1C,MAAI;AACF,WAAO,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,QAAQ,aAAY,EAAG;AACvD,cAAU,QAAQ,KAAK,WAAW,EAAE,MAAM,aAAY,CAAE;AAExD,UAAM,EAAE,WAAW,GAAG,GAAG,KAAI,IAAK;AAClC,UAAM,OAAO,MAAM,UAAU,QAAQ,IAAI;AACzC,UAAM,WAAW,KAAK;AAEtB,cAAU,QAAQ,IAAI,WAAW,OAAO,UAAU,OAAO,OAAO;AAChE,cAAU,QAAQ,GAAG,UAAU,OAAO,UAAU,OAAO,MAAM;AAC7D,cAAU,QAAQ,GAAG,cAAc,OAAO,UAAU,OAAO,UAAU;AAErE,UAAM,OAAO,SAAS,QAAQ,qBAAqB,UAAU,EAAE;AAC/D,WAAO,SAAS,CAAC,OAAO;MACtB,GAAG;MACH,aAAa,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,UAAU,KAAK;QACrD;QACA,SAAS,KAAK;QACd;OACD;MACD,SAAS,UAAU;MACnB,QAAQ;MACR;AAEF,WAAO,EAAE,UAAU,SAAS,KAAK,QAAO;EAC1C,SAAS,OAAO;AACd,WAAO,SAAS,CAAC,OAAO;MACtB,GAAG;;MAEH,QAAQ,EAAE,UAAU,cAAc;MAClC;AACF,UAAM;EACR;AACF;;;ACvCA,eAAsB,mBAIpB,QACA,aAA4D,CAAA,GAAE;AAG9D,MAAI;AACJ,MAAI,WAAW,WAAW;AACxB,UAAM,EAAE,WAAAC,WAAS,IAAK;AACtB,QACE,OAAO,MAAM,WAAW,kBACxB,CAACA,WAAU,eACX,CAACA,WAAU;AAEX,YAAM,IAAI,sCAAsC,EAAE,WAAAA,WAAS,CAAE;AAE/D,UAAM,CAAC,UAAUC,QAAO,IAAI,MAAM,QAAQ,IAAI;MAC5CD,WAAU,YAAW,EAAG,MAAM,CAAC,MAAK;AAClC,YAAI,WAAW,YAAY;AAAM,iBAAO,CAAA;AACxC,cAAM;MACR,CAAC;MACDA,WAAU,WAAU;KACrB;AACD,iBAAa;MACX;MACA,SAAAC;MACA,WAAAD;;EAEJ;AAAO,iBAAa,OAAO,MAAM,YAAY,IAAI,OAAO,MAAM,OAAQ;AACtE,MAAI,CAAC;AAAY,UAAM,IAAI,2BAA0B;AAErD,QAAM,UAAU,WAAW,WAAW,WAAW;AAGjD,QAAM,mBAAmB,MAAM,WAAW,UAAU,WAAU;AAC9D,MAAI,qBAAqB,WAAW;AAClC,UAAM,IAAI,4BAA4B;MACpC,mBAAmB,WAAW;MAC9B;KACD;AAIH,QAAM,YAAY,WAAW;AAC7B,MAAI,UAAU;AACZ,WAAO,UAAU,UAAU,EAAE,QAAO,CAAE;AAGxC,QAAM,UAAU,aAAa,WAAW,WAAW,WAAW,SAAS,CAAC,CAAE;AAC1E,MAAI;AAAS,YAAQ,UAAU,WAAW,QAAQ,OAAO;AAGzD,MACE,WAAW,WACX,CAAC,WAAW,SAAS,KACnB,CAAC,MAAM,EAAE,YAAW,MAAO,QAAQ,QAAQ,YAAW,CAAE;AAG1D,UAAM,IAAI,8BAA8B;MACtC,SAAS,QAAQ;MACjB;KACD;AAEH,QAAM,QAAQ,OAAO,OAAO,KAAK,CAACE,WAAUA,OAAM,OAAO,OAAO;AAChE,QAAM,WAAY,MAAM,WAAW,UAAU,YAAY,EAAE,QAAO,CAAE;AAIpE,SAAO,aAAa;IAClB;IACA;IACA,MAAM;IACN,WAAW,CAAC,SAAS,OAAO,QAAQ,EAAE,EAAE,GAAG,MAAM,YAAY,EAAC,CAAE;GACjE;AACH;;;ACxFA,eAAsBC,gBAKpB,QACA,YAA0D;AAE1D,QAAM,EAAE,SAAS,SAAS,WAAW,GAAG,KAAI,IAAK;AAEjD,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY,SAAS,SAAS;AACnD,aAAS,OAAO,UAAU,EAAE,QAAO,CAAE;;AAErC,aAAS,MAAM,mBAAmB,QAAQ;MACxC,SAAS,WAAW;MACpB;MACA;KACD;AAEH,QAAM,SAAS,UAAU,QAAQ,gBAAqB,gBAAgB;AACtE,QAAM,OAAO,MAAM,OAAO;IACxB,GAAI;IACJ,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;IAC5B,OAAO,UAAU,EAAE,IAAI,QAAO,IAAK;GACpC;AAED,SAAO;AACT;;;AClEA,eAAsB,WACpB,QACA,aAAmC,CAAA,GAAE;AAErC,MAAI;AACJ,MAAI,WAAW;AAAW,gBAAY,WAAW;OAC5C;AACH,UAAM,EAAE,aAAAC,cAAa,QAAO,IAAK,OAAO;AACxC,UAAM,aAAaA,aAAY,IAAI,OAAQ;AAC3C,gBAAY,YAAY;EAC1B;AAEA,QAAM,cAAc,OAAO,MAAM;AAEjC,MAAI,WAAW;AACb,UAAM,UAAU,WAAU;AAC1B,cAAU,QAAQ,IAAI,UAAU,OAAO,UAAU,OAAO,MAAM;AAC9D,cAAU,QAAQ,IAAI,cAAc,OAAO,UAAU,OAAO,UAAU;AACtE,cAAU,QAAQ,GAAG,WAAW,OAAO,UAAU,OAAO,OAAO;AAE/D,gBAAY,OAAO,UAAU,GAAG;EAClC;AAEA,SAAO,SAAS,CAAC,MAAK;AAEpB,QAAI,YAAY,SAAS;AACvB,aAAO;QACL,GAAG;QACH,aAAa,oBAAI,IAAG;QACpB,SAAS;QACT,QAAQ;;AAIZ,UAAM,iBAAiB,YAAY,OAAM,EAAG,KAAI,EAAG;AACnD,WAAO;MACL,GAAG;MACH,aAAa,IAAI,IAAI,WAAW;MAChC,SAAS,eAAe,UAAU;;EAEtC,CAAC;AAGD;AACE,UAAM,UAAU,OAAO,MAAM;AAC7B,QAAI,CAAC;AAAS;AACd,UAAMC,aAAY,OAAO,MAAM,YAAY,IAAI,OAAO,GAAG;AACzD,QAAI,CAACA;AAAW;AAChB,UAAM,OAAO,SAAS,QAAQ,qBAAqBA,WAAU,EAAE;EACjE;AACF;;;AClEM,SAAU,QAAQ,MAAU;AAChC,MAAI,OAAO,SAAS;AAAU,WAAO;AACrC,MAAI,SAAS;AAAO,WAAO;AAC3B,SAAO,KAAK,IAAI,SAAS,IAAI,CAAC;AAChC;;;ACyCA,eAAsBC,oBAIpB,QACA,aAAyD,CAAA,GAAE;AAE3D,QAAM,EAAE,SAAS,aAAa,QAAQ,QAAQ,GAAG,KAAI,IAAK;AAE1D,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,oBACA,oBAAoB;AAGtB,QAAM,EAAE,UAAU,cAAc,qBAAoB,IAAK,MAAM,OAAO;IACpE,GAAG;IACH,OAAO,OAAO;GACf;AAED,QAAM,OAAO,QAAQ,KAAK;AAC1B,QAAM,YAAY;IAChB,UAAU,WAAW,YAAY,UAAU,IAAI,IAAI;IACnD,cAAc,eAAe,YAAY,cAAc,IAAI,IAAI;IAC/D,sBAAsB,uBAClB,YAAY,sBAAsB,IAAI,IACtC;;AAGN,SAAO;IACL;IACA;IACA;IACA;;AAEJ;;;ACpCA,eAAsBC,aAIpB,QACA,YAAkD;AAElD,QAAM,EAAE,SAAS,WAAW,GAAG,KAAI,IAAK;AAExC,MAAI;AACJ,MAAI,WAAW;AAAS,cAAU,WAAW;OACxC;AACH,UAAM,kBAAkB,MAAM,mBAAmB,QAAQ;MACvD,SAAS,WAAW;MACpB;MACA;KACD;AACD,cAAU,gBAAgB;EAC5B;AAEA,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,aAAkB,aAAa;AAChE,SAAO,OAAO,EAAE,GAAI,MAAqC,QAAO,CAAE;AACpE;;;ACxCA,eAAsBC,8BAKpB,QACA,aAAsE,CAAA,GAAE;AAExE,QAAM,EAAE,QAAO,IAAK;AACpB,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,8BACA,8BAA8B;AAEhC,SAAO,OAAO,EAAE,OAAO,OAAO,MAAK,CAAE;AACvC;;;ACWM,SAAU,WACd,QAAc;AAEd,QAAM,MAAM,OAAO,MAAM;AACzB,QAAM,aAAa,OAAO,MAAM,YAAY,IAAI,GAAG;AACnD,QAAM,YAAY,YAAY;AAC9B,QAAM,UAAU,YAAY,CAAC;AAC7B,QAAM,QAAQ,OAAO,OAAO,KAC1B,CAACC,WAAUA,OAAM,OAAO,YAAY,OAAO;AAE7C,QAAM,SAAS,OAAO,MAAM;AAE5B,UAAQ,QAAQ;IACd,KAAK;AACH,aAAO;QACL;QACA;QACA;QACA,SAAS,YAAY;QACrB,WAAW,YAAY;QACvB,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;;IAEJ,KAAK;AACH,aAAO;QACL;QACA;QACA;QACA,SAAS,YAAY;QACrB,WAAW,YAAY;QACvB,aAAa,CAAC,CAAC;QACf,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;;IAEJ,KAAK;AACH,aAAO;QACL;QACA;QACA;QACA,SAAS,YAAY;QACrB,WAAW,YAAY;QACvB,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;;IAEJ,KAAK;AACH,aAAO;QACL,SAAS;QACT,WAAW;QACX,OAAO;QACP,SAAS;QACT,WAAW;QACX,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB;;EAEN;AACF;;;ACpGA,eAAsBC,WAKpB,QACA,YAAgE;AAEhE,QAAM,EAAE,eAAe,MAAM,SAAS,WAAW,GAAG,KAAI,IAAK;AAC7D,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,WAAgB,WAAW;AAC5D,SAAO,OAAO;IACZ;IACA;IACA,GAAG;GACJ;AACH;;;ACEM,SAAUC,cAMd,QACA,YAAmE;AAEnE,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,cAAmB,cAAc;AAClE,SAAO,OAAO,IAAW;AAC3B;;;AC3BA,eAAsB,cAKpB,QACA,YAAoE;AAEpE,QAAM,EAAE,eAAe,MAAM,aAAa,UAAU,GAAG,KAAI,IAAK;AAChE,QAAM,YAAY,WAAW;AAI7B,MAAI;AACF,UAAM,qBAKF,CAAA;AACJ,eAAW,CAAC,OAAO,QAAQ,KAAK,UAAU,QAAO,GAAI;AACnD,YAAM,UAAU,SAAS,WAAW,OAAO,MAAM;AACjD,UAAI,CAAC,mBAAmB,OAAO;AAAG,2BAAmB,OAAO,IAAI,CAAA;AAChE,yBAAmB,OAAO,GAAG,KAAK,EAAE,UAAU,MAAK,CAAE;IACvD;AACA,UAAM,WAAW,MACf,OAAO,QAAQ,kBAAkB,EAAE,IAAI,CAAC,CAAC,SAASC,UAAS,MACzDC,WAAU,QAAQ;MAChB,GAAG;MACH;MACA;MACA;MACA,SAAS,OAAO,SAAS,OAAO;MAChC,WAAWD,WAAU,IAAI,CAAC,EAAE,SAAQ,MAAO,QAAQ;KACpD,CAAC;AAGN,UAAM,oBAAoB,MAAM,QAAQ,IAAI,SAAQ,CAAE,GAAG,KAAI;AAG7D,UAAM,gBAAgB,OAAO,OAAO,kBAAkB,EAAE,QACtD,CAACA,eAAcA,WAAU,IAAI,CAAC,EAAE,MAAK,MAAO,KAAK,CAAC;AAEpD,WAAO,iBAAiB,OAAO,CAAC,SAAS,QAAQ,UAAS;AACxD,UAAI;AAAU,gBAAsB,cAAc,KAAK,CAAE,IAAI;AAC7D,aAAO;IACT,GAAG,CAAA,CAAe;EACpB,SAAS,OAAO;AACd,QAAI,iBAAiB;AAAgC,YAAM;AAE3D,UAAM,WAAW,MACf,UAAU,IAAI,CAAC,aACbE,cAAa,QAAQ,EAAE,GAAG,UAAU,aAAa,SAAQ,CAAE,CAAC;AAEhE,QAAI;AACF,cAAQ,MAAM,QAAQ,WAAW,SAAQ,CAAE,GAAG,IAAI,CAAC,WAAU;AAC3D,YAAI,OAAO,WAAW;AACpB,iBAAO,EAAE,QAAQ,OAAO,OAAO,QAAQ,UAAS;AAClD,eAAO,EAAE,OAAO,OAAO,QAAQ,QAAQ,QAAW,QAAQ,UAAS;MACrE,CAAC;AAEH,WAAQ,MAAM,QAAQ,IAAI,SAAQ,CAAE;EAItC;AACF;;;AC3DA,eAAsBC,YACpB,QACA,YAAwC;AAExC,QAAM,EACJ,SACA,aACA,UACA,SACA,OAAO,cACP,OAAO,QAAO,IACZ;AAEJ,MAAI,cAAc;AAChB,QAAI;AACF,aAAO,MAAM,gBAAgB,QAAQ;QACnC,gBAAgB;QAChB;QACA,YAAY;QACZ;OACD;IACH,SAAS,OAAO;AAId,UACG,MAAiC,SAClC,kCACA;AACA,cAAM,UAAU,MAAM,gBAAgB,QAAQ;UAC5C,gBAAgB;UAChB;UACA,YAAY;UACZ;SACD;AACD,cAAM,SAAS,YACb,KAAK,QAAQ,QAAe,EAAE,KAAK,QAAO,CAAE,CAAC;AAE/C,eAAO,EAAE,GAAG,SAAS,OAAM;MAC7B;AACA,YAAM;IACR;EACF;AAEA,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,YAAiB,YAAY;AAC9D,QAAM,QAAQ,MAAM,OAClB,cAAc,EAAE,SAAS,YAAW,IAAK,EAAE,SAAS,SAAQ,CAAE;AAEhE,QAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK,OAAO;AACpE,SAAO;IACL,UAAU,MAAM,eAAe;IAC/B,WAAW,YAAY,OAAO,QAAQ,IAAI,CAAC;IAC3C,QAAQ,MAAM,eAAe;IAC7B;;AAEJ;AAUA,eAAe,gBACb,QACA,YAAqC;AAErC,QAAM,EAAE,gBAAgB,SAAS,YAAY,cAAc,KAAI,IAAK;AACpE,QAAM,WAAW;IACf,KAAK;MACH;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAC,EAAE,MAAM,UAAS,CAAE;QAC5B,SAAS,CAAC,EAAE,MAAM,UAAS,CAAE;;MAE/B;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,MAAM,QAAO,CAAE;;MAE7B;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,MAAM,WAAU,CAAE;;;IAGlC,SAAS;;AAEX,QAAM,CAAC,OAAO,UAAU,MAAM,IAAI,MAAM,cAAc,QAAQ;IAC5D,cAAc;IACd,WAAW;MACT;QACE,GAAG;QACH,cAAc;QACd,MAAM,CAAC,cAAc;QACrB;;MAEF,EAAE,GAAG,UAAU,cAAc,YAAY,QAAO;MAChD,EAAE,GAAG,UAAU,cAAc,UAAU,QAAO;;GAEjD;AACD,QAAM,YAAY,YAAY,SAAS,KAAK,QAAQ,QAAQ,QAAQ,CAAC;AACrE,SAAO,EAAE,UAAU,WAAW,QAAQ,MAAK;AAC7C;;;ACtGA,eAAsBC,UAMpB,QACA,aAKI,CAAA,GAAE;AAEN,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,UAAe,UAAU;AAC1D,QAAM,QAAQ,MAAM,OAAO,IAAI;AAC/B,SAAO;IACL,GAAI;IAMJ,SAAS,OAAO,MAAM;;AAE1B;;;AClDM,SAAUC,gBAKd,QACA,aAAwD,CAAA,GAAE;AAE1D,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,gBAAqB,gBAAgB;AACtE,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,0BAKd,QACA,aAAkE,CAAA,GAAE;AAEpE,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,0BACA,0BAA0B;AAE5B,SAAO,OAAO,IAAI;AACpB;;;ACtBA,eAAsB,YACpB,QACA,YAAyC;AAEzC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,SAAkB,aAAa;AAChE,SAAO,OAAO,IAAI;AACpB;;;ACVA,eAAsBC,gBACpB,QACA,YAAoC;AAEpC,QAAM,EAAE,WAAW,GAAE,IAAK;AAC1B,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,UAAS,CAAE;AAC7D,SAAO,eAAoB,QAAQ,EAAE,GAAE,CAAE;AAC3C;;;ACDA,eAAsBC,iBAIpB,QACA,aAAyD,CAAA,GAAE;AAE3D,QAAM,EAAE,SAAS,SAAS,UAAS,IAAK;AACxC,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,SAAS,UAAS,CAAE;AACtE,SAAO,gBAAqB,QAAe;IACzC;IACA;GACD;AACH;;;AChCM,SAAU,WACd,QAAc;AAEd,SAAO,OAAO,MAAM;AACtB;;;ACRM,SAAU,UAAU,GAAQ,GAAM;AACtC,MAAI,MAAM;AAAG,WAAO;AAEpB,MAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,QAAI,EAAE,gBAAgB,EAAE;AAAa,aAAO;AAE5C,QAAI;AACJ,QAAI;AAEJ,QAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACxC,eAAS,EAAE;AACX,UAAI,WAAW,EAAE;AAAQ,eAAO;AAChC,WAAK,IAAI,QAAQ,QAAQ;AAAK,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,iBAAO;AACjE,aAAO;IACT;AAEA,QAAI,EAAE,YAAY,OAAO,UAAU;AACjC,aAAO,EAAE,QAAO,MAAO,EAAE,QAAO;AAClC,QAAI,EAAE,aAAa,OAAO,UAAU;AAClC,aAAO,EAAE,SAAQ,MAAO,EAAE,SAAQ;AAEpC,UAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE;AAAQ,aAAO;AAE7C,SAAK,IAAI,QAAQ,QAAQ;AAAK,UAAI,CAAC,OAAO,OAAO,GAAG,KAAK,CAAC,CAAE;AAAG,eAAO;AAEtE,SAAK,IAAI,QAAQ,QAAQ,KAAK;AAC5B,YAAM,MAAM,KAAK,CAAC;AAElB,UAAI,OAAO,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAG,eAAO;IAChD;AAEA,WAAO;EACT;AAIA,SAAO,MAAM,KAAK,MAAM;AAC1B;;;AChCA,IAAI,iBAAmC,CAAA;AAGjC,SAAU,UACd,QAAc;AAEd,QAAM,SAAS,OAAO;AACtB,MAAI,UAAU,gBAAgB,MAAM;AAClC,WAAO;AACT,mBAAiB;AACjB,SAAO;AACT;;;ACmBM,SAAU,UAId,QACA,aAAmD,CAAA,GAAE;AAErD,MAAI;AACF,WAAO,OAAO,UAAU,UAAU;EACpC,QAAQ;AACN,WAAO;EACT;AACF;;;AC7CA,IAAI,sBAAoC,CAAA;AAGlC,SAAU,eAAe,QAAc;AAC3C,QAAM,cAAc,CAAC,GAAG,OAAO,MAAM,YAAY,OAAM,CAAE;AACzD,MAAI,OAAO,MAAM,WAAW;AAAgB,WAAO;AACnD,MAAI,UAAU,qBAAqB,WAAW;AAAG,WAAO;AACxD,wBAAsB;AACtB,SAAO;AACT;;;ACTA,IAAI,qBAA2C,CAAA;AAGzC,SAAU,cACd,QAAc;AAEd,QAAM,aAAa,OAAO;AAC1B,MAAI,UAAU,oBAAoB,UAAU;AAAG,WAAO;AACtD,uBAAqB;AACrB,SAAO;AACT;;;ACKM,SAAUC,eACd,QACA,YAA2C;AAE3C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,cACd,QACA,YAA0C;AAE1C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,cAAmB,cAAc;AAClE,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,YACd,QACA,YAAwC;AAExC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,YAAiB,YAAY;AAC9D,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,gBACd,QACA,YAA4C;AAE5C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,gBAAqB,gBAAgB;AACtE,SAAO,OAAO,IAAI;AACpB;;;ACRM,SAAUC,YACd,QACA,YAAwC;AAExC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,YAAiB,YAAY;AAC9D,SAAO,OAAO,IAAI;AACpB;;;ACNM,SAAUC,eAKd,QACA,YAAoD;AAEpD,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,SAAO,OAAO,IAAI;AACpB;;;ACbM,SAAUC,aAKd,QACA,aAAqD,CAAA,GAAE;AAEvD,QAAM,EAAE,QAAO,IAAK;AACpB,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,aAAkB,aAAa;AAChE,SAAO,OAAO,CAAA,CAAE;AAClB;;;ACbA,eAAsBC,UACpB,QACA,YAAsC;AAEtC,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,UAAe,UAAU;AAC1D,SAAO,OAAO,IAAI;AACpB;;;ACUM,SAAU,gBAId,QACA,aAAyD,CAAA,GAAE;AAE3D,QAAM,SAAS,UAAU,QAAQ,UAAU;AAC3C,SAAQ,QAAmB,OAAO,aAAa;AAIjD;;;AC9BA,eAAsBC,cACpB,QACA,YAA0C;AAE1C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,cAAmB,cAAc;AAClE,SAAO,OAAO,IAAI;AACpB;;;ACOA,eAAsB,SACpB,QACA,YAAsC;AAEtC,QAAM,EAAE,SAAS,SAAS,aAAa,OAAO,GAAE,IAAK;AAErD,WAAS,OAA0C,MAAU;AAC3D,WAAO;MACL;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,MAAM,QAAO,CAAE;;MAE7B;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,KAAI,CAAE;;MAEpB;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,KAAI,CAAE;;MAEpB;QACE,MAAM;QACN,MAAM;QACN,iBAAiB;QACjB,QAAQ,CAAA;QACR,SAAS,CAAC,EAAE,MAAM,UAAS,CAAE;;;EAGnC;AAEA,MAAI;AACF,UAAM,MAAM,OAAO,QAAQ;AAC3B,UAAM,iBAAiB,EAAE,SAAS,KAAK,QAAO;AAC9C,UAAM,CAAC,UAAU,MAAM,QAAQ,WAAW,IAAI,MAAM,cAAc,QAAQ;MACxE,cAAc;MACd,WAAW;QACT,EAAE,GAAG,gBAAgB,cAAc,WAAU;QAC7C,EAAE,GAAG,gBAAgB,cAAc,OAAM;QACzC,EAAE,GAAG,gBAAgB,cAAc,SAAQ;QAC3C,EAAE,GAAG,gBAAgB,cAAc,cAAa;;KAEnD;AAGD,QAAI,KAAK,iBAAiB;AAAgC,YAAM,KAAK;AACrE,QAAI,OAAO,iBAAiB;AAC1B,YAAM,OAAO;AAGf,QAAI,SAAS;AAAO,YAAM,SAAS;AACnC,QAAI,YAAY;AAAO,YAAM,YAAY;AAEzC,WAAO;MACL;MACA,UAAU,SAAS;MACnB,MAAM,KAAK;MACX,QAAQ,OAAO;MACf,aAAa;QACX,WAAW,YAAY,YAAY,QAAS,QAAQ,IAAI,CAAC;QACzD,OAAO,YAAY;;;EAGzB,SAAS,OAAO;AAId,QAAI,iBAAiB,gCAAgC;AACnD,YAAM,MAAM,OAAO,SAAS;AAC5B,YAAM,iBAAiB,EAAE,SAAS,KAAK,QAAO;AAC9C,YAAM,CAAC,UAAU,MAAM,QAAQ,WAAW,IAAI,MAAM,cAClD,QACA;QACE,cAAc;QACd,WAAW;UACT,EAAE,GAAG,gBAAgB,cAAc,WAAU;UAC7C,EAAE,GAAG,gBAAgB,cAAc,OAAM;UACzC,EAAE,GAAG,gBAAgB,cAAc,SAAQ;UAC3C,EAAE,GAAG,gBAAgB,cAAc,cAAa;;OAEnD;AAEH,aAAO;QACL;QACA;QACA,MAAM,YAAY,KAAK,MAAa,EAAE,KAAK,QAAO,CAAE,CAAC;QACrD,QAAQ,YAAY,KAAK,QAAe,EAAE,KAAK,QAAO,CAAE,CAAC;QACzD,aAAa;UACX,WAAW,YAAY,aAAa,QAAQ,IAAI,CAAC;UACjD,OAAO;;;IAGb;AAEA,UAAM;EACR;AACF;;;ACvGM,SAAUC,gBAId,QACA,YAAqD;AAErD,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,gBAAqB,gBAAgB;AACtE,SAAO,OAAO,IAAI;AAGpB;;;AChBM,SAAUC,6BAMd,QACA,YAAkE;AAElE,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,6BACA,6BAA6B;AAE/B,SAAO,OAAO,IAAkD;AAClE;;;AC/BA,eAAsBC,qBACpB,QACA,YAAiD;AAEjD,QAAM,EAAE,SAAS,aAAa,UAAU,QAAO,IAAK;AAEpD,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,qBACA,qBAAqB;AAEvB,SAAO,OAAO,cAAc,EAAE,SAAS,YAAW,IAAK,EAAE,SAAS,SAAQ,CAAE;AAC9E;;;ACMA,eAAsBC,uBAIpB,QACA,YAAmD;AAEnD,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,uBACA,uBAAuB;AAEzB,SAAO,OAAO,IAAI;AAGpB;;;ACpBA,eAAsB,gBAIpB,QACA,aAAyD,CAAA,GAAE;AAE3D,QAAM,SAAS,MAAM,mBAAmB,QAAQ,UAAU;AAE1D,SAAO,OAAO,OAAO,aAAa;AAIpC;;;ACgDA,eAAsBC,2BAQpB,QACA,YAAyE;AAEzE,QAAM,EAAE,SAAS,UAAU,SAAS,GAAG,KAAI,IAAK;AAEhD,QAAM,UAAU,YAAY,WAAW,MAAM,EAAE;AAC/C,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAE3C,QAAM,SAAS,UACb,QACA,2BACA,2BAA2B;AAE7B,SAAO,OAAO;IACZ,GAAG;IACH,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;GAC0B;AAG1D;;;AC5GA,IAAI,iBAAiB;AAGrB,eAAsB,UACpB,QACA,aAAkC,CAAA,GAAE;AAGpC,MAAI;AAAgB,WAAO,CAAA;AAC3B,mBAAiB;AAEjB,SAAO,SAAS,CAAC,OAAO;IACtB,GAAG;IACH,QAAQ,EAAE,UAAU,iBAAiB;IACrC;AAEF,QAAM,aAA0B,CAAA;AAChC,MAAI,WAAW,YAAY,QAAQ;AACjC,eAAW,cAAc,WAAW,YAAY;AAC9C,UAAI;AAEJ,UAAI,OAAO,eAAe;AACxB,oBAAY,OAAO,UAAU,WAAW,MAAM,UAAU;;AACrD,oBAAY;AACjB,iBAAW,KAAK,SAAS;IAC3B;EACF;AAAO,eAAW,KAAK,GAAG,OAAO,UAAU;AAG3C,MAAI;AACJ,MAAI;AACF,wBAAoB,MAAM,OAAO,SAAS,QAAQ,mBAAmB;EACvE,QAAQ;EAAC;AACT,QAAM,SAAiC,CAAA;AACvC,aAAW,CAAC,EAAE,UAAU,KAAK,OAAO,MAAM,aAAa;AACrD,WAAO,WAAW,UAAU,EAAE,IAAI;EACpC;AACA,MAAI;AAAmB,WAAO,iBAAiB,IAAI;AACnD,QAAM,SACJ,OAAO,KAAK,MAAM,EAAE,SAAS;;IAEzB,CAAC,GAAG,UAAU,EAAE,KACd,CAAC,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,OAAO,OAAO,EAAE,EAAE,KAAK,GAAG;MAEvD;AAGN,MAAI,YAAY;AAChB,QAAM,cAA4B,CAAA;AAClC,QAAM,YAAuB,CAAA;AAC7B,aAAW,aAAa,QAAQ;AAC9B,UAAM,WAAW,MAAM,UAAU,YAAW,EAAG,MAAM,MAAM,MAAS;AACpE,QAAI,CAAC;AAAU;AAMf,QAAI,UAAU,KAAK,CAAC,MAAM,MAAM,QAAQ;AAAG;AAE3C,UAAM,eAAe,MAAM,UAAU,aAAY;AACjD,QAAI,CAAC;AAAc;AAEnB,UAAM,OAAO,MAAM,UAChB,QAAQ,EAAE,gBAAgB,KAAI,CAAE,EAChC,MAAM,MAAM,IAAI;AACnB,QAAI,CAAC;AAAM;AAEX,cAAU,QAAQ,IAAI,WAAW,OAAO,UAAU,OAAO,OAAO;AAChE,cAAU,QAAQ,GAAG,UAAU,OAAO,UAAU,OAAO,MAAM;AAC7D,cAAU,QAAQ,GAAG,cAAc,OAAO,UAAU,OAAO,UAAU;AAErE,WAAO,SAAS,CAAC,MAAK;AACpB,YAAMC,eAAc,IAAI,IAAI,YAAY,EAAE,cAAc,oBAAI,IAAG,CAAE,EAAE,IACjE,UAAU,KACV,EAAE,UAAU,KAAK,UAAU,SAAS,KAAK,SAAS,UAAS,CAAE;AAE/D,aAAO;QACL,GAAG;QACH,SAAS,YAAY,EAAE,UAAU,UAAU;QAC3C,aAAAA;;IAEJ,CAAC;AACD,gBAAY,KAAK;MACf,UAAU,KAAK;MACf,SAAS,KAAK;MACd;KACD;AACD,cAAU,KAAK,QAAQ;AACvB,gBAAY;EACd;AAGA,MACE,OAAO,MAAM,WAAW,kBACxB,OAAO,MAAM,WAAW,cACxB;AAEA,QAAI,CAAC;AACH,aAAO,SAAS,CAAC,OAAO;QACtB,GAAG;QACH,aAAa,oBAAI,IAAG;QACpB,SAAS;QACT,QAAQ;QACR;;AACC,aAAO,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,QAAQ,YAAW,EAAG;EAC7D;AAEA,mBAAiB;AACjB,SAAO;AACT;;;AC3EA,eAAsBC,WAKpB,QACA,YAAuD;AAEvD,QAAM,EAAE,SAAS,SAAS,WAAW,OAAO,GAAG,KAAI,IAAK;AAExD,QAAM,SAAS,MAAM,mBAAmB,QAAQ;IAC9C;IACA;IACA;GACD;AAED,SAAO,UAAe,QAAQ;IAC5B,GAAI;IACJ,GAAI,OAAO,YAAY,cAAc,EAAE,QAAO,IAAK,CAAA;IACnD;IACA,OAAO,UAAU,EAAE,IAAI,QAAO,IAAK;GACpC;AACH;;;AChBA,eAAsBC,iBAIpB,QACA,YAAsD;AAEtD,QAAM,EAAE,SAAS,SAAS,WAAW,GAAG,KAAI,IAAK;AAEjD,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY,SAAS,SAAS;AACnD,aAAS,OAAO,UAAU,EAAE,QAAO,CAAE;;AAErC,aAAS,MAAM,mBAAmB,QAAQ;MACxC,SAAS,WAAW;MACpB;MACA;KACD;AAEH,QAAM,SAAS,UAAU,QAAQ,iBAAsB,iBAAiB;AACxE,QAAM,OAAO,MAAM,OAAO;IACxB,GAAI;IACJ,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;IAC5B,OAAO,UAAU,EAAE,IAAI,QAAO,IAAK;IACnC,KAAK,KAAK,OAAO;GAClB;AAED,SAAO;AACT;;;AClEA,eAAsBC,iBACpB,QACA,YAAqC;AAErC,QAAM,EAAE,WAAW,GAAE,IAAK;AAC1B,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,UAAS,CAAE;AAC7D,SAAO,gBAAqB,QAAQ,EAAE,GAAE,CAAE;AAC5C;;;ACQA,eAAsBC,aACpB,QACA,YAAiC;AAEjC,QAAM,EAAE,SAAS,WAAW,GAAG,KAAI,IAAK;AAExC,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY,QAAQ,SAAS;AAClD,aAAS,OAAO,UAAS;;AACtB,aAAS,MAAM,mBAAmB,QAAQ,EAAE,SAAS,UAAS,CAAE;AAErE,QAAM,SAAS,UAAU,QAAQ,aAAkB,aAAa;AAChE,SAAO,OAAO;IACZ,GAAG;IACH,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;GACU;AAC1C;;;ACVA,eAAsBC,eAIpB,QACA,YAA2D;AAE3D,QAAM,EAAE,SAAS,WAAW,GAAG,KAAI,IAAK;AAExC,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY,QAAQ,SAAS;AAClD,aAAS,OAAO,UAAS;;AACtB,aAAS,MAAM,mBAAmB,QAAQ,EAAE,SAAS,UAAS,CAAE;AAErE,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,SAAO,OAAO;IACZ,GAAG;IACH,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;GACc;AAC9C;;;ACwDA,eAAsBC,kBAWpB,QACA,YAMC;AAID,QAAM,EAAE,KAAK,SAAS,WAAW,GAAG,KAAI,IACtC;AAEF,MAAI;AACJ,MAAI,WAAW;AAAS,cAAU,WAAW;OACxC;AACH,UAAM,kBAAkB,MAAM,mBAAmB,QAAQ;MACvD;MACA;KACD;AACD,cAAU,gBAAgB;EAC5B;AAEA,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,kBAAuB,kBAAkB;AAC1E,QAAM,EAAE,QAAQ,QAAO,IAAK,MAAM,OAAO,EAAE,GAAG,MAAM,KAAK,QAAO,CAAE;AAElE,SAAO;IACL,SAAS,OAAO,MAAM;IACtB;IACA,SAAS,EAAE,GAAG,SAAS,QAAO;;AAQlC;;;AC3IA,eAAsB,cACpB,QACA,YAAmC;AAEnC,QAAM,EAAE,UAAS,IAAK;AAEtB,QAAM,aAAa,OAAO,MAAM,YAAY,IAAI,UAAU,GAAG;AAC7D,MAAI,CAAC;AAAY,UAAM,IAAI,2BAA0B;AAErD,QAAM,OAAO,SAAS,QAAQ,qBAAqB,UAAU,EAAE;AAC/D,SAAO,SAAS,CAAC,OAAO;IACtB,GAAG;IACH,SAAS,UAAU;IACnB;AACF,SAAO;IACL,UAAU,WAAW;IACrB,SAAS,WAAW;;AAExB;;;ACtCM,IAAO,wBAAP,cAAqC,UAAS;EAElD,cAAA;AACE,UAAM,qBAAqB;AAFpB,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGhB;;AAMI,IAAO,+BAAP,cAA4C,UAAS;EAGzD,YAAY,EAAE,UAAS,GAA4B;AACjD,UAAM,IAAI,UAAU,IAAI,kDAAkD;AAHnE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIhB;;;;ACkCF,eAAsB,YAIpB,QACA,YAAkD;AAElD,QAAM,EAAE,2BAA2B,QAAO,IAAK;AAE/C,QAAM,aAAa,OAAO,MAAM,YAAY,IAC1C,WAAW,WAAW,OAAO,OAAO,MAAM,OAAQ;AAEpD,MAAI,YAAY;AACd,UAAM,YAAY,WAAW;AAC7B,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,6BAA6B,EAAE,UAAS,CAAE;AACtD,UAAMC,SAAQ,MAAM,UAAU,YAAY;MACxC;MACA;KACD;AACD,WAAOA;EACT;AAEA,QAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,MAAI,CAAC;AAAO,UAAM,IAAI,wBAAuB;AAC7C,SAAO,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,QAAO,EAAG;AAC1C,SAAO;AACT;;;AC7DA,eAAsBC,eACpB,QACA,YAA2C;AAE3C,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,SAAO,OAAO,IAAI;AACpB;;;ACFA,eAAsBC,iBAKpB,QACA,YAAqE;AAErE,QAAM,EAAE,SAAS,GAAG,KAAI,IAAK;AAC7B,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UAAU,QAAQ,iBAAsB,iBAAiB;AACxE,SAAO,OAAO,IAAsC;AACtD;;;ACpBA,eAAsBC,oBACpB,QACA,YAAwC;AAExC,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,UAAS,CAAE;AAC7D,SAAO,mBAAwB,QAAQ,UAAU;AACnD;;;ACgBA,eAAsBC,2BAIpB,QACA,YAAgE;AAEhE,QAAM,EAAE,SAAS,UAAU,GAAG,GAAG,KAAI,IAAK;AAE1C,QAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,QAAM,SAAS,UACb,QACA,2BACA,2BAA2B;AAE7B,QAAM,UAAU,MAAM,OAAO,EAAE,GAAG,MAAM,QAAO,CAAE;AAEjD,MAAI,QAAQ,WAAW,YAAY;AACjC,UAAM,wBAAwB,UAC5B,QACA,gBACA,gBAAgB;AAElB,UAAM,MAAM,MAAM,sBAAsB,EAAE,MAAM,QAAQ,gBAAe,CAAE;AACzE,UAAM,cAAc,UAAU,QAAQ,MAAM,MAAM;AAClD,UAAM,OAAO,MAAM,YAAY;MAC7B,GAAI;MACJ,MAAM,IAAI;MACV,UAAU,IAAI,SAAS,YAAY,IAAI,WAAW;MAClD,cAAc,IAAI,SAAS,YAAY,IAAI,eAAe;MAC1D,sBACE,IAAI,SAAS,YAAY,IAAI,uBAAuB;KACvD;AACD,UAAM,SAAS,MAAM,OACjB,YAAY,KAAK,KAAK,KAAK,UAAU,GAAG,CAAC,EAAE,IAC3C;AACJ,UAAM,IAAI,MAAM,MAAM;EACxB;AAEA,SAAO;IACL,GAAG;IACH,SAAS,OAAO,MAAM;;AAE1B;;;ACvEM,SAAU,aACd,QACA,YAA0C;AAE1C,QAAM,EAAE,SAAQ,IAAK;AAErB,SAAO,OAAO,UAAU,MAAM,WAAW,MAAM,GAAG,UAAU;IAC1D,WAAW,GAAG,GAAC;AACb,YAAM,EAAE,WAAW,YAAY,GAAG,MAAK,IAAK;AAC5C,YAAM,EAAE,WAAW,YAAY,GAAG,MAAK,IAAK;AAC5C,aACE,UAAU,OAAO,KAAK;MAEtB,YAAY,OAAO,YAAY,MAC/B,YAAY,QAAQ,YAAY;IAEpC;GACD;AACH;;;ACCA,eAAsBC,YACpB,QACA,YAAgC;AAEhC,QAAM,EAAE,WAAW,GAAG,KAAI,IAAK;AAE/B,QAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,UAAS,CAAE;AAE7D,QAAM,SAAS,UAAU,QAAQ,YAAiB,YAAY;AAC9D,SAAO,OAAO,IAAiC;AACjD;;;ACHM,SAAUC,kBAKd,QACA,YAAuD;AAEvD,QAAM,EAAE,qBAAqB,OAAO,UAAU,oBAAoB,GAAG,KAAI,IACvE;AAEF,MAAI;AACJ,QAAM,WAAW,CAAC,YAA+B;AAC/C,QAAI;AAAS,cAAO;AAEpB,UAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,UAAM,SAAS,UAAU,QAAQ,kBAAuB,kBAAkB;AAC1E,cAAU,OAAO,IAAuC;AACxD,WAAO;EACT;AAGA,QAAM,WAAW,SAAS,WAAW,OAAO;AAG5C,MAAI;AACJ,MAAI,sBAAsB,CAAC,WAAW;AACpC,kBAAc,OAAO,UACnB,CAAC,EAAE,QAAO,MAAO,SACjB,OAAO,YAAY,SAAS,OAAO,CAAC;AAGxC,SAAO,MAAK;AACV,eAAU;AACV,kBAAa;EACf;AACF;;;AC/BM,SAAUC,aAOd,QACA,YAKC;AAED,QAAM,EAAE,qBAAqB,OAAO,UAAU,oBAAoB,GAAG,KAAI,IACvE;AAEF,MAAI;AACJ,QAAM,WAAW,CAAC,YAA+B;AAC/C,QAAI;AAAS,cAAO;AAEpB,UAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,UAAM,SAAS,UAAU,QAAQ,aAAkB,aAAa;AAChE,cAAU,OAAO,IAAkC;AACnD,WAAO;EACT;AAGA,QAAM,WAAW,SAAS,WAAW,OAAO;AAG5C,MAAI;AACJ,MAAI,sBAAsB,CAAC,WAAW;AACpC,kBAAc,OAAO,UACnB,CAAC,EAAE,QAAO,MAAO,SACjB,OAAO,YAAY,SAAS,OAAO,CAAC;AAGxC,SAAO,MAAK;AACV,eAAU;AACV,kBAAa;EACf;AACF;;;AC3EM,SAAU,aACd,QACA,YAA0C;AAE1C,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UAAU,CAAC,UAAU,MAAM,SAAS,QAAQ;AAC5D;;;ACFM,SAAU,YAId,QACA,YAAkD;AAElD,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UACZ,MAAM,UAAU,MAAM,GACtB,UACA;IACE,WAAW,GAAG,GAAC;AACb,aAAO,GAAG,QAAQ,GAAG;IACvB;GACD;AAEL;;;ACjBM,SAAU,iBACd,QACA,YAAsC;AAEtC,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UAAU,MAAM,eAAe,MAAM,GAAG,UAAU;IAC9D,YAAY;GACb;AACH;;;ACZM,SAAU,gBACd,QACA,YAA6C;AAE7C,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UAAU,WAAW,UAAU,CAAC,YAAY,mBAAkB;AAC1E,aAAS,OAAO,OAAO,UAAU,GAAG,cAAc;EACpD,CAAC;AACH;;;ACgCM,SAAUC,oBAOd,QACA,YAMC;AAED,QAAM,EAAE,qBAAqB,OAAO,UAAU,oBAAoB,GAAG,KAAI,IACvE;AAEF,MAAI;AACJ,QAAM,WAAW,CAAC,YAA+B;AAC/C,QAAI;AAAS,cAAO;AAEpB,UAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,UAAM,SAAS,UACb,QACA,oBACA,oBAAoB;AAEtB,cAAU,OAAO,IAAoD;AACrE,WAAO;EACT;AAGA,QAAM,WAAW,SAAS,WAAW,OAAO;AAG5C,MAAI;AACJ,MAAI,sBAAsB,CAAC,WAAW;AACpC,kBAAc,OAAO,UACnB,CAAC,EAAE,QAAO,MAAO,SACjB,OAAO,YAAY,SAAS,OAAO,CAAC;AAGxC,SAAO,MAAK;AACV,eAAU;AACV,kBAAa;EACf;AACF;;;AC3DM,SAAUC,0BAId,QACA,YAA+D;AAE/D,QAAM,EAAE,qBAAqB,OAAO,UAAU,oBAAoB,GAAG,KAAI,IACvE;AAEF,MAAI;AACJ,QAAM,WAAW,CAAC,YAA+B;AAC/C,QAAI;AAAS,cAAO;AAEpB,UAAM,SAAS,OAAO,UAAU,EAAE,QAAO,CAAE;AAC3C,UAAM,SAAS,UACb,QACA,0BACA,0BAA0B;AAE5B,cAAU,OAAO,IAA+C;AAChE,WAAO;EACT;AAGA,QAAM,WAAW,SAAS,WAAW,OAAO;AAG5C,MAAI;AACJ,MAAI,sBAAsB,CAAC,WAAW;AACpC,kBAAc,OAAO,UACnB,CAAC,EAAE,QAAO,MAAO,SACjB,OAAO,YAAY,SAAS,OAAO,CAAC;AAGxC,SAAO,MAAK;AACV,eAAU;AACV,kBAAa;EACf;AACF;;;AC7DM,SAAU,kBAId,QACA,YAAwD;AAExD,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UACZ,MAAM,gBAAgB,MAAM,GAC5B,UACA;IACE,WAAW,GAAG,GAAC;AACb,aAAO,GAAG,QAAQ,GAAG;IACvB;GACD;AAEL;;;AC0CA,eAAsBC,eAWpB,QACA,YAA6E;AAE7E,QAAM,EAAE,SAAS,SAAS,WAAW,GAAG,QAAO,IAAK;AAEpD,MAAI;AACJ,MAAI,OAAO,YAAY,YAAY,SAAS,SAAS;AACnD,aAAS,OAAO,UAAU,EAAE,QAAO,CAAE;;AAErC,aAAS,MAAM,mBAAmB,QAAQ;MACxC,SAAS,WAAW;MACpB;MACA;KACD;AAEH,QAAM,SAAS,UAAU,QAAQ,eAAoB,eAAe;AACpE,QAAM,OAAO,MAAM,OAAO;IACxB,GAAI;IACJ,GAAI,UAAU,EAAE,QAAO,IAAK,CAAA;IAC5B,OAAO,UAAU,EAAE,IAAI,QAAO,IAAK;GACpC;AAED,SAAO;AACT;", "names": ["call", "_BaseError_walk", "connector", "chainId", "chain", "deployContract", "connections", "connector", "estimateFeesPerGas", "estimateGas", "estimateMaxPriorityFeePerGas", "chain", "multicall", "readContract", "contracts", "multicall", "readContract", "getBalance", "getBlock", "getBlockNumber", "getBlockTransactionCount", "getCallsStatus", "getCapabilities", "getEnsAddress", "getEnsAvatar", "getEnsName", "getEnsResolver", "getEnsText", "getFeeHistory", "getGasPrice", "getProof", "getStorageAt", "getTransaction", "getTransactionConfirmations", "getTransactionCount", "getTransactionReceipt", "prepareTransactionRequest", "connections", "sendCalls", "sendTransaction", "showCallsStatus", "signMessage", "signTypedData", "simulateContract", "chain", "verifyMessage", "verifyTypedData", "waitForCallsStatus", "waitForTransactionReceipt", "watchAsset", "watchBlockNumber", "watchBlocks", "watchContractEvent", "watchPendingTransactions", "writeContract"]}