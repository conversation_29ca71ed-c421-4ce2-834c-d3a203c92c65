import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const lineaGoerli = /*#__PURE__*/ defineChain({
  id: 59_140,
  name: 'Linea Goerli Testnet',
  nativeCurrency: { name: '<PERSON><PERSON> Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.goerli.linea.build'],
      webSocket: ['wss://rpc.goerli.linea.build'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Etherscan',
      url: 'https://goerli.lineascan.build',
      apiUrl: 'https://api-goerli.lineascan.build/api',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 498623,
    },
  },
  testnet: true,
})
