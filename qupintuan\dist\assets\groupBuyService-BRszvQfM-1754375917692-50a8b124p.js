import{c as t}from"./index-CaV4ohF9-1754375917692-opz9ain10.js";import{E as j,R as k,d as E,h as L,e as U,f as g,r as v}from"./index-CaV4ohF9-1754375917692-opz9ain10.js";import{createAndLock as r,fetchTotalRooms as e,fetchRoom as c,joinRoom as n,claimReward as m}from"./core-DKqkut0a-1754375917692-50a8b124p.js";import"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";import"./web3-NCUyEZtP-1754375917692-50a8b124p.js";import"./basicOperations-BOq0NLuf-1754375917692-50a8b124p.js";import"./roomManagement-BZPf_gXk-1754375917692-50a8b124p.js";import"./rewardOperations-I9R1_Rnl-1754375917692-50a8b124p.js";import"./transaction-DfFoEzbA-1754375917692-50a8b124p.js";async function h(o,a){return r(o,a)}async function y(o){return e(o)}async function S(o,a){return c(o,a)}async function T(o){return n(o)}async function w(o){return m(o)}async function $(o){return t.fetchRooms(o)}export{j as ERROR_CODES,k as ROOM_STATUS,E as calculateRemainingTime,L as canUserClaimReward,U as canUserJoinRoom,w as claimReward,h as createAndLock,t as default,S as fetchRoom,$ as fetchRooms,y as fetchTotalRooms,g as formatRoomStatus,T as joinRoom,t as newGroupBuyService,v as recalculateRoomStatus};
