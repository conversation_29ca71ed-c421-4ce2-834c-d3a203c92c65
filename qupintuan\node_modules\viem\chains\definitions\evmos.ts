import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const evmos = /*#__PURE__*/ define<PERSON>hain({
  id: 9_001,
  name: 'Evmos',
  nativeCurrency: {
    decimals: 18,
    name: 'Evmos',
    symbol: 'EVMOS',
  },
  rpcUrls: {
    default: { http: ['https://eth.bd.evmos.org:8545'] },
  },
  blockExplorers: {
    default: {
      name: 'Evmos Block Explorer',
      url: 'https://escan.live',
    },
  },
})
