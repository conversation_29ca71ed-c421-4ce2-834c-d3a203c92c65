{"version": 3, "sources": ["../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/accounts/utils/parseAccount.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/constants/abis.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/constants/contract.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/constants/contracts.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/version.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/base.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/chain.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/constants/solidity.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/formatAbiItem.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/data/isHex.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/data/size.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/abi.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/data.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/data/slice.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/data/pad.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/encoding.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/data/trim.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/encoding/fromHex.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/encoding/toHex.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/encoding/toBytes.ts", "../../@reown/appkit-controllers/node_modules/@noble/hashes/src/_u64.ts", "../../@reown/appkit-controllers/node_modules/@noble/hashes/src/sha3.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/hash/keccak256.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/hash/hashSignature.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/hash/normalizeSignature.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/hash/toSignature.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/hash/toSignatureHash.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/hash/toFunctionSelector.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/address.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/lru.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/address/isAddress.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/address/getAddress.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/cursor.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/cursor.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/encoding/fromBytes.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/data/concat.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/regex.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/encodeAbiParameters.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/decodeAbiParameters.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/decodeErrorResult.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/stringify.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/hash/toEventSelector.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/getAbiItem.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/constants/unit.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/unit/formatUnits.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/unit/formatEther.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/unit/formatGwei.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/stateOverride.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/transaction.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/utils.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/contract.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/decodeFunctionResult.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/encodeDeployData.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/prepareEncodeFunctionData.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/abi/encodeFunctionData.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/chain/getChainContractAddress.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/node.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/request.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/rpc.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/errors/getNodeError.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/errors/getCallError.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/extract.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/formatter.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/formatters/transactionRequest.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/promise/withResolvers.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/promise/createBatchScheduler.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/stateOverride.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/constants/number.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/transaction/assertRequest.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/actions/public/call.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/errors/ccip.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/address/isAddressEqual.ts", "../../@reown/appkit-controllers/node_modules/@walletconnect/utils/node_modules/viem/utils/ccip.ts"], "sourcesContent": ["import type { Address } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Account } from '../types.js'\n\nexport type ParseAccountErrorType = ErrorType\n\nexport function parseAccount<accountOrAddress extends Address | Account>(\n  account: accountOrAddress,\n): accountOrAddress extends Address ? Account : accountOrAddress {\n  if (typeof account === 'string')\n    return { address: account, type: 'json-rpc' } as any\n  return account as any\n}\n", "/* [Multicall3](https://github.com/mds1/multicall) */\nexport const multicall3Abi = [\n  {\n    inputs: [\n      {\n        components: [\n          {\n            name: 'target',\n            type: 'address',\n          },\n          {\n            name: 'allowFailure',\n            type: 'bool',\n          },\n          {\n            name: 'callData',\n            type: 'bytes',\n          },\n        ],\n        name: 'calls',\n        type: 'tuple[]',\n      },\n    ],\n    name: 'aggregate3',\n    outputs: [\n      {\n        components: [\n          {\n            name: 'success',\n            type: 'bool',\n          },\n          {\n            name: 'returnData',\n            type: 'bytes',\n          },\n        ],\n        name: 'returnData',\n        type: 'tuple[]',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n] as const\n\nconst universalResolverErrors = [\n  {\n    inputs: [],\n    name: 'ResolverNotFound',\n    type: 'error',\n  },\n  {\n    inputs: [],\n    name: 'ResolverWildcardNotSupported',\n    type: 'error',\n  },\n  {\n    inputs: [],\n    name: 'ResolverNotContract',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        name: 'returnData',\n        type: 'bytes',\n      },\n    ],\n    name: 'ResolverError',\n    type: 'error',\n  },\n  {\n    inputs: [\n      {\n        components: [\n          {\n            name: 'status',\n            type: 'uint16',\n          },\n          {\n            name: 'message',\n            type: 'string',\n          },\n        ],\n        name: 'errors',\n        type: 'tuple[]',\n      },\n    ],\n    name: 'HttpError',\n    type: 'error',\n  },\n] as const\n\nexport const universalResolverResolveAbi = [\n  ...universalResolverErrors,\n  {\n    name: 'resolve',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes' },\n      { name: 'data', type: 'bytes' },\n    ],\n    outputs: [\n      { name: '', type: 'bytes' },\n      { name: 'address', type: 'address' },\n    ],\n  },\n  {\n    name: 'resolve',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes' },\n      { name: 'data', type: 'bytes' },\n      { name: 'gateways', type: 'string[]' },\n    ],\n    outputs: [\n      { name: '', type: 'bytes' },\n      { name: 'address', type: 'address' },\n    ],\n  },\n] as const\n\nexport const universalResolverReverseAbi = [\n  ...universalResolverErrors,\n  {\n    name: 'reverse',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [{ type: 'bytes', name: 'reverseName' }],\n    outputs: [\n      { type: 'string', name: 'resolvedName' },\n      { type: 'address', name: 'resolvedAddress' },\n      { type: 'address', name: 'reverseResolver' },\n      { type: 'address', name: 'resolver' },\n    ],\n  },\n  {\n    name: 'reverse',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { type: 'bytes', name: 'reverseName' },\n      { type: 'string[]', name: 'gateways' },\n    ],\n    outputs: [\n      { type: 'string', name: 'resolvedName' },\n      { type: 'address', name: 'resolvedAddress' },\n      { type: 'address', name: 'reverseResolver' },\n      { type: 'address', name: 'resolver' },\n    ],\n  },\n] as const\n\nexport const textResolverAbi = [\n  {\n    name: 'text',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes32' },\n      { name: 'key', type: 'string' },\n    ],\n    outputs: [{ name: '', type: 'string' }],\n  },\n] as const\n\nexport const addressResolverAbi = [\n  {\n    name: 'addr',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [{ name: 'name', type: 'bytes32' }],\n    outputs: [{ name: '', type: 'address' }],\n  },\n  {\n    name: 'addr',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'name', type: 'bytes32' },\n      { name: 'coinType', type: 'uint256' },\n    ],\n    outputs: [{ name: '', type: 'bytes' }],\n  },\n] as const\n\n// ERC-1271\n// isValidSignature(bytes32 hash, bytes signature) → bytes4 magicValue\n/** @internal */\nexport const smartAccountAbi = [\n  {\n    name: 'isValidSignature',\n    type: 'function',\n    stateMutability: 'view',\n    inputs: [\n      { name: 'hash', type: 'bytes32' },\n      { name: 'signature', type: 'bytes' },\n    ],\n    outputs: [{ name: '', type: 'bytes4' }],\n  },\n] as const\n\n// ERC-6492 - universal deployless signature validator contract\n// constructor(address _signer, bytes32 _hash, bytes _signature) → bytes4 returnValue\n// returnValue is either 0x1 (valid) or 0x0 (invalid)\nexport const universalSignatureValidatorAbi = [\n  {\n    inputs: [\n      {\n        name: '_signer',\n        type: 'address',\n      },\n      {\n        name: '_hash',\n        type: 'bytes32',\n      },\n      {\n        name: '_signature',\n        type: 'bytes',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'constructor',\n  },\n  {\n    inputs: [\n      {\n        name: '_signer',\n        type: 'address',\n      },\n      {\n        name: '_hash',\n        type: 'bytes32',\n      },\n      {\n        name: '_signature',\n        type: 'bytes',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n    name: 'isValidSig',\n  },\n] as const\n\n/** [ERC-20 Token Standard](https://ethereum.org/en/developers/docs/standards/tokens/erc-20) */\nexport const erc20Abi = [\n  {\n    type: 'event',\n    name: 'Approval',\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'event',\n    name: 'Transfer',\n    inputs: [\n      {\n        indexed: true,\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'allowance',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'spender',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'approve',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'balanceOf',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'account',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'decimals',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint8',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'name',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'symbol',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'totalSupply',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transfer',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transferFrom',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n] as const\n\n/**\n * [bytes32-flavored ERC-20](https://docs.makerdao.com/smart-contract-modules/mkr-module#4.-gotchas-potential-source-of-user-error)\n * for tokens (ie. Maker) that use bytes32 instead of string.\n */\nexport const erc20Abi_bytes32 = [\n  {\n    type: 'event',\n    name: 'Approval',\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'event',\n    name: 'Transfer',\n    inputs: [\n      {\n        indexed: true,\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'allowance',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'spender',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'approve',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'balanceOf',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'account',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'decimals',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint8',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'name',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'bytes32',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'symbol',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'bytes32',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'totalSupply',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transfer',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transferFrom',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n] as const\n\n/** [ERC-721 Non-Fungible Token Standard](https://ethereum.org/en/developers/docs/standards/tokens/erc-721) */\nexport const erc721Abi = [\n  {\n    type: 'event',\n    name: 'Approval',\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'event',\n    name: 'ApprovalForAll',\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'approved',\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'event',\n    name: 'Transfer',\n    inputs: [\n      {\n        indexed: true,\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'approve',\n    stateMutability: 'payable',\n    inputs: [\n      {\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [],\n  },\n  {\n    type: 'function',\n    name: 'balanceOf',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'account',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'getApproved',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'address',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'isApprovedForAll',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'operator',\n        type: 'address',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'name',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'ownerOf',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'safeTransferFrom',\n    stateMutability: 'payable',\n    inputs: [\n      {\n        name: 'from',\n        type: 'address',\n      },\n      {\n        name: 'to',\n        type: 'address',\n      },\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [],\n  },\n  {\n    type: 'function',\n    name: 'safeTransferFrom',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'from',\n        type: 'address',\n      },\n      {\n        name: 'to',\n        type: 'address',\n      },\n      {\n        name: 'id',\n        type: 'uint256',\n      },\n      {\n        name: 'data',\n        type: 'bytes',\n      },\n    ],\n    outputs: [],\n  },\n  {\n    type: 'function',\n    name: 'setApprovalForAll',\n    stateMutability: 'nonpayable',\n    inputs: [\n      {\n        name: 'operator',\n        type: 'address',\n      },\n      {\n        name: 'approved',\n        type: 'bool',\n      },\n    ],\n    outputs: [],\n  },\n  {\n    type: 'function',\n    name: 'symbol',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'tokenByIndex',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'index',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'tokenByIndex',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'index',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'tokenURI',\n    stateMutability: 'view',\n    inputs: [\n      {\n        name: 'tokenId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [\n      {\n        type: 'string',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'totalSupply',\n    stateMutability: 'view',\n    inputs: [],\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n  },\n  {\n    type: 'function',\n    name: 'transferFrom',\n    stateMutability: 'payable',\n    inputs: [\n      {\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        name: 'recipient',\n        type: 'address',\n      },\n      {\n        name: 'tokeId',\n        type: 'uint256',\n      },\n    ],\n    outputs: [],\n  },\n] as const\n\n/** [ERC-4626 Tokenized Vaults Standard](https://ethereum.org/en/developers/docs/standards/tokens/erc-4626) */\nexport const erc4626Abi = [\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n    name: 'Approval',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'receiver',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'assets',\n        type: 'uint256',\n      },\n      {\n        indexed: false,\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'Deposit',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: 'from',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'to',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'value',\n        type: 'uint256',\n      },\n    ],\n    name: 'Transfer',\n    type: 'event',\n  },\n  {\n    anonymous: false,\n    inputs: [\n      {\n        indexed: true,\n        name: 'sender',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'receiver',\n        type: 'address',\n      },\n      {\n        indexed: true,\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        indexed: false,\n        name: 'assets',\n        type: 'uint256',\n      },\n      {\n        indexed: false,\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'Withdraw',\n    type: 'event',\n  },\n  {\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n      {\n        name: 'spender',\n        type: 'address',\n      },\n    ],\n    name: 'allowance',\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'spender',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    name: 'approve',\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [],\n    name: 'asset',\n    outputs: [\n      {\n        name: 'assetTokenAddress',\n        type: 'address',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'account',\n        type: 'address',\n      },\n    ],\n    name: 'balanceOf',\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'convertToAssets',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    name: 'convertToShares',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n      {\n        name: 'receiver',\n        type: 'address',\n      },\n    ],\n    name: 'deposit',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'caller',\n        type: 'address',\n      },\n    ],\n    name: 'maxDeposit',\n    outputs: [\n      {\n        name: 'maxAssets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'caller',\n        type: 'address',\n      },\n    ],\n    name: 'maxMint',\n    outputs: [\n      {\n        name: 'maxShares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'maxRedeem',\n    outputs: [\n      {\n        name: 'maxShares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'maxWithdraw',\n    outputs: [\n      {\n        name: 'maxAssets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n      {\n        name: 'receiver',\n        type: 'address',\n      },\n    ],\n    name: 'mint',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    name: 'previewDeposit',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'previewMint',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    name: 'previewRedeem',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    name: 'previewWithdraw',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n      {\n        name: 'receiver',\n        type: 'address',\n      },\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'redeem',\n    outputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [],\n    name: 'totalAssets',\n    outputs: [\n      {\n        name: 'totalManagedAssets',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [],\n    name: 'totalSupply',\n    outputs: [\n      {\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'view',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'to',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    name: 'transfer',\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'from',\n        type: 'address',\n      },\n      {\n        name: 'to',\n        type: 'address',\n      },\n      {\n        name: 'amount',\n        type: 'uint256',\n      },\n    ],\n    name: 'transferFrom',\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n  {\n    inputs: [\n      {\n        name: 'assets',\n        type: 'uint256',\n      },\n      {\n        name: 'receiver',\n        type: 'address',\n      },\n      {\n        name: 'owner',\n        type: 'address',\n      },\n    ],\n    name: 'withdraw',\n    outputs: [\n      {\n        name: 'shares',\n        type: 'uint256',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n  },\n] as const\n", "export const aggregate3Signature = '0x82ad56cb'\n", "export const deploylessCallViaBytecodeBytecode =\n  '0x608060405234801561001057600080fd5b5060405161018e38038061018e83398101604081905261002f91610124565b6000808351602085016000f59050803b61004857600080fd5b6000808351602085016000855af16040513d6000823e81610067573d81fd5b3d81f35b634e487b7160e01b600052604160045260246000fd5b600082601f83011261009257600080fd5b81516001600160401b038111156100ab576100ab61006b565b604051601f8201601f19908116603f011681016001600160401b03811182821017156100d9576100d961006b565b6040528181528382016020018510156100f157600080fd5b60005b82811015610110576020818601810151838301820152016100f4565b506000918101602001919091529392505050565b6000806040838503121561013757600080fd5b82516001600160401b0381111561014d57600080fd5b61015985828601610081565b602085015190935090506001600160401b0381111561017757600080fd5b61018385828601610081565b915050925092905056fe'\n\nexport const deploylessCallViaFactoryBytecode =\n  '0x608060405234801561001057600080fd5b506040516102c03803806102c083398101604081905261002f916101e6565b836001600160a01b03163b6000036100e457600080836001600160a01b03168360405161005c9190610270565b6000604051808303816000865af19150503d8060008114610099576040519150601f19603f3d011682016040523d82523d6000602084013e61009e565b606091505b50915091508115806100b857506001600160a01b0386163b155b156100e1578060405163101bb98d60e01b81526004016100d8919061028c565b60405180910390fd5b50505b6000808451602086016000885af16040513d6000823e81610103573d81fd5b3d81f35b80516001600160a01b038116811461011e57600080fd5b919050565b634e487b7160e01b600052604160045260246000fd5b60005b8381101561015457818101518382015260200161013c565b50506000910152565b600082601f83011261016e57600080fd5b81516001600160401b0381111561018757610187610123565b604051601f8201601f19908116603f011681016001600160401b03811182821017156101b5576101b5610123565b6040528181528382016020018510156101cd57600080fd5b6101de826020830160208701610139565b949350505050565b600080600080608085870312156101fc57600080fd5b61020585610107565b60208601519094506001600160401b0381111561022157600080fd5b61022d8782880161015d565b93505061023c60408601610107565b60608601519092506001600160401b0381111561025857600080fd5b6102648782880161015d565b91505092959194509250565b60008251610282818460208701610139565b9190910192915050565b60208152600082518060208401526102ab816040850160208701610139565b601f01601f1916919091016040019291505056fe'\n\nexport const universalSignatureValidatorByteCode =\n  '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'\n", "export const version = '2.23.2'\n", "import { version } from './version.js'\n\ntype ErrorConfig = {\n  getDocsUrl?: ((args: BaseErrorParameters) => string | undefined) | undefined\n  version?: string | undefined\n}\n\nlet errorConfig: ErrorConfig = {\n  getDocsUrl: ({\n    docsBaseUrl,\n    docsPath = '',\n    docsSlug,\n  }: BaseErrorParameters) =>\n    docsPath\n      ? `${docsBaseUrl ?? 'https://viem.sh'}${docsPath}${\n          docsSlug ? `#${docsSlug}` : ''\n        }`\n      : undefined,\n  version: `viem@${version}`,\n}\n\nexport function setErrorConfig(config: ErrorConfig) {\n  errorConfig = config\n}\n\ntype BaseErrorParameters = {\n  cause?: BaseError | Error | undefined\n  details?: string | undefined\n  docsBaseUrl?: string | undefined\n  docsPath?: string | undefined\n  docsSlug?: string | undefined\n  metaMessages?: string[] | undefined\n  name?: string | undefined\n}\n\nexport type BaseErrorType = BaseError & { name: 'BaseError' }\nexport class BaseError extends Error {\n  details: string\n  docsPath?: string | undefined\n  metaMessages?: string[] | undefined\n  shortMessage: string\n  version: string\n\n  override name = 'BaseError'\n\n  constructor(shortMessage: string, args: BaseErrorParameters = {}) {\n    const details = (() => {\n      if (args.cause instanceof BaseError) return args.cause.details\n      if (args.cause?.message) return args.cause.message\n      return args.details!\n    })()\n    const docsPath = (() => {\n      if (args.cause instanceof BaseError)\n        return args.cause.docsPath || args.docsPath\n      return args.docsPath\n    })()\n    const docsUrl = errorConfig.getDocsUrl?.({ ...args, docsPath })\n\n    const message = [\n      shortMessage || 'An error occurred.',\n      '',\n      ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n      ...(docsUrl ? [`Docs: ${docsUrl}`] : []),\n      ...(details ? [`Details: ${details}`] : []),\n      ...(errorConfig.version ? [`Version: ${errorConfig.version}`] : []),\n    ].join('\\n')\n\n    super(message, args.cause ? { cause: args.cause } : undefined)\n\n    this.details = details\n    this.docsPath = docsPath\n    this.metaMessages = args.metaMessages\n    this.name = args.name ?? this.name\n    this.shortMessage = shortMessage\n    this.version = version\n  }\n\n  walk(): Error\n  walk(fn: (err: unknown) => boolean): Error | null\n  walk(fn?: any): any {\n    return walk(this, fn)\n  }\n}\n\nfunction walk(\n  err: unknown,\n  fn?: ((err: unknown) => boolean) | undefined,\n): unknown {\n  if (fn?.(err)) return err\n  if (\n    err &&\n    typeof err === 'object' &&\n    'cause' in err &&\n    err.cause !== undefined\n  )\n    return walk(err.cause, fn)\n  return fn ? null : err\n}\n", "import type { Chain } from '../types/chain.js'\n\nimport { BaseError } from './base.js'\n\nexport type ChainDoesNotSupportContractErrorType =\n  ChainDoesNotSupportContract & {\n    name: 'ChainDoesNotSupportContract'\n  }\nexport class ChainDoesNotSupportContract extends BaseError {\n  constructor({\n    blockNumber,\n    chain,\n    contract,\n  }: {\n    blockNumber?: bigint | undefined\n    chain: Chain\n    contract: { name: string; blockCreated?: number | undefined }\n  }) {\n    super(\n      `Chain \"${chain.name}\" does not support contract \"${contract.name}\".`,\n      {\n        metaMessages: [\n          'This could be due to any of the following:',\n          ...(blockNumber &&\n          contract.blockCreated &&\n          contract.blockCreated > blockNumber\n            ? [\n                `- The contract \"${contract.name}\" was not deployed until block ${contract.blockCreated} (current block ${blockNumber}).`,\n              ]\n            : [\n                `- The chain does not have the contract \"${contract.name}\" configured.`,\n              ]),\n        ],\n        name: 'ChainDoesNotSupportContract',\n      },\n    )\n  }\n}\n\nexport type ChainMismatchErrorType = ChainMismatchError & {\n  name: 'ChainMismatchError'\n}\nexport class ChainMismatchError extends BaseError {\n  constructor({\n    chain,\n    currentChainId,\n  }: {\n    chain: Chain\n    currentChainId: number\n  }) {\n    super(\n      `The current chain of the wallet (id: ${currentChainId}) does not match the target chain for the transaction (id: ${chain.id} – ${chain.name}).`,\n      {\n        metaMessages: [\n          `Current Chain ID:  ${currentChainId}`,\n          `Expected Chain ID: ${chain.id} – ${chain.name}`,\n        ],\n        name: 'ChainMismatchError',\n      },\n    )\n  }\n}\n\nexport type ChainNotFoundErrorType = ChainNotFoundError & {\n  name: 'ChainNotFoundError'\n}\nexport class ChainNotFoundError extends BaseError {\n  constructor() {\n    super(\n      [\n        'No chain was provided to the request.',\n        'Please provide a chain with the `chain` argument on the Action, or by supplying a `chain` to WalletClient.',\n      ].join('\\n'),\n      {\n        name: 'ChainNotFoundError',\n      },\n    )\n  }\n}\n\nexport type ClientChainNotConfiguredErrorType =\n  ClientChainNotConfiguredError & {\n    name: 'ClientChainNotConfiguredError'\n  }\nexport class ClientChainNotConfiguredError extends BaseError {\n  constructor() {\n    super('No chain was provided to the Client.', {\n      name: 'ClientChainNotConfiguredError',\n    })\n  }\n}\n\nexport type InvalidChainIdErrorType = InvalidChainIdError & {\n  name: 'InvalidChainIdError'\n}\nexport class InvalidChainIdError extends BaseError {\n  constructor({ chainId }: { chainId?: number | undefined }) {\n    super(\n      typeof chainId === 'number'\n        ? `Chain ID \"${chainId}\" is invalid.`\n        : 'Chain ID is invalid.',\n      { name: 'InvalidChainIdError' },\n    )\n  }\n}\n", "import type { AbiError } from 'abitype'\n\n// https://docs.soliditylang.org/en/v0.8.16/control-structures.html#panic-via-assert-and-error-via-require\nexport const panicReasons = {\n  1: 'An `assert` condition failed.',\n  17: 'Arithmetic operation resulted in underflow or overflow.',\n  18: 'Division or modulo by zero (e.g. `5 / 0` or `23 % 0`).',\n  33: 'Attempted to convert to an invalid type.',\n  34: 'Attempted to access a storage byte array that is incorrectly encoded.',\n  49: 'Performed `.pop()` on an empty array',\n  50: 'Array index is out of bounds.',\n  65: 'Allocated too much memory or created an array which is too large.',\n  81: 'Attempted to call a zero-initialized variable of internal function type.',\n} as const\n\nexport const solidityError: AbiError = {\n  inputs: [\n    {\n      name: 'message',\n      type: 'string',\n    },\n  ],\n  name: 'Error',\n  type: 'error',\n}\nexport const solidityPanic: AbiError = {\n  inputs: [\n    {\n      name: 'reason',\n      type: 'uint256',\n    },\n  ],\n  name: 'Panic',\n  type: 'error',\n}\n", "import type { AbiParameter } from 'abitype'\n\nimport {\n  InvalidDefinitionTypeError,\n  type InvalidDefinitionTypeErrorType,\n} from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { AbiItem } from '../../types/contract.js'\n\nexport type FormatAbiItemErrorType =\n  | FormatAbiParamsErrorType\n  | InvalidDefinitionTypeErrorType\n  | ErrorType\n\nexport function formatAbiItem(\n  abiItem: AbiItem,\n  { includeName = false }: { includeName?: boolean | undefined } = {},\n) {\n  if (\n    abiItem.type !== 'function' &&\n    abiItem.type !== 'event' &&\n    abiItem.type !== 'error'\n  )\n    throw new InvalidDefinitionTypeError(abiItem.type)\n\n  return `${abiItem.name}(${formatAbiParams(abiItem.inputs, { includeName })})`\n}\n\nexport type FormatAbiParamsErrorType = ErrorType\n\nexport function formatAbiParams(\n  params: readonly AbiParameter[] | undefined,\n  { includeName = false }: { includeName?: boolean | undefined } = {},\n): string {\n  if (!params) return ''\n  return params\n    .map((param) => formatAbiParam(param, { includeName }))\n    .join(includeName ? ', ' : ',')\n}\n\nexport type FormatAbiParamErrorType = ErrorType\n\nfunction formatAbiParam(\n  param: AbiParameter,\n  { includeName }: { includeName: boolean },\n): string {\n  if (param.type.startsWith('tuple')) {\n    return `(${formatAbiParams(\n      (param as unknown as { components: AbiParameter[] }).components,\n      { includeName },\n    )})${param.type.slice('tuple'.length)}`\n  }\n  return param.type + (includeName && param.name ? ` ${param.name}` : '')\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\n\nexport type IsHexErrorType = ErrorType\n\nexport function isHex(\n  value: unknown,\n  { strict = true }: { strict?: boolean | undefined } = {},\n): value is Hex {\n  if (!value) return false\n  if (typeof value !== 'string') return false\n  return strict ? /^0x[0-9a-fA-F]*$/.test(value) : value.startsWith('0x')\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\nimport { type IsHexErrorType, isHex } from './isHex.js'\n\nexport type SizeErrorType = IsHexErrorType | ErrorType\n\n/**\n * @description Retrieves the size of the value (in bytes).\n *\n * @param value The value (hex or byte array) to retrieve the size of.\n * @returns The size of the value (in bytes).\n */\nexport function size(value: Hex | ByteArray) {\n  if (isHex(value, { strict: false })) return Math.ceil((value.length - 2) / 2)\n  return value.length\n}\n", "import type { <PERSON><PERSON>, <PERSON>biEvent, AbiParameter } from 'abitype'\n\nimport type { Hex } from '../types/misc.js'\nimport { formatAbiItem, formatAbiParams } from '../utils/abi/formatAbiItem.js'\nimport { size } from '../utils/data/size.js'\n\nimport { BaseError } from './base.js'\n\nexport type AbiConstructorNotFoundErrorType = AbiConstructorNotFoundError & {\n  name: 'AbiConstructorNotFoundError'\n}\nexport class AbiConstructorNotFoundError extends BaseError {\n  constructor({ docsPath }: { docsPath: string }) {\n    super(\n      [\n        'A constructor was not found on the ABI.',\n        'Make sure you are using the correct ABI and that the constructor exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiConstructorNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiConstructorParamsNotFoundErrorType =\n  AbiConstructorParamsNotFoundError & {\n    name: 'AbiConstructorParamsNotFoundError'\n  }\n\nexport class AbiConstructorParamsNotFoundError extends BaseError {\n  constructor({ docsPath }: { docsPath: string }) {\n    super(\n      [\n        'Constructor arguments were provided (`args`), but a constructor parameters (`inputs`) were not found on the ABI.',\n        'Make sure you are using the correct ABI, and that the `inputs` attribute on the constructor exists.',\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiConstructorParamsNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiDecodingDataSizeInvalidErrorType =\n  AbiDecodingDataSizeInvalidError & {\n    name: 'AbiDecodingDataSizeInvalidError'\n  }\nexport class AbiDecodingDataSizeInvalidError extends BaseError {\n  constructor({ data, size }: { data: Hex; size: number }) {\n    super(\n      [\n        `Data size of ${size} bytes is invalid.`,\n        'Size must be in increments of 32 bytes (size % 32 === 0).',\n      ].join('\\n'),\n      {\n        metaMessages: [`Data: ${data} (${size} bytes)`],\n        name: 'AbiDecodingDataSizeInvalidError',\n      },\n    )\n  }\n}\n\nexport type AbiDecodingDataSizeTooSmallErrorType =\n  AbiDecodingDataSizeTooSmallError & {\n    name: 'AbiDecodingDataSizeTooSmallError'\n  }\nexport class AbiDecodingDataSizeTooSmallError extends BaseError {\n  data: Hex\n  params: readonly AbiParameter[]\n  size: number\n\n  constructor({\n    data,\n    params,\n    size,\n  }: { data: Hex; params: readonly AbiParameter[]; size: number }) {\n    super(\n      [`Data size of ${size} bytes is too small for given parameters.`].join(\n        '\\n',\n      ),\n      {\n        metaMessages: [\n          `Params: (${formatAbiParams(params, { includeName: true })})`,\n          `Data:   ${data} (${size} bytes)`,\n        ],\n        name: 'AbiDecodingDataSizeTooSmallError',\n      },\n    )\n\n    this.data = data\n    this.params = params\n    this.size = size\n  }\n}\n\nexport type AbiDecodingZeroDataErrorType = AbiDecodingZeroDataError & {\n  name: 'AbiDecodingZeroDataError'\n}\nexport class AbiDecodingZeroDataError extends BaseError {\n  constructor() {\n    super('Cannot decode zero data (\"0x\") with ABI parameters.', {\n      name: 'AbiDecodingZeroDataError',\n    })\n  }\n}\n\nexport type AbiEncodingArrayLengthMismatchErrorType =\n  AbiEncodingArrayLengthMismatchError & {\n    name: 'AbiEncodingArrayLengthMismatchError'\n  }\nexport class AbiEncodingArrayLengthMismatchError extends BaseError {\n  constructor({\n    expectedLength,\n    givenLength,\n    type,\n  }: { expectedLength: number; givenLength: number; type: string }) {\n    super(\n      [\n        `ABI encoding array length mismatch for type ${type}.`,\n        `Expected length: ${expectedLength}`,\n        `Given length: ${givenLength}`,\n      ].join('\\n'),\n      { name: 'AbiEncodingArrayLengthMismatchError' },\n    )\n  }\n}\n\nexport type AbiEncodingBytesSizeMismatchErrorType =\n  AbiEncodingBytesSizeMismatchError & {\n    name: 'AbiEncodingBytesSizeMismatchError'\n  }\nexport class AbiEncodingBytesSizeMismatchError extends BaseError {\n  constructor({ expectedSize, value }: { expectedSize: number; value: Hex }) {\n    super(\n      `Size of bytes \"${value}\" (bytes${size(\n        value,\n      )}) does not match expected size (bytes${expectedSize}).`,\n      { name: 'AbiEncodingBytesSizeMismatchError' },\n    )\n  }\n}\n\nexport type AbiEncodingLengthMismatchErrorType =\n  AbiEncodingLengthMismatchError & {\n    name: 'AbiEncodingLengthMismatchError'\n  }\nexport class AbiEncodingLengthMismatchError extends BaseError {\n  constructor({\n    expectedLength,\n    givenLength,\n  }: { expectedLength: number; givenLength: number }) {\n    super(\n      [\n        'ABI encoding params/values length mismatch.',\n        `Expected length (params): ${expectedLength}`,\n        `Given length (values): ${givenLength}`,\n      ].join('\\n'),\n      { name: 'AbiEncodingLengthMismatchError' },\n    )\n  }\n}\n\nexport type AbiErrorInputsNotFoundErrorType = AbiErrorInputsNotFoundError & {\n  name: 'AbiErrorInputsNotFoundError'\n}\nexport class AbiErrorInputsNotFoundError extends BaseError {\n  constructor(errorName: string, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Arguments (\\`args\\`) were provided to \"${errorName}\", but \"${errorName}\" on the ABI does not contain any parameters (\\`inputs\\`).`,\n        'Cannot encode error result without knowing what the parameter types are.',\n        'Make sure you are using the correct ABI and that the inputs exist on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiErrorInputsNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiErrorNotFoundErrorType = AbiErrorNotFoundError & {\n  name: 'AbiErrorNotFoundError'\n}\nexport class AbiErrorNotFoundError extends BaseError {\n  constructor(\n    errorName?: string | undefined,\n    { docsPath }: { docsPath?: string | undefined } = {},\n  ) {\n    super(\n      [\n        `Error ${errorName ? `\"${errorName}\" ` : ''}not found on ABI.`,\n        'Make sure you are using the correct ABI and that the error exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiErrorNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiErrorSignatureNotFoundErrorType =\n  AbiErrorSignatureNotFoundError & {\n    name: 'AbiErrorSignatureNotFoundError'\n  }\nexport class AbiErrorSignatureNotFoundError extends BaseError {\n  signature: Hex\n\n  constructor(signature: Hex, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Encoded error signature \"${signature}\" not found on ABI.`,\n        'Make sure you are using the correct ABI and that the error exists on it.',\n        `You can look up the decoded signature here: https://openchain.xyz/signatures?query=${signature}.`,\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiErrorSignatureNotFoundError',\n      },\n    )\n    this.signature = signature\n  }\n}\n\nexport type AbiEventSignatureEmptyTopicsErrorType =\n  AbiEventSignatureEmptyTopicsError & {\n    name: 'AbiEventSignatureEmptyTopicsError'\n  }\nexport class AbiEventSignatureEmptyTopicsError extends BaseError {\n  constructor({ docsPath }: { docsPath: string }) {\n    super('Cannot extract event signature from empty topics.', {\n      docsPath,\n      name: 'AbiEventSignatureEmptyTopicsError',\n    })\n  }\n}\n\nexport type AbiEventSignatureNotFoundErrorType =\n  AbiEventSignatureNotFoundError & {\n    name: 'AbiEventSignatureNotFoundError'\n  }\nexport class AbiEventSignatureNotFoundError extends BaseError {\n  constructor(signature: Hex, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Encoded event signature \"${signature}\" not found on ABI.`,\n        'Make sure you are using the correct ABI and that the event exists on it.',\n        `You can look up the signature here: https://openchain.xyz/signatures?query=${signature}.`,\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiEventSignatureNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiEventNotFoundErrorType = AbiEventNotFoundError & {\n  name: 'AbiEventNotFoundError'\n}\nexport class AbiEventNotFoundError extends BaseError {\n  constructor(\n    eventName?: string | undefined,\n    { docsPath }: { docsPath?: string | undefined } = {},\n  ) {\n    super(\n      [\n        `Event ${eventName ? `\"${eventName}\" ` : ''}not found on ABI.`,\n        'Make sure you are using the correct ABI and that the event exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiEventNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiFunctionNotFoundErrorType = AbiFunctionNotFoundError & {\n  name: 'AbiFunctionNotFoundError'\n}\nexport class AbiFunctionNotFoundError extends BaseError {\n  constructor(\n    functionName?: string | undefined,\n    { docsPath }: { docsPath?: string | undefined } = {},\n  ) {\n    super(\n      [\n        `Function ${functionName ? `\"${functionName}\" ` : ''}not found on ABI.`,\n        'Make sure you are using the correct ABI and that the function exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiFunctionNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiFunctionOutputsNotFoundErrorType =\n  AbiFunctionOutputsNotFoundError & {\n    name: 'AbiFunctionOutputsNotFoundError'\n  }\nexport class AbiFunctionOutputsNotFoundError extends BaseError {\n  constructor(functionName: string, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Function \"${functionName}\" does not contain any \\`outputs\\` on ABI.`,\n        'Cannot decode function result without knowing what the parameter types are.',\n        'Make sure you are using the correct ABI and that the function exists on it.',\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiFunctionOutputsNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiFunctionSignatureNotFoundErrorType =\n  AbiFunctionSignatureNotFoundError & {\n    name: 'AbiFunctionSignatureNotFoundError'\n  }\nexport class AbiFunctionSignatureNotFoundError extends BaseError {\n  constructor(signature: Hex, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Encoded function signature \"${signature}\" not found on ABI.`,\n        'Make sure you are using the correct ABI and that the function exists on it.',\n        `You can look up the signature here: https://openchain.xyz/signatures?query=${signature}.`,\n      ].join('\\n'),\n      {\n        docsPath,\n        name: 'AbiFunctionSignatureNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AbiItemAmbiguityErrorType = AbiItemAmbiguityError & {\n  name: 'AbiItemAmbiguityError'\n}\nexport class AbiItemAmbiguityError extends BaseError {\n  constructor(\n    x: { abiItem: Abi[number]; type: string },\n    y: { abiItem: Abi[number]; type: string },\n  ) {\n    super('Found ambiguous types in overloaded ABI items.', {\n      metaMessages: [\n        `\\`${x.type}\\` in \\`${formatAbiItem(x.abiItem)}\\`, and`,\n        `\\`${y.type}\\` in \\`${formatAbiItem(y.abiItem)}\\``,\n        '',\n        'These types encode differently and cannot be distinguished at runtime.',\n        'Remove one of the ambiguous items in the ABI.',\n      ],\n      name: 'AbiItemAmbiguityError',\n    })\n  }\n}\n\nexport type BytesSizeMismatchErrorType = BytesSizeMismatchError & {\n  name: 'BytesSizeMismatchError'\n}\nexport class BytesSizeMismatchError extends BaseError {\n  constructor({\n    expectedSize,\n    givenSize,\n  }: { expectedSize: number; givenSize: number }) {\n    super(`Expected bytes${expectedSize}, got bytes${givenSize}.`, {\n      name: 'BytesSizeMismatchError',\n    })\n  }\n}\n\nexport type DecodeLogDataMismatchErrorType = DecodeLogDataMismatch & {\n  name: 'DecodeLogDataMismatch'\n}\nexport class DecodeLogDataMismatch extends BaseError {\n  abiItem: AbiEvent\n  data: Hex\n  params: readonly AbiParameter[]\n  size: number\n\n  constructor({\n    abiItem,\n    data,\n    params,\n    size,\n  }: {\n    abiItem: AbiEvent\n    data: Hex\n    params: readonly AbiParameter[]\n    size: number\n  }) {\n    super(\n      [\n        `Data size of ${size} bytes is too small for non-indexed event parameters.`,\n      ].join('\\n'),\n      {\n        metaMessages: [\n          `Params: (${formatAbiParams(params, { includeName: true })})`,\n          `Data:   ${data} (${size} bytes)`,\n        ],\n        name: 'DecodeLogDataMismatch',\n      },\n    )\n\n    this.abiItem = abiItem\n    this.data = data\n    this.params = params\n    this.size = size\n  }\n}\n\nexport type DecodeLogTopicsMismatchErrorType = DecodeLogTopicsMismatch & {\n  name: 'DecodeLogTopicsMismatch'\n}\nexport class DecodeLogTopicsMismatch extends BaseError {\n  abiItem: AbiEvent\n\n  constructor({\n    abiItem,\n    param,\n  }: {\n    abiItem: AbiEvent\n    param: AbiParameter & { indexed: boolean }\n  }) {\n    super(\n      [\n        `Expected a topic for indexed event parameter${\n          param.name ? ` \"${param.name}\"` : ''\n        } on event \"${formatAbiItem(abiItem, { includeName: true })}\".`,\n      ].join('\\n'),\n      { name: 'DecodeLogTopicsMismatch' },\n    )\n\n    this.abiItem = abiItem\n  }\n}\n\nexport type InvalidAbiEncodingTypeErrorType = InvalidAbiEncodingTypeError & {\n  name: 'InvalidAbiEncodingTypeError'\n}\nexport class InvalidAbiEncodingTypeError extends BaseError {\n  constructor(type: string, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Type \"${type}\" is not a valid encoding type.`,\n        'Please provide a valid ABI type.',\n      ].join('\\n'),\n      { docsPath, name: 'InvalidAbiEncodingType' },\n    )\n  }\n}\n\nexport type InvalidAbiDecodingTypeErrorType = InvalidAbiDecodingTypeError & {\n  name: 'InvalidAbiDecodingTypeError'\n}\nexport class InvalidAbiDecodingTypeError extends BaseError {\n  constructor(type: string, { docsPath }: { docsPath: string }) {\n    super(\n      [\n        `Type \"${type}\" is not a valid decoding type.`,\n        'Please provide a valid ABI type.',\n      ].join('\\n'),\n      { docsPath, name: 'InvalidAbiDecodingType' },\n    )\n  }\n}\n\nexport type InvalidArrayErrorType = InvalidArrayError & {\n  name: 'InvalidArrayError'\n}\nexport class InvalidArrayError extends BaseError {\n  constructor(value: unknown) {\n    super([`Value \"${value}\" is not a valid array.`].join('\\n'), {\n      name: 'InvalidArrayError',\n    })\n  }\n}\n\nexport type InvalidDefinitionTypeErrorType = InvalidDefinitionTypeError & {\n  name: 'InvalidDefinitionTypeError'\n}\nexport class InvalidDefinitionTypeError extends BaseError {\n  constructor(type: string) {\n    super(\n      [\n        `\"${type}\" is not a valid definition type.`,\n        'Valid types: \"function\", \"event\", \"error\"',\n      ].join('\\n'),\n      { name: 'InvalidDefinitionTypeError' },\n    )\n  }\n}\n\nexport type UnsupportedPackedAbiTypeErrorType = UnsupportedPackedAbiType & {\n  name: 'UnsupportedPackedAbiType'\n}\nexport class UnsupportedPackedAbiType extends BaseError {\n  constructor(type: unknown) {\n    super(`Type \"${type}\" is not supported for packed encoding.`, {\n      name: 'UnsupportedPackedAbiType',\n    })\n  }\n}\n", "import { BaseError } from './base.js'\n\nexport type SliceOffsetOutOfBoundsErrorType = SliceOffsetOutOfBoundsError & {\n  name: 'SliceOffsetOutOfBoundsError'\n}\nexport class SliceOffsetOutOfBoundsError extends BaseError {\n  constructor({\n    offset,\n    position,\n    size,\n  }: { offset: number; position: 'start' | 'end'; size: number }) {\n    super(\n      `Slice ${\n        position === 'start' ? 'starting' : 'ending'\n      } at offset \"${offset}\" is out-of-bounds (size: ${size}).`,\n      { name: 'SliceOffsetOutOfBoundsError' },\n    )\n  }\n}\n\nexport type SizeExceedsPaddingSizeErrorType = SizeExceedsPaddingSizeError & {\n  name: 'SizeExceedsPaddingSizeError'\n}\nexport class SizeExceedsPaddingSizeError extends BaseError {\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'hex' | 'bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} size (${size}) exceeds padding size (${targetSize}).`,\n      { name: 'SizeExceedsPaddingSizeError' },\n    )\n  }\n}\n\nexport type InvalidBytesLengthErrorType = InvalidBytesLengthError & {\n  name: 'InvalidBytesLengthError'\n}\nexport class InvalidBytesLengthError extends BaseError {\n  constructor({\n    size,\n    targetSize,\n    type,\n  }: {\n    size: number\n    targetSize: number\n    type: 'hex' | 'bytes'\n  }) {\n    super(\n      `${type.charAt(0).toUpperCase()}${type\n        .slice(1)\n        .toLowerCase()} is expected to be ${targetSize} ${type} long, but is ${size} ${type} long.`,\n      { name: 'InvalidBytesLengthError' },\n    )\n  }\n}\n", "import {\n  SliceOffsetOutOfBoundsError,\n  type SliceOffsetOutOfBoundsErrorType,\n} from '../../errors/data.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\nimport { type IsHexErrorType, isHex } from './isHex.js'\nimport { type SizeErrorType, size } from './size.js'\n\nexport type SliceReturnType<value extends ByteArray | Hex> = value extends Hex\n  ? Hex\n  : ByteArray\n\nexport type SliceErrorType =\n  | IsHexErrorType\n  | SliceBytesErrorType\n  | SliceHexErrorType\n  | ErrorType\n\n/**\n * @description Returns a section of the hex or byte array given a start/end bytes offset.\n *\n * @param value The hex or byte array to slice.\n * @param start The start offset (in bytes).\n * @param end The end offset (in bytes).\n */\nexport function slice<value extends ByteArray | Hex>(\n  value: value,\n  start?: number | undefined,\n  end?: number | undefined,\n  { strict }: { strict?: boolean | undefined } = {},\n): SliceReturnType<value> {\n  if (isHex(value, { strict: false }))\n    return sliceHex(value as Hex, start, end, {\n      strict,\n    }) as SliceReturnType<value>\n  return sliceBytes(value as ByteArray, start, end, {\n    strict,\n  }) as SliceReturnType<value>\n}\n\nexport type AssertStartOffsetErrorType =\n  | SliceOffsetOutOfBoundsErrorType\n  | SizeErrorType\n  | ErrorType\n\nfunction assertStartOffset(value: Hex | ByteArray, start?: number | undefined) {\n  if (typeof start === 'number' && start > 0 && start > size(value) - 1)\n    throw new SliceOffsetOutOfBoundsError({\n      offset: start,\n      position: 'start',\n      size: size(value),\n    })\n}\n\nexport type AssertEndOffsetErrorType =\n  | SliceOffsetOutOfBoundsErrorType\n  | SizeErrorType\n  | ErrorType\n\nfunction assertEndOffset(\n  value: Hex | ByteArray,\n  start?: number | undefined,\n  end?: number | undefined,\n) {\n  if (\n    typeof start === 'number' &&\n    typeof end === 'number' &&\n    size(value) !== end - start\n  ) {\n    throw new SliceOffsetOutOfBoundsError({\n      offset: end,\n      position: 'end',\n      size: size(value),\n    })\n  }\n}\n\nexport type SliceBytesErrorType =\n  | AssertStartOffsetErrorType\n  | AssertEndOffsetErrorType\n  | ErrorType\n\n/**\n * @description Returns a section of the byte array given a start/end bytes offset.\n *\n * @param value The byte array to slice.\n * @param start The start offset (in bytes).\n * @param end The end offset (in bytes).\n */\nexport function sliceBytes(\n  value_: ByteArray,\n  start?: number | undefined,\n  end?: number | undefined,\n  { strict }: { strict?: boolean | undefined } = {},\n): ByteArray {\n  assertStartOffset(value_, start)\n  const value = value_.slice(start, end)\n  if (strict) assertEndOffset(value, start, end)\n  return value\n}\n\nexport type SliceHexErrorType =\n  | AssertStartOffsetErrorType\n  | AssertEndOffsetErrorType\n  | ErrorType\n\n/**\n * @description Returns a section of the hex value given a start/end bytes offset.\n *\n * @param value The hex value to slice.\n * @param start The start offset (in bytes).\n * @param end The end offset (in bytes).\n */\nexport function sliceHex(\n  value_: Hex,\n  start?: number | undefined,\n  end?: number | undefined,\n  { strict }: { strict?: boolean | undefined } = {},\n): Hex {\n  assertStartOffset(value_, start)\n  const value = `0x${value_\n    .replace('0x', '')\n    .slice((start ?? 0) * 2, (end ?? value_.length) * 2)}` as const\n  if (strict) assertEndOffset(value, start, end)\n  return value\n}\n", "import {\n  SizeExceedsPaddingSizeError,\n  type SizeExceedsPaddingSizeErrorType,\n} from '../../errors/data.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\ntype PadOptions = {\n  dir?: 'left' | 'right' | undefined\n  size?: number | null | undefined\n}\nexport type PadReturnType<value extends ByteArray | Hex> = value extends Hex\n  ? Hex\n  : ByteArray\n\nexport type PadErrorType = PadHexErrorType | PadBytesErrorType | ErrorType\n\nexport function pad<value extends ByteArray | Hex>(\n  hexOrBytes: value,\n  { dir, size = 32 }: PadOptions = {},\n): PadReturnType<value> {\n  if (typeof hexOrBytes === 'string')\n    return padHex(hexOrBytes, { dir, size }) as PadReturnType<value>\n  return padBytes(hexOrBytes, { dir, size }) as PadReturnType<value>\n}\n\nexport type PadHexErrorType = SizeExceedsPaddingSizeErrorType | ErrorType\n\nexport function padHex(hex_: Hex, { dir, size = 32 }: PadOptions = {}) {\n  if (size === null) return hex_\n  const hex = hex_.replace('0x', '')\n  if (hex.length > size * 2)\n    throw new SizeExceedsPaddingSizeError({\n      size: Math.ceil(hex.length / 2),\n      targetSize: size,\n      type: 'hex',\n    })\n\n  return `0x${hex[dir === 'right' ? 'padEnd' : 'padStart'](\n    size * 2,\n    '0',\n  )}` as Hex\n}\n\nexport type PadBytesErrorType = SizeExceedsPaddingSizeErrorType | ErrorType\n\nexport function padBytes(\n  bytes: ByteArray,\n  { dir, size = 32 }: PadOptions = {},\n) {\n  if (size === null) return bytes\n  if (bytes.length > size)\n    throw new SizeExceedsPaddingSizeError({\n      size: bytes.length,\n      targetSize: size,\n      type: 'bytes',\n    })\n  const paddedBytes = new Uint8Array(size)\n  for (let i = 0; i < size; i++) {\n    const padEnd = dir === 'right'\n    paddedBytes[padEnd ? i : size - i - 1] =\n      bytes[padEnd ? i : bytes.length - i - 1]\n  }\n  return paddedBytes\n}\n", "import type { ByteArray, Hex } from '../types/misc.js'\n\nimport { BaseError } from './base.js'\n\nexport type IntegerOutOfRangeErrorType = IntegerOutOfRangeError & {\n  name: 'IntegerOutOfRangeError'\n}\nexport class IntegerOutOfRangeError extends BaseError {\n  constructor({\n    max,\n    min,\n    signed,\n    size,\n    value,\n  }: {\n    max?: string | undefined\n    min: string\n    signed?: boolean | undefined\n    size?: number | undefined\n    value: string\n  }) {\n    super(\n      `Number \"${value}\" is not in safe ${\n        size ? `${size * 8}-bit ${signed ? 'signed' : 'unsigned'} ` : ''\n      }integer range ${max ? `(${min} to ${max})` : `(above ${min})`}`,\n      { name: 'IntegerOutOfRangeError' },\n    )\n  }\n}\n\nexport type InvalidBytesBooleanErrorType = InvalidBytesBooleanError & {\n  name: 'InvalidBytesBooleanError'\n}\nexport class InvalidBytesBooleanError extends BaseError {\n  constructor(bytes: ByteArray) {\n    super(\n      `Bytes value \"${bytes}\" is not a valid boolean. The bytes array must contain a single byte of either a 0 or 1 value.`,\n      {\n        name: 'InvalidBytesBooleanError',\n      },\n    )\n  }\n}\n\nexport type InvalidHexBooleanErrorType = InvalidHexBooleanError & {\n  name: 'InvalidHexBooleanError'\n}\nexport class InvalidHexBooleanError extends BaseError {\n  constructor(hex: Hex) {\n    super(\n      `Hex value \"${hex}\" is not a valid boolean. The hex value must be \"0x0\" (false) or \"0x1\" (true).`,\n      { name: 'InvalidHexBooleanError' },\n    )\n  }\n}\n\nexport type InvalidHexValueErrorType = InvalidHexValueError & {\n  name: 'InvalidHexValueError'\n}\nexport class InvalidHexValueError extends BaseError {\n  constructor(value: Hex) {\n    super(\n      `Hex value \"${value}\" is an odd length (${value.length}). It must be an even length.`,\n      { name: 'InvalidHexValueError' },\n    )\n  }\n}\n\nexport type SizeOverflowErrorType = SizeOverflowError & {\n  name: 'SizeOverflowError'\n}\nexport class SizeOverflowError extends BaseError {\n  constructor({ givenSize, maxSize }: { givenSize: number; maxSize: number }) {\n    super(\n      `Size cannot exceed ${maxSize} bytes. Given size: ${givenSize} bytes.`,\n      { name: 'SizeOverflowError' },\n    )\n  }\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\ntype TrimOptions = {\n  dir?: 'left' | 'right' | undefined\n}\nexport type TrimReturnType<value extends ByteArray | Hex> = value extends Hex\n  ? Hex\n  : ByteArray\n\nexport type TrimErrorType = ErrorType\n\nexport function trim<value extends ByteArray | Hex>(\n  hexOrBytes: value,\n  { dir = 'left' }: TrimOptions = {},\n): TrimReturnType<value> {\n  let data: any =\n    typeof hexOrBytes === 'string' ? hexOrBytes.replace('0x', '') : hexOrBytes\n\n  let sliceLength = 0\n  for (let i = 0; i < data.length - 1; i++) {\n    if (data[dir === 'left' ? i : data.length - i - 1].toString() === '0')\n      sliceLength++\n    else break\n  }\n  data =\n    dir === 'left'\n      ? data.slice(sliceLength)\n      : data.slice(0, data.length - sliceLength)\n\n  if (typeof hexOrBytes === 'string') {\n    if (data.length === 1 && dir === 'right') data = `${data}0`\n    return `0x${\n      data.length % 2 === 1 ? `0${data}` : data\n    }` as TrimReturnType<value>\n  }\n  return data as TrimReturnType<value>\n}\n", "import {\n  InvalidHexBooleanError,\n  type InvalidHexBooleanErrorType,\n  SizeOverflowError,\n  type SizeOverflowErrorType,\n} from '../../errors/encoding.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type SizeErrorType, size as size_ } from '../data/size.js'\nimport { type TrimErrorType, trim } from '../data/trim.js'\n\nimport { type HexToBytesErrorType, hexToBytes } from './toBytes.js'\n\nexport type AssertSizeErrorType =\n  | SizeOverflowErrorType\n  | SizeErrorType\n  | ErrorType\n\nexport function assertSize(\n  hexOrBytes: Hex | ByteArray,\n  { size }: { size: number },\n): void {\n  if (size_(hexOrBytes) > size)\n    throw new SizeOverflowError({\n      givenSize: size_(hexOrBytes),\n      maxSize: size,\n    })\n}\n\nexport type FromHexParameters<\n  to extends 'string' | 'bigint' | 'number' | 'bytes' | 'boolean',\n> =\n  | to\n  | {\n      /** Size (in bytes) of the hex value. */\n      size?: number | undefined\n      /** Type to convert to. */\n      to: to\n    }\n\nexport type FromHexReturnType<to> = to extends 'string'\n  ? string\n  : to extends 'bigint'\n    ? bigint\n    : to extends 'number'\n      ? number\n      : to extends 'bytes'\n        ? ByteArray\n        : to extends 'boolean'\n          ? boolean\n          : never\n\nexport type FromHexErrorType =\n  | HexToNumberErrorType\n  | HexToBigIntErrorType\n  | HexToBoolErrorType\n  | HexToStringErrorType\n  | HexToBytesErrorType\n  | ErrorType\n\n/**\n * Decodes a hex string into a string, number, bigint, boolean, or byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex\n * - Example: https://viem.sh/docs/utilities/fromHex#usage\n *\n * @param hex Hex string to decode.\n * @param toOrOpts Type to convert to or options.\n * @returns Decoded value.\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x1a4', 'number')\n * // 420\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x48656c6c6f20576f726c6421', 'string')\n * // 'Hello world'\n *\n * @example\n * import { fromHex } from 'viem'\n * const data = fromHex('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *   size: 32,\n *   to: 'string'\n * })\n * // 'Hello world'\n */\nexport function fromHex<\n  to extends 'string' | 'bigint' | 'number' | 'bytes' | 'boolean',\n>(hex: Hex, toOrOpts: FromHexParameters<to>): FromHexReturnType<to> {\n  const opts = typeof toOrOpts === 'string' ? { to: toOrOpts } : toOrOpts\n  const to = opts.to\n\n  if (to === 'number') return hexToNumber(hex, opts) as FromHexReturnType<to>\n  if (to === 'bigint') return hexToBigInt(hex, opts) as FromHexReturnType<to>\n  if (to === 'string') return hexToString(hex, opts) as FromHexReturnType<to>\n  if (to === 'boolean') return hexToBool(hex, opts) as FromHexReturnType<to>\n  return hexToBytes(hex, opts) as FromHexReturnType<to>\n}\n\nexport type HexToBigIntOpts = {\n  /** Whether or not the number of a signed representation. */\n  signed?: boolean | undefined\n  /** Size (in bytes) of the hex value. */\n  size?: number | undefined\n}\n\nexport type HexToBigIntErrorType = AssertSizeErrorType | ErrorType\n\n/**\n * Decodes a hex value into a bigint.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextobigint\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns BigInt value.\n *\n * @example\n * import { hexToBigInt } from 'viem'\n * const data = hexToBigInt('0x1a4', { signed: true })\n * // 420n\n *\n * @example\n * import { hexToBigInt } from 'viem'\n * const data = hexToBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // 420n\n */\nexport function hexToBigInt(hex: Hex, opts: HexToBigIntOpts = {}): bigint {\n  const { signed } = opts\n\n  if (opts.size) assertSize(hex, { size: opts.size })\n\n  const value = BigInt(hex)\n  if (!signed) return value\n\n  const size = (hex.length - 2) / 2\n  const max = (1n << (BigInt(size) * 8n - 1n)) - 1n\n  if (value <= max) return value\n\n  return value - BigInt(`0x${'f'.padStart(size * 2, 'f')}`) - 1n\n}\n\nexport type HexToBoolOpts = {\n  /** Size (in bytes) of the hex value. */\n  size?: number | undefined\n}\n\nexport type HexToBoolErrorType =\n  | AssertSizeErrorType\n  | InvalidHexBooleanErrorType\n  | TrimErrorType\n  | ErrorType\n\n/**\n * Decodes a hex value into a boolean.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextobool\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns Boolean value.\n *\n * @example\n * import { hexToBool } from 'viem'\n * const data = hexToBool('0x01')\n * // true\n *\n * @example\n * import { hexToBool } from 'viem'\n * const data = hexToBool('0x0000000000000000000000000000000000000000000000000000000000000001', { size: 32 })\n * // true\n */\nexport function hexToBool(hex_: Hex, opts: HexToBoolOpts = {}): boolean {\n  let hex = hex_\n  if (opts.size) {\n    assertSize(hex, { size: opts.size })\n    hex = trim(hex)\n  }\n  if (trim(hex) === '0x00') return false\n  if (trim(hex) === '0x01') return true\n  throw new InvalidHexBooleanError(hex)\n}\n\nexport type HexToNumberOpts = HexToBigIntOpts\n\nexport type HexToNumberErrorType = HexToBigIntErrorType | ErrorType\n\n/**\n * Decodes a hex string into a number.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextonumber\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns Number value.\n *\n * @example\n * import { hexToNumber } from 'viem'\n * const data = hexToNumber('0x1a4')\n * // 420\n *\n * @example\n * import { hexToNumber } from 'viem'\n * const data = hexToBigInt('0x00000000000000000000000000000000000000000000000000000000000001a4', { size: 32 })\n * // 420\n */\nexport function hexToNumber(hex: Hex, opts: HexToNumberOpts = {}): number {\n  return Number(hexToBigInt(hex, opts))\n}\n\nexport type HexToStringOpts = {\n  /** Size (in bytes) of the hex value. */\n  size?: number | undefined\n}\n\nexport type HexToStringErrorType =\n  | AssertSizeErrorType\n  | HexToBytesErrorType\n  | TrimErrorType\n  | ErrorType\n\n/**\n * Decodes a hex value into a UTF-8 string.\n *\n * - Docs: https://viem.sh/docs/utilities/fromHex#hextostring\n *\n * @param hex Hex value to decode.\n * @param opts Options.\n * @returns String value.\n *\n * @example\n * import { hexToString } from 'viem'\n * const data = hexToString('0x48656c6c6f20576f726c6421')\n * // 'Hello world!'\n *\n * @example\n * import { hexToString } from 'viem'\n * const data = hexToString('0x48656c6c6f20576f726c64210000000000000000000000000000000000000000', {\n *  size: 32,\n * })\n * // 'Hello world'\n */\nexport function hexToString(hex: Hex, opts: HexToStringOpts = {}): string {\n  let bytes = hexToBytes(hex)\n  if (opts.size) {\n    assertSize(bytes, { size: opts.size })\n    bytes = trim(bytes, { dir: 'right' })\n  }\n  return new TextDecoder().decode(bytes)\n}\n", "import {\n  IntegerOutOfRangeError,\n  type IntegerOutOfRangeErrorType,\n} from '../../errors/encoding.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type PadErrorType, pad } from '../data/pad.js'\n\nimport { type AssertSizeErrorType, assertSize } from './fromHex.js'\n\nconst hexes = /*#__PURE__*/ Array.from({ length: 256 }, (_v, i) =>\n  i.toString(16).padStart(2, '0'),\n)\n\nexport type ToHexParameters = {\n  /** The size (in bytes) of the output hex value. */\n  size?: number | undefined\n}\n\nexport type ToHexErrorType =\n  | BoolToHexErrorType\n  | BytesToHexErrorType\n  | NumberToHexErrorType\n  | StringToHexErrorType\n  | ErrorType\n\n/**\n * Encodes a string, number, bigint, or ByteArray into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex\n * - Example: https://viem.sh/docs/utilities/toHex#usage\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex('Hello world')\n * // '0x48656c6c6f20776f726c6421'\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex(420)\n * // '0x1a4'\n *\n * @example\n * import { toHex } from 'viem'\n * const data = toHex('Hello world', { size: 32 })\n * // '0x48656c6c6f20776f726c64210000000000000000000000000000000000000000'\n */\nexport function toHex(\n  value: string | number | bigint | boolean | ByteArray,\n  opts: ToHexParameters = {},\n): Hex {\n  if (typeof value === 'number' || typeof value === 'bigint')\n    return numberToHex(value, opts)\n  if (typeof value === 'string') {\n    return stringToHex(value, opts)\n  }\n  if (typeof value === 'boolean') return boolToHex(value, opts)\n  return bytesToHex(value, opts)\n}\n\nexport type BoolToHexOpts = {\n  /** The size (in bytes) of the output hex value. */\n  size?: number | undefined\n}\n\nexport type BoolToHexErrorType = AssertSizeErrorType | PadErrorType | ErrorType\n\n/**\n * Encodes a boolean into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#booltohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(true)\n * // '0x1'\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(false)\n * // '0x0'\n *\n * @example\n * import { boolToHex } from 'viem'\n * const data = boolToHex(true, { size: 32 })\n * // '0x0000000000000000000000000000000000000000000000000000000000000001'\n */\nexport function boolToHex(value: boolean, opts: BoolToHexOpts = {}): Hex {\n  const hex: Hex = `0x${Number(value)}`\n  if (typeof opts.size === 'number') {\n    assertSize(hex, { size: opts.size })\n    return pad(hex, { size: opts.size })\n  }\n  return hex\n}\n\nexport type BytesToHexOpts = {\n  /** The size (in bytes) of the output hex value. */\n  size?: number | undefined\n}\n\nexport type BytesToHexErrorType = AssertSizeErrorType | PadErrorType | ErrorType\n\n/**\n * Encodes a bytes array into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#bytestohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { bytesToHex } from 'viem'\n * const data = bytesToHex(Uint8Array.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n * // '0x48656c6c6f20576f726c6421'\n *\n * @example\n * import { bytesToHex } from 'viem'\n * const data = bytesToHex(Uint8Array.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]), { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n */\nexport function bytesToHex(value: ByteArray, opts: BytesToHexOpts = {}): Hex {\n  let string = ''\n  for (let i = 0; i < value.length; i++) {\n    string += hexes[value[i]]\n  }\n  const hex = `0x${string}` as const\n\n  if (typeof opts.size === 'number') {\n    assertSize(hex, { size: opts.size })\n    return pad(hex, { dir: 'right', size: opts.size })\n  }\n  return hex\n}\n\nexport type NumberToHexOpts =\n  | {\n      /** Whether or not the number of a signed representation. */\n      signed?: boolean | undefined\n      /** The size (in bytes) of the output hex value. */\n      size: number\n    }\n  | {\n      signed?: undefined\n      /** The size (in bytes) of the output hex value. */\n      size?: number | undefined\n    }\n\nexport type NumberToHexErrorType =\n  | IntegerOutOfRangeErrorType\n  | PadErrorType\n  | ErrorType\n\n/**\n * Encodes a number or bigint into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#numbertohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { numberToHex } from 'viem'\n * const data = numberToHex(420)\n * // '0x1a4'\n *\n * @example\n * import { numberToHex } from 'viem'\n * const data = numberToHex(420, { size: 32 })\n * // '0x00000000000000000000000000000000000000000000000000000000000001a4'\n */\nexport function numberToHex(\n  value_: number | bigint,\n  opts: NumberToHexOpts = {},\n): Hex {\n  const { signed, size } = opts\n\n  const value = BigInt(value_)\n\n  let maxValue: bigint | number | undefined\n  if (size) {\n    if (signed) maxValue = (1n << (BigInt(size) * 8n - 1n)) - 1n\n    else maxValue = 2n ** (BigInt(size) * 8n) - 1n\n  } else if (typeof value_ === 'number') {\n    maxValue = BigInt(Number.MAX_SAFE_INTEGER)\n  }\n\n  const minValue = typeof maxValue === 'bigint' && signed ? -maxValue - 1n : 0\n\n  if ((maxValue && value > maxValue) || value < minValue) {\n    const suffix = typeof value_ === 'bigint' ? 'n' : ''\n    throw new IntegerOutOfRangeError({\n      max: maxValue ? `${maxValue}${suffix}` : undefined,\n      min: `${minValue}${suffix}`,\n      signed,\n      size,\n      value: `${value_}${suffix}`,\n    })\n  }\n\n  const hex = `0x${(\n    signed && value < 0 ? (1n << BigInt(size * 8)) + BigInt(value) : value\n  ).toString(16)}` as Hex\n  if (size) return pad(hex, { size }) as Hex\n  return hex\n}\n\nexport type StringToHexOpts = {\n  /** The size (in bytes) of the output hex value. */\n  size?: number | undefined\n}\n\nexport type StringToHexErrorType = BytesToHexErrorType | ErrorType\n\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\n/**\n * Encodes a UTF-8 string into a hex string\n *\n * - Docs: https://viem.sh/docs/utilities/toHex#stringtohex\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Hex value.\n *\n * @example\n * import { stringToHex } from 'viem'\n * const data = stringToHex('Hello World!')\n * // '0x48656c6c6f20576f726c6421'\n *\n * @example\n * import { stringToHex } from 'viem'\n * const data = stringToHex('Hello World!', { size: 32 })\n * // '0x48656c6c6f20576f726c64210000000000000000000000000000000000000000'\n */\nexport function stringToHex(value_: string, opts: StringToHexOpts = {}): Hex {\n  const value = encoder.encode(value_)\n  return bytesToHex(value, opts)\n}\n", "import { BaseError } from '../../errors/base.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type IsHexErrorType, isHex } from '../data/isHex.js'\nimport { type PadErrorType, pad } from '../data/pad.js'\n\nimport { type AssertSizeErrorType, assertSize } from './fromHex.js'\nimport {\n  type NumberToHexErrorType,\n  type NumberToHexOpts,\n  numberToHex,\n} from './toHex.js'\n\nconst encoder = /*#__PURE__*/ new TextEncoder()\n\nexport type ToBytesParameters = {\n  /** Size of the output bytes. */\n  size?: number | undefined\n}\n\nexport type ToBytesErrorType =\n  | NumberToBytesErrorType\n  | BoolToBytesErrorType\n  | HexToBytesErrorType\n  | StringToBytesErrorType\n  | IsHexErrorType\n  | ErrorType\n\n/**\n * Encodes a UTF-8 string, hex value, bigint, number or boolean to a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes\n * - Example: https://viem.sh/docs/utilities/toBytes#usage\n *\n * @param value Value to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes('Hello world')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes(420)\n * // Uint8Array([1, 164])\n *\n * @example\n * import { toBytes } from 'viem'\n * const data = toBytes(420, { size: 4 })\n * // Uint8Array([0, 0, 1, 164])\n */\nexport function toBytes(\n  value: string | bigint | number | boolean | Hex,\n  opts: ToBytesParameters = {},\n): ByteArray {\n  if (typeof value === 'number' || typeof value === 'bigint')\n    return numberToBytes(value, opts)\n  if (typeof value === 'boolean') return boolToBytes(value, opts)\n  if (isHex(value)) return hexToBytes(value, opts)\n  return stringToBytes(value, opts)\n}\n\nexport type BoolToBytesOpts = {\n  /** Size of the output bytes. */\n  size?: number | undefined\n}\n\nexport type BoolToBytesErrorType =\n  | AssertSizeErrorType\n  | PadErrorType\n  | ErrorType\n\n/**\n * Encodes a boolean into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#booltobytes\n *\n * @param value Boolean value to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { boolToBytes } from 'viem'\n * const data = boolToBytes(true)\n * // Uint8Array([1])\n *\n * @example\n * import { boolToBytes } from 'viem'\n * const data = boolToBytes(true, { size: 32 })\n * // Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])\n */\nexport function boolToBytes(value: boolean, opts: BoolToBytesOpts = {}) {\n  const bytes = new Uint8Array(1)\n  bytes[0] = Number(value)\n  if (typeof opts.size === 'number') {\n    assertSize(bytes, { size: opts.size })\n    return pad(bytes, { size: opts.size })\n  }\n  return bytes\n}\n\n// We use very optimized technique to convert hex string to byte array\nconst charCodeMap = {\n  zero: 48,\n  nine: 57,\n  A: 65,\n  F: 70,\n  a: 97,\n  f: 102,\n} as const\n\nfunction charCodeToBase16(char: number) {\n  if (char >= charCodeMap.zero && char <= charCodeMap.nine)\n    return char - charCodeMap.zero\n  if (char >= charCodeMap.A && char <= charCodeMap.F)\n    return char - (charCodeMap.A - 10)\n  if (char >= charCodeMap.a && char <= charCodeMap.f)\n    return char - (charCodeMap.a - 10)\n  return undefined\n}\n\nexport type HexToBytesOpts = {\n  /** Size of the output bytes. */\n  size?: number | undefined\n}\n\nexport type HexToBytesErrorType = AssertSizeErrorType | PadErrorType | ErrorType\n\n/**\n * Encodes a hex string into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#hextobytes\n *\n * @param hex Hex string to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { hexToBytes } from 'viem'\n * const data = hexToBytes('0x48656c6c6f20776f726c6421')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])\n *\n * @example\n * import { hexToBytes } from 'viem'\n * const data = hexToBytes('0x48656c6c6f20776f726c6421', { size: 32 })\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n */\nexport function hexToBytes(hex_: Hex, opts: HexToBytesOpts = {}): ByteArray {\n  let hex = hex_\n  if (opts.size) {\n    assertSize(hex, { size: opts.size })\n    hex = pad(hex, { dir: 'right', size: opts.size })\n  }\n\n  let hexString = hex.slice(2) as string\n  if (hexString.length % 2) hexString = `0${hexString}`\n\n  const length = hexString.length / 2\n  const bytes = new Uint8Array(length)\n  for (let index = 0, j = 0; index < length; index++) {\n    const nibbleLeft = charCodeToBase16(hexString.charCodeAt(j++))\n    const nibbleRight = charCodeToBase16(hexString.charCodeAt(j++))\n    if (nibbleLeft === undefined || nibbleRight === undefined) {\n      throw new BaseError(\n        `Invalid byte sequence (\"${hexString[j - 2]}${\n          hexString[j - 1]\n        }\" in \"${hexString}\").`,\n      )\n    }\n    bytes[index] = nibbleLeft * 16 + nibbleRight\n  }\n  return bytes\n}\n\nexport type NumberToBytesErrorType =\n  | NumberToHexErrorType\n  | HexToBytesErrorType\n  | ErrorType\n\n/**\n * Encodes a number into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#numbertobytes\n *\n * @param value Number to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { numberToBytes } from 'viem'\n * const data = numberToBytes(420)\n * // Uint8Array([1, 164])\n *\n * @example\n * import { numberToBytes } from 'viem'\n * const data = numberToBytes(420, { size: 4 })\n * // Uint8Array([0, 0, 1, 164])\n */\nexport function numberToBytes(\n  value: bigint | number,\n  opts?: NumberToHexOpts | undefined,\n) {\n  const hex = numberToHex(value, opts)\n  return hexToBytes(hex)\n}\n\nexport type StringToBytesOpts = {\n  /** Size of the output bytes. */\n  size?: number | undefined\n}\n\nexport type StringToBytesErrorType =\n  | AssertSizeErrorType\n  | PadErrorType\n  | ErrorType\n\n/**\n * Encodes a UTF-8 string into a byte array.\n *\n * - Docs: https://viem.sh/docs/utilities/toBytes#stringtobytes\n *\n * @param value String to encode.\n * @param opts Options.\n * @returns Byte array value.\n *\n * @example\n * import { stringToBytes } from 'viem'\n * const data = stringToBytes('Hello world!')\n * // Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])\n *\n * @example\n * import { stringToBytes } from 'viem'\n * const data = stringToBytes('Hello world!', { size: 32 })\n * // Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])\n */\nexport function stringToBytes(\n  value: string,\n  opts: StringToBytesOpts = {},\n): ByteArray {\n  const bytes = encoder.encode(value)\n  if (typeof opts.size === 'number') {\n    assertSize(bytes, { size: opts.size })\n    return pad(bytes, { dir: 'right', size: opts.size })\n  }\n  return bytes\n}\n", "/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\nfunction fromBig(\n  n: bigint,\n  le = false\n): {\n  h: number;\n  l: number;\n} {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false): Uint32Array[] {\n  let Ah = new Uint32Array(lst.length);\n  let Al = new Uint32Array(lst.length);\n  for (let i = 0; i < lst.length; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number): bigint => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number): number => h >>> s;\nconst shrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number): number => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number): number => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number): number => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number): number => l;\nconst rotr32L = (h: number, _l: number): number => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number): number => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number): number => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number): number => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number): number => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(\n  Ah: number,\n  Al: number,\n  Bh: number,\n  Bl: number\n): {\n  h: number;\n  l: number;\n} {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number): number => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number): number =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number): number =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number): number =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\n// prettier-ignore\nconst u64: { fromBig: typeof fromBig; split: typeof split; toBig: (h: number, l: number) => bigint; shrSH: (h: number, _l: number, s: number) => number; shrSL: (h: number, l: number, s: number) => number; rotrSH: (h: number, l: number, s: number) => number; rotrSL: (h: number, l: number, s: number) => number; rotrBH: (h: number, l: number, s: number) => number; rotrBL: (h: number, l: number, s: number) => number; rotr32H: (_h: number, l: number) => number; rotr32L: (h: number, _l: number) => number; rotlSH: (h: number, l: number, s: number) => number; rotlSL: (h: number, l: number, s: number) => number; rotlBH: (h: number, l: number, s: number) => number; rotlBL: (h: number, l: number, s: number) => number; add: typeof add; add3L: (Al: number, Bl: number, Cl: number) => number; add3H: (low: number, Ah: number, Bh: number, Ch: number) => number; add4L: (Al: number, Bl: number, Cl: number, Dl: number) => number; add4H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number) => number; add5H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) => number; add5L: (Al: number, Bl: number, Cl: number, Dl: number, El: number) => number; } = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n", "/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHA<PERSON>, k12, and others.\n * @module\n */\nimport { abytes, aexists, anumber, aoutput } from './_assert.js';\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.js';\nimport {\n  byteSwap32,\n  Hash,\n  isLE,\n  toBytes,\n  u32,\n  wrapConstructor,\n  wrapXOFConstructorWithOpts,\n  type CHash,\n  type CHashXO,\n  type HashXOF,\n  type Input,\n} from './utils.js';\n\n// Various per round constants calculations\nconst SHA3_PI: number[] = [];\nconst SHA3_ROTL: number[] = [];\nconst _SHA3_IOTA: bigint[] = [];\nconst _0n = /* @__PURE__ */ BigInt(0);\nconst _1n = /* @__PURE__ */ BigInt(1);\nconst _2n = /* @__PURE__ */ BigInt(2);\nconst _7n = /* @__PURE__ */ BigInt(7);\nconst _256n = /* @__PURE__ */ BigInt(256);\nconst _0x71n = /* @__PURE__ */ BigInt(0x71);\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n    if (R & _2n) t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n  }\n  _SHA3_IOTA.push(t);\n}\nconst [SHA3_IOTA_H, SHA3_IOTA_L] = /* @__PURE__ */ split(_SHA3_IOTA, true);\n\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h: number, l: number, s: number) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h: number, l: number, s: number) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nexport function keccakP(s: Uint32Array, rounds: number = 24): void {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  B.fill(0);\n}\n\n/** Keccak sponge function. */\nexport class Keccak extends Hash<Keccak> implements HashXOF<Keccak> {\n  protected state: Uint8Array;\n  protected pos = 0;\n  protected posOut = 0;\n  protected finished = false;\n  protected state32: Uint32Array;\n  protected destroyed = false;\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(\n    public blockLen: number,\n    public suffix: number,\n    public outputLen: number,\n    protected enableXOF = false,\n    protected rounds: number = 24\n  ) {\n    super();\n    // Can be passed from user as dkLen\n    anumber(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    // 0 < blockLen < 200\n    if (0 >= this.blockLen || this.blockLen >= 200)\n      throw new Error('Sha3 supports only keccak-f1600 function');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  protected keccak(): void {\n    if (!isLE) byteSwap32(this.state32);\n    keccakP(this.state32, this.rounds);\n    if (!isLE) byteSwap32(this.state32);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data: Input): this {\n    aexists(this);\n    const { blockLen, state } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  protected finish(): void {\n    if (this.finished) return;\n    this.finished = true;\n    const { state, suffix, pos, blockLen } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  protected writeInto(out: Uint8Array): Uint8Array {\n    aexists(this, false);\n    abytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const { blockLen } = this;\n    for (let pos = 0, len = out.length; pos < len; ) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out: Uint8Array): Uint8Array {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes: number): Uint8Array {\n    anumber(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out: Uint8Array): Uint8Array {\n    aoutput(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest(): Uint8Array {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy(): void {\n    this.destroyed = true;\n    this.state.fill(0);\n  }\n  _cloneInto(to?: Keccak): Keccak {\n    const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n    to ||= new Keccak(blockLen, suffix, outputLen, enableXOF, rounds);\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\n\nconst gen = (suffix: number, blockLen: number, outputLen: number) =>\n  wrapConstructor(() => new Keccak(blockLen, suffix, outputLen));\n\n/** SHA3-224 hash function. */\nexport const sha3_224: CHash = /* @__PURE__ */ gen(0x06, 144, 224 / 8);\n/** SHA3-256 hash function. Different from keccak-256. */\nexport const sha3_256: CHash = /* @__PURE__ */ gen(0x06, 136, 256 / 8);\n/** SHA3-384 hash function. */\nexport const sha3_384: CHash = /* @__PURE__ */ gen(0x06, 104, 384 / 8);\n/** SHA3-512 hash function. */\nexport const sha3_512: CHash = /* @__PURE__ */ gen(0x06, 72, 512 / 8);\n\n/** keccak-224 hash function. */\nexport const keccak_224: CHash = /* @__PURE__ */ gen(0x01, 144, 224 / 8);\n/** keccak-256 hash function. Different from SHA3-256. */\nexport const keccak_256: CHash = /* @__PURE__ */ gen(0x01, 136, 256 / 8);\n/** keccak-384 hash function. */\nexport const keccak_384: CHash = /* @__PURE__ */ gen(0x01, 104, 384 / 8);\n/** keccak-512 hash function. */\nexport const keccak_512: CHash = /* @__PURE__ */ gen(0x01, 72, 512 / 8);\n\nexport type ShakeOpts = { dkLen?: number };\n\nconst genShake = (suffix: number, blockLen: number, outputLen: number) =>\n  wrapXOFConstructorWithOpts<HashXOF<Keccak>, ShakeOpts>(\n    (opts: ShakeOpts = {}) =>\n      new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true)\n  );\n\n/** SHAKE128 XOF with 128-bit security. */\nexport const shake128: CHashXO = /* @__PURE__ */ genShake(0x1f, 168, 128 / 8);\n/** SHAKE256 XOF with 256-bit security. */\nexport const shake256: CHashXO = /* @__PURE__ */ genShake(0x1f, 136, 256 / 8);\n", "import { keccak_256 } from '@noble/hashes/sha3'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type IsHexErrorType, isHex } from '../data/isHex.js'\nimport { type ToBytesErrorType, toBytes } from '../encoding/toBytes.js'\nimport { type ToHexErrorType, toHex } from '../encoding/toHex.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type Keccak256Hash<to extends To> =\n  | (to extends 'bytes' ? ByteArray : never)\n  | (to extends 'hex' ? Hex : never)\n\nexport type Keccak256ErrorType =\n  | IsHexErrorType\n  | ToBytesErrorType\n  | ToHexErrorType\n  | ErrorType\n\nexport function keccak256<to extends To = 'hex'>(\n  value: Hex | ByteArray,\n  to_?: to | undefined,\n): Keccak256Hash<to> {\n  const to = to_ || 'hex'\n  const bytes = keccak_256(\n    isHex(value, { strict: false }) ? toBytes(value) : value,\n  )\n  if (to === 'bytes') return bytes as Keccak256Hash<to>\n  return toHex(bytes) as Keccak256Hash<to>\n}\n", "import { type ToBytesErrorType, toBytes } from '../encoding/toBytes.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport { type Keccak256ErrorType, keccak256 } from './keccak256.js'\n\nconst hash = (value: string) => keccak256(toBytes(value))\n\nexport type HashSignatureErrorType =\n  | Keccak256ErrorType\n  | ToBytesErrorType\n  | ErrorType\n\nexport function hashSignature(sig: string) {\n  return hash(sig)\n}\n", "import { BaseError } from '../../errors/base.js'\nimport type { ErrorType } from '../../errors/utils.js'\n\ntype NormalizeSignatureParameters = string\ntype NormalizeSignatureReturnType = string\nexport type NormalizeSignatureErrorType = ErrorType\n\nexport function normalizeSignature(\n  signature: NormalizeSignatureParameters,\n): NormalizeSignatureReturnType {\n  let active = true\n  let current = ''\n  let level = 0\n  let result = ''\n  let valid = false\n\n  for (let i = 0; i < signature.length; i++) {\n    const char = signature[i]\n\n    // If the character is a separator, we want to reactivate.\n    if (['(', ')', ','].includes(char)) active = true\n\n    // If the character is a \"level\" token, we want to increment/decrement.\n    if (char === '(') level++\n    if (char === ')') level--\n\n    // If we aren't active, we don't want to mutate the result.\n    if (!active) continue\n\n    // If level === 0, we are at the definition level.\n    if (level === 0) {\n      if (char === ' ' && ['event', 'function', ''].includes(result))\n        result = ''\n      else {\n        result += char\n\n        // If we are at the end of the definition, we must be finished.\n        if (char === ')') {\n          valid = true\n          break\n        }\n      }\n\n      continue\n    }\n\n    // Ignore spaces\n    if (char === ' ') {\n      // If the previous character is a separator, and the current section isn't empty, we want to deactivate.\n      if (signature[i - 1] !== ',' && current !== ',' && current !== ',(') {\n        current = ''\n        active = false\n      }\n      continue\n    }\n\n    result += char\n    current += char\n  }\n\n  if (!valid) throw new BaseError('Unable to normalize signature.')\n\n  return result\n}\n", "import { type AbiEvent, type AbiFunction, formatAbiItem } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type NormalizeSignatureErrorType,\n  normalizeSignature,\n} from './normalizeSignature.js'\n\nexport type ToSignatureErrorType = NormalizeSignatureErrorType | ErrorType\n\n/**\n * Returns the signature for a given function or event definition.\n *\n * @example\n * const signature = toSignature('function ownerOf(uint256 tokenId)')\n * // 'ownerOf(uint256)'\n *\n * @example\n * const signature_3 = toSignature({\n *   name: 'ownerOf',\n *   type: 'function',\n *   inputs: [{ name: 'tokenId', type: 'uint256' }],\n *   outputs: [],\n *   stateMutability: 'view',\n * })\n * // 'ownerOf(uint256)'\n */\nexport const toSignature = (def: string | AbiFunction | AbiEvent) => {\n  const def_ = (() => {\n    if (typeof def === 'string') return def\n    return formatAbiItem(def)\n  })()\n  return normalizeSignature(def_)\n}\n", "import type { AbiEvent, AbiFunction } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport { type HashSignatureErrorType, hashSignature } from './hashSignature.js'\nimport { type ToSignatureErrorType, toSignature } from './toSignature.js'\n\nexport type ToSignatureHashErrorType =\n  | HashSignatureErrorType\n  | ToSignatureErrorType\n  | ErrorType\n\n/**\n * Returns the hash (of the function/event signature) for a given event or function definition.\n */\nexport function toSignatureHash(fn: string | AbiFunction | AbiEvent) {\n  return hashSignature(toSignature(fn))\n}\n", "import type { AbiFunction } from 'abitype'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport { type SliceErrorType, slice } from '../data/slice.js'\nimport {\n  type ToSignatureHashErrorType,\n  toSignatureHash,\n} from './toSignatureHash.js'\n\nexport type ToFunctionSelectorErrorType =\n  | ToSignatureHashErrorType\n  | SliceErrorType\n  | ErrorType\n\n/**\n * Returns the function selector for a given function definition.\n *\n * @example\n * const selector = toFunctionSelector('function ownerOf(uint256 tokenId)')\n * // 0x6352211e\n */\nexport const toFunctionSelector = (fn: string | AbiFunction) =>\n  slice(toSignatureHash(fn), 0, 4)\n", "import { BaseError } from './base.js'\n\nexport type InvalidAddressErrorType = InvalidAddressError & {\n  name: 'InvalidAddressError'\n}\nexport class InvalidAddressError extends BaseError {\n  constructor({ address }: { address: string }) {\n    super(`Address \"${address}\" is invalid.`, {\n      metaMessages: [\n        '- Address must be a hex value of 20 bytes (40 hex characters).',\n        '- Address must match its checksum counterpart.',\n      ],\n      name: 'InvalidAddressError',\n    })\n  }\n}\n", "/**\n * Map with a LRU (Least recently used) policy.\n *\n * @link https://en.wikipedia.org/wiki/Cache_replacement_policies#LRU\n */\nexport class LruMap<value = unknown> extends Map<string, value> {\n  maxSize: number\n\n  constructor(size: number) {\n    super()\n    this.maxSize = size\n  }\n\n  override get(key: string) {\n    const value = super.get(key)\n\n    if (super.has(key) && value !== undefined) {\n      this.delete(key)\n      super.set(key, value)\n    }\n\n    return value\n  }\n\n  override set(key: string, value: value) {\n    super.set(key, value)\n    if (this.maxSize && this.size > this.maxSize) {\n      const firstKey = this.keys().next().value\n      if (firstKey) this.delete(firstKey)\n    }\n    return this\n  }\n}\n", "import type { Address } from 'abitype'\nimport type { ErrorType } from '../../errors/utils.js'\nimport { LruMap } from '../lru.js'\nimport { checksumAddress } from './getAddress.js'\n\nconst addressRegex = /^0x[a-fA-F0-9]{40}$/\n\n/** @internal */\nexport const isAddressCache = /*#__PURE__*/ new LruMap<boolean>(8192)\n\nexport type IsAddressOptions = {\n  /**\n   * Enables strict mode. Whether or not to compare the address against its checksum.\n   *\n   * @default true\n   */\n  strict?: boolean | undefined\n}\n\nexport type IsAddressErrorType = ErrorType\n\nexport function isAddress(\n  address: string,\n  options?: IsAddressOptions | undefined,\n): address is Address {\n  const { strict = true } = options ?? {}\n  const cacheKey = `${address}.${strict}`\n\n  if (isAddressCache.has(cacheKey)) return isAddressCache.get(cacheKey)!\n\n  const result = (() => {\n    if (!addressRegex.test(address)) return false\n    if (address.toLowerCase() === address) return true\n    if (strict) return checksumAddress(address as Address) === address\n    return true\n  })()\n  isAddressCache.set(cacheKey, result)\n  return result\n}\n", "import type { Address } from 'abitype'\n\nimport { InvalidAddressError } from '../../errors/address.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type StringToBytesErrorType,\n  stringToBytes,\n} from '../encoding/toBytes.js'\nimport { type Keccak256ErrorType, keccak256 } from '../hash/keccak256.js'\nimport { LruMap } from '../lru.js'\nimport { type IsAddressErrorType, isAddress } from './isAddress.js'\n\nconst checksumAddressCache = /*#__PURE__*/ new LruMap<Address>(8192)\n\nexport type ChecksumAddressErrorType =\n  | Keccak256ErrorType\n  | StringToBytesErrorType\n  | ErrorType\n\nexport function checksumAddress(\n  address_: Address,\n  /**\n   * Warning: EIP-1191 checksum addresses are generally not backwards compatible with the\n   * wider Ethereum ecosystem, meaning it will break when validated against an application/tool\n   * that relies on EIP-55 checksum encoding (checksum without chainId).\n   *\n   * It is highly recommended to not use this feature unless you\n   * know what you are doing.\n   *\n   * See more: https://github.com/ethereum/EIPs/issues/1121\n   */\n  chainId?: number | undefined,\n): Address {\n  if (checksumAddressCache.has(`${address_}.${chainId}`))\n    return checksumAddressCache.get(`${address_}.${chainId}`)!\n\n  const hexAddress = chainId\n    ? `${chainId}${address_.toLowerCase()}`\n    : address_.substring(2).toLowerCase()\n  const hash = keccak256(stringToBytes(hexAddress), 'bytes')\n\n  const address = (\n    chainId ? hexAddress.substring(`${chainId}0x`.length) : hexAddress\n  ).split('')\n  for (let i = 0; i < 40; i += 2) {\n    if (hash[i >> 1] >> 4 >= 8 && address[i]) {\n      address[i] = address[i].toUpperCase()\n    }\n    if ((hash[i >> 1] & 0x0f) >= 8 && address[i + 1]) {\n      address[i + 1] = address[i + 1].toUpperCase()\n    }\n  }\n\n  const result = `0x${address.join('')}` as const\n  checksumAddressCache.set(`${address_}.${chainId}`, result)\n  return result\n}\n\nexport type GetAddressErrorType =\n  | ChecksumAddressErrorType\n  | IsAddressErrorType\n  | ErrorType\n\nexport function getAddress(\n  address: string,\n  /**\n   * Warning: EIP-1191 checksum addresses are generally not backwards compatible with the\n   * wider Ethereum ecosystem, meaning it will break when validated against an application/tool\n   * that relies on EIP-55 checksum encoding (checksum without chainId).\n   *\n   * It is highly recommended to not use this feature unless you\n   * know what you are doing.\n   *\n   * See more: https://github.com/ethereum/EIPs/issues/1121\n   */\n  chainId?: number,\n): Address {\n  if (!isAddress(address, { strict: false }))\n    throw new InvalidAddressError({ address })\n  return checksumAddress(address, chainId)\n}\n", "import { BaseError } from './base.js'\n\nexport type NegativeOffsetErrorType = NegativeOffsetError & {\n  name: 'NegativeOffsetError'\n}\nexport class NegativeOffsetError extends BaseError {\n  constructor({ offset }: { offset: number }) {\n    super(`Offset \\`${offset}\\` cannot be negative.`, {\n      name: 'NegativeOffsetError',\n    })\n  }\n}\n\nexport type PositionOutOfBoundsErrorType = PositionOutOfBoundsError & {\n  name: 'PositionOutOfBoundsError'\n}\nexport class PositionOutOfBoundsError extends BaseError {\n  constructor({ length, position }: { length: number; position: number }) {\n    super(\n      `Position \\`${position}\\` is out of bounds (\\`0 < position < ${length}\\`).`,\n      { name: 'PositionOutOfBoundsError' },\n    )\n  }\n}\n\nexport type RecursiveReadLimitExceededErrorType =\n  RecursiveReadLimitExceededError & {\n    name: 'RecursiveReadLimitExceededError'\n  }\nexport class RecursiveReadLimitExceededError extends BaseError {\n  constructor({ count, limit }: { count: number; limit: number }) {\n    super(\n      `Recursive read limit of \\`${limit}\\` exceeded (recursive read count: \\`${count}\\`).`,\n      { name: 'RecursiveReadLimitExceededError' },\n    )\n  }\n}\n", "import {\n  NegativeOffsetError,\n  type NegativeOffsetErrorType,\n  PositionOutOfBoundsError,\n  type PositionOutOfBoundsErrorType,\n  RecursiveReadLimitExceededError,\n  type RecursiveReadLimitExceededErrorType,\n} from '../errors/cursor.js'\nimport type { ErrorType } from '../errors/utils.js'\nimport type { ByteArray } from '../types/misc.js'\n\nexport type Cursor = {\n  bytes: ByteArray\n  dataView: DataView\n  position: number\n  positionReadCount: Map<number, number>\n  recursiveReadCount: number\n  recursiveReadLimit: number\n  remaining: number\n  assertReadLimit(position?: number): void\n  assertPosition(position: number): void\n  decrementPosition(offset: number): void\n  getReadCount(position?: number): number\n  incrementPosition(offset: number): void\n  inspectByte(position?: number): ByteArray[number]\n  inspectBytes(length: number, position?: number): ByteArray\n  inspectUint8(position?: number): number\n  inspectUint16(position?: number): number\n  inspectUint24(position?: number): number\n  inspectUint32(position?: number): number\n  pushByte(byte: ByteArray[number]): void\n  pushBytes(bytes: ByteArray): void\n  pushUint8(value: number): void\n  pushUint16(value: number): void\n  pushUint24(value: number): void\n  pushUint32(value: number): void\n  readByte(): ByteArray[number]\n  readBytes(length: number, size?: number): ByteArray\n  readUint8(): number\n  readUint16(): number\n  readUint24(): number\n  readUint32(): number\n  setPosition(position: number): () => void\n  _touch(): void\n}\n\ntype CursorErrorType =\n  | CursorAssertPositionErrorType\n  | CursorDecrementPositionErrorType\n  | CursorIncrementPositionErrorType\n  | ErrorType\n\ntype CursorAssertPositionErrorType = PositionOutOfBoundsErrorType | ErrorType\n\ntype CursorDecrementPositionErrorType = NegativeOffsetErrorType | ErrorType\n\ntype CursorIncrementPositionErrorType = NegativeOffsetErrorType | ErrorType\n\ntype StaticCursorErrorType =\n  | NegativeOffsetErrorType\n  | RecursiveReadLimitExceededErrorType\n\nconst staticCursor: Cursor = {\n  bytes: new Uint8Array(),\n  dataView: new DataView(new ArrayBuffer(0)),\n  position: 0,\n  positionReadCount: new Map(),\n  recursiveReadCount: 0,\n  recursiveReadLimit: Number.POSITIVE_INFINITY,\n  assertReadLimit() {\n    if (this.recursiveReadCount >= this.recursiveReadLimit)\n      throw new RecursiveReadLimitExceededError({\n        count: this.recursiveReadCount + 1,\n        limit: this.recursiveReadLimit,\n      })\n  },\n  assertPosition(position) {\n    if (position < 0 || position > this.bytes.length - 1)\n      throw new PositionOutOfBoundsError({\n        length: this.bytes.length,\n        position,\n      })\n  },\n  decrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position - offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  getReadCount(position) {\n    return this.positionReadCount.get(position || this.position) || 0\n  },\n  incrementPosition(offset) {\n    if (offset < 0) throw new NegativeOffsetError({ offset })\n    const position = this.position + offset\n    this.assertPosition(position)\n    this.position = position\n  },\n  inspectByte(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]\n  },\n  inspectBytes(length, position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + length - 1)\n    return this.bytes.subarray(position, position + length)\n  },\n  inspectUint8(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position)\n    return this.bytes[position]\n  },\n  inspectUint16(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 1)\n    return this.dataView.getUint16(position)\n  },\n  inspectUint24(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 2)\n    return (\n      (this.dataView.getUint16(position) << 8) +\n      this.dataView.getUint8(position + 2)\n    )\n  },\n  inspectUint32(position_) {\n    const position = position_ ?? this.position\n    this.assertPosition(position + 3)\n    return this.dataView.getUint32(position)\n  },\n  pushByte(byte: ByteArray[number]) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = byte\n    this.position++\n  },\n  pushBytes(bytes: ByteArray) {\n    this.assertPosition(this.position + bytes.length - 1)\n    this.bytes.set(bytes, this.position)\n    this.position += bytes.length\n  },\n  pushUint8(value: number) {\n    this.assertPosition(this.position)\n    this.bytes[this.position] = value\n    this.position++\n  },\n  pushUint16(value: number) {\n    this.assertPosition(this.position + 1)\n    this.dataView.setUint16(this.position, value)\n    this.position += 2\n  },\n  pushUint24(value: number) {\n    this.assertPosition(this.position + 2)\n    this.dataView.setUint16(this.position, value >> 8)\n    this.dataView.setUint8(this.position + 2, value & ~4294967040)\n    this.position += 3\n  },\n  pushUint32(value: number) {\n    this.assertPosition(this.position + 3)\n    this.dataView.setUint32(this.position, value)\n    this.position += 4\n  },\n  readByte() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectByte()\n    this.position++\n    return value\n  },\n  readBytes(length, size) {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectBytes(length)\n    this.position += size ?? length\n    return value\n  },\n  readUint8() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint8()\n    this.position += 1\n    return value\n  },\n  readUint16() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint16()\n    this.position += 2\n    return value\n  },\n  readUint24() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint24()\n    this.position += 3\n    return value\n  },\n  readUint32() {\n    this.assertReadLimit()\n    this._touch()\n    const value = this.inspectUint32()\n    this.position += 4\n    return value\n  },\n  get remaining() {\n    return this.bytes.length - this.position\n  },\n  setPosition(position) {\n    const oldPosition = this.position\n    this.assertPosition(position)\n    this.position = position\n    return () => (this.position = oldPosition)\n  },\n  _touch() {\n    if (this.recursiveReadLimit === Number.POSITIVE_INFINITY) return\n    const count = this.getReadCount()\n    this.positionReadCount.set(this.position, count + 1)\n    if (count > 0) this.recursiveReadCount++\n  },\n}\n\ntype CursorConfig = { recursiveReadLimit?: number | undefined }\n\nexport type CreateCursorErrorType =\n  | CursorErrorType\n  | StaticCursorErrorType\n  | ErrorType\n\nexport function createCursor(\n  bytes: ByteArray,\n  { recursiveReadLimit = 8_192 }: CursorConfig = {},\n): Cursor {\n  const cursor: Cursor = Object.create(staticCursor)\n  cursor.bytes = bytes\n  cursor.dataView = new DataView(\n    bytes.buffer,\n    bytes.byteOffset,\n    bytes.byteLength,\n  )\n  cursor.positionReadCount = new Map()\n  cursor.recursiveReadLimit = recursiveReadLimit\n  return cursor\n}\n", "import { InvalidBytesBooleanError } from '../../errors/encoding.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type TrimErrorType, trim } from '../data/trim.js'\n\nimport {\n  type AssertSizeErrorType,\n  type HexToBigIntErrorType,\n  type HexToNumberErrorType,\n  assertSize,\n  hexToBigInt,\n  hexToNumber,\n} from './fromHex.js'\nimport { type BytesToHexErrorType, bytesToHex } from './toHex.js'\n\nexport type FromBytesParameters<\n  to extends 'string' | 'hex' | 'bigint' | 'number' | 'boolean',\n> =\n  | to\n  | {\n      /** Size of the bytes. */\n      size?: number | undefined\n      /** Type to convert to. */\n      to: to\n    }\n\nexport type FromBytesReturnType<to> = to extends 'string'\n  ? string\n  : to extends 'hex'\n    ? Hex\n    : to extends 'bigint'\n      ? bigint\n      : to extends 'number'\n        ? number\n        : to extends 'boolean'\n          ? boolean\n          : never\n\nexport type FromBytesErrorType =\n  | BytesToHexErrorType\n  | BytesToBigIntErrorType\n  | BytesToBoolErrorType\n  | BytesToNumberErrorType\n  | BytesToStringErrorType\n  | ErrorType\n\n/**\n * Decodes a byte array into a UTF-8 string, hex value, number, bigint or boolean.\n *\n * - Docs: https://viem.sh/docs/utilities/fromBytes\n * - Example: https://viem.sh/docs/utilities/fromBytes#usage\n *\n * @param bytes Byte array to decode.\n * @param toOrOpts Type to convert to or options.\n * @returns Decoded value.\n *\n * @example\n * import { fromBytes } from 'viem'\n * const data = fromBytes(new Uint8Array([1, 164]), 'number')\n * // 420\n *\n * @example\n * import { fromBytes } from 'viem'\n * const data = fromBytes(\n *   new Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]),\n *   'string'\n * )\n * // 'Hello world'\n */\nexport function fromBytes<\n  to extends 'string' | 'hex' | 'bigint' | 'number' | 'boolean',\n>(\n  bytes: ByteArray,\n  toOrOpts: FromBytesParameters<to>,\n): FromBytesReturnType<to> {\n  const opts = typeof toOrOpts === 'string' ? { to: toOrOpts } : toOrOpts\n  const to = opts.to\n\n  if (to === 'number')\n    return bytesToNumber(bytes, opts) as FromBytesReturnType<to>\n  if (to === 'bigint')\n    return bytesToBigInt(bytes, opts) as FromBytesReturnType<to>\n  if (to === 'boolean')\n    return bytesToBool(bytes, opts) as FromBytesReturnType<to>\n  if (to === 'string')\n    return bytesToString(bytes, opts) as FromBytesReturnType<to>\n  return bytesToHex(bytes, opts) as FromBytesReturnType<to>\n}\n\nexport type BytesToBigIntOpts = {\n  /** Whether or not the number of a signed representation. */\n  signed?: boolean | undefined\n  /** Size of the bytes. */\n  size?: number | undefined\n}\n\nexport type BytesToBigIntErrorType =\n  | BytesToHexErrorType\n  | HexToBigIntErrorType\n  | ErrorType\n\n/**\n * Decodes a byte array into a bigint.\n *\n * - Docs: https://viem.sh/docs/utilities/fromBytes#bytestobigint\n *\n * @param bytes Byte array to decode.\n * @param opts Options.\n * @returns BigInt value.\n *\n * @example\n * import { bytesToBigInt } from 'viem'\n * const data = bytesToBigInt(new Uint8Array([1, 164]))\n * // 420n\n */\nexport function bytesToBigInt(\n  bytes: ByteArray,\n  opts: BytesToBigIntOpts = {},\n): bigint {\n  if (typeof opts.size !== 'undefined') assertSize(bytes, { size: opts.size })\n  const hex = bytesToHex(bytes, opts)\n  return hexToBigInt(hex, opts)\n}\n\nexport type BytesToBoolOpts = {\n  /** Size of the bytes. */\n  size?: number | undefined\n}\n\nexport type BytesToBoolErrorType =\n  | AssertSizeErrorType\n  | TrimErrorType\n  | ErrorType\n\n/**\n * Decodes a byte array into a boolean.\n *\n * - Docs: https://viem.sh/docs/utilities/fromBytes#bytestobool\n *\n * @param bytes Byte array to decode.\n * @param opts Options.\n * @returns Boolean value.\n *\n * @example\n * import { bytesToBool } from 'viem'\n * const data = bytesToBool(new Uint8Array([1]))\n * // true\n */\nexport function bytesToBool(\n  bytes_: ByteArray,\n  opts: BytesToBoolOpts = {},\n): boolean {\n  let bytes = bytes_\n  if (typeof opts.size !== 'undefined') {\n    assertSize(bytes, { size: opts.size })\n    bytes = trim(bytes)\n  }\n  if (bytes.length > 1 || bytes[0] > 1)\n    throw new InvalidBytesBooleanError(bytes)\n  return Boolean(bytes[0])\n}\n\nexport type BytesToNumberOpts = BytesToBigIntOpts\n\nexport type BytesToNumberErrorType =\n  | BytesToHexErrorType\n  | HexToNumberErrorType\n  | ErrorType\n\n/**\n * Decodes a byte array into a number.\n *\n * - Docs: https://viem.sh/docs/utilities/fromBytes#bytestonumber\n *\n * @param bytes Byte array to decode.\n * @param opts Options.\n * @returns Number value.\n *\n * @example\n * import { bytesToNumber } from 'viem'\n * const data = bytesToNumber(new Uint8Array([1, 164]))\n * // 420\n */\nexport function bytesToNumber(\n  bytes: ByteArray,\n  opts: BytesToNumberOpts = {},\n): number {\n  if (typeof opts.size !== 'undefined') assertSize(bytes, { size: opts.size })\n  const hex = bytesToHex(bytes, opts)\n  return hexToNumber(hex, opts)\n}\n\nexport type BytesToStringOpts = {\n  /** Size of the bytes. */\n  size?: number | undefined\n}\n\nexport type BytesToStringErrorType =\n  | AssertSizeErrorType\n  | TrimErrorType\n  | ErrorType\n\n/**\n * Decodes a byte array into a UTF-8 string.\n *\n * - Docs: https://viem.sh/docs/utilities/fromBytes#bytestostring\n *\n * @param bytes Byte array to decode.\n * @param opts Options.\n * @returns String value.\n *\n * @example\n * import { bytesToString } from 'viem'\n * const data = bytesToString(new Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))\n * // 'Hello world'\n */\nexport function bytesToString(\n  bytes_: ByteArray,\n  opts: BytesToStringOpts = {},\n): string {\n  let bytes = bytes_\n  if (typeof opts.size !== 'undefined') {\n    assertSize(bytes, { size: opts.size })\n    bytes = trim(bytes, { dir: 'right' })\n  }\n  return new TextDecoder().decode(bytes)\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\nexport type ConcatReturnType<value extends Hex | ByteArray> = value extends Hex\n  ? Hex\n  : ByteArray\n\nexport type ConcatErrorType =\n  | ConcatBytesErrorType\n  | ConcatHexErrorType\n  | ErrorType\n\nexport function concat<value extends Hex | ByteArray>(\n  values: readonly value[],\n): ConcatReturnType<value> {\n  if (typeof values[0] === 'string')\n    return concatHex(values as readonly Hex[]) as ConcatReturnType<value>\n  return concatBytes(values as readonly ByteArray[]) as ConcatReturnType<value>\n}\n\nexport type ConcatBytesErrorType = ErrorType\n\nexport function concatBytes(values: readonly ByteArray[]): ByteArray {\n  let length = 0\n  for (const arr of values) {\n    length += arr.length\n  }\n  const result = new Uint8Array(length)\n  let offset = 0\n  for (const arr of values) {\n    result.set(arr, offset)\n    offset += arr.length\n  }\n  return result\n}\n\nexport type ConcatHexErrorType = ErrorType\n\nexport function concatHex(values: readonly Hex[]): Hex {\n  return `0x${(values as Hex[]).reduce(\n    (acc, x) => acc + x.replace('0x', ''),\n    '',\n  )}`\n}\n", "export const arrayRegex = /^(.*)\\[([0-9]*)\\]$/\n\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nexport const bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/\n\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nexport const integerRegex =\n  /^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/\n", "import type {\n  AbiParameter,\n  AbiParameterToPrimitiveType,\n  AbiParametersToPrimitiveTypes,\n} from 'abitype'\n\nimport {\n  AbiEncodingArrayLengthMismatchError,\n  type AbiEncodingArrayLengthMismatchErrorType,\n  AbiEncodingBytesSizeMismatchError,\n  type AbiEncodingBytesSizeMismatchErrorType,\n  AbiEncodingLengthMismatchError,\n  type AbiEncodingLengthMismatchErrorType,\n  InvalidAbiEncodingTypeError,\n  type InvalidAbiEncodingTypeErrorType,\n  InvalidArrayError,\n  type InvalidArrayErrorType,\n} from '../../errors/abi.js'\nimport {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../../errors/address.js'\nimport { BaseError } from '../../errors/base.js'\nimport { IntegerOutOfRangeError } from '../../errors/encoding.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport { type IsAddressErrorType, isAddress } from '../address/isAddress.js'\nimport { type ConcatErrorType, concat } from '../data/concat.js'\nimport { type PadHexErrorType, padHex } from '../data/pad.js'\nimport { type SizeErrorType, size } from '../data/size.js'\nimport { type SliceErrorType, slice } from '../data/slice.js'\nimport {\n  type BoolToHexErrorType,\n  type NumberToHexErrorType,\n  type StringToHexErrorType,\n  boolToHex,\n  numberToHex,\n  stringToHex,\n} from '../encoding/toHex.js'\nimport { integerRegex } from '../regex.js'\n\nexport type EncodeAbiParametersReturnType = Hex\n\nexport type EncodeAbiParametersErrorType =\n  | AbiEncodingLengthMismatchErrorType\n  | PrepareParamsErrorType\n  | EncodeParamsErrorType\n  | ErrorType\n\n/**\n * @description Encodes a list of primitive values into an ABI-encoded hex value.\n *\n * - Docs: https://viem.sh/docs/abi/encodeAbiParameters#encodeabiparameters\n *\n *   Generates ABI encoded data using the [ABI specification](https://docs.soliditylang.org/en/latest/abi-spec), given a set of ABI parameters (inputs/outputs) and their corresponding values.\n *\n * @param params - a set of ABI Parameters (params), that can be in the shape of the inputs or outputs attribute of an ABI Item.\n * @param values - a set of values (values) that correspond to the given params.\n * @example\n * ```typescript\n * import { encodeAbiParameters } from 'viem'\n *\n * const encodedData = encodeAbiParameters(\n *   [\n *     { name: 'x', type: 'string' },\n *     { name: 'y', type: 'uint' },\n *     { name: 'z', type: 'bool' }\n *   ],\n *   ['wagmi', 420n, true]\n * )\n * ```\n *\n * You can also pass in Human Readable parameters with the parseAbiParameters utility.\n *\n * @example\n * ```typescript\n * import { encodeAbiParameters, parseAbiParameters } from 'viem'\n *\n * const encodedData = encodeAbiParameters(\n *   parseAbiParameters('string x, uint y, bool z'),\n *   ['wagmi', 420n, true]\n * )\n * ```\n */\nexport function encodeAbiParameters<\n  const params extends readonly AbiParameter[] | readonly unknown[],\n>(\n  params: params,\n  values: params extends readonly AbiParameter[]\n    ? AbiParametersToPrimitiveTypes<params>\n    : never,\n): EncodeAbiParametersReturnType {\n  if (params.length !== values.length)\n    throw new AbiEncodingLengthMismatchError({\n      expectedLength: params.length as number,\n      givenLength: values.length as any,\n    })\n  // Prepare the parameters to determine dynamic types to encode.\n  const preparedParams = prepareParams({\n    params: params as readonly AbiParameter[],\n    values: values as any,\n  })\n  const data = encodeParams(preparedParams)\n  if (data.length === 0) return '0x'\n  return data\n}\n\n/////////////////////////////////////////////////////////////////\n\ntype PreparedParam = { dynamic: boolean; encoded: Hex }\n\ntype TupleAbiParameter = AbiParameter & { components: readonly AbiParameter[] }\ntype Tuple = AbiParameterToPrimitiveType<TupleAbiParameter>\n\ntype PrepareParamsErrorType = PrepareParamErrorType | ErrorType\n\nfunction prepareParams<const params extends readonly AbiParameter[]>({\n  params,\n  values,\n}: {\n  params: params\n  values: AbiParametersToPrimitiveTypes<params>\n}) {\n  const preparedParams: PreparedParam[] = []\n  for (let i = 0; i < params.length; i++) {\n    preparedParams.push(prepareParam({ param: params[i], value: values[i] }))\n  }\n  return preparedParams\n}\n\ntype PrepareParamErrorType =\n  | EncodeAddressErrorType\n  | EncodeArrayErrorType\n  | EncodeBytesErrorType\n  | EncodeBoolErrorType\n  | EncodeNumberErrorType\n  | EncodeStringErrorType\n  | EncodeTupleErrorType\n  | GetArrayComponentsErrorType\n  | InvalidAbiEncodingTypeErrorType\n  | ErrorType\n\nfunction prepareParam<const param extends AbiParameter>({\n  param,\n  value,\n}: {\n  param: param\n  value: AbiParameterToPrimitiveType<param>\n}): PreparedParam {\n  const arrayComponents = getArrayComponents(param.type)\n  if (arrayComponents) {\n    const [length, type] = arrayComponents\n    return encodeArray(value, { length, param: { ...param, type } })\n  }\n  if (param.type === 'tuple') {\n    return encodeTuple(value as unknown as Tuple, {\n      param: param as TupleAbiParameter,\n    })\n  }\n  if (param.type === 'address') {\n    return encodeAddress(value as unknown as Hex)\n  }\n  if (param.type === 'bool') {\n    return encodeBool(value as unknown as boolean)\n  }\n  if (param.type.startsWith('uint') || param.type.startsWith('int')) {\n    const signed = param.type.startsWith('int')\n    const [, , size = '256'] = integerRegex.exec(param.type) ?? []\n    return encodeNumber(value as unknown as number, {\n      signed,\n      size: Number(size),\n    })\n  }\n  if (param.type.startsWith('bytes')) {\n    return encodeBytes(value as unknown as Hex, { param })\n  }\n  if (param.type === 'string') {\n    return encodeString(value as unknown as string)\n  }\n  throw new InvalidAbiEncodingTypeError(param.type, {\n    docsPath: '/docs/contract/encodeAbiParameters',\n  })\n}\n\n/////////////////////////////////////////////////////////////////\n\ntype EncodeParamsErrorType = NumberToHexErrorType | SizeErrorType | ErrorType\n\nfunction encodeParams(preparedParams: PreparedParam[]): Hex {\n  // 1. Compute the size of the static part of the parameters.\n  let staticSize = 0\n  for (let i = 0; i < preparedParams.length; i++) {\n    const { dynamic, encoded } = preparedParams[i]\n    if (dynamic) staticSize += 32\n    else staticSize += size(encoded)\n  }\n\n  // 2. Split the parameters into static and dynamic parts.\n  const staticParams: Hex[] = []\n  const dynamicParams: Hex[] = []\n  let dynamicSize = 0\n  for (let i = 0; i < preparedParams.length; i++) {\n    const { dynamic, encoded } = preparedParams[i]\n    if (dynamic) {\n      staticParams.push(numberToHex(staticSize + dynamicSize, { size: 32 }))\n      dynamicParams.push(encoded)\n      dynamicSize += size(encoded)\n    } else {\n      staticParams.push(encoded)\n    }\n  }\n\n  // 3. Concatenate static and dynamic parts.\n  return concat([...staticParams, ...dynamicParams])\n}\n\n/////////////////////////////////////////////////////////////////\n\ntype EncodeAddressErrorType =\n  | InvalidAddressErrorType\n  | IsAddressErrorType\n  | ErrorType\n\nfunction encodeAddress(value: Hex): PreparedParam {\n  if (!isAddress(value)) throw new InvalidAddressError({ address: value })\n  return { dynamic: false, encoded: padHex(value.toLowerCase() as Hex) }\n}\n\ntype EncodeArrayErrorType =\n  | AbiEncodingArrayLengthMismatchErrorType\n  | ConcatErrorType\n  | EncodeParamsErrorType\n  | InvalidArrayErrorType\n  | NumberToHexErrorType\n  // TODO: Add back once circular type reference is resolved\n  // | PrepareParamErrorType\n  | ErrorType\n\nfunction encodeArray<const param extends AbiParameter>(\n  value: AbiParameterToPrimitiveType<param>,\n  {\n    length,\n    param,\n  }: {\n    length: number | null\n    param: param\n  },\n): PreparedParam {\n  const dynamic = length === null\n\n  if (!Array.isArray(value)) throw new InvalidArrayError(value)\n  if (!dynamic && value.length !== length)\n    throw new AbiEncodingArrayLengthMismatchError({\n      expectedLength: length!,\n      givenLength: value.length,\n      type: `${param.type}[${length}]`,\n    })\n\n  let dynamicChild = false\n  const preparedParams: PreparedParam[] = []\n  for (let i = 0; i < value.length; i++) {\n    const preparedParam = prepareParam({ param, value: value[i] })\n    if (preparedParam.dynamic) dynamicChild = true\n    preparedParams.push(preparedParam)\n  }\n\n  if (dynamic || dynamicChild) {\n    const data = encodeParams(preparedParams)\n    if (dynamic) {\n      const length = numberToHex(preparedParams.length, { size: 32 })\n      return {\n        dynamic: true,\n        encoded: preparedParams.length > 0 ? concat([length, data]) : length,\n      }\n    }\n    if (dynamicChild) return { dynamic: true, encoded: data }\n  }\n  return {\n    dynamic: false,\n    encoded: concat(preparedParams.map(({ encoded }) => encoded)),\n  }\n}\n\ntype EncodeBytesErrorType =\n  | AbiEncodingBytesSizeMismatchErrorType\n  | ConcatErrorType\n  | PadHexErrorType\n  | NumberToHexErrorType\n  | SizeErrorType\n  | ErrorType\n\nfunction encodeBytes<const param extends AbiParameter>(\n  value: Hex,\n  { param }: { param: param },\n): PreparedParam {\n  const [, paramSize] = param.type.split('bytes')\n  const bytesSize = size(value)\n  if (!paramSize) {\n    let value_ = value\n    // If the size is not divisible by 32 bytes, pad the end\n    // with empty bytes to the ceiling 32 bytes.\n    if (bytesSize % 32 !== 0)\n      value_ = padHex(value_, {\n        dir: 'right',\n        size: Math.ceil((value.length - 2) / 2 / 32) * 32,\n      })\n    return {\n      dynamic: true,\n      encoded: concat([padHex(numberToHex(bytesSize, { size: 32 })), value_]),\n    }\n  }\n  if (bytesSize !== Number.parseInt(paramSize))\n    throw new AbiEncodingBytesSizeMismatchError({\n      expectedSize: Number.parseInt(paramSize),\n      value,\n    })\n  return { dynamic: false, encoded: padHex(value, { dir: 'right' }) }\n}\n\ntype EncodeBoolErrorType = PadHexErrorType | BoolToHexErrorType | ErrorType\n\nfunction encodeBool(value: boolean): PreparedParam {\n  if (typeof value !== 'boolean')\n    throw new BaseError(\n      `Invalid boolean value: \"${value}\" (type: ${typeof value}). Expected: \\`true\\` or \\`false\\`.`,\n    )\n  return { dynamic: false, encoded: padHex(boolToHex(value)) }\n}\n\ntype EncodeNumberErrorType = NumberToHexErrorType | ErrorType\n\nfunction encodeNumber(\n  value: number,\n  { signed, size = 256 }: { signed: boolean; size?: number | undefined },\n): PreparedParam {\n  if (typeof size === 'number') {\n    const max = 2n ** (BigInt(size) - (signed ? 1n : 0n)) - 1n\n    const min = signed ? -max - 1n : 0n\n    if (value > max || value < min)\n      throw new IntegerOutOfRangeError({\n        max: max.toString(),\n        min: min.toString(),\n        signed,\n        size: size / 8,\n        value: value.toString(),\n      })\n  }\n  return {\n    dynamic: false,\n    encoded: numberToHex(value, {\n      size: 32,\n      signed,\n    }),\n  }\n}\n\ntype EncodeStringErrorType =\n  | ConcatErrorType\n  | NumberToHexErrorType\n  | PadHexErrorType\n  | SizeErrorType\n  | SliceErrorType\n  | StringToHexErrorType\n  | ErrorType\n\nfunction encodeString(value: string): PreparedParam {\n  const hexValue = stringToHex(value)\n  const partsLength = Math.ceil(size(hexValue) / 32)\n  const parts: Hex[] = []\n  for (let i = 0; i < partsLength; i++) {\n    parts.push(\n      padHex(slice(hexValue, i * 32, (i + 1) * 32), {\n        dir: 'right',\n      }),\n    )\n  }\n  return {\n    dynamic: true,\n    encoded: concat([\n      padHex(numberToHex(size(hexValue), { size: 32 })),\n      ...parts,\n    ]),\n  }\n}\n\ntype EncodeTupleErrorType =\n  | ConcatErrorType\n  | EncodeParamsErrorType\n  // TODO: Add back once circular type reference is resolved\n  // | PrepareParamErrorType\n  | ErrorType\n\nfunction encodeTuple<\n  const param extends AbiParameter & { components: readonly AbiParameter[] },\n>(\n  value: AbiParameterToPrimitiveType<param>,\n  { param }: { param: param },\n): PreparedParam {\n  let dynamic = false\n  const preparedParams: PreparedParam[] = []\n  for (let i = 0; i < param.components.length; i++) {\n    const param_ = param.components[i]\n    const index = Array.isArray(value) ? i : param_.name\n    const preparedParam = prepareParam({\n      param: param_,\n      value: (value as any)[index!] as readonly unknown[],\n    })\n    preparedParams.push(preparedParam)\n    if (preparedParam.dynamic) dynamic = true\n  }\n  return {\n    dynamic,\n    encoded: dynamic\n      ? encodeParams(preparedParams)\n      : concat(preparedParams.map(({ encoded }) => encoded)),\n  }\n}\n\ntype GetArrayComponentsErrorType = ErrorType\n\nexport function getArrayComponents(\n  type: string,\n): [length: number | null, innerType: string] | undefined {\n  const matches = type.match(/^(.*)\\[(\\d+)?\\]$/)\n  return matches\n    ? // Return `null` if the array is dynamic.\n      [matches[2] ? Number(matches[2]) : null, matches[1]]\n    : undefined\n}\n", "import type { AbiParameter, AbiParametersToPrimitiveTypes } from 'abitype'\n\nimport type { ByteArray, Hex } from '../../types/misc.js'\n\nimport {\n  AbiDecodingDataSizeTooSmallError,\n  AbiDecodingZeroDataError,\n  InvalidAbiDecodingTypeError,\n  type InvalidAbiDecodingTypeErrorType,\n} from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport {\n  type ChecksumAddressErrorType,\n  checksumAddress,\n} from '../address/getAddress.js'\nimport {\n  type CreateCursorErrorType,\n  type Cursor,\n  createCursor,\n} from '../cursor.js'\nimport { type SizeErrorType, size } from '../data/size.js'\nimport { type SliceBytesErrorType, sliceBytes } from '../data/slice.js'\nimport { type TrimErrorType, trim } from '../data/trim.js'\nimport {\n  type BytesToBigIntErrorType,\n  type BytesToBoolErrorType,\n  type BytesToNumberErrorType,\n  type BytesToStringErrorType,\n  bytesToBigInt,\n  bytesToBool,\n  bytesToNumber,\n  bytesToString,\n} from '../encoding/fromBytes.js'\nimport { type HexToBytesErrorType, hexToBytes } from '../encoding/toBytes.js'\nimport { type BytesToHexErrorType, bytesToHex } from '../encoding/toHex.js'\nimport { getArrayComponents } from './encodeAbiParameters.js'\n\nexport type DecodeAbiParametersReturnType<\n  params extends readonly AbiParameter[] = readonly AbiParameter[],\n> = AbiParametersToPrimitiveTypes<\n  params extends readonly AbiParameter[] ? params : AbiParameter[]\n>\n\nexport type DecodeAbiParametersErrorType =\n  | HexToBytesErrorType\n  | BytesToHexErrorType\n  | DecodeParameterErrorType\n  | SizeErrorType\n  | CreateCursorErrorType\n  | ErrorType\n\nexport function decodeAbiParameters<\n  const params extends readonly AbiParameter[],\n>(\n  params: params,\n  data: ByteArray | Hex,\n): DecodeAbiParametersReturnType<params> {\n  const bytes = typeof data === 'string' ? hexToBytes(data) : data\n  const cursor = createCursor(bytes)\n\n  if (size(bytes) === 0 && params.length > 0)\n    throw new AbiDecodingZeroDataError()\n  if (size(data) && size(data) < 32)\n    throw new AbiDecodingDataSizeTooSmallError({\n      data: typeof data === 'string' ? data : bytesToHex(data),\n      params: params as readonly AbiParameter[],\n      size: size(data),\n    })\n\n  let consumed = 0\n  const values = []\n  for (let i = 0; i < params.length; ++i) {\n    const param = params[i]\n    cursor.setPosition(consumed)\n    const [data, consumed_] = decodeParameter(cursor, param, {\n      staticPosition: 0,\n    })\n    consumed += consumed_\n    values.push(data)\n  }\n  return values as DecodeAbiParametersReturnType<params>\n}\n\ntype DecodeParameterErrorType =\n  | DecodeArrayErrorType\n  | DecodeTupleErrorType\n  | DecodeAddressErrorType\n  | DecodeBoolErrorType\n  | DecodeBytesErrorType\n  | DecodeNumberErrorType\n  | DecodeStringErrorType\n  | InvalidAbiDecodingTypeErrorType\n\nfunction decodeParameter(\n  cursor: Cursor,\n  param: AbiParameter,\n  { staticPosition }: { staticPosition: number },\n) {\n  const arrayComponents = getArrayComponents(param.type)\n  if (arrayComponents) {\n    const [length, type] = arrayComponents\n    return decodeArray(cursor, { ...param, type }, { length, staticPosition })\n  }\n  if (param.type === 'tuple')\n    return decodeTuple(cursor, param as TupleAbiParameter, { staticPosition })\n\n  if (param.type === 'address') return decodeAddress(cursor)\n  if (param.type === 'bool') return decodeBool(cursor)\n  if (param.type.startsWith('bytes'))\n    return decodeBytes(cursor, param, { staticPosition })\n  if (param.type.startsWith('uint') || param.type.startsWith('int'))\n    return decodeNumber(cursor, param)\n  if (param.type === 'string') return decodeString(cursor, { staticPosition })\n  throw new InvalidAbiDecodingTypeError(param.type, {\n    docsPath: '/docs/contract/decodeAbiParameters',\n  })\n}\n\n////////////////////////////////////////////////////////////////////\n// Type Decoders\n\nconst sizeOfLength = 32\nconst sizeOfOffset = 32\n\ntype DecodeAddressErrorType =\n  | ChecksumAddressErrorType\n  | BytesToHexErrorType\n  | SliceBytesErrorType\n  | ErrorType\n\nfunction decodeAddress(cursor: Cursor) {\n  const value = cursor.readBytes(32)\n  return [checksumAddress(bytesToHex(sliceBytes(value, -20))), 32]\n}\n\ntype DecodeArrayErrorType = BytesToNumberErrorType | ErrorType\n\nfunction decodeArray(\n  cursor: Cursor,\n  param: AbiParameter,\n  { length, staticPosition }: { length: number | null; staticPosition: number },\n) {\n  // If the length of the array is not known in advance (dynamic array),\n  // this means we will need to wonder off to the pointer and decode.\n  if (!length) {\n    // Dealing with a dynamic type, so get the offset of the array data.\n    const offset = bytesToNumber(cursor.readBytes(sizeOfOffset))\n\n    // Start is the static position of current slot + offset.\n    const start = staticPosition + offset\n    const startOfData = start + sizeOfLength\n\n    // Get the length of the array from the offset.\n    cursor.setPosition(start)\n    const length = bytesToNumber(cursor.readBytes(sizeOfLength))\n\n    // Check if the array has any dynamic children.\n    const dynamicChild = hasDynamicChild(param)\n\n    let consumed = 0\n    const value: unknown[] = []\n    for (let i = 0; i < length; ++i) {\n      // If any of the children is dynamic, then all elements will be offset pointer, thus size of one slot (32 bytes).\n      // Otherwise, elements will be the size of their encoding (consumed bytes).\n      cursor.setPosition(startOfData + (dynamicChild ? i * 32 : consumed))\n      const [data, consumed_] = decodeParameter(cursor, param, {\n        staticPosition: startOfData,\n      })\n      consumed += consumed_\n      value.push(data)\n    }\n\n    // As we have gone wondering, restore to the original position + next slot.\n    cursor.setPosition(staticPosition + 32)\n    return [value, 32]\n  }\n\n  // If the length of the array is known in advance,\n  // and the length of an element deeply nested in the array is not known,\n  // we need to decode the offset of the array data.\n  if (hasDynamicChild(param)) {\n    // Dealing with dynamic types, so get the offset of the array data.\n    const offset = bytesToNumber(cursor.readBytes(sizeOfOffset))\n\n    // Start is the static position of current slot + offset.\n    const start = staticPosition + offset\n\n    const value: unknown[] = []\n    for (let i = 0; i < length; ++i) {\n      // Move cursor along to the next slot (next offset pointer).\n      cursor.setPosition(start + i * 32)\n      const [data] = decodeParameter(cursor, param, {\n        staticPosition: start,\n      })\n      value.push(data)\n    }\n\n    // As we have gone wondering, restore to the original position + next slot.\n    cursor.setPosition(staticPosition + 32)\n    return [value, 32]\n  }\n\n  // If the length of the array is known in advance and the array is deeply static,\n  // then we can just decode each element in sequence.\n  let consumed = 0\n  const value: unknown[] = []\n  for (let i = 0; i < length; ++i) {\n    const [data, consumed_] = decodeParameter(cursor, param, {\n      staticPosition: staticPosition + consumed,\n    })\n    consumed += consumed_\n    value.push(data)\n  }\n  return [value, consumed]\n}\n\ntype DecodeBoolErrorType = BytesToBoolErrorType | ErrorType\n\nfunction decodeBool(cursor: Cursor) {\n  return [bytesToBool(cursor.readBytes(32), { size: 32 }), 32]\n}\n\ntype DecodeBytesErrorType =\n  | BytesToNumberErrorType\n  | BytesToHexErrorType\n  | ErrorType\n\nfunction decodeBytes(\n  cursor: Cursor,\n  param: AbiParameter,\n  { staticPosition }: { staticPosition: number },\n) {\n  const [_, size] = param.type.split('bytes')\n  if (!size) {\n    // Dealing with dynamic types, so get the offset of the bytes data.\n    const offset = bytesToNumber(cursor.readBytes(32))\n\n    // Set position of the cursor to start of bytes data.\n    cursor.setPosition(staticPosition + offset)\n\n    const length = bytesToNumber(cursor.readBytes(32))\n\n    // If there is no length, we have zero data.\n    if (length === 0) {\n      // As we have gone wondering, restore to the original position + next slot.\n      cursor.setPosition(staticPosition + 32)\n      return ['0x', 32]\n    }\n\n    const data = cursor.readBytes(length)\n\n    // As we have gone wondering, restore to the original position + next slot.\n    cursor.setPosition(staticPosition + 32)\n    return [bytesToHex(data), 32]\n  }\n\n  const value = bytesToHex(cursor.readBytes(Number.parseInt(size), 32))\n  return [value, 32]\n}\n\ntype DecodeNumberErrorType =\n  | BytesToNumberErrorType\n  | BytesToBigIntErrorType\n  | ErrorType\n\nfunction decodeNumber(cursor: Cursor, param: AbiParameter) {\n  const signed = param.type.startsWith('int')\n  const size = Number.parseInt(param.type.split('int')[1] || '256')\n  const value = cursor.readBytes(32)\n  return [\n    size > 48\n      ? bytesToBigInt(value, { signed })\n      : bytesToNumber(value, { signed }),\n    32,\n  ]\n}\n\ntype TupleAbiParameter = AbiParameter & { components: readonly AbiParameter[] }\n\ntype DecodeTupleErrorType = BytesToNumberErrorType | ErrorType\n\nfunction decodeTuple(\n  cursor: Cursor,\n  param: TupleAbiParameter,\n  { staticPosition }: { staticPosition: number },\n) {\n  // Tuples can have unnamed components (i.e. they are arrays), so we must\n  // determine whether the tuple is named or unnamed. In the case of a named\n  // tuple, the value will be an object where each property is the name of the\n  // component. In the case of an unnamed tuple, the value will be an array.\n  const hasUnnamedChild =\n    param.components.length === 0 || param.components.some(({ name }) => !name)\n\n  // Initialize the value to an object or an array, depending on whether the\n  // tuple is named or unnamed.\n  const value: any = hasUnnamedChild ? [] : {}\n  let consumed = 0\n\n  // If the tuple has a dynamic child, we must first decode the offset to the\n  // tuple data.\n  if (hasDynamicChild(param)) {\n    // Dealing with dynamic types, so get the offset of the tuple data.\n    const offset = bytesToNumber(cursor.readBytes(sizeOfOffset))\n\n    // Start is the static position of referencing slot + offset.\n    const start = staticPosition + offset\n\n    for (let i = 0; i < param.components.length; ++i) {\n      const component = param.components[i]\n      cursor.setPosition(start + consumed)\n      const [data, consumed_] = decodeParameter(cursor, component, {\n        staticPosition: start,\n      })\n      consumed += consumed_\n      value[hasUnnamedChild ? i : component?.name!] = data\n    }\n\n    // As we have gone wondering, restore to the original position + next slot.\n    cursor.setPosition(staticPosition + 32)\n    return [value, 32]\n  }\n\n  // If the tuple has static children, we can just decode each component\n  // in sequence.\n  for (let i = 0; i < param.components.length; ++i) {\n    const component = param.components[i]\n    const [data, consumed_] = decodeParameter(cursor, component, {\n      staticPosition,\n    })\n    value[hasUnnamedChild ? i : component?.name!] = data\n    consumed += consumed_\n  }\n  return [value, consumed]\n}\n\ntype DecodeStringErrorType =\n  | BytesToNumberErrorType\n  | BytesToStringErrorType\n  | TrimErrorType\n  | ErrorType\n\nfunction decodeString(\n  cursor: Cursor,\n  { staticPosition }: { staticPosition: number },\n) {\n  // Get offset to start of string data.\n  const offset = bytesToNumber(cursor.readBytes(32))\n\n  // Start is the static position of current slot + offset.\n  const start = staticPosition + offset\n  cursor.setPosition(start)\n\n  const length = bytesToNumber(cursor.readBytes(32))\n\n  // If there is no length, we have zero data (empty string).\n  if (length === 0) {\n    cursor.setPosition(staticPosition + 32)\n    return ['', 32]\n  }\n\n  const data = cursor.readBytes(length, 32)\n  const value = bytesToString(trim(data))\n\n  // As we have gone wondering, restore to the original position + next slot.\n  cursor.setPosition(staticPosition + 32)\n\n  return [value, 32]\n}\n\nfunction hasDynamicChild(param: AbiParameter) {\n  const { type } = param\n  if (type === 'string') return true\n  if (type === 'bytes') return true\n  if (type.endsWith('[]')) return true\n\n  if (type === 'tuple') return (param as any).components?.some(hasDynamicChild)\n\n  const arrayComponents = getArrayComponents(param.type)\n  if (\n    arrayComponents &&\n    hasDynamicChild({ ...param, type: arrayComponents[1] } as AbiParameter)\n  )\n    return true\n\n  return false\n}\n", "import type { Abi, ExtractAbiError } from 'abitype'\n\nimport { solidityError, solidityPanic } from '../../constants/solidity.js'\nimport {\n  AbiDecodingZeroDataError,\n  type AbiDecodingZeroDataErrorType,\n  AbiErrorSignatureNotFoundError,\n  type AbiErrorSignatureNotFoundErrorType,\n} from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type {\n  AbiItem,\n  ContractErrorArgs,\n  ContractErrorName,\n} from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport { slice } from '../data/slice.js'\nimport {\n  type ToFunctionSelectorErrorType,\n  toFunctionSelector,\n} from '../hash/toFunctionSelector.js'\nimport {\n  type DecodeAbiParametersErrorType,\n  decodeAbiParameters,\n} from './decodeAbiParameters.js'\nimport { type FormatAbiItemErrorType, formatAbiItem } from './formatAbiItem.js'\n\nexport type DecodeErrorResultParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n> = { abi?: abi | undefined; data: Hex }\n\nexport type DecodeErrorResultReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  ///\n  allErrorNames extends ContractErrorName<abi> = ContractErrorName<abi>,\n> = IsNarrowable<abi, Abi> extends true\n  ? UnionEvaluate<\n      {\n        [errorName in allErrorNames]: {\n          abiItem: abi extends Abi\n            ? Abi extends abi\n              ? AbiItem\n              : ExtractAbiError<abi, errorName>\n            : AbiItem\n          args: ContractErrorArgs<abi, errorName>\n          errorName: errorName\n        }\n      }[allErrorNames]\n    >\n  : {\n      abiItem: AbiItem\n      args: readonly unknown[] | undefined\n      errorName: string\n    }\n\nexport type DecodeErrorResultErrorType =\n  | AbiDecodingZeroDataErrorType\n  | AbiErrorSignatureNotFoundErrorType\n  | DecodeAbiParametersErrorType\n  | FormatAbiItemErrorType\n  | ToFunctionSelectorErrorType\n  | ErrorType\n\nexport function decodeErrorResult<const abi extends Abi | readonly unknown[]>(\n  parameters: DecodeErrorResultParameters<abi>,\n): DecodeErrorResultReturnType<abi> {\n  const { abi, data } = parameters as DecodeErrorResultParameters\n\n  const signature = slice(data, 0, 4)\n  if (signature === '0x') throw new AbiDecodingZeroDataError()\n\n  const abi_ = [...(abi || []), solidityError, solidityPanic]\n  const abiItem = abi_.find(\n    (x) =>\n      x.type === 'error' && signature === toFunctionSelector(formatAbiItem(x)),\n  )\n  if (!abiItem)\n    throw new AbiErrorSignatureNotFoundError(signature, {\n      docsPath: '/docs/contract/decodeErrorResult',\n    })\n  return {\n    abiItem,\n    args:\n      'inputs' in abiItem && abiItem.inputs && abiItem.inputs.length > 0\n        ? decodeAbiParameters(abiItem.inputs, slice(data, 4))\n        : undefined,\n    errorName: (abiItem as { name: string }).name,\n  } as DecodeErrorResultReturnType<abi>\n}\n", "import type { ErrorType } from '../errors/utils.js'\n\nexport type StringifyErrorType = ErrorType\n\nexport const stringify: typeof JSON.stringify = (value, replacer, space) =>\n  JSON.stringify(\n    value,\n    (key, value_) => {\n      const value = typeof value_ === 'bigint' ? value_.toString() : value_\n      return typeof replacer === 'function' ? replacer(key, value) : value\n    },\n    space,\n  )\n", "import type { ErrorType } from '../../errors/utils.js'\nimport {\n  type ToSignatureHashErrorType,\n  toSignatureHash,\n} from './toSignatureHash.js'\n\nexport type ToEventSelectorErrorType = ToSignatureHashErrorType | ErrorType\n\n/**\n * Returns the event selector for a given event definition.\n *\n * @example\n * const selector = toEventSelector('Transfer(address indexed from, address indexed to, uint256 amount)')\n * // 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef\n */\nexport const toEventSelector = toSignatureHash\n", "import type { <PERSON>bi, AbiParameter, Address } from 'abitype'\n\nimport {\n  AbiItemAmbiguityError,\n  type AbiItemAmbiguityErrorType,\n} from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type {\n  AbiItem,\n  AbiItemArgs,\n  AbiItemName,\n  ExtractAbiItemForArgs,\n  Widen,\n} from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { UnionEvaluate } from '../../types/utils.js'\nimport { type IsHexErrorType, isHex } from '../../utils/data/isHex.js'\nimport { type IsAddressErrorType, isAddress } from '../address/isAddress.js'\nimport { toEventSelector } from '../hash/toEventSelector.js'\nimport {\n  type ToFunctionSelectorErrorType,\n  toFunctionSelector,\n} from '../hash/toFunctionSelector.js'\n\nexport type GetAbiItemParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  name extends AbiItemName<abi> = AbiItemName<abi>,\n  args extends AbiItemArgs<abi, name> | undefined = AbiItemArgs<abi, name>,\n  ///\n  allArgs = AbiItemArgs<abi, name>,\n  allNames = AbiItemName<abi>,\n> = {\n  abi: abi\n  name:\n    | allNames // show all options\n    | (name extends allNames ? name : never) // infer value\n    | Hex // function selector\n} & UnionEvaluate<\n  readonly [] extends allArgs\n    ? {\n        args?:\n          | allArgs // show all options\n          // infer value, widen inferred value of `args` conditionally to match `allArgs`\n          | (abi extends Abi\n              ? args extends allArgs\n                ? Widen<args>\n                : never\n              : never)\n          | undefined\n      }\n    : {\n        args?:\n          | allArgs // show all options\n          | (Widen<args> & (args extends allArgs ? unknown : never)) // infer value, widen inferred value of `args` match `allArgs` (e.g. avoid union `args: readonly [123n] | readonly [bigint]`)\n          | undefined\n      }\n>\n\nexport type GetAbiItemErrorType =\n  | IsArgOfTypeErrorType\n  | IsHexErrorType\n  | ToFunctionSelectorErrorType\n  | AbiItemAmbiguityErrorType\n  | ErrorType\n\nexport type GetAbiItemReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  name extends AbiItemName<abi> = AbiItemName<abi>,\n  args extends AbiItemArgs<abi, name> | undefined = AbiItemArgs<abi, name>,\n> = abi extends Abi\n  ? Abi extends abi\n    ? AbiItem | undefined\n    : ExtractAbiItemForArgs<\n        abi,\n        name,\n        args extends AbiItemArgs<abi, name> ? args : AbiItemArgs<abi, name>\n      >\n  : AbiItem | undefined\n\nexport function getAbiItem<\n  const abi extends Abi | readonly unknown[],\n  name extends AbiItemName<abi>,\n  const args extends AbiItemArgs<abi, name> | undefined = undefined,\n>(\n  parameters: GetAbiItemParameters<abi, name, args>,\n): GetAbiItemReturnType<abi, name, args> {\n  const { abi, args = [], name } = parameters as unknown as GetAbiItemParameters\n\n  const isSelector = isHex(name, { strict: false })\n  const abiItems = (abi as Abi).filter((abiItem) => {\n    if (isSelector) {\n      if (abiItem.type === 'function')\n        return toFunctionSelector(abiItem) === name\n      if (abiItem.type === 'event') return toEventSelector(abiItem) === name\n      return false\n    }\n    return 'name' in abiItem && abiItem.name === name\n  })\n\n  if (abiItems.length === 0)\n    return undefined as GetAbiItemReturnType<abi, name, args>\n  if (abiItems.length === 1)\n    return abiItems[0] as GetAbiItemReturnType<abi, name, args>\n\n  let matchedAbiItem: AbiItem | undefined = undefined\n  for (const abiItem of abiItems) {\n    if (!('inputs' in abiItem)) continue\n    if (!args || args.length === 0) {\n      if (!abiItem.inputs || abiItem.inputs.length === 0)\n        return abiItem as GetAbiItemReturnType<abi, name, args>\n      continue\n    }\n    if (!abiItem.inputs) continue\n    if (abiItem.inputs.length === 0) continue\n    if (abiItem.inputs.length !== args.length) continue\n    const matched = args.every((arg, index) => {\n      const abiParameter = 'inputs' in abiItem && abiItem.inputs![index]\n      if (!abiParameter) return false\n      return isArgOfType(arg, abiParameter)\n    })\n    if (matched) {\n      // Check for ambiguity against already matched parameters (e.g. `address` vs `bytes20`).\n      if (\n        matchedAbiItem &&\n        'inputs' in matchedAbiItem &&\n        matchedAbiItem.inputs\n      ) {\n        const ambiguousTypes = getAmbiguousTypes(\n          abiItem.inputs,\n          matchedAbiItem.inputs,\n          args as readonly unknown[],\n        )\n        if (ambiguousTypes)\n          throw new AbiItemAmbiguityError(\n            {\n              abiItem,\n              type: ambiguousTypes[0],\n            },\n            {\n              abiItem: matchedAbiItem,\n              type: ambiguousTypes[1],\n            },\n          )\n      }\n\n      matchedAbiItem = abiItem\n    }\n  }\n\n  if (matchedAbiItem)\n    return matchedAbiItem as GetAbiItemReturnType<abi, name, args>\n  return abiItems[0] as GetAbiItemReturnType<abi, name, args>\n}\n\ntype IsArgOfTypeErrorType = IsAddressErrorType | ErrorType\n\n/** @internal */\nexport function isArgOfType(arg: unknown, abiParameter: AbiParameter): boolean {\n  const argType = typeof arg\n  const abiParameterType = abiParameter.type\n  switch (abiParameterType) {\n    case 'address':\n      return isAddress(arg as Address, { strict: false })\n    case 'bool':\n      return argType === 'boolean'\n    case 'function':\n      return argType === 'string'\n    case 'string':\n      return argType === 'string'\n    default: {\n      if (abiParameterType === 'tuple' && 'components' in abiParameter)\n        return Object.values(abiParameter.components).every(\n          (component, index) => {\n            return isArgOfType(\n              Object.values(arg as unknown[] | Record<string, unknown>)[index],\n              component as AbiParameter,\n            )\n          },\n        )\n\n      // `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n      // https://regexr.com/6v8hp\n      if (\n        /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/.test(\n          abiParameterType,\n        )\n      )\n        return argType === 'number' || argType === 'bigint'\n\n      // `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n      // https://regexr.com/6va55\n      if (/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/.test(abiParameterType))\n        return argType === 'string' || arg instanceof Uint8Array\n\n      // fixed-length (`<type>[M]`) and dynamic (`<type>[]`) arrays\n      // https://regexr.com/6va6i\n      if (/[a-z]+[1-9]{0,3}(\\[[0-9]{0,}\\])+$/.test(abiParameterType)) {\n        return (\n          Array.isArray(arg) &&\n          arg.every((x: unknown) =>\n            isArgOfType(x, {\n              ...abiParameter,\n              // Pop off `[]` or `[M]` from end of type\n              type: abiParameterType.replace(/(\\[[0-9]{0,}\\])$/, ''),\n            } as AbiParameter),\n          )\n        )\n      }\n\n      return false\n    }\n  }\n}\n\n/** @internal */\nexport function getAmbiguousTypes(\n  sourceParameters: readonly AbiParameter[],\n  targetParameters: readonly AbiParameter[],\n  args: AbiItemArgs,\n): AbiParameter['type'][] | undefined {\n  for (const parameterIndex in sourceParameters) {\n    const sourceParameter = sourceParameters[parameterIndex]\n    const targetParameter = targetParameters[parameterIndex]\n\n    if (\n      sourceParameter.type === 'tuple' &&\n      targetParameter.type === 'tuple' &&\n      'components' in sourceParameter &&\n      'components' in targetParameter\n    )\n      return getAmbiguousTypes(\n        sourceParameter.components,\n        targetParameter.components,\n        (args as any)[parameterIndex],\n      )\n\n    const types = [sourceParameter.type, targetParameter.type]\n\n    const ambiguous = (() => {\n      if (types.includes('address') && types.includes('bytes20')) return true\n      if (types.includes('address') && types.includes('string'))\n        return isAddress(args[parameterIndex] as Address, { strict: false })\n      if (types.includes('address') && types.includes('bytes'))\n        return isAddress(args[parameterIndex] as Address, { strict: false })\n      return false\n    })()\n\n    if (ambiguous) return types\n  }\n\n  return\n}\n", "export const etherUnits = {\n  gwei: 9,\n  wei: 18,\n}\nexport const gweiUnits = {\n  ether: -9,\n  wei: 9,\n}\nexport const weiUnits = {\n  ether: -18,\n  gwei: -9,\n}\n", "import type { ErrorType } from '../../errors/utils.js'\n\nexport type FormatUnitsErrorType = ErrorType\n\n/**\n *  Divides a number by a given exponent of base 10 (10exponent), and formats it into a string representation of the number..\n *\n * - Docs: https://viem.sh/docs/utilities/formatUnits\n *\n * @example\n * import { formatUnits } from 'viem'\n *\n * formatUnits(420000000000n, 9)\n * // '420'\n */\nexport function formatUnits(value: bigint, decimals: number) {\n  let display = value.toString()\n\n  const negative = display.startsWith('-')\n  if (negative) display = display.slice(1)\n\n  display = display.padStart(decimals, '0')\n\n  let [integer, fraction] = [\n    display.slice(0, display.length - decimals),\n    display.slice(display.length - decimals),\n  ]\n  fraction = fraction.replace(/(0+)$/, '')\n  return `${negative ? '-' : ''}${integer || '0'}${\n    fraction ? `.${fraction}` : ''\n  }`\n}\n", "import { etherUnits } from '../../constants/unit.js'\n\nimport { type FormatUnitsErrorType, formatUnits } from './formatUnits.js'\n\nexport type FormatEtherErrorType = FormatUnitsErrorType\n\n/**\n * Converts numerical wei to a string representation of ether.\n *\n * - Docs: https://viem.sh/docs/utilities/formatEther\n *\n * @example\n * import { formatEther } from 'viem'\n *\n * formatEther(1000000000000000000n)\n * // '1'\n */\nexport function formatEther(wei: bigint, unit: 'wei' | 'gwei' = 'wei') {\n  return formatUnits(wei, etherUnits[unit])\n}\n", "import { gweiUnits } from '../../constants/unit.js'\n\nimport { type FormatUnitsErrorType, formatUnits } from './formatUnits.js'\n\nexport type FormatGweiErrorType = FormatUnitsErrorType\n\n/**\n * Converts numerical wei to a string representation of gwei.\n *\n * - Docs: https://viem.sh/docs/utilities/formatGwei\n *\n * @example\n * import { formatGwei } from 'viem'\n *\n * formatGwei(1000000000n)\n * // '1'\n */\nexport function formatGwei(wei: bigint, unit: 'wei' = 'wei') {\n  return formatUnits(wei, gweiUnits[unit])\n}\n", "import type { StateMapping, StateOverride } from '../types/stateOverride.js'\nimport { BaseError } from './base.js'\n\nexport type AccountStateConflictErrorType = AccountStateConflictError & {\n  name: 'AccountStateConflictError'\n}\n\nexport class AccountStateConflictError extends BaseError {\n  constructor({ address }: { address: string }) {\n    super(`State for account \"${address}\" is set multiple times.`, {\n      name: 'AccountStateConflictError',\n    })\n  }\n}\n\nexport type StateAssignmentConflictErrorType = StateAssignmentConflictError & {\n  name: 'StateAssignmentConflictError'\n}\n\nexport class StateAssignmentConflictError extends BaseError {\n  constructor() {\n    super('state and stateDiff are set on the same account.', {\n      name: 'StateAssignmentConflictError',\n    })\n  }\n}\n\n/** @internal */\nexport function prettyStateMapping(stateMapping: StateMapping) {\n  return stateMapping.reduce((pretty, { slot, value }) => {\n    return `${pretty}        ${slot}: ${value}\\n`\n  }, '')\n}\n\nexport function prettyStateOverride(stateOverride: StateOverride) {\n  return stateOverride\n    .reduce((pretty, { address, ...state }) => {\n      let val = `${pretty}    ${address}:\\n`\n      if (state.nonce) val += `      nonce: ${state.nonce}\\n`\n      if (state.balance) val += `      balance: ${state.balance}\\n`\n      if (state.code) val += `      code: ${state.code}\\n`\n      if (state.state) {\n        val += '      state:\\n'\n        val += prettyStateMapping(state.state)\n      }\n      if (state.stateDiff) {\n        val += '      stateDiff:\\n'\n        val += prettyStateMapping(state.stateDiff)\n      }\n      return val\n    }, '  State Override:\\n')\n    .slice(0, -1)\n}\n", "import type { Account } from '../accounts/types.js'\nimport type { SendTransactionParameters } from '../actions/wallet/sendTransaction.js'\nimport type { BlockTag } from '../types/block.js'\nimport type { Chain } from '../types/chain.js'\nimport type { Hash, Hex } from '../types/misc.js'\nimport type { TransactionType } from '../types/transaction.js'\nimport { formatEther } from '../utils/unit/formatEther.js'\nimport { formatGwei } from '../utils/unit/formatGwei.js'\n\nimport { BaseError } from './base.js'\n\nexport function prettyPrint(\n  args: Record<string, bigint | number | string | undefined | false | unknown>,\n) {\n  const entries = Object.entries(args)\n    .map(([key, value]) => {\n      if (value === undefined || value === false) return null\n      return [key, value]\n    })\n    .filter(Boolean) as [string, string][]\n  const maxLength = entries.reduce((acc, [key]) => Math.max(acc, key.length), 0)\n  return entries\n    .map(([key, value]) => `  ${`${key}:`.padEnd(maxLength + 1)}  ${value}`)\n    .join('\\n')\n}\n\nexport type FeeConflictErrorType = FeeConflictError & {\n  name: 'FeeConflictError'\n}\nexport class FeeConflictError extends BaseError {\n  constructor() {\n    super(\n      [\n        'Cannot specify both a `gasPrice` and a `maxFeePerGas`/`maxPriorityFeePerGas`.',\n        'Use `maxFeePerGas`/`maxPriorityFeePerGas` for EIP-1559 compatible networks, and `gasPrice` for others.',\n      ].join('\\n'),\n      { name: 'FeeConflictError' },\n    )\n  }\n}\n\nexport type InvalidLegacyVErrorType = InvalidLegacyVError & {\n  name: 'InvalidLegacyVError'\n}\nexport class InvalidLegacyVError extends BaseError {\n  constructor({ v }: { v: bigint }) {\n    super(`Invalid \\`v\\` value \"${v}\". Expected 27 or 28.`, {\n      name: 'InvalidLegacyVError',\n    })\n  }\n}\n\nexport type InvalidSerializableTransactionErrorType =\n  InvalidSerializableTransactionError & {\n    name: 'InvalidSerializableTransactionError'\n  }\nexport class InvalidSerializableTransactionError extends BaseError {\n  constructor({ transaction }: { transaction: Record<string, unknown> }) {\n    super('Cannot infer a transaction type from provided transaction.', {\n      metaMessages: [\n        'Provided Transaction:',\n        '{',\n        prettyPrint(transaction),\n        '}',\n        '',\n        'To infer the type, either provide:',\n        '- a `type` to the Transaction, or',\n        '- an EIP-1559 Transaction with `maxFeePerGas`, or',\n        '- an EIP-2930 Transaction with `gasPrice` & `accessList`, or',\n        '- an EIP-4844 Transaction with `blobs`, `blobVersionedHashes`, `sidecars`, or',\n        '- an EIP-7702 Transaction with `authorizationList`, or',\n        '- a Legacy Transaction with `gasPrice`',\n      ],\n      name: 'InvalidSerializableTransactionError',\n    })\n  }\n}\n\nexport type InvalidSerializedTransactionTypeErrorType =\n  InvalidSerializedTransactionTypeError & {\n    name: 'InvalidSerializedTransactionTypeError'\n  }\nexport class InvalidSerializedTransactionTypeError extends BaseError {\n  serializedType: Hex\n\n  constructor({ serializedType }: { serializedType: Hex }) {\n    super(`Serialized transaction type \"${serializedType}\" is invalid.`, {\n      name: 'InvalidSerializedTransactionType',\n    })\n\n    this.serializedType = serializedType\n  }\n}\n\nexport type InvalidSerializedTransactionErrorType =\n  InvalidSerializedTransactionError & {\n    name: 'InvalidSerializedTransactionError'\n  }\nexport class InvalidSerializedTransactionError extends BaseError {\n  serializedTransaction: Hex\n  type: TransactionType\n\n  constructor({\n    attributes,\n    serializedTransaction,\n    type,\n  }: {\n    attributes: Record<string, unknown>\n    serializedTransaction: Hex\n    type: TransactionType\n  }) {\n    const missing = Object.entries(attributes)\n      .map(([key, value]) => (typeof value === 'undefined' ? key : undefined))\n      .filter(Boolean)\n    super(`Invalid serialized transaction of type \"${type}\" was provided.`, {\n      metaMessages: [\n        `Serialized Transaction: \"${serializedTransaction}\"`,\n        missing.length > 0 ? `Missing Attributes: ${missing.join(', ')}` : '',\n      ].filter(Boolean),\n      name: 'InvalidSerializedTransactionError',\n    })\n\n    this.serializedTransaction = serializedTransaction\n    this.type = type\n  }\n}\n\nexport type InvalidStorageKeySizeErrorType = InvalidStorageKeySizeError & {\n  name: 'InvalidStorageKeySizeError'\n}\nexport class InvalidStorageKeySizeError extends BaseError {\n  constructor({ storageKey }: { storageKey: Hex }) {\n    super(\n      `Size for storage key \"${storageKey}\" is invalid. Expected 32 bytes. Got ${Math.floor(\n        (storageKey.length - 2) / 2,\n      )} bytes.`,\n      { name: 'InvalidStorageKeySizeError' },\n    )\n  }\n}\n\nexport type TransactionExecutionErrorType = TransactionExecutionError & {\n  name: 'TransactionExecutionError'\n}\nexport class TransactionExecutionError extends BaseError {\n  override cause: BaseError\n\n  constructor(\n    cause: BaseError,\n    {\n      account,\n      docsPath,\n      chain,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value,\n    }: Omit<SendTransactionParameters, 'account' | 'chain'> & {\n      account: Account | null\n      chain?: Chain | undefined\n      docsPath?: string | undefined\n    },\n  ) {\n    const prettyArgs = prettyPrint({\n      chain: chain && `${chain?.name} (id: ${chain?.id})`,\n      from: account?.address,\n      to,\n      value:\n        typeof value !== 'undefined' &&\n        `${formatEther(value)} ${chain?.nativeCurrency?.symbol || 'ETH'}`,\n      data,\n      gas,\n      gasPrice:\n        typeof gasPrice !== 'undefined' && `${formatGwei(gasPrice)} gwei`,\n      maxFeePerGas:\n        typeof maxFeePerGas !== 'undefined' &&\n        `${formatGwei(maxFeePerGas)} gwei`,\n      maxPriorityFeePerGas:\n        typeof maxPriorityFeePerGas !== 'undefined' &&\n        `${formatGwei(maxPriorityFeePerGas)} gwei`,\n      nonce,\n    })\n\n    super(cause.shortMessage, {\n      cause,\n      docsPath,\n      metaMessages: [\n        ...(cause.metaMessages ? [...cause.metaMessages, ' '] : []),\n        'Request Arguments:',\n        prettyArgs,\n      ].filter(Boolean) as string[],\n      name: 'TransactionExecutionError',\n    })\n    this.cause = cause\n  }\n}\n\nexport type TransactionNotFoundErrorType = TransactionNotFoundError & {\n  name: 'TransactionNotFoundError'\n}\nexport class TransactionNotFoundError extends BaseError {\n  constructor({\n    blockHash,\n    blockNumber,\n    blockTag,\n    hash,\n    index,\n  }: {\n    blockHash?: Hash | undefined\n    blockNumber?: bigint | undefined\n    blockTag?: BlockTag | undefined\n    hash?: Hash | undefined\n    index?: number | undefined\n  }) {\n    let identifier = 'Transaction'\n    if (blockTag && index !== undefined)\n      identifier = `Transaction at block time \"${blockTag}\" at index \"${index}\"`\n    if (blockHash && index !== undefined)\n      identifier = `Transaction at block hash \"${blockHash}\" at index \"${index}\"`\n    if (blockNumber && index !== undefined)\n      identifier = `Transaction at block number \"${blockNumber}\" at index \"${index}\"`\n    if (hash) identifier = `Transaction with hash \"${hash}\"`\n    super(`${identifier} could not be found.`, {\n      name: 'TransactionNotFoundError',\n    })\n  }\n}\n\nexport type TransactionReceiptNotFoundErrorType =\n  TransactionReceiptNotFoundError & {\n    name: 'TransactionReceiptNotFoundError'\n  }\nexport class TransactionReceiptNotFoundError extends BaseError {\n  constructor({ hash }: { hash: Hash }) {\n    super(\n      `Transaction receipt with hash \"${hash}\" could not be found. The Transaction may not be processed on a block yet.`,\n      {\n        name: 'TransactionReceiptNotFoundError',\n      },\n    )\n  }\n}\n\nexport type WaitForTransactionReceiptTimeoutErrorType =\n  WaitForTransactionReceiptTimeoutError & {\n    name: 'WaitForTransactionReceiptTimeoutError'\n  }\nexport class WaitForTransactionReceiptTimeoutError extends BaseError {\n  constructor({ hash }: { hash: Hash }) {\n    super(\n      `Timed out while waiting for transaction with hash \"${hash}\" to be confirmed.`,\n      { name: 'WaitForTransactionReceiptTimeoutError' },\n    )\n  }\n}\n", "import type { Address } from 'abitype'\n\nexport type ErrorType<name extends string = 'Error'> = Error & { name: name }\n\nexport const getContractAddress = (address: Address) => address\nexport const getUrl = (url: string) => url\n", "import type { Abi, Address } from 'abitype'\n\nimport { parseAccount } from '../accounts/utils/parseAccount.js'\nimport type { CallParameters } from '../actions/public/call.js'\nimport { panicReasons } from '../constants/solidity.js'\nimport type { Chain } from '../types/chain.js'\nimport type { Hex } from '../types/misc.js'\nimport {\n  type DecodeErrorResultReturnType,\n  decodeErrorResult,\n} from '../utils/abi/decodeErrorResult.js'\nimport { formatAbiItem } from '../utils/abi/formatAbiItem.js'\nimport { formatAbiItemWithArgs } from '../utils/abi/formatAbiItemWithArgs.js'\nimport { getAbiItem } from '../utils/abi/getAbiItem.js'\nimport { formatEther } from '../utils/unit/formatEther.js'\nimport { formatGwei } from '../utils/unit/formatGwei.js'\n\nimport { AbiErrorSignatureNotFoundError } from './abi.js'\nimport { BaseError } from './base.js'\nimport { prettyStateOverride } from './stateOverride.js'\nimport { prettyPrint } from './transaction.js'\nimport { getContractAddress } from './utils.js'\n\nexport type CallExecutionErrorType = CallExecutionError & {\n  name: 'CallExecutionError'\n}\nexport class CallExecutionError extends BaseError {\n  override cause: BaseError\n\n  constructor(\n    cause: BaseError,\n    {\n      account: account_,\n      docsPath,\n      chain,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to,\n      value,\n      stateOverride,\n    }: CallParameters & {\n      chain?: Chain | undefined\n      docsPath?: string | undefined\n    },\n  ) {\n    const account = account_ ? parseAccount(account_) : undefined\n    let prettyArgs = prettyPrint({\n      from: account?.address,\n      to,\n      value:\n        typeof value !== 'undefined' &&\n        `${formatEther(value)} ${chain?.nativeCurrency?.symbol || 'ETH'}`,\n      data,\n      gas,\n      gasPrice:\n        typeof gasPrice !== 'undefined' && `${formatGwei(gasPrice)} gwei`,\n      maxFeePerGas:\n        typeof maxFeePerGas !== 'undefined' &&\n        `${formatGwei(maxFeePerGas)} gwei`,\n      maxPriorityFeePerGas:\n        typeof maxPriorityFeePerGas !== 'undefined' &&\n        `${formatGwei(maxPriorityFeePerGas)} gwei`,\n      nonce,\n    })\n\n    if (stateOverride) {\n      prettyArgs += `\\n${prettyStateOverride(stateOverride)}`\n    }\n\n    super(cause.shortMessage, {\n      cause,\n      docsPath,\n      metaMessages: [\n        ...(cause.metaMessages ? [...cause.metaMessages, ' '] : []),\n        'Raw Call Arguments:',\n        prettyArgs,\n      ].filter(Boolean) as string[],\n      name: 'CallExecutionError',\n    })\n    this.cause = cause\n  }\n}\n\nexport type ContractFunctionExecutionErrorType =\n  ContractFunctionExecutionError & {\n    name: 'ContractFunctionExecutionError'\n  }\nexport class ContractFunctionExecutionError extends BaseError {\n  abi: Abi\n  args?: unknown[] | undefined\n  override cause: BaseError\n  contractAddress?: Address | undefined\n  formattedArgs?: string | undefined\n  functionName: string\n  sender?: Address | undefined\n\n  constructor(\n    cause: BaseError,\n    {\n      abi,\n      args,\n      contractAddress,\n      docsPath,\n      functionName,\n      sender,\n    }: {\n      abi: Abi\n      args?: any | undefined\n      contractAddress?: Address | undefined\n      docsPath?: string | undefined\n      functionName: string\n      sender?: Address | undefined\n    },\n  ) {\n    const abiItem = getAbiItem({ abi, args, name: functionName })\n    const formattedArgs = abiItem\n      ? formatAbiItemWithArgs({\n          abiItem,\n          args,\n          includeFunctionName: false,\n          includeName: false,\n        })\n      : undefined\n    const functionWithParams = abiItem\n      ? formatAbiItem(abiItem, { includeName: true })\n      : undefined\n\n    const prettyArgs = prettyPrint({\n      address: contractAddress && getContractAddress(contractAddress),\n      function: functionWithParams,\n      args:\n        formattedArgs &&\n        formattedArgs !== '()' &&\n        `${[...Array(functionName?.length ?? 0).keys()]\n          .map(() => ' ')\n          .join('')}${formattedArgs}`,\n      sender,\n    })\n\n    super(\n      cause.shortMessage ||\n        `An unknown error occurred while executing the contract function \"${functionName}\".`,\n      {\n        cause,\n        docsPath,\n        metaMessages: [\n          ...(cause.metaMessages ? [...cause.metaMessages, ' '] : []),\n          prettyArgs && 'Contract Call:',\n          prettyArgs,\n        ].filter(Boolean) as string[],\n        name: 'ContractFunctionExecutionError',\n      },\n    )\n    this.abi = abi\n    this.args = args\n    this.cause = cause\n    this.contractAddress = contractAddress\n    this.functionName = functionName\n    this.sender = sender\n  }\n}\n\nexport type ContractFunctionRevertedErrorType =\n  ContractFunctionRevertedError & {\n    name: 'ContractFunctionRevertedError'\n  }\nexport class ContractFunctionRevertedError extends BaseError {\n  data?: DecodeErrorResultReturnType | undefined\n  raw?: Hex | undefined\n  reason?: string | undefined\n  signature?: Hex | undefined\n\n  constructor({\n    abi,\n    data,\n    functionName,\n    message,\n  }: {\n    abi: Abi\n    data?: Hex | undefined\n    functionName: string\n    message?: string | undefined\n  }) {\n    let cause: Error | undefined\n    let decodedData: DecodeErrorResultReturnType | undefined = undefined\n    let metaMessages: string[] | undefined\n    let reason: string | undefined\n    if (data && data !== '0x') {\n      try {\n        decodedData = decodeErrorResult({ abi, data })\n        const { abiItem, errorName, args: errorArgs } = decodedData\n        if (errorName === 'Error') {\n          reason = (errorArgs as [string])[0]\n        } else if (errorName === 'Panic') {\n          const [firstArg] = errorArgs as [number]\n          reason = panicReasons[firstArg as keyof typeof panicReasons]\n        } else {\n          const errorWithParams = abiItem\n            ? formatAbiItem(abiItem, { includeName: true })\n            : undefined\n          const formattedArgs =\n            abiItem && errorArgs\n              ? formatAbiItemWithArgs({\n                  abiItem,\n                  args: errorArgs,\n                  includeFunctionName: false,\n                  includeName: false,\n                })\n              : undefined\n\n          metaMessages = [\n            errorWithParams ? `Error: ${errorWithParams}` : '',\n            formattedArgs && formattedArgs !== '()'\n              ? `       ${[...Array(errorName?.length ?? 0).keys()]\n                  .map(() => ' ')\n                  .join('')}${formattedArgs}`\n              : '',\n          ]\n        }\n      } catch (err) {\n        cause = err as Error\n      }\n    } else if (message) reason = message\n\n    let signature: Hex | undefined\n    if (cause instanceof AbiErrorSignatureNotFoundError) {\n      signature = cause.signature\n      metaMessages = [\n        `Unable to decode signature \"${signature}\" as it was not found on the provided ABI.`,\n        'Make sure you are using the correct ABI and that the error exists on it.',\n        `You can look up the decoded signature here: https://openchain.xyz/signatures?query=${signature}.`,\n      ]\n    }\n\n    super(\n      (reason && reason !== 'execution reverted') || signature\n        ? [\n            `The contract function \"${functionName}\" reverted with the following ${\n              signature ? 'signature' : 'reason'\n            }:`,\n            reason || signature,\n          ].join('\\n')\n        : `The contract function \"${functionName}\" reverted.`,\n      {\n        cause,\n        metaMessages,\n        name: 'ContractFunctionRevertedError',\n      },\n    )\n\n    this.data = decodedData\n    this.raw = data\n    this.reason = reason\n    this.signature = signature\n  }\n}\n\nexport type ContractFunctionZeroDataErrorType =\n  ContractFunctionZeroDataError & {\n    name: 'ContractFunctionZeroDataError'\n  }\nexport class ContractFunctionZeroDataError extends BaseError {\n  constructor({ functionName }: { functionName: string }) {\n    super(`The contract function \"${functionName}\" returned no data (\"0x\").`, {\n      metaMessages: [\n        'This could be due to any of the following:',\n        `  - The contract does not have the function \"${functionName}\",`,\n        '  - The parameters passed to the contract function may be invalid, or',\n        '  - The address is not a contract.',\n      ],\n      name: 'ContractFunctionZeroDataError',\n    })\n  }\n}\n\nexport type CounterfactualDeploymentFailedErrorType =\n  CounterfactualDeploymentFailedError & {\n    name: 'CounterfactualDeploymentFailedError'\n  }\nexport class CounterfactualDeploymentFailedError extends BaseError {\n  constructor({ factory }: { factory?: Address | undefined }) {\n    super(\n      `Deployment for counterfactual contract call failed${\n        factory ? ` for factory \"${factory}\".` : ''\n      }`,\n      {\n        metaMessages: [\n          'Please ensure:',\n          '- The `factory` is a valid contract deployment factory (ie. Create2 Factory, ERC-4337 Factory, etc).',\n          '- The `factoryData` is a valid encoded function call for contract deployment function on the factory.',\n        ],\n        name: 'CounterfactualDeploymentFailedError',\n      },\n    )\n  }\n}\n\nexport type RawContractErrorType = RawContractError & {\n  name: 'RawContractError'\n}\nexport class RawContractError extends BaseError {\n  code = 3\n\n  data?: Hex | { data?: Hex | undefined } | undefined\n\n  constructor({\n    data,\n    message,\n  }: {\n    data?: Hex | { data?: Hex | undefined } | undefined\n    message?: string | undefined\n  }) {\n    super(message || '', { name: 'RawContractError' })\n    this.data = data\n  }\n}\n", "import type { Abi, AbiStateMutability, ExtractAbiFunctions } from 'abitype'\n\nimport {\n  AbiFunctionNotFoundError,\n  type AbiFunctionNotFoundErrorType,\n  AbiFunctionOutputsNotFoundError,\n  type AbiFunctionOutputsNotFoundErrorType,\n} from '../../errors/abi.js'\nimport type {\n  ContractFunctionArgs,\n  ContractFunctionName,\n  ContractFunctionReturnType,\n  Widen,\n} from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport {\n  type DecodeAbiParametersErrorType,\n  decodeAbiParameters,\n} from './decodeAbiParameters.js'\nimport { type GetAbiItemErrorType, getAbiItem } from './getAbiItem.js'\n\nconst docsPath = '/docs/contract/decodeFunctionResult'\n\nexport type DecodeFunctionResultParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n  args extends ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  > = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n  ///\n  hasFunctions = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [ExtractAbiFunctions<abi>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n  allFunctionNames = ContractFunctionName<abi>,\n> = {\n  abi: abi\n  data: Hex\n} & UnionEvaluate<\n  IsNarrowable<abi, Abi> extends true\n    ? abi['length'] extends 1\n      ? { functionName?: functionName | allFunctionNames | undefined }\n      : { functionName: functionName | allFunctionNames }\n    : { functionName?: functionName | allFunctionNames | undefined }\n> &\n  UnionEvaluate<\n    readonly [] extends allArgs\n      ? {\n          args?:\n            | allArgs // show all options\n            // infer value, widen inferred value of `args` conditionally to match `allArgs`\n            | (abi extends Abi\n                ? args extends allArgs\n                  ? Widen<args>\n                  : never\n                : never)\n            | undefined\n        }\n      : {\n          args?:\n            | allArgs // show all options\n            | (Widen<args> & (args extends allArgs ? unknown : never)) // infer value, widen inferred value of `args` match `allArgs` (e.g. avoid union `args: readonly [123n] | readonly [bigint]`)\n            | undefined\n        }\n  > &\n  (hasFunctions extends true ? unknown : never)\n\nexport type DecodeFunctionResultReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n  args extends ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  > = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n> = ContractFunctionReturnType<\n  abi,\n  AbiStateMutability,\n  functionName extends ContractFunctionName<abi>\n    ? functionName\n    : ContractFunctionName<abi>,\n  args\n>\n\nexport type DecodeFunctionResultErrorType =\n  | AbiFunctionNotFoundErrorType\n  | AbiFunctionOutputsNotFoundErrorType\n  | DecodeAbiParametersErrorType\n  | GetAbiItemErrorType\n  | ErrorType\n\nexport function decodeFunctionResult<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi> | undefined = undefined,\n  const args extends ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  > = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n>(\n  parameters: DecodeFunctionResultParameters<abi, functionName, args>,\n): DecodeFunctionResultReturnType<abi, functionName, args> {\n  const { abi, args, functionName, data } =\n    parameters as DecodeFunctionResultParameters\n\n  let abiItem = abi[0]\n  if (functionName) {\n    const item = getAbiItem({ abi, args, name: functionName })\n    if (!item) throw new AbiFunctionNotFoundError(functionName, { docsPath })\n    abiItem = item\n  }\n\n  if (abiItem.type !== 'function')\n    throw new AbiFunctionNotFoundError(undefined, { docsPath })\n  if (!abiItem.outputs)\n    throw new AbiFunctionOutputsNotFoundError(abiItem.name, { docsPath })\n\n  const values = decodeAbiParameters(abiItem.outputs, data)\n  if (values && values.length > 1)\n    return values as DecodeFunctionResultReturnType<abi, functionName, args>\n  if (values && values.length === 1)\n    return values[0] as DecodeFunctionResultReturnType<abi, functionName, args>\n  return undefined as DecodeFunctionResultReturnType<abi, functionName, args>\n}\n", "import type { Abi } from 'abitype'\n\nimport {\n  AbiConstructorNotFoundError,\n  type AbiConstructorNotFoundErrorType,\n  AbiConstructorParamsNotFoundError,\n} from '../../errors/abi.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ContractConstructorArgs } from '../../types/contract.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { UnionEvaluate } from '../../types/utils.js'\nimport { type ConcatHexErrorType, concatHex } from '../data/concat.js'\nimport {\n  type EncodeAbiParametersErrorType,\n  encodeAbiParameters,\n} from './encodeAbiParameters.js'\n\nconst docsPath = '/docs/contract/encodeDeployData'\n\nexport type EncodeDeployDataParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  ///\n  hasConstructor = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [Extract<abi[number], { type: 'constructor' }>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractConstructorArgs<abi>,\n> = {\n  abi: abi\n  bytecode: Hex\n} & UnionEvaluate<\n  hasConstructor extends false\n    ? { args?: undefined }\n    : readonly [] extends allArgs\n      ? { args?: allArgs | undefined }\n      : { args: allArgs }\n>\n\nexport type EncodeDeployDataReturnType = Hex\n\nexport type EncodeDeployDataErrorType =\n  | AbiConstructorNotFoundErrorType\n  | ConcatHexErrorType\n  | EncodeAbiParametersErrorType\n  | ErrorType\n\nexport function encodeDeployData<const abi extends Abi | readonly unknown[]>(\n  parameters: EncodeDeployDataParameters<abi>,\n): EncodeDeployDataReturnType {\n  const { abi, args, bytecode } = parameters as EncodeDeployDataParameters\n  if (!args || args.length === 0) return bytecode\n\n  const description = abi.find((x) => 'type' in x && x.type === 'constructor')\n  if (!description) throw new AbiConstructorNotFoundError({ docsPath })\n  if (!('inputs' in description))\n    throw new AbiConstructorParamsNotFoundError({ docsPath })\n  if (!description.inputs || description.inputs.length === 0)\n    throw new AbiConstructorParamsNotFoundError({ docsPath })\n\n  const data = encodeAbiParameters(description.inputs, args)\n  return concatHex([bytecode, data!])\n}\n", "import type {\n  Abi,\n  AbiStateMutability,\n  ExtractAbiFunction,\n  ExtractAbiFunctions,\n} from 'abitype'\n\nimport {\n  AbiFunctionNotFoundError,\n  type AbiFunctionNotFoundErrorType,\n} from '../../errors/abi.js'\nimport type {\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from '../../types/contract.js'\nimport type { ConcatHexErrorType } from '../data/concat.js'\nimport {\n  type ToFunctionSelectorErrorType,\n  toFunctionSelector,\n} from '../hash/toFunctionSelector.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport { type FormatAbiItemErrorType, formatAbiItem } from './formatAbiItem.js'\nimport { type GetAbiItemErrorType, getAbiItem } from './getAbiItem.js'\n\nconst docsPath = '/docs/contract/encodeFunctionData'\n\nexport type PrepareEncodeFunctionDataParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n  ///\n  hasFunctions = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [ExtractAbiFunctions<abi>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n  allFunctionNames = ContractFunctionName<abi>,\n> = {\n  abi: abi\n} & UnionEvaluate<\n  IsNarrowable<abi, Abi> extends true\n    ? abi['length'] extends 1\n      ? { functionName?: functionName | allFunctionNames | Hex | undefined }\n      : { functionName: functionName | allFunctionNames | Hex }\n    : { functionName?: functionName | allFunctionNames | Hex | undefined }\n> &\n  UnionEvaluate<{ args?: allArgs | undefined }> &\n  (hasFunctions extends true ? unknown : never)\n\nexport type PrepareEncodeFunctionDataReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | undefined = ContractFunctionName<abi>,\n> = {\n  abi: abi extends Abi\n    ? functionName extends ContractFunctionName<abi>\n      ? [ExtractAbiFunction<abi, functionName>]\n      : abi\n    : Abi\n  functionName: Hex\n}\n\nexport type PrepareEncodeFunctionDataErrorType =\n  | AbiFunctionNotFoundErrorType\n  | ConcatHexErrorType\n  | FormatAbiItemErrorType\n  | GetAbiItemErrorType\n  | ToFunctionSelectorErrorType\n  | ErrorType\n\nexport function prepareEncodeFunctionData<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi> | undefined = undefined,\n>(\n  parameters: PrepareEncodeFunctionDataParameters<abi, functionName>,\n): PrepareEncodeFunctionDataReturnType<abi, functionName> {\n  const { abi, args, functionName } =\n    parameters as PrepareEncodeFunctionDataParameters\n\n  let abiItem = abi[0]\n  if (functionName) {\n    const item = getAbiItem({\n      abi,\n      args,\n      name: functionName,\n    })\n    if (!item) throw new AbiFunctionNotFoundError(functionName, { docsPath })\n    abiItem = item\n  }\n\n  if (abiItem.type !== 'function')\n    throw new AbiFunctionNotFoundError(undefined, { docsPath })\n\n  return {\n    abi: [abiItem],\n    functionName: toFunctionSelector(formatAbiItem(abiItem)),\n  } as unknown as PrepareEncodeFunctionDataReturnType<abi, functionName>\n}\n", "import type { Abi, AbiStateMutability, ExtractAbiFunctions } from 'abitype'\n\nimport type { AbiFunctionNotFoundErrorType } from '../../errors/abi.js'\nimport type {\n  ContractFunctionArgs,\n  ContractFunctionName,\n} from '../../types/contract.js'\nimport { type ConcatHexErrorType, concatHex } from '../data/concat.js'\nimport type { ToFunctionSelectorErrorType } from '../hash/toFunctionSelector.js'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { IsNarrowable, UnionEvaluate } from '../../types/utils.js'\nimport {\n  type EncodeAbiParametersErrorType,\n  encodeAbiParameters,\n} from './encodeAbiParameters.js'\nimport type { FormatAbiItemErrorType } from './formatAbiItem.js'\nimport type { GetAbiItemErrorType } from './getAbiItem.js'\nimport { prepareEncodeFunctionData } from './prepareEncodeFunctionData.js'\n\nexport type EncodeFunctionDataParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends\n    | ContractFunctionName<abi>\n    | Hex\n    | undefined = ContractFunctionName<abi>,\n  ///\n  hasFunctions = abi extends Abi\n    ? Abi extends abi\n      ? true\n      : [ExtractAbiFunctions<abi>] extends [never]\n        ? false\n        : true\n    : true,\n  allArgs = ContractFunctionArgs<\n    abi,\n    AbiStateMutability,\n    functionName extends ContractFunctionName<abi>\n      ? functionName\n      : ContractFunctionName<abi>\n  >,\n  allFunctionNames = ContractFunctionName<abi>,\n> = {\n  abi: abi\n} & UnionEvaluate<\n  IsNarrowable<abi, Abi> extends true\n    ? abi['length'] extends 1\n      ? { functionName?: functionName | allFunctionNames | Hex | undefined }\n      : { functionName: functionName | allFunctionNames | Hex }\n    : { functionName?: functionName | allFunctionNames | Hex | undefined }\n> &\n  UnionEvaluate<\n    readonly [] extends allArgs\n      ? { args?: allArgs | undefined }\n      : { args: allArgs }\n  > &\n  (hasFunctions extends true ? unknown : never)\n\nexport type EncodeFunctionDataReturnType = Hex\n\nexport type EncodeFunctionDataErrorType =\n  | AbiFunctionNotFoundErrorType\n  | ConcatHexErrorType\n  | EncodeAbiParametersErrorType\n  | FormatAbiItemErrorType\n  | GetAbiItemErrorType\n  | ToFunctionSelectorErrorType\n  | ErrorType\n\nexport function encodeFunctionData<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi> | undefined = undefined,\n>(\n  parameters: EncodeFunctionDataParameters<abi, functionName>,\n): EncodeFunctionDataReturnType {\n  const { args } = parameters as EncodeFunctionDataParameters\n\n  const { abi, functionName } = (() => {\n    if (\n      parameters.abi.length === 1 &&\n      parameters.functionName?.startsWith('0x')\n    )\n      return parameters as { abi: Abi; functionName: Hex }\n    return prepareEncodeFunctionData(parameters)\n  })()\n\n  const abiItem = abi[0]\n  const signature = functionName\n\n  const data =\n    'inputs' in abiItem && abiItem.inputs\n      ? encodeAbiParameters(abiItem.inputs, args ?? [])\n      : undefined\n  return concatHex([signature, data ?? '0x'])\n}\n", "import {\n  ChainDoesNotSupportContract,\n  type ChainDoesNotSupportContractErrorType,\n} from '../../errors/chain.js'\nimport type { Chain, ChainContract } from '../../types/chain.js'\n\nexport type GetChainContractAddressErrorType =\n  ChainDoesNotSupportContractErrorType\n\nexport function getChainContractAddress({\n  blockNumber,\n  chain,\n  contract: name,\n}: {\n  blockNumber?: bigint | undefined\n  chain: Chain\n  contract: string\n}) {\n  const contract = (chain?.contracts as Record<string, ChainContract>)?.[name]\n  if (!contract)\n    throw new ChainDoesNotSupportContract({\n      chain,\n      contract: { name },\n    })\n\n  if (\n    blockNumber &&\n    contract.blockCreated &&\n    contract.blockCreated > blockNumber\n  )\n    throw new ChainDoesNotSupportContract({\n      blockNumber,\n      chain,\n      contract: {\n        name,\n        blockCreated: contract.blockCreated,\n      },\n    })\n\n  return contract.address\n}\n", "import { formatGwei } from '../utils/unit/formatGwei.js'\n\nimport { BaseError } from './base.js'\n\n/**\n * geth:    https://github.com/ethereum/go-ethereum/blob/master/core/error.go\n *          https://github.com/ethereum/go-ethereum/blob/master/core/types/transaction.go#L34-L41\n *\n * erigon:  https://github.com/ledgerwatch/erigon/blob/master/core/error.go\n *          https://github.com/ledgerwatch/erigon/blob/master/core/types/transaction.go#L41-L46\n *\n * anvil:   https://github.com/foundry-rs/foundry/blob/master/anvil/src/eth/error.rs#L108\n */\nexport type ExecutionRevertedErrorType = ExecutionRevertedError & {\n  code: 3\n  name: 'ExecutionRevertedError'\n}\nexport class ExecutionRevertedError extends BaseError {\n  static code = 3\n  static nodeMessage = /execution reverted/\n\n  constructor({\n    cause,\n    message,\n  }: { cause?: BaseError | undefined; message?: string | undefined } = {}) {\n    const reason = message\n      ?.replace('execution reverted: ', '')\n      ?.replace('execution reverted', '')\n    super(\n      `Execution reverted ${\n        reason ? `with reason: ${reason}` : 'for an unknown reason'\n      }.`,\n      {\n        cause,\n        name: 'ExecutionRevertedError',\n      },\n    )\n  }\n}\n\nexport type FeeCapTooHighErrorType = FeeCapTooHighError & {\n  name: 'FeeCapTooHighError'\n}\nexport class FeeCapTooHighError extends BaseError {\n  static nodeMessage =\n    /max fee per gas higher than 2\\^256-1|fee cap higher than 2\\^256-1/\n  constructor({\n    cause,\n    maxFeePerGas,\n  }: {\n    cause?: BaseError | undefined\n    maxFeePerGas?: bigint | undefined\n  } = {}) {\n    super(\n      `The fee cap (\\`maxFeePerGas\\`${\n        maxFeePerGas ? ` = ${formatGwei(maxFeePerGas)} gwei` : ''\n      }) cannot be higher than the maximum allowed value (2^256-1).`,\n      {\n        cause,\n        name: 'FeeCapTooHighError',\n      },\n    )\n  }\n}\n\nexport type FeeCapTooLowErrorType = FeeCapTooLowError & {\n  name: 'FeeCapTooLowError'\n}\nexport class FeeCapTooLowError extends BaseError {\n  static nodeMessage =\n    /max fee per gas less than block base fee|fee cap less than block base fee|transaction is outdated/\n  constructor({\n    cause,\n    maxFeePerGas,\n  }: {\n    cause?: BaseError | undefined\n    maxFeePerGas?: bigint | undefined\n  } = {}) {\n    super(\n      `The fee cap (\\`maxFeePerGas\\`${\n        maxFeePerGas ? ` = ${formatGwei(maxFeePerGas)}` : ''\n      } gwei) cannot be lower than the block base fee.`,\n      {\n        cause,\n        name: 'FeeCapTooLowError',\n      },\n    )\n  }\n}\n\nexport type NonceTooHighErrorType = NonceTooHighError & {\n  name: 'NonceTooHighError'\n}\nexport class NonceTooHighError extends BaseError {\n  static nodeMessage = /nonce too high/\n  constructor({\n    cause,\n    nonce,\n  }: { cause?: BaseError | undefined; nonce?: number | undefined } = {}) {\n    super(\n      `Nonce provided for the transaction ${\n        nonce ? `(${nonce}) ` : ''\n      }is higher than the next one expected.`,\n      { cause, name: 'NonceTooHighError' },\n    )\n  }\n}\n\nexport type NonceTooLowErrorType = NonceTooLowError & {\n  name: 'NonceTooLowError'\n}\nexport class NonceTooLowError extends BaseError {\n  static nodeMessage =\n    /nonce too low|transaction already imported|already known/\n  constructor({\n    cause,\n    nonce,\n  }: { cause?: BaseError | undefined; nonce?: number | undefined } = {}) {\n    super(\n      [\n        `Nonce provided for the transaction ${\n          nonce ? `(${nonce}) ` : ''\n        }is lower than the current nonce of the account.`,\n        'Try increasing the nonce or find the latest nonce with `getTransactionCount`.',\n      ].join('\\n'),\n      { cause, name: 'NonceTooLowError' },\n    )\n  }\n}\n\nexport type NonceMaxValueErrorType = NonceMaxValueError & {\n  name: 'NonceMaxValueError'\n}\nexport class NonceMaxValueError extends BaseError {\n  static nodeMessage = /nonce has max value/\n  constructor({\n    cause,\n    nonce,\n  }: { cause?: BaseError | undefined; nonce?: number | undefined } = {}) {\n    super(\n      `Nonce provided for the transaction ${\n        nonce ? `(${nonce}) ` : ''\n      }exceeds the maximum allowed nonce.`,\n      { cause, name: 'NonceMaxValueError' },\n    )\n  }\n}\n\nexport type InsufficientFundsErrorType = InsufficientFundsError & {\n  name: 'InsufficientFundsError'\n}\nexport class InsufficientFundsError extends BaseError {\n  static nodeMessage =\n    /insufficient funds|exceeds transaction sender account balance/\n  constructor({ cause }: { cause?: BaseError | undefined } = {}) {\n    super(\n      [\n        'The total cost (gas * gas fee + value) of executing this transaction exceeds the balance of the account.',\n      ].join('\\n'),\n      {\n        cause,\n        metaMessages: [\n          'This error could arise when the account does not have enough funds to:',\n          ' - pay for the total gas fee,',\n          ' - pay for the value to send.',\n          ' ',\n          'The cost of the transaction is calculated as `gas * gas fee + value`, where:',\n          ' - `gas` is the amount of gas needed for transaction to execute,',\n          ' - `gas fee` is the gas fee,',\n          ' - `value` is the amount of ether to send to the recipient.',\n        ],\n        name: 'InsufficientFundsError',\n      },\n    )\n  }\n}\n\nexport type IntrinsicGasTooHighErrorType = IntrinsicGasTooHighError & {\n  name: 'IntrinsicGasTooHighError'\n}\nexport class IntrinsicGasTooHighError extends BaseError {\n  static nodeMessage = /intrinsic gas too high|gas limit reached/\n  constructor({\n    cause,\n    gas,\n  }: { cause?: BaseError | undefined; gas?: bigint | undefined } = {}) {\n    super(\n      `The amount of gas ${\n        gas ? `(${gas}) ` : ''\n      }provided for the transaction exceeds the limit allowed for the block.`,\n      {\n        cause,\n        name: 'IntrinsicGasTooHighError',\n      },\n    )\n  }\n}\n\nexport type IntrinsicGasTooLowErrorType = IntrinsicGasTooLowError & {\n  name: 'IntrinsicGasTooLowError'\n}\nexport class IntrinsicGasTooLowError extends BaseError {\n  static nodeMessage = /intrinsic gas too low/\n  constructor({\n    cause,\n    gas,\n  }: { cause?: BaseError | undefined; gas?: bigint | undefined } = {}) {\n    super(\n      `The amount of gas ${\n        gas ? `(${gas}) ` : ''\n      }provided for the transaction is too low.`,\n      {\n        cause,\n        name: 'IntrinsicGasTooLowError',\n      },\n    )\n  }\n}\n\nexport type TransactionTypeNotSupportedErrorType =\n  TransactionTypeNotSupportedError & {\n    name: 'TransactionTypeNotSupportedError'\n  }\nexport class TransactionTypeNotSupportedError extends BaseError {\n  static nodeMessage = /transaction type not valid/\n  constructor({ cause }: { cause?: BaseError | undefined }) {\n    super('The transaction type is not supported for this chain.', {\n      cause,\n      name: 'TransactionTypeNotSupportedError',\n    })\n  }\n}\n\nexport type TipAboveFeeCapErrorType = TipAboveFeeCapError & {\n  name: 'TipAboveFeeCapError'\n}\nexport class TipAboveFeeCapError extends BaseError {\n  static nodeMessage =\n    /max priority fee per gas higher than max fee per gas|tip higher than fee cap/\n  constructor({\n    cause,\n    maxPriorityFeePerGas,\n    maxFeePerGas,\n  }: {\n    cause?: BaseError | undefined\n    maxPriorityFeePerGas?: bigint | undefined\n    maxFeePerGas?: bigint | undefined\n  } = {}) {\n    super(\n      [\n        `The provided tip (\\`maxPriorityFeePerGas\\`${\n          maxPriorityFeePerGas\n            ? ` = ${formatGwei(maxPriorityFeePerGas)} gwei`\n            : ''\n        }) cannot be higher than the fee cap (\\`maxFeePerGas\\`${\n          maxFeePerGas ? ` = ${formatGwei(maxFeePerGas)} gwei` : ''\n        }).`,\n      ].join('\\n'),\n      {\n        cause,\n        name: 'TipAboveFeeCapError',\n      },\n    )\n  }\n}\n\nexport type UnknownNodeErrorType = UnknownNodeError & {\n  name: 'UnknownNodeError'\n}\nexport class UnknownNodeError extends BaseError {\n  constructor({ cause }: { cause?: BaseError | undefined }) {\n    super(`An error occurred while executing: ${cause?.shortMessage}`, {\n      cause,\n      name: 'UnknownNodeError',\n    })\n  }\n}\n", "import { stringify } from '../utils/stringify.js'\n\nimport { BaseError } from './base.js'\nimport { getUrl } from './utils.js'\n\nexport type HttpRequestErrorType = HttpRequestError & {\n  name: 'HttpRequestError'\n}\nexport class HttpRequestError extends BaseError {\n  body?: { [x: string]: unknown } | { [y: string]: unknown }[] | undefined\n  headers?: Headers | undefined\n  status?: number | undefined\n  url: string\n\n  constructor({\n    body,\n    cause,\n    details,\n    headers,\n    status,\n    url,\n  }: {\n    body?: { [x: string]: unknown } | { [y: string]: unknown }[] | undefined\n    cause?: Error | undefined\n    details?: string | undefined\n    headers?: Headers | undefined\n    status?: number | undefined\n    url: string\n  }) {\n    super('HTTP request failed.', {\n      cause,\n      details,\n      metaMessages: [\n        status && `Status: ${status}`,\n        `URL: ${getUrl(url)}`,\n        body && `Request body: ${stringify(body)}`,\n      ].filter(Boolean) as string[],\n      name: 'HttpRequestError',\n    })\n    this.body = body\n    this.headers = headers\n    this.status = status\n    this.url = url\n  }\n}\n\nexport type WebSocketRequestErrorType = WebSocketRequestError & {\n  name: 'WebSocketRequestError'\n}\nexport class WebSocketRequestError extends BaseError {\n  constructor({\n    body,\n    cause,\n    details,\n    url,\n  }: {\n    body?: { [key: string]: unknown } | undefined\n    cause?: Error | undefined\n    details?: string | undefined\n    url: string\n  }) {\n    super('WebSocket request failed.', {\n      cause,\n      details,\n      metaMessages: [\n        `URL: ${getUrl(url)}`,\n        body && `Request body: ${stringify(body)}`,\n      ].filter(Boolean) as string[],\n      name: 'WebSocketRequestError',\n    })\n  }\n}\n\nexport type RpcRequestErrorType = RpcRequestError & {\n  name: 'RpcRequestError'\n}\nexport class RpcRequestError extends BaseError {\n  code: number\n  data?: unknown\n\n  constructor({\n    body,\n    error,\n    url,\n  }: {\n    body: { [x: string]: unknown } | { [y: string]: unknown }[]\n    error: { code: number; data?: unknown; message: string }\n    url: string\n  }) {\n    super('RPC Request failed.', {\n      cause: error as any,\n      details: error.message,\n      metaMessages: [`URL: ${getUrl(url)}`, `Request body: ${stringify(body)}`],\n      name: 'RpcRequestError',\n    })\n    this.code = error.code\n    this.data = error.data\n  }\n}\n\nexport type SocketClosedErrorType = SocketClosedError & {\n  name: 'SocketClosedError'\n}\nexport class SocketClosedError extends BaseError {\n  constructor({\n    url,\n  }: {\n    url?: string | undefined\n  } = {}) {\n    super('The socket has been closed.', {\n      metaMessages: [url && `URL: ${getUrl(url)}`].filter(Boolean) as string[],\n      name: 'SocketClosedError',\n    })\n  }\n}\n\nexport type TimeoutErrorType = TimeoutError & {\n  name: 'TimeoutError'\n}\nexport class TimeoutError extends BaseError {\n  constructor({\n    body,\n    url,\n  }: {\n    body: { [x: string]: unknown } | { [y: string]: unknown }[]\n    url: string\n  }) {\n    super('The request took too long to respond.', {\n      details: 'The request timed out.',\n      metaMessages: [`URL: ${getUrl(url)}`, `Request body: ${stringify(body)}`],\n      name: 'TimeoutError',\n    })\n  }\n}\n", "import type { Prettify } from '../types/utils.js'\nimport { BaseError } from './base.js'\nimport { RpcRequestError } from './request.js'\n\nconst unknownErrorCode = -1\n\nexport type RpcErrorCode =\n  | -1\n  | -32700 // Parse error\n  | -32600 // Invalid request\n  | -32601 // Method not found\n  | -32602 // Invalid params\n  | -32603 // Internal error\n  | -32000 // Invalid input\n  | -32001 // Resource not found\n  | -32002 // Resource unavailable\n  | -32003 // Transaction rejected\n  | -32004 // Method not supported\n  | -32005 // Limit exceeded\n  | -32006 // JSON-RPC version not supported\n  | -32042 // Method not found\n\ntype RpcErrorOptions<code extends number = RpcErrorCode> = {\n  code?: code | (number & {}) | undefined\n  docsPath?: string | undefined\n  metaMessages?: string[] | undefined\n  name?: string | undefined\n  shortMessage: string\n}\n\n/**\n * Error subclass implementing JSON RPC 2.0 errors and Ethereum RPC errors per EIP-1474.\n *\n * - EIP https://eips.ethereum.org/EIPS/eip-1474\n */\nexport type RpcErrorType = RpcError & { name: 'RpcError' }\nexport class RpcError<code_ extends number = RpcErrorCode> extends BaseError {\n  code: code_ | (number & {})\n\n  constructor(\n    cause: Error,\n    {\n      code,\n      docsPath,\n      metaMessages,\n      name,\n      shortMessage,\n    }: RpcErrorOptions<code_>,\n  ) {\n    super(shortMessage, {\n      cause,\n      docsPath,\n      metaMessages:\n        metaMessages || (cause as { metaMessages?: string[] })?.metaMessages,\n      name: name || 'RpcError',\n    })\n    this.name = name || cause.name\n    this.code = (\n      cause instanceof RpcRequestError ? cause.code : (code ?? unknownErrorCode)\n    ) as code_\n  }\n}\n\nexport type ProviderRpcErrorCode =\n  | 4001 // User Rejected Request\n  | 4100 // Unauthorized\n  | 4200 // Unsupported Method\n  | 4900 // Disconnected\n  | 4901 // Chain Disconnected\n  | 4902 // Chain Not Recognized\n\n/**\n * Error subclass implementing Ethereum Provider errors per EIP-1193.\n *\n * - EIP https://eips.ethereum.org/EIPS/eip-1193\n */\nexport type ProviderRpcErrorType = ProviderRpcError & {\n  name: 'ProviderRpcError'\n}\nexport class ProviderRpcError<\n  T = undefined,\n> extends RpcError<ProviderRpcErrorCode> {\n  data?: T | undefined\n\n  constructor(\n    cause: Error,\n    options: Prettify<\n      RpcErrorOptions<ProviderRpcErrorCode> & {\n        data?: T | undefined\n      }\n    >,\n  ) {\n    super(cause, options)\n\n    this.data = options.data\n  }\n}\n\n/**\n * Subclass for a \"Parse error\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type ParseRpcErrorType = ParseRpcError & {\n  code: -32700\n  name: 'ParseRpcError'\n}\nexport class ParseRpcError extends RpcError {\n  static code = -32700 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ParseRpcError.code,\n      name: 'ParseRpcError',\n      shortMessage:\n        'Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Invalid request\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type InvalidRequestRpcErrorType = InvalidRequestRpcError & {\n  code: -32600\n  name: 'InvalidRequestRpcError'\n}\nexport class InvalidRequestRpcError extends RpcError {\n  static code = -32600 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: InvalidRequestRpcError.code,\n      name: 'InvalidRequestRpcError',\n      shortMessage: 'JSON is not a valid request object.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Method not found\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type MethodNotFoundRpcErrorType = MethodNotFoundRpcError & {\n  code: -32601\n  name: 'MethodNotFoundRpcError'\n}\nexport class MethodNotFoundRpcError extends RpcError {\n  static code = -32601 as const\n\n  constructor(cause: Error, { method }: { method?: string } = {}) {\n    super(cause, {\n      code: MethodNotFoundRpcError.code,\n      name: 'MethodNotFoundRpcError',\n      shortMessage: `The method${method ? ` \"${method}\"` : ''} does not exist / is not available.`,\n    })\n  }\n}\n\n/**\n * Subclass for an \"Invalid params\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type InvalidParamsRpcErrorType = InvalidParamsRpcError & {\n  code: -32602\n  name: 'InvalidParamsRpcError'\n}\nexport class InvalidParamsRpcError extends RpcError {\n  static code = -32602 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: InvalidParamsRpcError.code,\n      name: 'InvalidParamsRpcError',\n      shortMessage: [\n        'Invalid parameters were provided to the RPC method.',\n        'Double check you have provided the correct parameters.',\n      ].join('\\n'),\n    })\n  }\n}\n\n/**\n * Subclass for an \"Internal error\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type InternalRpcErrorType = InternalRpcError & {\n  code: -32603\n  name: 'InternalRpcError'\n}\nexport class InternalRpcError extends RpcError {\n  static code = -32603 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: InternalRpcError.code,\n      name: 'InternalRpcError',\n      shortMessage: 'An internal error was received.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Invalid input\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type InvalidInputRpcErrorType = InvalidInputRpcError & {\n  code: -32000\n  name: 'InvalidInputRpcError'\n}\nexport class InvalidInputRpcError extends RpcError {\n  static code = -32000 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: InvalidInputRpcError.code,\n      name: 'InvalidInputRpcError',\n      shortMessage: [\n        'Missing or invalid parameters.',\n        'Double check you have provided the correct parameters.',\n      ].join('\\n'),\n    })\n  }\n}\n\n/**\n * Subclass for a \"Resource not found\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type ResourceNotFoundRpcErrorType = ResourceNotFoundRpcError & {\n  code: -32001\n  name: 'ResourceNotFoundRpcError'\n}\nexport class ResourceNotFoundRpcError extends RpcError {\n  override name = 'ResourceNotFoundRpcError'\n  static code = -32001 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ResourceNotFoundRpcError.code,\n      name: 'ResourceNotFoundRpcError',\n      shortMessage: 'Requested resource not found.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Resource unavailable\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type ResourceUnavailableRpcErrorType = ResourceUnavailableRpcError & {\n  code: -32002\n  name: 'ResourceUnavailableRpcError'\n}\nexport class ResourceUnavailableRpcError extends RpcError {\n  static code = -32002 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ResourceUnavailableRpcError.code,\n      name: 'ResourceUnavailableRpcError',\n      shortMessage: 'Requested resource not available.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Transaction rejected\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type TransactionRejectedRpcErrorType = TransactionRejectedRpcError & {\n  code: -32003\n  name: 'TransactionRejectedRpcError'\n}\nexport class TransactionRejectedRpcError extends RpcError {\n  static code = -32003 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: TransactionRejectedRpcError.code,\n      name: 'TransactionRejectedRpcError',\n      shortMessage: 'Transaction creation failed.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"Method not supported\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type MethodNotSupportedRpcErrorType = MethodNotSupportedRpcError & {\n  code: -32004\n  name: 'MethodNotSupportedRpcError'\n}\nexport class MethodNotSupportedRpcError extends RpcError {\n  static code = -32004 as const\n\n  constructor(cause: Error, { method }: { method?: string } = {}) {\n    super(cause, {\n      code: MethodNotSupportedRpcError.code,\n      name: 'MethodNotSupportedRpcError',\n      shortMessage: `Method${method ? ` \"${method}\"` : ''} is not supported.`,\n    })\n  }\n}\n\n/**\n * Subclass for a \"Limit exceeded\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type LimitExceededRpcErrorType = LimitExceededRpcError & {\n  code: -32005\n  name: 'LimitExceededRpcError'\n}\nexport class LimitExceededRpcError extends RpcError {\n  static code = -32005 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: LimitExceededRpcError.code,\n      name: 'LimitExceededRpcError',\n      shortMessage: 'Request exceeds defined limit.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"JSON-RPC version not supported\" EIP-1474 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1474#error-codes\n */\nexport type JsonRpcVersionUnsupportedErrorType =\n  JsonRpcVersionUnsupportedError & {\n    code: -32006\n    name: 'JsonRpcVersionUnsupportedError'\n  }\nexport class JsonRpcVersionUnsupportedError extends RpcError {\n  static code = -32006 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: JsonRpcVersionUnsupportedError.code,\n      name: 'JsonRpcVersionUnsupportedError',\n      shortMessage: 'Version of JSON-RPC protocol is not supported.',\n    })\n  }\n}\n\n/**\n * Subclass for a \"User Rejected Request\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type UserRejectedRequestErrorType = UserRejectedRequestError & {\n  code: 4001\n  name: 'UserRejectedRequestError'\n}\nexport class UserRejectedRequestError extends ProviderRpcError {\n  static code = 4001 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: UserRejectedRequestError.code,\n      name: 'UserRejectedRequestError',\n      shortMessage: 'User rejected the request.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Unauthorized\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type UnauthorizedProviderErrorType = UnauthorizedProviderError & {\n  code: 4100\n  name: 'UnauthorizedProviderError'\n}\nexport class UnauthorizedProviderError extends ProviderRpcError {\n  static code = 4100 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: UnauthorizedProviderError.code,\n      name: 'UnauthorizedProviderError',\n      shortMessage:\n        'The requested method and/or account has not been authorized by the user.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Unsupported Method\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type UnsupportedProviderMethodErrorType =\n  UnsupportedProviderMethodError & {\n    code: 4200\n    name: 'UnsupportedProviderMethodError'\n  }\nexport class UnsupportedProviderMethodError extends ProviderRpcError {\n  static code = 4200 as const\n\n  constructor(cause: Error, { method }: { method?: string } = {}) {\n    super(cause, {\n      code: UnsupportedProviderMethodError.code,\n      name: 'UnsupportedProviderMethodError',\n      shortMessage: `The Provider does not support the requested method${method ? ` \" ${method}\"` : ''}.`,\n    })\n  }\n}\n\n/**\n * Subclass for an \"Disconnected\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type ProviderDisconnectedErrorType = ProviderDisconnectedError & {\n  code: 4900\n  name: 'ProviderDisconnectedError'\n}\nexport class ProviderDisconnectedError extends ProviderRpcError {\n  static code = 4900 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ProviderDisconnectedError.code,\n      name: 'ProviderDisconnectedError',\n      shortMessage: 'The Provider is disconnected from all chains.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Chain Disconnected\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type ChainDisconnectedErrorType = ChainDisconnectedError & {\n  code: 4901\n  name: 'ChainDisconnectedError'\n}\nexport class ChainDisconnectedError extends ProviderRpcError {\n  static code = 4901 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: ChainDisconnectedError.code,\n      name: 'ChainDisconnectedError',\n      shortMessage: 'The Provider is not connected to the requested chain.',\n    })\n  }\n}\n\n/**\n * Subclass for an \"Switch Chain\" EIP-1193 error.\n *\n * EIP https://eips.ethereum.org/EIPS/eip-1193#provider-errors\n */\nexport type SwitchChainErrorType = SwitchChainError & {\n  code: 4902\n  name: 'SwitchChainError'\n}\nexport class SwitchChainError extends ProviderRpcError {\n  static code = 4902 as const\n\n  constructor(cause: Error) {\n    super(cause, {\n      code: SwitchChainError.code,\n      name: 'SwitchChainError',\n      shortMessage: 'An error occurred when attempting to switch chain.',\n    })\n  }\n}\n\n/**\n * Subclass for an unknown RPC error.\n */\nexport type UnknownRpcErrorType = UnknownRpcError & {\n  name: 'UnknownRpcError'\n}\nexport class UnknownRpcError extends RpcError {\n  constructor(cause: Error) {\n    super(cause, {\n      name: 'UnknownRpcError',\n      shortMessage: 'An unknown RPC error occurred.',\n    })\n  }\n}\n", "import type { SendTransactionParameters } from '../../actions/wallet/sendTransaction.js'\nimport { BaseError } from '../../errors/base.js'\nimport {\n  ExecutionRevertedError,\n  type ExecutionRevertedErrorType,\n  FeeCapTooHighError,\n  type FeeCapTooHighErrorType,\n  FeeCapTooLowError,\n  type FeeCapTooLowErrorType,\n  InsufficientFundsError,\n  type InsufficientFundsErrorType,\n  IntrinsicGasTooHighError,\n  type IntrinsicGasTooHighErrorType,\n  IntrinsicGasTooLowError,\n  type IntrinsicGasTooLowErrorType,\n  NonceMaxValueError,\n  type NonceMaxValueErrorType,\n  NonceTooHighError,\n  type NonceTooHighErrorType,\n  NonceTooLowError,\n  type NonceTooLowErrorType,\n  TipAboveFeeCapError,\n  type TipAboveFeeCapErrorType,\n  TransactionTypeNotSupportedError,\n  type TransactionTypeNotSupportedErrorType,\n  UnknownNodeError,\n  type UnknownNodeErrorType,\n} from '../../errors/node.js'\nimport { RpcRequestError } from '../../errors/request.js'\nimport {\n  InvalidInputRpcError,\n  TransactionRejectedRpcError,\n} from '../../errors/rpc.js'\nimport type { ExactPartial } from '../../types/utils.js'\n\nexport function containsNodeError(err: BaseError) {\n  return (\n    err instanceof TransactionRejectedRpcError ||\n    err instanceof InvalidInputRpcError ||\n    (err instanceof RpcRequestError && err.code === ExecutionRevertedError.code)\n  )\n}\n\nexport type GetNodeErrorParameters = ExactPartial<\n  SendTransactionParameters<any>\n>\n\nexport type GetNodeErrorReturnType =\n  | ExecutionRevertedErrorType\n  | FeeCapTooHighErrorType\n  | FeeCapTooLowErrorType\n  | NonceTooHighErrorType\n  | NonceTooLowErrorType\n  | NonceMaxValueErrorType\n  | InsufficientFundsErrorType\n  | IntrinsicGasTooHighErrorType\n  | IntrinsicGasTooLowErrorType\n  | TransactionTypeNotSupportedErrorType\n  | TipAboveFeeCapErrorType\n  | UnknownNodeErrorType\n\nexport function getNodeError(\n  err: BaseError,\n  args: GetNodeErrorParameters,\n): GetNodeErrorReturnType {\n  const message = (err.details || '').toLowerCase()\n\n  const executionRevertedError =\n    err instanceof BaseError\n      ? err.walk(\n          (e) =>\n            (e as { code: number } | null | undefined)?.code ===\n            ExecutionRevertedError.code,\n        )\n      : err\n  if (executionRevertedError instanceof BaseError)\n    return new ExecutionRevertedError({\n      cause: err,\n      message: executionRevertedError.details,\n    }) as any\n  if (ExecutionRevertedError.nodeMessage.test(message))\n    return new ExecutionRevertedError({\n      cause: err,\n      message: err.details,\n    }) as any\n  if (FeeCapTooHighError.nodeMessage.test(message))\n    return new FeeCapTooHighError({\n      cause: err,\n      maxFeePerGas: args?.maxFeePerGas,\n    }) as any\n  if (FeeCapTooLowError.nodeMessage.test(message))\n    return new FeeCapTooLowError({\n      cause: err,\n      maxFeePerGas: args?.maxFeePerGas,\n    }) as any\n  if (NonceTooHighError.nodeMessage.test(message))\n    return new NonceTooHighError({ cause: err, nonce: args?.nonce }) as any\n  if (NonceTooLowError.nodeMessage.test(message))\n    return new NonceTooLowError({ cause: err, nonce: args?.nonce }) as any\n  if (NonceMaxValueError.nodeMessage.test(message))\n    return new NonceMaxValueError({ cause: err, nonce: args?.nonce }) as any\n  if (InsufficientFundsError.nodeMessage.test(message))\n    return new InsufficientFundsError({ cause: err }) as any\n  if (IntrinsicGasTooHighError.nodeMessage.test(message))\n    return new IntrinsicGasTooHighError({ cause: err, gas: args?.gas }) as any\n  if (IntrinsicGasTooLowError.nodeMessage.test(message))\n    return new IntrinsicGasTooLowError({ cause: err, gas: args?.gas }) as any\n  if (TransactionTypeNotSupportedError.nodeMessage.test(message))\n    return new TransactionTypeNotSupportedError({ cause: err }) as any\n  if (TipAboveFeeCapError.nodeMessage.test(message))\n    return new TipAboveFeeCapError({\n      cause: err,\n      maxFeePerGas: args?.maxFeePerGas,\n      maxPriorityFeePerGas: args?.maxPriorityFeePerGas,\n    }) as any\n  return new UnknownNodeError({\n    cause: err,\n  }) as any\n}\n", "import type { CallParameters } from '../../actions/public/call.js'\nimport type { BaseError } from '../../errors/base.js'\nimport {\n  CallExecutionError,\n  type CallExecutionErrorType,\n} from '../../errors/contract.js'\nimport { UnknownNodeError } from '../../errors/node.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\n\nimport {\n  type GetNodeErrorParameters,\n  type GetNodeErrorReturnType,\n  getNodeError,\n} from './getNodeError.js'\n\nexport type GetCallErrorReturnType<cause = ErrorType> = Omit<\n  CallExecutionErrorType,\n  'cause'\n> & {\n  cause: cause | GetNodeErrorReturnType\n}\n\nexport function getCallError<err extends ErrorType<string>>(\n  err: err,\n  {\n    docsPath,\n    ...args\n  }: CallParameters & {\n    chain?: Chain | undefined\n    docsPath?: string | undefined\n  },\n): GetCallErrorReturnType<err> {\n  const cause = (() => {\n    const cause = getNodeError(\n      err as {} as BaseError,\n      args as GetNodeErrorParameters,\n    )\n    if (cause instanceof UnknownNodeError) return err as {} as BaseError\n    return cause\n  })()\n  return new CallExecutionError(cause, {\n    docsPath,\n    ...args,\n  }) as GetCallErrorReturnType<err>\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ChainFormatter } from '../../types/chain.js'\n\nexport type ExtractErrorType = ErrorType\n\n/**\n * @description Picks out the keys from `value` that exist in the formatter..\n */\nexport function extract(\n  value_: Record<string, unknown>,\n  { format }: { format?: ChainFormatter['format'] | undefined },\n) {\n  if (!format) return {}\n\n  const value: Record<string, unknown> = {}\n  function extract_(formatted: Record<string, any>) {\n    const keys = Object.keys(formatted)\n    for (const key of keys) {\n      if (key in value_) value[key] = value_[key]\n      if (\n        formatted[key] &&\n        typeof formatted[key] === 'object' &&\n        !Array.isArray(formatted[key])\n      )\n        extract_(formatted[key])\n    }\n  }\n\n  const formatted = format(value_ || {})\n  extract_(formatted)\n\n  return value\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Prettify } from '../../types/utils.js'\n\nexport type DefineFormatterErrorType = ErrorType\n\nexport function defineFormatter<type extends string, parameters, returnType>(\n  type: type,\n  format: (_: parameters) => returnType,\n) {\n  return <\n    parametersOverride,\n    returnTypeOverride,\n    exclude extends (keyof parameters | keyof parametersOverride)[] = [],\n  >({\n    exclude,\n    format: overrides,\n  }: {\n    exclude?: exclude | undefined\n    format: (_: parametersOverride) => returnTypeOverride\n  }) => {\n    return {\n      exclude,\n      format: (args: parametersOverride) => {\n        const formatted = format(args as any)\n        if (exclude) {\n          for (const key of exclude) {\n            delete (formatted as any)[key]\n          }\n        }\n        return {\n          ...formatted,\n          ...overrides(args),\n        } as Prettify<returnTypeOverride> & {\n          [_key in exclude[number]]: never\n        }\n      },\n      type,\n    }\n  }\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { AuthorizationList } from '../../experimental/eip7702/types/authorization.js'\nimport type { RpcAuthorizationList } from '../../experimental/eip7702/types/rpc.js'\nimport type {\n  Chain,\n  ExtractChainFormatterParameters,\n} from '../../types/chain.js'\nimport type { ByteArray } from '../../types/misc.js'\nimport type { RpcTransactionRequest } from '../../types/rpc.js'\nimport type { TransactionRequest } from '../../types/transaction.js'\nimport type { ExactPartial } from '../../types/utils.js'\nimport { bytesToHex, numberToHex } from '../encoding/toHex.js'\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\n\nexport type FormattedTransactionRequest<\n  chain extends Chain | undefined = Chain | undefined,\n> = ExtractChainFormatterParameters<\n  chain,\n  'transactionRequest',\n  TransactionRequest\n>\n\nexport const rpcTransactionType = {\n  legacy: '0x0',\n  eip2930: '0x1',\n  eip1559: '0x2',\n  eip4844: '0x3',\n  eip7702: '0x4',\n} as const\n\nexport type FormatTransactionRequestErrorType = ErrorType\n\nexport function formatTransactionRequest(\n  request: ExactPartial<TransactionRequest>,\n) {\n  const rpcRequest = {} as RpcTransactionRequest\n\n  if (typeof request.authorizationList !== 'undefined')\n    rpcRequest.authorizationList = formatAuthorizationList(\n      request.authorizationList,\n    )\n  if (typeof request.accessList !== 'undefined')\n    rpcRequest.accessList = request.accessList\n  if (typeof request.blobVersionedHashes !== 'undefined')\n    rpcRequest.blobVersionedHashes = request.blobVersionedHashes\n  if (typeof request.blobs !== 'undefined') {\n    if (typeof request.blobs[0] !== 'string')\n      rpcRequest.blobs = (request.blobs as ByteArray[]).map((x) =>\n        bytesToHex(x),\n      )\n    else rpcRequest.blobs = request.blobs\n  }\n  if (typeof request.data !== 'undefined') rpcRequest.data = request.data\n  if (typeof request.from !== 'undefined') rpcRequest.from = request.from\n  if (typeof request.gas !== 'undefined')\n    rpcRequest.gas = numberToHex(request.gas)\n  if (typeof request.gasPrice !== 'undefined')\n    rpcRequest.gasPrice = numberToHex(request.gasPrice)\n  if (typeof request.maxFeePerBlobGas !== 'undefined')\n    rpcRequest.maxFeePerBlobGas = numberToHex(request.maxFeePerBlobGas)\n  if (typeof request.maxFeePerGas !== 'undefined')\n    rpcRequest.maxFeePerGas = numberToHex(request.maxFeePerGas)\n  if (typeof request.maxPriorityFeePerGas !== 'undefined')\n    rpcRequest.maxPriorityFeePerGas = numberToHex(request.maxPriorityFeePerGas)\n  if (typeof request.nonce !== 'undefined')\n    rpcRequest.nonce = numberToHex(request.nonce)\n  if (typeof request.to !== 'undefined') rpcRequest.to = request.to\n  if (typeof request.type !== 'undefined')\n    rpcRequest.type = rpcTransactionType[request.type]\n  if (typeof request.value !== 'undefined')\n    rpcRequest.value = numberToHex(request.value)\n\n  return rpcRequest\n}\n\nexport type DefineTransactionRequestErrorType =\n  | DefineFormatterErrorType\n  | ErrorType\n\nexport const defineTransactionRequest = /*#__PURE__*/ defineFormatter(\n  'transactionRequest',\n  formatTransactionRequest,\n)\n\n//////////////////////////////////////////////////////////////////////////////\n\nfunction formatAuthorizationList(\n  authorizationList: AuthorizationList<number, boolean>,\n): RpcAuthorizationList {\n  return authorizationList.map(\n    (authorization) =>\n      ({\n        address: authorization.contractAddress,\n        r: authorization.r,\n        s: authorization.s,\n        chainId: numberToHex(authorization.chainId),\n        nonce: numberToHex(authorization.nonce),\n        ...(typeof authorization.yParity !== 'undefined'\n          ? { yParity: numberToHex(authorization.yParity) }\n          : {}),\n        ...(typeof authorization.v !== 'undefined' &&\n        typeof authorization.yParity === 'undefined'\n          ? { v: numberToHex(authorization.v) }\n          : {}),\n      }) as any,\n  ) as RpcAuthorizationList\n}\n", "/** @internal */\nexport type PromiseWithResolvers<type> = {\n  promise: Promise<type>\n  resolve: (value: type | PromiseLike<type>) => void\n  reject: (reason?: unknown) => void\n}\n\n/** @internal */\nexport function withResolvers<type>(): PromiseWithResolvers<type> {\n  let resolve: PromiseWithResolvers<type>['resolve'] = () => undefined\n  let reject: PromiseWithResolvers<type>['reject'] = () => undefined\n\n  const promise = new Promise<type>((resolve_, reject_) => {\n    resolve = resolve_\n    reject = reject_\n  })\n\n  return { promise, resolve, reject }\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport { type PromiseWithResolvers, withResol<PERSON> } from './withResolvers.js'\n\ntype Resolved<returnType extends readonly unknown[] = any> = [\n  result: returnType[number],\n  results: returnType,\n]\n\ntype SchedulerItem = {\n  args: unknown\n  resolve: PromiseWithResolvers<unknown>['resolve']\n  reject: PromiseWithResolvers<unknown>['reject']\n}\n\ntype BatchResultsCompareFn<result = unknown> = (a: result, b: result) => number\n\ntype CreateBatchSchedulerArguments<\n  parameters = unknown,\n  returnType extends readonly unknown[] = readonly unknown[],\n> = {\n  fn: (args: parameters[]) => Promise<returnType>\n  id: number | string\n  shouldSplitBatch?: ((args: parameters[]) => boolean) | undefined\n  wait?: number | undefined\n  sort?: BatchResultsCompareFn<returnType[number]> | undefined\n}\n\ntype CreateBatchSchedulerReturnType<\n  parameters = unknown,\n  returnType extends readonly unknown[] = readonly unknown[],\n> = {\n  flush: () => void\n  schedule: parameters extends undefined\n    ? (args?: parameters | undefined) => Promise<Resolved<returnType>>\n    : (args: parameters) => Promise<Resolved<returnType>>\n}\n\nexport type CreateBatchSchedulerErrorType = ErrorType\n\nconst schedulerCache = /*#__PURE__*/ new Map<number | string, SchedulerItem[]>()\n\n/** @internal */\nexport function createBatchScheduler<\n  parameters,\n  returnType extends readonly unknown[],\n>({\n  fn,\n  id,\n  shouldSplitBatch,\n  wait = 0,\n  sort,\n}: CreateBatchSchedulerArguments<\n  parameters,\n  returnType\n>): CreateBatchSchedulerReturnType<parameters, returnType> {\n  const exec = async () => {\n    const scheduler = getScheduler()\n    flush()\n\n    const args = scheduler.map(({ args }) => args)\n\n    if (args.length === 0) return\n\n    fn(args as parameters[])\n      .then((data) => {\n        if (sort && Array.isArray(data)) data.sort(sort)\n        for (let i = 0; i < scheduler.length; i++) {\n          const { resolve } = scheduler[i]\n          resolve?.([data[i], data])\n        }\n      })\n      .catch((err) => {\n        for (let i = 0; i < scheduler.length; i++) {\n          const { reject } = scheduler[i]\n          reject?.(err)\n        }\n      })\n  }\n\n  const flush = () => schedulerCache.delete(id)\n\n  const getBatchedArgs = () =>\n    getScheduler().map(({ args }) => args) as parameters[]\n\n  const getScheduler = () => schedulerCache.get(id) || []\n\n  const setScheduler = (item: SchedulerItem) =>\n    schedulerCache.set(id, [...getScheduler(), item])\n\n  return {\n    flush,\n    async schedule(args: parameters) {\n      const { promise, resolve, reject } = withResolvers()\n\n      const split = shouldSplitBatch?.([...getBatchedArgs(), args])\n\n      if (split) exec()\n\n      const hasActiveScheduler = getScheduler().length > 0\n      if (hasActiveScheduler) {\n        setScheduler({ args, resolve, reject })\n        return promise\n      }\n\n      setScheduler({ args, resolve, reject })\n      setTimeout(exec, wait)\n      return promise\n    },\n  } as unknown as CreateBatchSchedulerReturnType<parameters, returnType>\n}\n", "import {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../errors/address.js'\nimport {\n  InvalidBytesLengthError,\n  type InvalidBytesLengthErrorType,\n} from '../errors/data.js'\nimport {\n  AccountStateConflictError,\n  type AccountStateConflictErrorType,\n  StateAssignmentConflictError,\n  type StateAssignmentConflictErrorType,\n} from '../errors/stateOverride.js'\nimport type {\n  RpcAccountStateOverride,\n  RpcStateMapping,\n  RpcStateOverride,\n} from '../types/rpc.js'\nimport type { StateMapping, StateOverride } from '../types/stateOverride.js'\nimport { isAddress } from './address/isAddress.js'\nimport { type NumberToHexErrorType, numberToHex } from './encoding/toHex.js'\n\ntype SerializeStateMappingParameters = StateMapping | undefined\n\ntype SerializeStateMappingErrorType = InvalidBytesLengthErrorType\n\n/** @internal */\nexport function serializeStateMapping(\n  stateMapping: SerializeStateMappingParameters,\n): RpcStateMapping | undefined {\n  if (!stateMapping || stateMapping.length === 0) return undefined\n  return stateMapping.reduce((acc, { slot, value }) => {\n    if (slot.length !== 66)\n      throw new InvalidBytesLengthError({\n        size: slot.length,\n        targetSize: 66,\n        type: 'hex',\n      })\n    if (value.length !== 66)\n      throw new InvalidBytesLengthError({\n        size: value.length,\n        targetSize: 66,\n        type: 'hex',\n      })\n    acc[slot] = value\n    return acc\n  }, {} as RpcStateMapping)\n}\n\ntype SerializeAccountStateOverrideParameters = Omit<\n  StateOverride[number],\n  'address'\n>\n\ntype SerializeAccountStateOverrideErrorType =\n  | NumberToHexErrorType\n  | StateAssignmentConflictErrorType\n  | SerializeStateMappingErrorType\n\n/** @internal */\nexport function serializeAccountStateOverride(\n  parameters: SerializeAccountStateOverrideParameters,\n): RpcAccountStateOverride {\n  const { balance, nonce, state, stateDiff, code } = parameters\n  const rpcAccountStateOverride: RpcAccountStateOverride = {}\n  if (code !== undefined) rpcAccountStateOverride.code = code\n  if (balance !== undefined)\n    rpcAccountStateOverride.balance = numberToHex(balance)\n  if (nonce !== undefined) rpcAccountStateOverride.nonce = numberToHex(nonce)\n  if (state !== undefined)\n    rpcAccountStateOverride.state = serializeStateMapping(state)\n  if (stateDiff !== undefined) {\n    if (rpcAccountStateOverride.state) throw new StateAssignmentConflictError()\n    rpcAccountStateOverride.stateDiff = serializeStateMapping(stateDiff)\n  }\n  return rpcAccountStateOverride\n}\n\ntype SerializeStateOverrideParameters = StateOverride | undefined\n\nexport type SerializeStateOverrideErrorType =\n  | InvalidAddressErrorType\n  | AccountStateConflictErrorType\n  | SerializeAccountStateOverrideErrorType\n\n/** @internal */\nexport function serializeStateOverride(\n  parameters?: SerializeStateOverrideParameters,\n): RpcStateOverride | undefined {\n  if (!parameters) return undefined\n  const rpcStateOverride: RpcStateOverride = {}\n  for (const { address, ...accountState } of parameters) {\n    if (!isAddress(address, { strict: false }))\n      throw new InvalidAddressError({ address })\n    if (rpcStateOverride[address])\n      throw new AccountStateConflictError({ address: address })\n    rpcStateOverride[address] = serializeAccountStateOverride(accountState)\n  }\n  return rpcStateOverride\n}\n", "export const maxInt8 = 2n ** (8n - 1n) - 1n\nexport const maxInt16 = 2n ** (16n - 1n) - 1n\nexport const maxInt24 = 2n ** (24n - 1n) - 1n\nexport const maxInt32 = 2n ** (32n - 1n) - 1n\nexport const maxInt40 = 2n ** (40n - 1n) - 1n\nexport const maxInt48 = 2n ** (48n - 1n) - 1n\nexport const maxInt56 = 2n ** (56n - 1n) - 1n\nexport const maxInt64 = 2n ** (64n - 1n) - 1n\nexport const maxInt72 = 2n ** (72n - 1n) - 1n\nexport const maxInt80 = 2n ** (80n - 1n) - 1n\nexport const maxInt88 = 2n ** (88n - 1n) - 1n\nexport const maxInt96 = 2n ** (96n - 1n) - 1n\nexport const maxInt104 = 2n ** (104n - 1n) - 1n\nexport const maxInt112 = 2n ** (112n - 1n) - 1n\nexport const maxInt120 = 2n ** (120n - 1n) - 1n\nexport const maxInt128 = 2n ** (128n - 1n) - 1n\nexport const maxInt136 = 2n ** (136n - 1n) - 1n\nexport const maxInt144 = 2n ** (144n - 1n) - 1n\nexport const maxInt152 = 2n ** (152n - 1n) - 1n\nexport const maxInt160 = 2n ** (160n - 1n) - 1n\nexport const maxInt168 = 2n ** (168n - 1n) - 1n\nexport const maxInt176 = 2n ** (176n - 1n) - 1n\nexport const maxInt184 = 2n ** (184n - 1n) - 1n\nexport const maxInt192 = 2n ** (192n - 1n) - 1n\nexport const maxInt200 = 2n ** (200n - 1n) - 1n\nexport const maxInt208 = 2n ** (208n - 1n) - 1n\nexport const maxInt216 = 2n ** (216n - 1n) - 1n\nexport const maxInt224 = 2n ** (224n - 1n) - 1n\nexport const maxInt232 = 2n ** (232n - 1n) - 1n\nexport const maxInt240 = 2n ** (240n - 1n) - 1n\nexport const maxInt248 = 2n ** (248n - 1n) - 1n\nexport const maxInt256 = 2n ** (256n - 1n) - 1n\n\nexport const minInt8 = -(2n ** (8n - 1n))\nexport const minInt16 = -(2n ** (16n - 1n))\nexport const minInt24 = -(2n ** (24n - 1n))\nexport const minInt32 = -(2n ** (32n - 1n))\nexport const minInt40 = -(2n ** (40n - 1n))\nexport const minInt48 = -(2n ** (48n - 1n))\nexport const minInt56 = -(2n ** (56n - 1n))\nexport const minInt64 = -(2n ** (64n - 1n))\nexport const minInt72 = -(2n ** (72n - 1n))\nexport const minInt80 = -(2n ** (80n - 1n))\nexport const minInt88 = -(2n ** (88n - 1n))\nexport const minInt96 = -(2n ** (96n - 1n))\nexport const minInt104 = -(2n ** (104n - 1n))\nexport const minInt112 = -(2n ** (112n - 1n))\nexport const minInt120 = -(2n ** (120n - 1n))\nexport const minInt128 = -(2n ** (128n - 1n))\nexport const minInt136 = -(2n ** (136n - 1n))\nexport const minInt144 = -(2n ** (144n - 1n))\nexport const minInt152 = -(2n ** (152n - 1n))\nexport const minInt160 = -(2n ** (160n - 1n))\nexport const minInt168 = -(2n ** (168n - 1n))\nexport const minInt176 = -(2n ** (176n - 1n))\nexport const minInt184 = -(2n ** (184n - 1n))\nexport const minInt192 = -(2n ** (192n - 1n))\nexport const minInt200 = -(2n ** (200n - 1n))\nexport const minInt208 = -(2n ** (208n - 1n))\nexport const minInt216 = -(2n ** (216n - 1n))\nexport const minInt224 = -(2n ** (224n - 1n))\nexport const minInt232 = -(2n ** (232n - 1n))\nexport const minInt240 = -(2n ** (240n - 1n))\nexport const minInt248 = -(2n ** (248n - 1n))\nexport const minInt256 = -(2n ** (256n - 1n))\n\nexport const maxUint8 = 2n ** 8n - 1n\nexport const maxUint16 = 2n ** 16n - 1n\nexport const maxUint24 = 2n ** 24n - 1n\nexport const maxUint32 = 2n ** 32n - 1n\nexport const maxUint40 = 2n ** 40n - 1n\nexport const maxUint48 = 2n ** 48n - 1n\nexport const maxUint56 = 2n ** 56n - 1n\nexport const maxUint64 = 2n ** 64n - 1n\nexport const maxUint72 = 2n ** 72n - 1n\nexport const maxUint80 = 2n ** 80n - 1n\nexport const maxUint88 = 2n ** 88n - 1n\nexport const maxUint96 = 2n ** 96n - 1n\nexport const maxUint104 = 2n ** 104n - 1n\nexport const maxUint112 = 2n ** 112n - 1n\nexport const maxUint120 = 2n ** 120n - 1n\nexport const maxUint128 = 2n ** 128n - 1n\nexport const maxUint136 = 2n ** 136n - 1n\nexport const maxUint144 = 2n ** 144n - 1n\nexport const maxUint152 = 2n ** 152n - 1n\nexport const maxUint160 = 2n ** 160n - 1n\nexport const maxUint168 = 2n ** 168n - 1n\nexport const maxUint176 = 2n ** 176n - 1n\nexport const maxUint184 = 2n ** 184n - 1n\nexport const maxUint192 = 2n ** 192n - 1n\nexport const maxUint200 = 2n ** 200n - 1n\nexport const maxUint208 = 2n ** 208n - 1n\nexport const maxUint216 = 2n ** 216n - 1n\nexport const maxUint224 = 2n ** 224n - 1n\nexport const maxUint232 = 2n ** 232n - 1n\nexport const maxUint240 = 2n ** 240n - 1n\nexport const maxUint248 = 2n ** 248n - 1n\nexport const maxUint256 = 2n ** 256n - 1n\n", "import {\n  type ParseAccountErrorType,\n  parseAccount,\n} from '../../accounts/utils/parseAccount.js'\nimport type { SendTransactionParameters } from '../../actions/wallet/sendTransaction.js'\nimport { maxUint256 } from '../../constants/number.js'\nimport {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../../errors/address.js'\nimport {\n  FeeCapTooHighError,\n  type FeeCapTooHighErrorType,\n  TipAboveFeeCapError,\n  type TipAboveFeeCapErrorType,\n} from '../../errors/node.js'\nimport {\n  FeeConflictError,\n  type FeeConflictErrorType,\n} from '../../errors/transaction.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { ExactPartial } from '../../types/utils.js'\nimport { isAddress } from '../address/isAddress.js'\n\nexport type AssertRequestParameters = ExactPartial<\n  SendTransactionParameters<Chain>\n>\n\nexport type AssertRequestErrorType =\n  | InvalidAddressErrorType\n  | FeeConflictErrorType\n  | FeeCapTooHighErrorType\n  | ParseAccountErrorType\n  | TipAboveFeeCapErrorType\n  | ErrorType\n\nexport function assertRequest(args: AssertRequestParameters) {\n  const {\n    account: account_,\n    gasPrice,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    to,\n  } = args\n  const account = account_ ? parseAccount(account_) : undefined\n  if (account && !isAddress(account.address))\n    throw new InvalidAddressError({ address: account.address })\n  if (to && !isAddress(to)) throw new InvalidAddressError({ address: to })\n  if (\n    typeof gasPrice !== 'undefined' &&\n    (typeof maxFeePerGas !== 'undefined' ||\n      typeof maxPriorityFeePerGas !== 'undefined')\n  )\n    throw new FeeConflictError()\n\n  if (maxFeePerGas && maxFeePerGas > maxUint256)\n    throw new FeeCapTooHighError({ maxFeePerGas })\n  if (\n    maxPriorityFeePerGas &&\n    maxFeePerGas &&\n    maxPriorityFeePerGas > maxFeePerGas\n  )\n    throw new TipAboveFeeCapError({ maxFeePerGas, maxPriorityFeePerGas })\n}\n", "import { type Address, parseAbi } from 'abitype'\n\nimport type { Account } from '../../accounts/types.js'\nimport {\n  type ParseAccountErrorType,\n  parseAccount,\n} from '../../accounts/utils/parseAccount.js'\nimport type { Client } from '../../clients/createClient.js'\nimport type { Transport } from '../../clients/transports/createTransport.js'\nimport { multicall3Abi } from '../../constants/abis.js'\nimport { aggregate3Signature } from '../../constants/contract.js'\nimport {\n  deploylessCallViaBytecodeBytecode,\n  deploylessCallViaFactoryBytecode,\n} from '../../constants/contracts.js'\nimport { BaseError } from '../../errors/base.js'\nimport {\n  ChainDoesNotSupportContract,\n  ClientChainNotConfiguredError,\n} from '../../errors/chain.js'\nimport {\n  CounterfactualDeploymentFailedError,\n  RawContractError,\n  type RawContractErrorType,\n} from '../../errors/contract.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { RpcTransactionRequest } from '../../types/rpc.js'\nimport type { StateOverride } from '../../types/stateOverride.js'\nimport type { TransactionRequest } from '../../types/transaction.js'\nimport type { ExactPartial, UnionOmit } from '../../types/utils.js'\nimport {\n  type DecodeFunctionResultErrorType,\n  decodeFunctionResult,\n} from '../../utils/abi/decodeFunctionResult.js'\nimport {\n  type EncodeDeployDataErrorType,\n  encodeDeployData,\n} from '../../utils/abi/encodeDeployData.js'\nimport {\n  type EncodeFunctionDataErrorType,\n  encodeFunctionData,\n} from '../../utils/abi/encodeFunctionData.js'\nimport type { RequestErrorType } from '../../utils/buildRequest.js'\nimport {\n  type GetChainContractAddressErrorType,\n  getChainContractAddress,\n} from '../../utils/chain/getChainContractAddress.js'\nimport {\n  type NumberToHexErrorType,\n  numberToHex,\n} from '../../utils/encoding/toHex.js'\nimport {\n  type GetCallErrorReturnType,\n  getCallError,\n} from '../../utils/errors/getCallError.js'\nimport { extract } from '../../utils/formatters/extract.js'\nimport {\n  type FormatTransactionRequestErrorType,\n  type FormattedTransactionRequest,\n  formatTransactionRequest,\n} from '../../utils/formatters/transactionRequest.js'\nimport {\n  type CreateBatchSchedulerErrorType,\n  createBatchScheduler,\n} from '../../utils/promise/createBatchScheduler.js'\nimport {\n  type SerializeStateOverrideErrorType,\n  serializeStateOverride,\n} from '../../utils/stateOverride.js'\nimport { assertRequest } from '../../utils/transaction/assertRequest.js'\nimport type {\n  AssertRequestErrorType,\n  AssertRequestParameters,\n} from '../../utils/transaction/assertRequest.js'\n\nexport type CallParameters<\n  chain extends Chain | undefined = Chain | undefined,\n> = UnionOmit<FormattedCall<chain>, 'from'> & {\n  /** Account attached to the call (msg.sender). */\n  account?: Account | Address | undefined\n  /** Whether or not to enable multicall batching on this call. */\n  batch?: boolean | undefined\n  /** Bytecode to perform the call on. */\n  code?: Hex | undefined\n  /** Contract deployment factory address (ie. Create2 factory, Smart Account factory, etc). */\n  factory?: Address | undefined\n  /** Calldata to execute on the factory to deploy the contract. */\n  factoryData?: Hex | undefined\n  /** State overrides for the call. */\n  stateOverride?: StateOverride | undefined\n} & (\n    | {\n        /** The balance of the account at a block number. */\n        blockNumber?: bigint | undefined\n        blockTag?: undefined\n      }\n    | {\n        blockNumber?: undefined\n        /**\n         * The balance of the account at a block tag.\n         * @default 'latest'\n         */\n        blockTag?: BlockTag | undefined\n      }\n  )\ntype FormattedCall<chain extends Chain | undefined = Chain | undefined> =\n  FormattedTransactionRequest<chain>\n\nexport type CallReturnType = { data: Hex | undefined }\n\nexport type CallErrorType = GetCallErrorReturnType<\n  | ParseAccountErrorType\n  | SerializeStateOverrideErrorType\n  | AssertRequestErrorType\n  | NumberToHexErrorType\n  | FormatTransactionRequestErrorType\n  | ScheduleMulticallErrorType\n  | RequestErrorType\n  | ToDeploylessCallViaBytecodeDataErrorType\n  | ToDeploylessCallViaFactoryDataErrorType\n>\n\n/**\n * Executes a new message call immediately without submitting a transaction to the network.\n *\n * - Docs: https://viem.sh/docs/actions/public/call\n * - JSON-RPC Methods: [`eth_call`](https://ethereum.org/en/developers/docs/apis/json-rpc/#eth_call)\n *\n * @param client - Client to use\n * @param parameters - {@link CallParameters}\n * @returns The call data. {@link CallReturnType}\n *\n * @example\n * import { createPublicClient, http } from 'viem'\n * import { mainnet } from 'viem/chains'\n * import { call } from 'viem/public'\n *\n * const client = createPublicClient({\n *   chain: mainnet,\n *   transport: http(),\n * })\n * const data = await call(client, {\n *   account: '******************************************',\n *   data: '******************************************',\n *   to: '******************************************',\n * })\n */\nexport async function call<chain extends Chain | undefined>(\n  client: Client<Transport, chain>,\n  args: CallParameters<chain>,\n): Promise<CallReturnType> {\n  const {\n    account: account_ = client.account,\n    batch = Boolean(client.batch?.multicall),\n    blockNumber,\n    blockTag = 'latest',\n    accessList,\n    blobs,\n    code,\n    data: data_,\n    factory,\n    factoryData,\n    gas,\n    gasPrice,\n    maxFeePerBlobGas,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    nonce,\n    to,\n    value,\n    stateOverride,\n    ...rest\n  } = args\n  const account = account_ ? parseAccount(account_) : undefined\n\n  if (code && (factory || factoryData))\n    throw new BaseError(\n      'Cannot provide both `code` & `factory`/`factoryData` as parameters.',\n    )\n  if (code && to)\n    throw new BaseError('Cannot provide both `code` & `to` as parameters.')\n\n  // Check if the call is deployless via bytecode.\n  const deploylessCallViaBytecode = code && data_\n  // Check if the call is deployless via a factory.\n  const deploylessCallViaFactory = factory && factoryData && to && data_\n  const deploylessCall = deploylessCallViaBytecode || deploylessCallViaFactory\n\n  const data = (() => {\n    if (deploylessCallViaBytecode)\n      return toDeploylessCallViaBytecodeData({\n        code,\n        data: data_,\n      })\n    if (deploylessCallViaFactory)\n      return toDeploylessCallViaFactoryData({\n        data: data_,\n        factory,\n        factoryData,\n        to,\n      })\n    return data_\n  })()\n\n  try {\n    assertRequest(args as AssertRequestParameters)\n\n    const blockNumberHex = blockNumber ? numberToHex(blockNumber) : undefined\n    const block = blockNumberHex || blockTag\n\n    const rpcStateOverride = serializeStateOverride(stateOverride)\n\n    const chainFormat = client.chain?.formatters?.transactionRequest?.format\n    const format = chainFormat || formatTransactionRequest\n\n    const request = format({\n      // Pick out extra data that might exist on the chain's transaction request type.\n      ...extract(rest, { format: chainFormat }),\n      from: account?.address,\n      accessList,\n      blobs,\n      data,\n      gas,\n      gasPrice,\n      maxFeePerBlobGas,\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n      nonce,\n      to: deploylessCall ? undefined : to,\n      value,\n    } as TransactionRequest) as TransactionRequest\n\n    if (batch && shouldPerformMulticall({ request }) && !rpcStateOverride) {\n      try {\n        return await scheduleMulticall(client, {\n          ...request,\n          blockNumber,\n          blockTag,\n        } as unknown as ScheduleMulticallParameters<chain>)\n      } catch (err) {\n        if (\n          !(err instanceof ClientChainNotConfiguredError) &&\n          !(err instanceof ChainDoesNotSupportContract)\n        )\n          throw err\n      }\n    }\n\n    const response = await client.request({\n      method: 'eth_call',\n      params: rpcStateOverride\n        ? [\n            request as ExactPartial<RpcTransactionRequest>,\n            block,\n            rpcStateOverride,\n          ]\n        : [request as ExactPartial<RpcTransactionRequest>, block],\n    })\n    if (response === '0x') return { data: undefined }\n    return { data: response }\n  } catch (err) {\n    const data = getRevertErrorData(err)\n\n    // Check for CCIP-Read offchain lookup signature.\n    const { offchainLookup, offchainLookupSignature } = await import(\n      '../../utils/ccip.js'\n    )\n    if (\n      client.ccipRead !== false &&\n      data?.slice(0, 10) === offchainLookupSignature &&\n      to\n    )\n      return { data: await offchainLookup(client, { data, to }) }\n\n    // Check for counterfactual deployment error.\n    if (deploylessCall && data?.slice(0, 10) === '0x101bb98d')\n      throw new CounterfactualDeploymentFailedError({ factory })\n\n    throw getCallError(err as ErrorType, {\n      ...args,\n      account,\n      chain: client.chain,\n    })\n  }\n}\n\n// We only want to perform a scheduled multicall if:\n// - The request has calldata,\n// - The request has a target address,\n// - The target address is not already the aggregate3 signature,\n// - The request has no other properties (`nonce`, `gas`, etc cannot be sent with a multicall).\nfunction shouldPerformMulticall({ request }: { request: TransactionRequest }) {\n  const { data, to, ...request_ } = request\n  if (!data) return false\n  if (data.startsWith(aggregate3Signature)) return false\n  if (!to) return false\n  if (\n    Object.values(request_).filter((x) => typeof x !== 'undefined').length > 0\n  )\n    return false\n  return true\n}\n\ntype ScheduleMulticallParameters<chain extends Chain | undefined> = Pick<\n  CallParameters<chain>,\n  'blockNumber' | 'blockTag'\n> & {\n  data: Hex\n  multicallAddress?: Address | undefined\n  to: Address\n}\n\ntype ScheduleMulticallErrorType =\n  | GetChainContractAddressErrorType\n  | NumberToHexErrorType\n  | CreateBatchSchedulerErrorType\n  | EncodeFunctionDataErrorType\n  | DecodeFunctionResultErrorType\n  | RawContractErrorType\n  | ErrorType\n\nasync function scheduleMulticall<chain extends Chain | undefined>(\n  client: Client<Transport>,\n  args: ScheduleMulticallParameters<chain>,\n) {\n  const { batchSize = 1024, wait = 0 } =\n    typeof client.batch?.multicall === 'object' ? client.batch.multicall : {}\n  const {\n    blockNumber,\n    blockTag = 'latest',\n    data,\n    multicallAddress: multicallAddress_,\n    to,\n  } = args\n\n  let multicallAddress = multicallAddress_\n  if (!multicallAddress) {\n    if (!client.chain) throw new ClientChainNotConfiguredError()\n\n    multicallAddress = getChainContractAddress({\n      blockNumber,\n      chain: client.chain,\n      contract: 'multicall3',\n    })\n  }\n\n  const blockNumberHex = blockNumber ? numberToHex(blockNumber) : undefined\n  const block = blockNumberHex || blockTag\n\n  const { schedule } = createBatchScheduler({\n    id: `${client.uid}.${block}`,\n    wait,\n    shouldSplitBatch(args) {\n      const size = args.reduce((size, { data }) => size + (data.length - 2), 0)\n      return size > batchSize * 2\n    },\n    fn: async (\n      requests: {\n        data: Hex\n        to: Address\n      }[],\n    ) => {\n      const calls = requests.map((request) => ({\n        allowFailure: true,\n        callData: request.data,\n        target: request.to,\n      }))\n\n      const calldata = encodeFunctionData({\n        abi: multicall3Abi,\n        args: [calls],\n        functionName: 'aggregate3',\n      })\n\n      const data = await client.request({\n        method: 'eth_call',\n        params: [\n          {\n            data: calldata,\n            to: multicallAddress,\n          },\n          block,\n        ],\n      })\n\n      return decodeFunctionResult({\n        abi: multicall3Abi,\n        args: [calls],\n        functionName: 'aggregate3',\n        data: data || '0x',\n      })\n    },\n  })\n\n  const [{ returnData, success }] = await schedule({ data, to })\n\n  if (!success) throw new RawContractError({ data: returnData })\n  if (returnData === '0x') return { data: undefined }\n  return { data: returnData }\n}\n\ntype ToDeploylessCallViaBytecodeDataErrorType =\n  | EncodeDeployDataErrorType\n  | ErrorType\n\nfunction toDeploylessCallViaBytecodeData(parameters: {\n  code: Hex\n  data: Hex\n}) {\n  const { code, data } = parameters\n  return encodeDeployData({\n    abi: parseAbi(['constructor(bytes, bytes)']),\n    bytecode: deploylessCallViaBytecodeBytecode,\n    args: [code, data],\n  })\n}\n\ntype ToDeploylessCallViaFactoryDataErrorType =\n  | EncodeDeployDataErrorType\n  | ErrorType\n\nfunction toDeploylessCallViaFactoryData(parameters: {\n  data: Hex\n  factory: Address\n  factoryData: Hex\n  to: Address\n}) {\n  const { data, factory, factoryData, to } = parameters\n  return encodeDeployData({\n    abi: parseAbi(['constructor(address, bytes, address, bytes)']),\n    bytecode: deploylessCallViaFactoryBytecode,\n    args: [to, data, factory, factoryData],\n  })\n}\n\n/** @internal */\nexport type GetRevertErrorDataErrorType = ErrorType\n\n/** @internal */\nexport function getRevertErrorData(err: unknown) {\n  if (!(err instanceof BaseError)) return undefined\n  const error = err.walk() as RawContractError\n  return typeof error?.data === 'object' ? error.data?.data : error.data\n}\n", "import type { Address } from 'abitype'\n\nimport type { Hex } from '../types/misc.js'\nimport { stringify } from '../utils/stringify.js'\n\nimport { BaseError } from './base.js'\nimport { getUrl } from './utils.js'\n\nexport type OffchainLookupErrorType = OffchainLookupError & {\n  name: 'OffchainLookupError'\n}\nexport class OffchainLookupError extends BaseError {\n  constructor({\n    callbackSelector,\n    cause,\n    data,\n    extraData,\n    sender,\n    urls,\n  }: {\n    callbackSelector: Hex\n    cause: BaseError\n    data: Hex\n    extraData: Hex\n    sender: Address\n    urls: readonly string[]\n  }) {\n    super(\n      cause.shortMessage ||\n        'An error occurred while fetching for an offchain result.',\n      {\n        cause,\n        metaMessages: [\n          ...(cause.metaMessages || []),\n          cause.metaMessages?.length ? '' : [],\n          'Offchain Gateway Call:',\n          urls && [\n            '  Gateway URL(s):',\n            ...urls.map((url) => `    ${getUrl(url)}`),\n          ],\n          `  Sender: ${sender}`,\n          `  Data: ${data}`,\n          `  Callback selector: ${callbackSelector}`,\n          `  Extra data: ${extraData}`,\n        ].flat(),\n        name: 'OffchainLookupError',\n      },\n    )\n  }\n}\n\nexport type OffchainLookupResponseMalformedErrorType =\n  OffchainLookupResponseMalformedError & {\n    name: 'OffchainLookupResponseMalformedError'\n  }\nexport class OffchainLookupResponseMalformedError extends BaseError {\n  constructor({ result, url }: { result: any; url: string }) {\n    super(\n      'Offchain gateway response is malformed. Response data must be a hex value.',\n      {\n        metaMessages: [\n          `Gateway URL: ${getUrl(url)}`,\n          `Response: ${stringify(result)}`,\n        ],\n        name: 'OffchainLookupResponseMalformedError',\n      },\n    )\n  }\n}\n\n/** @internal */\nexport type OffchainLookupSenderMismatchErrorType =\n  OffchainLookupSenderMismatchError & {\n    name: 'OffchainLookupSenderMismatchError'\n  }\nexport class OffchainLookupSenderMismatchError extends BaseError {\n  constructor({ sender, to }: { sender: Address; to: Address }) {\n    super(\n      'Reverted sender address does not match target contract address (`to`).',\n      {\n        metaMessages: [\n          `Contract address: ${to}`,\n          `OffchainLookup sender address: ${sender}`,\n        ],\n        name: 'OffchainLookupSenderMismatchError',\n      },\n    )\n  }\n}\n", "import type { Address } from 'abitype'\n\nimport {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../../errors/address.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport { isAddress } from './isAddress.js'\n\nexport type IsAddressEqualReturnType = boolean\nexport type IsAddressEqualErrorType = InvalidAddressErrorType | ErrorType\n\nexport function isAddressEqual(a: Address, b: Address) {\n  if (!isAddress(a, { strict: false }))\n    throw new InvalidAddressError({ address: a })\n  if (!isAddress(b, { strict: false }))\n    throw new InvalidAddressError({ address: b })\n  return a.toLowerCase() === b.toLowerCase()\n}\n", "import type { Abi, Address } from 'abitype'\n\nimport { type CallParameters, call } from '../actions/public/call.js'\nimport type { Transport } from '../clients/transports/createTransport.js'\nimport type { BaseError } from '../errors/base.js'\nimport {\n  OffchainLookupError,\n  type OffchainLookupErrorType as OffchainLookupErrorType_,\n  OffchainLookupResponseMalformedError,\n  type OffchainLookupResponseMalformedErrorType,\n  OffchainLookupSenderMismatchError,\n} from '../errors/ccip.js'\nimport {\n  HttpRequestError,\n  type HttpRequestErrorType,\n} from '../errors/request.js'\nimport type { Chain } from '../types/chain.js'\nimport type { Hex } from '../types/misc.js'\n\nimport type { Client } from '../clients/createClient.js'\nimport type { ErrorType } from '../errors/utils.js'\nimport { decodeErrorResult } from './abi/decodeErrorResult.js'\nimport { encodeAbiParameters } from './abi/encodeAbiParameters.js'\nimport { isAddressEqual } from './address/isAddressEqual.js'\nimport { concat } from './data/concat.js'\nimport { isHex } from './data/isHex.js'\nimport { stringify } from './stringify.js'\n\nexport const offchainLookupSignature = '0x556f1830'\nexport const offchainLookupAbiItem = {\n  name: 'OffchainLookup',\n  type: 'error',\n  inputs: [\n    {\n      name: 'sender',\n      type: 'address',\n    },\n    {\n      name: 'urls',\n      type: 'string[]',\n    },\n    {\n      name: 'callData',\n      type: 'bytes',\n    },\n    {\n      name: 'callbackFunction',\n      type: 'bytes4',\n    },\n    {\n      name: 'extraData',\n      type: 'bytes',\n    },\n  ],\n} as const satisfies Abi[number]\n\nexport type OffchainLookupErrorType = OffchainLookupErrorType_ | ErrorType\n\nexport async function offchainLookup<chain extends Chain | undefined>(\n  client: Client<Transport, chain>,\n  {\n    blockNumber,\n    blockTag,\n    data,\n    to,\n  }: Pick<CallParameters, 'blockNumber' | 'blockTag'> & {\n    data: Hex\n    to: Address\n  },\n): Promise<Hex> {\n  const { args } = decodeErrorResult({\n    data,\n    abi: [offchainLookupAbiItem],\n  })\n  const [sender, urls, callData, callbackSelector, extraData] = args\n\n  const { ccipRead } = client\n  const ccipRequest_ =\n    ccipRead && typeof ccipRead?.request === 'function'\n      ? ccipRead.request\n      : ccipRequest\n\n  try {\n    if (!isAddressEqual(to, sender))\n      throw new OffchainLookupSenderMismatchError({ sender, to })\n\n    const result = await ccipRequest_({ data: callData, sender, urls })\n\n    const { data: data_ } = await call(client, {\n      blockNumber,\n      blockTag,\n      data: concat([\n        callbackSelector,\n        encodeAbiParameters(\n          [{ type: 'bytes' }, { type: 'bytes' }],\n          [result, extraData],\n        ),\n      ]),\n      to,\n    } as CallParameters)\n\n    return data_!\n  } catch (err) {\n    throw new OffchainLookupError({\n      callbackSelector,\n      cause: err as BaseError,\n      data,\n      extraData,\n      sender,\n      urls,\n    })\n  }\n}\n\nexport type CcipRequestParameters = {\n  data: Hex\n  sender: Address\n  urls: readonly string[]\n}\n\nexport type CcipRequestReturnType = Hex\n\nexport type CcipRequestErrorType =\n  | HttpRequestErrorType\n  | OffchainLookupResponseMalformedErrorType\n  | ErrorType\n\nexport async function ccipRequest({\n  data,\n  sender,\n  urls,\n}: CcipRequestParameters): Promise<CcipRequestReturnType> {\n  let error = new Error('An unknown error occurred.')\n\n  for (let i = 0; i < urls.length; i++) {\n    const url = urls[i]\n    const method = url.includes('{data}') ? 'GET' : 'POST'\n    const body = method === 'POST' ? { data, sender } : undefined\n    const headers: HeadersInit =\n      method === 'POST' ? { 'Content-Type': 'application/json' } : {}\n\n    try {\n      const response = await fetch(\n        url.replace('{sender}', sender).replace('{data}', data),\n        {\n          body: JSON.stringify(body),\n          headers,\n          method,\n        },\n      )\n\n      let result: any\n      if (\n        response.headers.get('Content-Type')?.startsWith('application/json')\n      ) {\n        result = (await response.json()).data\n      } else {\n        result = (await response.text()) as any\n      }\n\n      if (!response.ok) {\n        error = new HttpRequestError({\n          body,\n          details: result?.error\n            ? stringify(result.error)\n            : response.statusText,\n          headers: response.headers,\n          status: response.status,\n          url,\n        })\n        continue\n      }\n\n      if (!isHex(result)) {\n        error = new OffchainLookupResponseMalformedError({\n          result,\n          url,\n        })\n        continue\n      }\n\n      return result\n    } catch (err) {\n      error = new HttpRequestError({\n        body,\n        details: (err as Error).message,\n        url,\n      })\n    }\n  }\n\n  throw error\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAOM,SAAU,aACd,SAAyB;AAEzB,MAAI,OAAO,YAAY;AACrB,WAAO,EAAE,SAAS,SAAS,MAAM,WAAU;AAC7C,SAAO;AACT;;;ACZO,IAAM,gBAAgB;EAC3B;IACE,QAAQ;MACN;QACE,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;QAGV,MAAM;QACN,MAAM;;;IAGV,MAAM;IACN,SAAS;MACP;QACE,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;QAGV,MAAM;QACN,MAAM;;;IAGV,iBAAiB;IACjB,MAAM;;;AAIV,IAAM,0BAA0B;EAC9B;IACE,QAAQ,CAAA;IACR,MAAM;IACN,MAAM;;EAER;IACE,QAAQ,CAAA;IACR,MAAM;IACN,MAAM;;EAER;IACE,QAAQ,CAAA;IACR,MAAM;IACN,MAAM;;EAER;IACE,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;;IAGV,MAAM;IACN,MAAM;;EAER;IACE,QAAQ;MACN;QACE,YAAY;UACV;YACE,MAAM;YACN,MAAM;;UAER;YACE,MAAM;YACN,MAAM;;;QAGV,MAAM;QACN,MAAM;;;IAGV,MAAM;IACN,MAAM;;;AAIH,IAAM,8BAA8B;EACzC,GAAG;EACH;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,QAAO;MAC7B,EAAE,MAAM,QAAQ,MAAM,QAAO;;IAE/B,SAAS;MACP,EAAE,MAAM,IAAI,MAAM,QAAO;MACzB,EAAE,MAAM,WAAW,MAAM,UAAS;;;EAGtC;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,QAAQ,MAAM,QAAO;MAC7B,EAAE,MAAM,QAAQ,MAAM,QAAO;MAC7B,EAAE,MAAM,YAAY,MAAM,WAAU;;IAEtC,SAAS;MACP,EAAE,MAAM,IAAI,MAAM,QAAO;MACzB,EAAE,MAAM,WAAW,MAAM,UAAS;;;;AAKjC,IAAM,8BAA8B;EACzC,GAAG;EACH;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ,CAAC,EAAE,MAAM,SAAS,MAAM,cAAa,CAAE;IAC/C,SAAS;MACP,EAAE,MAAM,UAAU,MAAM,eAAc;MACtC,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,WAAU;;;EAGvC;IACE,MAAM;IACN,MAAM;IACN,iBAAiB;IACjB,QAAQ;MACN,EAAE,MAAM,SAAS,MAAM,cAAa;MACpC,EAAE,MAAM,YAAY,MAAM,WAAU;;IAEtC,SAAS;MACP,EAAE,MAAM,UAAU,MAAM,eAAc;MACtC,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,kBAAiB;MAC1C,EAAE,MAAM,WAAW,MAAM,WAAU;;;;;;ACtJlC,IAAM,sBAAsB;;;ACA5B,IAAM,oCACX;AAEK,IAAM,mCACX;;;ACJK,IAAM,UAAU;;;ACOvB,IAAI,cAA2B;EAC7B,YAAY,CAAC,EACX,aACA,UAAAA,YAAW,IACX,SAAQ,MAERA,YACI,GAAG,eAAe,iBAAiB,GAAGA,SAAQ,GAC5C,WAAW,IAAI,QAAQ,KAAK,EAC9B,KACA;EACN,SAAS,QAAQ,OAAO;;AAkBpB,IAAO,YAAP,MAAO,mBAAkB,MAAK;EASlC,YAAY,cAAsB,OAA4B,CAAA,GAAE;AAC9D,UAAM,WAAW,MAAK;AACpB,UAAI,KAAK,iBAAiB;AAAW,eAAO,KAAK,MAAM;AACvD,UAAI,KAAK,OAAO;AAAS,eAAO,KAAK,MAAM;AAC3C,aAAO,KAAK;IACd,GAAE;AACF,UAAMC,aAAY,MAAK;AACrB,UAAI,KAAK,iBAAiB;AACxB,eAAO,KAAK,MAAM,YAAY,KAAK;AACrC,aAAO,KAAK;IACd,GAAE;AACF,UAAM,UAAU,YAAY,aAAa,EAAE,GAAG,MAAM,UAAAA,UAAQ,CAAE;AAE9D,UAAM,UAAU;MACd,gBAAgB;MAChB;MACA,GAAI,KAAK,eAAe,CAAC,GAAG,KAAK,cAAc,EAAE,IAAI,CAAA;MACrD,GAAI,UAAU,CAAC,SAAS,OAAO,EAAE,IAAI,CAAA;MACrC,GAAI,UAAU,CAAC,YAAY,OAAO,EAAE,IAAI,CAAA;MACxC,GAAI,YAAY,UAAU,CAAC,YAAY,YAAY,OAAO,EAAE,IAAI,CAAA;MAChE,KAAK,IAAI;AAEX,UAAM,SAAS,KAAK,QAAQ,EAAE,OAAO,KAAK,MAAK,IAAK,MAAS;AA9B/D,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,WAAA;;;;;;AAES,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AA0Bd,SAAK,UAAU;AACf,SAAK,WAAWA;AAChB,SAAK,eAAe,KAAK;AACzB,SAAK,OAAO,KAAK,QAAQ,KAAK;AAC9B,SAAK,eAAe;AACpB,SAAK,UAAU;EACjB;EAIA,KAAK,IAAQ;AACX,WAAO,KAAK,MAAM,EAAE;EACtB;;AAGF,SAAS,KACP,KACA,IAA4C;AAE5C,MAAI,KAAK,GAAG;AAAG,WAAO;AACtB,MACE,OACA,OAAO,QAAQ,YACf,WAAW,OACX,IAAI,UAAU;AAEd,WAAO,KAAK,IAAI,OAAO,EAAE;AAC3B,SAAO,KAAK,OAAO;AACrB;;;ACzFM,IAAO,8BAAP,cAA2C,UAAS;EACxD,YAAY,EACV,aACA,OACA,SAAQ,GAKT;AACC,UACE,UAAU,MAAM,IAAI,gCAAgC,SAAS,IAAI,MACjE;MACE,cAAc;QACZ;QACA,GAAI,eACJ,SAAS,gBACT,SAAS,eAAe,cACpB;UACE,mBAAmB,SAAS,IAAI,kCAAkC,SAAS,YAAY,mBAAmB,WAAW;YAEvH;UACE,2CAA2C,SAAS,IAAI;;;MAGhE,MAAM;KACP;EAEL;;AAgDI,IAAO,gCAAP,cAA6C,UAAS;EAC1D,cAAA;AACE,UAAM,wCAAwC;MAC5C,MAAM;KACP;EACH;;;;AC1EK,IAAM,gBAA0B;EACrC,QAAQ;IACN;MACE,MAAM;MACN,MAAM;;;EAGV,MAAM;EACN,MAAM;;AAED,IAAM,gBAA0B;EACrC,QAAQ;IACN;MACE,MAAM;MACN,MAAM;;;EAGV,MAAM;EACN,MAAM;;;;ACnBF,SAAUC,eACd,SACA,EAAE,cAAc,MAAK,IAA4C,CAAA,GAAE;AAEnE,MACE,QAAQ,SAAS,cACjB,QAAQ,SAAS,WACjB,QAAQ,SAAS;AAEjB,UAAM,IAAI,2BAA2B,QAAQ,IAAI;AAEnD,SAAO,GAAG,QAAQ,IAAI,IAAI,gBAAgB,QAAQ,QAAQ,EAAE,YAAW,CAAE,CAAC;AAC5E;AAIM,SAAU,gBACd,QACA,EAAE,cAAc,MAAK,IAA4C,CAAA,GAAE;AAEnE,MAAI,CAAC;AAAQ,WAAO;AACpB,SAAO,OACJ,IAAI,CAAC,UAAU,eAAe,OAAO,EAAE,YAAW,CAAE,CAAC,EACrD,KAAK,cAAc,OAAO,GAAG;AAClC;AAIA,SAAS,eACP,OACA,EAAE,YAAW,GAA4B;AAEzC,MAAI,MAAM,KAAK,WAAW,OAAO,GAAG;AAClC,WAAO,IAAI,gBACR,MAAoD,YACrD,EAAE,YAAW,CAAE,CAChB,IAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,CAAC;EACvC;AACA,SAAO,MAAM,QAAQ,eAAe,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK;AACtE;;;AChDM,SAAU,MACd,OACA,EAAE,SAAS,KAAI,IAAuC,CAAA,GAAE;AAExD,MAAI,CAAC;AAAO,WAAO;AACnB,MAAI,OAAO,UAAU;AAAU,WAAO;AACtC,SAAO,SAAS,mBAAmB,KAAK,KAAK,IAAI,MAAM,WAAW,IAAI;AACxE;;;ACCM,SAAU,KAAK,OAAsB;AACzC,MAAI,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE;AAAG,WAAO,KAAK,MAAM,MAAM,SAAS,KAAK,CAAC;AAC5E,SAAO,MAAM;AACf;;;ACLM,IAAO,8BAAP,cAA2C,UAAS;EACxD,YAAY,EAAE,UAAAC,UAAQ,GAAwB;AAC5C,UACE;MACE;MACA;MACA,KAAK,IAAI,GACX;MACE,UAAAA;MACA,MAAM;KACP;EAEL;;AAQI,IAAO,oCAAP,cAAiD,UAAS;EAC9D,YAAY,EAAE,UAAAA,UAAQ,GAAwB;AAC5C,UACE;MACE;MACA;MACA,KAAK,IAAI,GACX;MACE,UAAAA;MACA,MAAM;KACP;EAEL;;AA0BI,IAAO,mCAAP,cAAgD,UAAS;EAK7D,YAAY,EACV,MACA,QACA,MAAAC,MAAI,GACyD;AAC7D,UACE,CAAC,gBAAgBA,KAAI,2CAA2C,EAAE,KAChE,IAAI,GAEN;MACE,cAAc;QACZ,YAAY,gBAAgB,QAAQ,EAAE,aAAa,KAAI,CAAE,CAAC;QAC1D,WAAW,IAAI,KAAKA,KAAI;;MAE1B,MAAM;KACP;AAnBL,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AAoBE,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAOA;EACd;;AAMI,IAAO,2BAAP,cAAwC,UAAS;EACrD,cAAA;AACE,UAAM,uDAAuD;MAC3D,MAAM;KACP;EACH;;AAOI,IAAO,sCAAP,cAAmD,UAAS;EAChE,YAAY,EACV,gBACA,aACA,KAAI,GAC0D;AAC9D,UACE;MACE,+CAA+C,IAAI;MACnD,oBAAoB,cAAc;MAClC,iBAAiB,WAAW;MAC5B,KAAK,IAAI,GACX,EAAE,MAAM,sCAAqC,CAAE;EAEnD;;AAOI,IAAO,oCAAP,cAAiD,UAAS;EAC9D,YAAY,EAAE,cAAc,MAAK,GAAwC;AACvE,UACE,kBAAkB,KAAK,WAAW,KAChC,KAAK,CACN,wCAAwC,YAAY,MACrD,EAAE,MAAM,oCAAmC,CAAE;EAEjD;;AAOI,IAAO,iCAAP,cAA8C,UAAS;EAC3D,YAAY,EACV,gBACA,YAAW,GACqC;AAChD,UACE;MACE;MACA,6BAA6B,cAAc;MAC3C,0BAA0B,WAAW;MACrC,KAAK,IAAI,GACX,EAAE,MAAM,iCAAgC,CAAE;EAE9C;;AA+CI,IAAO,iCAAP,cAA8C,UAAS;EAG3D,YAAY,WAAgB,EAAE,UAAAC,UAAQ,GAAwB;AAC5D,UACE;MACE,4BAA4B,SAAS;MACrC;MACA,sFAAsF,SAAS;MAC/F,KAAK,IAAI,GACX;MACE,UAAAA;MACA,MAAM;KACP;AAZL,WAAA,eAAA,MAAA,aAAA;;;;;;AAcE,SAAK,YAAY;EACnB;;AA4DI,IAAO,2BAAP,cAAwC,UAAS;EACrD,YACE,cACA,EAAE,UAAAC,UAAQ,IAAwC,CAAA,GAAE;AAEpD,UACE;MACE,YAAY,eAAe,IAAI,YAAY,OAAO,EAAE;MACpD;MACA,KAAK,IAAI,GACX;MACE,UAAAA;MACA,MAAM;KACP;EAEL;;AAOI,IAAO,kCAAP,cAA+C,UAAS;EAC5D,YAAY,cAAsB,EAAE,UAAAA,UAAQ,GAAwB;AAClE,UACE;MACE,aAAa,YAAY;MACzB;MACA;MACA,KAAK,IAAI,GACX;MACE,UAAAA;MACA,MAAM;KACP;EAEL;;AA0BI,IAAO,wBAAP,cAAqC,UAAS;EAClD,YACE,GACA,GAAyC;AAEzC,UAAM,kDAAkD;MACtD,cAAc;QACZ,KAAK,EAAE,IAAI,WAAWC,eAAc,EAAE,OAAO,CAAC;QAC9C,KAAK,EAAE,IAAI,WAAWA,eAAc,EAAE,OAAO,CAAC;QAC9C;QACA;QACA;;MAEF,MAAM;KACP;EACH;;AAsFI,IAAO,8BAAP,cAA2C,UAAS;EACxD,YAAY,MAAc,EAAE,UAAAC,UAAQ,GAAwB;AAC1D,UACE;MACE,SAAS,IAAI;MACb;MACA,KAAK,IAAI,GACX,EAAE,UAAAA,WAAU,MAAM,yBAAwB,CAAE;EAEhD;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EACxD,YAAY,MAAc,EAAE,UAAAA,UAAQ,GAAwB;AAC1D,UACE;MACE,SAAS,IAAI;MACb;MACA,KAAK,IAAI,GACX,EAAE,UAAAA,WAAU,MAAM,yBAAwB,CAAE;EAEhD;;AAMI,IAAO,oBAAP,cAAiC,UAAS;EAC9C,YAAY,OAAc;AACxB,UAAM,CAAC,UAAU,KAAK,yBAAyB,EAAE,KAAK,IAAI,GAAG;MAC3D,MAAM;KACP;EACH;;AAMI,IAAO,6BAAP,cAA0C,UAAS;EACvD,YAAY,MAAY;AACtB,UACE;MACE,IAAI,IAAI;MACR;MACA,KAAK,IAAI,GACX,EAAE,MAAM,6BAA4B,CAAE;EAE1C;;;;AC5eI,IAAO,8BAAP,cAA2C,UAAS;EACxD,YAAY,EACV,QACA,UACA,MAAAC,MAAI,GACwD;AAC5D,UACE,SACE,aAAa,UAAU,aAAa,QACtC,eAAe,MAAM,6BAA6BA,KAAI,MACtD,EAAE,MAAM,8BAA6B,CAAE;EAE3C;;AAMI,IAAO,8BAAP,cAA2C,UAAS;EACxD,YAAY,EACV,MAAAA,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,UAAUA,KAAI,2BAA2B,UAAU,MACnE,EAAE,MAAM,8BAA6B,CAAE;EAE3C;;AAMI,IAAO,0BAAP,cAAuC,UAAS;EACpD,YAAY,EACV,MAAAA,OACA,YACA,KAAI,GAKL;AACC,UACE,GAAG,KAAK,OAAO,CAAC,EAAE,YAAW,CAAE,GAAG,KAC/B,MAAM,CAAC,EACP,YAAW,CAAE,sBAAsB,UAAU,IAAI,IAAI,iBAAiBA,KAAI,IAAI,IAAI,UACrF,EAAE,MAAM,0BAAyB,CAAE;EAEvC;;;;AClCI,SAAU,MACd,OACA,OACA,KACA,EAAE,OAAM,IAAuC,CAAA,GAAE;AAEjD,MAAI,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE;AAChC,WAAO,SAAS,OAAc,OAAO,KAAK;MACxC;KACD;AACH,SAAO,WAAW,OAAoB,OAAO,KAAK;IAChD;GACD;AACH;AAOA,SAAS,kBAAkB,OAAwB,OAA0B;AAC3E,MAAI,OAAO,UAAU,YAAY,QAAQ,KAAK,QAAQ,KAAK,KAAK,IAAI;AAClE,UAAM,IAAI,4BAA4B;MACpC,QAAQ;MACR,UAAU;MACV,MAAM,KAAK,KAAK;KACjB;AACL;AAOA,SAAS,gBACP,OACA,OACA,KAAwB;AAExB,MACE,OAAO,UAAU,YACjB,OAAO,QAAQ,YACf,KAAK,KAAK,MAAM,MAAM,OACtB;AACA,UAAM,IAAI,4BAA4B;MACpC,QAAQ;MACR,UAAU;MACV,MAAM,KAAK,KAAK;KACjB;EACH;AACF;AAcM,SAAU,WACd,QACA,OACA,KACA,EAAE,OAAM,IAAuC,CAAA,GAAE;AAEjD,oBAAkB,QAAQ,KAAK;AAC/B,QAAM,QAAQ,OAAO,MAAM,OAAO,GAAG;AACrC,MAAI;AAAQ,oBAAgB,OAAO,OAAO,GAAG;AAC7C,SAAO;AACT;AAcM,SAAU,SACd,QACA,OACA,KACA,EAAE,OAAM,IAAuC,CAAA,GAAE;AAEjD,oBAAkB,QAAQ,KAAK;AAC/B,QAAM,QAAQ,KAAK,OAChB,QAAQ,MAAM,EAAE,EAChB,OAAO,SAAS,KAAK,IAAI,OAAO,OAAO,UAAU,CAAC,CAAC;AACtD,MAAI;AAAQ,oBAAgB,OAAO,OAAO,GAAG;AAC7C,SAAO;AACT;;;AC9GM,SAAU,IACd,YACA,EAAE,KAAK,MAAAC,QAAO,GAAE,IAAiB,CAAA,GAAE;AAEnC,MAAI,OAAO,eAAe;AACxB,WAAO,OAAO,YAAY,EAAE,KAAK,MAAAA,MAAI,CAAE;AACzC,SAAO,SAAS,YAAY,EAAE,KAAK,MAAAA,MAAI,CAAE;AAC3C;AAIM,SAAU,OAAO,MAAW,EAAE,KAAK,MAAAA,QAAO,GAAE,IAAiB,CAAA,GAAE;AACnE,MAAIA,UAAS;AAAM,WAAO;AAC1B,QAAM,MAAM,KAAK,QAAQ,MAAM,EAAE;AACjC,MAAI,IAAI,SAASA,QAAO;AACtB,UAAM,IAAI,4BAA4B;MACpC,MAAM,KAAK,KAAK,IAAI,SAAS,CAAC;MAC9B,YAAYA;MACZ,MAAM;KACP;AAEH,SAAO,KAAK,IAAI,QAAQ,UAAU,WAAW,UAAU,EACrDA,QAAO,GACP,GAAG,CACJ;AACH;AAIM,SAAU,SACd,OACA,EAAE,KAAK,MAAAA,QAAO,GAAE,IAAiB,CAAA,GAAE;AAEnC,MAAIA,UAAS;AAAM,WAAO;AAC1B,MAAI,MAAM,SAASA;AACjB,UAAM,IAAI,4BAA4B;MACpC,MAAM,MAAM;MACZ,YAAYA;MACZ,MAAM;KACP;AACH,QAAM,cAAc,IAAI,WAAWA,KAAI;AACvC,WAAS,IAAI,GAAG,IAAIA,OAAM,KAAK;AAC7B,UAAM,SAAS,QAAQ;AACvB,gBAAY,SAAS,IAAIA,QAAO,IAAI,CAAC,IACnC,MAAM,SAAS,IAAI,MAAM,SAAS,IAAI,CAAC;EAC3C;AACA,SAAO;AACT;;;ACzDM,IAAO,yBAAP,cAAsC,UAAS;EACnD,YAAY,EACV,KACA,KACA,QACA,MAAAC,OACA,MAAK,GAON;AACC,UACE,WAAW,KAAK,oBACdA,QAAO,GAAGA,QAAO,CAAC,QAAQ,SAAS,WAAW,UAAU,MAAM,EAChE,iBAAiB,MAAM,IAAI,GAAG,OAAO,GAAG,MAAM,UAAU,GAAG,GAAG,IAC9D,EAAE,MAAM,yBAAwB,CAAE;EAEtC;;AAMI,IAAO,2BAAP,cAAwC,UAAS;EACrD,YAAY,OAAgB;AAC1B,UACE,gBAAgB,KAAK,kGACrB;MACE,MAAM;KACP;EAEL;;AA8BI,IAAO,oBAAP,cAAiC,UAAS;EAC9C,YAAY,EAAE,WAAW,QAAO,GAA0C;AACxE,UACE,sBAAsB,OAAO,uBAAuB,SAAS,WAC7D,EAAE,MAAM,oBAAmB,CAAE;EAEjC;;;;ACjEI,SAAU,KACd,YACA,EAAE,MAAM,OAAM,IAAkB,CAAA,GAAE;AAElC,MAAI,OACF,OAAO,eAAe,WAAW,WAAW,QAAQ,MAAM,EAAE,IAAI;AAElE,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACxC,QAAI,KAAK,QAAQ,SAAS,IAAI,KAAK,SAAS,IAAI,CAAC,EAAE,SAAQ,MAAO;AAChE;;AACG;EACP;AACA,SACE,QAAQ,SACJ,KAAK,MAAM,WAAW,IACtB,KAAK,MAAM,GAAG,KAAK,SAAS,WAAW;AAE7C,MAAI,OAAO,eAAe,UAAU;AAClC,QAAI,KAAK,WAAW,KAAK,QAAQ;AAAS,aAAO,GAAG,IAAI;AACxD,WAAO,KACL,KAAK,SAAS,MAAM,IAAI,IAAI,IAAI,KAAK,IACvC;EACF;AACA,SAAO;AACT;;;ACnBM,SAAU,WACd,YACA,EAAE,MAAAC,MAAI,GAAoB;AAE1B,MAAI,KAAM,UAAU,IAAIA;AACtB,UAAM,IAAI,kBAAkB;MAC1B,WAAW,KAAM,UAAU;MAC3B,SAASA;KACV;AACL;AAsGM,SAAU,YAAY,KAAU,OAAwB,CAAA,GAAE;AAC9D,QAAM,EAAE,OAAM,IAAK;AAEnB,MAAI,KAAK;AAAM,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AAElD,QAAM,QAAQ,OAAO,GAAG;AACxB,MAAI,CAAC;AAAQ,WAAO;AAEpB,QAAMC,SAAQ,IAAI,SAAS,KAAK;AAChC,QAAM,OAAO,MAAO,OAAOA,KAAI,IAAI,KAAK,MAAO;AAC/C,MAAI,SAAS;AAAK,WAAO;AAEzB,SAAO,QAAQ,OAAO,KAAK,IAAI,SAASA,QAAO,GAAG,GAAG,CAAC,EAAE,IAAI;AAC9D;AAkEM,SAAU,YAAY,KAAU,OAAwB,CAAA,GAAE;AAC9D,SAAO,OAAO,YAAY,KAAK,IAAI,CAAC;AACtC;;;ACxMA,IAAM,QAAsB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,IAAI,MAC3D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAwC3B,SAAU,MACd,OACA,OAAwB,CAAA,GAAE;AAE1B,MAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAChD,WAAO,YAAY,OAAO,IAAI;AAChC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,YAAY,OAAO,IAAI;EAChC;AACA,MAAI,OAAO,UAAU;AAAW,WAAO,UAAU,OAAO,IAAI;AAC5D,SAAO,WAAW,OAAO,IAAI;AAC/B;AAiCM,SAAU,UAAU,OAAgB,OAAsB,CAAA,GAAE;AAChE,QAAM,MAAW,KAAK,OAAO,KAAK,CAAC;AACnC,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AACnC,WAAO,IAAI,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;EACrC;AACA,SAAO;AACT;AA4BM,SAAU,WAAW,OAAkB,OAAuB,CAAA,GAAE;AACpE,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAU,MAAM,MAAM,CAAC,CAAC;EAC1B;AACA,QAAM,MAAM,KAAK,MAAM;AAEvB,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AACnC,WAAO,IAAI,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAI,CAAE;EACnD;AACA,SAAO;AACT;AAuCM,SAAU,YACd,QACA,OAAwB,CAAA,GAAE;AAE1B,QAAM,EAAE,QAAQ,MAAAC,MAAI,IAAK;AAEzB,QAAM,QAAQ,OAAO,MAAM;AAE3B,MAAI;AACJ,MAAIA,OAAM;AACR,QAAI;AAAQ,kBAAY,MAAO,OAAOA,KAAI,IAAI,KAAK,MAAO;;AACrD,iBAAW,OAAO,OAAOA,KAAI,IAAI,MAAM;EAC9C,WAAW,OAAO,WAAW,UAAU;AACrC,eAAW,OAAO,OAAO,gBAAgB;EAC3C;AAEA,QAAM,WAAW,OAAO,aAAa,YAAY,SAAS,CAAC,WAAW,KAAK;AAE3E,MAAK,YAAY,QAAQ,YAAa,QAAQ,UAAU;AACtD,UAAM,SAAS,OAAO,WAAW,WAAW,MAAM;AAClD,UAAM,IAAI,uBAAuB;MAC/B,KAAK,WAAW,GAAG,QAAQ,GAAG,MAAM,KAAK;MACzC,KAAK,GAAG,QAAQ,GAAG,MAAM;MACzB;MACA,MAAAA;MACA,OAAO,GAAG,MAAM,GAAG,MAAM;KAC1B;EACH;AAEA,QAAM,MAAM,MACV,UAAU,QAAQ,KAAK,MAAM,OAAOA,QAAO,CAAC,KAAK,OAAO,KAAK,IAAI,OACjE,SAAS,EAAE,CAAC;AACd,MAAIA;AAAM,WAAO,IAAI,KAAK,EAAE,MAAAA,MAAI,CAAE;AAClC,SAAO;AACT;AASA,IAAM,UAAwB,IAAI,YAAW;AAqBvC,SAAU,YAAY,QAAgB,OAAwB,CAAA,GAAE;AACpE,QAAM,QAAQ,QAAQ,OAAO,MAAM;AACnC,SAAO,WAAW,OAAO,IAAI;AAC/B;;;AC3OA,IAAMC,WAAwB,IAAI,YAAW;AAwCvC,SAAUC,SACd,OACA,OAA0B,CAAA,GAAE;AAE5B,MAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAChD,WAAO,cAAc,OAAO,IAAI;AAClC,MAAI,OAAO,UAAU;AAAW,WAAO,YAAY,OAAO,IAAI;AAC9D,MAAI,MAAM,KAAK;AAAG,WAAO,WAAW,OAAO,IAAI;AAC/C,SAAO,cAAc,OAAO,IAAI;AAClC;AA+BM,SAAU,YAAY,OAAgB,OAAwB,CAAA,GAAE;AACpE,QAAM,QAAQ,IAAI,WAAW,CAAC;AAC9B,QAAM,CAAC,IAAI,OAAO,KAAK;AACvB,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,eAAW,OAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AACrC,WAAO,IAAI,OAAO,EAAE,MAAM,KAAK,KAAI,CAAE;EACvC;AACA,SAAO;AACT;AAGA,IAAM,cAAc;EAClB,MAAM;EACN,MAAM;EACN,GAAG;EACH,GAAG;EACH,GAAG;EACH,GAAG;;AAGL,SAAS,iBAAiB,MAAY;AACpC,MAAI,QAAQ,YAAY,QAAQ,QAAQ,YAAY;AAClD,WAAO,OAAO,YAAY;AAC5B,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,MAAI,QAAQ,YAAY,KAAK,QAAQ,YAAY;AAC/C,WAAO,QAAQ,YAAY,IAAI;AACjC,SAAO;AACT;AA4BM,SAAU,WAAW,MAAW,OAAuB,CAAA,GAAE;AAC7D,MAAI,MAAM;AACV,MAAI,KAAK,MAAM;AACb,eAAW,KAAK,EAAE,MAAM,KAAK,KAAI,CAAE;AACnC,UAAM,IAAI,KAAK,EAAE,KAAK,SAAS,MAAM,KAAK,KAAI,CAAE;EAClD;AAEA,MAAI,YAAY,IAAI,MAAM,CAAC;AAC3B,MAAI,UAAU,SAAS;AAAG,gBAAY,IAAI,SAAS;AAEnD,QAAM,SAAS,UAAU,SAAS;AAClC,QAAM,QAAQ,IAAI,WAAW,MAAM;AACnC,WAAS,QAAQ,GAAG,IAAI,GAAG,QAAQ,QAAQ,SAAS;AAClD,UAAM,aAAa,iBAAiB,UAAU,WAAW,GAAG,CAAC;AAC7D,UAAM,cAAc,iBAAiB,UAAU,WAAW,GAAG,CAAC;AAC9D,QAAI,eAAe,UAAa,gBAAgB,QAAW;AACzD,YAAM,IAAI,UACR,2BAA2B,UAAU,IAAI,CAAC,CAAC,GACzC,UAAU,IAAI,CAAC,CACjB,SAAS,SAAS,KAAK;IAE3B;AACA,UAAM,KAAK,IAAI,aAAa,KAAK;EACnC;AACA,SAAO;AACT;AA0BM,SAAU,cACd,OACA,MAAkC;AAElC,QAAM,MAAM,YAAY,OAAO,IAAI;AACnC,SAAO,WAAW,GAAG;AACvB;AA+BM,SAAU,cACd,OACA,OAA0B,CAAA,GAAE;AAE5B,QAAM,QAAQD,SAAQ,OAAO,KAAK;AAClC,MAAI,OAAO,KAAK,SAAS,UAAU;AACjC,eAAW,OAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AACrC,WAAO,IAAI,OAAO,EAAE,KAAK,SAAS,MAAM,KAAK,KAAI,CAAE;EACrD;AACA,SAAO;AACT;;;AClPA,IAAM,aAA6B,OAAO,KAAK,KAAK,CAAC;AACrD,IAAM,OAAuB,OAAO,EAAE;AAEtC,SAAS,QACP,GACA,KAAK,OAAK;AAKV,MAAI;AAAI,WAAO,EAAE,GAAG,OAAO,IAAI,UAAU,GAAG,GAAG,OAAQ,KAAK,OAAQ,UAAU,EAAC;AAC/E,SAAO,EAAE,GAAG,OAAQ,KAAK,OAAQ,UAAU,IAAI,GAAG,GAAG,OAAO,IAAI,UAAU,IAAI,EAAC;AACjF;AAEA,SAAS,MAAM,KAAe,KAAK,OAAK;AACtC,MAAI,KAAK,IAAI,YAAY,IAAI,MAAM;AACnC,MAAI,KAAK,IAAI,YAAY,IAAI,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM,EAAE,GAAG,EAAC,IAAK,QAAQ,IAAI,CAAC,GAAG,EAAE;AACnC,KAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACxB;AACA,SAAO,CAAC,IAAI,EAAE;AAChB;AAgBA,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AACpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AAEpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;AAC3F,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;;;ACnB3F,IAAM,UAAoB,CAAA;AAC1B,IAAM,YAAsB,CAAA;AAC5B,IAAM,aAAuB,CAAA;AAC7B,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,MAAsB,OAAO,CAAC;AACpC,IAAM,QAAwB,OAAO,GAAG;AACxC,IAAM,SAAyB,OAAO,GAAI;AAC1C,SAAS,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,SAAS;AAE9D,GAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,CAAC;AAChC,UAAQ,KAAK,KAAK,IAAI,IAAI,EAAE;AAE5B,YAAU,MAAQ,QAAQ,MAAM,QAAQ,KAAM,IAAK,EAAE;AAErD,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,SAAM,KAAK,OAAS,KAAK,OAAO,UAAW;AAC3C,QAAI,IAAI;AAAK,WAAK,QAAS,OAAuB,OAAO,CAAC,KAAK;EACjE;AACA,aAAW,KAAK,CAAC;AACnB;AACA,IAAM,CAAC,aAAa,WAAW,IAAoB,MAAM,YAAY,IAAI;AAGzE,IAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AAC7F,IAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC;AAGvF,SAAU,QAAQ,GAAgB,SAAiB,IAAE;AACzD,QAAM,IAAI,IAAI,YAAY,IAAI,CAAC;AAE/B,WAAS,QAAQ,KAAK,QAAQ,QAAQ,IAAI,SAAS;AAEjD,aAAS,IAAI,GAAG,IAAI,IAAI;AAAK,QAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACvF,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,YAAM,QAAQ,IAAI,KAAK;AACvB,YAAM,QAAQ,IAAI,KAAK;AACvB,YAAM,KAAK,EAAE,IAAI;AACjB,YAAM,KAAK,EAAE,OAAO,CAAC;AACrB,YAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI;AACpC,YAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACxC,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,UAAE,IAAI,CAAC,KAAK;AACZ,UAAE,IAAI,IAAI,CAAC,KAAK;MAClB;IACF;AAEA,QAAI,OAAO,EAAE,CAAC;AACd,QAAI,OAAO,EAAE,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,QAAQ,UAAU,CAAC;AACzB,YAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,YAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,YAAM,KAAK,QAAQ,CAAC;AACpB,aAAO,EAAE,EAAE;AACX,aAAO,EAAE,KAAK,CAAC;AACf,QAAE,EAAE,IAAI;AACR,QAAE,KAAK,CAAC,IAAI;IACd;AAEA,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI;AAC/B,eAAS,IAAI,GAAG,IAAI,IAAI;AAAK,UAAE,CAAC,IAAI,EAAE,IAAI,CAAC;AAC3C,eAAS,IAAI,GAAG,IAAI,IAAI;AAAK,UAAE,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,KAAK,EAAE;IAC5E;AAEA,MAAE,CAAC,KAAK,YAAY,KAAK;AACzB,MAAE,CAAC,KAAK,YAAY,KAAK;EAC3B;AACA,IAAE,KAAK,CAAC;AACV;AAGM,IAAO,SAAP,MAAO,gBAAe,KAAY;;EAQtC,YACS,UACA,QACA,WACG,YAAY,OACZ,SAAiB,IAAE;AAE7B,UAAK;AANE,SAAA,WAAA;AACA,SAAA,SAAA;AACA,SAAA,YAAA;AACG,SAAA,YAAA;AACA,SAAA,SAAA;AAXF,SAAA,MAAM;AACN,SAAA,SAAS;AACT,SAAA,WAAW;AAEX,SAAA,YAAY;AAWpB,YAAQ,SAAS;AAGjB,QAAI,KAAK,KAAK,YAAY,KAAK,YAAY;AACzC,YAAM,IAAI,MAAM,0CAA0C;AAC5D,SAAK,QAAQ,IAAI,WAAW,GAAG;AAC/B,SAAK,UAAU,IAAI,KAAK,KAAK;EAC/B;EACU,SAAM;AACd,QAAI,CAAC;AAAM,iBAAW,KAAK,OAAO;AAClC,YAAQ,KAAK,SAAS,KAAK,MAAM;AACjC,QAAI,CAAC;AAAM,iBAAW,KAAK,OAAO;AAClC,SAAK,SAAS;AACd,SAAK,MAAM;EACb;EACA,OAAO,MAAW;AAChB,YAAQ,IAAI;AACZ,UAAM,EAAE,UAAU,MAAK,IAAK;AAC5B,WAAO,QAAQ,IAAI;AACnB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AACpD,eAAS,IAAI,GAAG,IAAI,MAAM;AAAK,cAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAC9D,UAAI,KAAK,QAAQ;AAAU,aAAK,OAAM;IACxC;AACA,WAAO;EACT;EACU,SAAM;AACd,QAAI,KAAK;AAAU;AACnB,SAAK,WAAW;AAChB,UAAM,EAAE,OAAO,QAAQ,KAAK,SAAQ,IAAK;AAEzC,UAAM,GAAG,KAAK;AACd,SAAK,SAAS,SAAU,KAAK,QAAQ,WAAW;AAAG,WAAK,OAAM;AAC9D,UAAM,WAAW,CAAC,KAAK;AACvB,SAAK,OAAM;EACb;EACU,UAAU,KAAe;AACjC,YAAQ,MAAM,KAAK;AACnB,WAAO,GAAG;AACV,SAAK,OAAM;AACX,UAAM,YAAY,KAAK;AACvB,UAAM,EAAE,SAAQ,IAAK;AACrB,aAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,MAAM,OAAO;AAC/C,UAAI,KAAK,UAAU;AAAU,aAAK,OAAM;AACxC,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,QAAQ,MAAM,GAAG;AACvD,UAAI,IAAI,UAAU,SAAS,KAAK,QAAQ,KAAK,SAAS,IAAI,GAAG,GAAG;AAChE,WAAK,UAAU;AACf,aAAO;IACT;AACA,WAAO;EACT;EACA,QAAQ,KAAe;AAErB,QAAI,CAAC,KAAK;AAAW,YAAM,IAAI,MAAM,uCAAuC;AAC5E,WAAO,KAAK,UAAU,GAAG;EAC3B;EACA,IAAI,OAAa;AACf,YAAQ,KAAK;AACb,WAAO,KAAK,QAAQ,IAAI,WAAW,KAAK,CAAC;EAC3C;EACA,WAAW,KAAe;AACxB,YAAQ,KAAK,IAAI;AACjB,QAAI,KAAK;AAAU,YAAM,IAAI,MAAM,6BAA6B;AAChE,SAAK,UAAU,GAAG;AAClB,SAAK,QAAO;AACZ,WAAO;EACT;EACA,SAAM;AACJ,WAAO,KAAK,WAAW,IAAI,WAAW,KAAK,SAAS,CAAC;EACvD;EACA,UAAO;AACL,SAAK,YAAY;AACjB,SAAK,MAAM,KAAK,CAAC;EACnB;EACA,WAAW,IAAW;AACpB,UAAM,EAAE,UAAU,QAAQ,WAAW,QAAQ,UAAS,IAAK;AAC3D,WAAA,KAAO,IAAI,QAAO,UAAU,QAAQ,WAAW,WAAW,MAAM;AAChE,OAAG,QAAQ,IAAI,KAAK,OAAO;AAC3B,OAAG,MAAM,KAAK;AACd,OAAG,SAAS,KAAK;AACjB,OAAG,WAAW,KAAK;AACnB,OAAG,SAAS;AAEZ,OAAG,SAAS;AACZ,OAAG,YAAY;AACf,OAAG,YAAY;AACf,OAAG,YAAY,KAAK;AACpB,WAAO;EACT;;AAGF,IAAM,MAAM,CAAC,QAAgB,UAAkB,cAC7C,gBAAgB,MAAM,IAAI,OAAO,UAAU,QAAQ,SAAS,CAAC;AAGxD,IAAM,WAAkC,IAAI,GAAM,KAAK,MAAM,CAAC;AAE9D,IAAM,WAAkC,IAAI,GAAM,KAAK,MAAM,CAAC;AAE9D,IAAM,WAAkC,IAAI,GAAM,KAAK,MAAM,CAAC;AAE9D,IAAM,WAAkC,IAAI,GAAM,IAAI,MAAM,CAAC;AAG7D,IAAM,aAAoC,IAAI,GAAM,KAAK,MAAM,CAAC;AAEhE,IAAM,aAAoC,IAAI,GAAM,KAAK,MAAM,CAAC;AAEhE,IAAM,aAAoC,IAAI,GAAM,KAAK,MAAM,CAAC;AAEhE,IAAM,aAAoC,IAAI,GAAM,IAAI,MAAM,CAAC;AAItE,IAAM,WAAW,CAAC,QAAgB,UAAkB,cAClD,2BACE,CAAC,OAAkB,CAAA,MACjB,IAAI,OAAO,UAAU,QAAQ,KAAK,UAAU,SAAY,YAAY,KAAK,OAAO,IAAI,CAAC;AAIpF,IAAM,WAAoC,SAAS,IAAM,KAAK,MAAM,CAAC;AAErE,IAAM,WAAoC,SAAS,IAAM,KAAK,MAAM,CAAC;;;AC/NtE,SAAU,UACd,OACA,KAAoB;AAEpB,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQ,WACZ,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE,IAAIE,SAAQ,KAAK,IAAI,KAAK;AAE1D,MAAI,OAAO;AAAS,WAAO;AAC3B,SAAO,MAAM,KAAK;AACpB;;;ACzBA,IAAM,OAAO,CAAC,UAAkB,UAAUC,SAAQ,KAAK,CAAC;AAOlD,SAAU,cAAc,KAAW;AACvC,SAAO,KAAK,GAAG;AACjB;;;ACPM,SAAU,mBACd,WAAuC;AAEvC,MAAI,SAAS;AACb,MAAI,UAAU;AACd,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,QAAQ;AAEZ,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAM,OAAO,UAAU,CAAC;AAGxB,QAAI,CAAC,KAAK,KAAK,GAAG,EAAE,SAAS,IAAI;AAAG,eAAS;AAG7C,QAAI,SAAS;AAAK;AAClB,QAAI,SAAS;AAAK;AAGlB,QAAI,CAAC;AAAQ;AAGb,QAAI,UAAU,GAAG;AACf,UAAI,SAAS,OAAO,CAAC,SAAS,YAAY,EAAE,EAAE,SAAS,MAAM;AAC3D,iBAAS;WACN;AACH,kBAAU;AAGV,YAAI,SAAS,KAAK;AAChB,kBAAQ;AACR;QACF;MACF;AAEA;IACF;AAGA,QAAI,SAAS,KAAK;AAEhB,UAAI,UAAU,IAAI,CAAC,MAAM,OAAO,YAAY,OAAO,YAAY,MAAM;AACnE,kBAAU;AACV,iBAAS;MACX;AACA;IACF;AAEA,cAAU;AACV,eAAW;EACb;AAEA,MAAI,CAAC;AAAO,UAAM,IAAI,UAAU,gCAAgC;AAEhE,SAAO;AACT;;;ACpCO,IAAM,cAAc,CAAC,QAAwC;AAClE,QAAM,QAAQ,MAAK;AACjB,QAAI,OAAO,QAAQ;AAAU,aAAO;AACpC,WAAO,cAAc,GAAG;EAC1B,GAAE;AACF,SAAO,mBAAmB,IAAI;AAChC;;;ACnBM,SAAU,gBAAgB,IAAmC;AACjE,SAAO,cAAc,YAAY,EAAE,CAAC;AACtC;;;ACKO,IAAM,qBAAqB,CAAC,OACjC,MAAM,gBAAgB,EAAE,GAAG,GAAG,CAAC;;;ACjB3B,IAAO,sBAAP,cAAmC,UAAS;EAChD,YAAY,EAAE,QAAO,GAAuB;AAC1C,UAAM,YAAY,OAAO,iBAAiB;MACxC,cAAc;QACZ;QACA;;MAEF,MAAM;KACP;EACH;;;;ACTI,IAAO,SAAP,cAAuC,IAAkB;EAG7D,YAAYC,OAAY;AACtB,UAAK;AAHP,WAAA,eAAA,MAAA,WAAA;;;;;;AAIE,SAAK,UAAUA;EACjB;EAES,IAAI,KAAW;AACtB,UAAM,QAAQ,MAAM,IAAI,GAAG;AAE3B,QAAI,MAAM,IAAI,GAAG,KAAK,UAAU,QAAW;AACzC,WAAK,OAAO,GAAG;AACf,YAAM,IAAI,KAAK,KAAK;IACtB;AAEA,WAAO;EACT;EAES,IAAI,KAAa,OAAY;AACpC,UAAM,IAAI,KAAK,KAAK;AACpB,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS;AAC5C,YAAM,WAAW,KAAK,KAAI,EAAG,KAAI,EAAG;AACpC,UAAI;AAAU,aAAK,OAAO,QAAQ;IACpC;AACA,WAAO;EACT;;;;AC1BF,IAAM,eAAe;AAGd,IAAM,iBAA+B,IAAI,OAAgB,IAAI;AAa9D,SAAU,UACd,SACA,SAAsC;AAEtC,QAAM,EAAE,SAAS,KAAI,IAAK,WAAW,CAAA;AACrC,QAAM,WAAW,GAAG,OAAO,IAAI,MAAM;AAErC,MAAI,eAAe,IAAI,QAAQ;AAAG,WAAO,eAAe,IAAI,QAAQ;AAEpE,QAAM,UAAU,MAAK;AACnB,QAAI,CAAC,aAAa,KAAK,OAAO;AAAG,aAAO;AACxC,QAAI,QAAQ,YAAW,MAAO;AAAS,aAAO;AAC9C,QAAI;AAAQ,aAAO,gBAAgB,OAAkB,MAAM;AAC3D,WAAO;EACT,GAAE;AACF,iBAAe,IAAI,UAAU,MAAM;AACnC,SAAO;AACT;;;AC1BA,IAAM,uBAAqC,IAAI,OAAgB,IAAI;AAO7D,SAAU,gBACd,UAWA,SAA4B;AAE5B,MAAI,qBAAqB,IAAI,GAAG,QAAQ,IAAI,OAAO,EAAE;AACnD,WAAO,qBAAqB,IAAI,GAAG,QAAQ,IAAI,OAAO,EAAE;AAE1D,QAAM,aAAa,UACf,GAAG,OAAO,GAAG,SAAS,YAAW,CAAE,KACnC,SAAS,UAAU,CAAC,EAAE,YAAW;AACrC,QAAMC,QAAO,UAAU,cAAc,UAAU,GAAG,OAAO;AAEzD,QAAM,WACJ,UAAU,WAAW,UAAU,GAAG,OAAO,KAAK,MAAM,IAAI,YACxD,MAAM,EAAE;AACV,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,QAAIA,MAAK,KAAK,CAAC,KAAK,KAAK,KAAK,QAAQ,CAAC,GAAG;AACxC,cAAQ,CAAC,IAAI,QAAQ,CAAC,EAAE,YAAW;IACrC;AACA,SAAKA,MAAK,KAAK,CAAC,IAAI,OAAS,KAAK,QAAQ,IAAI,CAAC,GAAG;AAChD,cAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE,YAAW;IAC7C;EACF;AAEA,QAAM,SAAS,KAAK,QAAQ,KAAK,EAAE,CAAC;AACpC,uBAAqB,IAAI,GAAG,QAAQ,IAAI,OAAO,IAAI,MAAM;AACzD,SAAO;AACT;;;ACnDM,IAAO,sBAAP,cAAmC,UAAS;EAChD,YAAY,EAAE,OAAM,GAAsB;AACxC,UAAM,YAAY,MAAM,0BAA0B;MAChD,MAAM;KACP;EACH;;AAMI,IAAO,2BAAP,cAAwC,UAAS;EACrD,YAAY,EAAE,QAAQ,SAAQ,GAAwC;AACpE,UACE,cAAc,QAAQ,yCAAyC,MAAM,QACrE,EAAE,MAAM,2BAA0B,CAAE;EAExC;;AAOI,IAAO,kCAAP,cAA+C,UAAS;EAC5D,YAAY,EAAE,OAAO,MAAK,GAAoC;AAC5D,UACE,6BAA6B,KAAK,wCAAwC,KAAK,QAC/E,EAAE,MAAM,kCAAiC,CAAE;EAE/C;;;;AC2BF,IAAM,eAAuB;EAC3B,OAAO,IAAI,WAAU;EACrB,UAAU,IAAI,SAAS,IAAI,YAAY,CAAC,CAAC;EACzC,UAAU;EACV,mBAAmB,oBAAI,IAAG;EAC1B,oBAAoB;EACpB,oBAAoB,OAAO;EAC3B,kBAAe;AACb,QAAI,KAAK,sBAAsB,KAAK;AAClC,YAAM,IAAI,gCAAgC;QACxC,OAAO,KAAK,qBAAqB;QACjC,OAAO,KAAK;OACb;EACL;EACA,eAAe,UAAQ;AACrB,QAAI,WAAW,KAAK,WAAW,KAAK,MAAM,SAAS;AACjD,YAAM,IAAI,yBAAyB;QACjC,QAAQ,KAAK,MAAM;QACnB;OACD;EACL;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,aAAa,UAAQ;AACnB,WAAO,KAAK,kBAAkB,IAAI,YAAY,KAAK,QAAQ,KAAK;EAClE;EACA,kBAAkB,QAAM;AACtB,QAAI,SAAS;AAAG,YAAM,IAAI,oBAAoB,EAAE,OAAM,CAAE;AACxD,UAAM,WAAW,KAAK,WAAW;AACjC,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;EAClB;EACA,YAAY,WAAS;AACnB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,aAAa,QAAQ,WAAS;AAC5B,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,SAAS,CAAC;AACzC,WAAO,KAAK,MAAM,SAAS,UAAU,WAAW,MAAM;EACxD;EACA,aAAa,WAAS;AACpB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,QAAQ;AAC5B,WAAO,KAAK,MAAM,QAAQ;EAC5B;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,YACG,KAAK,SAAS,UAAU,QAAQ,KAAK,KACtC,KAAK,SAAS,SAAS,WAAW,CAAC;EAEvC;EACA,cAAc,WAAS;AACrB,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,eAAe,WAAW,CAAC;AAChC,WAAO,KAAK,SAAS,UAAU,QAAQ;EACzC;EACA,SAAS,MAAuB;AAC9B,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,UAAU,OAAgB;AACxB,SAAK,eAAe,KAAK,WAAW,MAAM,SAAS,CAAC;AACpD,SAAK,MAAM,IAAI,OAAO,KAAK,QAAQ;AACnC,SAAK,YAAY,MAAM;EACzB;EACA,UAAU,OAAa;AACrB,SAAK,eAAe,KAAK,QAAQ;AACjC,SAAK,MAAM,KAAK,QAAQ,IAAI;AAC5B,SAAK;EACP;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,SAAS,CAAC;AACjD,SAAK,SAAS,SAAS,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU;AAC7D,SAAK,YAAY;EACnB;EACA,WAAW,OAAa;AACtB,SAAK,eAAe,KAAK,WAAW,CAAC;AACrC,SAAK,SAAS,UAAU,KAAK,UAAU,KAAK;AAC5C,SAAK,YAAY;EACnB;EACA,WAAQ;AACN,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,YAAW;AAC9B,SAAK;AACL,WAAO;EACT;EACA,UAAU,QAAQC,OAAI;AACpB,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAa,MAAM;AACtC,SAAK,YAAYA,SAAQ;AACzB,WAAO;EACT;EACA,YAAS;AACP,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,aAAU;AACR,SAAK,gBAAe;AACpB,SAAK,OAAM;AACX,UAAM,QAAQ,KAAK,cAAa;AAChC,SAAK,YAAY;AACjB,WAAO;EACT;EACA,IAAI,YAAS;AACX,WAAO,KAAK,MAAM,SAAS,KAAK;EAClC;EACA,YAAY,UAAQ;AAClB,UAAM,cAAc,KAAK;AACzB,SAAK,eAAe,QAAQ;AAC5B,SAAK,WAAW;AAChB,WAAO,MAAO,KAAK,WAAW;EAChC;EACA,SAAM;AACJ,QAAI,KAAK,uBAAuB,OAAO;AAAmB;AAC1D,UAAM,QAAQ,KAAK,aAAY;AAC/B,SAAK,kBAAkB,IAAI,KAAK,UAAU,QAAQ,CAAC;AACnD,QAAI,QAAQ;AAAG,WAAK;EACtB;;AAUI,SAAU,aACd,OACA,EAAE,qBAAqB,KAAK,IAAmB,CAAA,GAAE;AAEjD,QAAM,SAAiB,OAAO,OAAO,YAAY;AACjD,SAAO,QAAQ;AACf,SAAO,WAAW,IAAI,SACpB,MAAM,QACN,MAAM,YACN,MAAM,UAAU;AAElB,SAAO,oBAAoB,oBAAI,IAAG;AAClC,SAAO,qBAAqB;AAC5B,SAAO;AACT;;;AC/HM,SAAU,cACd,OACA,OAA0B,CAAA,GAAE;AAE5B,MAAI,OAAO,KAAK,SAAS;AAAa,eAAW,OAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AAC3E,QAAM,MAAM,WAAW,OAAO,IAAI;AAClC,SAAO,YAAY,KAAK,IAAI;AAC9B;AA0BM,SAAU,YACd,QACA,OAAwB,CAAA,GAAE;AAE1B,MAAI,QAAQ;AACZ,MAAI,OAAO,KAAK,SAAS,aAAa;AACpC,eAAW,OAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AACrC,YAAQ,KAAK,KAAK;EACpB;AACA,MAAI,MAAM,SAAS,KAAK,MAAM,CAAC,IAAI;AACjC,UAAM,IAAI,yBAAyB,KAAK;AAC1C,SAAO,QAAQ,MAAM,CAAC,CAAC;AACzB;AAuBM,SAAU,cACd,OACA,OAA0B,CAAA,GAAE;AAE5B,MAAI,OAAO,KAAK,SAAS;AAAa,eAAW,OAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AAC3E,QAAM,MAAM,WAAW,OAAO,IAAI;AAClC,SAAO,YAAY,KAAK,IAAI;AAC9B;AA0BM,SAAU,cACd,QACA,OAA0B,CAAA,GAAE;AAE5B,MAAI,QAAQ;AACZ,MAAI,OAAO,KAAK,SAAS,aAAa;AACpC,eAAW,OAAO,EAAE,MAAM,KAAK,KAAI,CAAE;AACrC,YAAQ,KAAK,OAAO,EAAE,KAAK,QAAO,CAAE;EACtC;AACA,SAAO,IAAI,YAAW,EAAG,OAAO,KAAK;AACvC;;;ACtNM,SAAU,OACd,QAAwB;AAExB,MAAI,OAAO,OAAO,CAAC,MAAM;AACvB,WAAO,UAAU,MAAwB;AAC3C,SAAO,YAAY,MAA8B;AACnD;AAIM,SAAU,YAAY,QAA4B;AACtD,MAAI,SAAS;AACb,aAAW,OAAO,QAAQ;AACxB,cAAU,IAAI;EAChB;AACA,QAAM,SAAS,IAAI,WAAW,MAAM;AACpC,MAAI,SAAS;AACb,aAAW,OAAO,QAAQ;AACxB,WAAO,IAAI,KAAK,MAAM;AACtB,cAAU,IAAI;EAChB;AACA,SAAO;AACT;AAIM,SAAU,UAAU,QAAsB;AAC9C,SAAO,KAAM,OAAiB,OAC5B,CAAC,KAAK,MAAM,MAAM,EAAE,QAAQ,MAAM,EAAE,GACpC,EAAE,CACH;AACH;;;ACnCO,IAAM,eACX;;;AC2EI,SAAU,oBAGd,QACA,QAES;AAET,MAAI,OAAO,WAAW,OAAO;AAC3B,UAAM,IAAI,+BAA+B;MACvC,gBAAgB,OAAO;MACvB,aAAa,OAAO;KACrB;AAEH,QAAM,iBAAiB,cAAc;IACnC;IACA;GACD;AACD,QAAM,OAAO,aAAa,cAAc;AACxC,MAAI,KAAK,WAAW;AAAG,WAAO;AAC9B,SAAO;AACT;AAWA,SAAS,cAA4D,EACnE,QACA,OAAM,GAIP;AACC,QAAM,iBAAkC,CAAA;AACxC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,mBAAe,KAAK,aAAa,EAAE,OAAO,OAAO,CAAC,GAAG,OAAO,OAAO,CAAC,EAAC,CAAE,CAAC;EAC1E;AACA,SAAO;AACT;AAcA,SAAS,aAA+C,EACtD,OACA,MAAK,GAIN;AACC,QAAM,kBAAkB,mBAAmB,MAAM,IAAI;AACrD,MAAI,iBAAiB;AACnB,UAAM,CAAC,QAAQ,IAAI,IAAI;AACvB,WAAO,YAAY,OAAO,EAAE,QAAQ,OAAO,EAAE,GAAG,OAAO,KAAI,EAAE,CAAE;EACjE;AACA,MAAI,MAAM,SAAS,SAAS;AAC1B,WAAO,YAAY,OAA2B;MAC5C;KACD;EACH;AACA,MAAI,MAAM,SAAS,WAAW;AAC5B,WAAO,cAAc,KAAuB;EAC9C;AACA,MAAI,MAAM,SAAS,QAAQ;AACzB,WAAO,WAAW,KAA2B;EAC/C;AACA,MAAI,MAAM,KAAK,WAAW,MAAM,KAAK,MAAM,KAAK,WAAW,KAAK,GAAG;AACjE,UAAM,SAAS,MAAM,KAAK,WAAW,KAAK;AAC1C,UAAM,CAAC,EAAC,EAAGC,QAAO,KAAK,IAAI,aAAa,KAAK,MAAM,IAAI,KAAK,CAAA;AAC5D,WAAO,aAAa,OAA4B;MAC9C;MACA,MAAM,OAAOA,KAAI;KAClB;EACH;AACA,MAAI,MAAM,KAAK,WAAW,OAAO,GAAG;AAClC,WAAO,YAAY,OAAyB,EAAE,MAAK,CAAE;EACvD;AACA,MAAI,MAAM,SAAS,UAAU;AAC3B,WAAO,aAAa,KAA0B;EAChD;AACA,QAAM,IAAI,4BAA4B,MAAM,MAAM;IAChD,UAAU;GACX;AACH;AAMA,SAAS,aAAa,gBAA+B;AAEnD,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,UAAM,EAAE,SAAS,QAAO,IAAK,eAAe,CAAC;AAC7C,QAAI;AAAS,oBAAc;;AACtB,oBAAc,KAAK,OAAO;EACjC;AAGA,QAAM,eAAsB,CAAA;AAC5B,QAAM,gBAAuB,CAAA;AAC7B,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,UAAM,EAAE,SAAS,QAAO,IAAK,eAAe,CAAC;AAC7C,QAAI,SAAS;AACX,mBAAa,KAAK,YAAY,aAAa,aAAa,EAAE,MAAM,GAAE,CAAE,CAAC;AACrE,oBAAc,KAAK,OAAO;AAC1B,qBAAe,KAAK,OAAO;IAC7B,OAAO;AACL,mBAAa,KAAK,OAAO;IAC3B;EACF;AAGA,SAAO,OAAO,CAAC,GAAG,cAAc,GAAG,aAAa,CAAC;AACnD;AASA,SAAS,cAAc,OAAU;AAC/B,MAAI,CAAC,UAAU,KAAK;AAAG,UAAM,IAAI,oBAAoB,EAAE,SAAS,MAAK,CAAE;AACvE,SAAO,EAAE,SAAS,OAAO,SAAS,OAAO,MAAM,YAAW,CAAS,EAAC;AACtE;AAYA,SAAS,YACP,OACA,EACE,QACA,MAAK,GAIN;AAED,QAAM,UAAU,WAAW;AAE3B,MAAI,CAAC,MAAM,QAAQ,KAAK;AAAG,UAAM,IAAI,kBAAkB,KAAK;AAC5D,MAAI,CAAC,WAAW,MAAM,WAAW;AAC/B,UAAM,IAAI,oCAAoC;MAC5C,gBAAgB;MAChB,aAAa,MAAM;MACnB,MAAM,GAAG,MAAM,IAAI,IAAI,MAAM;KAC9B;AAEH,MAAI,eAAe;AACnB,QAAM,iBAAkC,CAAA;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,gBAAgB,aAAa,EAAE,OAAO,OAAO,MAAM,CAAC,EAAC,CAAE;AAC7D,QAAI,cAAc;AAAS,qBAAe;AAC1C,mBAAe,KAAK,aAAa;EACnC;AAEA,MAAI,WAAW,cAAc;AAC3B,UAAM,OAAO,aAAa,cAAc;AACxC,QAAI,SAAS;AACX,YAAMC,UAAS,YAAY,eAAe,QAAQ,EAAE,MAAM,GAAE,CAAE;AAC9D,aAAO;QACL,SAAS;QACT,SAAS,eAAe,SAAS,IAAI,OAAO,CAACA,SAAQ,IAAI,CAAC,IAAIA;;IAElE;AACA,QAAI;AAAc,aAAO,EAAE,SAAS,MAAM,SAAS,KAAI;EACzD;AACA,SAAO;IACL,SAAS;IACT,SAAS,OAAO,eAAe,IAAI,CAAC,EAAE,QAAO,MAAO,OAAO,CAAC;;AAEhE;AAUA,SAAS,YACP,OACA,EAAE,MAAK,GAAoB;AAE3B,QAAM,CAAC,EAAE,SAAS,IAAI,MAAM,KAAK,MAAM,OAAO;AAC9C,QAAM,YAAY,KAAK,KAAK;AAC5B,MAAI,CAAC,WAAW;AACd,QAAI,SAAS;AAGb,QAAI,YAAY,OAAO;AACrB,eAAS,OAAO,QAAQ;QACtB,KAAK;QACL,MAAM,KAAK,MAAM,MAAM,SAAS,KAAK,IAAI,EAAE,IAAI;OAChD;AACH,WAAO;MACL,SAAS;MACT,SAAS,OAAO,CAAC,OAAO,YAAY,WAAW,EAAE,MAAM,GAAE,CAAE,CAAC,GAAG,MAAM,CAAC;;EAE1E;AACA,MAAI,cAAc,OAAO,SAAS,SAAS;AACzC,UAAM,IAAI,kCAAkC;MAC1C,cAAc,OAAO,SAAS,SAAS;MACvC;KACD;AACH,SAAO,EAAE,SAAS,OAAO,SAAS,OAAO,OAAO,EAAE,KAAK,QAAO,CAAE,EAAC;AACnE;AAIA,SAAS,WAAW,OAAc;AAChC,MAAI,OAAO,UAAU;AACnB,UAAM,IAAI,UACR,2BAA2B,KAAK,YAAY,OAAO,KAAK,qCAAqC;AAEjG,SAAO,EAAE,SAAS,OAAO,SAAS,OAAO,UAAU,KAAK,CAAC,EAAC;AAC5D;AAIA,SAAS,aACP,OACA,EAAE,QAAQ,MAAAD,QAAO,IAAG,GAAkD;AAEtE,MAAI,OAAOA,UAAS,UAAU;AAC5B,UAAM,MAAM,OAAO,OAAOA,KAAI,KAAK,SAAS,KAAK,OAAO;AACxD,UAAM,MAAM,SAAS,CAAC,MAAM,KAAK;AACjC,QAAI,QAAQ,OAAO,QAAQ;AACzB,YAAM,IAAI,uBAAuB;QAC/B,KAAK,IAAI,SAAQ;QACjB,KAAK,IAAI,SAAQ;QACjB;QACA,MAAMA,QAAO;QACb,OAAO,MAAM,SAAQ;OACtB;EACL;AACA,SAAO;IACL,SAAS;IACT,SAAS,YAAY,OAAO;MAC1B,MAAM;MACN;KACD;;AAEL;AAWA,SAAS,aAAa,OAAa;AACjC,QAAM,WAAW,YAAY,KAAK;AAClC,QAAM,cAAc,KAAK,KAAK,KAAK,QAAQ,IAAI,EAAE;AACjD,QAAM,QAAe,CAAA;AACrB,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,UAAM,KACJ,OAAO,MAAM,UAAU,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG;MAC5C,KAAK;KACN,CAAC;EAEN;AACA,SAAO;IACL,SAAS;IACT,SAAS,OAAO;MACd,OAAO,YAAY,KAAK,QAAQ,GAAG,EAAE,MAAM,GAAE,CAAE,CAAC;MAChD,GAAG;KACJ;;AAEL;AASA,SAAS,YAGP,OACA,EAAE,MAAK,GAAoB;AAE3B,MAAI,UAAU;AACd,QAAM,iBAAkC,CAAA;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,KAAK;AAChD,UAAM,SAAS,MAAM,WAAW,CAAC;AACjC,UAAM,QAAQ,MAAM,QAAQ,KAAK,IAAI,IAAI,OAAO;AAChD,UAAM,gBAAgB,aAAa;MACjC,OAAO;MACP,OAAQ,MAAc,KAAM;KAC7B;AACD,mBAAe,KAAK,aAAa;AACjC,QAAI,cAAc;AAAS,gBAAU;EACvC;AACA,SAAO;IACL;IACA,SAAS,UACL,aAAa,cAAc,IAC3B,OAAO,eAAe,IAAI,CAAC,EAAE,QAAO,MAAO,OAAO,CAAC;;AAE3D;AAIM,SAAU,mBACd,MAAY;AAEZ,QAAM,UAAU,KAAK,MAAM,kBAAkB;AAC7C,SAAO;;IAEH,CAAC,QAAQ,CAAC,IAAI,OAAO,QAAQ,CAAC,CAAC,IAAI,MAAM,QAAQ,CAAC,CAAC;MACnD;AACN;;;ACzXM,SAAU,oBAGd,QACA,MAAqB;AAErB,QAAM,QAAQ,OAAO,SAAS,WAAW,WAAW,IAAI,IAAI;AAC5D,QAAM,SAAS,aAAa,KAAK;AAEjC,MAAI,KAAK,KAAK,MAAM,KAAK,OAAO,SAAS;AACvC,UAAM,IAAI,yBAAwB;AACpC,MAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI;AAC7B,UAAM,IAAI,iCAAiC;MACzC,MAAM,OAAO,SAAS,WAAW,OAAO,WAAW,IAAI;MACvD;MACA,MAAM,KAAK,IAAI;KAChB;AAEH,MAAI,WAAW;AACf,QAAM,SAAS,CAAA;AACf,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,WAAO,YAAY,QAAQ;AAC3B,UAAM,CAACE,OAAM,SAAS,IAAI,gBAAgB,QAAQ,OAAO;MACvD,gBAAgB;KACjB;AACD,gBAAY;AACZ,WAAO,KAAKA,KAAI;EAClB;AACA,SAAO;AACT;AAYA,SAAS,gBACP,QACA,OACA,EAAE,eAAc,GAA8B;AAE9C,QAAM,kBAAkB,mBAAmB,MAAM,IAAI;AACrD,MAAI,iBAAiB;AACnB,UAAM,CAAC,QAAQ,IAAI,IAAI;AACvB,WAAO,YAAY,QAAQ,EAAE,GAAG,OAAO,KAAI,GAAI,EAAE,QAAQ,eAAc,CAAE;EAC3E;AACA,MAAI,MAAM,SAAS;AACjB,WAAO,YAAY,QAAQ,OAA4B,EAAE,eAAc,CAAE;AAE3E,MAAI,MAAM,SAAS;AAAW,WAAO,cAAc,MAAM;AACzD,MAAI,MAAM,SAAS;AAAQ,WAAO,WAAW,MAAM;AACnD,MAAI,MAAM,KAAK,WAAW,OAAO;AAC/B,WAAO,YAAY,QAAQ,OAAO,EAAE,eAAc,CAAE;AACtD,MAAI,MAAM,KAAK,WAAW,MAAM,KAAK,MAAM,KAAK,WAAW,KAAK;AAC9D,WAAO,aAAa,QAAQ,KAAK;AACnC,MAAI,MAAM,SAAS;AAAU,WAAO,aAAa,QAAQ,EAAE,eAAc,CAAE;AAC3E,QAAM,IAAI,4BAA4B,MAAM,MAAM;IAChD,UAAU;GACX;AACH;AAKA,IAAM,eAAe;AACrB,IAAM,eAAe;AAQrB,SAAS,cAAc,QAAc;AACnC,QAAM,QAAQ,OAAO,UAAU,EAAE;AACjC,SAAO,CAAC,gBAAgB,WAAW,WAAW,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE;AACjE;AAIA,SAAS,YACP,QACA,OACA,EAAE,QAAQ,eAAc,GAAqD;AAI7E,MAAI,CAAC,QAAQ;AAEX,UAAM,SAAS,cAAc,OAAO,UAAU,YAAY,CAAC;AAG3D,UAAM,QAAQ,iBAAiB;AAC/B,UAAM,cAAc,QAAQ;AAG5B,WAAO,YAAY,KAAK;AACxB,UAAMC,UAAS,cAAc,OAAO,UAAU,YAAY,CAAC;AAG3D,UAAM,eAAe,gBAAgB,KAAK;AAE1C,QAAIC,YAAW;AACf,UAAMC,SAAmB,CAAA;AACzB,aAAS,IAAI,GAAG,IAAIF,SAAQ,EAAE,GAAG;AAG/B,aAAO,YAAY,eAAe,eAAe,IAAI,KAAKC,UAAS;AACnE,YAAM,CAAC,MAAM,SAAS,IAAI,gBAAgB,QAAQ,OAAO;QACvD,gBAAgB;OACjB;AACD,MAAAA,aAAY;AACZ,MAAAC,OAAM,KAAK,IAAI;IACjB;AAGA,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAACA,QAAO,EAAE;EACnB;AAKA,MAAI,gBAAgB,KAAK,GAAG;AAE1B,UAAM,SAAS,cAAc,OAAO,UAAU,YAAY,CAAC;AAG3D,UAAM,QAAQ,iBAAiB;AAE/B,UAAMA,SAAmB,CAAA;AACzB,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAE/B,aAAO,YAAY,QAAQ,IAAI,EAAE;AACjC,YAAM,CAAC,IAAI,IAAI,gBAAgB,QAAQ,OAAO;QAC5C,gBAAgB;OACjB;AACD,MAAAA,OAAM,KAAK,IAAI;IACjB;AAGA,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAACA,QAAO,EAAE;EACnB;AAIA,MAAI,WAAW;AACf,QAAM,QAAmB,CAAA;AACzB,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,UAAM,CAAC,MAAM,SAAS,IAAI,gBAAgB,QAAQ,OAAO;MACvD,gBAAgB,iBAAiB;KAClC;AACD,gBAAY;AACZ,UAAM,KAAK,IAAI;EACjB;AACA,SAAO,CAAC,OAAO,QAAQ;AACzB;AAIA,SAAS,WAAW,QAAc;AAChC,SAAO,CAAC,YAAY,OAAO,UAAU,EAAE,GAAG,EAAE,MAAM,GAAE,CAAE,GAAG,EAAE;AAC7D;AAOA,SAAS,YACP,QACA,OACA,EAAE,eAAc,GAA8B;AAE9C,QAAM,CAAC,GAAGC,KAAI,IAAI,MAAM,KAAK,MAAM,OAAO;AAC1C,MAAI,CAACA,OAAM;AAET,UAAM,SAAS,cAAc,OAAO,UAAU,EAAE,CAAC;AAGjD,WAAO,YAAY,iBAAiB,MAAM;AAE1C,UAAM,SAAS,cAAc,OAAO,UAAU,EAAE,CAAC;AAGjD,QAAI,WAAW,GAAG;AAEhB,aAAO,YAAY,iBAAiB,EAAE;AACtC,aAAO,CAAC,MAAM,EAAE;IAClB;AAEA,UAAM,OAAO,OAAO,UAAU,MAAM;AAGpC,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAAC,WAAW,IAAI,GAAG,EAAE;EAC9B;AAEA,QAAM,QAAQ,WAAW,OAAO,UAAU,OAAO,SAASA,KAAI,GAAG,EAAE,CAAC;AACpE,SAAO,CAAC,OAAO,EAAE;AACnB;AAOA,SAAS,aAAa,QAAgB,OAAmB;AACvD,QAAM,SAAS,MAAM,KAAK,WAAW,KAAK;AAC1C,QAAMA,QAAO,OAAO,SAAS,MAAM,KAAK,MAAM,KAAK,EAAE,CAAC,KAAK,KAAK;AAChE,QAAM,QAAQ,OAAO,UAAU,EAAE;AACjC,SAAO;IACLA,QAAO,KACH,cAAc,OAAO,EAAE,OAAM,CAAE,IAC/B,cAAc,OAAO,EAAE,OAAM,CAAE;IACnC;;AAEJ;AAMA,SAAS,YACP,QACA,OACA,EAAE,eAAc,GAA8B;AAM9C,QAAM,kBACJ,MAAM,WAAW,WAAW,KAAK,MAAM,WAAW,KAAK,CAAC,EAAE,KAAI,MAAO,CAAC,IAAI;AAI5E,QAAM,QAAa,kBAAkB,CAAA,IAAK,CAAA;AAC1C,MAAI,WAAW;AAIf,MAAI,gBAAgB,KAAK,GAAG;AAE1B,UAAM,SAAS,cAAc,OAAO,UAAU,YAAY,CAAC;AAG3D,UAAM,QAAQ,iBAAiB;AAE/B,aAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,YAAM,YAAY,MAAM,WAAW,CAAC;AACpC,aAAO,YAAY,QAAQ,QAAQ;AACnC,YAAM,CAAC,MAAM,SAAS,IAAI,gBAAgB,QAAQ,WAAW;QAC3D,gBAAgB;OACjB;AACD,kBAAY;AACZ,YAAM,kBAAkB,IAAI,WAAW,IAAK,IAAI;IAClD;AAGA,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAAC,OAAO,EAAE;EACnB;AAIA,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,UAAM,YAAY,MAAM,WAAW,CAAC;AACpC,UAAM,CAAC,MAAM,SAAS,IAAI,gBAAgB,QAAQ,WAAW;MAC3D;KACD;AACD,UAAM,kBAAkB,IAAI,WAAW,IAAK,IAAI;AAChD,gBAAY;EACd;AACA,SAAO,CAAC,OAAO,QAAQ;AACzB;AAQA,SAAS,aACP,QACA,EAAE,eAAc,GAA8B;AAG9C,QAAM,SAAS,cAAc,OAAO,UAAU,EAAE,CAAC;AAGjD,QAAM,QAAQ,iBAAiB;AAC/B,SAAO,YAAY,KAAK;AAExB,QAAM,SAAS,cAAc,OAAO,UAAU,EAAE,CAAC;AAGjD,MAAI,WAAW,GAAG;AAChB,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAAC,IAAI,EAAE;EAChB;AAEA,QAAM,OAAO,OAAO,UAAU,QAAQ,EAAE;AACxC,QAAM,QAAQ,cAAc,KAAK,IAAI,CAAC;AAGtC,SAAO,YAAY,iBAAiB,EAAE;AAEtC,SAAO,CAAC,OAAO,EAAE;AACnB;AAEA,SAAS,gBAAgB,OAAmB;AAC1C,QAAM,EAAE,KAAI,IAAK;AACjB,MAAI,SAAS;AAAU,WAAO;AAC9B,MAAI,SAAS;AAAS,WAAO;AAC7B,MAAI,KAAK,SAAS,IAAI;AAAG,WAAO;AAEhC,MAAI,SAAS;AAAS,WAAQ,MAAc,YAAY,KAAK,eAAe;AAE5E,QAAM,kBAAkB,mBAAmB,MAAM,IAAI;AACrD,MACE,mBACA,gBAAgB,EAAE,GAAG,OAAO,MAAM,gBAAgB,CAAC,EAAC,CAAkB;AAEtE,WAAO;AAET,SAAO;AACT;;;ACjUM,SAAU,kBACd,YAA4C;AAE5C,QAAM,EAAE,KAAK,KAAI,IAAK;AAEtB,QAAM,YAAY,MAAM,MAAM,GAAG,CAAC;AAClC,MAAI,cAAc;AAAM,UAAM,IAAI,yBAAwB;AAE1D,QAAM,OAAO,CAAC,GAAI,OAAO,CAAA,GAAK,eAAe,aAAa;AAC1D,QAAM,UAAU,KAAK,KACnB,CAAC,MACC,EAAE,SAAS,WAAW,cAAc,mBAAmBC,eAAc,CAAC,CAAC,CAAC;AAE5E,MAAI,CAAC;AACH,UAAM,IAAI,+BAA+B,WAAW;MAClD,UAAU;KACX;AACH,SAAO;IACL;IACA,MACE,YAAY,WAAW,QAAQ,UAAU,QAAQ,OAAO,SAAS,IAC7D,oBAAoB,QAAQ,QAAQ,MAAM,MAAM,CAAC,CAAC,IAClD;IACN,WAAY,QAA6B;;AAE7C;;;ACrFO,IAAM,YAAmC,CAAC,OAAO,UAAU,UAChE,KAAK,UACH,OACA,CAAC,KAAK,WAAU;AACd,QAAMC,SAAQ,OAAO,WAAW,WAAW,OAAO,SAAQ,IAAK;AAC/D,SAAO,OAAO,aAAa,aAAa,SAAS,KAAKA,MAAK,IAAIA;AACjE,GACA,KAAK;;;ACIF,IAAM,kBAAkB;;;ACgEzB,SAAU,WAKd,YAAiD;AAEjD,QAAM,EAAE,KAAK,OAAO,CAAA,GAAI,KAAI,IAAK;AAEjC,QAAM,aAAa,MAAM,MAAM,EAAE,QAAQ,MAAK,CAAE;AAChD,QAAM,WAAY,IAAY,OAAO,CAAC,YAAW;AAC/C,QAAI,YAAY;AACd,UAAI,QAAQ,SAAS;AACnB,eAAO,mBAAmB,OAAO,MAAM;AACzC,UAAI,QAAQ,SAAS;AAAS,eAAO,gBAAgB,OAAO,MAAM;AAClE,aAAO;IACT;AACA,WAAO,UAAU,WAAW,QAAQ,SAAS;EAC/C,CAAC;AAED,MAAI,SAAS,WAAW;AACtB,WAAO;AACT,MAAI,SAAS,WAAW;AACtB,WAAO,SAAS,CAAC;AAEnB,MAAI,iBAAsC;AAC1C,aAAW,WAAW,UAAU;AAC9B,QAAI,EAAE,YAAY;AAAU;AAC5B,QAAI,CAAC,QAAQ,KAAK,WAAW,GAAG;AAC9B,UAAI,CAAC,QAAQ,UAAU,QAAQ,OAAO,WAAW;AAC/C,eAAO;AACT;IACF;AACA,QAAI,CAAC,QAAQ;AAAQ;AACrB,QAAI,QAAQ,OAAO,WAAW;AAAG;AACjC,QAAI,QAAQ,OAAO,WAAW,KAAK;AAAQ;AAC3C,UAAM,UAAU,KAAK,MAAM,CAAC,KAAK,UAAS;AACxC,YAAM,eAAe,YAAY,WAAW,QAAQ,OAAQ,KAAK;AACjE,UAAI,CAAC;AAAc,eAAO;AAC1B,aAAO,YAAY,KAAK,YAAY;IACtC,CAAC;AACD,QAAI,SAAS;AAEX,UACE,kBACA,YAAY,kBACZ,eAAe,QACf;AACA,cAAM,iBAAiB,kBACrB,QAAQ,QACR,eAAe,QACf,IAA0B;AAE5B,YAAI;AACF,gBAAM,IAAI,sBACR;YACE;YACA,MAAM,eAAe,CAAC;aAExB;YACE,SAAS;YACT,MAAM,eAAe,CAAC;WACvB;MAEP;AAEA,uBAAiB;IACnB;EACF;AAEA,MAAI;AACF,WAAO;AACT,SAAO,SAAS,CAAC;AACnB;AAKM,SAAU,YAAY,KAAc,cAA0B;AAClE,QAAM,UAAU,OAAO;AACvB,QAAM,mBAAmB,aAAa;AACtC,UAAQ,kBAAkB;IACxB,KAAK;AACH,aAAO,UAAU,KAAgB,EAAE,QAAQ,MAAK,CAAE;IACpD,KAAK;AACH,aAAO,YAAY;IACrB,KAAK;AACH,aAAO,YAAY;IACrB,KAAK;AACH,aAAO,YAAY;IACrB,SAAS;AACP,UAAI,qBAAqB,WAAW,gBAAgB;AAClD,eAAO,OAAO,OAAO,aAAa,UAAU,EAAE,MAC5C,CAAC,WAAW,UAAS;AACnB,iBAAO,YACL,OAAO,OAAO,GAA0C,EAAE,KAAK,GAC/D,SAAyB;QAE7B,CAAC;AAKL,UACE,+HAA+H,KAC7H,gBAAgB;AAGlB,eAAO,YAAY,YAAY,YAAY;AAI7C,UAAI,uCAAuC,KAAK,gBAAgB;AAC9D,eAAO,YAAY,YAAY,eAAe;AAIhD,UAAI,oCAAoC,KAAK,gBAAgB,GAAG;AAC9D,eACE,MAAM,QAAQ,GAAG,KACjB,IAAI,MAAM,CAAC,MACT,YAAY,GAAG;UACb,GAAG;;UAEH,MAAM,iBAAiB,QAAQ,oBAAoB,EAAE;SACtC,CAAC;MAGxB;AAEA,aAAO;IACT;EACF;AACF;AAGM,SAAU,kBACd,kBACA,kBACA,MAAiB;AAEjB,aAAW,kBAAkB,kBAAkB;AAC7C,UAAM,kBAAkB,iBAAiB,cAAc;AACvD,UAAM,kBAAkB,iBAAiB,cAAc;AAEvD,QACE,gBAAgB,SAAS,WACzB,gBAAgB,SAAS,WACzB,gBAAgB,mBAChB,gBAAgB;AAEhB,aAAO,kBACL,gBAAgB,YAChB,gBAAgB,YACf,KAAa,cAAc,CAAC;AAGjC,UAAM,QAAQ,CAAC,gBAAgB,MAAM,gBAAgB,IAAI;AAEzD,UAAM,aAAa,MAAK;AACtB,UAAI,MAAM,SAAS,SAAS,KAAK,MAAM,SAAS,SAAS;AAAG,eAAO;AACnE,UAAI,MAAM,SAAS,SAAS,KAAK,MAAM,SAAS,QAAQ;AACtD,eAAO,UAAU,KAAK,cAAc,GAAc,EAAE,QAAQ,MAAK,CAAE;AACrE,UAAI,MAAM,SAAS,SAAS,KAAK,MAAM,SAAS,OAAO;AACrD,eAAO,UAAU,KAAK,cAAc,GAAc,EAAE,QAAQ,MAAK,CAAE;AACrE,aAAO;IACT,GAAE;AAEF,QAAI;AAAW,aAAO;EACxB;AAEA;AACF;;;AC3PO,IAAM,aAAa;EACxB,MAAM;EACN,KAAK;;AAEA,IAAM,YAAY;EACvB,OAAO;EACP,KAAK;;;;ACSD,SAAU,YAAY,OAAe,UAAgB;AACzD,MAAI,UAAU,MAAM,SAAQ;AAE5B,QAAM,WAAW,QAAQ,WAAW,GAAG;AACvC,MAAI;AAAU,cAAU,QAAQ,MAAM,CAAC;AAEvC,YAAU,QAAQ,SAAS,UAAU,GAAG;AAExC,MAAI,CAAC,SAAS,QAAQ,IAAI;IACxB,QAAQ,MAAM,GAAG,QAAQ,SAAS,QAAQ;IAC1C,QAAQ,MAAM,QAAQ,SAAS,QAAQ;;AAEzC,aAAW,SAAS,QAAQ,SAAS,EAAE;AACvC,SAAO,GAAG,WAAW,MAAM,EAAE,GAAG,WAAW,GAAG,GAC5C,WAAW,IAAI,QAAQ,KAAK,EAC9B;AACF;;;ACdM,SAAU,YAAY,KAAa,OAAuB,OAAK;AACnE,SAAO,YAAY,KAAK,WAAW,IAAI,CAAC;AAC1C;;;ACFM,SAAU,WAAW,KAAa,OAAc,OAAK;AACzD,SAAO,YAAY,KAAK,UAAU,IAAI,CAAC;AACzC;;;ACZM,IAAO,4BAAP,cAAyC,UAAS;EACtD,YAAY,EAAE,QAAO,GAAuB;AAC1C,UAAM,sBAAsB,OAAO,4BAA4B;MAC7D,MAAM;KACP;EACH;;AAOI,IAAO,+BAAP,cAA4C,UAAS;EACzD,cAAA;AACE,UAAM,oDAAoD;MACxD,MAAM;KACP;EACH;;AAII,SAAU,mBAAmB,cAA0B;AAC3D,SAAO,aAAa,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAK,MAAM;AACrD,WAAO,GAAG,MAAM,WAAW,IAAI,KAAK,KAAK;;EAC3C,GAAG,EAAE;AACP;AAEM,SAAU,oBAAoB,eAA4B;AAC9D,SAAO,cACJ,OAAO,CAAC,QAAQ,EAAE,SAAS,GAAG,MAAK,MAAM;AACxC,QAAI,MAAM,GAAG,MAAM,OAAO,OAAO;;AACjC,QAAI,MAAM;AAAO,aAAO,gBAAgB,MAAM,KAAK;;AACnD,QAAI,MAAM;AAAS,aAAO,kBAAkB,MAAM,OAAO;;AACzD,QAAI,MAAM;AAAM,aAAO,eAAe,MAAM,IAAI;;AAChD,QAAI,MAAM,OAAO;AACf,aAAO;AACP,aAAO,mBAAmB,MAAM,KAAK;IACvC;AACA,QAAI,MAAM,WAAW;AACnB,aAAO;AACP,aAAO,mBAAmB,MAAM,SAAS;IAC3C;AACA,WAAO;EACT,GAAG,qBAAqB,EACvB,MAAM,GAAG,EAAE;AAChB;;;ACzCM,SAAU,YACd,MAA4E;AAE5E,QAAM,UAAU,OAAO,QAAQ,IAAI,EAChC,IAAI,CAAC,CAAC,KAAK,KAAK,MAAK;AACpB,QAAI,UAAU,UAAa,UAAU;AAAO,aAAO;AACnD,WAAO,CAAC,KAAK,KAAK;EACpB,CAAC,EACA,OAAO,OAAO;AACjB,QAAM,YAAY,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC;AAC7E,SAAO,QACJ,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,KAAK,GAAG,GAAG,IAAI,OAAO,YAAY,CAAC,CAAC,KAAK,KAAK,EAAE,EACtE,KAAK,IAAI;AACd;AAKM,IAAO,mBAAP,cAAgC,UAAS;EAC7C,cAAA;AACE,UACE;MACE;MACA;MACA,KAAK,IAAI,GACX,EAAE,MAAM,mBAAkB,CAAE;EAEhC;;;;ACjCK,IAAM,SAAS,CAAC,QAAgB;;;ACqBjC,IAAO,qBAAP,cAAkC,UAAS;EAG/C,YACE,OACA,EACE,SAAS,UACT,UAAAC,WACA,OACA,MACA,KACA,UACA,cACA,sBACA,OACA,IACA,OACA,cAAa,GAId;AAED,UAAM,UAAU,WAAW,aAAa,QAAQ,IAAI;AACpD,QAAI,aAAa,YAAY;MAC3B,MAAM,SAAS;MACf;MACA,OACE,OAAO,UAAU,eACjB,GAAG,YAAY,KAAK,CAAC,IAAI,OAAO,gBAAgB,UAAU,KAAK;MACjE;MACA;MACA,UACE,OAAO,aAAa,eAAe,GAAG,WAAW,QAAQ,CAAC;MAC5D,cACE,OAAO,iBAAiB,eACxB,GAAG,WAAW,YAAY,CAAC;MAC7B,sBACE,OAAO,yBAAyB,eAChC,GAAG,WAAW,oBAAoB,CAAC;MACrC;KACD;AAED,QAAI,eAAe;AACjB,oBAAc;EAAK,oBAAoB,aAAa,CAAC;IACvD;AAEA,UAAM,MAAM,cAAc;MACxB;MACA,UAAAA;MACA,cAAc;QACZ,GAAI,MAAM,eAAe,CAAC,GAAG,MAAM,cAAc,GAAG,IAAI,CAAA;QACxD;QACA;QACA,OAAO,OAAO;MAChB,MAAM;KACP;AAvDM,WAAA,eAAA,MAAA,SAAA;;;;;;AAwDP,SAAK,QAAQ;EACf;;AAuMI,IAAO,sCAAP,cAAmD,UAAS;EAChE,YAAY,EAAE,QAAO,GAAqC;AACxD,UACE,qDACE,UAAU,iBAAiB,OAAO,OAAO,EAC3C,IACA;MACE,cAAc;QACZ;QACA;QACA;;MAEF,MAAM;KACP;EAEL;;AAMI,IAAO,mBAAP,cAAgC,UAAS;EAK7C,YAAY,EACV,MACA,QAAO,GAIR;AACC,UAAM,WAAW,IAAI,EAAE,MAAM,mBAAkB,CAAE;AAXnD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAEP,WAAA,eAAA,MAAA,QAAA;;;;;;AAUE,SAAK,OAAO;EACd;;;;ACtSF,IAAM,WAAW;AAsGX,SAAU,qBAiBd,YAAmE;AAEnE,QAAM,EAAE,KAAK,MAAM,cAAc,KAAI,IACnC;AAEF,MAAI,UAAU,IAAI,CAAC;AACnB,MAAI,cAAc;AAChB,UAAM,OAAO,WAAW,EAAE,KAAK,MAAM,MAAM,aAAY,CAAE;AACzD,QAAI,CAAC;AAAM,YAAM,IAAI,yBAAyB,cAAc,EAAE,SAAQ,CAAE;AACxE,cAAU;EACZ;AAEA,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,yBAAyB,QAAW,EAAE,SAAQ,CAAE;AAC5D,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,gCAAgC,QAAQ,MAAM,EAAE,SAAQ,CAAE;AAEtE,QAAM,SAAS,oBAAoB,QAAQ,SAAS,IAAI;AACxD,MAAI,UAAU,OAAO,SAAS;AAC5B,WAAO;AACT,MAAI,UAAU,OAAO,WAAW;AAC9B,WAAO,OAAO,CAAC;AACjB,SAAO;AACT;;;ACrJA,IAAMC,YAAW;AAgCX,SAAU,iBACd,YAA2C;AAE3C,QAAM,EAAE,KAAK,MAAM,SAAQ,IAAK;AAChC,MAAI,CAAC,QAAQ,KAAK,WAAW;AAAG,WAAO;AAEvC,QAAM,cAAc,IAAI,KAAK,CAAC,MAAM,UAAU,KAAK,EAAE,SAAS,aAAa;AAC3E,MAAI,CAAC;AAAa,UAAM,IAAI,4BAA4B,EAAE,UAAAA,UAAQ,CAAE;AACpE,MAAI,EAAE,YAAY;AAChB,UAAM,IAAI,kCAAkC,EAAE,UAAAA,UAAQ,CAAE;AAC1D,MAAI,CAAC,YAAY,UAAU,YAAY,OAAO,WAAW;AACvD,UAAM,IAAI,kCAAkC,EAAE,UAAAA,UAAQ,CAAE;AAE1D,QAAM,OAAO,oBAAoB,YAAY,QAAQ,IAAI;AACzD,SAAO,UAAU,CAAC,UAAU,IAAK,CAAC;AACpC;;;ACrCA,IAAMC,YAAW;AAyDX,SAAU,0BAId,YAAkE;AAElE,QAAM,EAAE,KAAK,MAAM,aAAY,IAC7B;AAEF,MAAI,UAAU,IAAI,CAAC;AACnB,MAAI,cAAc;AAChB,UAAM,OAAO,WAAW;MACtB;MACA;MACA,MAAM;KACP;AACD,QAAI,CAAC;AAAM,YAAM,IAAI,yBAAyB,cAAc,EAAE,UAAAA,UAAQ,CAAE;AACxE,cAAU;EACZ;AAEA,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,yBAAyB,QAAW,EAAE,UAAAA,UAAQ,CAAE;AAE5D,SAAO;IACL,KAAK,CAAC,OAAO;IACb,cAAc,mBAAmBC,eAAc,OAAO,CAAC;;AAE3D;;;ACzCM,SAAU,mBAId,YAA2D;AAE3D,QAAM,EAAE,KAAI,IAAK;AAEjB,QAAM,EAAE,KAAK,aAAY,KAAM,MAAK;AAClC,QACE,WAAW,IAAI,WAAW,KAC1B,WAAW,cAAc,WAAW,IAAI;AAExC,aAAO;AACT,WAAO,0BAA0B,UAAU;EAC7C,GAAE;AAEF,QAAM,UAAU,IAAI,CAAC;AACrB,QAAM,YAAY;AAElB,QAAM,OACJ,YAAY,WAAW,QAAQ,SAC3B,oBAAoB,QAAQ,QAAQ,QAAQ,CAAA,CAAE,IAC9C;AACN,SAAO,UAAU,CAAC,WAAW,QAAQ,IAAI,CAAC;AAC5C;;;ACtFM,SAAU,wBAAwB,EACtC,aACA,OACA,UAAU,KAAI,GAKf;AACC,QAAM,WAAY,OAAO,YAA8C,IAAI;AAC3E,MAAI,CAAC;AACH,UAAM,IAAI,4BAA4B;MACpC;MACA,UAAU,EAAE,KAAI;KACjB;AAEH,MACE,eACA,SAAS,gBACT,SAAS,eAAe;AAExB,UAAM,IAAI,4BAA4B;MACpC;MACA;MACA,UAAU;QACR;QACA,cAAc,SAAS;;KAE1B;AAEH,SAAO,SAAS;AAClB;;;ACvBM,IAAO,yBAAP,cAAsC,UAAS;EAInD,YAAY,EACV,OACA,QAAO,IAC4D,CAAA,GAAE;AACrE,UAAM,SAAS,SACX,QAAQ,wBAAwB,EAAE,GAClC,QAAQ,sBAAsB,EAAE;AACpC,UACE,sBACE,SAAS,gBAAgB,MAAM,KAAK,uBACtC,KACA;MACE;MACA,MAAM;KACP;EAEL;;AAnBO,OAAA,eAAA,wBAAA,QAAA;;;;SAAO;;AACP,OAAA,eAAA,wBAAA,eAAA;;;;SAAc;;AAwBjB,IAAO,qBAAP,cAAkC,UAAS;EAG/C,YAAY,EACV,OACA,aAAY,IAIV,CAAA,GAAE;AACJ,UACE,gCACE,eAAe,MAAM,WAAW,YAAY,CAAC,UAAU,EACzD,gEACA;MACE;MACA,MAAM;KACP;EAEL;;AAlBO,OAAA,eAAA,oBAAA,eAAA;;;;SACL;;AAuBE,IAAO,oBAAP,cAAiC,UAAS;EAG9C,YAAY,EACV,OACA,aAAY,IAIV,CAAA,GAAE;AACJ,UACE,gCACE,eAAe,MAAM,WAAW,YAAY,CAAC,KAAK,EACpD,mDACA;MACE;MACA,MAAM;KACP;EAEL;;AAlBO,OAAA,eAAA,mBAAA,eAAA;;;;SACL;;AAuBE,IAAO,oBAAP,cAAiC,UAAS;EAE9C,YAAY,EACV,OACA,MAAK,IAC4D,CAAA,GAAE;AACnE,UACE,sCACE,QAAQ,IAAI,KAAK,OAAO,EAC1B,yCACA,EAAE,OAAO,MAAM,oBAAmB,CAAE;EAExC;;AAXO,OAAA,eAAA,mBAAA,eAAA;;;;SAAc;;AAiBjB,IAAO,mBAAP,cAAgC,UAAS;EAG7C,YAAY,EACV,OACA,MAAK,IAC4D,CAAA,GAAE;AACnE,UACE;MACE,sCACE,QAAQ,IAAI,KAAK,OAAO,EAC1B;MACA;MACA,KAAK,IAAI,GACX,EAAE,OAAO,MAAM,mBAAkB,CAAE;EAEvC;;AAfO,OAAA,eAAA,kBAAA,eAAA;;;;SACL;;AAoBE,IAAO,qBAAP,cAAkC,UAAS;EAE/C,YAAY,EACV,OACA,MAAK,IAC4D,CAAA,GAAE;AACnE,UACE,sCACE,QAAQ,IAAI,KAAK,OAAO,EAC1B,sCACA,EAAE,OAAO,MAAM,qBAAoB,CAAE;EAEzC;;AAXO,OAAA,eAAA,oBAAA,eAAA;;;;SAAc;;AAiBjB,IAAO,yBAAP,cAAsC,UAAS;EAGnD,YAAY,EAAE,MAAK,IAAwC,CAAA,GAAE;AAC3D,UACE;MACE;MACA,KAAK,IAAI,GACX;MACE;MACA,cAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;MAEF,MAAM;KACP;EAEL;;AAtBO,OAAA,eAAA,wBAAA,eAAA;;;;SACL;;AA2BE,IAAO,2BAAP,cAAwC,UAAS;EAErD,YAAY,EACV,OACA,IAAG,IAC4D,CAAA,GAAE;AACjE,UACE,qBACE,MAAM,IAAI,GAAG,OAAO,EACtB,yEACA;MACE;MACA,MAAM;KACP;EAEL;;AAdO,OAAA,eAAA,0BAAA,eAAA;;;;SAAc;;AAoBjB,IAAO,0BAAP,cAAuC,UAAS;EAEpD,YAAY,EACV,OACA,IAAG,IAC4D,CAAA,GAAE;AACjE,UACE,qBACE,MAAM,IAAI,GAAG,OAAO,EACtB,4CACA;MACE;MACA,MAAM;KACP;EAEL;;AAdO,OAAA,eAAA,yBAAA,eAAA;;;;SAAc;;AAqBjB,IAAO,mCAAP,cAAgD,UAAS;EAE7D,YAAY,EAAE,MAAK,GAAqC;AACtD,UAAM,yDAAyD;MAC7D;MACA,MAAM;KACP;EACH;;AANO,OAAA,eAAA,kCAAA,eAAA;;;;SAAc;;AAYjB,IAAO,sBAAP,cAAmC,UAAS;EAGhD,YAAY,EACV,OACA,sBACA,aAAY,IAKV,CAAA,GAAE;AACJ,UACE;MACE,6CACE,uBACI,MAAM,WAAW,oBAAoB,CAAC,UACtC,EACN,wDACE,eAAe,MAAM,WAAW,YAAY,CAAC,UAAU,EACzD;MACA,KAAK,IAAI,GACX;MACE;MACA,MAAM;KACP;EAEL;;AA1BO,OAAA,eAAA,qBAAA,eAAA;;;;SACL;;AA+BE,IAAO,mBAAP,cAAgC,UAAS;EAC7C,YAAY,EAAE,MAAK,GAAqC;AACtD,UAAM,sCAAsC,OAAO,YAAY,IAAI;MACjE;MACA,MAAM;KACP;EACH;;;;AC3QI,IAAO,mBAAP,cAAgC,UAAS;EAM7C,YAAY,EACV,MACA,OACA,SACA,SACA,QACA,IAAG,GAQJ;AACC,UAAM,wBAAwB;MAC5B;MACA;MACA,cAAc;QACZ,UAAU,WAAW,MAAM;QAC3B,QAAQ,OAAO,GAAG,CAAC;QACnB,QAAQ,iBAAiB,UAAU,IAAI,CAAC;QACxC,OAAO,OAAO;MAChB,MAAM;KACP;AA7BH,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,UAAA;;;;;;AACA,WAAA,eAAA,MAAA,OAAA;;;;;;AA2BE,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,MAAM;EACb;;AAiCI,IAAO,kBAAP,cAA+B,UAAS;EAI5C,YAAY,EACV,MACA,OACA,IAAG,GAKJ;AACC,UAAM,uBAAuB;MAC3B,OAAO;MACP,SAAS,MAAM;MACf,cAAc,CAAC,QAAQ,OAAO,GAAG,CAAC,IAAI,iBAAiB,UAAU,IAAI,CAAC,EAAE;MACxE,MAAM;KACP;AAjBH,WAAA,eAAA,MAAA,QAAA;;;;;;AACA,WAAA,eAAA,MAAA,QAAA;;;;;;AAiBE,SAAK,OAAO,MAAM;AAClB,SAAK,OAAO,MAAM;EACpB;;;;AC7FF,IAAM,mBAAmB;AAgCnB,IAAO,WAAP,cAA6D,UAAS;EAG1E,YACE,OACA,EACE,MACA,UAAAC,WACA,cACA,MACA,aAAY,GACW;AAEzB,UAAM,cAAc;MAClB;MACA,UAAAA;MACA,cACE,gBAAiB,OAAuC;MAC1D,MAAM,QAAQ;KACf;AAlBH,WAAA,eAAA,MAAA,QAAA;;;;;;AAmBE,SAAK,OAAO,QAAQ,MAAM;AAC1B,SAAK,OACH,iBAAiB,kBAAkB,MAAM,OAAQ,QAAQ;EAE7D;;AAmBI,IAAO,mBAAP,cAEI,SAA8B;EAGtC,YACE,OACA,SAIC;AAED,UAAM,OAAO,OAAO;AAVtB,WAAA,eAAA,MAAA,QAAA;;;;;;AAYE,SAAK,OAAO,QAAQ;EACtB;;AAYI,IAAO,gBAAP,MAAO,uBAAsB,SAAQ;EAGzC,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,eAAc;MACpB,MAAM;MACN,cACE;KACH;EACH;;AATO,OAAA,eAAA,eAAA,QAAA;;;;SAAO;;AAqBV,IAAO,yBAAP,MAAO,gCAA+B,SAAQ;EAGlD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,wBAAuB;MAC7B,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,wBAAA,QAAA;;;;SAAO;;AAoBV,IAAO,yBAAP,MAAO,gCAA+B,SAAQ;EAGlD,YAAY,OAAc,EAAE,OAAM,IAA0B,CAAA,GAAE;AAC5D,UAAM,OAAO;MACX,MAAM,wBAAuB;MAC7B,MAAM;MACN,cAAc,aAAa,SAAS,KAAK,MAAM,MAAM,EAAE;KACxD;EACH;;AARO,OAAA,eAAA,wBAAA,QAAA;;;;SAAO;;AAoBV,IAAO,wBAAP,MAAO,+BAA8B,SAAQ;EAGjD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,uBAAsB;MAC5B,MAAM;MACN,cAAc;QACZ;QACA;QACA,KAAK,IAAI;KACZ;EACH;;AAXO,OAAA,eAAA,uBAAA,QAAA;;;;SAAO;;AAuBV,IAAO,mBAAP,MAAO,0BAAyB,SAAQ;EAG5C,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,kBAAiB;MACvB,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,kBAAA,QAAA;;;;SAAO;;AAoBV,IAAO,uBAAP,MAAO,8BAA6B,SAAQ;EAGhD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,sBAAqB;MAC3B,MAAM;MACN,cAAc;QACZ;QACA;QACA,KAAK,IAAI;KACZ;EACH;;AAXO,OAAA,eAAA,sBAAA,QAAA;;;;SAAO;;AAuBV,IAAO,2BAAP,MAAO,kCAAiC,SAAQ;EAIpD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,0BAAyB;MAC/B,MAAM;MACN,cAAc;KACf;AARM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AARO,OAAA,eAAA,0BAAA,QAAA;;;;SAAO;;AAoBV,IAAO,8BAAP,MAAO,qCAAoC,SAAQ;EAGvD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,6BAA4B;MAClC,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,6BAAA,QAAA;;;;SAAO;;AAoBV,IAAO,8BAAP,MAAO,qCAAoC,SAAQ;EAGvD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,6BAA4B;MAClC,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,6BAAA,QAAA;;;;SAAO;;AAoBV,IAAO,6BAAP,MAAO,oCAAmC,SAAQ;EAGtD,YAAY,OAAc,EAAE,OAAM,IAA0B,CAAA,GAAE;AAC5D,UAAM,OAAO;MACX,MAAM,4BAA2B;MACjC,MAAM;MACN,cAAc,SAAS,SAAS,KAAK,MAAM,MAAM,EAAE;KACpD;EACH;;AARO,OAAA,eAAA,4BAAA,QAAA;;;;SAAO;;AAoBV,IAAO,wBAAP,MAAO,+BAA8B,SAAQ;EAGjD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,uBAAsB;MAC5B,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,uBAAA,QAAA;;;;SAAO;;AAqBV,IAAO,iCAAP,MAAO,wCAAuC,SAAQ;EAG1D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,gCAA+B;MACrC,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,gCAAA,QAAA;;;;SAAO;;AAoBV,IAAO,2BAAP,MAAO,kCAAiC,iBAAgB;EAG5D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,0BAAyB;MAC/B,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,0BAAA,QAAA;;;;SAAO;;AAoBV,IAAO,4BAAP,MAAO,mCAAkC,iBAAgB;EAG7D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,2BAA0B;MAChC,MAAM;MACN,cACE;KACH;EACH;;AATO,OAAA,eAAA,2BAAA,QAAA;;;;SAAO;;AAsBV,IAAO,iCAAP,MAAO,wCAAuC,iBAAgB;EAGlE,YAAY,OAAc,EAAE,OAAM,IAA0B,CAAA,GAAE;AAC5D,UAAM,OAAO;MACX,MAAM,gCAA+B;MACrC,MAAM;MACN,cAAc,qDAAqD,SAAS,MAAM,MAAM,MAAM,EAAE;KACjG;EACH;;AARO,OAAA,eAAA,gCAAA,QAAA;;;;SAAO;;AAoBV,IAAO,4BAAP,MAAO,mCAAkC,iBAAgB;EAG7D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,2BAA0B;MAChC,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,2BAAA,QAAA;;;;SAAO;;AAoBV,IAAO,yBAAP,MAAO,gCAA+B,iBAAgB;EAG1D,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,wBAAuB;MAC7B,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,wBAAA,QAAA;;;;SAAO;;AAoBV,IAAO,mBAAP,MAAO,0BAAyB,iBAAgB;EAGpD,YAAY,OAAY;AACtB,UAAM,OAAO;MACX,MAAM,kBAAiB;MACvB,MAAM;MACN,cAAc;KACf;EACH;;AARO,OAAA,eAAA,kBAAA,QAAA;;;;SAAO;;;;AC/ZV,SAAU,aACd,KACA,MAA4B;AAE5B,QAAM,WAAW,IAAI,WAAW,IAAI,YAAW;AAE/C,QAAM,yBACJ,eAAe,YACX,IAAI,KACF,CAAC,MACE,GAA2C,SAC5C,uBAAuB,IAAI,IAE/B;AACN,MAAI,kCAAkC;AACpC,WAAO,IAAI,uBAAuB;MAChC,OAAO;MACP,SAAS,uBAAuB;KACjC;AACH,MAAI,uBAAuB,YAAY,KAAK,OAAO;AACjD,WAAO,IAAI,uBAAuB;MAChC,OAAO;MACP,SAAS,IAAI;KACd;AACH,MAAI,mBAAmB,YAAY,KAAK,OAAO;AAC7C,WAAO,IAAI,mBAAmB;MAC5B,OAAO;MACP,cAAc,MAAM;KACrB;AACH,MAAI,kBAAkB,YAAY,KAAK,OAAO;AAC5C,WAAO,IAAI,kBAAkB;MAC3B,OAAO;MACP,cAAc,MAAM;KACrB;AACH,MAAI,kBAAkB,YAAY,KAAK,OAAO;AAC5C,WAAO,IAAI,kBAAkB,EAAE,OAAO,KAAK,OAAO,MAAM,MAAK,CAAE;AACjE,MAAI,iBAAiB,YAAY,KAAK,OAAO;AAC3C,WAAO,IAAI,iBAAiB,EAAE,OAAO,KAAK,OAAO,MAAM,MAAK,CAAE;AAChE,MAAI,mBAAmB,YAAY,KAAK,OAAO;AAC7C,WAAO,IAAI,mBAAmB,EAAE,OAAO,KAAK,OAAO,MAAM,MAAK,CAAE;AAClE,MAAI,uBAAuB,YAAY,KAAK,OAAO;AACjD,WAAO,IAAI,uBAAuB,EAAE,OAAO,IAAG,CAAE;AAClD,MAAI,yBAAyB,YAAY,KAAK,OAAO;AACnD,WAAO,IAAI,yBAAyB,EAAE,OAAO,KAAK,KAAK,MAAM,IAAG,CAAE;AACpE,MAAI,wBAAwB,YAAY,KAAK,OAAO;AAClD,WAAO,IAAI,wBAAwB,EAAE,OAAO,KAAK,KAAK,MAAM,IAAG,CAAE;AACnE,MAAI,iCAAiC,YAAY,KAAK,OAAO;AAC3D,WAAO,IAAI,iCAAiC,EAAE,OAAO,IAAG,CAAE;AAC5D,MAAI,oBAAoB,YAAY,KAAK,OAAO;AAC9C,WAAO,IAAI,oBAAoB;MAC7B,OAAO;MACP,cAAc,MAAM;MACpB,sBAAsB,MAAM;KAC7B;AACH,SAAO,IAAI,iBAAiB;IAC1B,OAAO;GACR;AACH;;;AC/FM,SAAU,aACd,KACA,EACE,UAAAC,WACA,GAAG,KAAI,GAIR;AAED,QAAM,SAAS,MAAK;AAClB,UAAMC,SAAQ,aACZ,KACA,IAA8B;AAEhC,QAAIA,kBAAiB;AAAkB,aAAO;AAC9C,WAAOA;EACT,GAAE;AACF,SAAO,IAAI,mBAAmB,OAAO;IACnC,UAAAD;IACA,GAAG;GACJ;AACH;;;ACrCM,SAAU,QACd,QACA,EAAE,OAAM,GAAqD;AAE7D,MAAI,CAAC;AAAQ,WAAO,CAAA;AAEpB,QAAM,QAAiC,CAAA;AACvC,WAAS,SAASE,YAA8B;AAC9C,UAAM,OAAO,OAAO,KAAKA,UAAS;AAClC,eAAW,OAAO,MAAM;AACtB,UAAI,OAAO;AAAQ,cAAM,GAAG,IAAI,OAAO,GAAG;AAC1C,UACEA,WAAU,GAAG,KACb,OAAOA,WAAU,GAAG,MAAM,YAC1B,CAAC,MAAM,QAAQA,WAAU,GAAG,CAAC;AAE7B,iBAASA,WAAU,GAAG,CAAC;IAC3B;EACF;AAEA,QAAM,YAAY,OAAO,UAAU,CAAA,CAAE;AACrC,WAAS,SAAS;AAElB,SAAO;AACT;;;AC3BM,SAAU,gBACd,MACA,QAAqC;AAErC,SAAO,CAIL,EACA,SACA,QAAQ,UAAS,MAId;AACH,WAAO;MACL;MACA,QAAQ,CAAC,SAA4B;AACnC,cAAM,YAAY,OAAO,IAAW;AACpC,YAAI,SAAS;AACX,qBAAW,OAAO,SAAS;AACzB,mBAAQ,UAAkB,GAAG;UAC/B;QACF;AACA,eAAO;UACL,GAAG;UACH,GAAG,UAAU,IAAI;;MAIrB;MACA;;EAEJ;AACF;;;ACjBO,IAAM,qBAAqB;EAChC,QAAQ;EACR,SAAS;EACT,SAAS;EACT,SAAS;EACT,SAAS;;AAKL,SAAU,yBACd,SAAyC;AAEzC,QAAM,aAAa,CAAA;AAEnB,MAAI,OAAO,QAAQ,sBAAsB;AACvC,eAAW,oBAAoB,wBAC7B,QAAQ,iBAAiB;AAE7B,MAAI,OAAO,QAAQ,eAAe;AAChC,eAAW,aAAa,QAAQ;AAClC,MAAI,OAAO,QAAQ,wBAAwB;AACzC,eAAW,sBAAsB,QAAQ;AAC3C,MAAI,OAAO,QAAQ,UAAU,aAAa;AACxC,QAAI,OAAO,QAAQ,MAAM,CAAC,MAAM;AAC9B,iBAAW,QAAS,QAAQ,MAAsB,IAAI,CAAC,MACrD,WAAW,CAAC,CAAC;;AAEZ,iBAAW,QAAQ,QAAQ;EAClC;AACA,MAAI,OAAO,QAAQ,SAAS;AAAa,eAAW,OAAO,QAAQ;AACnE,MAAI,OAAO,QAAQ,SAAS;AAAa,eAAW,OAAO,QAAQ;AACnE,MAAI,OAAO,QAAQ,QAAQ;AACzB,eAAW,MAAM,YAAY,QAAQ,GAAG;AAC1C,MAAI,OAAO,QAAQ,aAAa;AAC9B,eAAW,WAAW,YAAY,QAAQ,QAAQ;AACpD,MAAI,OAAO,QAAQ,qBAAqB;AACtC,eAAW,mBAAmB,YAAY,QAAQ,gBAAgB;AACpE,MAAI,OAAO,QAAQ,iBAAiB;AAClC,eAAW,eAAe,YAAY,QAAQ,YAAY;AAC5D,MAAI,OAAO,QAAQ,yBAAyB;AAC1C,eAAW,uBAAuB,YAAY,QAAQ,oBAAoB;AAC5E,MAAI,OAAO,QAAQ,UAAU;AAC3B,eAAW,QAAQ,YAAY,QAAQ,KAAK;AAC9C,MAAI,OAAO,QAAQ,OAAO;AAAa,eAAW,KAAK,QAAQ;AAC/D,MAAI,OAAO,QAAQ,SAAS;AAC1B,eAAW,OAAO,mBAAmB,QAAQ,IAAI;AACnD,MAAI,OAAO,QAAQ,UAAU;AAC3B,eAAW,QAAQ,YAAY,QAAQ,KAAK;AAE9C,SAAO;AACT;AAMO,IAAM,2BAAyC,gBACpD,sBACA,wBAAwB;AAK1B,SAAS,wBACP,mBAAqD;AAErD,SAAO,kBAAkB,IACvB,CAAC,mBACE;IACC,SAAS,cAAc;IACvB,GAAG,cAAc;IACjB,GAAG,cAAc;IACjB,SAAS,YAAY,cAAc,OAAO;IAC1C,OAAO,YAAY,cAAc,KAAK;IACtC,GAAI,OAAO,cAAc,YAAY,cACjC,EAAE,SAAS,YAAY,cAAc,OAAO,EAAC,IAC7C,CAAA;IACJ,GAAI,OAAO,cAAc,MAAM,eAC/B,OAAO,cAAc,YAAY,cAC7B,EAAE,GAAG,YAAY,cAAc,CAAC,EAAC,IACjC,CAAA;IACG;AAEf;;;AClGM,SAAU,gBAAa;AAC3B,MAAI,UAAiD,MAAM;AAC3D,MAAI,SAA+C,MAAM;AAEzD,QAAM,UAAU,IAAI,QAAc,CAAC,UAAU,YAAW;AACtD,cAAU;AACV,aAAS;EACX,CAAC;AAED,SAAO,EAAE,SAAS,SAAS,OAAM;AACnC;;;ACqBA,IAAM,iBAA+B,oBAAI,IAAG;AAGtC,SAAU,qBAGd,EACA,IACA,IACA,kBACA,OAAO,GACP,KAAI,GAIL;AACC,QAAM,OAAO,YAAW;AACtB,UAAM,YAAY,aAAY;AAC9B,UAAK;AAEL,UAAM,OAAO,UAAU,IAAI,CAAC,EAAE,MAAAC,MAAI,MAAOA,KAAI;AAE7C,QAAI,KAAK,WAAW;AAAG;AAEvB,OAAG,IAAoB,EACpB,KAAK,CAAC,SAAQ;AACb,UAAI,QAAQ,MAAM,QAAQ,IAAI;AAAG,aAAK,KAAK,IAAI;AAC/C,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,EAAE,QAAO,IAAK,UAAU,CAAC;AAC/B,kBAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;MAC3B;IACF,CAAC,EACA,MAAM,CAAC,QAAO;AACb,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAM,EAAE,OAAM,IAAK,UAAU,CAAC;AAC9B,iBAAS,GAAG;MACd;IACF,CAAC;EACL;AAEA,QAAM,QAAQ,MAAM,eAAe,OAAO,EAAE;AAE5C,QAAM,iBAAiB,MACrB,aAAY,EAAG,IAAI,CAAC,EAAE,KAAI,MAAO,IAAI;AAEvC,QAAM,eAAe,MAAM,eAAe,IAAI,EAAE,KAAK,CAAA;AAErD,QAAM,eAAe,CAAC,SACpB,eAAe,IAAI,IAAI,CAAC,GAAG,aAAY,GAAI,IAAI,CAAC;AAElD,SAAO;IACL;IACA,MAAM,SAAS,MAAgB;AAC7B,YAAM,EAAE,SAAS,SAAS,OAAM,IAAK,cAAa;AAElD,YAAMC,SAAQ,mBAAmB,CAAC,GAAG,eAAc,GAAI,IAAI,CAAC;AAE5D,UAAIA;AAAO,aAAI;AAEf,YAAM,qBAAqB,aAAY,EAAG,SAAS;AACnD,UAAI,oBAAoB;AACtB,qBAAa,EAAE,MAAM,SAAS,OAAM,CAAE;AACtC,eAAO;MACT;AAEA,mBAAa,EAAE,MAAM,SAAS,OAAM,CAAE;AACtC,iBAAW,MAAM,IAAI;AACrB,aAAO;IACT;;AAEJ;;;ACjFM,SAAU,sBACd,cAA6C;AAE7C,MAAI,CAAC,gBAAgB,aAAa,WAAW;AAAG,WAAO;AACvD,SAAO,aAAa,OAAO,CAAC,KAAK,EAAE,MAAM,MAAK,MAAM;AAClD,QAAI,KAAK,WAAW;AAClB,YAAM,IAAI,wBAAwB;QAChC,MAAM,KAAK;QACX,YAAY;QACZ,MAAM;OACP;AACH,QAAI,MAAM,WAAW;AACnB,YAAM,IAAI,wBAAwB;QAChC,MAAM,MAAM;QACZ,YAAY;QACZ,MAAM;OACP;AACH,QAAI,IAAI,IAAI;AACZ,WAAO;EACT,GAAG,CAAA,CAAqB;AAC1B;AAaM,SAAU,8BACd,YAAmD;AAEnD,QAAM,EAAE,SAAS,OAAO,OAAO,WAAW,KAAI,IAAK;AACnD,QAAM,0BAAmD,CAAA;AACzD,MAAI,SAAS;AAAW,4BAAwB,OAAO;AACvD,MAAI,YAAY;AACd,4BAAwB,UAAU,YAAY,OAAO;AACvD,MAAI,UAAU;AAAW,4BAAwB,QAAQ,YAAY,KAAK;AAC1E,MAAI,UAAU;AACZ,4BAAwB,QAAQ,sBAAsB,KAAK;AAC7D,MAAI,cAAc,QAAW;AAC3B,QAAI,wBAAwB;AAAO,YAAM,IAAI,6BAA4B;AACzE,4BAAwB,YAAY,sBAAsB,SAAS;EACrE;AACA,SAAO;AACT;AAUM,SAAU,uBACd,YAA6C;AAE7C,MAAI,CAAC;AAAY,WAAO;AACxB,QAAM,mBAAqC,CAAA;AAC3C,aAAW,EAAE,SAAS,GAAG,aAAY,KAAM,YAAY;AACrD,QAAI,CAAC,UAAU,SAAS,EAAE,QAAQ,MAAK,CAAE;AACvC,YAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;AAC3C,QAAI,iBAAiB,OAAO;AAC1B,YAAM,IAAI,0BAA0B,EAAE,QAAgB,CAAE;AAC1D,qBAAiB,OAAO,IAAI,8BAA8B,YAAY;EACxE;AACA,SAAO;AACT;;;ACpGO,IAAM,UAAU,OAAO,KAAK,MAAM;AAClC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,WAAW,OAAO,MAAM,MAAM;AACpC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AACtC,IAAM,YAAY,OAAO,OAAO,MAAM;AAEtC,IAAM,UAAU,EAAE,OAAO,KAAK;AAC9B,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,WAAW,EAAE,OAAO,MAAM;AAChC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAClC,IAAM,YAAY,EAAE,OAAO,OAAO;AAElC,IAAM,WAAW,MAAM,KAAK;AAC5B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,YAAY,MAAM,MAAM;AAC9B,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;AAChC,IAAM,aAAa,MAAM,OAAO;;;AC5DjC,SAAU,cAAc,MAA6B;AACzD,QAAM,EACJ,SAAS,UACT,UACA,cACA,sBACA,GAAE,IACA;AACJ,QAAM,UAAU,WAAW,aAAa,QAAQ,IAAI;AACpD,MAAI,WAAW,CAAC,UAAU,QAAQ,OAAO;AACvC,UAAM,IAAI,oBAAoB,EAAE,SAAS,QAAQ,QAAO,CAAE;AAC5D,MAAI,MAAM,CAAC,UAAU,EAAE;AAAG,UAAM,IAAI,oBAAoB,EAAE,SAAS,GAAE,CAAE;AACvE,MACE,OAAO,aAAa,gBACnB,OAAO,iBAAiB,eACvB,OAAO,yBAAyB;AAElC,UAAM,IAAI,iBAAgB;AAE5B,MAAI,gBAAgB,eAAe;AACjC,UAAM,IAAI,mBAAmB,EAAE,aAAY,CAAE;AAC/C,MACE,wBACA,gBACA,uBAAuB;AAEvB,UAAM,IAAI,oBAAoB,EAAE,cAAc,qBAAoB,CAAE;AACxE;;;ACsFA,eAAsB,KACpB,QACA,MAA2B;AAE3B,QAAM,EACJ,SAAS,WAAW,OAAO,SAC3B,QAAQ,QAAQ,OAAO,OAAO,SAAS,GACvC,aACA,WAAW,UACX,YACA,OACA,MACA,MAAM,OACN,SACA,aACA,KACA,UACA,kBACA,cACA,sBACA,OACA,IACA,OACA,eACA,GAAG,KAAI,IACL;AACJ,QAAM,UAAU,WAAW,aAAa,QAAQ,IAAI;AAEpD,MAAI,SAAS,WAAW;AACtB,UAAM,IAAI,UACR,qEAAqE;AAEzE,MAAI,QAAQ;AACV,UAAM,IAAI,UAAU,kDAAkD;AAGxE,QAAM,4BAA4B,QAAQ;AAE1C,QAAM,2BAA2B,WAAW,eAAe,MAAM;AACjE,QAAM,iBAAiB,6BAA6B;AAEpD,QAAM,QAAQ,MAAK;AACjB,QAAI;AACF,aAAO,gCAAgC;QACrC;QACA,MAAM;OACP;AACH,QAAI;AACF,aAAO,+BAA+B;QACpC,MAAM;QACN;QACA;QACA;OACD;AACH,WAAO;EACT,GAAE;AAEF,MAAI;AACF,kBAAc,IAA+B;AAE7C,UAAM,iBAAiB,cAAc,YAAY,WAAW,IAAI;AAChE,UAAM,QAAQ,kBAAkB;AAEhC,UAAM,mBAAmB,uBAAuB,aAAa;AAE7D,UAAM,cAAc,OAAO,OAAO,YAAY,oBAAoB;AAClE,UAAM,SAAS,eAAe;AAE9B,UAAM,UAAU,OAAO;;MAErB,GAAG,QAAQ,MAAM,EAAE,QAAQ,YAAW,CAAE;MACxC,MAAM,SAAS;MACf;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,iBAAiB,SAAY;MACjC;KACqB;AAEvB,QAAI,SAAS,uBAAuB,EAAE,QAAO,CAAE,KAAK,CAAC,kBAAkB;AACrE,UAAI;AACF,eAAO,MAAM,kBAAkB,QAAQ;UACrC,GAAG;UACH;UACA;SACgD;MACpD,SAAS,KAAK;AACZ,YACE,EAAE,eAAe,kCACjB,EAAE,eAAe;AAEjB,gBAAM;MACV;IACF;AAEA,UAAM,WAAW,MAAM,OAAO,QAAQ;MACpC,QAAQ;MACR,QAAQ,mBACJ;QACE;QACA;QACA;UAEF,CAAC,SAAgD,KAAK;KAC3D;AACD,QAAI,aAAa;AAAM,aAAO,EAAE,MAAM,OAAS;AAC/C,WAAO,EAAE,MAAM,SAAQ;EACzB,SAAS,KAAK;AACZ,UAAMC,QAAO,mBAAmB,GAAG;AAGnC,UAAM,EAAE,gBAAAC,iBAAgB,yBAAAC,yBAAuB,IAAK,MAAM,OACxD,oBAAqB;AAEvB,QACE,OAAO,aAAa,SACpBF,OAAM,MAAM,GAAG,EAAE,MAAME,4BACvB;AAEA,aAAO,EAAE,MAAM,MAAMD,gBAAe,QAAQ,EAAE,MAAAD,OAAM,GAAE,CAAE,EAAC;AAG3D,QAAI,kBAAkBA,OAAM,MAAM,GAAG,EAAE,MAAM;AAC3C,YAAM,IAAI,oCAAoC,EAAE,QAAO,CAAE;AAE3D,UAAM,aAAa,KAAkB;MACnC,GAAG;MACH;MACA,OAAO,OAAO;KACf;EACH;AACF;AAOA,SAAS,uBAAuB,EAAE,QAAO,GAAmC;AAC1E,QAAM,EAAE,MAAM,IAAI,GAAG,SAAQ,IAAK;AAClC,MAAI,CAAC;AAAM,WAAO;AAClB,MAAI,KAAK,WAAW,mBAAmB;AAAG,WAAO;AACjD,MAAI,CAAC;AAAI,WAAO;AAChB,MACE,OAAO,OAAO,QAAQ,EAAE,OAAO,CAAC,MAAM,OAAO,MAAM,WAAW,EAAE,SAAS;AAEzE,WAAO;AACT,SAAO;AACT;AAoBA,eAAe,kBACb,QACA,MAAwC;AAExC,QAAM,EAAE,YAAY,MAAM,OAAO,EAAC,IAChC,OAAO,OAAO,OAAO,cAAc,WAAW,OAAO,MAAM,YAAY,CAAA;AACzE,QAAM,EACJ,aACA,WAAW,UACX,MACA,kBAAkB,mBAClB,GAAE,IACA;AAEJ,MAAI,mBAAmB;AACvB,MAAI,CAAC,kBAAkB;AACrB,QAAI,CAAC,OAAO;AAAO,YAAM,IAAI,8BAA6B;AAE1D,uBAAmB,wBAAwB;MACzC;MACA,OAAO,OAAO;MACd,UAAU;KACX;EACH;AAEA,QAAM,iBAAiB,cAAc,YAAY,WAAW,IAAI;AAChE,QAAM,QAAQ,kBAAkB;AAEhC,QAAM,EAAE,SAAQ,IAAK,qBAAqB;IACxC,IAAI,GAAG,OAAO,GAAG,IAAI,KAAK;IAC1B;IACA,iBAAiBG,OAAI;AACnB,YAAMC,QAAOD,MAAK,OAAO,CAACC,OAAM,EAAE,MAAAJ,MAAI,MAAOI,SAAQJ,MAAK,SAAS,IAAI,CAAC;AACxE,aAAOI,QAAO,YAAY;IAC5B;IACA,IAAI,OACF,aAIE;AACF,YAAM,QAAQ,SAAS,IAAI,CAAC,aAAa;QACvC,cAAc;QACd,UAAU,QAAQ;QAClB,QAAQ,QAAQ;QAChB;AAEF,YAAM,WAAW,mBAAmB;QAClC,KAAK;QACL,MAAM,CAAC,KAAK;QACZ,cAAc;OACf;AAED,YAAMJ,QAAO,MAAM,OAAO,QAAQ;QAChC,QAAQ;QACR,QAAQ;UACN;YACE,MAAM;YACN,IAAI;;UAEN;;OAEH;AAED,aAAO,qBAAqB;QAC1B,KAAK;QACL,MAAM,CAAC,KAAK;QACZ,cAAc;QACd,MAAMA,SAAQ;OACf;IACH;GACD;AAED,QAAM,CAAC,EAAE,YAAY,QAAO,CAAE,IAAI,MAAM,SAAS,EAAE,MAAM,GAAE,CAAE;AAE7D,MAAI,CAAC;AAAS,UAAM,IAAI,iBAAiB,EAAE,MAAM,WAAU,CAAE;AAC7D,MAAI,eAAe;AAAM,WAAO,EAAE,MAAM,OAAS;AACjD,SAAO,EAAE,MAAM,WAAU;AAC3B;AAMA,SAAS,gCAAgC,YAGxC;AACC,QAAM,EAAE,MAAM,KAAI,IAAK;AACvB,SAAO,iBAAiB;IACtB,KAAK,SAAS,CAAC,2BAA2B,CAAC;IAC3C,UAAU;IACV,MAAM,CAAC,MAAM,IAAI;GAClB;AACH;AAMA,SAAS,+BAA+B,YAKvC;AACC,QAAM,EAAE,MAAM,SAAS,aAAa,GAAE,IAAK;AAC3C,SAAO,iBAAiB;IACtB,KAAK,SAAS,CAAC,6CAA6C,CAAC;IAC7D,UAAU;IACV,MAAM,CAAC,IAAI,MAAM,SAAS,WAAW;GACtC;AACH;AAMM,SAAU,mBAAmB,KAAY;AAC7C,MAAI,EAAE,eAAe;AAAY,WAAO;AACxC,QAAM,QAAQ,IAAI,KAAI;AACtB,SAAO,OAAO,OAAO,SAAS,WAAW,MAAM,MAAM,OAAO,MAAM;AACpE;;;ACnbM,IAAO,sBAAP,cAAmC,UAAS;EAChD,YAAY,EACV,kBACA,OACA,MACA,WACA,QACA,KAAI,GAQL;AACC,UACE,MAAM,gBACJ,4DACF;MACE;MACA,cAAc;QACZ,GAAI,MAAM,gBAAgB,CAAA;QAC1B,MAAM,cAAc,SAAS,KAAK,CAAA;QAClC;QACA,QAAQ;UACN;UACA,GAAG,KAAK,IAAI,CAAC,QAAQ,OAAO,OAAO,GAAG,CAAC,EAAE;;QAE3C,aAAa,MAAM;QACnB,WAAW,IAAI;QACf,wBAAwB,gBAAgB;QACxC,iBAAiB,SAAS;QAC1B,KAAI;MACN,MAAM;KACP;EAEL;;AAOI,IAAO,uCAAP,cAAoD,UAAS;EACjE,YAAY,EAAE,QAAQ,IAAG,GAAgC;AACvD,UACE,8EACA;MACE,cAAc;QACZ,gBAAgB,OAAO,GAAG,CAAC;QAC3B,aAAa,UAAU,MAAM,CAAC;;MAEhC,MAAM;KACP;EAEL;;AAQI,IAAO,oCAAP,cAAiD,UAAS;EAC9D,YAAY,EAAE,QAAQ,GAAE,GAAoC;AAC1D,UACE,0EACA;MACE,cAAc;QACZ,qBAAqB,EAAE;QACvB,kCAAkC,MAAM;;MAE1C,MAAM;KACP;EAEL;;;;AC3EI,SAAU,eAAe,GAAY,GAAU;AACnD,MAAI,CAAC,UAAU,GAAG,EAAE,QAAQ,MAAK,CAAE;AACjC,UAAM,IAAI,oBAAoB,EAAE,SAAS,EAAC,CAAE;AAC9C,MAAI,CAAC,UAAU,GAAG,EAAE,QAAQ,MAAK,CAAE;AACjC,UAAM,IAAI,oBAAoB,EAAE,SAAS,EAAC,CAAE;AAC9C,SAAO,EAAE,YAAW,MAAO,EAAE,YAAW;AAC1C;;;ACUO,IAAM,0BAA0B;AAChC,IAAM,wBAAwB;EACnC,MAAM;EACN,MAAM;EACN,QAAQ;IACN;MACE,MAAM;MACN,MAAM;;IAER;MACE,MAAM;MACN,MAAM;;IAER;MACE,MAAM;MACN,MAAM;;IAER;MACE,MAAM;MACN,MAAM;;IAER;MACE,MAAM;MACN,MAAM;;;;AAOZ,eAAsB,eACpB,QACA,EACE,aACA,UACA,MACA,GAAE,GAIH;AAED,QAAM,EAAE,KAAI,IAAK,kBAAkB;IACjC;IACA,KAAK,CAAC,qBAAqB;GAC5B;AACD,QAAM,CAAC,QAAQ,MAAM,UAAU,kBAAkB,SAAS,IAAI;AAE9D,QAAM,EAAE,SAAQ,IAAK;AACrB,QAAM,eACJ,YAAY,OAAO,UAAU,YAAY,aACrC,SAAS,UACT;AAEN,MAAI;AACF,QAAI,CAAC,eAAe,IAAI,MAAM;AAC5B,YAAM,IAAI,kCAAkC,EAAE,QAAQ,GAAE,CAAE;AAE5D,UAAM,SAAS,MAAM,aAAa,EAAE,MAAM,UAAU,QAAQ,KAAI,CAAE;AAElE,UAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,QAAQ;MACzC;MACA;MACA,MAAM,OAAO;QACX;QACA,oBACE,CAAC,EAAE,MAAM,QAAO,GAAI,EAAE,MAAM,QAAO,CAAE,GACrC,CAAC,QAAQ,SAAS,CAAC;OAEtB;MACD;KACiB;AAEnB,WAAO;EACT,SAAS,KAAK;AACZ,UAAM,IAAI,oBAAoB;MAC5B;MACA,OAAO;MACP;MACA;MACA;MACA;KACD;EACH;AACF;AAeA,eAAsB,YAAY,EAChC,MACA,QACA,KAAI,GACkB;AACtB,MAAI,QAAQ,IAAI,MAAM,4BAA4B;AAElD,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,MAAM,KAAK,CAAC;AAClB,UAAM,SAAS,IAAI,SAAS,QAAQ,IAAI,QAAQ;AAChD,UAAM,OAAO,WAAW,SAAS,EAAE,MAAM,OAAM,IAAK;AACpD,UAAM,UACJ,WAAW,SAAS,EAAE,gBAAgB,mBAAkB,IAAK,CAAA;AAE/D,QAAI;AACF,YAAM,WAAW,MAAM,MACrB,IAAI,QAAQ,YAAY,MAAM,EAAE,QAAQ,UAAU,IAAI,GACtD;QACE,MAAM,KAAK,UAAU,IAAI;QACzB;QACA;OACD;AAGH,UAAI;AACJ,UACE,SAAS,QAAQ,IAAI,cAAc,GAAG,WAAW,kBAAkB,GACnE;AACA,kBAAU,MAAM,SAAS,KAAI,GAAI;MACnC,OAAO;AACL,iBAAU,MAAM,SAAS,KAAI;MAC/B;AAEA,UAAI,CAAC,SAAS,IAAI;AAChB,gBAAQ,IAAI,iBAAiB;UAC3B;UACA,SAAS,QAAQ,QACb,UAAU,OAAO,KAAK,IACtB,SAAS;UACb,SAAS,SAAS;UAClB,QAAQ,SAAS;UACjB;SACD;AACD;MACF;AAEA,UAAI,CAAC,MAAM,MAAM,GAAG;AAClB,gBAAQ,IAAI,qCAAqC;UAC/C;UACA;SACD;AACD;MACF;AAEA,aAAO;IACT,SAAS,KAAK;AACZ,cAAQ,IAAI,iBAAiB;QAC3B;QACA,SAAU,IAAc;QACxB;OACD;IACH;EACF;AAEA,QAAM;AACR;", "names": ["docs<PERSON><PERSON>", "docs<PERSON><PERSON>", "formatAbiItem", "docs<PERSON><PERSON>", "size", "docs<PERSON><PERSON>", "docs<PERSON><PERSON>", "formatAbiItem", "docs<PERSON><PERSON>", "size", "size", "size", "size", "size", "size", "encoder", "toBytes", "toBytes", "toBytes", "size", "hash", "size", "size", "length", "data", "length", "consumed", "value", "size", "formatAbiItem", "value", "docs<PERSON><PERSON>", "docs<PERSON><PERSON>", "docs<PERSON><PERSON>", "formatAbiItem", "docs<PERSON><PERSON>", "docs<PERSON><PERSON>", "cause", "formatted", "args", "split", "data", "offchainLookup", "offchainLookupSignature", "args", "size"]}