{"version": 3, "file": "decode.d.ts", "sourceRoot": "", "sources": ["src/decode.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;EAeE;AACF;;;;;;;GAOG;AAEH,OAAO,KAAK,EAAiC,KAAK,EAAQ,KAAK,EAAE,MAAM,YAAY,CAAC;AACpF,OAAO,EAAE,MAAM,EAAS,MAAM,YAAY,CAAC;AAgB3C,MAAM,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AA2B9D;;;GAGG;AACH,iBAAS,QAAQ,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,CAwEpC;AAGD,KAAK,OAAO,GAAG,KAAK,GAAG;IAAE,UAAU,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC;AA6I7D,iBAAS,UAAU,CAAC,CAAC,EAAE,MAAM,GAAG;IAC9B,EAAE,EAAE,OAAO,CAAC;IACZ,EAAE,EAAE,OAAO,CAAC;IACZ,EAAE,EAAE,OAAO,CAAC;CACb,CA8HA;AAoHD,iBAAS,MAAM,CAAC,CAAC,EAAE,MAAM,GAAG;IAC1B,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,YAAY,CAAC;CACtB,CAkDA;AAyJD,iBAAS,YAAY,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAgFvC;AAED,MAAM,MAAM,UAAU,GAAG;IACvB,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,YAAY,KAAK,IAAI,CAAC;IAChD,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,IAAI,CAAC;IACrC,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,IAAI,CAAC;IACrC,aAAa,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,IAAI,CAAC;CACtC,CAAC;AAsBF,wBAAgB,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,GAAE,UAAe,GAAG,MAAM,CA8BlE;AAED,eAAe,QAAQ,CAAC;AAGxB,eAAO,MAAM,MAAM,EAAE;IACnB,QAAQ,EAAE,OAAO,QAAQ,CAAC;IAC1B,YAAY,EAAE,OAAO,YAAY,CAAC;IAClC,UAAU,EAAE,OAAO,UAAU,CAAC;IAC9B,MAAM,EAAE,OAAO,MAAM,CAAC;CAMvB,CAAC"}