import {
  BaseFeeScalarError,
  BlockNotFoundError,
  BundleFailedError,
  Eip1559FeesNotSupportedError,
  EnsAvatarInvalidNftUriError,
  EnsAvatarUnsupportedNamespaceError,
  EnsAvatarUriResolutionError,
  EstimateGasExecutionError,
  FilterTypeNotSupportedError,
  InvalidDecimalNumberError,
  InvalidDomainError,
  InvalidPrimaryTypeError,
  InvalidStructTypeError,
  MaxFeePerGasTooLowError,
  ProviderRpcError as ProviderRpcError2,
  UrlRequiredError,
  WaitForCallsStatusTimeoutError,
  compactSignatureToSignature,
  createClient,
  createNonceManager,
  createPublicClient,
  createTestClient,
  createTransport,
  createWalletClient,
  custom,
  decodeDeployData,
  decodeEventLog,
  defineKzg,
  domainSeparator,
  encodeEventTopics,
  encodePacked,
  ethAddress,
  fallback,
  fromBlobs,
  fromRlp,
  getContract,
  getContractAddress,
  getContractError,
  getCreate2Address,
  getCreateAddress,
  getSerializedTransactionType,
  getTypesForEIP712Domain,
  hashDomain,
  hashMessage,
  hashStruct,
  hashTypedData,
  http,
  isBytes,
  isErc6492Signature,
  isHash,
  labelhash,
  namehash,
  nonceManager,
  parseCompactSignature,
  parseErc6492Signature,
  parseEther,
  parseEventLogs,
  parseGwei,
  parseSignature,
  parseTransaction,
  parseUnits,
  presignMessagePrefix,
  publicActions,
  recoverAddress,
  recoverMessageAddress,
  recoverPublicKey,
  recoverTransactionAddress,
  recoverTypedDataAddress,
  ripemd160,
  rpcSchema,
  serializeCompactSignature,
  serializeErc6492Signature,
  serializeSignature,
  serializeTypedData,
  setupKzg,
  shouldThrow,
  sidecarsToVersionedHashes,
  signatureToCompactSignature,
  testActions,
  toPrefixedMessage,
  validateTypedData,
  verifyHash,
  verifyMessage,
  verifyTypedData,
  walletActions,
  webSocket,
  withCache,
  withRetry,
  withTimeout,
  zeroAddress,
  zeroHash
} from "./chunk-FLKSXK4W.js";
import "./chunk-2U7OI32P.js";
import {
  ccipRequest,
  decodeFunctionData,
  decodeFunctionResult,
  deploylessCallViaBytecodeBytecode,
  deploylessCallViaFactoryBytecode,
  encodeDeployData,
  encodeErrorResult,
  encodeFunctionData,
  encodeFunctionResult,
  erc1155Abi,
  erc20Abi,
  erc20Abi_bytes32,
  erc4626Abi,
  erc721Abi,
  isAddressEqual,
  multicall3Abi,
  offchainLookup,
  offchainLookupAbiItem,
  offchainLookupSignature,
  prepareEncodeFunctionData,
  universalSignatureValidatorAbi,
  universalSignatureValidatorByteCode
} from "./chunk-PNEMLORN.js";
import {
  assertCurrentChain,
  assertTransactionEIP1559,
  assertTransactionEIP2930,
  assertTransactionLegacy,
  blobsToCommitments,
  blobsToProofs,
  bytesToRlp,
  commitmentToVersionedHash,
  commitmentsToVersionedHashes,
  defineBlock,
  defineChain,
  defineTransaction,
  defineTransactionReceipt,
  extractChain,
  formatBlock,
  formatLog,
  formatTransaction,
  formatTransactionReceipt,
  getTransactionType,
  hexToRlp,
  serializeAccessList,
  serializeTransaction,
  sha256,
  toBlobSidecars,
  toBlobs,
  toRlp,
  transactionType
} from "./chunk-D7FY4WQF.js";
import "./chunk-4VVLOBZB.js";
import {
  AbiConstructorNotFoundError,
  AbiConstructorParamsNotFoundError,
  AbiDecodingDataSizeInvalidError,
  AbiDecodingDataSizeTooSmallError,
  AbiDecodingZeroDataError,
  AbiEncodingArrayLengthMismatchError,
  AbiEncodingBytesSizeMismatchError,
  AbiEncodingLengthMismatchError,
  AbiErrorInputsNotFoundError,
  AbiErrorNotFoundError,
  AbiErrorSignatureNotFoundError,
  AbiEventNotFoundError,
  AbiEventSignatureEmptyTopicsError,
  AbiEventSignatureNotFoundError,
  AbiFunctionNotFoundError,
  AbiFunctionOutputsNotFoundError,
  AbiFunctionSignatureNotFoundError,
  AccountStateConflictError,
  AtomicReadyWalletRejectedUpgradeError,
  AtomicityNotSupportedError,
  BaseError,
  BundleTooLargeError,
  BytesSizeMismatchError,
  CallExecutionError,
  ChainDisconnectedError,
  ChainDoesNotSupportContract,
  ChainMismatchError,
  ChainNotFoundError,
  ClientChainNotConfiguredError,
  ContractFunctionExecutionError,
  ContractFunctionRevertedError,
  ContractFunctionZeroDataError,
  CounterfactualDeploymentFailedError,
  DecodeLogDataMismatch,
  DecodeLogTopicsMismatch,
  DuplicateIdError,
  ExecutionRevertedError,
  FeeCapTooHighError,
  FeeCapTooLowError,
  FeeConflictError,
  HttpRequestError,
  InsufficientFundsError,
  IntegerOutOfRangeError,
  InternalRpcError,
  IntrinsicGasTooHighError,
  IntrinsicGasTooLowError,
  InvalidAbiDecodingTypeError,
  InvalidAbiEncodingTypeError,
  InvalidAddressError,
  InvalidArrayError,
  InvalidBytesBooleanError,
  InvalidChainIdError,
  InvalidDefinitionTypeError,
  InvalidHexBooleanError,
  InvalidHexValueError,
  InvalidInputRpcError,
  InvalidLegacyVError,
  InvalidParamsRpcError,
  InvalidRequestRpcError,
  InvalidSerializableTransactionError,
  InvalidSerializedTransactionError,
  InvalidSerializedTransactionTypeError,
  InvalidStorageKeySizeError,
  JsonRpcVersionUnsupportedError,
  LimitExceededRpcError,
  MethodNotFoundRpcError,
  MethodNotSupportedRpcError,
  NonceMaxValueError,
  NonceTooHighError,
  NonceTooLowError,
  ParseRpcError,
  ProviderDisconnectedError,
  ProviderRpcError,
  RawContractError,
  ResourceNotFoundRpcError,
  ResourceUnavailableRpcError,
  RpcError,
  RpcRequestError,
  SizeExceedsPaddingSizeError,
  SizeOverflowError,
  SliceOffsetOutOfBoundsError,
  SocketClosedError,
  StateAssignmentConflictError,
  SwitchChainError,
  TimeoutError,
  TipAboveFeeCapError,
  TransactionExecutionError,
  TransactionNotFoundError,
  TransactionReceiptNotFoundError,
  TransactionRejectedRpcError,
  TransactionTypeNotSupportedError,
  UnauthorizedProviderError,
  UnknownBundleIdError,
  UnknownNodeError,
  UnknownRpcError,
  UnsupportedChainIdError,
  UnsupportedNonOptionalCapabilityError,
  UnsupportedPackedAbiType,
  UnsupportedProviderMethodError,
  UserRejectedRequestError,
  WaitForTransactionReceiptTimeoutError,
  WebSocketRequestError,
  assertRequest,
  boolToBytes,
  boolToHex,
  bytesToBigInt,
  bytesToBool,
  bytesToHex,
  bytesToNumber,
  bytesToString,
  checksumAddress,
  concat,
  concatBytes,
  concatHex,
  decodeAbiParameters,
  decodeErrorResult,
  defineTransactionRequest,
  encodeAbiParameters,
  etherUnits,
  formatEther,
  formatGwei,
  formatTransactionRequest,
  formatUnits,
  fromBytes,
  fromHex,
  getAbiItem,
  getAddress,
  getChainContractAddress,
  gweiUnits,
  hexToBigInt,
  hexToBool,
  hexToBytes,
  hexToNumber,
  hexToString,
  isAddress,
  isHex,
  keccak256,
  maxInt104,
  maxInt112,
  maxInt120,
  maxInt128,
  maxInt136,
  maxInt144,
  maxInt152,
  maxInt16,
  maxInt160,
  maxInt168,
  maxInt176,
  maxInt184,
  maxInt192,
  maxInt200,
  maxInt208,
  maxInt216,
  maxInt224,
  maxInt232,
  maxInt24,
  maxInt240,
  maxInt248,
  maxInt256,
  maxInt32,
  maxInt40,
  maxInt48,
  maxInt56,
  maxInt64,
  maxInt72,
  maxInt8,
  maxInt80,
  maxInt88,
  maxInt96,
  maxUint104,
  maxUint112,
  maxUint120,
  maxUint128,
  maxUint136,
  maxUint144,
  maxUint152,
  maxUint16,
  maxUint160,
  maxUint168,
  maxUint176,
  maxUint184,
  maxUint192,
  maxUint200,
  maxUint208,
  maxUint216,
  maxUint224,
  maxUint232,
  maxUint24,
  maxUint240,
  maxUint248,
  maxUint256,
  maxUint32,
  maxUint40,
  maxUint48,
  maxUint56,
  maxUint64,
  maxUint72,
  maxUint8,
  maxUint80,
  maxUint88,
  maxUint96,
  minInt104,
  minInt112,
  minInt120,
  minInt128,
  minInt136,
  minInt144,
  minInt152,
  minInt16,
  minInt160,
  minInt168,
  minInt176,
  minInt184,
  minInt192,
  minInt200,
  minInt208,
  minInt216,
  minInt224,
  minInt232,
  minInt24,
  minInt240,
  minInt248,
  minInt256,
  minInt32,
  minInt40,
  minInt48,
  minInt56,
  minInt64,
  minInt72,
  minInt8,
  minInt80,
  minInt88,
  minInt96,
  numberToBytes,
  numberToHex,
  pad,
  padBytes,
  padHex,
  rpcTransactionType,
  setErrorConfig,
  size,
  slice,
  sliceBytes,
  sliceHex,
  stringToBytes,
  stringToHex,
  stringify,
  toBytes,
  toEventSelector,
  toFunctionSelector,
  toHex,
  toSignature,
  toSignatureHash,
  trim,
  weiUnits
} from "./chunk-NG2JVWA6.js";
import {
  CircularReferenceError,
  InvalidAbiItemError,
  InvalidAbiParameterError,
  InvalidAbiParametersError,
  InvalidAbiTypeParameterError,
  InvalidFunctionModifierError,
  InvalidModifierError,
  InvalidParameterError,
  InvalidParenthesisError,
  InvalidSignatureError,
  InvalidStructSignatureError,
  SolidityProtectedKeywordError,
  UnknownSignatureError,
  UnknownTypeError,
  parseAbi,
  parseAbiItem,
  parseAbiParameter,
  parseAbiParameters
} from "./chunk-DS4H6JFL.js";
import "./chunk-2BLWM4FZ.js";
import "./chunk-ONY6HBPH.js";
export {
  AbiConstructorNotFoundError,
  AbiConstructorParamsNotFoundError,
  AbiDecodingDataSizeInvalidError,
  AbiDecodingDataSizeTooSmallError,
  AbiDecodingZeroDataError,
  AbiEncodingArrayLengthMismatchError,
  AbiEncodingBytesSizeMismatchError,
  AbiEncodingLengthMismatchError,
  AbiErrorInputsNotFoundError,
  AbiErrorNotFoundError,
  AbiErrorSignatureNotFoundError,
  AbiEventNotFoundError,
  AbiEventSignatureEmptyTopicsError,
  AbiEventSignatureNotFoundError,
  AbiFunctionNotFoundError,
  AbiFunctionOutputsNotFoundError,
  AbiFunctionSignatureNotFoundError,
  AccountStateConflictError,
  AtomicReadyWalletRejectedUpgradeError,
  AtomicityNotSupportedError,
  BaseError,
  BaseFeeScalarError,
  BlockNotFoundError,
  BundleFailedError,
  BundleTooLargeError,
  BytesSizeMismatchError,
  CallExecutionError,
  ChainDisconnectedError,
  ChainDoesNotSupportContract,
  ChainMismatchError,
  ChainNotFoundError,
  CircularReferenceError,
  ClientChainNotConfiguredError,
  ContractFunctionExecutionError,
  ContractFunctionRevertedError,
  ContractFunctionZeroDataError,
  CounterfactualDeploymentFailedError,
  DecodeLogDataMismatch,
  DecodeLogTopicsMismatch,
  DuplicateIdError,
  ProviderRpcError2 as EIP1193ProviderRpcError,
  Eip1559FeesNotSupportedError,
  EnsAvatarInvalidNftUriError,
  EnsAvatarUnsupportedNamespaceError,
  EnsAvatarUriResolutionError,
  EstimateGasExecutionError,
  ExecutionRevertedError,
  FeeCapTooHighError,
  FeeCapTooLowError,
  FeeConflictError,
  FilterTypeNotSupportedError,
  HttpRequestError,
  InsufficientFundsError,
  IntegerOutOfRangeError,
  InternalRpcError,
  IntrinsicGasTooHighError,
  IntrinsicGasTooLowError,
  InvalidAbiDecodingTypeError,
  InvalidAbiEncodingTypeError,
  InvalidAbiItemError,
  InvalidAbiParameterError,
  InvalidAbiParametersError,
  InvalidAbiTypeParameterError,
  InvalidAddressError,
  InvalidArrayError,
  InvalidBytesBooleanError,
  InvalidChainIdError,
  InvalidDecimalNumberError,
  InvalidDefinitionTypeError,
  InvalidDomainError,
  InvalidFunctionModifierError,
  InvalidHexBooleanError,
  InvalidHexValueError,
  InvalidInputRpcError,
  InvalidLegacyVError,
  InvalidModifierError,
  InvalidParameterError,
  InvalidParamsRpcError,
  InvalidParenthesisError,
  InvalidPrimaryTypeError,
  InvalidRequestRpcError,
  InvalidSerializableTransactionError,
  InvalidSerializedTransactionError,
  InvalidSerializedTransactionTypeError,
  InvalidSignatureError,
  InvalidStorageKeySizeError,
  InvalidStructSignatureError,
  InvalidStructTypeError,
  JsonRpcVersionUnsupportedError,
  LimitExceededRpcError,
  MaxFeePerGasTooLowError,
  MethodNotFoundRpcError,
  MethodNotSupportedRpcError,
  NonceMaxValueError,
  NonceTooHighError,
  NonceTooLowError,
  ParseRpcError,
  ProviderDisconnectedError,
  ProviderRpcError,
  RawContractError,
  ResourceNotFoundRpcError,
  ResourceUnavailableRpcError,
  RpcError,
  RpcRequestError,
  SizeExceedsPaddingSizeError,
  SizeOverflowError,
  SliceOffsetOutOfBoundsError,
  SocketClosedError,
  SolidityProtectedKeywordError,
  StateAssignmentConflictError,
  SwitchChainError,
  TimeoutError,
  TipAboveFeeCapError,
  TransactionExecutionError,
  TransactionNotFoundError,
  TransactionReceiptNotFoundError,
  TransactionRejectedRpcError,
  TransactionTypeNotSupportedError,
  UnauthorizedProviderError,
  UnknownBundleIdError,
  UnknownNodeError,
  UnknownRpcError,
  UnknownSignatureError,
  UnknownTypeError,
  UnsupportedChainIdError,
  UnsupportedNonOptionalCapabilityError,
  UnsupportedPackedAbiType,
  UnsupportedProviderMethodError,
  UrlRequiredError,
  UserRejectedRequestError,
  WaitForCallsStatusTimeoutError,
  WaitForTransactionReceiptTimeoutError,
  WebSocketRequestError,
  assertCurrentChain,
  assertRequest,
  assertTransactionEIP1559,
  assertTransactionEIP2930,
  assertTransactionLegacy,
  blobsToCommitments,
  blobsToProofs,
  boolToBytes,
  boolToHex,
  bytesToBigInt,
  bytesToBool,
  bytesToHex,
  bytesToNumber,
  bytesToRlp,
  bytesToString,
  ccipRequest as ccipFetch,
  ccipRequest,
  checksumAddress,
  commitmentToVersionedHash,
  commitmentsToVersionedHashes,
  serializeCompactSignature as compactSignatureToHex,
  compactSignatureToSignature,
  concat,
  concatBytes,
  concatHex,
  createClient,
  createNonceManager,
  createPublicClient,
  createTestClient,
  createTransport,
  createWalletClient,
  custom,
  decodeAbiParameters,
  decodeDeployData,
  decodeErrorResult,
  decodeEventLog,
  decodeFunctionData,
  decodeFunctionResult,
  defineBlock,
  defineChain,
  defineKzg,
  defineTransaction,
  defineTransactionReceipt,
  defineTransactionRequest,
  deploylessCallViaBytecodeBytecode,
  deploylessCallViaFactoryBytecode,
  domainSeparator,
  encodeAbiParameters,
  encodeDeployData,
  encodeErrorResult,
  encodeEventTopics,
  encodeFunctionData,
  encodeFunctionResult,
  encodePacked,
  erc1155Abi,
  erc20Abi,
  erc20Abi_bytes32,
  erc4626Abi,
  erc721Abi,
  ethAddress,
  etherUnits,
  extractChain,
  fallback,
  formatBlock,
  formatEther,
  formatGwei,
  formatLog,
  formatTransaction,
  formatTransactionReceipt,
  formatTransactionRequest,
  formatUnits,
  fromBlobs,
  fromBytes,
  fromHex,
  fromRlp,
  getAbiItem,
  getAddress,
  getChainContractAddress,
  getContract,
  getContractAddress,
  getContractError,
  getCreate2Address,
  getCreateAddress,
  toEventSelector as getEventSelector,
  toSignature as getEventSignature,
  toFunctionSelector as getFunctionSelector,
  toSignature as getFunctionSignature,
  getSerializedTransactionType,
  getTransactionType,
  getTypesForEIP712Domain,
  gweiUnits,
  hashDomain,
  hashMessage,
  hashStruct,
  hashTypedData,
  hexToBigInt,
  hexToBool,
  hexToBytes,
  parseCompactSignature as hexToCompactSignature,
  hexToNumber,
  hexToRlp,
  parseSignature as hexToSignature,
  hexToString,
  http,
  isAddress,
  isAddressEqual,
  isBytes,
  isErc6492Signature,
  isHash,
  isHex,
  keccak256,
  labelhash,
  maxInt104,
  maxInt112,
  maxInt120,
  maxInt128,
  maxInt136,
  maxInt144,
  maxInt152,
  maxInt16,
  maxInt160,
  maxInt168,
  maxInt176,
  maxInt184,
  maxInt192,
  maxInt200,
  maxInt208,
  maxInt216,
  maxInt224,
  maxInt232,
  maxInt24,
  maxInt240,
  maxInt248,
  maxInt256,
  maxInt32,
  maxInt40,
  maxInt48,
  maxInt56,
  maxInt64,
  maxInt72,
  maxInt8,
  maxInt80,
  maxInt88,
  maxInt96,
  maxUint104,
  maxUint112,
  maxUint120,
  maxUint128,
  maxUint136,
  maxUint144,
  maxUint152,
  maxUint16,
  maxUint160,
  maxUint168,
  maxUint176,
  maxUint184,
  maxUint192,
  maxUint200,
  maxUint208,
  maxUint216,
  maxUint224,
  maxUint232,
  maxUint24,
  maxUint240,
  maxUint248,
  maxUint256,
  maxUint32,
  maxUint40,
  maxUint48,
  maxUint56,
  maxUint64,
  maxUint72,
  maxUint8,
  maxUint80,
  maxUint88,
  maxUint96,
  minInt104,
  minInt112,
  minInt120,
  minInt128,
  minInt136,
  minInt144,
  minInt152,
  minInt16,
  minInt160,
  minInt168,
  minInt176,
  minInt184,
  minInt192,
  minInt200,
  minInt208,
  minInt216,
  minInt224,
  minInt232,
  minInt24,
  minInt240,
  minInt248,
  minInt256,
  minInt32,
  minInt40,
  minInt48,
  minInt56,
  minInt64,
  minInt72,
  minInt8,
  minInt80,
  minInt88,
  minInt96,
  multicall3Abi,
  namehash,
  nonceManager,
  numberToBytes,
  numberToHex,
  offchainLookup,
  offchainLookupAbiItem,
  offchainLookupSignature,
  pad,
  padBytes,
  padHex,
  parseAbi,
  parseAbiItem,
  parseAbiParameter,
  parseAbiParameters,
  parseCompactSignature,
  parseErc6492Signature,
  parseEther,
  parseEventLogs,
  parseGwei,
  parseSignature,
  parseTransaction,
  parseUnits,
  prepareEncodeFunctionData,
  presignMessagePrefix,
  publicActions,
  recoverAddress,
  recoverMessageAddress,
  recoverPublicKey,
  recoverTransactionAddress,
  recoverTypedDataAddress,
  ripemd160,
  rpcSchema,
  rpcTransactionType,
  serializeAccessList,
  serializeCompactSignature,
  serializeErc6492Signature,
  serializeSignature,
  serializeTransaction,
  serializeTypedData,
  setErrorConfig,
  setupKzg,
  sha256,
  shouldThrow,
  sidecarsToVersionedHashes,
  signatureToCompactSignature,
  serializeSignature as signatureToHex,
  size,
  slice,
  sliceBytes,
  sliceHex,
  stringToBytes,
  stringToHex,
  stringify,
  testActions,
  toBlobSidecars,
  toBlobs,
  toBytes,
  toSignatureHash as toEventHash,
  toEventSelector,
  toSignature as toEventSignature,
  toSignatureHash as toFunctionHash,
  toFunctionSelector,
  toSignature as toFunctionSignature,
  toHex,
  toPrefixedMessage,
  toRlp,
  transactionType,
  trim,
  universalSignatureValidatorAbi,
  universalSignatureValidatorByteCode,
  validateTypedData,
  verifyHash,
  verifyMessage,
  verifyTypedData,
  walletActions,
  webSocket,
  weiUnits,
  withCache,
  withRetry,
  withTimeout,
  zeroAddress,
  zeroHash
};
