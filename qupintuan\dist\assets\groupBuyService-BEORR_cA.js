import{e as t}from"./index-BDw84Puy.js";import{E as k,R as E,h as L,j as U,i as g,f as v,r as x}from"./index-BDw84Puy.js";import{createAndLock as r,fetchTotalRooms as e,fetchRoom as c,joinRoom as n,claimReward as m}from"./core-Df_LCCfn.js";import"./web3-DnWbColA.js";import"./vendor-CgHzTxSQ.js";import"./ui-B9ZzSjJF.js";import"./basicOperations-DQxdkWLT.js";import"./roomManagement-D4JBlV07.js";import"./rewardOperations-CoNCQb06.js";import"./transaction-r-FebHwR.js";async function y(o,a){return r(o,a)}async function S(o){return e(o)}async function T(o,a){return c(o,a)}async function w(o){return n(o)}async function $(o){return m(o)}async function j(o){return t.fetchRooms(o)}export{k as ERROR_CODES,E as ROOM_STATUS,L as calculateRemainingTime,U as canUserClaimReward,g as canUserJoinRoom,$ as claimReward,y as createAndLock,t as default,T as fetchRoom,j as fetchRooms,S as fetchTotalRooms,v as formatRoomStatus,w as joinRoom,t as newGroupBuyService,x as recalculateRoomStatus};
