import{g as p,A as m,b as C}from"./index-BDw84Puy.js";import{c as N,h as B,f as n}from"./web3-DnWbColA.js";import"./ui-B9ZzSjJF.js";import"./vendor-CgHzTxSQ.js";const l=N({chain:C,transport:B("https://bsc-testnet.public.blastapi.io",{batch:!0,fetchOptions:{timeout:12e3},retryCount:2,retryDelay:1500})}),i=new Map,S=6e4;function g(e,...t){return`${e}_${JSON.stringify(t)}`}function b(e){const t=i.get(e);return t&&Date.now()-t.timestamp<S?t.data:null}function h(e,t){i.set(e,{data:t,timestamp:Date.now()})}async function P(){const e=g("systemOverview"),t=b(e);if(t)return t;try{const o=p(97,"SimpleQueryContract"),[a,r]=await Promise.all([l.readContract({address:o,abi:m.SimpleQueryContract,functionName:"getGroupBuyStats",args:[50]}),l.readContract({address:o,abi:m.SimpleQueryContract,functionName:"getLockingStats",args:[50]})]),c={groupBuyStats:{totalRooms:Number(a.totalRooms),activeRooms:Number(a.activeRooms),completedRooms:Number(a.completedRooms),totalParticipants:Number(a.totalParticipants)},lockingStats:{totalNodeStaked:n(r.totalNodeStaked,18),totalGroupBuyLocked:n(r.totalGroupBuyLocked,18),totalBuybackLocked:n(r.totalBuybackLocked,18),totalLocked:n(r.totalLocked,18),activeGroupBuyRooms:Number(r.activeGroupBuyRooms),activeBuybackRooms:Number(r.activeBuybackRooms)},timestamp:new Date};return h(e,c),c}catch(o){throw o}}async function O(){const e=g("optimizedDailyPointsStats"),t=b(e);if(t)return t;try{const o=p(97,"SimpleQueryContract"),a=await l.readContract({address:o,abi:m.SimpleQueryContract,functionName:"getGroupBuyStats",args:[100]}),r=new Date().toDateString(),c=`groupBuyStats_${new Date(Date.now()-1440*60*1e3).toDateString()}`;let u=null;try{const s=localStorage.getItem(c);s&&(u=JSON.parse(s))}catch{}const y=u?Number(a.completedRooms)-Number(u.completedRooms||0):0,f=Math.max(0,y*30);try{localStorage.setItem(`groupBuyStats_${r}`,JSON.stringify({completedRooms:Number(a.completedRooms),totalRooms:Number(a.totalRooms),timestamp:Date.now()}))}catch(s){console.warn("无法缓存拼团统计数据:",s)}const d={dailyGroupBuyPointsIssued:f.toString(),dailySalesPointsIssued:"0",dailyPointsTransfers:"0",dailyPointsExchanged:"0",dailyPointsBurned:"0",todayCompletedRooms:y,source:"SimpleQueryContract + Estimation",note:"基于查询合约的房间统计估算每日积分发放"};return h(e,d),d}catch(o){throw o}}function R(){const e=Date.now();for(const[t,o]of i.entries())e-o.timestamp>S&&i.delete(t)}setInterval(R,6e4);export{R as clearExpiredCache,O as getOptimizedDailyPointsStatsFromQueryContract,P as getSystemOverviewFromQueryContract};
