{"version": 3, "sources": ["../../@walletconnect/time/node_modules/tslib/tslib.es6.js", "../../@walletconnect/time/src/utils/delay.ts", "../../@walletconnect/time/src/constants/misc.ts", "../../@walletconnect/time/src/constants/time.ts", "../../@walletconnect/time/src/constants/index.ts", "../../@walletconnect/time/src/utils/convert.ts", "../../@walletconnect/time/src/utils/index.ts", "../../@walletconnect/time/src/watch.ts", "../../@walletconnect/time/src/types/watch.ts", "../../@walletconnect/time/src/types/index.ts", "../../@walletconnect/time/src/index.ts", "../../@walletconnect/window-getters/src/index.ts", "../../@walletconnect/window-metadata/src/index.ts", "../../quick-format-unescaped/index.js", "../../pino/browser.js", "../../@walletconnect/environment/node_modules/tslib/tslib.es6.js", "../../@walletconnect/environment/src/crypto.ts", "../../@walletconnect/environment/src/env.ts", "../../@walletconnect/environment/src/index.ts", "../../@walletconnect/jsonrpc-http-connection/node_modules/cross-fetch/dist/browser-ponyfill.js", "../../@walletconnect/jsonrpc-ws-connection/node_modules/ws/browser.js", "../../detect-browser/es/index.js", "../../base-x/src/esm/index.js", "../../bs58/src/esm/index.js", "../../@walletconnect/safe-json/src/index.ts", "../../uint8arrays/esm/src/alloc.js", "../../uint8arrays/esm/src/concat.js", "../../multiformats/esm/src/bases/identity.js", "../../multiformats/esm/vendor/base-x.js", "../../multiformats/esm/src/bytes.js", "../../multiformats/esm/src/bases/base.js", "../../multiformats/esm/src/bases/base2.js", "../../multiformats/esm/src/bases/base8.js", "../../multiformats/esm/src/bases/base10.js", "../../multiformats/esm/src/bases/base16.js", "../../multiformats/esm/src/bases/base32.js", "../../multiformats/esm/src/bases/base36.js", "../../multiformats/esm/src/bases/base58.js", "../../multiformats/esm/src/bases/base64.js", "../../multiformats/esm/src/bases/base256emoji.js", "../../multiformats/esm/src/hashes/sha2-browser.js", "../../multiformats/esm/vendor/varint.js", "../../multiformats/esm/src/varint.js", "../../multiformats/esm/src/hashes/digest.js", "../../multiformats/esm/src/hashes/hasher.js", "../../multiformats/esm/src/hashes/identity.js", "../../multiformats/esm/src/codecs/json.js", "../../multiformats/esm/src/cid.js", "../../multiformats/esm/src/basics.js", "../../uint8arrays/esm/src/util/bases.js", "../../uint8arrays/esm/src/from-string.js", "../../uint8arrays/esm/src/to-string.js", "../../@walletconnect/relay-api/src/misc.ts", "../../@walletconnect/relay-api/src/validators.ts", "../../@walletconnect/relay-api/src/parsers.ts", "../../@walletconnect/relay-api/src/jsonrpc.ts", "../../@walletconnect/jsonrpc-utils/src/constants.ts", "../../@walletconnect/jsonrpc-utils/src/error.ts", "../../@walletconnect/jsonrpc-utils/src/format.ts", "../../@walletconnect/jsonrpc-utils/src/index.ts", "../../@walletconnect/jsonrpc-utils/src/env.ts", "../../@walletconnect/jsonrpc-utils/src/routing.ts", "../../@walletconnect/jsonrpc-types/src/misc.ts", "../../@walletconnect/jsonrpc-types/src/provider.ts", "../../@walletconnect/jsonrpc-utils/src/url.ts", "../../@walletconnect/jsonrpc-utils/src/validators.ts", "../../@walletconnect/events/src/events.ts", "../../destr/dist/index.mjs"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", null, null, null, null, null, null, null, null, null, null, null, null, "'use strict'\nfunction tryStringify (o) {\n  try { return JSON.stringify(o) } catch(e) { return '\"[Circular]\"' }\n}\n\nmodule.exports = format\n\nfunction format(f, args, opts) {\n  var ss = (opts && opts.stringify) || tryStringify\n  var offset = 1\n  if (typeof f === 'object' && f !== null) {\n    var len = args.length + offset\n    if (len === 1) return f\n    var objects = new Array(len)\n    objects[0] = ss(f)\n    for (var index = 1; index < len; index++) {\n      objects[index] = ss(args[index])\n    }\n    return objects.join(' ')\n  }\n  if (typeof f !== 'string') {\n    return f\n  }\n  var argLen = args.length\n  if (argLen === 0) return f\n  var str = ''\n  var a = 1 - offset\n  var lastPos = -1\n  var flen = (f && f.length) || 0\n  for (var i = 0; i < flen;) {\n    if (f.charCodeAt(i) === 37 && i + 1 < flen) {\n      lastPos = lastPos > -1 ? lastPos : 0\n      switch (f.charCodeAt(i + 1)) {\n        case 100: // 'd'\n        case 102: // 'f'\n          if (a >= argLen)\n            break\n          if (args[a] == null)  break\n          if (lastPos < i)\n            str += f.slice(lastPos, i)\n          str += Number(args[a])\n          lastPos = i + 2\n          i++\n          break\n        case 105: // 'i'\n          if (a >= argLen)\n            break\n          if (args[a] == null)  break\n          if (lastPos < i)\n            str += f.slice(lastPos, i)\n          str += Math.floor(Number(args[a]))\n          lastPos = i + 2\n          i++\n          break\n        case 79: // 'O'\n        case 111: // 'o'\n        case 106: // 'j'\n          if (a >= argLen)\n            break\n          if (args[a] === undefined) break\n          if (lastPos < i)\n            str += f.slice(lastPos, i)\n          var type = typeof args[a]\n          if (type === 'string') {\n            str += '\\'' + args[a] + '\\''\n            lastPos = i + 2\n            i++\n            break\n          }\n          if (type === 'function') {\n            str += args[a].name || '<anonymous>'\n            lastPos = i + 2\n            i++\n            break\n          }\n          str += ss(args[a])\n          lastPos = i + 2\n          i++\n          break\n        case 115: // 's'\n          if (a >= argLen)\n            break\n          if (lastPos < i)\n            str += f.slice(lastPos, i)\n          str += String(args[a])\n          lastPos = i + 2\n          i++\n          break\n        case 37: // '%'\n          if (lastPos < i)\n            str += f.slice(lastPos, i)\n          str += '%'\n          lastPos = i + 2\n          i++\n          a--\n          break\n      }\n      ++a\n    }\n    ++i\n  }\n  if (lastPos === -1)\n    return f\n  else if (lastPos < flen) {\n    str += f.slice(lastPos)\n  }\n\n  return str\n}\n", "'use strict'\n\nconst format = require('quick-format-unescaped')\n\nmodule.exports = pino\n\nconst _console = pfGlobalThisOrFallback().console || {}\nconst stdSerializers = {\n  mapHttpRequest: mock,\n  mapHttpResponse: mock,\n  wrapRequestSerializer: passthrough,\n  wrapResponseSerializer: passthrough,\n  wrapErrorSerializer: passthrough,\n  req: mock,\n  res: mock,\n  err: asErrValue\n}\n\nfunction shouldSerialize (serialize, serializers) {\n  if (Array.isArray(serialize)) {\n    const hasToFilter = serialize.filter(function (k) {\n      return k !== '!stdSerializers.err'\n    })\n    return hasToFilter\n  } else if (serialize === true) {\n    return Object.keys(serializers)\n  }\n\n  return false\n}\n\nfunction pino (opts) {\n  opts = opts || {}\n  opts.browser = opts.browser || {}\n\n  const transmit = opts.browser.transmit\n  if (transmit && typeof transmit.send !== 'function') { throw Error('pino: transmit option must have a send function') }\n\n  const proto = opts.browser.write || _console\n  if (opts.browser.write) opts.browser.asObject = true\n  const serializers = opts.serializers || {}\n  const serialize = shouldSerialize(opts.browser.serialize, serializers)\n  let stdErrSerialize = opts.browser.serialize\n\n  if (\n    Array.isArray(opts.browser.serialize) &&\n    opts.browser.serialize.indexOf('!stdSerializers.err') > -1\n  ) stdErrSerialize = false\n\n  const levels = ['error', 'fatal', 'warn', 'info', 'debug', 'trace']\n\n  if (typeof proto === 'function') {\n    proto.error = proto.fatal = proto.warn =\n    proto.info = proto.debug = proto.trace = proto\n  }\n  if (opts.enabled === false) opts.level = 'silent'\n  const level = opts.level || 'info'\n  const logger = Object.create(proto)\n  if (!logger.log) logger.log = noop\n\n  Object.defineProperty(logger, 'levelVal', {\n    get: getLevelVal\n  })\n  Object.defineProperty(logger, 'level', {\n    get: getLevel,\n    set: setLevel\n  })\n\n  const setOpts = {\n    transmit,\n    serialize,\n    asObject: opts.browser.asObject,\n    levels,\n    timestamp: getTimeFunction(opts)\n  }\n  logger.levels = pino.levels\n  logger.level = level\n\n  logger.setMaxListeners = logger.getMaxListeners =\n  logger.emit = logger.addListener = logger.on =\n  logger.prependListener = logger.once =\n  logger.prependOnceListener = logger.removeListener =\n  logger.removeAllListeners = logger.listeners =\n  logger.listenerCount = logger.eventNames =\n  logger.write = logger.flush = noop\n  logger.serializers = serializers\n  logger._serialize = serialize\n  logger._stdErrSerialize = stdErrSerialize\n  logger.child = child\n\n  if (transmit) logger._logEvent = createLogEventShape()\n\n  function getLevelVal () {\n    return this.level === 'silent'\n      ? Infinity\n      : this.levels.values[this.level]\n  }\n\n  function getLevel () {\n    return this._level\n  }\n  function setLevel (level) {\n    if (level !== 'silent' && !this.levels.values[level]) {\n      throw Error('unknown level ' + level)\n    }\n    this._level = level\n\n    set(setOpts, logger, 'error', 'log') // <-- must stay first\n    set(setOpts, logger, 'fatal', 'error')\n    set(setOpts, logger, 'warn', 'error')\n    set(setOpts, logger, 'info', 'log')\n    set(setOpts, logger, 'debug', 'log')\n    set(setOpts, logger, 'trace', 'log')\n  }\n\n  function child (bindings, childOptions) {\n    if (!bindings) {\n      throw new Error('missing bindings for child Pino')\n    }\n    childOptions = childOptions || {}\n    if (serialize && bindings.serializers) {\n      childOptions.serializers = bindings.serializers\n    }\n    const childOptionsSerializers = childOptions.serializers\n    if (serialize && childOptionsSerializers) {\n      var childSerializers = Object.assign({}, serializers, childOptionsSerializers)\n      var childSerialize = opts.browser.serialize === true\n        ? Object.keys(childSerializers)\n        : serialize\n      delete bindings.serializers\n      applySerializers([bindings], childSerialize, childSerializers, this._stdErrSerialize)\n    }\n    function Child (parent) {\n      this._childLevel = (parent._childLevel | 0) + 1\n      this.error = bind(parent, bindings, 'error')\n      this.fatal = bind(parent, bindings, 'fatal')\n      this.warn = bind(parent, bindings, 'warn')\n      this.info = bind(parent, bindings, 'info')\n      this.debug = bind(parent, bindings, 'debug')\n      this.trace = bind(parent, bindings, 'trace')\n      if (childSerializers) {\n        this.serializers = childSerializers\n        this._serialize = childSerialize\n      }\n      if (transmit) {\n        this._logEvent = createLogEventShape(\n          [].concat(parent._logEvent.bindings, bindings)\n        )\n      }\n    }\n    Child.prototype = this\n    return new Child(this)\n  }\n  return logger\n}\n\npino.levels = {\n  values: {\n    fatal: 60,\n    error: 50,\n    warn: 40,\n    info: 30,\n    debug: 20,\n    trace: 10\n  },\n  labels: {\n    10: 'trace',\n    20: 'debug',\n    30: 'info',\n    40: 'warn',\n    50: 'error',\n    60: 'fatal'\n  }\n}\n\npino.stdSerializers = stdSerializers\npino.stdTimeFunctions = Object.assign({}, { nullTime, epochTime, unixTime, isoTime })\n\nfunction set (opts, logger, level, fallback) {\n  const proto = Object.getPrototypeOf(logger)\n  logger[level] = logger.levelVal > logger.levels.values[level]\n    ? noop\n    : (proto[level] ? proto[level] : (_console[level] || _console[fallback] || noop))\n\n  wrap(opts, logger, level)\n}\n\nfunction wrap (opts, logger, level) {\n  if (!opts.transmit && logger[level] === noop) return\n\n  logger[level] = (function (write) {\n    return function LOG () {\n      const ts = opts.timestamp()\n      const args = new Array(arguments.length)\n      const proto = (Object.getPrototypeOf && Object.getPrototypeOf(this) === _console) ? _console : this\n      for (var i = 0; i < args.length; i++) args[i] = arguments[i]\n\n      if (opts.serialize && !opts.asObject) {\n        applySerializers(args, this._serialize, this.serializers, this._stdErrSerialize)\n      }\n      if (opts.asObject) write.call(proto, asObject(this, level, args, ts))\n      else write.apply(proto, args)\n\n      if (opts.transmit) {\n        const transmitLevel = opts.transmit.level || logger.level\n        const transmitValue = pino.levels.values[transmitLevel]\n        const methodValue = pino.levels.values[level]\n        if (methodValue < transmitValue) return\n        transmit(this, {\n          ts,\n          methodLevel: level,\n          methodValue,\n          transmitLevel,\n          transmitValue: pino.levels.values[opts.transmit.level || logger.level],\n          send: opts.transmit.send,\n          val: logger.levelVal\n        }, args)\n      }\n    }\n  })(logger[level])\n}\n\nfunction asObject (logger, level, args, ts) {\n  if (logger._serialize) applySerializers(args, logger._serialize, logger.serializers, logger._stdErrSerialize)\n  const argsCloned = args.slice()\n  let msg = argsCloned[0]\n  const o = {}\n  if (ts) {\n    o.time = ts\n  }\n  o.level = pino.levels.values[level]\n  let lvl = (logger._childLevel | 0) + 1\n  if (lvl < 1) lvl = 1\n  // deliberate, catching objects, arrays\n  if (msg !== null && typeof msg === 'object') {\n    while (lvl-- && typeof argsCloned[0] === 'object') {\n      Object.assign(o, argsCloned.shift())\n    }\n    msg = argsCloned.length ? format(argsCloned.shift(), argsCloned) : undefined\n  } else if (typeof msg === 'string') msg = format(argsCloned.shift(), argsCloned)\n  if (msg !== undefined) o.msg = msg\n  return o\n}\n\nfunction applySerializers (args, serialize, serializers, stdErrSerialize) {\n  for (const i in args) {\n    if (stdErrSerialize && args[i] instanceof Error) {\n      args[i] = pino.stdSerializers.err(args[i])\n    } else if (typeof args[i] === 'object' && !Array.isArray(args[i])) {\n      for (const k in args[i]) {\n        if (serialize && serialize.indexOf(k) > -1 && k in serializers) {\n          args[i][k] = serializers[k](args[i][k])\n        }\n      }\n    }\n  }\n}\n\nfunction bind (parent, bindings, level) {\n  return function () {\n    const args = new Array(1 + arguments.length)\n    args[0] = bindings\n    for (var i = 1; i < args.length; i++) {\n      args[i] = arguments[i - 1]\n    }\n    return parent[level].apply(this, args)\n  }\n}\n\nfunction transmit (logger, opts, args) {\n  const send = opts.send\n  const ts = opts.ts\n  const methodLevel = opts.methodLevel\n  const methodValue = opts.methodValue\n  const val = opts.val\n  const bindings = logger._logEvent.bindings\n\n  applySerializers(\n    args,\n    logger._serialize || Object.keys(logger.serializers),\n    logger.serializers,\n    logger._stdErrSerialize === undefined ? true : logger._stdErrSerialize\n  )\n  logger._logEvent.ts = ts\n  logger._logEvent.messages = args.filter(function (arg) {\n    // bindings can only be objects, so reference equality check via indexOf is fine\n    return bindings.indexOf(arg) === -1\n  })\n\n  logger._logEvent.level.label = methodLevel\n  logger._logEvent.level.value = methodValue\n\n  send(methodLevel, logger._logEvent, val)\n\n  logger._logEvent = createLogEventShape(bindings)\n}\n\nfunction createLogEventShape (bindings) {\n  return {\n    ts: 0,\n    messages: [],\n    bindings: bindings || [],\n    level: { label: '', value: 0 }\n  }\n}\n\nfunction asErrValue (err) {\n  const obj = {\n    type: err.constructor.name,\n    msg: err.message,\n    stack: err.stack\n  }\n  for (const key in err) {\n    if (obj[key] === undefined) {\n      obj[key] = err[key]\n    }\n  }\n  return obj\n}\n\nfunction getTimeFunction (opts) {\n  if (typeof opts.timestamp === 'function') {\n    return opts.timestamp\n  }\n  if (opts.timestamp === false) {\n    return nullTime\n  }\n  return epochTime\n}\n\nfunction mock () { return {} }\nfunction passthrough (a) { return a }\nfunction noop () {}\n\nfunction nullTime () { return false }\nfunction epochTime () { return Date.now() }\nfunction unixTime () { return Math.round(Date.now() / 1000.0) }\nfunction isoTime () { return new Date(Date.now()).toISOString() } // using Date.now() for testability\n\n/* eslint-disable */\n/* istanbul ignore next */\nfunction pfGlobalThisOrFallback () {\n  function defd (o) { return typeof o !== 'undefined' && o }\n  try {\n    if (typeof globalThis !== 'undefined') return globalThis\n    Object.defineProperty(Object.prototype, 'globalThis', {\n      get: function () {\n        delete Object.prototype.globalThis\n        return (this.globalThis = this)\n      },\n      configurable: true\n    })\n    return globalThis\n  } catch (e) {\n    return defd(self) || defd(window) || defd(this) || {}\n  }\n}\n/* eslint-enable */\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", null, null, null, "// Save global object in a variable\nvar __global__ =\n(typeof globalThis !== 'undefined' && globalThis) ||\n(typeof self !== 'undefined' && self) ||\n(typeof global !== 'undefined' && global);\n// Create an object that extends from __global__ without the fetch function\nvar __globalThis__ = (function () {\nfunction F() {\nthis.fetch = false;\nthis.DOMException = __global__.DOMException\n}\nF.prototype = __global__; // Needed for feature detection on whatwg-fetch's code\nreturn new F();\n})();\n// Wraps whatwg-fetch with a function scope to hijack the global object\n// \"globalThis\" that's going to be patched\n(function(globalThis) {\n\nvar irrelevant = (function (exports) {\n\n  /* eslint-disable no-prototype-builtins */\n  var g =\n    (typeof globalThis !== 'undefined' && globalThis) ||\n    (typeof self !== 'undefined' && self) ||\n    // eslint-disable-next-line no-undef\n    (typeof global !== 'undefined' && global) ||\n    {};\n\n  var support = {\n    searchParams: 'URLSearchParams' in g,\n    iterable: 'Symbol' in g && 'iterator' in Symbol,\n    blob:\n      'FileReader' in g &&\n      'Blob' in g &&\n      (function() {\n        try {\n          new Blob();\n          return true\n        } catch (e) {\n          return false\n        }\n      })(),\n    formData: 'FormData' in g,\n    arrayBuffer: 'ArrayBuffer' in g\n  };\n\n  function isDataView(obj) {\n    return obj && DataView.prototype.isPrototypeOf(obj)\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ];\n\n    var isArrayBufferView =\n      ArrayBuffer.isView ||\n      function(obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n      };\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name);\n    }\n    if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n      throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift();\n        return {done: value === undefined, value: value}\n      }\n    };\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      };\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {};\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        if (header.length != 2) {\n          throw new TypeError('Headers constructor: expected name/value pair to be length 2, found' + header.length)\n        }\n        this.append(header[0], header[1]);\n      }, this);\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name]);\n      }, this);\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name);\n    value = normalizeValue(value);\n    var oldValue = this.map[name];\n    this.map[name] = oldValue ? oldValue + ', ' + value : value;\n  };\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)];\n  };\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name);\n    return this.has(name) ? this.map[name] : null\n  };\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  };\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value);\n  };\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this);\n      }\n    }\n  };\n\n  Headers.prototype.keys = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push(name);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.values = function() {\n    var items = [];\n    this.forEach(function(value) {\n      items.push(value);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.entries = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push([name, value]);\n    });\n    return iteratorFor(items)\n  };\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n  }\n\n  function consumed(body) {\n    if (body._noBody) return\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true;\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result);\n      };\n      reader.onerror = function() {\n        reject(reader.error);\n      };\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsArrayBuffer(blob);\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type);\n    var encoding = match ? match[1] : 'utf-8';\n    reader.readAsText(blob, encoding);\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf);\n    var chars = new Array(view.length);\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i]);\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength);\n      view.set(new Uint8Array(buf));\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false;\n\n    this._initBody = function(body) {\n      /*\n        fetch-mock wraps the Response object in an ES6 Proxy to\n        provide useful test harness features such as flush. However, on\n        ES5 browsers without fetch or Proxy support pollyfills must be used;\n        the proxy-pollyfill is unable to proxy an attribute unless it exists\n        on the object before the Proxy is created. This change ensures\n        Response.bodyUsed exists on the instance, while maintaining the\n        semantic of setting Request.bodyUsed in the constructor before\n        _initBody is called.\n      */\n      // eslint-disable-next-line no-self-assign\n      this.bodyUsed = this.bodyUsed;\n      this._bodyInit = body;\n      if (!body) {\n        this._noBody = true;\n        this._bodyText = '';\n      } else if (typeof body === 'string') {\n        this._bodyText = body;\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body;\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body;\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString();\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer);\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer]);\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body);\n      } else {\n        this._bodyText = body = Object.prototype.toString.call(body);\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8');\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type);\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        }\n      }\n    };\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      };\n    }\n\n    this.arrayBuffer = function() {\n      if (this._bodyArrayBuffer) {\n        var isConsumed = consumed(this);\n        if (isConsumed) {\n          return isConsumed\n        } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n          return Promise.resolve(\n            this._bodyArrayBuffer.buffer.slice(\n              this._bodyArrayBuffer.byteOffset,\n              this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n            )\n          )\n        } else {\n          return Promise.resolve(this._bodyArrayBuffer)\n        }\n      } else if (support.blob) {\n        return this.blob().then(readBlobAsArrayBuffer)\n      } else {\n        throw new Error('could not read as ArrayBuffer')\n      }\n    };\n\n    this.text = function() {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    };\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      };\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    };\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['CONNECT', 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', 'TRACE'];\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase();\n    return methods.indexOf(upcased) > -1 ? upcased : method\n  }\n\n  function Request(input, options) {\n    if (!(this instanceof Request)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n\n    options = options || {};\n    var body = options.body;\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url;\n      this.credentials = input.credentials;\n      if (!options.headers) {\n        this.headers = new Headers(input.headers);\n      }\n      this.method = input.method;\n      this.mode = input.mode;\n      this.signal = input.signal;\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit;\n        input.bodyUsed = true;\n      }\n    } else {\n      this.url = String(input);\n    }\n\n    this.credentials = options.credentials || this.credentials || 'same-origin';\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers);\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET');\n    this.mode = options.mode || this.mode || null;\n    this.signal = options.signal || this.signal || (function () {\n      if ('AbortController' in g) {\n        var ctrl = new AbortController();\n        return ctrl.signal;\n      }\n    }());\n    this.referrer = null;\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body);\n\n    if (this.method === 'GET' || this.method === 'HEAD') {\n      if (options.cache === 'no-store' || options.cache === 'no-cache') {\n        // Search for a '_' parameter in the query string\n        var reParamSearch = /([?&])_=[^&]*/;\n        if (reParamSearch.test(this.url)) {\n          // If it already exists then set the value with the current time\n          this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime());\n        } else {\n          // Otherwise add a new '_' parameter to the end with the current time\n          var reQueryString = /\\?/;\n          this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime();\n        }\n      }\n    }\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, {body: this._bodyInit})\n  };\n\n  function decode(body) {\n    var form = new FormData();\n    body\n      .trim()\n      .split('&')\n      .forEach(function(bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers();\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n    // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n    // https://github.com/github/fetch/issues/748\n    // https://github.com/zloirock/core-js/issues/751\n    preProcessedHeaders\n      .split('\\r')\n      .map(function(header) {\n        return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n      })\n      .forEach(function(line) {\n        var parts = line.split(':');\n        var key = parts.shift().trim();\n        if (key) {\n          var value = parts.join(':').trim();\n          try {\n            headers.append(key, value);\n          } catch (error) {\n            console.warn('Response ' + error.message);\n          }\n        }\n      });\n    return headers\n  }\n\n  Body.call(Request.prototype);\n\n  function Response(bodyInit, options) {\n    if (!(this instanceof Response)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n    if (!options) {\n      options = {};\n    }\n\n    this.type = 'default';\n    this.status = options.status === undefined ? 200 : options.status;\n    if (this.status < 200 || this.status > 599) {\n      throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\")\n    }\n    this.ok = this.status >= 200 && this.status < 300;\n    this.statusText = options.statusText === undefined ? '' : '' + options.statusText;\n    this.headers = new Headers(options.headers);\n    this.url = options.url || '';\n    this._initBody(bodyInit);\n  }\n\n  Body.call(Response.prototype);\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  };\n\n  Response.error = function() {\n    var response = new Response(null, {status: 200, statusText: ''});\n    response.ok = false;\n    response.status = 0;\n    response.type = 'error';\n    return response\n  };\n\n  var redirectStatuses = [301, 302, 303, 307, 308];\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  };\n\n  exports.DOMException = g.DOMException;\n  try {\n    new exports.DOMException();\n  } catch (err) {\n    exports.DOMException = function(message, name) {\n      this.message = message;\n      this.name = name;\n      var error = Error(message);\n      this.stack = error.stack;\n    };\n    exports.DOMException.prototype = Object.create(Error.prototype);\n    exports.DOMException.prototype.constructor = exports.DOMException;\n  }\n\n  function fetch(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init);\n\n      if (request.signal && request.signal.aborted) {\n        return reject(new exports.DOMException('Aborted', 'AbortError'))\n      }\n\n      var xhr = new XMLHttpRequest();\n\n      function abortXhr() {\n        xhr.abort();\n      }\n\n      xhr.onload = function() {\n        var options = {\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        };\n        // This check if specifically for when a user fetches a file locally from the file system\n        // Only if the status is out of a normal range\n        if (request.url.indexOf('file://') === 0 && (xhr.status < 200 || xhr.status > 599)) {\n          options.status = 200;\n        } else {\n          options.status = xhr.status;\n        }\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n        var body = 'response' in xhr ? xhr.response : xhr.responseText;\n        setTimeout(function() {\n          resolve(new Response(body, options));\n        }, 0);\n      };\n\n      xhr.onerror = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.ontimeout = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request timed out'));\n        }, 0);\n      };\n\n      xhr.onabort = function() {\n        setTimeout(function() {\n          reject(new exports.DOMException('Aborted', 'AbortError'));\n        }, 0);\n      };\n\n      function fixUrl(url) {\n        try {\n          return url === '' && g.location.href ? g.location.href : url\n        } catch (e) {\n          return url\n        }\n      }\n\n      xhr.open(request.method, fixUrl(request.url), true);\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true;\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false;\n      }\n\n      if ('responseType' in xhr) {\n        if (support.blob) {\n          xhr.responseType = 'blob';\n        } else if (\n          support.arrayBuffer\n        ) {\n          xhr.responseType = 'arraybuffer';\n        }\n      }\n\n      if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers || (g.Headers && init.headers instanceof g.Headers))) {\n        var names = [];\n        Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n          names.push(normalizeName(name));\n          xhr.setRequestHeader(name, normalizeValue(init.headers[name]));\n        });\n        request.headers.forEach(function(value, name) {\n          if (names.indexOf(name) === -1) {\n            xhr.setRequestHeader(name, value);\n          }\n        });\n      } else {\n        request.headers.forEach(function(value, name) {\n          xhr.setRequestHeader(name, value);\n        });\n      }\n\n      if (request.signal) {\n        request.signal.addEventListener('abort', abortXhr);\n\n        xhr.onreadystatechange = function() {\n          // DONE (success or failure)\n          if (xhr.readyState === 4) {\n            request.signal.removeEventListener('abort', abortXhr);\n          }\n        };\n      }\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n    })\n  }\n\n  fetch.polyfill = true;\n\n  if (!g.fetch) {\n    g.fetch = fetch;\n    g.Headers = Headers;\n    g.Request = Request;\n    g.Response = Response;\n  }\n\n  exports.Headers = Headers;\n  exports.Request = Request;\n  exports.Response = Response;\n  exports.fetch = fetch;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n  return exports;\n\n})({});\n})(__globalThis__);\n// This is a ponyfill, so...\n__globalThis__.fetch.ponyfill = true;\ndelete __globalThis__.fetch.polyfill;\n// Choose between native implementation (__global__) or custom implementation (__globalThis__)\nvar ctx = __global__.fetch ? __global__ : __globalThis__;\nexports = ctx.fetch // To enable: import fetch from 'cross-fetch'\nexports.default = ctx.fetch // For TypeScript consumers without esModuleInterop.\nexports.fetch = ctx.fetch // To enable: import {fetch} from 'cross-fetch'\nexports.Headers = ctx.Headers\nexports.Request = ctx.Request\nexports.Response = ctx.Response\nmodule.exports = exports\n", "'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n", "var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar BrowserInfo = /** @class */ (function () {\n    function BrowserInfo(name, version, os) {\n        this.name = name;\n        this.version = version;\n        this.os = os;\n        this.type = 'browser';\n    }\n    return BrowserInfo;\n}());\nexport { BrowserInfo };\nvar NodeInfo = /** @class */ (function () {\n    function NodeInfo(version) {\n        this.version = version;\n        this.type = 'node';\n        this.name = 'node';\n        this.os = process.platform;\n    }\n    return NodeInfo;\n}());\nexport { NodeInfo };\nvar SearchBotDeviceInfo = /** @class */ (function () {\n    function SearchBotDeviceInfo(name, version, os, bot) {\n        this.name = name;\n        this.version = version;\n        this.os = os;\n        this.bot = bot;\n        this.type = 'bot-device';\n    }\n    return SearchBotDeviceInfo;\n}());\nexport { SearchBotDeviceInfo };\nvar BotInfo = /** @class */ (function () {\n    function BotInfo() {\n        this.type = 'bot';\n        this.bot = true; // NOTE: deprecated test name instead\n        this.name = 'bot';\n        this.version = null;\n        this.os = null;\n    }\n    return BotInfo;\n}());\nexport { BotInfo };\nvar ReactNativeInfo = /** @class */ (function () {\n    function ReactNativeInfo() {\n        this.type = 'react-native';\n        this.name = 'react-native';\n        this.version = null;\n        this.os = null;\n    }\n    return ReactNativeInfo;\n}());\nexport { ReactNativeInfo };\n// tslint:disable-next-line:max-line-length\nvar SEARCHBOX_UA_REGEX = /alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/;\nvar SEARCHBOT_OS_REGEX = /(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\\ Jeeves\\/Teoma|ia_archiver)/;\nvar REQUIRED_VERSION_PARTS = 3;\nvar userAgentRules = [\n    ['aol', /AOLShield\\/([0-9\\._]+)/],\n    ['edge', /Edge\\/([0-9\\._]+)/],\n    ['edge-ios', /EdgiOS\\/([0-9\\._]+)/],\n    ['yandexbrowser', /YaBrowser\\/([0-9\\._]+)/],\n    ['kakaotalk', /KAKAOTALK\\s([0-9\\.]+)/],\n    ['samsung', /SamsungBrowser\\/([0-9\\.]+)/],\n    ['silk', /\\bSilk\\/([0-9._-]+)\\b/],\n    ['miui', /MiuiBrowser\\/([0-9\\.]+)$/],\n    ['beaker', /BeakerBrowser\\/([0-9\\.]+)/],\n    ['edge-chromium', /EdgA?\\/([0-9\\.]+)/],\n    [\n        'chromium-webview',\n        /(?!Chrom.*OPR)wv\\).*Chrom(?:e|ium)\\/([0-9\\.]+)(:?\\s|$)/,\n    ],\n    ['chrome', /(?!Chrom.*OPR)Chrom(?:e|ium)\\/([0-9\\.]+)(:?\\s|$)/],\n    ['phantomjs', /PhantomJS\\/([0-9\\.]+)(:?\\s|$)/],\n    ['crios', /CriOS\\/([0-9\\.]+)(:?\\s|$)/],\n    ['firefox', /Firefox\\/([0-9\\.]+)(?:\\s|$)/],\n    ['fxios', /FxiOS\\/([0-9\\.]+)/],\n    ['opera-mini', /Opera Mini.*Version\\/([0-9\\.]+)/],\n    ['opera', /Opera\\/([0-9\\.]+)(?:\\s|$)/],\n    ['opera', /OPR\\/([0-9\\.]+)(:?\\s|$)/],\n    ['pie', /^Microsoft Pocket Internet Explorer\\/(\\d+\\.\\d+)$/],\n    ['pie', /^Mozilla\\/\\d\\.\\d+\\s\\(compatible;\\s(?:MSP?IE|MSInternet Explorer) (\\d+\\.\\d+);.*Windows CE.*\\)$/],\n    ['netfront', /^Mozilla\\/\\d\\.\\d+.*NetFront\\/(\\d.\\d)/],\n    ['ie', /Trident\\/7\\.0.*rv\\:([0-9\\.]+).*\\).*Gecko$/],\n    ['ie', /MSIE\\s([0-9\\.]+);.*Trident\\/[4-7].0/],\n    ['ie', /MSIE\\s(7\\.0)/],\n    ['bb10', /BB10;\\sTouch.*Version\\/([0-9\\.]+)/],\n    ['android', /Android\\s([0-9\\.]+)/],\n    ['ios', /Version\\/([0-9\\._]+).*Mobile.*Safari.*/],\n    ['safari', /Version\\/([0-9\\._]+).*Safari/],\n    ['facebook', /FB[AS]V\\/([0-9\\.]+)/],\n    ['instagram', /Instagram\\s([0-9\\.]+)/],\n    ['ios-webview', /AppleWebKit\\/([0-9\\.]+).*Mobile/],\n    ['ios-webview', /AppleWebKit\\/([0-9\\.]+).*Gecko\\)$/],\n    ['curl', /^curl\\/([0-9\\.]+)$/],\n    ['searchbot', SEARCHBOX_UA_REGEX],\n];\nvar operatingSystemRules = [\n    ['iOS', /iP(hone|od|ad)/],\n    ['Android OS', /Android/],\n    ['BlackBerry OS', /BlackBerry|BB10/],\n    ['Windows Mobile', /IEMobile/],\n    ['Amazon OS', /Kindle/],\n    ['Windows 3.11', /Win16/],\n    ['Windows 95', /(Windows 95)|(Win95)|(Windows_95)/],\n    ['Windows 98', /(Windows 98)|(Win98)/],\n    ['Windows 2000', /(Windows NT 5.0)|(Windows 2000)/],\n    ['Windows XP', /(Windows NT 5.1)|(Windows XP)/],\n    ['Windows Server 2003', /(Windows NT 5.2)/],\n    ['Windows Vista', /(Windows NT 6.0)/],\n    ['Windows 7', /(Windows NT 6.1)/],\n    ['Windows 8', /(Windows NT 6.2)/],\n    ['Windows 8.1', /(Windows NT 6.3)/],\n    ['Windows 10', /(Windows NT 10.0)/],\n    ['Windows ME', /Windows ME/],\n    ['Windows CE', /Windows CE|WinCE|Microsoft Pocket Internet Explorer/],\n    ['Open BSD', /OpenBSD/],\n    ['Sun OS', /SunOS/],\n    ['Chrome OS', /CrOS/],\n    ['Linux', /(Linux)|(X11)/],\n    ['Mac OS', /(Mac_PowerPC)|(Macintosh)/],\n    ['QNX', /QNX/],\n    ['BeOS', /BeOS/],\n    ['OS/2', /OS\\/2/],\n];\nexport function detect(userAgent) {\n    if (!!userAgent) {\n        return parseUserAgent(userAgent);\n    }\n    if (typeof document === 'undefined' &&\n        typeof navigator !== 'undefined' &&\n        navigator.product === 'ReactNative') {\n        return new ReactNativeInfo();\n    }\n    if (typeof navigator !== 'undefined') {\n        return parseUserAgent(navigator.userAgent);\n    }\n    return getNodeVersion();\n}\nfunction matchUserAgent(ua) {\n    // opted for using reduce here rather than Array#first with a regex.test call\n    // this is primarily because using the reduce we only perform the regex\n    // execution once rather than once for the test and for the exec again below\n    // probably something that needs to be benchmarked though\n    return (ua !== '' &&\n        userAgentRules.reduce(function (matched, _a) {\n            var browser = _a[0], regex = _a[1];\n            if (matched) {\n                return matched;\n            }\n            var uaMatch = regex.exec(ua);\n            return !!uaMatch && [browser, uaMatch];\n        }, false));\n}\nexport function browserName(ua) {\n    var data = matchUserAgent(ua);\n    return data ? data[0] : null;\n}\nexport function parseUserAgent(ua) {\n    var matchedRule = matchUserAgent(ua);\n    if (!matchedRule) {\n        return null;\n    }\n    var name = matchedRule[0], match = matchedRule[1];\n    if (name === 'searchbot') {\n        return new BotInfo();\n    }\n    // Do not use RegExp for split operation as some browser do not support it (See: http://blog.stevenlevithan.com/archives/cross-browser-split)\n    var versionParts = match[1] && match[1].split('.').join('_').split('_').slice(0, 3);\n    if (versionParts) {\n        if (versionParts.length < REQUIRED_VERSION_PARTS) {\n            versionParts = __spreadArray(__spreadArray([], versionParts, true), createVersionParts(REQUIRED_VERSION_PARTS - versionParts.length), true);\n        }\n    }\n    else {\n        versionParts = [];\n    }\n    var version = versionParts.join('.');\n    var os = detectOS(ua);\n    var searchBotMatch = SEARCHBOT_OS_REGEX.exec(ua);\n    if (searchBotMatch && searchBotMatch[1]) {\n        return new SearchBotDeviceInfo(name, version, os, searchBotMatch[1]);\n    }\n    return new BrowserInfo(name, version, os);\n}\nexport function detectOS(ua) {\n    for (var ii = 0, count = operatingSystemRules.length; ii < count; ii++) {\n        var _a = operatingSystemRules[ii], os = _a[0], regex = _a[1];\n        var match = regex.exec(ua);\n        if (match) {\n            return os;\n        }\n    }\n    return null;\n}\nexport function getNodeVersion() {\n    var isNode = typeof process !== 'undefined' && process.version;\n    return isNode ? new NodeInfo(process.version.slice(1)) : null;\n}\nfunction createVersionParts(count) {\n    var output = [];\n    for (var ii = 0; ii < count; ii++) {\n        output.push('0');\n    }\n    return output;\n}\n", "// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  const BASE_MAP = new Uint8Array(256)\n  for (let j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (let i = 0; i < ALPHABET.length; i++) {\n    const x = ALPHABET.charAt(i)\n    const xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  const BASE = ALPHABET.length\n  const LEADER = ALPHABET.charAt(0)\n  const FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  const iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    // eslint-disable-next-line no-empty\n    if (source instanceof Uint8Array) { } else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength)\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source)\n    }\n    if (!(source instanceof Uint8Array)) { throw new TypeError('Expected Uint8Array') }\n    if (source.length === 0) { return '' }\n    // Skip & count leading zeroes.\n    let zeroes = 0\n    let length = 0\n    let pbegin = 0\n    const pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n    // Allocate enough space in big-endian base58 representation.\n    const size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    const b58 = new Uint8Array(size)\n    // Process the bytes.\n    while (pbegin !== pend) {\n      let carry = source[pbegin]\n      // Apply \"b58 = b58 * 256 + ch\".\n      let i = 0\n      for (let it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n    // Skip leading zeroes in base58 result.\n    let it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n    // Translate the result into a string.\n    let str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return new Uint8Array() }\n    let psz = 0\n    // Skip and count leading '1's.\n    let zeroes = 0\n    let length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n    // Allocate enough space in big-endian base256 representation.\n    const size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    const b256 = new Uint8Array(size)\n    // Process the characters.\n    while (psz < source.length) {\n      // Find code of next character\n      const charCode = source.charCodeAt(psz)\n      // Base map can not be indexed using char code\n      if (charCode > 255) { return }\n      // Decode character\n      let carry = BASE_MAP[charCode]\n      // Invalid character\n      if (carry === 255) { return }\n      let i = 0\n      for (let it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n    // Skip leading zeroes in b256.\n    let it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    const vch = new Uint8Array(zeroes + (size - it4))\n    let j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    const buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode,\n    decodeUnsafe,\n    decode\n  }\n}\nexport default base\n", "import basex from 'base-x';\nvar ALPHABET = '**********************************************************';\nexport default basex(ALPHABET);\n", null, "export function alloc(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.alloc != null) {\n    return globalThis.Buffer.alloc(size);\n  }\n  return new Uint8Array(size);\n}\nexport function allocUnsafe(size = 0) {\n  if (globalThis.Buffer != null && globalThis.Buffer.allocUnsafe != null) {\n    return globalThis.Buffer.allocUnsafe(size);\n  }\n  return new Uint8Array(size);\n}", "import { allocUnsafe } from './alloc.js';\nexport function concat(arrays, length) {\n  if (!length) {\n    length = arrays.reduce((acc, curr) => acc + curr.length, 0);\n  }\n  const output = allocUnsafe(length);\n  let offset = 0;\n  for (const arr of arrays) {\n    output.set(arr, offset);\n    offset += arr.length;\n  }\n  return output;\n}", "import { from } from './base.js';\nimport {\n  fromString,\n  toString\n} from '../bytes.js';\nexport const identity = from({\n  prefix: '\\0',\n  name: 'identity',\n  encode: buf => toString(buf),\n  decode: str => fromString(str)\n});", "function base(ALPHABET, name) {\n  if (ALPHABET.length >= 255) {\n    throw new TypeError('Alphabet too long');\n  }\n  var BASE_MAP = new Uint8Array(256);\n  for (var j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255;\n  }\n  for (var i = 0; i < ALPHABET.length; i++) {\n    var x = ALPHABET.charAt(i);\n    var xc = x.charCodeAt(0);\n    if (BASE_MAP[xc] !== 255) {\n      throw new TypeError(x + ' is ambiguous');\n    }\n    BASE_MAP[xc] = i;\n  }\n  var BASE = ALPHABET.length;\n  var LEADER = ALPHABET.charAt(0);\n  var FACTOR = Math.log(BASE) / Math.log(256);\n  var iFACTOR = Math.log(256) / Math.log(BASE);\n  function encode(source) {\n    if (source instanceof Uint8Array);\n    else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength);\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source);\n    }\n    if (!(source instanceof Uint8Array)) {\n      throw new TypeError('Expected Uint8Array');\n    }\n    if (source.length === 0) {\n      return '';\n    }\n    var zeroes = 0;\n    var length = 0;\n    var pbegin = 0;\n    var pend = source.length;\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++;\n      zeroes++;\n    }\n    var size = (pend - pbegin) * iFACTOR + 1 >>> 0;\n    var b58 = new Uint8Array(size);\n    while (pbegin !== pend) {\n      var carry = source[pbegin];\n      var i = 0;\n      for (var it1 = size - 1; (carry !== 0 || i < length) && it1 !== -1; it1--, i++) {\n        carry += 256 * b58[it1] >>> 0;\n        b58[it1] = carry % BASE >>> 0;\n        carry = carry / BASE >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      pbegin++;\n    }\n    var it2 = size - length;\n    while (it2 !== size && b58[it2] === 0) {\n      it2++;\n    }\n    var str = LEADER.repeat(zeroes);\n    for (; it2 < size; ++it2) {\n      str += ALPHABET.charAt(b58[it2]);\n    }\n    return str;\n  }\n  function decodeUnsafe(source) {\n    if (typeof source !== 'string') {\n      throw new TypeError('Expected String');\n    }\n    if (source.length === 0) {\n      return new Uint8Array();\n    }\n    var psz = 0;\n    if (source[psz] === ' ') {\n      return;\n    }\n    var zeroes = 0;\n    var length = 0;\n    while (source[psz] === LEADER) {\n      zeroes++;\n      psz++;\n    }\n    var size = (source.length - psz) * FACTOR + 1 >>> 0;\n    var b256 = new Uint8Array(size);\n    while (source[psz]) {\n      var carry = BASE_MAP[source.charCodeAt(psz)];\n      if (carry === 255) {\n        return;\n      }\n      var i = 0;\n      for (var it3 = size - 1; (carry !== 0 || i < length) && it3 !== -1; it3--, i++) {\n        carry += BASE * b256[it3] >>> 0;\n        b256[it3] = carry % 256 >>> 0;\n        carry = carry / 256 >>> 0;\n      }\n      if (carry !== 0) {\n        throw new Error('Non-zero carry');\n      }\n      length = i;\n      psz++;\n    }\n    if (source[psz] === ' ') {\n      return;\n    }\n    var it4 = size - length;\n    while (it4 !== size && b256[it4] === 0) {\n      it4++;\n    }\n    var vch = new Uint8Array(zeroes + (size - it4));\n    var j = zeroes;\n    while (it4 !== size) {\n      vch[j++] = b256[it4++];\n    }\n    return vch;\n  }\n  function decode(string) {\n    var buffer = decodeUnsafe(string);\n    if (buffer) {\n      return buffer;\n    }\n    throw new Error(`Non-${ name } character`);\n  }\n  return {\n    encode: encode,\n    decodeUnsafe: decodeUnsafe,\n    decode: decode\n  };\n}\nvar src = base;\nvar _brrp__multiformats_scope_baseX = src;\nexport default _brrp__multiformats_scope_baseX;", "const empty = new Uint8Array(0);\nconst toHex = d => d.reduce((hex, byte) => hex + byte.toString(16).padStart(2, '0'), '');\nconst fromHex = hex => {\n  const hexes = hex.match(/../g);\n  return hexes ? new Uint8Array(hexes.map(b => parseInt(b, 16))) : empty;\n};\nconst equals = (aa, bb) => {\n  if (aa === bb)\n    return true;\n  if (aa.byteLength !== bb.byteLength) {\n    return false;\n  }\n  for (let ii = 0; ii < aa.byteLength; ii++) {\n    if (aa[ii] !== bb[ii]) {\n      return false;\n    }\n  }\n  return true;\n};\nconst coerce = o => {\n  if (o instanceof Uint8Array && o.constructor.name === 'Uint8Array')\n    return o;\n  if (o instanceof ArrayBuffer)\n    return new Uint8Array(o);\n  if (ArrayBuffer.isView(o)) {\n    return new Uint8Array(o.buffer, o.byteOffset, o.byteLength);\n  }\n  throw new Error('Unknown type, must be binary type');\n};\nconst isBinary = o => o instanceof ArrayBuffer || ArrayBuffer.isView(o);\nconst fromString = str => new TextEncoder().encode(str);\nconst toString = b => new TextDecoder().decode(b);\nexport {\n  equals,\n  coerce,\n  isBinary,\n  fromHex,\n  toHex,\n  fromString,\n  toString,\n  empty\n};", "import basex from '../../vendor/base-x.js';\nimport { coerce } from '../bytes.js';\nclass Encoder {\n  constructor(name, prefix, baseEncode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n  }\n  encode(bytes) {\n    if (bytes instanceof Uint8Array) {\n      return `${ this.prefix }${ this.baseEncode(bytes) }`;\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}\nclass Decoder {\n  constructor(name, prefix, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    if (prefix.codePointAt(0) === undefined) {\n      throw new Error('Invalid prefix character');\n    }\n    this.prefixCodePoint = prefix.codePointAt(0);\n    this.baseDecode = baseDecode;\n  }\n  decode(text) {\n    if (typeof text === 'string') {\n      if (text.codePointAt(0) !== this.prefixCodePoint) {\n        throw Error(`Unable to decode multibase string ${ JSON.stringify(text) }, ${ this.name } decoder only supports inputs prefixed with ${ this.prefix }`);\n      }\n      return this.baseDecode(text.slice(this.prefix.length));\n    } else {\n      throw Error('Can only multibase decode strings');\n    }\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n}\nclass ComposedDecoder {\n  constructor(decoders) {\n    this.decoders = decoders;\n  }\n  or(decoder) {\n    return or(this, decoder);\n  }\n  decode(input) {\n    const prefix = input[0];\n    const decoder = this.decoders[prefix];\n    if (decoder) {\n      return decoder.decode(input);\n    } else {\n      throw RangeError(`Unable to decode multibase string ${ JSON.stringify(input) }, only inputs prefixed with ${ Object.keys(this.decoders) } are supported`);\n    }\n  }\n}\nexport const or = (left, right) => new ComposedDecoder({\n  ...left.decoders || { [left.prefix]: left },\n  ...right.decoders || { [right.prefix]: right }\n});\nexport class Codec {\n  constructor(name, prefix, baseEncode, baseDecode) {\n    this.name = name;\n    this.prefix = prefix;\n    this.baseEncode = baseEncode;\n    this.baseDecode = baseDecode;\n    this.encoder = new Encoder(name, prefix, baseEncode);\n    this.decoder = new Decoder(name, prefix, baseDecode);\n  }\n  encode(input) {\n    return this.encoder.encode(input);\n  }\n  decode(input) {\n    return this.decoder.decode(input);\n  }\n}\nexport const from = ({name, prefix, encode, decode}) => new Codec(name, prefix, encode, decode);\nexport const baseX = ({prefix, name, alphabet}) => {\n  const {encode, decode} = basex(alphabet, name);\n  return from({\n    prefix,\n    name,\n    encode,\n    decode: text => coerce(decode(text))\n  });\n};\nconst decode = (string, alphabet, bitsPerChar, name) => {\n  const codes = {};\n  for (let i = 0; i < alphabet.length; ++i) {\n    codes[alphabet[i]] = i;\n  }\n  let end = string.length;\n  while (string[end - 1] === '=') {\n    --end;\n  }\n  const out = new Uint8Array(end * bitsPerChar / 8 | 0);\n  let bits = 0;\n  let buffer = 0;\n  let written = 0;\n  for (let i = 0; i < end; ++i) {\n    const value = codes[string[i]];\n    if (value === undefined) {\n      throw new SyntaxError(`Non-${ name } character`);\n    }\n    buffer = buffer << bitsPerChar | value;\n    bits += bitsPerChar;\n    if (bits >= 8) {\n      bits -= 8;\n      out[written++] = 255 & buffer >> bits;\n    }\n  }\n  if (bits >= bitsPerChar || 255 & buffer << 8 - bits) {\n    throw new SyntaxError('Unexpected end of data');\n  }\n  return out;\n};\nconst encode = (data, alphabet, bitsPerChar) => {\n  const pad = alphabet[alphabet.length - 1] === '=';\n  const mask = (1 << bitsPerChar) - 1;\n  let out = '';\n  let bits = 0;\n  let buffer = 0;\n  for (let i = 0; i < data.length; ++i) {\n    buffer = buffer << 8 | data[i];\n    bits += 8;\n    while (bits > bitsPerChar) {\n      bits -= bitsPerChar;\n      out += alphabet[mask & buffer >> bits];\n    }\n  }\n  if (bits) {\n    out += alphabet[mask & buffer << bitsPerChar - bits];\n  }\n  if (pad) {\n    while (out.length * bitsPerChar & 7) {\n      out += '=';\n    }\n  }\n  return out;\n};\nexport const rfc4648 = ({name, prefix, bitsPerChar, alphabet}) => {\n  return from({\n    prefix,\n    name,\n    encode(input) {\n      return encode(input, alphabet, bitsPerChar);\n    },\n    decode(input) {\n      return decode(input, alphabet, bitsPerChar, name);\n    }\n  });\n};", "import { rfc4648 } from './base.js';\nexport const base2 = rfc4648({\n  prefix: '0',\n  name: 'base2',\n  alphabet: '01',\n  bitsPerChar: 1\n});", "import { rfc4648 } from './base.js';\nexport const base8 = rfc4648({\n  prefix: '7',\n  name: 'base8',\n  alphabet: '01234567',\n  bitsPerChar: 3\n});", "import { baseX } from './base.js';\nexport const base10 = baseX({\n  prefix: '9',\n  name: 'base10',\n  alphabet: '0123456789'\n});", "import { rfc4648 } from './base.js';\nexport const base16 = rfc4648({\n  prefix: 'f',\n  name: 'base16',\n  alphabet: '0123456789abcdef',\n  bitsPerChar: 4\n});\nexport const base16upper = rfc4648({\n  prefix: 'F',\n  name: 'base16upper',\n  alphabet: '0123456789ABCDEF',\n  bitsPerChar: 4\n});", "import { rfc4648 } from './base.js';\nexport const base32 = rfc4648({\n  prefix: 'b',\n  name: 'base32',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567',\n  bitsPerChar: 5\n});\nexport const base32upper = rfc4648({\n  prefix: 'B',\n  name: 'base32upper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567',\n  bitsPerChar: 5\n});\nexport const base32pad = rfc4648({\n  prefix: 'c',\n  name: 'base32pad',\n  alphabet: 'abcdefghijklmnopqrstuvwxyz234567=',\n  bitsPerChar: 5\n});\nexport const base32padupper = rfc4648({\n  prefix: 'C',\n  name: 'base32padupper',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567=',\n  bitsPerChar: 5\n});\nexport const base32hex = rfc4648({\n  prefix: 'v',\n  name: 'base32hex',\n  alphabet: '0123456789abcdefghijklmnopqrstuv',\n  bitsPerChar: 5\n});\nexport const base32hexupper = rfc4648({\n  prefix: 'V',\n  name: 'base32hexupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV',\n  bitsPerChar: 5\n});\nexport const base32hexpad = rfc4648({\n  prefix: 't',\n  name: 'base32hexpad',\n  alphabet: '0123456789abcdefghijklmnopqrstuv=',\n  bitsPerChar: 5\n});\nexport const base32hexpadupper = rfc4648({\n  prefix: 'T',\n  name: 'base32hexpadupper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUV=',\n  bitsPerChar: 5\n});\nexport const base32z = rfc4648({\n  prefix: 'h',\n  name: 'base32z',\n  alphabet: 'ybndrfg8ejkmcpqxot1uwisza345h769',\n  bitsPerChar: 5\n});", "import { baseX } from './base.js';\nexport const base36 = baseX({\n  prefix: 'k',\n  name: 'base36',\n  alphabet: '0123456789abcdefghijklmnopqrstuvwxyz'\n});\nexport const base36upper = baseX({\n  prefix: 'K',\n  name: 'base36upper',\n  alphabet: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n});", "import { baseX } from './base.js';\nexport const base58btc = baseX({\n  name: 'base58btc',\n  prefix: 'z',\n  alphabet: '**********************************************************'\n});\nexport const base58flickr = baseX({\n  name: 'base58flickr',\n  prefix: 'Z',\n  alphabet: '**********************************************************'\n});", "import { rfc4648 } from './base.js';\nexport const base64 = rfc4648({\n  prefix: 'm',\n  name: 'base64',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/',\n  bitsPerChar: 6\n});\nexport const base64pad = rfc4648({\n  prefix: 'M',\n  name: 'base64pad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',\n  bitsPerChar: 6\n});\nexport const base64url = rfc4648({\n  prefix: 'u',\n  name: 'base64url',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_',\n  bitsPerChar: 6\n});\nexport const base64urlpad = rfc4648({\n  prefix: 'U',\n  name: 'base64urlpad',\n  alphabet: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=',\n  bitsPerChar: 6\n});", "import { from } from './base.js';\nconst alphabet = Array.from('\\uD83D\\uDE80\\uD83E\\uDE90\\u2604\\uD83D\\uDEF0\\uD83C\\uDF0C\\uD83C\\uDF11\\uD83C\\uDF12\\uD83C\\uDF13\\uD83C\\uDF14\\uD83C\\uDF15\\uD83C\\uDF16\\uD83C\\uDF17\\uD83C\\uDF18\\uD83C\\uDF0D\\uD83C\\uDF0F\\uD83C\\uDF0E\\uD83D\\uDC09\\u2600\\uD83D\\uDCBB\\uD83D\\uDDA5\\uD83D\\uDCBE\\uD83D\\uDCBF\\uD83D\\uDE02\\u2764\\uD83D\\uDE0D\\uD83E\\uDD23\\uD83D\\uDE0A\\uD83D\\uDE4F\\uD83D\\uDC95\\uD83D\\uDE2D\\uD83D\\uDE18\\uD83D\\uDC4D\\uD83D\\uDE05\\uD83D\\uDC4F\\uD83D\\uDE01\\uD83D\\uDD25\\uD83E\\uDD70\\uD83D\\uDC94\\uD83D\\uDC96\\uD83D\\uDC99\\uD83D\\uDE22\\uD83E\\uDD14\\uD83D\\uDE06\\uD83D\\uDE44\\uD83D\\uDCAA\\uD83D\\uDE09\\u263A\\uD83D\\uDC4C\\uD83E\\uDD17\\uD83D\\uDC9C\\uD83D\\uDE14\\uD83D\\uDE0E\\uD83D\\uDE07\\uD83C\\uDF39\\uD83E\\uDD26\\uD83C\\uDF89\\uD83D\\uDC9E\\u270C\\u2728\\uD83E\\uDD37\\uD83D\\uDE31\\uD83D\\uDE0C\\uD83C\\uDF38\\uD83D\\uDE4C\\uD83D\\uDE0B\\uD83D\\uDC97\\uD83D\\uDC9A\\uD83D\\uDE0F\\uD83D\\uDC9B\\uD83D\\uDE42\\uD83D\\uDC93\\uD83E\\uDD29\\uD83D\\uDE04\\uD83D\\uDE00\\uD83D\\uDDA4\\uD83D\\uDE03\\uD83D\\uDCAF\\uD83D\\uDE48\\uD83D\\uDC47\\uD83C\\uDFB6\\uD83D\\uDE12\\uD83E\\uDD2D\\u2763\\uD83D\\uDE1C\\uD83D\\uDC8B\\uD83D\\uDC40\\uD83D\\uDE2A\\uD83D\\uDE11\\uD83D\\uDCA5\\uD83D\\uDE4B\\uD83D\\uDE1E\\uD83D\\uDE29\\uD83D\\uDE21\\uD83E\\uDD2A\\uD83D\\uDC4A\\uD83E\\uDD73\\uD83D\\uDE25\\uD83E\\uDD24\\uD83D\\uDC49\\uD83D\\uDC83\\uD83D\\uDE33\\u270B\\uD83D\\uDE1A\\uD83D\\uDE1D\\uD83D\\uDE34\\uD83C\\uDF1F\\uD83D\\uDE2C\\uD83D\\uDE43\\uD83C\\uDF40\\uD83C\\uDF37\\uD83D\\uDE3B\\uD83D\\uDE13\\u2B50\\u2705\\uD83E\\uDD7A\\uD83C\\uDF08\\uD83D\\uDE08\\uD83E\\uDD18\\uD83D\\uDCA6\\u2714\\uD83D\\uDE23\\uD83C\\uDFC3\\uD83D\\uDC90\\u2639\\uD83C\\uDF8A\\uD83D\\uDC98\\uD83D\\uDE20\\u261D\\uD83D\\uDE15\\uD83C\\uDF3A\\uD83C\\uDF82\\uD83C\\uDF3B\\uD83D\\uDE10\\uD83D\\uDD95\\uD83D\\uDC9D\\uD83D\\uDE4A\\uD83D\\uDE39\\uD83D\\uDDE3\\uD83D\\uDCAB\\uD83D\\uDC80\\uD83D\\uDC51\\uD83C\\uDFB5\\uD83E\\uDD1E\\uD83D\\uDE1B\\uD83D\\uDD34\\uD83D\\uDE24\\uD83C\\uDF3C\\uD83D\\uDE2B\\u26BD\\uD83E\\uDD19\\u2615\\uD83C\\uDFC6\\uD83E\\uDD2B\\uD83D\\uDC48\\uD83D\\uDE2E\\uD83D\\uDE46\\uD83C\\uDF7B\\uD83C\\uDF43\\uD83D\\uDC36\\uD83D\\uDC81\\uD83D\\uDE32\\uD83C\\uDF3F\\uD83E\\uDDE1\\uD83C\\uDF81\\u26A1\\uD83C\\uDF1E\\uD83C\\uDF88\\u274C\\u270A\\uD83D\\uDC4B\\uD83D\\uDE30\\uD83E\\uDD28\\uD83D\\uDE36\\uD83E\\uDD1D\\uD83D\\uDEB6\\uD83D\\uDCB0\\uD83C\\uDF53\\uD83D\\uDCA2\\uD83E\\uDD1F\\uD83D\\uDE41\\uD83D\\uDEA8\\uD83D\\uDCA8\\uD83E\\uDD2C\\u2708\\uD83C\\uDF80\\uD83C\\uDF7A\\uD83E\\uDD13\\uD83D\\uDE19\\uD83D\\uDC9F\\uD83C\\uDF31\\uD83D\\uDE16\\uD83D\\uDC76\\uD83E\\uDD74\\u25B6\\u27A1\\u2753\\uD83D\\uDC8E\\uD83D\\uDCB8\\u2B07\\uD83D\\uDE28\\uD83C\\uDF1A\\uD83E\\uDD8B\\uD83D\\uDE37\\uD83D\\uDD7A\\u26A0\\uD83D\\uDE45\\uD83D\\uDE1F\\uD83D\\uDE35\\uD83D\\uDC4E\\uD83E\\uDD32\\uD83E\\uDD20\\uD83E\\uDD27\\uD83D\\uDCCC\\uD83D\\uDD35\\uD83D\\uDC85\\uD83E\\uDDD0\\uD83D\\uDC3E\\uD83C\\uDF52\\uD83D\\uDE17\\uD83E\\uDD11\\uD83C\\uDF0A\\uD83E\\uDD2F\\uD83D\\uDC37\\u260E\\uD83D\\uDCA7\\uD83D\\uDE2F\\uD83D\\uDC86\\uD83D\\uDC46\\uD83C\\uDFA4\\uD83D\\uDE47\\uD83C\\uDF51\\u2744\\uD83C\\uDF34\\uD83D\\uDCA3\\uD83D\\uDC38\\uD83D\\uDC8C\\uD83D\\uDCCD\\uD83E\\uDD40\\uD83E\\uDD22\\uD83D\\uDC45\\uD83D\\uDCA1\\uD83D\\uDCA9\\uD83D\\uDC50\\uD83D\\uDCF8\\uD83D\\uDC7B\\uD83E\\uDD10\\uD83E\\uDD2E\\uD83C\\uDFBC\\uD83E\\uDD75\\uD83D\\uDEA9\\uD83C\\uDF4E\\uD83C\\uDF4A\\uD83D\\uDC7C\\uD83D\\uDC8D\\uD83D\\uDCE3\\uD83E\\uDD42');\nconst alphabetBytesToChars = alphabet.reduce((p, c, i) => {\n  p[i] = c;\n  return p;\n}, []);\nconst alphabetCharsToBytes = alphabet.reduce((p, c, i) => {\n  p[c.codePointAt(0)] = i;\n  return p;\n}, []);\nfunction encode(data) {\n  return data.reduce((p, c) => {\n    p += alphabetBytesToChars[c];\n    return p;\n  }, '');\n}\nfunction decode(str) {\n  const byts = [];\n  for (const char of str) {\n    const byt = alphabetCharsToBytes[char.codePointAt(0)];\n    if (byt === undefined) {\n      throw new Error(`Non-base256emoji character: ${ char }`);\n    }\n    byts.push(byt);\n  }\n  return new Uint8Array(byts);\n}\nexport const base256emoji = from({\n  prefix: '\\uD83D\\uDE80',\n  name: 'base256emoji',\n  encode,\n  decode\n});", "import { from } from './hasher.js';\nconst sha = name => async data => new Uint8Array(await crypto.subtle.digest(name, data));\nexport const sha256 = from({\n  name: 'sha2-256',\n  code: 18,\n  encode: sha('SHA-256')\n});\nexport const sha512 = from({\n  name: 'sha2-512',\n  code: 19,\n  encode: sha('SHA-512')\n});", "var encode_1 = encode;\nvar MSB = 128, REST = 127, MSBALL = ~REST, INT = Math.pow(2, 31);\nfunction encode(num, out, offset) {\n  out = out || [];\n  offset = offset || 0;\n  var oldOffset = offset;\n  while (num >= INT) {\n    out[offset++] = num & 255 | MSB;\n    num /= 128;\n  }\n  while (num & MSBALL) {\n    out[offset++] = num & 255 | MSB;\n    num >>>= 7;\n  }\n  out[offset] = num | 0;\n  encode.bytes = offset - oldOffset + 1;\n  return out;\n}\nvar decode = read;\nvar MSB$1 = 128, REST$1 = 127;\nfunction read(buf, offset) {\n  var res = 0, offset = offset || 0, shift = 0, counter = offset, b, l = buf.length;\n  do {\n    if (counter >= l) {\n      read.bytes = 0;\n      throw new RangeError('Could not decode varint');\n    }\n    b = buf[counter++];\n    res += shift < 28 ? (b & REST$1) << shift : (b & REST$1) * Math.pow(2, shift);\n    shift += 7;\n  } while (b >= MSB$1);\n  read.bytes = counter - offset;\n  return res;\n}\nvar N1 = Math.pow(2, 7);\nvar N2 = Math.pow(2, 14);\nvar N3 = Math.pow(2, 21);\nvar N4 = Math.pow(2, 28);\nvar N5 = Math.pow(2, 35);\nvar N6 = Math.pow(2, 42);\nvar N7 = Math.pow(2, 49);\nvar N8 = Math.pow(2, 56);\nvar N9 = Math.pow(2, 63);\nvar length = function (value) {\n  return value < N1 ? 1 : value < N2 ? 2 : value < N3 ? 3 : value < N4 ? 4 : value < N5 ? 5 : value < N6 ? 6 : value < N7 ? 7 : value < N8 ? 8 : value < N9 ? 9 : 10;\n};\nvar varint = {\n  encode: encode_1,\n  decode: decode,\n  encodingLength: length\n};\nvar _brrp_varint = varint;\nexport default _brrp_varint;", "import varint from '../vendor/varint.js';\nexport const decode = (data, offset = 0) => {\n  const code = varint.decode(data, offset);\n  return [\n    code,\n    varint.decode.bytes\n  ];\n};\nexport const encodeTo = (int, target, offset = 0) => {\n  varint.encode(int, target, offset);\n  return target;\n};\nexport const encodingLength = int => {\n  return varint.encodingLength(int);\n};", "import {\n  coerce,\n  equals as equalBytes\n} from '../bytes.js';\nimport * as varint from '../varint.js';\nexport const create = (code, digest) => {\n  const size = digest.byteLength;\n  const sizeOffset = varint.encodingLength(code);\n  const digestOffset = sizeOffset + varint.encodingLength(size);\n  const bytes = new Uint8Array(digestOffset + size);\n  varint.encodeTo(code, bytes, 0);\n  varint.encodeTo(size, bytes, sizeOffset);\n  bytes.set(digest, digestOffset);\n  return new Digest(code, size, digest, bytes);\n};\nexport const decode = multihash => {\n  const bytes = coerce(multihash);\n  const [code, sizeOffset] = varint.decode(bytes);\n  const [size, digestOffset] = varint.decode(bytes.subarray(sizeOffset));\n  const digest = bytes.subarray(sizeOffset + digestOffset);\n  if (digest.byteLength !== size) {\n    throw new Error('Incorrect length');\n  }\n  return new Digest(code, size, digest, bytes);\n};\nexport const equals = (a, b) => {\n  if (a === b) {\n    return true;\n  } else {\n    return a.code === b.code && a.size === b.size && equalBytes(a.bytes, b.bytes);\n  }\n};\nexport class Digest {\n  constructor(code, size, digest, bytes) {\n    this.code = code;\n    this.size = size;\n    this.digest = digest;\n    this.bytes = bytes;\n  }\n}", "import * as Digest from './digest.js';\nexport const from = ({name, code, encode}) => new Hasher(name, code, encode);\nexport class Hasher {\n  constructor(name, code, encode) {\n    this.name = name;\n    this.code = code;\n    this.encode = encode;\n  }\n  digest(input) {\n    if (input instanceof Uint8Array) {\n      const result = this.encode(input);\n      return result instanceof Uint8Array ? Digest.create(this.code, result) : result.then(digest => Digest.create(this.code, digest));\n    } else {\n      throw Error('Unknown type, must be binary type');\n    }\n  }\n}", "import { coerce } from '../bytes.js';\nimport * as Digest from './digest.js';\nconst code = 0;\nconst name = 'identity';\nconst encode = coerce;\nconst digest = input => Digest.create(code, encode(input));\nexport const identity = {\n  code,\n  name,\n  encode,\n  digest\n};", "const textEncoder = new TextEncoder();\nconst textDecoder = new TextDecoder();\nexport const name = 'json';\nexport const code = 512;\nexport const encode = node => textEncoder.encode(JSON.stringify(node));\nexport const decode = data => JSON.parse(textDecoder.decode(data));", "import * as varint from './varint.js';\nimport * as Digest from './hashes/digest.js';\nimport { base58btc } from './bases/base58.js';\nimport { base32 } from './bases/base32.js';\nimport { coerce } from './bytes.js';\nexport class CID {\n  constructor(version, code, multihash, bytes) {\n    this.code = code;\n    this.version = version;\n    this.multihash = multihash;\n    this.bytes = bytes;\n    this.byteOffset = bytes.byteOffset;\n    this.byteLength = bytes.byteLength;\n    this.asCID = this;\n    this._baseCache = new Map();\n    Object.defineProperties(this, {\n      byteOffset: hidden,\n      byteLength: hidden,\n      code: readonly,\n      version: readonly,\n      multihash: readonly,\n      bytes: readonly,\n      _baseCache: hidden,\n      asCID: hidden\n    });\n  }\n  toV0() {\n    switch (this.version) {\n    case 0: {\n        return this;\n      }\n    default: {\n        const {code, multihash} = this;\n        if (code !== DAG_PB_CODE) {\n          throw new Error('Cannot convert a non dag-pb CID to CIDv0');\n        }\n        if (multihash.code !== SHA_256_CODE) {\n          throw new Error('Cannot convert non sha2-256 multihash CID to CIDv0');\n        }\n        return CID.createV0(multihash);\n      }\n    }\n  }\n  toV1() {\n    switch (this.version) {\n    case 0: {\n        const {code, digest} = this.multihash;\n        const multihash = Digest.create(code, digest);\n        return CID.createV1(this.code, multihash);\n      }\n    case 1: {\n        return this;\n      }\n    default: {\n        throw Error(`Can not convert CID version ${ this.version } to version 0. This is a bug please report`);\n      }\n    }\n  }\n  equals(other) {\n    return other && this.code === other.code && this.version === other.version && Digest.equals(this.multihash, other.multihash);\n  }\n  toString(base) {\n    const {bytes, version, _baseCache} = this;\n    switch (version) {\n    case 0:\n      return toStringV0(bytes, _baseCache, base || base58btc.encoder);\n    default:\n      return toStringV1(bytes, _baseCache, base || base32.encoder);\n    }\n  }\n  toJSON() {\n    return {\n      code: this.code,\n      version: this.version,\n      hash: this.multihash.bytes\n    };\n  }\n  get [Symbol.toStringTag]() {\n    return 'CID';\n  }\n  [Symbol.for('nodejs.util.inspect.custom')]() {\n    return 'CID(' + this.toString() + ')';\n  }\n  static isCID(value) {\n    deprecate(/^0\\.0/, IS_CID_DEPRECATION);\n    return !!(value && (value[cidSymbol] || value.asCID === value));\n  }\n  get toBaseEncodedString() {\n    throw new Error('Deprecated, use .toString()');\n  }\n  get codec() {\n    throw new Error('\"codec\" property is deprecated, use integer \"code\" property instead');\n  }\n  get buffer() {\n    throw new Error('Deprecated .buffer property, use .bytes to get Uint8Array instead');\n  }\n  get multibaseName() {\n    throw new Error('\"multibaseName\" property is deprecated');\n  }\n  get prefix() {\n    throw new Error('\"prefix\" property is deprecated');\n  }\n  static asCID(value) {\n    if (value instanceof CID) {\n      return value;\n    } else if (value != null && value.asCID === value) {\n      const {version, code, multihash, bytes} = value;\n      return new CID(version, code, multihash, bytes || encodeCID(version, code, multihash.bytes));\n    } else if (value != null && value[cidSymbol] === true) {\n      const {version, multihash, code} = value;\n      const digest = Digest.decode(multihash);\n      return CID.create(version, code, digest);\n    } else {\n      return null;\n    }\n  }\n  static create(version, code, digest) {\n    if (typeof code !== 'number') {\n      throw new Error('String codecs are no longer supported');\n    }\n    switch (version) {\n    case 0: {\n        if (code !== DAG_PB_CODE) {\n          throw new Error(`Version 0 CID must use dag-pb (code: ${ DAG_PB_CODE }) block encoding`);\n        } else {\n          return new CID(version, code, digest, digest.bytes);\n        }\n      }\n    case 1: {\n        const bytes = encodeCID(version, code, digest.bytes);\n        return new CID(version, code, digest, bytes);\n      }\n    default: {\n        throw new Error('Invalid version');\n      }\n    }\n  }\n  static createV0(digest) {\n    return CID.create(0, DAG_PB_CODE, digest);\n  }\n  static createV1(code, digest) {\n    return CID.create(1, code, digest);\n  }\n  static decode(bytes) {\n    const [cid, remainder] = CID.decodeFirst(bytes);\n    if (remainder.length) {\n      throw new Error('Incorrect length');\n    }\n    return cid;\n  }\n  static decodeFirst(bytes) {\n    const specs = CID.inspectBytes(bytes);\n    const prefixSize = specs.size - specs.multihashSize;\n    const multihashBytes = coerce(bytes.subarray(prefixSize, prefixSize + specs.multihashSize));\n    if (multihashBytes.byteLength !== specs.multihashSize) {\n      throw new Error('Incorrect length');\n    }\n    const digestBytes = multihashBytes.subarray(specs.multihashSize - specs.digestSize);\n    const digest = new Digest.Digest(specs.multihashCode, specs.digestSize, digestBytes, multihashBytes);\n    const cid = specs.version === 0 ? CID.createV0(digest) : CID.createV1(specs.codec, digest);\n    return [\n      cid,\n      bytes.subarray(specs.size)\n    ];\n  }\n  static inspectBytes(initialBytes) {\n    let offset = 0;\n    const next = () => {\n      const [i, length] = varint.decode(initialBytes.subarray(offset));\n      offset += length;\n      return i;\n    };\n    let version = next();\n    let codec = DAG_PB_CODE;\n    if (version === 18) {\n      version = 0;\n      offset = 0;\n    } else if (version === 1) {\n      codec = next();\n    }\n    if (version !== 0 && version !== 1) {\n      throw new RangeError(`Invalid CID version ${ version }`);\n    }\n    const prefixSize = offset;\n    const multihashCode = next();\n    const digestSize = next();\n    const size = offset + digestSize;\n    const multihashSize = size - prefixSize;\n    return {\n      version,\n      codec,\n      multihashCode,\n      digestSize,\n      multihashSize,\n      size\n    };\n  }\n  static parse(source, base) {\n    const [prefix, bytes] = parseCIDtoBytes(source, base);\n    const cid = CID.decode(bytes);\n    cid._baseCache.set(prefix, source);\n    return cid;\n  }\n}\nconst parseCIDtoBytes = (source, base) => {\n  switch (source[0]) {\n  case 'Q': {\n      const decoder = base || base58btc;\n      return [\n        base58btc.prefix,\n        decoder.decode(`${ base58btc.prefix }${ source }`)\n      ];\n    }\n  case base58btc.prefix: {\n      const decoder = base || base58btc;\n      return [\n        base58btc.prefix,\n        decoder.decode(source)\n      ];\n    }\n  case base32.prefix: {\n      const decoder = base || base32;\n      return [\n        base32.prefix,\n        decoder.decode(source)\n      ];\n    }\n  default: {\n      if (base == null) {\n        throw Error('To parse non base32 or base58btc encoded CID multibase decoder must be provided');\n      }\n      return [\n        source[0],\n        base.decode(source)\n      ];\n    }\n  }\n};\nconst toStringV0 = (bytes, cache, base) => {\n  const {prefix} = base;\n  if (prefix !== base58btc.prefix) {\n    throw Error(`Cannot string encode V0 in ${ base.name } encoding`);\n  }\n  const cid = cache.get(prefix);\n  if (cid == null) {\n    const cid = base.encode(bytes).slice(1);\n    cache.set(prefix, cid);\n    return cid;\n  } else {\n    return cid;\n  }\n};\nconst toStringV1 = (bytes, cache, base) => {\n  const {prefix} = base;\n  const cid = cache.get(prefix);\n  if (cid == null) {\n    const cid = base.encode(bytes);\n    cache.set(prefix, cid);\n    return cid;\n  } else {\n    return cid;\n  }\n};\nconst DAG_PB_CODE = 112;\nconst SHA_256_CODE = 18;\nconst encodeCID = (version, code, multihash) => {\n  const codeOffset = varint.encodingLength(version);\n  const hashOffset = codeOffset + varint.encodingLength(code);\n  const bytes = new Uint8Array(hashOffset + multihash.byteLength);\n  varint.encodeTo(version, bytes, 0);\n  varint.encodeTo(code, bytes, codeOffset);\n  bytes.set(multihash, hashOffset);\n  return bytes;\n};\nconst cidSymbol = Symbol.for('@ipld/js-cid/CID');\nconst readonly = {\n  writable: false,\n  configurable: false,\n  enumerable: true\n};\nconst hidden = {\n  writable: false,\n  enumerable: false,\n  configurable: false\n};\nconst version = '0.0.0-dev';\nconst deprecate = (range, message) => {\n  if (range.test(version)) {\n    console.warn(message);\n  } else {\n    throw new Error(message);\n  }\n};\nconst IS_CID_DEPRECATION = `CID.isCID(v) is deprecated and will be removed in the next major release.\nFollowing code pattern:\n\nif (CID.isCID(value)) {\n  doSomethingWithCID(value)\n}\n\nIs replaced with:\n\nconst cid = CID.asCID(value)\nif (cid) {\n  // Make sure to use cid instead of value\n  doSomethingWithCID(cid)\n}\n`;", "import * as identityBase from './bases/identity.js';\nimport * as base2 from './bases/base2.js';\nimport * as base8 from './bases/base8.js';\nimport * as base10 from './bases/base10.js';\nimport * as base16 from './bases/base16.js';\nimport * as base32 from './bases/base32.js';\nimport * as base36 from './bases/base36.js';\nimport * as base58 from './bases/base58.js';\nimport * as base64 from './bases/base64.js';\nimport * as base256emoji from './bases/base256emoji.js';\nimport * as sha2 from './hashes/sha2.js';\nimport * as identity from './hashes/identity.js';\nimport * as raw from './codecs/raw.js';\nimport * as json from './codecs/json.js';\nimport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes\n} from './index.js';\nconst bases = {\n  ...identityBase,\n  ...base2,\n  ...base8,\n  ...base10,\n  ...base16,\n  ...base32,\n  ...base36,\n  ...base58,\n  ...base64,\n  ...base256emoji\n};\nconst hashes = {\n  ...sha2,\n  ...identity\n};\nconst codecs = {\n  raw,\n  json\n};\nexport {\n  CID,\n  hasher,\n  digest,\n  varint,\n  bytes,\n  hashes,\n  bases,\n  codecs\n};", "import { bases } from 'multiformats/basics';\nimport { allocUnsafe } from '../alloc.js';\nfunction createCodec(name, prefix, encode, decode) {\n  return {\n    name,\n    prefix,\n    encoder: {\n      name,\n      prefix,\n      encode\n    },\n    decoder: { decode }\n  };\n}\nconst string = createCodec('utf8', 'u', buf => {\n  const decoder = new TextDecoder('utf8');\n  return 'u' + decoder.decode(buf);\n}, str => {\n  const encoder = new TextEncoder();\n  return encoder.encode(str.substring(1));\n});\nconst ascii = createCodec('ascii', 'a', buf => {\n  let string = 'a';\n  for (let i = 0; i < buf.length; i++) {\n    string += String.fromCharCode(buf[i]);\n  }\n  return string;\n}, str => {\n  str = str.substring(1);\n  const buf = allocUnsafe(str.length);\n  for (let i = 0; i < str.length; i++) {\n    buf[i] = str.charCodeAt(i);\n  }\n  return buf;\n});\nconst BASES = {\n  utf8: string,\n  'utf-8': string,\n  hex: bases.base16,\n  latin1: ascii,\n  ascii: ascii,\n  binary: ascii,\n  ...bases\n};\nexport default BASES;", "import bases from './util/bases.js';\nexport function fromString(string, encoding = 'utf8') {\n  const base = bases[encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return globalThis.Buffer.from(string, 'utf8');\n  }\n  return base.decoder.decode(`${ base.prefix }${ string }`);\n}", "import bases from './util/bases.js';\nexport function toString(array, encoding = 'utf8') {\n  const base = bases[encoding];\n  if (!base) {\n    throw new Error(`Unsupported encoding \"${ encoding }\"`);\n  }\n  if ((encoding === 'utf8' || encoding === 'utf-8') && globalThis.Buffer != null && globalThis.Buffer.from != null) {\n    return globalThis.Buffer.from(array.buffer, array.byteOffset, array.byteLength).toString('utf8');\n  }\n  return base.encoder.encode(array).substring(1);\n}", "export function assertType(obj: any, key: string, type = \"string\") {\n  if (!obj[key] || typeof obj[key] !== type) {\n    throw new Error(`Missing or invalid \"${key}\" param`);\n  }\n}\n\nexport function hasRequiredParams(params: any, required: string[]) {\n  let matches = true;\n  required.forEach((key) => {\n    const exists = key in params;\n    if (!exists) {\n      matches = false;\n    }\n  });\n  return matches;\n}\n\nexport function hasExactParamsLength(params: any, length: number): boolean {\n  return Array.isArray(params) ? params.length === length : Object.keys(params).length === length;\n}\n\nexport function hasRequiredParamsLength(params: any, minLength: number): boolean {\n  return Array.isArray(params)\n    ? params.length >= minLength\n    : Object.keys(params).length >= minLength;\n}\n\nexport function checkParams(params: any, required: string[], optional: string[]) {\n  const exact = !optional.length;\n  const matchesLength = exact\n    ? hasExactParamsLength(params, required.length)\n    : hasRequiredParamsLength(params, required.length);\n  if (!matchesLength) return false;\n  return hasRequiredParams(params, required);\n}\n\nexport function methodEndsWith(method: string, expected: string, separator = \"_\") {\n  const split = method.split(separator);\n  return split[split.length - 1].trim().toLowerCase() === expected.trim().toLowerCase();\n}\n", "import { JsonRpcRequest } from \"@walletconnect/jsonrpc-types\";\n\nimport { checkParams, methodEndsWith } from \"./misc\";\nimport { RelayJsonRpc } from \"./types\";\n\n// ---------- Subscribe ----------------------------------------------- //\n\nexport function isSubscribeRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.SubscribeParams> {\n  return isSubscribeMethod(request.method) && isSubscribeParams(request.params);\n}\n\nexport function isSubscribeMethod(method: string): boolean {\n  return methodEndsWith(method, \"subscribe\");\n}\n\nexport function isSubscribeParams(params: any): params is RelayJsonRpc.SubscribeParams {\n  const required = [\"topic\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Publish ----------------------------------------------- //\n\nexport function isPublishRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.PublishParams> {\n  return isPublishMethod(request.method) && isPublishParams(request.params);\n}\n\nexport function isPublishMethod(method: string): boolean {\n  return methodEndsWith(method, \"publish\");\n}\n\nexport function isPublishParams(params: any): params is RelayJsonRpc.PublishParams {\n  const required = [\"message\", \"topic\", \"ttl\"];\n  const optional = [\"prompt\", \"tag\"];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Unsubscribe ----------------------------------------------- //\n\nexport function isUnsubscribeRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.UnsubscribeParams> {\n  return isUnsubscribeMethod(request.method) && isUnsubscribeParams(request.params);\n}\n\nexport function isUnsubscribeMethod(method: string): boolean {\n  return methodEndsWith(method, \"unsubscribe\");\n}\n\nexport function isUnsubscribeParams(params: any): params is RelayJsonRpc.UnsubscribeParams {\n  const required = [\"id\", \"topic\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n\n// ---------- Subscription ----------------------------------------------- //\n\nexport function isSubscriptionRequest(\n  request: JsonRpcRequest,\n): request is JsonRpcRequest<RelayJsonRpc.SubscriptionParams> {\n  return isSubscriptionMethod(request.method) && isSubscriptionParams(request.params);\n}\n\nexport function isSubscriptionMethod(method: string): boolean {\n  return methodEndsWith(method, \"subscription\");\n}\n\nexport function isSubscriptionParams(params: any): params is RelayJsonRpc.SubscriptionParams {\n  const required = [\"id\", \"data\"];\n  const optional: string[] = [];\n  return checkParams(params, required, optional);\n}\n", "import { JsonRpcRequest } from \"@walletconnect/jsonrpc-types\";\n\nimport { RelayJsonRpc } from \"./types\";\nimport { assertType } from \"./misc\";\nimport {\n  isPublishMethod,\n  isPublishParams,\n  isSubscribeMethod,\n  isSubscribeParams,\n  isSubscriptionMethod,\n  isSubscriptionParams,\n  isUnsubscribeMethod,\n  isUnsubscribeParams,\n} from \"./validators\";\n\nexport function parseSubscribeRequest(request: JsonRpcRequest): RelayJsonRpc.SubscribeParams {\n  if (!isSubscribeMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid subscribe method\");\n  }\n  if (!isSubscribeParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid subscribe params\");\n  }\n  const params = request.params as RelayJsonRpc.SubscribeParams;\n\n  assertType(params, \"topic\");\n\n  return params;\n}\n\nexport function parsePublishRequest(request: JsonRpcRequest): RelayJsonRpc.PublishParams {\n  if (!isPublishMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid publish method\");\n  }\n  if (!isPublishParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid publish params\");\n  }\n  const params = request.params as RelayJsonRpc.PublishParams;\n\n  assertType(params, \"topic\");\n  assertType(params, \"message\");\n  assertType(params, \"ttl\", \"number\");\n\n  return params;\n}\n\nexport function parseUnsubscribeRequest(request: JsonRpcRequest): RelayJsonRpc.UnsubscribeParams {\n  if (!isUnsubscribeMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid unsubscribe method\");\n  }\n  if (!isUnsubscribeParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid unsubscribe params\");\n  }\n  const params = request.params as RelayJsonRpc.UnsubscribeParams;\n\n  assertType(params, \"id\");\n\n  return params;\n}\n\nexport function parseSubscriptionRequest(request: JsonRpcRequest): RelayJsonRpc.SubscriptionParams {\n  if (!isSubscriptionMethod(request.method)) {\n    throw new Error(\"JSON-RPC Request has invalid subscription method\");\n  }\n  if (!isSubscriptionParams(request.params)) {\n    throw new Error(\"JSON-RPC Request has invalid subscription params\");\n  }\n  const params = request.params as RelayJsonRpc.SubscriptionParams;\n\n  assertType(params, \"id\");\n  assertType(params, \"data\");\n\n  return params;\n}\n", "import { RelayJsonRpc } from \"./types\";\n\nexport const RELAY_JSONRPC: { [protocol: string]: RelayJsonRpc.Methods } = {\n  waku: {\n    publish: \"waku_publish\",\n    batchPublish: \"waku_batchPublish\",\n    subscribe: \"waku_subscribe\",\n    batchSubscribe: \"waku_batchSubscribe\",\n    subscription: \"waku_subscription\",\n    unsubscribe: \"waku_unsubscribe\",\n    batchUnsubscribe: \"waku_batchUnsubscribe\",\n    batchFetchMessages: \"waku_batchFetchMessages\",\n  },\n  irn: {\n    publish: \"irn_publish\",\n    batchPublish: \"irn_batchPublish\",\n    subscribe: \"irn_subscribe\",\n    batchSubscribe: \"irn_batchSubscribe\",\n    subscription: \"irn_subscription\",\n    unsubscribe: \"irn_unsubscribe\",\n    batchUnsubscribe: \"irn_batchUnsubscribe\",\n    batchFetchMessages: \"irn_batchFetchMessages\",\n  },\n  iridium: {\n    publish: \"iridium_publish\",\n    batchPublish: \"iridium_batchPublish\",\n    subscribe: \"iridium_subscribe\",\n    batchSubscribe: \"iridium_batchSubscribe\",\n    subscription: \"iridium_subscription\",\n    unsubscribe: \"iridium_unsubscribe\",\n    batchUnsubscribe: \"iridium_batchUnsubscribe\",\n    batchFetchMessages: \"iridium_batchFetchMessages\",\n  },\n};\n", null, null, null, null, null, null, "import { EventEmitter } from \"events\";\n\nexport abstract class IEvents {\n  public abstract events: EventEmitter;\n\n  // events\n  public abstract on(event: string, listener: any): void;\n  public abstract once(event: string, listener: any): void;\n  public abstract off(event: string, listener: any): void;\n  public abstract removeListener(event: string, listener: any): void;\n}\n", "import { JsonRpcPayload, JsonRpcRequest, RequestArguments } from \"./jsonrpc\";\nimport { IEvents } from \"./misc\";\n\nexport abstract class IJsonRpcConnection extends IEvents {\n  public abstract connected: boolean;\n  public abstract connecting: boolean;\n  // @ts-ignore - opts is not used in abstract class constructor\n  constructor(opts?: any) {\n    super();\n  }\n\n  public abstract open(opts?: any): Promise<void>;\n  public abstract close(): Promise<void>;\n  public abstract send(payload: JsonRpcPayload, context?: any): Promise<void>;\n}\n\nexport abstract class IBaseJsonRpcProvider extends IEvents {\n  // eslint-disable-next-line no-useless-constructor\n  constructor() {\n    super();\n  }\n\n  public abstract connect(params?: any): Promise<void>;\n\n  public abstract disconnect(): Promise<void>;\n\n  public abstract request<Result = any, Params = any>(\n    request: RequestArguments<Params>,\n    context?: any,\n  ): Promise<Result>;\n\n  // ---------- Protected ----------------------------------------------- //\n\n  protected abstract requestStrict<Result = any, Params = any>(\n    request: JsonRpcRequest<Params>,\n    context?: any,\n  ): Promise<Result>;\n}\n\nexport abstract class IJsonRpcProvider extends IBaseJsonRpcProvider {\n  public abstract connection: IJsonRpcConnection;\n\n  // @ts-ignore - connection is not used in abstract class constructor\n  constructor(connection: string | IJsonRpcConnection) {\n    super();\n  }\n\n  public abstract connect(connection?: string | IJsonRpcConnection): Promise<void>;\n\n  // ---------- Protected ----------------------------------------------- //\n\n  protected abstract setConnection(connection?: string | IJsonRpcConnection): IJsonRpcConnection;\n\n  protected abstract onPayload(payload: JsonRpcPayload): void;\n\n  protected abstract open(connection?: string | IJsonRpcConnection): Promise<void>;\n\n  protected abstract close(): Promise<void>;\n}\n", null, null, null, "const suspectProtoRx = /\"(?:_|\\\\u0{2}5[Ff]){2}(?:p|\\\\u0{2}70)(?:r|\\\\u0{2}72)(?:o|\\\\u0{2}6[Ff])(?:t|\\\\u0{2}74)(?:o|\\\\u0{2}6[Ff])(?:_|\\\\u0{2}5[Ff]){2}\"\\s*:/;\nconst suspectConstructorRx = /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/;\nconst JsonSigRx = /^\\s*[\"[{]|^\\s*-?\\d{1,16}(\\.\\d{1,17})?([Ee][+-]?\\d+)?\\s*$/;\nfunction jsonParseTransform(key, value) {\n  if (key === \"__proto__\" || key === \"constructor\" && value && typeof value === \"object\" && \"prototype\" in value) {\n    warnKeyDropped(key);\n    return;\n  }\n  return value;\n}\nfunction warnKeyDropped(key) {\n  console.warn(`[destr] Dropping \"${key}\" key to prevent prototype pollution.`);\n}\nfunction destr(value, options = {}) {\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  if (value[0] === '\"' && value[value.length - 1] === '\"' && value.indexOf(\"\\\\\") === -1) {\n    return value.slice(1, -1);\n  }\n  const _value = value.trim();\n  if (_value.length <= 9) {\n    switch (_value.toLowerCase()) {\n      case \"true\": {\n        return true;\n      }\n      case \"false\": {\n        return false;\n      }\n      case \"undefined\": {\n        return void 0;\n      }\n      case \"null\": {\n        return null;\n      }\n      case \"nan\": {\n        return Number.NaN;\n      }\n      case \"infinity\": {\n        return Number.POSITIVE_INFINITY;\n      }\n      case \"-infinity\": {\n        return Number.NEGATIVE_INFINITY;\n      }\n    }\n  }\n  if (!JsonSigRx.test(value)) {\n    if (options.strict) {\n      throw new SyntaxError(\"[destr] Invalid JSON\");\n    }\n    return value;\n  }\n  try {\n    if (suspectProtoRx.test(value) || suspectConstructorRx.test(value)) {\n      if (options.strict) {\n        throw new Error(\"[destr] Possible prototype pollution\");\n      }\n      return JSON.parse(value, jsonParseTransform);\n    }\n    return JSON.parse(value);\n  } catch (error) {\n    if (options.strict) {\n      throw error;\n    }\n    return value;\n  }\n}\nfunction safeDestr(value, options = {}) {\n  return destr(value, { ...options, strict: true });\n}\n\nexport { destr as default, destr, safeDestr };\n"], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBO,SAAS,UAAUA,IAAGC,IAAG;AAC5B,gBAAcD,IAAGC,EAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAcD;AAAA,EAAG;AACtC,EAAAA,GAAE,YAAYC,OAAM,OAAO,OAAO,OAAOA,EAAC,KAAK,GAAG,YAAYA,GAAE,WAAW,IAAI,GAAG;AACtF;AAaO,SAAS,OAAOC,IAAGC,IAAG;AACzB,MAAI,IAAI,CAAC;AACT,WAASC,MAAKF,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAGE,EAAC,KAAKD,GAAE,QAAQC,EAAC,IAAI;AAC9E,MAAEA,EAAC,IAAIF,GAAEE,EAAC;AACd,MAAIF,MAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAASG,KAAI,GAAGD,KAAI,OAAO,sBAAsBF,EAAC,GAAGG,KAAID,GAAE,QAAQC,MAAK;AACpE,UAAIF,GAAE,QAAQC,GAAEC,EAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAKH,IAAGE,GAAEC,EAAC,CAAC;AACzE,UAAED,GAAEC,EAAC,CAAC,IAAIH,GAAEE,GAAEC,EAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAEO,SAAS,WAAW,YAAY,QAAQ,KAAK,MAAM;AACtD,MAAIC,KAAI,UAAU,QAAQC,KAAID,KAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAMN;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,CAAAO,KAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA,MACxH,UAASF,KAAI,WAAW,SAAS,GAAGA,MAAK,GAAGA,KAAK,KAAIL,KAAI,WAAWK,EAAC,EAAG,CAAAE,MAAKD,KAAI,IAAIN,GAAEO,EAAC,IAAID,KAAI,IAAIN,GAAE,QAAQ,KAAKO,EAAC,IAAIP,GAAE,QAAQ,GAAG,MAAMO;AAChJ,SAAOD,KAAI,KAAKC,MAAK,OAAO,eAAe,QAAQ,KAAKA,EAAC,GAAGA;AAChE;AAEO,SAAS,QAAQ,YAAY,WAAW;AAC3C,SAAO,SAAU,QAAQ,KAAK;AAAE,cAAU,QAAQ,KAAK,UAAU;AAAA,EAAG;AACxE;AAEO,SAAS,WAAW,aAAa,eAAe;AACnD,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,QAAO,QAAQ,SAAS,aAAa,aAAa;AACjI;AAEO,SAAS,UAAU,SAAS,YAAYC,IAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiBA,KAAI,QAAQ,IAAIA,GAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAKA,OAAMA,KAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAASL,IAAG;AAAE,eAAOA,EAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAASA,IAAG;AAAE,eAAOA,EAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEO,SAAS,YAAY,SAAS,MAAM;AACvC,MAAIM,KAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAGC,IAAGC,IAAG,GAAGC;AAC/G,SAAOA,KAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAeA,GAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAIA;AACvJ,WAAS,KAAKC,IAAG;AAAE,WAAO,SAAUC,IAAG;AAAE,aAAO,KAAK,CAACD,IAAGC,EAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAIJ,GAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAOD,GAAG,KAAI;AACV,UAAIC,KAAI,GAAGC,OAAM,IAAI,GAAG,CAAC,IAAI,IAAIA,GAAE,QAAQ,IAAI,GAAG,CAAC,IAAIA,GAAE,OAAO,OAAO,IAAIA,GAAE,QAAQ,MAAM,EAAE,KAAKA,EAAC,GAAG,KAAKA,GAAE,SAAS,EAAE,IAAI,EAAE,KAAKA,IAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAIA,KAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,UAAAF,GAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,UAAAA,GAAE;AAAS,UAAAE,KAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAKF,GAAE,IAAI,IAAI;AAAG,UAAAA,GAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAIA,GAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,YAAAA,KAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,YAAAA,GAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAKA,GAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,YAAAA,GAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAKA,GAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,YAAAA,GAAE,QAAQ,EAAE,CAAC;AAAG,YAAAA,GAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,CAAAA,GAAE,IAAI,IAAI;AACpB,UAAAA,GAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAASA,EAAC;AAAA,IAC7B,SAASN,IAAG;AAAE,WAAK,CAAC,GAAGA,EAAC;AAAG,MAAAQ,KAAI;AAAA,IAAG,UAAE;AAAU,MAAAD,KAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAEO,SAAS,gBAAgBK,IAAGC,IAAGC,IAAGC,KAAI;AACzC,MAAIA,QAAO,OAAW,CAAAA,MAAKD;AAC3B,EAAAF,GAAEG,GAAE,IAAIF,GAAEC,EAAC;AACf;AAEO,SAAS,aAAaD,IAAG,SAAS;AACrC,WAASZ,MAAKY,GAAG,KAAIZ,OAAM,aAAa,CAAC,QAAQ,eAAeA,EAAC,EAAG,SAAQA,EAAC,IAAIY,GAAEZ,EAAC;AACxF;AAEO,SAAS,SAASW,IAAG;AACxB,MAAIb,KAAI,OAAO,WAAW,cAAc,OAAO,UAAUc,KAAId,MAAKa,GAAEb,EAAC,GAAGG,KAAI;AAC5E,MAAIW,GAAG,QAAOA,GAAE,KAAKD,EAAC;AACtB,MAAIA,MAAK,OAAOA,GAAE,WAAW,SAAU,QAAO;AAAA,IAC1C,MAAM,WAAY;AACd,UAAIA,MAAKV,MAAKU,GAAE,OAAQ,CAAAA,KAAI;AAC5B,aAAO,EAAE,OAAOA,MAAKA,GAAEV,IAAG,GAAG,MAAM,CAACU,GAAE;AAAA,IAC1C;AAAA,EACJ;AACA,QAAM,IAAI,UAAUb,KAAI,4BAA4B,iCAAiC;AACzF;AAEO,SAAS,OAAOa,IAAGF,IAAG;AACzB,MAAIG,KAAI,OAAO,WAAW,cAAcD,GAAE,OAAO,QAAQ;AACzD,MAAI,CAACC,GAAG,QAAOD;AACf,MAAIV,KAAIW,GAAE,KAAKD,EAAC,GAAGR,IAAGY,MAAK,CAAC,GAAGhB;AAC/B,MAAI;AACA,YAAQU,OAAM,UAAUA,OAAM,MAAM,EAAEN,KAAIF,GAAE,KAAK,GAAG,KAAM,CAAAc,IAAG,KAAKZ,GAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,IAAAJ,KAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAII,MAAK,CAACA,GAAE,SAASS,KAAIX,GAAE,QAAQ,GAAI,CAAAW,GAAE,KAAKX,EAAC;AAAA,IACnD,UACA;AAAU,UAAIF,GAAG,OAAMA,GAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAOgB;AACX;AAEO,SAAS,WAAW;AACvB,WAASA,MAAK,CAAC,GAAGd,KAAI,GAAGA,KAAI,UAAU,QAAQA;AAC3C,IAAAc,MAAKA,IAAG,OAAO,OAAO,UAAUd,EAAC,CAAC,CAAC;AACvC,SAAOc;AACX;AAEO,SAAS,iBAAiB;AAC7B,WAASjB,KAAI,GAAGG,KAAI,GAAG,KAAK,UAAU,QAAQA,KAAI,IAAIA,KAAK,CAAAH,MAAK,UAAUG,EAAC,EAAE;AAC7E,WAASE,KAAI,MAAML,EAAC,GAAGe,KAAI,GAAGZ,KAAI,GAAGA,KAAI,IAAIA;AACzC,aAASe,KAAI,UAAUf,EAAC,GAAGgB,KAAI,GAAG,KAAKD,GAAE,QAAQC,KAAI,IAAIA,MAAKJ;AAC1D,MAAAV,GAAEU,EAAC,IAAIG,GAAEC,EAAC;AAClB,SAAOd;AACX;AAEO,SAAS,QAAQO,IAAG;AACvB,SAAO,gBAAgB,WAAW,KAAK,IAAIA,IAAG,QAAQ,IAAI,QAAQA,EAAC;AACvE;AAEO,SAAS,iBAAiB,SAAS,YAAY,WAAW;AAC7D,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAIF,KAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAGP,IAAG,IAAI,CAAC;AAC5D,SAAOA,KAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAGA,GAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AACpH,WAAS,KAAKQ,IAAG;AAAE,QAAID,GAAEC,EAAC,EAAG,CAAAR,GAAEQ,EAAC,IAAI,SAAUC,IAAG;AAAE,aAAO,IAAI,QAAQ,SAAUM,IAAGnB,IAAG;AAAE,UAAE,KAAK,CAACY,IAAGC,IAAGM,IAAGnB,EAAC,CAAC,IAAI,KAAK,OAAOY,IAAGC,EAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AACzI,WAAS,OAAOD,IAAGC,IAAG;AAAE,QAAI;AAAE,WAAKF,GAAEC,EAAC,EAAEC,EAAC,CAAC;AAAA,IAAG,SAASX,IAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAGA,EAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAKI,IAAG;AAAE,IAAAA,GAAE,iBAAiB,UAAU,QAAQ,QAAQA,GAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAGA,EAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAOG,IAAGI,IAAG;AAAE,QAAIJ,GAAEI,EAAC,GAAG,EAAE,MAAM,GAAG,EAAE,OAAQ,QAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACrF;AAEO,SAAS,iBAAiBC,IAAG;AAChC,MAAIV,IAAGD;AACP,SAAOC,KAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,SAAS,SAAUF,IAAG;AAAE,UAAMA;AAAA,EAAG,CAAC,GAAG,KAAK,QAAQ,GAAGE,GAAE,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AAC1I,WAAS,KAAKQ,IAAGH,IAAG;AAAE,IAAAL,GAAEQ,EAAC,IAAIE,GAAEF,EAAC,IAAI,SAAUC,IAAG;AAAE,cAAQV,KAAI,CAACA,MAAK,EAAE,OAAO,QAAQW,GAAEF,EAAC,EAAEC,EAAC,CAAC,GAAG,MAAMD,OAAM,SAAS,IAAIH,KAAIA,GAAEI,EAAC,IAAIA;AAAA,IAAG,IAAIJ;AAAA,EAAG;AAClJ;AAEO,SAAS,cAAcK,IAAG;AAC7B,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAIC,KAAID,GAAE,OAAO,aAAa,GAAGV;AACjC,SAAOW,KAAIA,GAAE,KAAKD,EAAC,KAAKA,KAAI,OAAO,aAAa,aAAa,SAASA,EAAC,IAAIA,GAAE,OAAO,QAAQ,EAAE,GAAGV,KAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAGA,GAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AAC9M,WAAS,KAAKQ,IAAG;AAAE,IAAAR,GAAEQ,EAAC,IAAIE,GAAEF,EAAC,KAAK,SAAUC,IAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,QAAAA,KAAIC,GAAEF,EAAC,EAAEC,EAAC,GAAG,OAAO,SAAS,QAAQA,GAAE,MAAMA,GAAE,KAAK;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AAC/J,WAAS,OAAO,SAAS,QAAQd,IAAGc,IAAG;AAAE,YAAQ,QAAQA,EAAC,EAAE,KAAK,SAASA,IAAG;AAAE,cAAQ,EAAE,OAAOA,IAAG,MAAMd,GAAE,CAAC;AAAA,IAAG,GAAG,MAAM;AAAA,EAAG;AAC/H;AAEO,SAAS,qBAAqB,QAAQ,KAAK;AAC9C,MAAI,OAAO,gBAAgB;AAAE,WAAO,eAAe,QAAQ,OAAO,EAAE,OAAO,IAAI,CAAC;AAAA,EAAG,OAAO;AAAE,WAAO,MAAM;AAAA,EAAK;AAC9G,SAAO;AACX;AAEO,SAAS,aAAa,KAAK;AAC9B,MAAI,OAAO,IAAI,WAAY,QAAO;AAClC,MAAI,SAAS,CAAC;AACd,MAAI,OAAO;AAAM,aAASiB,MAAK,IAAK,KAAI,OAAO,eAAe,KAAK,KAAKA,EAAC,EAAG,QAAOA,EAAC,IAAI,IAAIA,EAAC;AAAA;AAC7F,SAAO,UAAU;AACjB,SAAO;AACX;AAEO,SAAS,gBAAgB,KAAK;AACjC,SAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,SAAS,IAAI;AAC1D;AAEO,SAAS,uBAAuB,UAAU,YAAY;AACzD,MAAI,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC3B,UAAM,IAAI,UAAU,gDAAgD;AAAA,EACxE;AACA,SAAO,WAAW,IAAI,QAAQ;AAClC;AAEO,SAAS,uBAAuB,UAAU,YAAY,OAAO;AAChE,MAAI,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC3B,UAAM,IAAI,UAAU,gDAAgD;AAAA,EACxE;AACA,aAAW,IAAI,UAAU,KAAK;AAC9B,SAAO;AACX;AAzNA,IAgBI,eAaO;AA7BX;AAAA;AAgBA,IAAI,gBAAgB,SAASjB,IAAGC,IAAG;AAC/B,sBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUD,IAAGC,IAAG;AAAE,QAAAD,GAAE,YAAYC;AAAA,MAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,iBAASG,MAAKH,GAAG,KAAIA,GAAE,eAAeG,EAAC,EAAG,CAAAJ,GAAEI,EAAC,IAAIH,GAAEG,EAAC;AAAA,MAAG;AAC7E,aAAO,cAAcJ,IAAGC,EAAC;AAAA,IAC7B;AAQO,IAAI,WAAW,WAAW;AAC7B,iBAAW,OAAO,UAAU,SAASqB,UAAS,GAAG;AAC7C,iBAASpB,IAAGG,KAAI,GAAGQ,KAAI,UAAU,QAAQR,KAAIQ,IAAGR,MAAK;AACjD,UAAAH,KAAI,UAAUG,EAAC;AACf,mBAASD,MAAKF,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAGE,EAAC,EAAG,GAAEA,EAAC,IAAIF,GAAEE,EAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAAA;AAAA;;;;;;;;ACtCA,aAAgB,MAAM,SAAe;AACnC,aAAO,IAAI,QAAQ,aAAU;AAC3B,mBAAW,MAAK;AACd,kBAAQ,IAAI;QACd,GAAG,OAAO;MACZ,CAAC;IACH;AANA,YAAA,QAAA;;;;;;;;;;ACEa,YAAA,cAAc;AAEd,YAAA,eAAe;;;;;;;;;;ACFf,YAAA,aAAa;AAEb,YAAA,eAAe;AAEf,YAAA,cAAc;AAEd,YAAA,iBAAiB;AAEjB,YAAA,gBAAgB;AAIhB,YAAA,aAAa,QAAA;AAEb,YAAA,eAAe,QAAA,aAAa;AAE5B,YAAA,cAAc,QAAA,aAAa;AAE3B,YAAA,iBAAiB,QAAA,aAAa;AAE9B,YAAA,gBAAgB,QAAA,aAAa;AAI7B,YAAA,WAAW,QAAA;AAEX,YAAA,cAAc,QAAA,WAAW;AAEzB,YAAA,YAAY,QAAA,WAAW;AAEvB,YAAA,eAAe,QAAA,WAAW;AAE1B,YAAA,oBAAoB,QAAA,WAAW;AAI/B,YAAA,UAAU,QAAA;AAEV,YAAA,aAAa,QAAA,UAAU;AAEvB,YAAA,YAAY,QAAA,UAAU;AAEtB,YAAA,aAAa,QAAA,UAAU;AAEvB,YAAA,cAAc,QAAA,UAAU;AAIxB,YAAA,WAAW,QAAA;AAEX,YAAA,YAAY,QAAA,WAAW;AAEvB,YAAA,cAAc,QAAA,WAAW;AAEzB,YAAA,aAAa,QAAA,WAAW;AAIxB,YAAA,WAAW,QAAA,UAAU;;;;;;;;;;AC5DlC,YAAA,aAAA,gBAAA,OAAA;AACA,YAAA,aAAA,gBAAA,OAAA;;;;;;;;;;ACDA,QAAA,cAAA;AAEA,aAAgB,cAAc,SAAe;AAC3C,aAAO,UAAU,YAAA;IACnB;AAFA,YAAA,gBAAA;AAIA,aAAgB,gBAAgB,aAAmB;AACjD,aAAO,KAAK,MAAM,cAAc,YAAA,YAAY;IAC9C;AAFA,YAAA,kBAAA;;;;;;;;;;ACNA,YAAA,aAAA,iBAAA,OAAA;AACA,YAAA,aAAA,mBAAA,OAAA;;;;;;;;;;ACCA,QAAa,QAAb,MAAkB;MAAlB,cAAA;AACS,aAAA,aAAa,oBAAI,IAAG;MA+B7B;MA7BS,MAAM,OAAa;AACxB,YAAI,KAAK,WAAW,IAAI,KAAK,GAAG;AAC9B,gBAAM,IAAI,MAAM,oCAAoC,KAAK,EAAE;;AAE7D,aAAK,WAAW,IAAI,OAAO,EAAE,SAAS,KAAK,IAAG,EAAE,CAAE;MACpD;MAEO,KAAK,OAAa;AACvB,cAAM,YAAY,KAAK,IAAI,KAAK;AAChC,YAAI,OAAO,UAAU,YAAY,aAAa;AAC5C,gBAAM,IAAI,MAAM,oCAAoC,KAAK,EAAE;;AAE7D,cAAM,UAAU,KAAK,IAAG,IAAK,UAAU;AACvC,aAAK,WAAW,IAAI,OAAO,EAAE,SAAS,UAAU,SAAS,QAAO,CAAE;MACpE;MAEO,IAAI,OAAa;AACtB,cAAM,YAAY,KAAK,WAAW,IAAI,KAAK;AAC3C,YAAI,OAAO,cAAc,aAAa;AACpC,gBAAM,IAAI,MAAM,iCAAiC,KAAK,EAAE;;AAE1D,eAAO;MACT;MAEO,QAAQ,OAAa;AAC1B,cAAM,YAAY,KAAK,IAAI,KAAK;AAChC,cAAM,UAAU,UAAU,WAAW,KAAK,IAAG,IAAK,UAAU;AAC5D,eAAO;MACT;;AA/BF,YAAA,QAAA;AAkCA,YAAA,UAAe;;;;;;;;;;AC/Bf,QAAsB,SAAtB,MAA4B;;AAA5B,YAAA,SAAA;;;;;;;;;;ACLA,YAAA,aAAA,kBAAA,OAAA;;;;;;;;;;ACAA,YAAA,aAAA,iBAAA,OAAA;AACA,YAAA,aAAA,iBAAA,OAAA;AACA,YAAA,aAAA,iBAAA,OAAA;AACA,YAAA,aAAA,qBAAA,OAAA;;;;;;;;;;ACHA,aAAgB,cAAiBmB,OAAY;AAC3C,UAAI,MAAqB;AACzB,UAAI,OAAO,WAAW,eAAe,OAAO,OAAOA,KAAI,MAAM,aAAa;AACxE,cAAM,OAAOA,KAAI;;AAEnB,aAAO;IACT;AANA,YAAA,gBAAA;AAQA,aAAgB,qBAAwBA,OAAY;AAClD,YAAM,MAAM,cAAiBA,KAAI;AACjC,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,GAAGA,KAAI,2BAA2B;;AAEpD,aAAO;IACT;AANA,YAAA,uBAAA;AAQA,aAAgB,qBAAkB;AAChC,aAAO,qBAA+B,UAAU;IAClD;AAFA,YAAA,qBAAA;AAIA,aAAgB,cAAW;AACzB,aAAO,cAAwB,UAAU;IAC3C;AAFA,YAAA,cAAA;AAIA,aAAgB,sBAAmB;AACjC,aAAO,qBAAgC,WAAW;IACpD;AAFA,YAAA,sBAAA;AAIA,aAAgB,eAAY;AAC1B,aAAO,cAAyB,WAAW;IAC7C;AAFA,YAAA,eAAA;AAIA,aAAgB,qBAAkB;AAChC,aAAO,qBAA+B,UAAU;IAClD;AAFA,YAAA,qBAAA;AAIA,aAAgB,cAAW;AACzB,aAAO,cAAwB,UAAU;IAC3C;AAFA,YAAA,cAAA;AAIA,aAAgB,mBAAgB;AAC9B,aAAO,qBAA6B,QAAQ;IAC9C;AAFA,YAAA,mBAAA;AAIA,aAAgB,YAAS;AACvB,aAAO,cAAsB,QAAQ;IACvC;AAFA,YAAA,YAAA;AAIA,aAAgB,yBAAsB;AACpC,aAAO,qBAA8B,cAAc;IACrD;AAFA,YAAA,yBAAA;AAIA,aAAgB,kBAAe;AAC7B,aAAO,cAAuB,cAAc;IAC9C;AAFA,YAAA,kBAAA;;;;;;;;;;ACpDA,QAAA,mBAAA;AAYA,aAAgB,oBAAiB;AAC/B,UAAI;AACJ,UAAI;AAEJ,UAAI;AACF,cAAM,iBAAA,mBAAkB;AACxB,cAAM,iBAAA,mBAAkB;eACjBC,IAAG;AACV,eAAO;;AAGT,eAAS,WAAQ;AACf,cAAM,QAA2C,IAAI,qBACnD,MAAM;AAER,cAAMC,SAAkB,CAAA;AAExB,iBAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACrC,gBAAM,OAAwB,MAAMA,EAAC;AAErC,gBAAM,MAAqB,KAAK,aAAa,KAAK;AAClD,cAAI,KAAK;AACP,gBAAI,IAAI,YAAW,EAAG,QAAQ,MAAM,IAAI,IAAI;AAC1C,oBAAM,OAAsB,KAAK,aAAa,MAAM;AAEpD,kBAAI,MAAM;AACR,oBACE,KAAK,YAAW,EAAG,QAAQ,QAAQ,MAAM,MACzC,KAAK,YAAW,EAAG,QAAQ,OAAO,MAAM,MACxC,KAAK,QAAQ,IAAI,MAAM,GACvB;AACA,sBAAI,eAAuB,IAAI,WAAW,OAAO,IAAI;AAErD,sBAAI,KAAK,QAAQ,GAAG,MAAM,GAAG;AAC3B,oCAAgB;yBACX;AACL,0BAAM,OAAiB,IAAI,SAAS,MAAM,GAAG;AAC7C,yBAAK,IAAG;AACR,0BAAM,YAAoB,KAAK,KAAK,GAAG;AACvC,oCAAgB,YAAY,MAAM;;AAGpC,kBAAAD,OAAM,KAAK,YAAY;2BACd,KAAK,QAAQ,IAAI,MAAM,GAAG;AACnC,wBAAM,cAAsB,IAAI,WAAW;AAE3C,kBAAAA,OAAM,KAAK,WAAW;uBACjB;AACL,kBAAAA,OAAM,KAAK,IAAI;;;;;;AAOzB,eAAOA;MACT;AAEA,eAAS,0BAA0B,MAAc;AAC/C,cAAM,WAA8C,IAAI,qBACtD,MAAM;AAGR,iBAASC,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACxC,gBAAM,MAAuB,SAASA,EAAC;AACvC,gBAAM,aAAmC,CAAC,YAAY,YAAY,MAAM,EACrE,IAAI,CAAC,WAAmB,IAAI,aAAa,MAAM,CAAC,EAChD,OAAO,CAAC,SAAuB;AAC9B,gBAAI,MAAM;AACR,qBAAO,KAAK,SAAS,IAAI;;AAE3B,mBAAO;UACT,CAAC;AAEH,cAAI,WAAW,UAAU,YAAY;AACnC,kBAAM,UAAyB,IAAI,aAAa,SAAS;AACzD,gBAAI,SAAS;AACX,qBAAO;;;;AAKb,eAAO;MACT;AAEA,eAAS,UAAO;AACd,YAAIC,QAAe,uBACjB,QACA,gBACA,YACA,eAAe;AAGjB,YAAI,CAACA,OAAM;AACT,UAAAA,QAAO,IAAI;;AAGb,eAAOA;MACT;AAEA,eAAS,iBAAc;AACrB,cAAMC,eAAsB,uBAC1B,eACA,kBACA,uBACA,UAAU;AAGZ,eAAOA;MACT;AAEA,YAAMD,QAAe,QAAO;AAC5B,YAAM,cAAsB,eAAc;AAC1C,YAAM,MAAc,IAAI;AACxB,YAAM,QAAkB,SAAQ;AAEhC,YAAM,OAAyB;QAC7B;QACA;QACA;QACA,MAAAA;;AAGF,aAAO;IACT;AA5HA,YAAA,oBAAA;;;;;ACZA;AAAA;AAAA;AACA,aAAS,aAAcE,IAAG;AACxB,UAAI;AAAE,eAAO,KAAK,UAAUA,EAAC;AAAA,MAAE,SAAQC,IAAG;AAAE,eAAO;AAAA,MAAe;AAAA,IACpE;AAEA,WAAO,UAAU;AAEjB,aAAS,OAAOC,IAAG,MAAM,MAAM;AAC7B,UAAI,KAAM,QAAQ,KAAK,aAAc;AACrC,UAAI,SAAS;AACb,UAAI,OAAOA,OAAM,YAAYA,OAAM,MAAM;AACvC,YAAI,MAAM,KAAK,SAAS;AACxB,YAAI,QAAQ,EAAG,QAAOA;AACtB,YAAI,UAAU,IAAI,MAAM,GAAG;AAC3B,gBAAQ,CAAC,IAAI,GAAGA,EAAC;AACjB,iBAAS,QAAQ,GAAG,QAAQ,KAAK,SAAS;AACxC,kBAAQ,KAAK,IAAI,GAAG,KAAK,KAAK,CAAC;AAAA,QACjC;AACA,eAAO,QAAQ,KAAK,GAAG;AAAA,MACzB;AACA,UAAI,OAAOA,OAAM,UAAU;AACzB,eAAOA;AAAA,MACT;AACA,UAAI,SAAS,KAAK;AAClB,UAAI,WAAW,EAAG,QAAOA;AACzB,UAAI,MAAM;AACV,UAAIC,KAAI,IAAI;AACZ,UAAI,UAAU;AACd,UAAI,OAAQD,MAAKA,GAAE,UAAW;AAC9B,eAASE,KAAI,GAAGA,KAAI,QAAO;AACzB,YAAIF,GAAE,WAAWE,EAAC,MAAM,MAAMA,KAAI,IAAI,MAAM;AAC1C,oBAAU,UAAU,KAAK,UAAU;AACnC,kBAAQF,GAAE,WAAWE,KAAI,CAAC,GAAG;AAAA,YAC3B,KAAK;AAAA;AAAA,YACL,KAAK;AACH,kBAAID,MAAK;AACP;AACF,kBAAI,KAAKA,EAAC,KAAK,KAAO;AACtB,kBAAI,UAAUC;AACZ,uBAAOF,GAAE,MAAM,SAASE,EAAC;AAC3B,qBAAO,OAAO,KAAKD,EAAC,CAAC;AACrB,wBAAUC,KAAI;AACd,cAAAA;AACA;AAAA,YACF,KAAK;AACH,kBAAID,MAAK;AACP;AACF,kBAAI,KAAKA,EAAC,KAAK,KAAO;AACtB,kBAAI,UAAUC;AACZ,uBAAOF,GAAE,MAAM,SAASE,EAAC;AAC3B,qBAAO,KAAK,MAAM,OAAO,KAAKD,EAAC,CAAC,CAAC;AACjC,wBAAUC,KAAI;AACd,cAAAA;AACA;AAAA,YACF,KAAK;AAAA;AAAA,YACL,KAAK;AAAA;AAAA,YACL,KAAK;AACH,kBAAID,MAAK;AACP;AACF,kBAAI,KAAKA,EAAC,MAAM,OAAW;AAC3B,kBAAI,UAAUC;AACZ,uBAAOF,GAAE,MAAM,SAASE,EAAC;AAC3B,kBAAI,OAAO,OAAO,KAAKD,EAAC;AACxB,kBAAI,SAAS,UAAU;AACrB,uBAAO,MAAO,KAAKA,EAAC,IAAI;AACxB,0BAAUC,KAAI;AACd,gBAAAA;AACA;AAAA,cACF;AACA,kBAAI,SAAS,YAAY;AACvB,uBAAO,KAAKD,EAAC,EAAE,QAAQ;AACvB,0BAAUC,KAAI;AACd,gBAAAA;AACA;AAAA,cACF;AACA,qBAAO,GAAG,KAAKD,EAAC,CAAC;AACjB,wBAAUC,KAAI;AACd,cAAAA;AACA;AAAA,YACF,KAAK;AACH,kBAAID,MAAK;AACP;AACF,kBAAI,UAAUC;AACZ,uBAAOF,GAAE,MAAM,SAASE,EAAC;AAC3B,qBAAO,OAAO,KAAKD,EAAC,CAAC;AACrB,wBAAUC,KAAI;AACd,cAAAA;AACA;AAAA,YACF,KAAK;AACH,kBAAI,UAAUA;AACZ,uBAAOF,GAAE,MAAM,SAASE,EAAC;AAC3B,qBAAO;AACP,wBAAUA,KAAI;AACd,cAAAA;AACA,cAAAD;AACA;AAAA,UACJ;AACA,YAAEA;AAAA,QACJ;AACA,UAAEC;AAAA,MACJ;AACA,UAAI,YAAY;AACd,eAAOF;AAAA,eACA,UAAU,MAAM;AACvB,eAAOA,GAAE,MAAM,OAAO;AAAA,MACxB;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC5GA;AAAA;AAAA;AAEA,QAAM,SAAS;AAEf,WAAO,UAAU;AAEjB,QAAM,WAAW,uBAAuB,EAAE,WAAW,CAAC;AACtD,QAAM,iBAAiB;AAAA,MACrB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,qBAAqB;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAEA,aAAS,gBAAiB,WAAW,aAAa;AAChD,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,cAAM,cAAc,UAAU,OAAO,SAAUG,IAAG;AAChD,iBAAOA,OAAM;AAAA,QACf,CAAC;AACD,eAAO;AAAA,MACT,WAAW,cAAc,MAAM;AAC7B,eAAO,OAAO,KAAK,WAAW;AAAA,MAChC;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,KAAM,MAAM;AACnB,aAAO,QAAQ,CAAC;AAChB,WAAK,UAAU,KAAK,WAAW,CAAC;AAEhC,YAAMC,YAAW,KAAK,QAAQ;AAC9B,UAAIA,aAAY,OAAOA,UAAS,SAAS,YAAY;AAAE,cAAM,MAAM,iDAAiD;AAAA,MAAE;AAEtH,YAAM,QAAQ,KAAK,QAAQ,SAAS;AACpC,UAAI,KAAK,QAAQ,MAAO,MAAK,QAAQ,WAAW;AAChD,YAAM,cAAc,KAAK,eAAe,CAAC;AACzC,YAAM,YAAY,gBAAgB,KAAK,QAAQ,WAAW,WAAW;AACrE,UAAI,kBAAkB,KAAK,QAAQ;AAEnC,UACE,MAAM,QAAQ,KAAK,QAAQ,SAAS,KACpC,KAAK,QAAQ,UAAU,QAAQ,qBAAqB,IAAI,GACxD,mBAAkB;AAEpB,YAAM,SAAS,CAAC,SAAS,SAAS,QAAQ,QAAQ,SAAS,OAAO;AAElE,UAAI,OAAO,UAAU,YAAY;AAC/B,cAAM,QAAQ,MAAM,QAAQ,MAAM,OAClC,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAQ;AAAA,MAC3C;AACA,UAAI,KAAK,YAAY,MAAO,MAAK,QAAQ;AACzC,YAAM,QAAQ,KAAK,SAAS;AAC5B,YAAM,SAAS,OAAO,OAAO,KAAK;AAClC,UAAI,CAAC,OAAO,IAAK,QAAO,MAAM;AAE9B,aAAO,eAAe,QAAQ,YAAY;AAAA,QACxC,KAAK;AAAA,MACP,CAAC;AACD,aAAO,eAAe,QAAQ,SAAS;AAAA,QACrC,KAAK;AAAA,QACL,KAAK;AAAA,MACP,CAAC;AAED,YAAM,UAAU;AAAA,QACd,UAAAA;AAAA,QACA;AAAA,QACA,UAAU,KAAK,QAAQ;AAAA,QACvB;AAAA,QACA,WAAW,gBAAgB,IAAI;AAAA,MACjC;AACA,aAAO,SAAS,KAAK;AACrB,aAAO,QAAQ;AAEf,aAAO,kBAAkB,OAAO,kBAChC,OAAO,OAAO,OAAO,cAAc,OAAO,KAC1C,OAAO,kBAAkB,OAAO,OAChC,OAAO,sBAAsB,OAAO,iBACpC,OAAO,qBAAqB,OAAO,YACnC,OAAO,gBAAgB,OAAO,aAC9B,OAAO,QAAQ,OAAO,QAAQ;AAC9B,aAAO,cAAc;AACrB,aAAO,aAAa;AACpB,aAAO,mBAAmB;AAC1B,aAAO,QAAQ;AAEf,UAAIA,UAAU,QAAO,YAAY,oBAAoB;AAErD,eAAS,cAAe;AACtB,eAAO,KAAK,UAAU,WAClB,WACA,KAAK,OAAO,OAAO,KAAK,KAAK;AAAA,MACnC;AAEA,eAAS,WAAY;AACnB,eAAO,KAAK;AAAA,MACd;AACA,eAAS,SAAUC,QAAO;AACxB,YAAIA,WAAU,YAAY,CAAC,KAAK,OAAO,OAAOA,MAAK,GAAG;AACpD,gBAAM,MAAM,mBAAmBA,MAAK;AAAA,QACtC;AACA,aAAK,SAASA;AAEd,YAAI,SAAS,QAAQ,SAAS,KAAK;AACnC,YAAI,SAAS,QAAQ,SAAS,OAAO;AACrC,YAAI,SAAS,QAAQ,QAAQ,OAAO;AACpC,YAAI,SAAS,QAAQ,QAAQ,KAAK;AAClC,YAAI,SAAS,QAAQ,SAAS,KAAK;AACnC,YAAI,SAAS,QAAQ,SAAS,KAAK;AAAA,MACrC;AAEA,eAAS,MAAO,UAAU,cAAc;AACtC,YAAI,CAAC,UAAU;AACb,gBAAM,IAAI,MAAM,iCAAiC;AAAA,QACnD;AACA,uBAAe,gBAAgB,CAAC;AAChC,YAAI,aAAa,SAAS,aAAa;AACrC,uBAAa,cAAc,SAAS;AAAA,QACtC;AACA,cAAM,0BAA0B,aAAa;AAC7C,YAAI,aAAa,yBAAyB;AACxC,cAAI,mBAAmB,OAAO,OAAO,CAAC,GAAG,aAAa,uBAAuB;AAC7E,cAAI,iBAAiB,KAAK,QAAQ,cAAc,OAC5C,OAAO,KAAK,gBAAgB,IAC5B;AACJ,iBAAO,SAAS;AAChB,2BAAiB,CAAC,QAAQ,GAAG,gBAAgB,kBAAkB,KAAK,gBAAgB;AAAA,QACtF;AACA,iBAAS,MAAO,QAAQ;AACtB,eAAK,eAAe,OAAO,cAAc,KAAK;AAC9C,eAAK,QAAQ,KAAK,QAAQ,UAAU,OAAO;AAC3C,eAAK,QAAQ,KAAK,QAAQ,UAAU,OAAO;AAC3C,eAAK,OAAO,KAAK,QAAQ,UAAU,MAAM;AACzC,eAAK,OAAO,KAAK,QAAQ,UAAU,MAAM;AACzC,eAAK,QAAQ,KAAK,QAAQ,UAAU,OAAO;AAC3C,eAAK,QAAQ,KAAK,QAAQ,UAAU,OAAO;AAC3C,cAAI,kBAAkB;AACpB,iBAAK,cAAc;AACnB,iBAAK,aAAa;AAAA,UACpB;AACA,cAAID,WAAU;AACZ,iBAAK,YAAY;AAAA,cACf,CAAC,EAAE,OAAO,OAAO,UAAU,UAAU,QAAQ;AAAA,YAC/C;AAAA,UACF;AAAA,QACF;AACA,cAAM,YAAY;AAClB,eAAO,IAAI,MAAM,IAAI;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAEA,SAAK,SAAS;AAAA,MACZ,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACN;AAAA,IACF;AAEA,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,OAAO,OAAO,CAAC,GAAG,EAAE,UAAU,WAAW,UAAU,QAAQ,CAAC;AAEpF,aAAS,IAAK,MAAM,QAAQ,OAAO,UAAU;AAC3C,YAAM,QAAQ,OAAO,eAAe,MAAM;AAC1C,aAAO,KAAK,IAAI,OAAO,WAAW,OAAO,OAAO,OAAO,KAAK,IACxD,OACC,MAAM,KAAK,IAAI,MAAM,KAAK,IAAK,SAAS,KAAK,KAAK,SAAS,QAAQ,KAAK;AAE7E,WAAK,MAAM,QAAQ,KAAK;AAAA,IAC1B;AAEA,aAAS,KAAM,MAAM,QAAQ,OAAO;AAClC,UAAI,CAAC,KAAK,YAAY,OAAO,KAAK,MAAM,KAAM;AAE9C,aAAO,KAAK,IAAK,yBAAU,OAAO;AAChC,eAAO,SAAS,MAAO;AACrB,gBAAM,KAAK,KAAK,UAAU;AAC1B,gBAAM,OAAO,IAAI,MAAM,UAAU,MAAM;AACvC,gBAAM,QAAS,OAAO,kBAAkB,OAAO,eAAe,IAAI,MAAM,WAAY,WAAW;AAC/F,mBAASE,KAAI,GAAGA,KAAI,KAAK,QAAQA,KAAK,MAAKA,EAAC,IAAI,UAAUA,EAAC;AAE3D,cAAI,KAAK,aAAa,CAAC,KAAK,UAAU;AACpC,6BAAiB,MAAM,KAAK,YAAY,KAAK,aAAa,KAAK,gBAAgB;AAAA,UACjF;AACA,cAAI,KAAK,SAAU,OAAM,KAAK,OAAO,SAAS,MAAM,OAAO,MAAM,EAAE,CAAC;AAAA,cAC/D,OAAM,MAAM,OAAO,IAAI;AAE5B,cAAI,KAAK,UAAU;AACjB,kBAAM,gBAAgB,KAAK,SAAS,SAAS,OAAO;AACpD,kBAAM,gBAAgB,KAAK,OAAO,OAAO,aAAa;AACtD,kBAAM,cAAc,KAAK,OAAO,OAAO,KAAK;AAC5C,gBAAI,cAAc,cAAe;AACjC,qBAAS,MAAM;AAAA,cACb;AAAA,cACA,aAAa;AAAA,cACb;AAAA,cACA;AAAA,cACA,eAAe,KAAK,OAAO,OAAO,KAAK,SAAS,SAAS,OAAO,KAAK;AAAA,cACrE,MAAM,KAAK,SAAS;AAAA,cACpB,KAAK,OAAO;AAAA,YACd,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AAAA,MACF,EAAG,OAAO,KAAK,CAAC;AAAA,IAClB;AAEA,aAAS,SAAU,QAAQ,OAAO,MAAM,IAAI;AAC1C,UAAI,OAAO,WAAY,kBAAiB,MAAM,OAAO,YAAY,OAAO,aAAa,OAAO,gBAAgB;AAC5G,YAAM,aAAa,KAAK,MAAM;AAC9B,UAAI,MAAM,WAAW,CAAC;AACtB,YAAMC,KAAI,CAAC;AACX,UAAI,IAAI;AACN,QAAAA,GAAE,OAAO;AAAA,MACX;AACA,MAAAA,GAAE,QAAQ,KAAK,OAAO,OAAO,KAAK;AAClC,UAAI,OAAO,OAAO,cAAc,KAAK;AACrC,UAAI,MAAM,EAAG,OAAM;AAEnB,UAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;AAC3C,eAAO,SAAS,OAAO,WAAW,CAAC,MAAM,UAAU;AACjD,iBAAO,OAAOA,IAAG,WAAW,MAAM,CAAC;AAAA,QACrC;AACA,cAAM,WAAW,SAAS,OAAO,WAAW,MAAM,GAAG,UAAU,IAAI;AAAA,MACrE,WAAW,OAAO,QAAQ,SAAU,OAAM,OAAO,WAAW,MAAM,GAAG,UAAU;AAC/E,UAAI,QAAQ,OAAW,CAAAA,GAAE,MAAM;AAC/B,aAAOA;AAAA,IACT;AAEA,aAAS,iBAAkB,MAAM,WAAW,aAAa,iBAAiB;AACxE,iBAAWD,MAAK,MAAM;AACpB,YAAI,mBAAmB,KAAKA,EAAC,aAAa,OAAO;AAC/C,eAAKA,EAAC,IAAI,KAAK,eAAe,IAAI,KAAKA,EAAC,CAAC;AAAA,QAC3C,WAAW,OAAO,KAAKA,EAAC,MAAM,YAAY,CAAC,MAAM,QAAQ,KAAKA,EAAC,CAAC,GAAG;AACjE,qBAAWH,MAAK,KAAKG,EAAC,GAAG;AACvB,gBAAI,aAAa,UAAU,QAAQH,EAAC,IAAI,MAAMA,MAAK,aAAa;AAC9D,mBAAKG,EAAC,EAAEH,EAAC,IAAI,YAAYA,EAAC,EAAE,KAAKG,EAAC,EAAEH,EAAC,CAAC;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,KAAM,QAAQ,UAAU,OAAO;AACtC,aAAO,WAAY;AACjB,cAAM,OAAO,IAAI,MAAM,IAAI,UAAU,MAAM;AAC3C,aAAK,CAAC,IAAI;AACV,iBAASG,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,eAAKA,EAAC,IAAI,UAAUA,KAAI,CAAC;AAAA,QAC3B;AACA,eAAO,OAAO,KAAK,EAAE,MAAM,MAAM,IAAI;AAAA,MACvC;AAAA,IACF;AAEA,aAAS,SAAU,QAAQ,MAAM,MAAM;AACrC,YAAM,OAAO,KAAK;AAClB,YAAM,KAAK,KAAK;AAChB,YAAM,cAAc,KAAK;AACzB,YAAM,cAAc,KAAK;AACzB,YAAM,MAAM,KAAK;AACjB,YAAM,WAAW,OAAO,UAAU;AAElC;AAAA,QACE;AAAA,QACA,OAAO,cAAc,OAAO,KAAK,OAAO,WAAW;AAAA,QACnD,OAAO;AAAA,QACP,OAAO,qBAAqB,SAAY,OAAO,OAAO;AAAA,MACxD;AACA,aAAO,UAAU,KAAK;AACtB,aAAO,UAAU,WAAW,KAAK,OAAO,SAAU,KAAK;AAErD,eAAO,SAAS,QAAQ,GAAG,MAAM;AAAA,MACnC,CAAC;AAED,aAAO,UAAU,MAAM,QAAQ;AAC/B,aAAO,UAAU,MAAM,QAAQ;AAE/B,WAAK,aAAa,OAAO,WAAW,GAAG;AAEvC,aAAO,YAAY,oBAAoB,QAAQ;AAAA,IACjD;AAEA,aAAS,oBAAqB,UAAU;AACtC,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,UAAU,CAAC;AAAA,QACX,UAAU,YAAY,CAAC;AAAA,QACvB,OAAO,EAAE,OAAO,IAAI,OAAO,EAAE;AAAA,MAC/B;AAAA,IACF;AAEA,aAAS,WAAY,KAAK;AACxB,YAAM,MAAM;AAAA,QACV,MAAM,IAAI,YAAY;AAAA,QACtB,KAAK,IAAI;AAAA,QACT,OAAO,IAAI;AAAA,MACb;AACA,iBAAW,OAAO,KAAK;AACrB,YAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,cAAI,GAAG,IAAI,IAAI,GAAG;AAAA,QACpB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,gBAAiB,MAAM;AAC9B,UAAI,OAAO,KAAK,cAAc,YAAY;AACxC,eAAO,KAAK;AAAA,MACd;AACA,UAAI,KAAK,cAAc,OAAO;AAC5B,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,aAAS,OAAQ;AAAE,aAAO,CAAC;AAAA,IAAE;AAC7B,aAAS,YAAaE,IAAG;AAAE,aAAOA;AAAA,IAAE;AACpC,aAAS,OAAQ;AAAA,IAAC;AAElB,aAAS,WAAY;AAAE,aAAO;AAAA,IAAM;AACpC,aAAS,YAAa;AAAE,aAAO,KAAK,IAAI;AAAA,IAAE;AAC1C,aAAS,WAAY;AAAE,aAAO,KAAK,MAAM,KAAK,IAAI,IAAI,GAAM;AAAA,IAAE;AAC9D,aAAS,UAAW;AAAE,aAAO,IAAI,KAAK,KAAK,IAAI,CAAC,EAAE,YAAY;AAAA,IAAE;AAIhE,aAAS,yBAA0B;AACjC,eAAS,KAAMD,IAAG;AAAE,eAAO,OAAOA,OAAM,eAAeA;AAAA,MAAE;AACzD,UAAI;AACF,YAAI,OAAO,eAAe,YAAa,QAAO;AAC9C,eAAO,eAAe,OAAO,WAAW,cAAc;AAAA,UACpD,KAAK,WAAY;AACf,mBAAO,OAAO,UAAU;AACxB,mBAAQ,KAAK,aAAa;AAAA,UAC5B;AAAA,UACA,cAAc;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,MACT,SAASE,IAAG;AACV,eAAO,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,MACtD;AAAA,IACF;AAAA;AAAA;;;ACpWA,IAAAC,qBAAA;AAAA,SAAAA,oBAAA;AAAA,kBAAAC;AAAA,EAAA,wBAAAC;AAAA,EAAA,wBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,8BAAAC;AAAA,EAAA,8BAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,oBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,oBAAAC;AAAA,EAAA,4BAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,gBAAAC;AAAA;AAuBO,SAASX,WAAUY,IAAGC,IAAG;AAC5B,EAAAC,eAAcF,IAAGC,EAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAcD;AAAA,EAAG;AACtC,EAAAA,GAAE,YAAYC,OAAM,OAAO,OAAO,OAAOA,EAAC,KAAK,GAAG,YAAYA,GAAE,WAAW,IAAI,GAAG;AACtF;AAaO,SAASL,QAAOO,IAAGC,IAAG;AACzB,MAAI,IAAI,CAAC;AACT,WAASC,MAAKF,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAGE,EAAC,KAAKD,GAAE,QAAQC,EAAC,IAAI;AAC9E,MAAEA,EAAC,IAAIF,GAAEE,EAAC;AACd,MAAIF,MAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAASG,KAAI,GAAGD,KAAI,OAAO,sBAAsBF,EAAC,GAAGG,KAAID,GAAE,QAAQC,MAAK;AACpE,UAAIF,GAAE,QAAQC,GAAEC,EAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAKH,IAAGE,GAAEC,EAAC,CAAC;AACzE,UAAED,GAAEC,EAAC,CAAC,IAAIH,GAAEE,GAAEC,EAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAEO,SAASpB,YAAW,YAAY,QAAQ,KAAK,MAAM;AACtD,MAAIqB,KAAI,UAAU,QAAQC,KAAID,KAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,yBAAyB,QAAQ,GAAG,IAAI,MAAMP;AAC3H,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,CAAAQ,KAAI,QAAQ,SAAS,YAAY,QAAQ,KAAK,IAAI;AAAA,MACxH,UAASF,KAAI,WAAW,SAAS,GAAGA,MAAK,GAAGA,KAAK,KAAIN,KAAI,WAAWM,EAAC,EAAG,CAAAE,MAAKD,KAAI,IAAIP,GAAEQ,EAAC,IAAID,KAAI,IAAIP,GAAE,QAAQ,KAAKQ,EAAC,IAAIR,GAAE,QAAQ,GAAG,MAAMQ;AAChJ,SAAOD,KAAI,KAAKC,MAAK,OAAO,eAAe,QAAQ,KAAKA,EAAC,GAAGA;AAChE;AAEO,SAASd,SAAQ,YAAY,WAAW;AAC3C,SAAO,SAAU,QAAQ,KAAK;AAAE,cAAU,QAAQ,KAAK,UAAU;AAAA,EAAG;AACxE;AAEO,SAASD,YAAW,aAAa,eAAe;AACnD,MAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,aAAa,WAAY,QAAO,QAAQ,SAAS,aAAa,aAAa;AACjI;AAEO,SAASX,WAAU,SAAS,YAAY2B,IAAG,WAAW;AACzD,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiBA,KAAI,QAAQ,IAAIA,GAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAKA,OAAMA,KAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAASL,IAAG;AAAE,eAAOA,EAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAASA,IAAG;AAAE,eAAOA,EAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEO,SAASf,aAAY,SAAS,MAAM;AACvC,MAAIqB,KAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAGC,IAAGC,IAAG,GAAGC;AAC/G,SAAOA,KAAI,EAAE,MAAM,KAAK,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,eAAeA,GAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAIA;AACvJ,WAAS,KAAKC,IAAG;AAAE,WAAO,SAAUC,IAAG;AAAE,aAAO,KAAK,CAACD,IAAGC,EAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAIJ,GAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAOD,GAAG,KAAI;AACV,UAAIC,KAAI,GAAGC,OAAM,IAAI,GAAG,CAAC,IAAI,IAAIA,GAAE,QAAQ,IAAI,GAAG,CAAC,IAAIA,GAAE,OAAO,OAAO,IAAIA,GAAE,QAAQ,MAAM,EAAE,KAAKA,EAAC,GAAG,KAAKA,GAAE,SAAS,EAAE,IAAI,EAAE,KAAKA,IAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAIA,KAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,UAAAF,GAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,UAAAA,GAAE;AAAS,UAAAE,KAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAKF,GAAE,IAAI,IAAI;AAAG,UAAAA,GAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAIA,GAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,YAAAA,KAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,YAAAA,GAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAKA,GAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,YAAAA,GAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAKA,GAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,YAAAA,GAAE,QAAQ,EAAE,CAAC;AAAG,YAAAA,GAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,CAAAA,GAAE,IAAI,IAAI;AACpB,UAAAA,GAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAASA,EAAC;AAAA,IAC7B,SAASN,IAAG;AAAE,WAAK,CAAC,GAAGA,EAAC;AAAG,MAAAQ,KAAI;AAAA,IAAG,UAAE;AAAU,MAAAD,KAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACJ;AAEO,SAAS1B,iBAAgB+B,IAAGC,IAAGC,IAAGC,KAAI;AACzC,MAAIA,QAAO,OAAW,CAAAA,MAAKD;AAC3B,EAAAF,GAAEG,GAAE,IAAIF,GAAEC,EAAC;AACf;AAEO,SAAS/B,cAAa8B,IAAG,SAAS;AACrC,WAASZ,MAAKY,GAAG,KAAIZ,OAAM,aAAa,CAAC,QAAQ,eAAeA,EAAC,EAAG,SAAQA,EAAC,IAAIY,GAAEZ,EAAC;AACxF;AAEO,SAASN,UAASiB,IAAG;AACxB,MAAIb,KAAI,OAAO,WAAW,cAAc,OAAO,UAAUc,KAAId,MAAKa,GAAEb,EAAC,GAAGG,KAAI;AAC5E,MAAIW,GAAG,QAAOA,GAAE,KAAKD,EAAC;AACtB,MAAIA,MAAK,OAAOA,GAAE,WAAW,SAAU,QAAO;AAAA,IAC1C,MAAM,WAAY;AACd,UAAIA,MAAKV,MAAKU,GAAE,OAAQ,CAAAA,KAAI;AAC5B,aAAO,EAAE,OAAOA,MAAKA,GAAEV,IAAG,GAAG,MAAM,CAACU,GAAE;AAAA,IAC1C;AAAA,EACJ;AACA,QAAM,IAAI,UAAUb,KAAI,4BAA4B,iCAAiC;AACzF;AAEO,SAASR,QAAOqB,IAAGF,IAAG;AACzB,MAAIG,KAAI,OAAO,WAAW,cAAcD,GAAE,OAAO,QAAQ;AACzD,MAAI,CAACC,GAAG,QAAOD;AACf,MAAIV,KAAIW,GAAE,KAAKD,EAAC,GAAGR,IAAGY,MAAK,CAAC,GAAGhB;AAC/B,MAAI;AACA,YAAQU,OAAM,UAAUA,OAAM,MAAM,EAAEN,KAAIF,GAAE,KAAK,GAAG,KAAM,CAAAc,IAAG,KAAKZ,GAAE,KAAK;AAAA,EAC7E,SACO,OAAO;AAAE,IAAAJ,KAAI,EAAE,MAAa;AAAA,EAAG,UACtC;AACI,QAAI;AACA,UAAII,MAAK,CAACA,GAAE,SAASS,KAAIX,GAAE,QAAQ,GAAI,CAAAW,GAAE,KAAKX,EAAC;AAAA,IACnD,UACA;AAAU,UAAIF,GAAG,OAAMA,GAAE;AAAA,IAAO;AAAA,EACpC;AACA,SAAOgB;AACX;AAEO,SAASvB,YAAW;AACvB,WAASuB,MAAK,CAAC,GAAGd,KAAI,GAAGA,KAAI,UAAU,QAAQA;AAC3C,IAAAc,MAAKA,IAAG,OAAOzB,QAAO,UAAUW,EAAC,CAAC,CAAC;AACvC,SAAOc;AACX;AAEO,SAAStB,kBAAiB;AAC7B,WAASK,KAAI,GAAGG,KAAI,GAAG,KAAK,UAAU,QAAQA,KAAI,IAAIA,KAAK,CAAAH,MAAK,UAAUG,EAAC,EAAE;AAC7E,WAASE,KAAI,MAAML,EAAC,GAAGe,KAAI,GAAGZ,KAAI,GAAGA,KAAI,IAAIA;AACzC,aAASe,KAAI,UAAUf,EAAC,GAAGgB,KAAI,GAAG,KAAKD,GAAE,QAAQC,KAAI,IAAIA,MAAKJ;AAC1D,MAAAV,GAAEU,EAAC,IAAIG,GAAEC,EAAC;AAClB,SAAOd;AACX;AAEO,SAAS3B,SAAQkC,IAAG;AACvB,SAAO,gBAAgBlC,YAAW,KAAK,IAAIkC,IAAG,QAAQ,IAAIlC,SAAQkC,EAAC;AACvE;AAEO,SAASpC,kBAAiB,SAAS,YAAY,WAAW;AAC7D,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAIkC,KAAI,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAGP,IAAG,IAAI,CAAC;AAC5D,SAAOA,KAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAGA,GAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AACpH,WAAS,KAAKQ,IAAG;AAAE,QAAID,GAAEC,EAAC,EAAG,CAAAR,GAAEQ,EAAC,IAAI,SAAUC,IAAG;AAAE,aAAO,IAAI,QAAQ,SAAUM,IAAGpB,IAAG;AAAE,UAAE,KAAK,CAACa,IAAGC,IAAGM,IAAGpB,EAAC,CAAC,IAAI,KAAK,OAAOa,IAAGC,EAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AACzI,WAAS,OAAOD,IAAGC,IAAG;AAAE,QAAI;AAAE,WAAKF,GAAEC,EAAC,EAAEC,EAAC,CAAC;AAAA,IAAG,SAASX,IAAG;AAAE,aAAO,EAAE,CAAC,EAAE,CAAC,GAAGA,EAAC;AAAA,IAAG;AAAA,EAAE;AACjF,WAAS,KAAKI,IAAG;AAAE,IAAAA,GAAE,iBAAiB3B,WAAU,QAAQ,QAAQ2B,GAAE,MAAM,CAAC,EAAE,KAAK,SAAS,MAAM,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,GAAGA,EAAC;AAAA,EAAG;AACvH,WAAS,QAAQ,OAAO;AAAE,WAAO,QAAQ,KAAK;AAAA,EAAG;AACjD,WAAS,OAAO,OAAO;AAAE,WAAO,SAAS,KAAK;AAAA,EAAG;AACjD,WAAS,OAAOG,IAAGI,IAAG;AAAE,QAAIJ,GAAEI,EAAC,GAAG,EAAE,MAAM,GAAG,EAAE,OAAQ,QAAO,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,EAAG;AACrF;AAEO,SAASrC,kBAAiBsC,IAAG;AAChC,MAAIV,IAAGD;AACP,SAAOC,KAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,SAAS,SAAUF,IAAG;AAAE,UAAMA;AAAA,EAAG,CAAC,GAAG,KAAK,QAAQ,GAAGE,GAAE,OAAO,QAAQ,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AAC1I,WAAS,KAAKQ,IAAGH,IAAG;AAAE,IAAAL,GAAEQ,EAAC,IAAIE,GAAEF,EAAC,IAAI,SAAUC,IAAG;AAAE,cAAQV,KAAI,CAACA,MAAK,EAAE,OAAOxB,SAAQmC,GAAEF,EAAC,EAAEC,EAAC,CAAC,GAAG,MAAMD,OAAM,SAAS,IAAIH,KAAIA,GAAEI,EAAC,IAAIA;AAAA,IAAG,IAAIJ;AAAA,EAAG;AAClJ;AAEO,SAAS/B,eAAcoC,IAAG;AAC7B,MAAI,CAAC,OAAO,cAAe,OAAM,IAAI,UAAU,sCAAsC;AACrF,MAAIC,KAAID,GAAE,OAAO,aAAa,GAAGV;AACjC,SAAOW,KAAIA,GAAE,KAAKD,EAAC,KAAKA,KAAI,OAAOjB,cAAa,aAAaA,UAASiB,EAAC,IAAIA,GAAE,OAAO,QAAQ,EAAE,GAAGV,KAAI,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAGA,GAAE,OAAO,aAAa,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,GAAGA;AAC9M,WAAS,KAAKQ,IAAG;AAAE,IAAAR,GAAEQ,EAAC,IAAIE,GAAEF,EAAC,KAAK,SAAUC,IAAG;AAAE,aAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAAE,QAAAA,KAAIC,GAAEF,EAAC,EAAEC,EAAC,GAAG,OAAO,SAAS,QAAQA,GAAE,MAAMA,GAAE,KAAK;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAG;AAC/J,WAAS,OAAO,SAAS,QAAQf,IAAGe,IAAG;AAAE,YAAQ,QAAQA,EAAC,EAAE,KAAK,SAASA,IAAG;AAAE,cAAQ,EAAE,OAAOA,IAAG,MAAMf,GAAE,CAAC;AAAA,IAAG,GAAG,MAAM;AAAA,EAAG;AAC/H;AAEO,SAASR,sBAAqB,QAAQ,KAAK;AAC9C,MAAI,OAAO,gBAAgB;AAAE,WAAO,eAAe,QAAQ,OAAO,EAAE,OAAO,IAAI,CAAC;AAAA,EAAG,OAAO;AAAE,WAAO,MAAM;AAAA,EAAK;AAC9G,SAAO;AACX;AAEO,SAASD,cAAa,KAAK;AAC9B,MAAI,OAAO,IAAI,WAAY,QAAO;AAClC,MAAI,SAAS,CAAC;AACd,MAAI,OAAO;AAAM,aAAS2B,MAAK,IAAK,KAAI,OAAO,eAAe,KAAK,KAAKA,EAAC,EAAG,QAAOA,EAAC,IAAI,IAAIA,EAAC;AAAA;AAC7F,SAAO,UAAU;AACjB,SAAO;AACX;AAEO,SAAS5B,iBAAgB,KAAK;AACjC,SAAQ,OAAO,IAAI,aAAc,MAAM,EAAE,SAAS,IAAI;AAC1D;AAEO,SAASP,wBAAuB,UAAU,YAAY;AACzD,MAAI,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC3B,UAAM,IAAI,UAAU,gDAAgD;AAAA,EACxE;AACA,SAAO,WAAW,IAAI,QAAQ;AAClC;AAEO,SAASC,wBAAuB,UAAU,YAAY,OAAO;AAChE,MAAI,CAAC,WAAW,IAAI,QAAQ,GAAG;AAC3B,UAAM,IAAI,UAAU,gDAAgD;AAAA,EACxE;AACA,aAAW,IAAI,UAAU,KAAK;AAC9B,SAAO;AACX;AAzNA,IAgBIkB,gBAaOzB;AA7BX,IAAA8C,kBAAA;AAAA;AAgBA,IAAIrB,iBAAgB,SAASF,IAAGC,IAAG;AAC/B,MAAAC,iBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUF,IAAGC,IAAG;AAAE,QAAAD,GAAE,YAAYC;AAAA,MAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,iBAASI,MAAKJ,GAAG,KAAIA,GAAE,eAAeI,EAAC,EAAG,CAAAL,GAAEK,EAAC,IAAIJ,GAAEI,EAAC;AAAA,MAAG;AAC7E,aAAOH,eAAcF,IAAGC,EAAC;AAAA,IAC7B;AAQO,IAAIxB,YAAW,WAAW;AAC7B,MAAAA,YAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,iBAAS0B,IAAGG,KAAI,GAAGQ,KAAI,UAAU,QAAQR,KAAIQ,IAAGR,MAAK;AACjD,UAAAH,KAAI,UAAUG,EAAC;AACf,mBAASD,MAAKF,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAGE,EAAC,EAAG,GAAEA,EAAC,IAAIF,GAAEE,EAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO5B,UAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAAA;AAAA;;;;;;;;ACtCA,aAAgB,kBAAe;AAE7B,cAAO,eAAM,QAAN,eAAM,SAAA,SAAN,WAAQ,YAAU,eAAM,QAAN,eAAM,SAAA,SAAN,WAAQ,aAAY,CAAA;IAC/C;AAHA,YAAA,kBAAA;AAKA,aAAgB,kBAAe;AAC7B,YAAM,gBAAgB,gBAAe;AAErC,aAAO,cAAc,UAAU,cAAc;IAC/C;AAJA,YAAA,kBAAA;AAMA,aAAgB,2BAAwB;AACtC,aAAO,CAAC,CAAC,gBAAe,KAAM,CAAC,CAAC,gBAAe;IACjD;AAFA,YAAA,2BAAA;;;;;;;;;;ACXA,aAAgB,gBAAa;AAC3B,aACE,OAAO,aAAa,eACpB,OAAO,cAAc,eACrB,UAAU,YAAY;IAE1B;AANA,YAAA,gBAAA;AAQA,aAAgB+C,UAAM;AACpB,aACE,OAAO,YAAY,eACnB,OAAO,QAAQ,aAAa,eAC5B,OAAO,QAAQ,SAAS,SAAS;IAErC;AANA,YAAA,SAAAA;AAQA,aAAgB,YAAS;AACvB,aAAO,CAAC,cAAa,KAAM,CAACA,QAAM;IACpC;AAFA,YAAA,YAAA;;;;;;;;;;AChBA,YAAA,aAAA,kBAAA,OAAA;AACA,YAAA,aAAA,eAAA,OAAA;;;;;ACDA;AAAA;AACA,QAAI,aACH,OAAO,eAAe,eAAe,cACrC,OAAO,SAAS,eAAe,QAC/B,OAAO,eAAW,eAAe;AAElC,QAAI,iBAAkB,WAAY;AAClC,eAAS,IAAI;AACb,aAAK,QAAQ;AACb,aAAK,eAAe,WAAW;AAAA,MAC/B;AACA,QAAE,YAAY;AACd,aAAO,IAAI,EAAE;AAAA,IACb,EAAG;AAGH,KAAC,SAASC,aAAY;AAEtB,UAAI,aAAc,SAAUC,UAAS;AAGnC,YAAIC,KACD,OAAOF,gBAAe,eAAeA,eACrC,OAAO,SAAS,eAAe;AAAA,QAE/B,OAAOA,gBAAW,eAAeA,eAClC,CAAC;AAEH,YAAI,UAAU;AAAA,UACZ,cAAc,qBAAqBE;AAAA,UACnC,UAAU,YAAYA,MAAK,cAAc;AAAA,UACzC,MACE,gBAAgBA,MAChB,UAAUA,MACT,WAAW;AACV,gBAAI;AACF,kBAAI,KAAK;AACT,qBAAO;AAAA,YACT,SAASC,IAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF,EAAG;AAAA,UACL,UAAU,cAAcD;AAAA,UACxB,aAAa,iBAAiBA;AAAA,QAChC;AAEA,iBAAS,WAAW,KAAK;AACvB,iBAAO,OAAO,SAAS,UAAU,cAAc,GAAG;AAAA,QACpD;AAEA,YAAI,QAAQ,aAAa;AACvB,cAAI,cAAc;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAEA,cAAI,oBACF,YAAY,UACZ,SAAS,KAAK;AACZ,mBAAO,OAAO,YAAY,QAAQ,OAAO,UAAU,SAAS,KAAK,GAAG,CAAC,IAAI;AAAA,UAC3E;AAAA,QACJ;AAEA,iBAAS,cAAcE,OAAM;AAC3B,cAAI,OAAOA,UAAS,UAAU;AAC5B,YAAAA,QAAO,OAAOA,KAAI;AAAA,UACpB;AACA,cAAI,6BAA6B,KAAKA,KAAI,KAAKA,UAAS,IAAI;AAC1D,kBAAM,IAAI,UAAU,8CAA8CA,QAAO,GAAG;AAAA,UAC9E;AACA,iBAAOA,MAAK,YAAY;AAAA,QAC1B;AAEA,iBAAS,eAAe,OAAO;AAC7B,cAAI,OAAO,UAAU,UAAU;AAC7B,oBAAQ,OAAO,KAAK;AAAA,UACtB;AACA,iBAAO;AAAA,QACT;AAGA,iBAAS,YAAY,OAAO;AAC1B,cAAI,WAAW;AAAA,YACb,MAAM,WAAW;AACf,kBAAI,QAAQ,MAAM,MAAM;AACxB,qBAAO,EAAC,MAAM,UAAU,QAAW,MAAY;AAAA,YACjD;AAAA,UACF;AAEA,cAAI,QAAQ,UAAU;AACpB,qBAAS,OAAO,QAAQ,IAAI,WAAW;AACrC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,QAAQ,SAAS;AACxB,eAAK,MAAM,CAAC;AAEZ,cAAI,mBAAmB,SAAS;AAC9B,oBAAQ,QAAQ,SAAS,OAAOA,OAAM;AACpC,mBAAK,OAAOA,OAAM,KAAK;AAAA,YACzB,GAAG,IAAI;AAAA,UACT,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,oBAAQ,QAAQ,SAAS,QAAQ;AAC/B,kBAAI,OAAO,UAAU,GAAG;AACtB,sBAAM,IAAI,UAAU,wEAAwE,OAAO,MAAM;AAAA,cAC3G;AACA,mBAAK,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,YAClC,GAAG,IAAI;AAAA,UACT,WAAW,SAAS;AAClB,mBAAO,oBAAoB,OAAO,EAAE,QAAQ,SAASA,OAAM;AACzD,mBAAK,OAAOA,OAAM,QAAQA,KAAI,CAAC;AAAA,YACjC,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AAEA,gBAAQ,UAAU,SAAS,SAASA,OAAM,OAAO;AAC/C,UAAAA,QAAO,cAAcA,KAAI;AACzB,kBAAQ,eAAe,KAAK;AAC5B,cAAI,WAAW,KAAK,IAAIA,KAAI;AAC5B,eAAK,IAAIA,KAAI,IAAI,WAAW,WAAW,OAAO,QAAQ;AAAA,QACxD;AAEA,gBAAQ,UAAU,QAAQ,IAAI,SAASA,OAAM;AAC3C,iBAAO,KAAK,IAAI,cAAcA,KAAI,CAAC;AAAA,QACrC;AAEA,gBAAQ,UAAU,MAAM,SAASA,OAAM;AACrC,UAAAA,QAAO,cAAcA,KAAI;AACzB,iBAAO,KAAK,IAAIA,KAAI,IAAI,KAAK,IAAIA,KAAI,IAAI;AAAA,QAC3C;AAEA,gBAAQ,UAAU,MAAM,SAASA,OAAM;AACrC,iBAAO,KAAK,IAAI,eAAe,cAAcA,KAAI,CAAC;AAAA,QACpD;AAEA,gBAAQ,UAAU,MAAM,SAASA,OAAM,OAAO;AAC5C,eAAK,IAAI,cAAcA,KAAI,CAAC,IAAI,eAAe,KAAK;AAAA,QACtD;AAEA,gBAAQ,UAAU,UAAU,SAAS,UAAU,SAAS;AACtD,mBAASA,SAAQ,KAAK,KAAK;AACzB,gBAAI,KAAK,IAAI,eAAeA,KAAI,GAAG;AACjC,uBAAS,KAAK,SAAS,KAAK,IAAIA,KAAI,GAAGA,OAAM,IAAI;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,UAAU,OAAO,WAAW;AAClC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAOA,OAAM;AACjC,kBAAM,KAAKA,KAAI;AAAA,UACjB,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,gBAAQ,UAAU,SAAS,WAAW;AACpC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO;AAC3B,kBAAM,KAAK,KAAK;AAAA,UAClB,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,gBAAQ,UAAU,UAAU,WAAW;AACrC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAOA,OAAM;AACjC,kBAAM,KAAK,CAACA,OAAM,KAAK,CAAC;AAAA,UAC1B,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,YAAI,QAAQ,UAAU;AACpB,kBAAQ,UAAU,OAAO,QAAQ,IAAI,QAAQ,UAAU;AAAA,QACzD;AAEA,iBAAS,SAAS,MAAM;AACtB,cAAI,KAAK,QAAS;AAClB,cAAI,KAAK,UAAU;AACjB,mBAAO,QAAQ,OAAO,IAAI,UAAU,cAAc,CAAC;AAAA,UACrD;AACA,eAAK,WAAW;AAAA,QAClB;AAEA,iBAAS,gBAAgB,QAAQ;AAC/B,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,mBAAO,SAAS,WAAW;AACzB,sBAAQ,OAAO,MAAM;AAAA,YACvB;AACA,mBAAO,UAAU,WAAW;AAC1B,qBAAO,OAAO,KAAK;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH;AAEA,iBAAS,sBAAsB,MAAM;AACnC,cAAI,SAAS,IAAI,WAAW;AAC5B,cAAI,UAAU,gBAAgB,MAAM;AACpC,iBAAO,kBAAkB,IAAI;AAC7B,iBAAO;AAAA,QACT;AAEA,iBAAS,eAAe,MAAM;AAC5B,cAAI,SAAS,IAAI,WAAW;AAC5B,cAAI,UAAU,gBAAgB,MAAM;AACpC,cAAI,QAAQ,2BAA2B,KAAK,KAAK,IAAI;AACrD,cAAI,WAAW,QAAQ,MAAM,CAAC,IAAI;AAClC,iBAAO,WAAW,MAAM,QAAQ;AAChC,iBAAO;AAAA,QACT;AAEA,iBAAS,sBAAsB,KAAK;AAClC,cAAI,OAAO,IAAI,WAAW,GAAG;AAC7B,cAAI,QAAQ,IAAI,MAAM,KAAK,MAAM;AAEjC,mBAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,kBAAMA,EAAC,IAAI,OAAO,aAAa,KAAKA,EAAC,CAAC;AAAA,UACxC;AACA,iBAAO,MAAM,KAAK,EAAE;AAAA,QACtB;AAEA,iBAAS,YAAY,KAAK;AACxB,cAAI,IAAI,OAAO;AACb,mBAAO,IAAI,MAAM,CAAC;AAAA,UACpB,OAAO;AACL,gBAAI,OAAO,IAAI,WAAW,IAAI,UAAU;AACxC,iBAAK,IAAI,IAAI,WAAW,GAAG,CAAC;AAC5B,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAEA,iBAAS,OAAO;AACd,eAAK,WAAW;AAEhB,eAAK,YAAY,SAAS,MAAM;AAY9B,iBAAK,WAAW,KAAK;AACrB,iBAAK,YAAY;AACjB,gBAAI,CAAC,MAAM;AACT,mBAAK,UAAU;AACf,mBAAK,YAAY;AAAA,YACnB,WAAW,OAAO,SAAS,UAAU;AACnC,mBAAK,YAAY;AAAA,YACnB,WAAW,QAAQ,QAAQ,KAAK,UAAU,cAAc,IAAI,GAAG;AAC7D,mBAAK,YAAY;AAAA,YACnB,WAAW,QAAQ,YAAY,SAAS,UAAU,cAAc,IAAI,GAAG;AACrE,mBAAK,gBAAgB;AAAA,YACvB,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,mBAAK,YAAY,KAAK,SAAS;AAAA,YACjC,WAAW,QAAQ,eAAe,QAAQ,QAAQ,WAAW,IAAI,GAAG;AAClE,mBAAK,mBAAmB,YAAY,KAAK,MAAM;AAE/C,mBAAK,YAAY,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC;AAAA,YACnD,WAAW,QAAQ,gBAAgB,YAAY,UAAU,cAAc,IAAI,KAAK,kBAAkB,IAAI,IAAI;AACxG,mBAAK,mBAAmB,YAAY,IAAI;AAAA,YAC1C,OAAO;AACL,mBAAK,YAAY,OAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,YAC7D;AAEA,gBAAI,CAAC,KAAK,QAAQ,IAAI,cAAc,GAAG;AACrC,kBAAI,OAAO,SAAS,UAAU;AAC5B,qBAAK,QAAQ,IAAI,gBAAgB,0BAA0B;AAAA,cAC7D,WAAW,KAAK,aAAa,KAAK,UAAU,MAAM;AAChD,qBAAK,QAAQ,IAAI,gBAAgB,KAAK,UAAU,IAAI;AAAA,cACtD,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,qBAAK,QAAQ,IAAI,gBAAgB,iDAAiD;AAAA,cACpF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,QAAQ,MAAM;AAChB,iBAAK,OAAO,WAAW;AACrB,kBAAI,WAAW,SAAS,IAAI;AAC5B,kBAAI,UAAU;AACZ,uBAAO;AAAA,cACT;AAEA,kBAAI,KAAK,WAAW;AAClB,uBAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,cACvC,WAAW,KAAK,kBAAkB;AAChC,uBAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC;AAAA,cAC1D,WAAW,KAAK,eAAe;AAC7B,sBAAM,IAAI,MAAM,sCAAsC;AAAA,cACxD,OAAO;AACL,uBAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AAEA,eAAK,cAAc,WAAW;AAC5B,gBAAI,KAAK,kBAAkB;AACzB,kBAAI,aAAa,SAAS,IAAI;AAC9B,kBAAI,YAAY;AACd,uBAAO;AAAA,cACT,WAAW,YAAY,OAAO,KAAK,gBAAgB,GAAG;AACpD,uBAAO,QAAQ;AAAA,kBACb,KAAK,iBAAiB,OAAO;AAAA,oBAC3B,KAAK,iBAAiB;AAAA,oBACtB,KAAK,iBAAiB,aAAa,KAAK,iBAAiB;AAAA,kBAC3D;AAAA,gBACF;AAAA,cACF,OAAO;AACL,uBAAO,QAAQ,QAAQ,KAAK,gBAAgB;AAAA,cAC9C;AAAA,YACF,WAAW,QAAQ,MAAM;AACvB,qBAAO,KAAK,KAAK,EAAE,KAAK,qBAAqB;AAAA,YAC/C,OAAO;AACL,oBAAM,IAAI,MAAM,+BAA+B;AAAA,YACjD;AAAA,UACF;AAEA,eAAK,OAAO,WAAW;AACrB,gBAAI,WAAW,SAAS,IAAI;AAC5B,gBAAI,UAAU;AACZ,qBAAO;AAAA,YACT;AAEA,gBAAI,KAAK,WAAW;AAClB,qBAAO,eAAe,KAAK,SAAS;AAAA,YACtC,WAAW,KAAK,kBAAkB;AAChC,qBAAO,QAAQ,QAAQ,sBAAsB,KAAK,gBAAgB,CAAC;AAAA,YACrE,WAAW,KAAK,eAAe;AAC7B,oBAAM,IAAI,MAAM,sCAAsC;AAAA,YACxD,OAAO;AACL,qBAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,YACvC;AAAA,UACF;AAEA,cAAI,QAAQ,UAAU;AACpB,iBAAK,WAAW,WAAW;AACzB,qBAAO,KAAK,KAAK,EAAE,KAAKC,OAAM;AAAA,YAChC;AAAA,UACF;AAEA,eAAK,OAAO,WAAW;AACrB,mBAAO,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK;AAAA,UACpC;AAEA,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,CAAC,WAAW,UAAU,OAAO,QAAQ,WAAW,SAAS,QAAQ,OAAO,OAAO;AAE7F,iBAAS,gBAAgB,QAAQ;AAC/B,cAAI,UAAU,OAAO,YAAY;AACjC,iBAAO,QAAQ,QAAQ,OAAO,IAAI,KAAK,UAAU;AAAA,QACnD;AAEA,iBAAS,QAAQ,OAAO,SAAS;AAC/B,cAAI,EAAE,gBAAgB,UAAU;AAC9B,kBAAM,IAAI,UAAU,4FAA4F;AAAA,UAClH;AAEA,oBAAU,WAAW,CAAC;AACtB,cAAI,OAAO,QAAQ;AAEnB,cAAI,iBAAiB,SAAS;AAC5B,gBAAI,MAAM,UAAU;AAClB,oBAAM,IAAI,UAAU,cAAc;AAAA,YACpC;AACA,iBAAK,MAAM,MAAM;AACjB,iBAAK,cAAc,MAAM;AACzB,gBAAI,CAAC,QAAQ,SAAS;AACpB,mBAAK,UAAU,IAAI,QAAQ,MAAM,OAAO;AAAA,YAC1C;AACA,iBAAK,SAAS,MAAM;AACpB,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,MAAM;AACpB,gBAAI,CAAC,QAAQ,MAAM,aAAa,MAAM;AACpC,qBAAO,MAAM;AACb,oBAAM,WAAW;AAAA,YACnB;AAAA,UACF,OAAO;AACL,iBAAK,MAAM,OAAO,KAAK;AAAA,UACzB;AAEA,eAAK,cAAc,QAAQ,eAAe,KAAK,eAAe;AAC9D,cAAI,QAAQ,WAAW,CAAC,KAAK,SAAS;AACpC,iBAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAAA,UAC5C;AACA,eAAK,SAAS,gBAAgB,QAAQ,UAAU,KAAK,UAAU,KAAK;AACpE,eAAK,OAAO,QAAQ,QAAQ,KAAK,QAAQ;AACzC,eAAK,SAAS,QAAQ,UAAU,KAAK,UAAW,WAAY;AAC1D,gBAAI,qBAAqBJ,IAAG;AAC1B,kBAAI,OAAO,IAAI,gBAAgB;AAC/B,qBAAO,KAAK;AAAA,YACd;AAAA,UACF,EAAE;AACF,eAAK,WAAW;AAEhB,eAAK,KAAK,WAAW,SAAS,KAAK,WAAW,WAAW,MAAM;AAC7D,kBAAM,IAAI,UAAU,2CAA2C;AAAA,UACjE;AACA,eAAK,UAAU,IAAI;AAEnB,cAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,gBAAI,QAAQ,UAAU,cAAc,QAAQ,UAAU,YAAY;AAEhE,kBAAI,gBAAgB;AACpB,kBAAI,cAAc,KAAK,KAAK,GAAG,GAAG;AAEhC,qBAAK,MAAM,KAAK,IAAI,QAAQ,eAAe,UAAS,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAAA,cAC1E,OAAO;AAEL,oBAAI,gBAAgB;AACpB,qBAAK,QAAQ,cAAc,KAAK,KAAK,GAAG,IAAI,MAAM,OAAO,QAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,cACrF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,UAAU,QAAQ,WAAW;AACnC,iBAAO,IAAI,QAAQ,MAAM,EAAC,MAAM,KAAK,UAAS,CAAC;AAAA,QACjD;AAEA,iBAASI,QAAO,MAAM;AACpB,cAAI,OAAO,IAAI,SAAS;AACxB,eACG,KAAK,EACL,MAAM,GAAG,EACT,QAAQ,SAAS,OAAO;AACvB,gBAAI,OAAO;AACT,kBAAI,QAAQ,MAAM,MAAM,GAAG;AAC3B,kBAAIF,QAAO,MAAM,MAAM,EAAE,QAAQ,OAAO,GAAG;AAC3C,kBAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC9C,mBAAK,OAAO,mBAAmBA,KAAI,GAAG,mBAAmB,KAAK,CAAC;AAAA,YACjE;AAAA,UACF,CAAC;AACH,iBAAO;AAAA,QACT;AAEA,iBAAS,aAAa,YAAY;AAChC,cAAI,UAAU,IAAI,QAAQ;AAG1B,cAAI,sBAAsB,WAAW,QAAQ,gBAAgB,GAAG;AAIhE,8BACG,MAAM,IAAI,EACV,IAAI,SAAS,QAAQ;AACpB,mBAAO,OAAO,QAAQ,IAAI,MAAM,IAAI,OAAO,OAAO,GAAG,OAAO,MAAM,IAAI;AAAA,UACxE,CAAC,EACA,QAAQ,SAAS,MAAM;AACtB,gBAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,gBAAI,MAAM,MAAM,MAAM,EAAE,KAAK;AAC7B,gBAAI,KAAK;AACP,kBAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,KAAK;AACjC,kBAAI;AACF,wBAAQ,OAAO,KAAK,KAAK;AAAA,cAC3B,SAAS,OAAO;AACd,wBAAQ,KAAK,cAAc,MAAM,OAAO;AAAA,cAC1C;AAAA,YACF;AAAA,UACF,CAAC;AACH,iBAAO;AAAA,QACT;AAEA,aAAK,KAAK,QAAQ,SAAS;AAE3B,iBAAS,SAAS,UAAU,SAAS;AACnC,cAAI,EAAE,gBAAgB,WAAW;AAC/B,kBAAM,IAAI,UAAU,4FAA4F;AAAA,UAClH;AACA,cAAI,CAAC,SAAS;AACZ,sBAAU,CAAC;AAAA,UACb;AAEA,eAAK,OAAO;AACZ,eAAK,SAAS,QAAQ,WAAW,SAAY,MAAM,QAAQ;AAC3D,cAAI,KAAK,SAAS,OAAO,KAAK,SAAS,KAAK;AAC1C,kBAAM,IAAI,WAAW,0FAA0F;AAAA,UACjH;AACA,eAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAC9C,eAAK,aAAa,QAAQ,eAAe,SAAY,KAAK,KAAK,QAAQ;AACvE,eAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAC1C,eAAK,MAAM,QAAQ,OAAO;AAC1B,eAAK,UAAU,QAAQ;AAAA,QACzB;AAEA,aAAK,KAAK,SAAS,SAAS;AAE5B,iBAAS,UAAU,QAAQ,WAAW;AACpC,iBAAO,IAAI,SAAS,KAAK,WAAW;AAAA,YAClC,QAAQ,KAAK;AAAA,YACb,YAAY,KAAK;AAAA,YACjB,SAAS,IAAI,QAAQ,KAAK,OAAO;AAAA,YACjC,KAAK,KAAK;AAAA,UACZ,CAAC;AAAA,QACH;AAEA,iBAAS,QAAQ,WAAW;AAC1B,cAAI,WAAW,IAAI,SAAS,MAAM,EAAC,QAAQ,KAAK,YAAY,GAAE,CAAC;AAC/D,mBAAS,KAAK;AACd,mBAAS,SAAS;AAClB,mBAAS,OAAO;AAChB,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAE/C,iBAAS,WAAW,SAAS,KAAK,QAAQ;AACxC,cAAI,iBAAiB,QAAQ,MAAM,MAAM,IAAI;AAC3C,kBAAM,IAAI,WAAW,qBAAqB;AAAA,UAC5C;AAEA,iBAAO,IAAI,SAAS,MAAM,EAAC,QAAgB,SAAS,EAAC,UAAU,IAAG,EAAC,CAAC;AAAA,QACtE;AAEA,QAAAH,SAAQ,eAAeC,GAAE;AACzB,YAAI;AACF,cAAID,SAAQ,aAAa;AAAA,QAC3B,SAAS,KAAK;AACZ,UAAAA,SAAQ,eAAe,SAAS,SAASG,OAAM;AAC7C,iBAAK,UAAU;AACf,iBAAK,OAAOA;AACZ,gBAAI,QAAQ,MAAM,OAAO;AACzB,iBAAK,QAAQ,MAAM;AAAA,UACrB;AACA,UAAAH,SAAQ,aAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AAC9D,UAAAA,SAAQ,aAAa,UAAU,cAAcA,SAAQ;AAAA,QACvD;AAEA,iBAAS,MAAM,OAAO,MAAM;AAC1B,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,gBAAI,UAAU,IAAI,QAAQ,OAAO,IAAI;AAErC,gBAAI,QAAQ,UAAU,QAAQ,OAAO,SAAS;AAC5C,qBAAO,OAAO,IAAIA,SAAQ,aAAa,WAAW,YAAY,CAAC;AAAA,YACjE;AAEA,gBAAI,MAAM,IAAI,eAAe;AAE7B,qBAAS,WAAW;AAClB,kBAAI,MAAM;AAAA,YACZ;AAEA,gBAAI,SAAS,WAAW;AACtB,kBAAI,UAAU;AAAA,gBACZ,YAAY,IAAI;AAAA,gBAChB,SAAS,aAAa,IAAI,sBAAsB,KAAK,EAAE;AAAA,cACzD;AAGA,kBAAI,QAAQ,IAAI,QAAQ,SAAS,MAAM,MAAM,IAAI,SAAS,OAAO,IAAI,SAAS,MAAM;AAClF,wBAAQ,SAAS;AAAA,cACnB,OAAO;AACL,wBAAQ,SAAS,IAAI;AAAA,cACvB;AACA,sBAAQ,MAAM,iBAAiB,MAAM,IAAI,cAAc,QAAQ,QAAQ,IAAI,eAAe;AAC1F,kBAAI,OAAO,cAAc,MAAM,IAAI,WAAW,IAAI;AAClD,yBAAW,WAAW;AACpB,wBAAQ,IAAI,SAAS,MAAM,OAAO,CAAC;AAAA,cACrC,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,UAAU,WAAW;AACvB,yBAAW,WAAW;AACpB,uBAAO,IAAI,UAAU,wBAAwB,CAAC;AAAA,cAChD,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,YAAY,WAAW;AACzB,yBAAW,WAAW;AACpB,uBAAO,IAAI,UAAU,2BAA2B,CAAC;AAAA,cACnD,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,UAAU,WAAW;AACvB,yBAAW,WAAW;AACpB,uBAAO,IAAIA,SAAQ,aAAa,WAAW,YAAY,CAAC;AAAA,cAC1D,GAAG,CAAC;AAAA,YACN;AAEA,qBAAS,OAAO,KAAK;AACnB,kBAAI;AACF,uBAAO,QAAQ,MAAMC,GAAE,SAAS,OAAOA,GAAE,SAAS,OAAO;AAAA,cAC3D,SAASC,IAAG;AACV,uBAAO;AAAA,cACT;AAAA,YACF;AAEA,gBAAI,KAAK,QAAQ,QAAQ,OAAO,QAAQ,GAAG,GAAG,IAAI;AAElD,gBAAI,QAAQ,gBAAgB,WAAW;AACrC,kBAAI,kBAAkB;AAAA,YACxB,WAAW,QAAQ,gBAAgB,QAAQ;AACzC,kBAAI,kBAAkB;AAAA,YACxB;AAEA,gBAAI,kBAAkB,KAAK;AACzB,kBAAI,QAAQ,MAAM;AAChB,oBAAI,eAAe;AAAA,cACrB,WACE,QAAQ,aACR;AACA,oBAAI,eAAe;AAAA,cACrB;AAAA,YACF;AAEA,gBAAI,QAAQ,OAAO,KAAK,YAAY,YAAY,EAAE,KAAK,mBAAmB,WAAYD,GAAE,WAAW,KAAK,mBAAmBA,GAAE,UAAW;AACtI,kBAAI,QAAQ,CAAC;AACb,qBAAO,oBAAoB,KAAK,OAAO,EAAE,QAAQ,SAASE,OAAM;AAC9D,sBAAM,KAAK,cAAcA,KAAI,CAAC;AAC9B,oBAAI,iBAAiBA,OAAM,eAAe,KAAK,QAAQA,KAAI,CAAC,CAAC;AAAA,cAC/D,CAAC;AACD,sBAAQ,QAAQ,QAAQ,SAAS,OAAOA,OAAM;AAC5C,oBAAI,MAAM,QAAQA,KAAI,MAAM,IAAI;AAC9B,sBAAI,iBAAiBA,OAAM,KAAK;AAAA,gBAClC;AAAA,cACF,CAAC;AAAA,YACH,OAAO;AACL,sBAAQ,QAAQ,QAAQ,SAAS,OAAOA,OAAM;AAC5C,oBAAI,iBAAiBA,OAAM,KAAK;AAAA,cAClC,CAAC;AAAA,YACH;AAEA,gBAAI,QAAQ,QAAQ;AAClB,sBAAQ,OAAO,iBAAiB,SAAS,QAAQ;AAEjD,kBAAI,qBAAqB,WAAW;AAElC,oBAAI,IAAI,eAAe,GAAG;AACxB,0BAAQ,OAAO,oBAAoB,SAAS,QAAQ;AAAA,gBACtD;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,KAAK,OAAO,QAAQ,cAAc,cAAc,OAAO,QAAQ,SAAS;AAAA,UAC9E,CAAC;AAAA,QACH;AAEA,cAAM,WAAW;AAEjB,YAAI,CAACF,GAAE,OAAO;AACZ,UAAAA,GAAE,QAAQ;AACV,UAAAA,GAAE,UAAU;AACZ,UAAAA,GAAE,UAAU;AACZ,UAAAA,GAAE,WAAW;AAAA,QACf;AAEA,QAAAD,SAAQ,UAAU;AAClB,QAAAA,SAAQ,UAAU;AAClB,QAAAA,SAAQ,WAAW;AACnB,QAAAA,SAAQ,QAAQ;AAEhB,eAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,eAAOA;AAAA,MAET,EAAG,CAAC,CAAC;AAAA,IACL,GAAG,cAAc;AAEjB,mBAAe,MAAM,WAAW;AAChC,WAAO,eAAe,MAAM;AAE5B,QAAI,MAAM,WAAW,QAAQ,aAAa;AAC1C,cAAU,IAAI;AACd,YAAQ,UAAU,IAAI;AACtB,YAAQ,QAAQ,IAAI;AACpB,YAAQ,UAAU,IAAI;AACtB,YAAQ,UAAU,IAAI;AACtB,YAAQ,WAAW,IAAI;AACvB,WAAO,UAAU;AAAA;AAAA;;;AC7qBjB,IAAAM,mBAAA;AAAA;AAAA;AAEA,WAAO,UAAU,WAAY;AAC3B,YAAM,IAAI;AAAA,QACR;AAAA,MAEF;AAAA,IACF;AAAA;AAAA;;;ACPA,IAAI,gBAAgD,SAAUC,KAAIC,OAAM,MAAM;AAC1E,MAAI,QAAQ,UAAU,WAAW,EAAG,UAASC,KAAI,GAAGC,KAAIF,MAAK,QAAQG,KAAIF,KAAIC,IAAGD,MAAK;AACjF,QAAIE,OAAM,EAAEF,MAAKD,QAAO;AACpB,UAAI,CAACG,IAAI,CAAAA,MAAK,MAAM,UAAU,MAAM,KAAKH,OAAM,GAAGC,EAAC;AACnD,MAAAE,IAAGF,EAAC,IAAID,MAAKC,EAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAOF,IAAG,OAAOI,OAAM,MAAM,UAAU,MAAM,KAAKH,KAAI,CAAC;AAC3D;AACA,IAAI;AAAA;AAAA,EAA6B,2BAAY;AACzC,aAASI,aAAYC,OAAMC,UAAS,IAAI;AACpC,WAAK,OAAOD;AACZ,WAAK,UAAUC;AACf,WAAK,KAAK;AACV,WAAK,OAAO;AAAA,IAChB;AACA,WAAOF;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAA0B,2BAAY;AACtC,aAASG,UAASC,UAAS;AACvB,WAAK,UAAUA;AACf,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,KAAK,QAAQ;AAAA,IACtB;AACA,WAAOD;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAqC,2BAAY;AACjD,aAASE,qBAAoBC,OAAMC,UAAS,IAAI,KAAK;AACjD,WAAK,OAAOD;AACZ,WAAK,UAAUC;AACf,WAAK,KAAK;AACV,WAAK,MAAM;AACX,WAAK,OAAO;AAAA,IAChB;AACA,WAAOF;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAyB,2BAAY;AACrC,aAASG,WAAU;AACf,WAAK,OAAO;AACZ,WAAK,MAAM;AACX,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,KAAK;AAAA,IACd;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI;AAAA;AAAA,EAAiC,2BAAY;AAC7C,aAASC,mBAAkB;AACvB,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,UAAU;AACf,WAAK,KAAK;AAAA,IACd;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAGF,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,yBAAyB;AAC7B,IAAI,iBAAiB;AAAA,EACjB,CAAC,OAAO,wBAAwB;AAAA,EAChC,CAAC,QAAQ,mBAAmB;AAAA,EAC5B,CAAC,YAAY,qBAAqB;AAAA,EAClC,CAAC,iBAAiB,wBAAwB;AAAA,EAC1C,CAAC,aAAa,uBAAuB;AAAA,EACrC,CAAC,WAAW,4BAA4B;AAAA,EACxC,CAAC,QAAQ,uBAAuB;AAAA,EAChC,CAAC,QAAQ,0BAA0B;AAAA,EACnC,CAAC,UAAU,2BAA2B;AAAA,EACtC,CAAC,iBAAiB,mBAAmB;AAAA,EACrC;AAAA,IACI;AAAA,IACA;AAAA,EACJ;AAAA,EACA,CAAC,UAAU,kDAAkD;AAAA,EAC7D,CAAC,aAAa,+BAA+B;AAAA,EAC7C,CAAC,SAAS,2BAA2B;AAAA,EACrC,CAAC,WAAW,6BAA6B;AAAA,EACzC,CAAC,SAAS,mBAAmB;AAAA,EAC7B,CAAC,cAAc,iCAAiC;AAAA,EAChD,CAAC,SAAS,2BAA2B;AAAA,EACrC,CAAC,SAAS,yBAAyB;AAAA,EACnC,CAAC,OAAO,kDAAkD;AAAA,EAC1D,CAAC,OAAO,+FAA+F;AAAA,EACvG,CAAC,YAAY,sCAAsC;AAAA,EACnD,CAAC,MAAM,2CAA2C;AAAA,EAClD,CAAC,MAAM,qCAAqC;AAAA,EAC5C,CAAC,MAAM,cAAc;AAAA,EACrB,CAAC,QAAQ,mCAAmC;AAAA,EAC5C,CAAC,WAAW,qBAAqB;AAAA,EACjC,CAAC,OAAO,wCAAwC;AAAA,EAChD,CAAC,UAAU,8BAA8B;AAAA,EACzC,CAAC,YAAY,qBAAqB;AAAA,EAClC,CAAC,aAAa,uBAAuB;AAAA,EACrC,CAAC,eAAe,iCAAiC;AAAA,EACjD,CAAC,eAAe,mCAAmC;AAAA,EACnD,CAAC,QAAQ,oBAAoB;AAAA,EAC7B,CAAC,aAAa,kBAAkB;AACpC;AACA,IAAI,uBAAuB;AAAA,EACvB,CAAC,OAAO,gBAAgB;AAAA,EACxB,CAAC,cAAc,SAAS;AAAA,EACxB,CAAC,iBAAiB,iBAAiB;AAAA,EACnC,CAAC,kBAAkB,UAAU;AAAA,EAC7B,CAAC,aAAa,QAAQ;AAAA,EACtB,CAAC,gBAAgB,OAAO;AAAA,EACxB,CAAC,cAAc,mCAAmC;AAAA,EAClD,CAAC,cAAc,sBAAsB;AAAA,EACrC,CAAC,gBAAgB,iCAAiC;AAAA,EAClD,CAAC,cAAc,+BAA+B;AAAA,EAC9C,CAAC,uBAAuB,kBAAkB;AAAA,EAC1C,CAAC,iBAAiB,kBAAkB;AAAA,EACpC,CAAC,aAAa,kBAAkB;AAAA,EAChC,CAAC,aAAa,kBAAkB;AAAA,EAChC,CAAC,eAAe,kBAAkB;AAAA,EAClC,CAAC,cAAc,mBAAmB;AAAA,EAClC,CAAC,cAAc,YAAY;AAAA,EAC3B,CAAC,cAAc,qDAAqD;AAAA,EACpE,CAAC,YAAY,SAAS;AAAA,EACtB,CAAC,UAAU,OAAO;AAAA,EAClB,CAAC,aAAa,MAAM;AAAA,EACpB,CAAC,SAAS,eAAe;AAAA,EACzB,CAAC,UAAU,2BAA2B;AAAA,EACtC,CAAC,OAAO,KAAK;AAAA,EACb,CAAC,QAAQ,MAAM;AAAA,EACf,CAAC,QAAQ,OAAO;AACpB;AACO,SAAS,OAAO,WAAW;AAC9B,MAAI,CAAC,CAAC,WAAW;AACb,WAAO,eAAe,SAAS;AAAA,EACnC;AACA,MAAI,OAAO,aAAa,eACpB,OAAO,cAAc,eACrB,UAAU,YAAY,eAAe;AACrC,WAAO,IAAI,gBAAgB;AAAA,EAC/B;AACA,MAAI,OAAO,cAAc,aAAa;AAClC,WAAO,eAAe,UAAU,SAAS;AAAA,EAC7C;AACA,SAAO,eAAe;AAC1B;AACA,SAAS,eAAe,IAAI;AAKxB,SAAQ,OAAO,MACX,eAAe,OAAO,SAAU,SAAS,IAAI;AACzC,QAAI,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AACjC,QAAI,SAAS;AACT,aAAO;AAAA,IACX;AACA,QAAI,UAAU,MAAM,KAAK,EAAE;AAC3B,WAAO,CAAC,CAAC,WAAW,CAAC,SAAS,OAAO;AAAA,EACzC,GAAG,KAAK;AAChB;AAKO,SAAS,eAAe,IAAI;AAC/B,MAAI,cAAc,eAAe,EAAE;AACnC,MAAI,CAAC,aAAa;AACd,WAAO;AAAA,EACX;AACA,MAAIC,QAAO,YAAY,CAAC,GAAG,QAAQ,YAAY,CAAC;AAChD,MAAIA,UAAS,aAAa;AACtB,WAAO,IAAI,QAAQ;AAAA,EACvB;AAEA,MAAI,eAAe,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,GAAG,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG,CAAC;AAClF,MAAI,cAAc;AACd,QAAI,aAAa,SAAS,wBAAwB;AAC9C,qBAAe,cAAc,cAAc,CAAC,GAAG,cAAc,IAAI,GAAG,mBAAmB,yBAAyB,aAAa,MAAM,GAAG,IAAI;AAAA,IAC9I;AAAA,EACJ,OACK;AACD,mBAAe,CAAC;AAAA,EACpB;AACA,MAAIC,WAAU,aAAa,KAAK,GAAG;AACnC,MAAI,KAAK,SAAS,EAAE;AACpB,MAAI,iBAAiB,mBAAmB,KAAK,EAAE;AAC/C,MAAI,kBAAkB,eAAe,CAAC,GAAG;AACrC,WAAO,IAAI,oBAAoBD,OAAMC,UAAS,IAAI,eAAe,CAAC,CAAC;AAAA,EACvE;AACA,SAAO,IAAI,YAAYD,OAAMC,UAAS,EAAE;AAC5C;AACO,SAAS,SAAS,IAAI;AACzB,WAAS,KAAK,GAAG,QAAQ,qBAAqB,QAAQ,KAAK,OAAO,MAAM;AACpE,QAAI,KAAK,qBAAqB,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AAC3D,QAAI,QAAQ,MAAM,KAAK,EAAE;AACzB,QAAI,OAAO;AACP,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,iBAAiB;AAC7B,MAAIC,UAAS,OAAO,YAAY,eAAe,QAAQ;AACvD,SAAOA,UAAS,IAAI,SAAS,QAAQ,QAAQ,MAAM,CAAC,CAAC,IAAI;AAC7D;AACA,SAAS,mBAAmB,OAAO;AAC/B,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,KAAK,OAAO,MAAM;AAC/B,WAAO,KAAK,GAAG;AAAA,EACnB;AACA,SAAO;AACX;;;AChNA,SAAS,KAAMC,WAAU;AACvB,MAAIA,UAAS,UAAU,KAAK;AAAE,UAAM,IAAI,UAAU,mBAAmB;AAAA,EAAE;AACvE,QAAM,WAAW,IAAI,WAAW,GAAG;AACnC,WAASC,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACxC,aAASA,EAAC,IAAI;AAAA,EAChB;AACA,WAASC,KAAI,GAAGA,KAAIF,UAAS,QAAQE,MAAK;AACxC,UAAMC,KAAIH,UAAS,OAAOE,EAAC;AAC3B,UAAM,KAAKC,GAAE,WAAW,CAAC;AACzB,QAAI,SAAS,EAAE,MAAM,KAAK;AAAE,YAAM,IAAI,UAAUA,KAAI,eAAe;AAAA,IAAE;AACrE,aAAS,EAAE,IAAID;AAAA,EACjB;AACA,QAAM,OAAOF,UAAS;AACtB,QAAM,SAASA,UAAS,OAAO,CAAC;AAChC,QAAM,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC5C,QAAM,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC7C,WAASI,QAAQ,QAAQ;AAEvB,QAAI,kBAAkB,YAAY;AAAA,IAAE,WAAW,YAAY,OAAO,MAAM,GAAG;AACzE,eAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,IAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,eAAS,WAAW,KAAK,MAAM;AAAA,IACjC;AACA,QAAI,EAAE,kBAAkB,aAAa;AAAE,YAAM,IAAI,UAAU,qBAAqB;AAAA,IAAE;AAClF,QAAI,OAAO,WAAW,GAAG;AAAE,aAAO;AAAA,IAAG;AAErC,QAAI,SAAS;AACb,QAAIC,UAAS;AACb,QAAI,SAAS;AACb,UAAM,OAAO,OAAO;AACpB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,IACF;AAEA,UAAM,QAAS,OAAO,UAAU,UAAU,MAAO;AACjD,UAAM,MAAM,IAAI,WAAW,IAAI;AAE/B,WAAO,WAAW,MAAM;AACtB,UAAI,QAAQ,OAAO,MAAM;AAEzB,UAAIH,KAAI;AACR,eAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAIG,YAAY,QAAQ,IAAK,OAAOH,MAAK;AAChF,iBAAU,MAAM,IAAI,GAAG,MAAO;AAC9B,YAAI,GAAG,IAAK,QAAQ,SAAU;AAC9B,gBAAS,QAAQ,SAAU;AAAA,MAC7B;AACA,UAAI,UAAU,GAAG;AAAE,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAAE;AACrD,MAAAG,UAASH;AACT;AAAA,IACF;AAEA,QAAI,MAAM,OAAOG;AACjB,WAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,IACF;AAEA,QAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,WAAO,MAAM,MAAM,EAAE,KAAK;AAAE,aAAOL,UAAS,OAAO,IAAI,GAAG,CAAC;AAAA,IAAE;AAC7D,WAAO;AAAA,EACT;AACA,WAAS,aAAc,QAAQ;AAC7B,QAAI,OAAO,WAAW,UAAU;AAAE,YAAM,IAAI,UAAU,iBAAiB;AAAA,IAAE;AACzE,QAAI,OAAO,WAAW,GAAG;AAAE,aAAO,IAAI,WAAW;AAAA,IAAE;AACnD,QAAI,MAAM;AAEV,QAAI,SAAS;AACb,QAAIK,UAAS;AACb,WAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,IACF;AAEA,UAAM,QAAU,OAAO,SAAS,OAAO,SAAU,MAAO;AACxD,UAAM,OAAO,IAAI,WAAW,IAAI;AAEhC,WAAO,MAAM,OAAO,QAAQ;AAE1B,YAAM,WAAW,OAAO,WAAW,GAAG;AAEtC,UAAI,WAAW,KAAK;AAAE;AAAA,MAAO;AAE7B,UAAI,QAAQ,SAAS,QAAQ;AAE7B,UAAI,UAAU,KAAK;AAAE;AAAA,MAAO;AAC5B,UAAIH,KAAI;AACR,eAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAIG,YAAY,QAAQ,IAAK,OAAOH,MAAK;AAChF,iBAAU,OAAO,KAAK,GAAG,MAAO;AAChC,aAAK,GAAG,IAAK,QAAQ,QAAS;AAC9B,gBAAS,QAAQ,QAAS;AAAA,MAC5B;AACA,UAAI,UAAU,GAAG;AAAE,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAAE;AACrD,MAAAG,UAASH;AACT;AAAA,IACF;AAEA,QAAI,MAAM,OAAOG;AACjB,WAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,IACF;AACA,UAAM,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAChD,QAAIJ,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,UAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,WAASK,QAAQC,SAAQ;AACvB,UAAM,SAAS,aAAaA,OAAM;AAClC,QAAI,QAAQ;AAAE,aAAO;AAAA,IAAO;AAC5B,UAAM,IAAI,MAAM,aAAa,OAAO,YAAY;AAAA,EAClD;AACA,SAAO;AAAA,IACL,QAAAH;AAAA,IACA;AAAA,IACA,QAAAE;AAAA,EACF;AACF;AACA,IAAO,cAAQ;;;AC1Hf,IAAI,WAAW;AACf,IAAOE,eAAQ,YAAM,QAAQ;A;;;;;ACM7B,IAAM,gBAAgB,UACpB,KAAK,UAAU,MAAM,CAACC,IAAG,UACvB,OAAO,UAAU,WAAW,MAAM,SAAQ,IAAK,MAAM,KAAK;AAU9D,IAAM,YAAY,UAAO;AAMvB,QAAM,0BAA0B;AAChC,QAAM,iBAAiB,KAAK,QAAQ,yBAAyB,WAAa;AAE1E,SAAO,KAAK,MAAM,gBAAgB,CAACA,IAAG,UAAS;AAC7C,UAAM,uBACJ,OAAO,UAAU,YAAY,MAAM,MAAM,QAAQ;AAEnD,QAAI;AACF,aAAO,OAAO,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,CAAC;AAEpD,WAAO;EACT,CAAC;AACH;AAEM,SAAU,cAAuB,OAAa;AAClD,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,MAAM,wCAAwC,OAAO,KAAK,EAAE;;AAExE,MAAI;AACF,WAAO,UAAU,KAAK;WACtB,IAAM;AACN,WAAO;;AAEX;AAEM,SAAU,kBAAkB,OAAU;AAC1C,SAAO,OAAO,UAAU,WAAW,QAAQ,cAAc,KAAK,KAAK;AACrE;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/CO,SAAS,YAAY,OAAO,GAAG;AACpC,MAAI,WAAW,UAAU,QAAQ,WAAW,OAAO,eAAe,MAAM;AACtE,WAAO,WAAW,OAAO,YAAY,IAAI;AAAA,EAC3C;AACA,SAAO,IAAI,WAAW,IAAI;AAC5B;;;ACVO,SAAS,OAAO,QAAQC,SAAQ;AACrC,MAAI,CAACA,SAAQ;AACX,IAAAA,UAAS,OAAO,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,QAAQ,CAAC;AAAA,EAC5D;AACA,QAAM,SAAS,YAAYA,OAAM;AACjC,MAAI,SAAS;AACb,aAAW,OAAO,QAAQ;AACxB,WAAO,IAAI,KAAK,MAAM;AACtB,cAAU,IAAI;AAAA,EAChB;AACA,SAAO;AACT;;;ACZA;AAAA;AAAA;AAAA;;;ACAA,SAASC,MAAKC,WAAUC,OAAM;AAC5B,MAAID,UAAS,UAAU,KAAK;AAC1B,UAAM,IAAI,UAAU,mBAAmB;AAAA,EACzC;AACA,MAAI,WAAW,IAAI,WAAW,GAAG;AACjC,WAASE,KAAI,GAAGA,KAAI,SAAS,QAAQA,MAAK;AACxC,aAASA,EAAC,IAAI;AAAA,EAChB;AACA,WAASC,KAAI,GAAGA,KAAIH,UAAS,QAAQG,MAAK;AACxC,QAAIC,KAAIJ,UAAS,OAAOG,EAAC;AACzB,QAAI,KAAKC,GAAE,WAAW,CAAC;AACvB,QAAI,SAAS,EAAE,MAAM,KAAK;AACxB,YAAM,IAAI,UAAUA,KAAI,eAAe;AAAA,IACzC;AACA,aAAS,EAAE,IAAID;AAAA,EACjB;AACA,MAAI,OAAOH,UAAS;AACpB,MAAI,SAASA,UAAS,OAAO,CAAC;AAC9B,MAAI,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC1C,MAAI,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC3C,WAASK,QAAO,QAAQ;AACtB,QAAI,kBAAkB,WAAW;AAAA,aACxB,YAAY,OAAO,MAAM,GAAG;AACnC,eAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,IAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,eAAS,WAAW,KAAK,MAAM;AAAA,IACjC;AACA,QAAI,EAAE,kBAAkB,aAAa;AACnC,YAAM,IAAI,UAAU,qBAAqB;AAAA,IAC3C;AACA,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAI,SAAS;AACb,QAAIC,UAAS;AACb,QAAI,SAAS;AACb,QAAI,OAAO,OAAO;AAClB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,IACF;AACA,QAAI,QAAQ,OAAO,UAAU,UAAU,MAAM;AAC7C,QAAI,MAAM,IAAI,WAAW,IAAI;AAC7B,WAAO,WAAW,MAAM;AACtB,UAAI,QAAQ,OAAO,MAAM;AACzB,UAAIH,KAAI;AACR,eAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAIG,YAAW,QAAQ,IAAI,OAAOH,MAAK;AAC9E,iBAAS,MAAM,IAAI,GAAG,MAAM;AAC5B,YAAI,GAAG,IAAI,QAAQ,SAAS;AAC5B,gBAAQ,QAAQ,SAAS;AAAA,MAC3B;AACA,UAAI,UAAU,GAAG;AACf,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AACA,MAAAG,UAASH;AACT;AAAA,IACF;AACA,QAAI,MAAM,OAAOG;AACjB,WAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,IACF;AACA,QAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,WAAO,MAAM,MAAM,EAAE,KAAK;AACxB,aAAON,UAAS,OAAO,IAAI,GAAG,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACA,WAAS,aAAa,QAAQ;AAC5B,QAAI,OAAO,WAAW,UAAU;AAC9B,YAAM,IAAI,UAAU,iBAAiB;AAAA,IACvC;AACA,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO,IAAI,WAAW;AAAA,IACxB;AACA,QAAI,MAAM;AACV,QAAI,OAAO,GAAG,MAAM,KAAK;AACvB;AAAA,IACF;AACA,QAAI,SAAS;AACb,QAAIM,UAAS;AACb,WAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,IACF;AACA,QAAI,QAAQ,OAAO,SAAS,OAAO,SAAS,MAAM;AAClD,QAAI,OAAO,IAAI,WAAW,IAAI;AAC9B,WAAO,OAAO,GAAG,GAAG;AAClB,UAAI,QAAQ,SAAS,OAAO,WAAW,GAAG,CAAC;AAC3C,UAAI,UAAU,KAAK;AACjB;AAAA,MACF;AACA,UAAIH,KAAI;AACR,eAAS,MAAM,OAAO,IAAI,UAAU,KAAKA,KAAIG,YAAW,QAAQ,IAAI,OAAOH,MAAK;AAC9E,iBAAS,OAAO,KAAK,GAAG,MAAM;AAC9B,aAAK,GAAG,IAAI,QAAQ,QAAQ;AAC5B,gBAAQ,QAAQ,QAAQ;AAAA,MAC1B;AACA,UAAI,UAAU,GAAG;AACf,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AACA,MAAAG,UAASH;AACT;AAAA,IACF;AACA,QAAI,OAAO,GAAG,MAAM,KAAK;AACvB;AAAA,IACF;AACA,QAAI,MAAM,OAAOG;AACjB,WAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,IACF;AACA,QAAI,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAC9C,QAAIJ,KAAI;AACR,WAAO,QAAQ,MAAM;AACnB,UAAIA,IAAG,IAAI,KAAK,KAAK;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,WAASK,QAAOC,SAAQ;AACtB,QAAI,SAAS,aAAaA,OAAM;AAChC,QAAI,QAAQ;AACV,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,OAAQP,KAAK,YAAY;AAAA,EAC3C;AACA,SAAO;AAAA,IACL,QAAQI;AAAA,IACR;AAAA,IACA,QAAQE;AAAA,EACV;AACF;AACA,IAAI,MAAMR;AACV,IAAI,kCAAkC;AACtC,IAAO,iBAAQ;;;ACpIf,IAAM,QAAQ,IAAI,WAAW,CAAC;AAM9B,IAAM,SAAS,CAAC,IAAI,OAAO;AACzB,MAAI,OAAO;AACT,WAAO;AACT,MAAI,GAAG,eAAe,GAAG,YAAY;AACnC,WAAO;AAAA,EACT;AACA,WAAS,KAAK,GAAG,KAAK,GAAG,YAAY,MAAM;AACzC,QAAI,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG;AACrB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,SAAS,CAAAU,OAAK;AAClB,MAAIA,cAAa,cAAcA,GAAE,YAAY,SAAS;AACpD,WAAOA;AACT,MAAIA,cAAa;AACf,WAAO,IAAI,WAAWA,EAAC;AACzB,MAAI,YAAY,OAAOA,EAAC,GAAG;AACzB,WAAO,IAAI,WAAWA,GAAE,QAAQA,GAAE,YAAYA,GAAE,UAAU;AAAA,EAC5D;AACA,QAAM,IAAI,MAAM,mCAAmC;AACrD;AAEA,IAAM,aAAa,SAAO,IAAI,YAAY,EAAE,OAAO,GAAG;AACtD,IAAM,WAAW,CAAAC,OAAK,IAAI,YAAY,EAAE,OAAOA,EAAC;;;AC7BhD,IAAM,UAAN,MAAc;AAAA,EACZ,YAAYC,OAAM,QAAQ,YAAY;AACpC,SAAK,OAAOA;AACZ,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,iBAAiB,YAAY;AAC/B,aAAO,GAAI,KAAK,MAAO,GAAI,KAAK,WAAW,KAAK,CAAE;AAAA,IACpD,OAAO;AACL,YAAM,MAAM,mCAAmC;AAAA,IACjD;AAAA,EACF;AACF;AACA,IAAM,UAAN,MAAc;AAAA,EACZ,YAAYA,OAAM,QAAQ,YAAY;AACpC,SAAK,OAAOA;AACZ,SAAK,SAAS;AACd,QAAI,OAAO,YAAY,CAAC,MAAM,QAAW;AACvC,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC5C;AACA,SAAK,kBAAkB,OAAO,YAAY,CAAC;AAC3C,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,MAAM;AACX,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,KAAK,YAAY,CAAC,MAAM,KAAK,iBAAiB;AAChD,cAAM,MAAM,qCAAsC,KAAK,UAAU,IAAI,CAAE,KAAM,KAAK,IAAK,+CAAgD,KAAK,MAAO,EAAE;AAAA,MACvJ;AACA,aAAO,KAAK,WAAW,KAAK,MAAM,KAAK,OAAO,MAAM,CAAC;AAAA,IACvD,OAAO;AACL,YAAM,MAAM,mCAAmC;AAAA,IACjD;AAAA,EACF;AAAA,EACA,GAAG,SAAS;AACV,WAAOC,IAAG,MAAM,OAAO;AAAA,EACzB;AACF;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,GAAG,SAAS;AACV,WAAOA,IAAG,MAAM,OAAO;AAAA,EACzB;AAAA,EACA,OAAO,OAAO;AACZ,UAAM,SAAS,MAAM,CAAC;AACtB,UAAM,UAAU,KAAK,SAAS,MAAM;AACpC,QAAI,SAAS;AACX,aAAO,QAAQ,OAAO,KAAK;AAAA,IAC7B,OAAO;AACL,YAAM,WAAW,qCAAsC,KAAK,UAAU,KAAK,CAAE,+BAAgC,OAAO,KAAK,KAAK,QAAQ,CAAE,gBAAgB;AAAA,IAC1J;AAAA,EACF;AACF;AACO,IAAMA,MAAK,CAAC,MAAM,UAAU,IAAI,gBAAgB;AAAA,EACrD,GAAG,KAAK,YAAY,EAAE,CAAC,KAAK,MAAM,GAAG,KAAK;AAAA,EAC1C,GAAG,MAAM,YAAY,EAAE,CAAC,MAAM,MAAM,GAAG,MAAM;AAC/C,CAAC;AACM,IAAM,QAAN,MAAY;AAAA,EACjB,YAAYD,OAAM,QAAQ,YAAY,YAAY;AAChD,SAAK,OAAOA;AACZ,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,UAAU,IAAI,QAAQA,OAAM,QAAQ,UAAU;AACnD,SAAK,UAAU,IAAI,QAAQA,OAAM,QAAQ,UAAU;AAAA,EACrD;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,KAAK,QAAQ,OAAO,KAAK;AAAA,EAClC;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,KAAK,QAAQ,OAAO,KAAK;AAAA,EAClC;AACF;AACO,IAAM,OAAO,CAAC,EAAC,MAAAA,OAAM,QAAQ,QAAAE,SAAQ,QAAAC,QAAM,MAAM,IAAI,MAAMH,OAAM,QAAQE,SAAQC,OAAM;AACvF,IAAM,QAAQ,CAAC,EAAC,QAAQ,MAAAH,OAAM,UAAAI,UAAQ,MAAM;AACjD,QAAM,EAAC,QAAAF,SAAQ,QAAAC,QAAM,IAAI,eAAMC,WAAUJ,KAAI;AAC7C,SAAO,KAAK;AAAA,IACV;AAAA,IACA,MAAAA;AAAA,IACA,QAAAE;AAAA,IACA,QAAQ,UAAQ,OAAOC,QAAO,IAAI,CAAC;AAAA,EACrC,CAAC;AACH;AACA,IAAM,SAAS,CAACE,SAAQD,WAAU,aAAaJ,UAAS;AACtD,QAAM,QAAQ,CAAC;AACf,WAASM,KAAI,GAAGA,KAAIF,UAAS,QAAQ,EAAEE,IAAG;AACxC,UAAMF,UAASE,EAAC,CAAC,IAAIA;AAAA,EACvB;AACA,MAAI,MAAMD,QAAO;AACjB,SAAOA,QAAO,MAAM,CAAC,MAAM,KAAK;AAC9B,MAAE;AAAA,EACJ;AACA,QAAM,MAAM,IAAI,WAAW,MAAM,cAAc,IAAI,CAAC;AACpD,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,UAAU;AACd,WAASC,KAAI,GAAGA,KAAI,KAAK,EAAEA,IAAG;AAC5B,UAAM,QAAQ,MAAMD,QAAOC,EAAC,CAAC;AAC7B,QAAI,UAAU,QAAW;AACvB,YAAM,IAAI,YAAY,OAAQN,KAAK,YAAY;AAAA,IACjD;AACA,aAAS,UAAU,cAAc;AACjC,YAAQ;AACR,QAAI,QAAQ,GAAG;AACb,cAAQ;AACR,UAAI,SAAS,IAAI,MAAM,UAAU;AAAA,IACnC;AAAA,EACF;AACA,MAAI,QAAQ,eAAe,MAAM,UAAU,IAAI,MAAM;AACnD,UAAM,IAAI,YAAY,wBAAwB;AAAA,EAChD;AACA,SAAO;AACT;AACA,IAAM,SAAS,CAAC,MAAMI,WAAU,gBAAgB;AAC9C,QAAM,MAAMA,UAASA,UAAS,SAAS,CAAC,MAAM;AAC9C,QAAM,QAAQ,KAAK,eAAe;AAClC,MAAI,MAAM;AACV,MAAI,OAAO;AACX,MAAI,SAAS;AACb,WAASE,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AACpC,aAAS,UAAU,IAAI,KAAKA,EAAC;AAC7B,YAAQ;AACR,WAAO,OAAO,aAAa;AACzB,cAAQ;AACR,aAAOF,UAAS,OAAO,UAAU,IAAI;AAAA,IACvC;AAAA,EACF;AACA,MAAI,MAAM;AACR,WAAOA,UAAS,OAAO,UAAU,cAAc,IAAI;AAAA,EACrD;AACA,MAAI,KAAK;AACP,WAAO,IAAI,SAAS,cAAc,GAAG;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAM,UAAU,CAAC,EAAC,MAAAJ,OAAM,QAAQ,aAAa,UAAAI,UAAQ,MAAM;AAChE,SAAO,KAAK;AAAA,IACV;AAAA,IACA,MAAAJ;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,OAAO,OAAOI,WAAU,WAAW;AAAA,IAC5C;AAAA,IACA,OAAO,OAAO;AACZ,aAAO,OAAO,OAAOA,WAAU,aAAaJ,KAAI;AAAA,IAClD;AAAA,EACF,CAAC;AACH;;;AHnJO,IAAM,WAAW,KAAK;AAAA,EAC3B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ,SAAO,SAAS,GAAG;AAAA,EAC3B,QAAQ,SAAO,WAAW,GAAG;AAC/B,CAAC;;;AIVD;AAAA;AAAA,eAAAO;AAAA;AACO,IAAMC,SAAQ,QAAQ;AAAA,EAC3B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACND;AAAA;AAAA;AAAA;AACO,IAAM,QAAQ,QAAQ;AAAA,EAC3B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACND;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,MAAM;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AACZ,CAAC;;;ACLD;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,QAAQ;AAAA,EAC5B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,cAAc,QAAQ;AAAA,EACjC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACZD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,QAAQ;AAAA,EAC5B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,cAAc,QAAQ;AAAA,EACjC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,YAAY,QAAQ;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,iBAAiB,QAAQ;AAAA,EACpC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,YAAY,QAAQ;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,iBAAiB,QAAQ;AAAA,EACpC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,eAAe,QAAQ;AAAA,EAClC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,oBAAoB,QAAQ;AAAA,EACvC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,UAAU,QAAQ;AAAA,EAC7B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACtDD;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,MAAM;AAAA,EAC1B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AACZ,CAAC;AACM,IAAM,cAAc,MAAM;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AACZ,CAAC;;;ACVD;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,YAAY,MAAM;AAAA,EAC7B,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC;AACM,IAAM,eAAe,MAAM;AAAA,EAChC,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AACZ,CAAC;;;ACVD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,SAAS,QAAQ;AAAA,EAC5B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,YAAY,QAAQ;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,YAAY,QAAQ;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;AACM,IAAM,eAAe,QAAQ;AAAA,EAClC,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AACf,CAAC;;;ACxBD;AAAA;AAAA;AAAA;AACA,IAAM,WAAW,MAAM,KAAK,weAAs2F;AACl4F,IAAM,uBAAuB,SAAS,OAAO,CAACC,IAAGC,IAAGC,OAAM;AACxD,EAAAF,GAAEE,EAAC,IAAID;AACP,SAAOD;AACT,GAAG,CAAC,CAAC;AACL,IAAM,uBAAuB,SAAS,OAAO,CAACA,IAAGC,IAAGC,OAAM;AACxD,EAAAF,GAAEC,GAAE,YAAY,CAAC,CAAC,IAAIC;AACtB,SAAOF;AACT,GAAG,CAAC,CAAC;AACL,SAASG,QAAO,MAAM;AACpB,SAAO,KAAK,OAAO,CAACH,IAAGC,OAAM;AAC3B,IAAAD,MAAK,qBAAqBC,EAAC;AAC3B,WAAOD;AAAA,EACT,GAAG,EAAE;AACP;AACA,SAASI,QAAO,KAAK;AACnB,QAAM,OAAO,CAAC;AACd,aAAW,QAAQ,KAAK;AACtB,UAAM,MAAM,qBAAqB,KAAK,YAAY,CAAC,CAAC;AACpD,QAAI,QAAQ,QAAW;AACrB,YAAM,IAAI,MAAM,+BAAgC,IAAK,EAAE;AAAA,IACzD;AACA,SAAK,KAAK,GAAG;AAAA,EACf;AACA,SAAO,IAAI,WAAW,IAAI;AAC5B;AACO,IAAM,eAAe,KAAK;AAAA,EAC/B,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAAD;AAAA,EACA,QAAAC;AACF,CAAC;;;AChCD;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAI,WAAWC;AACf,IAAI,MAAM;AAAV,IAAe,OAAO;AAAtB,IAA2B,SAAS,CAAC;AAArC,IAA2C,MAAM,KAAK,IAAI,GAAG,EAAE;AAC/D,SAASA,QAAO,KAAK,KAAK,QAAQ;AAChC,QAAM,OAAO,CAAC;AACd,WAAS,UAAU;AACnB,MAAI,YAAY;AAChB,SAAO,OAAO,KAAK;AACjB,QAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,MAAM,QAAQ;AACnB,QAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,aAAS;AAAA,EACX;AACA,MAAI,MAAM,IAAI,MAAM;AACpB,EAAAA,QAAO,QAAQ,SAAS,YAAY;AACpC,SAAO;AACT;AACA,IAAIC,UAAS;AACb,IAAI,QAAQ;AAAZ,IAAiB,SAAS;AAC1B,SAAS,KAAK,KAAK,QAAQ;AACzB,MAAI,MAAM,GAAG,SAAS,UAAU,GAAG,QAAQ,GAAG,UAAU,QAAQC,IAAGC,KAAI,IAAI;AAC3E,KAAG;AACD,QAAI,WAAWA,IAAG;AAChB,WAAK,QAAQ;AACb,YAAM,IAAI,WAAW,yBAAyB;AAAA,IAChD;AACA,IAAAD,KAAI,IAAI,SAAS;AACjB,WAAO,QAAQ,MAAMA,KAAI,WAAW,SAASA,KAAI,UAAU,KAAK,IAAI,GAAG,KAAK;AAC5E,aAAS;AAAA,EACX,SAASA,MAAK;AACd,OAAK,QAAQ,UAAU;AACvB,SAAO;AACT;AACA,IAAI,KAAK,KAAK,IAAI,GAAG,CAAC;AACtB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE;AACvB,IAAI,SAAS,SAAU,OAAO;AAC5B,SAAO,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI;AAClK;AACA,IAAI,SAAS;AAAA,EACX,QAAQ;AAAA,EACR,QAAQD;AAAA,EACR,gBAAgB;AAClB;AACA,IAAI,eAAe;AACnB,IAAO,iBAAQ;;;ACnDR,IAAMG,UAAS,CAAC,MAAM,SAAS,MAAM;AAC1C,QAAMC,QAAO,eAAO,OAAO,MAAM,MAAM;AACvC,SAAO;AAAA,IACLA;AAAA,IACA,eAAO,OAAO;AAAA,EAChB;AACF;AACO,IAAM,WAAW,CAAC,KAAK,QAAQ,SAAS,MAAM;AACnD,iBAAO,OAAO,KAAK,QAAQ,MAAM;AACjC,SAAO;AACT;AACO,IAAM,iBAAiB,SAAO;AACnC,SAAO,eAAO,eAAe,GAAG;AAClC;;;ACTO,IAAM,SAAS,CAACC,OAAMC,YAAW;AACtC,QAAM,OAAOA,QAAO;AACpB,QAAM,aAAoB,eAAeD,KAAI;AAC7C,QAAM,eAAe,aAAoB,eAAe,IAAI;AAC5D,QAAM,QAAQ,IAAI,WAAW,eAAe,IAAI;AAChD,EAAO,SAASA,OAAM,OAAO,CAAC;AAC9B,EAAO,SAAS,MAAM,OAAO,UAAU;AACvC,QAAM,IAAIC,SAAQ,YAAY;AAC9B,SAAO,IAAI,OAAOD,OAAM,MAAMC,SAAQ,KAAK;AAC7C;AACO,IAAMC,UAAS,eAAa;AACjC,QAAM,QAAQ,OAAO,SAAS;AAC9B,QAAM,CAACF,OAAM,UAAU,IAAWE,QAAO,KAAK;AAC9C,QAAM,CAAC,MAAM,YAAY,IAAWA,QAAO,MAAM,SAAS,UAAU,CAAC;AACrE,QAAMD,UAAS,MAAM,SAAS,aAAa,YAAY;AACvD,MAAIA,QAAO,eAAe,MAAM;AAC9B,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACpC;AACA,SAAO,IAAI,OAAOD,OAAM,MAAMC,SAAQ,KAAK;AAC7C;AACO,IAAME,UAAS,CAACC,IAAGC,OAAM;AAC9B,MAAID,OAAMC,IAAG;AACX,WAAO;AAAA,EACT,OAAO;AACL,WAAOD,GAAE,SAASC,GAAE,QAAQD,GAAE,SAASC,GAAE,QAAQ,OAAWD,GAAE,OAAOC,GAAE,KAAK;AAAA,EAC9E;AACF;AACO,IAAM,SAAN,MAAa;AAAA,EAClB,YAAYL,OAAM,MAAMC,SAAQ,OAAO;AACrC,SAAK,OAAOD;AACZ,SAAK,OAAO;AACZ,SAAK,SAASC;AACd,SAAK,QAAQ;AAAA,EACf;AACF;;;ACtCO,IAAMK,QAAO,CAAC,EAAC,MAAAC,OAAM,MAAAC,OAAM,QAAAC,QAAM,MAAM,IAAI,OAAOF,OAAMC,OAAMC,OAAM;AACpE,IAAM,SAAN,MAAa;AAAA,EAClB,YAAYF,OAAMC,OAAMC,SAAQ;AAC9B,SAAK,OAAOF;AACZ,SAAK,OAAOC;AACZ,SAAK,SAASC;AAAA,EAChB;AAAA,EACA,OAAO,OAAO;AACZ,QAAI,iBAAiB,YAAY;AAC/B,YAAM,SAAS,KAAK,OAAO,KAAK;AAChC,aAAO,kBAAkB,aAAoB,OAAO,KAAK,MAAM,MAAM,IAAI,OAAO,KAAK,CAAAC,YAAiB,OAAO,KAAK,MAAMA,OAAM,CAAC;AAAA,IACjI,OAAO;AACL,YAAM,MAAM,mCAAmC;AAAA,IACjD;AAAA,EACF;AACF;;;AJfA,IAAM,MAAM,CAAAC,UAAQ,OAAM,SAAQ,IAAI,WAAW,MAAM,OAAO,OAAO,OAAOA,OAAM,IAAI,CAAC;AAChF,IAAM,SAASC,MAAK;AAAA,EACzB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ,IAAI,SAAS;AACvB,CAAC;AACM,IAAM,SAASA,MAAK;AAAA,EACzB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,QAAQ,IAAI,SAAS;AACvB,CAAC;;;AKXD,IAAAC,oBAAA;AAAA,SAAAA,mBAAA;AAAA,kBAAAC;AAAA;AAEA,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAMC,UAAS;AACf,IAAM,SAAS,WAAgB,OAAO,MAAMA,QAAO,KAAK,CAAC;AAClD,IAAMC,YAAW;AAAA,EACtB;AAAA,EACA;AAAA,EACA,QAAAD;AAAA,EACA;AACF;;;ACXA,IAAM,cAAc,IAAI,YAAY;AACpC,IAAM,cAAc,IAAI,YAAY;;;ACI7B,IAAM,MAAN,MAAM,KAAI;AAAA,EACf,YAAYE,UAASC,OAAM,WAAW,OAAO;AAC3C,SAAK,OAAOA;AACZ,SAAK,UAAUD;AACf,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,aAAa,MAAM;AACxB,SAAK,aAAa,MAAM;AACxB,SAAK,QAAQ;AACb,SAAK,aAAa,oBAAI,IAAI;AAC1B,WAAO,iBAAiB,MAAM;AAAA,MAC5B,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,YAAQ,KAAK,SAAS;AAAA,MACtB,KAAK,GAAG;AACJ,eAAO;AAAA,MACT;AAAA,MACF,SAAS;AACL,cAAM,EAAC,MAAAC,OAAM,UAAS,IAAI;AAC1B,YAAIA,UAAS,aAAa;AACxB,gBAAM,IAAI,MAAM,0CAA0C;AAAA,QAC5D;AACA,YAAI,UAAU,SAAS,cAAc;AACnC,gBAAM,IAAI,MAAM,oDAAoD;AAAA,QACtE;AACA,eAAO,KAAI,SAAS,SAAS;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,YAAQ,KAAK,SAAS;AAAA,MACtB,KAAK,GAAG;AACJ,cAAM,EAAC,MAAAA,OAAM,QAAAC,QAAM,IAAI,KAAK;AAC5B,cAAM,YAAmB,OAAOD,OAAMC,OAAM;AAC5C,eAAO,KAAI,SAAS,KAAK,MAAM,SAAS;AAAA,MAC1C;AAAA,MACF,KAAK,GAAG;AACJ,eAAO;AAAA,MACT;AAAA,MACF,SAAS;AACL,cAAM,MAAM,+BAAgC,KAAK,OAAQ,4CAA4C;AAAA,MACvG;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,SAAS,KAAK,SAAS,MAAM,QAAQ,KAAK,YAAY,MAAM,WAAkBC,QAAO,KAAK,WAAW,MAAM,SAAS;AAAA,EAC7H;AAAA,EACA,SAASC,OAAM;AACb,UAAM,EAAC,OAAO,SAAAJ,UAAS,WAAU,IAAI;AACrC,YAAQA,UAAS;AAAA,MACjB,KAAK;AACH,eAAO,WAAW,OAAO,YAAYI,SAAQ,UAAU,OAAO;AAAA,MAChE;AACE,eAAO,WAAW,OAAO,YAAYA,SAAQ,OAAO,OAAO;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,MAAM,KAAK,UAAU;AAAA,IACvB;AAAA,EACF;AAAA,EACA,KAAK,OAAO,WAAW,IAAI;AACzB,WAAO;AAAA,EACT;AAAA,EACA,CAAC,OAAO,IAAI,4BAA4B,CAAC,IAAI;AAC3C,WAAO,SAAS,KAAK,SAAS,IAAI;AAAA,EACpC;AAAA,EACA,OAAO,MAAM,OAAO;AAClB,cAAU,SAAS,kBAAkB;AACrC,WAAO,CAAC,EAAE,UAAU,MAAM,SAAS,KAAK,MAAM,UAAU;AAAA,EAC1D;AAAA,EACA,IAAI,sBAAsB;AACxB,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AAAA,EACA,IAAI,QAAQ;AACV,UAAM,IAAI,MAAM,qEAAqE;AAAA,EACvF;AAAA,EACA,IAAI,SAAS;AACX,UAAM,IAAI,MAAM,mEAAmE;AAAA,EACrF;AAAA,EACA,IAAI,gBAAgB;AAClB,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC1D;AAAA,EACA,IAAI,SAAS;AACX,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACnD;AAAA,EACA,OAAO,MAAM,OAAO;AAClB,QAAI,iBAAiB,MAAK;AACxB,aAAO;AAAA,IACT,WAAW,SAAS,QAAQ,MAAM,UAAU,OAAO;AACjD,YAAM,EAAC,SAAAJ,UAAS,MAAAC,OAAM,WAAW,MAAK,IAAI;AAC1C,aAAO,IAAI,KAAID,UAASC,OAAM,WAAW,SAAS,UAAUD,UAASC,OAAM,UAAU,KAAK,CAAC;AAAA,IAC7F,WAAW,SAAS,QAAQ,MAAM,SAAS,MAAM,MAAM;AACrD,YAAM,EAAC,SAAAD,UAAS,WAAW,MAAAC,MAAI,IAAI;AACnC,YAAMC,UAAgBG,QAAO,SAAS;AACtC,aAAO,KAAI,OAAOL,UAASC,OAAMC,OAAM;AAAA,IACzC,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,OAAO,OAAOF,UAASC,OAAMC,SAAQ;AACnC,QAAI,OAAOD,UAAS,UAAU;AAC5B,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACzD;AACA,YAAQD,UAAS;AAAA,MACjB,KAAK,GAAG;AACJ,YAAIC,UAAS,aAAa;AACxB,gBAAM,IAAI,MAAM,wCAAyC,WAAY,kBAAkB;AAAA,QACzF,OAAO;AACL,iBAAO,IAAI,KAAID,UAASC,OAAMC,SAAQA,QAAO,KAAK;AAAA,QACpD;AAAA,MACF;AAAA,MACF,KAAK,GAAG;AACJ,cAAM,QAAQ,UAAUF,UAASC,OAAMC,QAAO,KAAK;AACnD,eAAO,IAAI,KAAIF,UAASC,OAAMC,SAAQ,KAAK;AAAA,MAC7C;AAAA,MACF,SAAS;AACL,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,SAASA,SAAQ;AACtB,WAAO,KAAI,OAAO,GAAG,aAAaA,OAAM;AAAA,EAC1C;AAAA,EACA,OAAO,SAASD,OAAMC,SAAQ;AAC5B,WAAO,KAAI,OAAO,GAAGD,OAAMC,OAAM;AAAA,EACnC;AAAA,EACA,OAAO,OAAO,OAAO;AACnB,UAAM,CAAC,KAAK,SAAS,IAAI,KAAI,YAAY,KAAK;AAC9C,QAAI,UAAU,QAAQ;AACpB,YAAM,IAAI,MAAM,kBAAkB;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,YAAY,OAAO;AACxB,UAAM,QAAQ,KAAI,aAAa,KAAK;AACpC,UAAM,aAAa,MAAM,OAAO,MAAM;AACtC,UAAM,iBAAiB,OAAO,MAAM,SAAS,YAAY,aAAa,MAAM,aAAa,CAAC;AAC1F,QAAI,eAAe,eAAe,MAAM,eAAe;AACrD,YAAM,IAAI,MAAM,kBAAkB;AAAA,IACpC;AACA,UAAM,cAAc,eAAe,SAAS,MAAM,gBAAgB,MAAM,UAAU;AAClF,UAAMA,UAAS,IAAW,OAAO,MAAM,eAAe,MAAM,YAAY,aAAa,cAAc;AACnG,UAAM,MAAM,MAAM,YAAY,IAAI,KAAI,SAASA,OAAM,IAAI,KAAI,SAAS,MAAM,OAAOA,OAAM;AACzF,WAAO;AAAA,MACL;AAAA,MACA,MAAM,SAAS,MAAM,IAAI;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,OAAO,aAAa,cAAc;AAChC,QAAI,SAAS;AACb,UAAM,OAAO,MAAM;AACjB,YAAM,CAACI,IAAGC,OAAM,IAAWF,QAAO,aAAa,SAAS,MAAM,CAAC;AAC/D,gBAAUE;AACV,aAAOD;AAAA,IACT;AACA,QAAIN,WAAU,KAAK;AACnB,QAAI,QAAQ;AACZ,QAAIA,aAAY,IAAI;AAClB,MAAAA,WAAU;AACV,eAAS;AAAA,IACX,WAAWA,aAAY,GAAG;AACxB,cAAQ,KAAK;AAAA,IACf;AACA,QAAIA,aAAY,KAAKA,aAAY,GAAG;AAClC,YAAM,IAAI,WAAW,uBAAwBA,QAAQ,EAAE;AAAA,IACzD;AACA,UAAM,aAAa;AACnB,UAAM,gBAAgB,KAAK;AAC3B,UAAM,aAAa,KAAK;AACxB,UAAM,OAAO,SAAS;AACtB,UAAM,gBAAgB,OAAO;AAC7B,WAAO;AAAA,MACL,SAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,MAAM,QAAQI,OAAM;AACzB,UAAM,CAAC,QAAQ,KAAK,IAAI,gBAAgB,QAAQA,KAAI;AACpD,UAAM,MAAM,KAAI,OAAO,KAAK;AAC5B,QAAI,WAAW,IAAI,QAAQ,MAAM;AACjC,WAAO;AAAA,EACT;AACF;AACA,IAAM,kBAAkB,CAAC,QAAQA,UAAS;AACxC,UAAQ,OAAO,CAAC,GAAG;AAAA,IACnB,KAAK,KAAK;AACN,YAAM,UAAUA,SAAQ;AACxB,aAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,OAAO,GAAI,UAAU,MAAO,GAAI,MAAO,EAAE;AAAA,MACnD;AAAA,IACF;AAAA,IACF,KAAK,UAAU,QAAQ;AACnB,YAAM,UAAUA,SAAQ;AACxB,aAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ,OAAO,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,IACF,KAAK,OAAO,QAAQ;AAChB,YAAM,UAAUA,SAAQ;AACxB,aAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ,OAAO,MAAM;AAAA,MACvB;AAAA,IACF;AAAA,IACF,SAAS;AACL,UAAIA,SAAQ,MAAM;AAChB,cAAM,MAAM,iFAAiF;AAAA,MAC/F;AACA,aAAO;AAAA,QACL,OAAO,CAAC;AAAA,QACRA,MAAK,OAAO,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,aAAa,CAAC,OAAO,OAAOA,UAAS;AACzC,QAAM,EAAC,OAAM,IAAIA;AACjB,MAAI,WAAW,UAAU,QAAQ;AAC/B,UAAM,MAAM,8BAA+BA,MAAK,IAAK,WAAW;AAAA,EAClE;AACA,QAAM,MAAM,MAAM,IAAI,MAAM;AAC5B,MAAI,OAAO,MAAM;AACf,UAAMI,OAAMJ,MAAK,OAAO,KAAK,EAAE,MAAM,CAAC;AACtC,UAAM,IAAI,QAAQI,IAAG;AACrB,WAAOA;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,aAAa,CAAC,OAAO,OAAOJ,UAAS;AACzC,QAAM,EAAC,OAAM,IAAIA;AACjB,QAAM,MAAM,MAAM,IAAI,MAAM;AAC5B,MAAI,OAAO,MAAM;AACf,UAAMI,OAAMJ,MAAK,OAAO,KAAK;AAC7B,UAAM,IAAI,QAAQI,IAAG;AACrB,WAAOA;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AACA,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,YAAY,CAACR,UAASC,OAAM,cAAc;AAC9C,QAAM,aAAoB,eAAeD,QAAO;AAChD,QAAM,aAAa,aAAoB,eAAeC,KAAI;AAC1D,QAAM,QAAQ,IAAI,WAAW,aAAa,UAAU,UAAU;AAC9D,EAAO,SAASD,UAAS,OAAO,CAAC;AACjC,EAAO,SAASC,OAAM,OAAO,UAAU;AACvC,QAAM,IAAI,WAAW,UAAU;AAC/B,SAAO;AACT;AACA,IAAM,YAAY,OAAO,IAAI,kBAAkB;AAC/C,IAAM,WAAW;AAAA,EACf,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AACd;AACA,IAAM,SAAS;AAAA,EACb,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,cAAc;AAChB;AACA,IAAM,UAAU;AAChB,IAAM,YAAY,CAAC,OAAO,YAAY;AACpC,MAAI,MAAM,KAAK,OAAO,GAAG;AACvB,YAAQ,KAAK,OAAO;AAAA,EACtB,OAAO;AACL,UAAM,IAAI,MAAM,OAAO;AAAA,EACzB;AACF;AACA,IAAM,qBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AChR3B,IAAM,QAAQ;AAAA,EACZ,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,SAAS;AAAA,EACb,GAAG;AAAA,EACH,GAAGQ;AACL;;;AClCA,SAAS,YAAYC,OAAM,QAAQC,SAAQC,SAAQ;AACjD,SAAO;AAAA,IACL,MAAAF;AAAA,IACA;AAAA,IACA,SAAS;AAAA,MACP,MAAAA;AAAA,MACA;AAAA,MACA,QAAAC;AAAA,IACF;AAAA,IACA,SAAS,EAAE,QAAAC,QAAO;AAAA,EACpB;AACF;AACA,IAAM,SAAS,YAAY,QAAQ,KAAK,SAAO;AAC7C,QAAM,UAAU,IAAI,YAAY,MAAM;AACtC,SAAO,MAAM,QAAQ,OAAO,GAAG;AACjC,GAAG,SAAO;AACR,QAAM,UAAU,IAAI,YAAY;AAChC,SAAO,QAAQ,OAAO,IAAI,UAAU,CAAC,CAAC;AACxC,CAAC;AACD,IAAM,QAAQ,YAAY,SAAS,KAAK,SAAO;AAC7C,MAAIC,UAAS;AACb,WAASC,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,IAAAD,WAAU,OAAO,aAAa,IAAIC,EAAC,CAAC;AAAA,EACtC;AACA,SAAOD;AACT,GAAG,SAAO;AACR,QAAM,IAAI,UAAU,CAAC;AACrB,QAAM,MAAM,YAAY,IAAI,MAAM;AAClC,WAASC,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACnC,QAAIA,EAAC,IAAI,IAAI,WAAWA,EAAC;AAAA,EAC3B;AACA,SAAO;AACT,CAAC;AACD,IAAM,QAAQ;AAAA,EACZ,MAAM;AAAA,EACN,SAAS;AAAA,EACT,KAAK,MAAM;AAAA,EACX,QAAQ;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,EACR,GAAG;AACL;AACA,IAAO,gBAAQ;;;AC3CR,SAASC,YAAWC,SAAQ,WAAW,QAAQ;AACpD,QAAMC,QAAO,cAAM,QAAQ;AAC3B,MAAI,CAACA,OAAM;AACT,UAAM,IAAI,MAAM,yBAA0B,QAAS,GAAG;AAAA,EACxD;AACA,OAAK,aAAa,UAAU,aAAa,YAAY,WAAW,UAAU,QAAQ,WAAW,OAAO,QAAQ,MAAM;AAChH,WAAO,WAAW,OAAO,KAAKD,SAAQ,MAAM;AAAA,EAC9C;AACA,SAAOC,MAAK,QAAQ,OAAO,GAAIA,MAAK,MAAO,GAAID,OAAO,EAAE;AAC1D;;;ACTO,SAASE,UAAS,OAAO,WAAW,QAAQ;AACjD,QAAMC,QAAO,cAAM,QAAQ;AAC3B,MAAI,CAACA,OAAM;AACT,UAAM,IAAI,MAAM,yBAA0B,QAAS,GAAG;AAAA,EACxD;AACA,OAAK,aAAa,UAAU,aAAa,YAAY,WAAW,UAAU,QAAQ,WAAW,OAAO,QAAQ,MAAM;AAChH,WAAO,WAAW,OAAO,KAAK,MAAM,QAAQ,MAAM,YAAY,MAAM,UAAU,EAAE,SAAS,MAAM;AAAA,EACjG;AACA,SAAOA,MAAK,QAAQ,OAAO,KAAK,EAAE,UAAU,CAAC;AAC/C;;;AC6BA,IAAA,IAAA,EAAA,MAAA,EAAA,SAAA,gBAAA,cAAA,qBAAA,WAAA,kBAAA,gBAAA,uBAAA,cAAA,qBAAA,aAAA,oBAAA,kBAAA,yBAAA,oBAAA,0BAAA,GAAA,KAAA,EAAA,SAAA,eAAA,cAAA,oBAAA,WAAA,iBAAA,gBAAA,sBAAA,cAAA,oBAAA,aAAA,mBAAA,kBAAA,wBAAA,oBAAA,yBAAA,GAAA,SAAA,EAAA,SAAA,mBAAA,cAAA,wBAAA,WAAA,qBAAA,gBAAA,0BAAA,cAAA,wBAAA,aAAA,uBAAA,kBAAA,4BAAA,oBAAA,6BAAA,EAAA;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AIvCO,IAAM,cAAc;AACpB,IAAM,kBAAkB;AACxB,IAAM,mBAAmB;AACzB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,eAAe;AAErB,IAAM,uBAAuB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AACpE,IAAM,0BAA0B,CAAC,OAAQ,MAAM;AAE/C,IAAM,qBAAqB;EAChC,CAAC,WAAW,GAAG,EAAE,MAAM,QAAQ,SAAS,cAAa;EACrD,CAAC,eAAe,GAAG,EAAE,MAAM,QAAQ,SAAS,kBAAiB;EAC7D,CAAC,gBAAgB,GAAG,EAAE,MAAM,QAAQ,SAAS,mBAAkB;EAC/D,CAAC,cAAc,GAAG,EAAE,MAAM,QAAQ,SAAS,iBAAgB;EAC3D,CAAC,cAAc,GAAG,EAAE,MAAM,QAAQ,SAAS,iBAAgB;EAC3D,CAAC,YAAY,GAAG,EAAE,MAAM,OAAQ,SAAS,eAAc;;AAGlD,IAAM,gBAAgB;;;ACVvB,SAAU,kBAAkBC,OAAY;AAC5C,SAAOA,SAAQ,wBAAwB,CAAC,KAAKA,SAAQ,wBAAwB,CAAC;AAChF;AAEM,SAAU,oBAAoBA,OAAY;AAC9C,SAAO,qBAAqB,SAASA,KAAI;AAC3C;AAEM,SAAU,iBAAiBA,OAAY;AAC3C,SAAO,OAAOA,UAAS;AACzB;AAEM,SAAU,SAAS,MAAY;AACnC,MAAI,CAAC,OAAO,KAAK,kBAAkB,EAAE,SAAS,IAAI,GAAG;AACnD,WAAO,mBAAmB,aAAa;;AAEzC,SAAO,mBAAmB,IAAI;AAChC;AAEM,SAAU,eAAeA,OAAY;AACzC,QAAM,QAAQ,OAAO,OAAO,kBAAkB,EAAE,KAAK,CAAAC,OAAKA,GAAE,SAASD,KAAI;AACzE,MAAI,CAAC,OAAO;AACV,WAAO,mBAAmB,aAAa;;AAEzC,SAAO;AACT;AAEM,SAAU,qBAAqB,UAAsB;AACzD,MAAI,OAAO,SAAS,MAAM,SAAS,aAAa;AAC9C,WAAO,EAAE,OAAO,OAAO,OAAO,kCAAiC;;AAEjE,MAAI,OAAO,SAAS,MAAM,YAAY,aAAa;AACjD,WAAO,EAAE,OAAO,OAAO,OAAO,qCAAoC;;AAEpE,MAAI,CAAC,iBAAiB,SAAS,MAAM,IAAI,GAAG;AAC1C,WAAO;MACL,OAAO;MACP,OAAO,yCAAyC,SAAS,MAAM,IAAI;;;AAGvE,MAAI,oBAAoB,SAAS,MAAM,IAAI,GAAG;AAC5C,UAAM,QAAQ,eAAe,SAAS,MAAM,IAAI;AAChD,QACE,MAAM,YAAY,mBAAmB,aAAa,EAAE,WACpD,SAAS,MAAM,YAAY,MAAM,SACjC;AACA,aAAO;QACL,OAAO;QACP,OAAO,4CAA4C,SAAS,MAAM,IAAI;;;;AAI5E,SAAO,EAAE,OAAO,KAAI;AACtB;AAEM,SAAU,qBAAqBC,IAAU,KAAa,MAAY;AACtE,SAAOA,GAAE,QAAQ,SAAS,uBAAuB,KAAKA,GAAE,QAAQ,SAAS,sBAAsB,IAC3F,IAAI,MAAM,eAAe,IAAI,eAAe,GAAG,EAAE,IACjDA;AACN;;;AChEM,SAAU,UAAU,UAAU,GAAC;AACnC,QAAM,OAAO,KAAK,IAAG,IAAK,KAAK,IAAI,IAAI,OAAO;AAC9C,QAAM,QAAQ,KAAK,MAAM,KAAK,OAAM,IAAK,KAAK,IAAI,IAAI,OAAO,CAAC;AAC9D,SAAO,OAAO;AAChB;AAEM,SAAU,eAAe,UAAU,GAAC;AACxC,SAAO,OAAO,UAAU,OAAO,CAAC;AAClC;AAEM,SAAU,qBACd,QACA,QACA,IAAW;AAEX,SAAO;IACL,IAAI,MAAM,UAAS;IACnB,SAAS;IACT;IACA;;AAEJ;AAEM,SAAU,oBAA6B,IAAY,QAAS;AAChE,SAAO;IACL;IACA,SAAS;IACT;;AAEJ;AAEM,SAAU,mBACd,IACA,OACA,MAAa;AAEb,SAAO;IACL;IACA,SAAS;IACT,OAAO,mBAAmB,OAAO,IAAI;;AAEzC;AAEM,SAAU,mBAAmB,OAAgC,MAAa;AAC9E,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO,SAAS,cAAc;;AAEhC,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAK,OAAA,OAAA,OAAA,OAAA,CAAA,GACA,SAAS,YAAY,CAAC,GAAA,EACzB,SAAS,MAAK,CAAA;;AAGlB,MAAI,OAAO,SAAS,aAAa;AAC/B,UAAM,OAAO;;AAEf,MAAI,oBAAoB,MAAM,IAAI,GAAG;AACnC,YAAQ,eAAe,MAAM,IAAI;;AAEnC,SAAO;AACT;;;AChEA;;;8BAAAC;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;;;;yBAAuB;AAKvB,wBAAc;AAFP,IAAM,WAAW;;;ADDxB,wBAAc;;;AEFR,SAAU,aAAa,OAAa;AACxC,MAAI,MAAM,SAAS,GAAG,GAAG;AACvB,WAAO,qBAAqB,KAAK;;AAEnC,MAAI,MAAM,KAAK,KAAK,GAAG;AACrB,WAAO;;AAET,SAAO;AACT;AAEM,SAAU,oBAAoB,OAAa;AAC/C,SAAO,UAAU;AACnB;AAEM,SAAU,qBAAqB,OAAa;AAChD,MAAI,oBAAoB,KAAK,GAAG;AAC9B,WAAO;;AAET,MAAI,CAAC,MAAM,SAAS,GAAG,GAAG;AACxB,WAAO;;AAET,MAAI,MAAM,MAAM,GAAG,EAAE,WAAW,GAAG;AACjC,WAAO;;AAET,MAAI,MAAM,MAAM,GAAG,EAAE,OAAO,CAAAC,OAAKA,GAAE,KAAI,MAAO,EAAE,EAAE,WAAW,GAAG;AAC9D,WAAO;;AAET,SAAO;AACT;AAEM,SAAU,4BAA4B,OAAa;AACvD,SAAO,CAAC,oBAAoB,KAAK,KAAK,qBAAqB,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,KAAI;AAChG;AAEM,SAAU,6BAA6B,OAAa;AACxD,SAAO,CAAC,oBAAoB,KAAK,KAAK,qBAAqB,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,EAAE,KAAI;AAChG;;;AClCO,IAAe,IAAf,MAAe;AAAA;AAAA,IAAQ,IAAR,cAAQ,EAAA;EAAA,YAAAC,IAAA;AAAA,UAAA;EAAA;AAAA;AAAA,IAAAC,KAAA,cAAA,EAAA;EAAA,cAAA;AAAA,UAAA;EAAA;AAAA;AAAA,IAAA,IAAA,cAAAA,GAAA;EAAA,YAAAD,IAAA;AAAA,UAAA;EAAA;AAAA;;;AEF9B,IAAM,aAAa;AAEnB,IAAM,WAAW;AAEjB,SAAS,eAAe,KAAW;AACjC,QAAM,UAAU,IAAI,MAAM,IAAI,OAAO,SAAS,IAAI,CAAC;AACnD,MAAI,CAAC,WAAW,CAAC,QAAQ;AAAQ;AACjC,SAAO,QAAQ,CAAC;AAClB;AAEA,SAAS,mBAAmB,KAAa,OAAa;AACpD,QAAM,WAAW,eAAe,GAAG;AACnC,MAAI,OAAO,aAAa;AAAa,WAAO;AAC5C,SAAO,IAAI,OAAO,KAAK,EAAE,KAAK,QAAQ;AACxC;AAEM,SAAU,UAAU,KAAW;AACnC,SAAO,mBAAmB,KAAK,UAAU;AAC3C;AAEM,SAAU,QAAQ,KAAW;AACjC,SAAO,mBAAmB,KAAK,QAAQ;AACzC;AAEM,SAAU,eAAe,KAAW;AACxC,SAAO,IAAI,OAAO,4BAA4B,EAAE,KAAK,GAAG;AAC1D;;;AChBM,SAAU,iBAAiB,SAAY;AAC3C,SACE,OAAO,YAAY,YACnB,QAAQ,WACR,aAAa,WACb,QAAQ,YAAY;AAExB;AAEM,SAAU,iBAA0B,SAAuB;AAC/D,SAAO,iBAAiB,OAAO,KAAK,YAAY;AAClD;AAEM,SAAU,kBAA2B,SAAuB;AAChE,SAAO,iBAAiB,OAAO,MAAM,gBAAgB,OAAO,KAAK,eAAe,OAAO;AACzF;AAEM,SAAU,gBAAyB,SAAuB;AAC9D,SAAO,YAAY;AACrB;AAEM,SAAU,eAAe,SAAuB;AACpD,SAAO,WAAW;AACpB;AAEM,SAAU,2BACd,YAA6B;AAE7B,SAAO,WAAW,cAAc,WAAW,UAAU;AACvD;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrCM,IAAgB,UAAhB,MAAuB;;A;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACF7B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,YAAY;AAClB,SAAS,mBAAmB,KAAK,OAAO;AACtC,MAAI,QAAQ,eAAe,QAAQ,iBAAiB,SAAS,OAAO,UAAU,YAAY,eAAe,OAAO;AAC9G,mBAAe,GAAG;AAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK;AAC3B,UAAQ,KAAK,qBAAqB,GAAG,uCAAuC;AAC9E;AACA,SAAS,MAAM,OAAO,UAAU,CAAC,GAAG;AAClC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,MAAM,CAAC,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC,MAAM,OAAO,MAAM,QAAQ,IAAI,MAAM,IAAI;AACrF,WAAO,MAAM,MAAM,GAAG,EAAE;AAAA,EAC1B;AACA,QAAM,SAAS,MAAM,KAAK;AAC1B,MAAI,OAAO,UAAU,GAAG;AACtB,YAAQ,OAAO,YAAY,GAAG;AAAA,MAC5B,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS;AACZ,eAAO;AAAA,MACT;AAAA,MACA,KAAK,aAAa;AAChB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MACA,KAAK,OAAO;AACV,eAAO,OAAO;AAAA,MAChB;AAAA,MACA,KAAK,YAAY;AACf,eAAO,OAAO;AAAA,MAChB;AAAA,MACA,KAAK,aAAa;AAChB,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,QAAI,QAAQ,QAAQ;AAClB,YAAM,IAAI,YAAY,sBAAsB;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AACA,MAAI;AACF,QAAI,eAAe,KAAK,KAAK,KAAK,qBAAqB,KAAK,KAAK,GAAG;AAClE,UAAI,QAAQ,QAAQ;AAClB,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AACA,aAAO,KAAK,MAAM,OAAO,kBAAkB;AAAA,IAC7C;AACA,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB,SAAS,OAAO;AACd,QAAI,QAAQ,QAAQ;AAClB,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACF;", "names": ["d", "b", "s", "e", "p", "i", "c", "r", "P", "_", "f", "y", "g", "n", "v", "o", "m", "k", "k2", "ar", "a", "j", "__assign", "name", "e", "icons", "i", "name", "description", "o", "e", "f", "a", "i", "k", "transmit", "level", "i", "o", "a", "e", "tslib_es6_exports", "__assign", "__asyncDelegator", "__asyncGenerator", "__asyncValues", "__await", "__awaiter", "__classPrivateFieldGet", "__classPrivateFieldSet", "__createBinding", "__decorate", "__exportStar", "__extends", "__generator", "__importDefault", "__importStar", "__makeTemplateObject", "__metadata", "__param", "__read", "__rest", "__spread", "__spreadA<PERSON>ys", "__values", "d", "b", "extendStatics", "s", "e", "p", "i", "c", "r", "P", "_", "f", "y", "g", "n", "v", "o", "m", "k", "k2", "ar", "a", "j", "init_tslib_es6", "isNode", "globalThis", "exports", "g", "e", "name", "i", "decode", "require_browser", "to", "from", "i", "l", "ar", "BrowserInfo", "name", "version", "NodeInfo", "version", "SearchBotDeviceInfo", "name", "version", "BotInfo", "ReactNativeInfo", "name", "version", "isNode", "ALPHABET", "j", "i", "x", "encode", "length", "decode", "string", "esm_default", "_", "length", "base", "ALPHABET", "name", "j", "i", "x", "encode", "length", "decode", "string", "o", "b", "name", "or", "encode", "decode", "alphabet", "string", "i", "base2", "base2", "p", "c", "i", "encode", "decode", "encode", "decode", "b", "l", "decode", "code", "code", "digest", "decode", "equals", "a", "b", "from", "name", "code", "encode", "digest", "name", "from", "identity_exports", "identity", "encode", "identity", "version", "code", "digest", "equals", "base", "decode", "i", "length", "cid", "identity_exports", "name", "encode", "decode", "string", "i", "fromString", "string", "base", "toString", "base", "code", "e", "n", "x", "c", "n"]}