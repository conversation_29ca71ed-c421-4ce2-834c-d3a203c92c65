{"version": 3, "file": "classes.cjs", "sourceRoot": "", "sources": ["../src/classes.ts"], "names": [], "mappings": ";;;;;;AAIA,2CAA6D;AAC7D,8EAAgD;AAGhD,uCAAuD;AAIvD;;;;;GAKG;AACH,MAAa,YAEX,SAAQ,KAAK;IAQb,YAAY,IAAY,EAAE,OAAe,EAAE,IAAW;QACpD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC/C;QAED,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;SAC1D;QAED,IAAI,IAAA,oBAAY,EAAC,IAAI,CAAC,EAAE;YACtB,8HAA8H;YAC9H,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAEtC,2CAA2C;YAC3C,IAAI,CAAC,IAAA,mBAAW,EAAC,IAAI,EAAE,OAAO,CAAC,EAAE;gBAC/B,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,KAAK,CAAC,OAAO,CAAC,CAAC;SAChB;QAED,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,SAAS;QACP,MAAM,UAAU,GAA2B;YACzC,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;QAEF,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;YAC3B,0EAA0E;YAC1E,qEAAqE;YACrE,2BAA2B;YAC3B,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAA+B,CAAC;YAEvD,IAAI,IAAA,qBAAa,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC5B,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACzD;SACF;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAC/B;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IACH,QAAQ;QACN,OAAO,IAAA,6BAAa,EAAC,IAAI,CAAC,SAAS,EAAE,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC;CACF;AA5ED,oCA4EC;AAED;;;GAGG;AACH,MAAa,qBAEX,SAAQ,YAAkB;IAC1B;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,OAAe,EAAE,IAAW;QACpD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;YACjC,MAAM,IAAI,KAAK,CACb,2DAA2D,CAC5D,CAAC;SACH;QAED,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC7B,CAAC;CACF;AApBD,sDAoBC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,IAAY;IAC1C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AAChE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,iBAAiB,CAAC,CAAU,EAAE,KAAc;IACnD,IAAI,KAAK,KAAK,YAAY,EAAE;QAC1B,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["import type {\n  <PERSON><PERSON>,\n  JsonRpcError as SerializedJsonRpcError,\n} from '@metamask/utils';\nimport { hasProperty, isPlainObject } from '@metamask/utils';\nimport safeStringify from 'fast-safe-stringify';\n\nimport type { OptionalDataWithOptionalCause } from './utils';\nimport { dataHasCause, serializeCause } from './utils';\n\nexport type { SerializedJsonRpcError };\n\n/**\n * Error subclass implementing JSON RPC 2.0 errors and Ethereum RPC errors\n * per EIP-1474.\n *\n * Permits any integer error code.\n */\nexport class JsonRpcError<\n  Data extends OptionalDataWithOptionalCause,\n> extends Error {\n  // The `cause` definition can be removed when tsconfig lib and/or target have changed to >=es2022\n  public cause?: unknown;\n\n  public code: number;\n\n  public data?: Data;\n\n  constructor(code: number, message: string, data?: Data) {\n    if (!Number.isInteger(code)) {\n      throw new Error('\"code\" must be an integer.');\n    }\n\n    if (!message || typeof message !== 'string') {\n      throw new Error('\"message\" must be a non-empty string.');\n    }\n\n    if (dataHasCause(data)) {\n      // @ts-expect-error - Error class does accept options argument depending on runtime, but types are mapping to oldest supported\n      super(message, { cause: data.cause });\n\n      // Browser backwards-compatibility fallback\n      if (!hasProperty(this, 'cause')) {\n        Object.assign(this, { cause: data.cause });\n      }\n    } else {\n      super(message);\n    }\n\n    if (data !== undefined) {\n      this.data = data;\n    }\n\n    this.code = code;\n  }\n\n  /**\n   * Get the error as JSON-serializable object.\n   *\n   * @returns A plain object with all public class properties.\n   */\n  serialize(): SerializedJsonRpcError {\n    const serialized: SerializedJsonRpcError = {\n      code: this.code,\n      message: this.message,\n    };\n\n    if (this.data !== undefined) {\n      // `this.data` is not guaranteed to be a plain object, but this simplifies\n      // the type guard below. We can safely cast it because we know it's a\n      // JSON-serializable value.\n      serialized.data = this.data as { [key: string]: Json };\n\n      if (isPlainObject(this.data)) {\n        serialized.data.cause = serializeCause(this.data.cause);\n      }\n    }\n\n    if (this.stack) {\n      serialized.stack = this.stack;\n    }\n\n    return serialized;\n  }\n\n  /**\n   * Get a string representation of the serialized error, omitting any circular\n   * references.\n   *\n   * @returns A string representation of the serialized error.\n   */\n  toString(): string {\n    return safeStringify(this.serialize(), stringifyReplacer, 2);\n  }\n}\n\n/**\n * Error subclass implementing Ethereum Provider errors per EIP-1193.\n * Permits integer error codes in the [ 1000 <= 4999 ] range.\n */\nexport class EthereumProviderError<\n  Data extends OptionalDataWithOptionalCause,\n> extends JsonRpcError<Data> {\n  /**\n   * Create an Ethereum Provider JSON-RPC error.\n   *\n   * @param code - The JSON-RPC error code. Must be an integer in the\n   * `1000 <= n <= 4999` range.\n   * @param message - The JSON-RPC error message.\n   * @param data - Optional data to include in the error.\n   */\n  constructor(code: number, message: string, data?: Data) {\n    if (!isValidEthProviderCode(code)) {\n      throw new Error(\n        '\"code\" must be an integer such that: 1000 <= code <= 4999',\n      );\n    }\n\n    super(code, message, data);\n  }\n}\n\n/**\n * Check if the given code is a valid JSON-RPC error code.\n *\n * @param code - The code to check.\n * @returns Whether the code is valid.\n */\nfunction isValidEthProviderCode(code: number): boolean {\n  return Number.isInteger(code) && code >= 1000 && code <= 4999;\n}\n\n/**\n * A JSON replacer function that omits circular references.\n *\n * @param _ - The key being replaced.\n * @param value - The value being replaced.\n * @returns The value to use in place of the original value.\n */\nfunction stringifyReplacer(_: unknown, value: unknown): unknown {\n  if (value === '[Circular]') {\n    return undefined;\n  }\n\n  return value;\n}\n"]}