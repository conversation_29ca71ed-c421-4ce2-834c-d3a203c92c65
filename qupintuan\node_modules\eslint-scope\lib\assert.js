/**
 * @fileoverview Assertion utilities.
 * <AUTHOR>
 */

/**
 * Throws an error if the given condition is not truthy.
 * @param {boolean} condition The condition to check.
 * @param {string} message The message to include with the error.
 * @returns {void}
 * @throws {Error} When the condition is not truthy.
 */
export function assert(condition, message = "<PERSON>ser<PERSON> failed.") {
    if (!condition) {
        throw new Error(message);
    }
}
