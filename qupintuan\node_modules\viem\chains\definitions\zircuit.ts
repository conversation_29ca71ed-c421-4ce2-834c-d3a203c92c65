import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 1 // ethereum mainnet

export const zircuit = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 48900,
  name: 'Zircuit Mainnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: {
      http: [
        'https://mainnet.zircuit.com',
        'https://zircuit1-mainnet.liquify.com',
        'https://zircuit1-mainnet.p2pify.com',
        'https://zircuit-mainnet.drpc.org',
      ],
    },
  },
  blockExplorers: {
    default: {
      name: 'Zircuit Explorer',
      url: 'https://explorer.zircuit.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
      },
    },
  },
  testnet: false,
})
