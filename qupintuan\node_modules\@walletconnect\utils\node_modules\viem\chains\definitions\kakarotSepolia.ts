import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const kakarotSepolia = /*#__PURE__*/ define<PERSON>hain({
  id: 1802203764,
  name: '<PERSON>kar<PERSON> Sepolia',
  nativeCurrency: {
    name: '<PERSON><PERSON>',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://sepolia-rpc.kakarot.org'],
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON>kar<PERSON> Scan',
      url: 'https://sepolia.kakarotscan.org',
    },
  },
  testnet: true,
})
