import {
  svg
} from "./chunk-NISX6KSY.js";
import "./chunk-ONY6HBPH.js";

// node_modules/@reown/appkit-ui/dist/esm/src/assets/svg/info-circle.js
var infoCircleSvg = svg`<svg fill="none" viewBox="0 0 14 15">
  <path
    fill="currentColor"
    d="M6 10.49a1 1 0 1 0 2 0v-2a1 1 0 0 0-2 0v2ZM7 4.49a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"
  />
  <path
    fill="currentColor"
    fill-rule="evenodd"
    d="M7 14.99a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm5-7a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z"
    clip-rule="evenodd"
  />
</svg>`;
export {
  infoCircleSvg
};
//# sourceMappingURL=info-circle-3EWCKWW3.js.map
