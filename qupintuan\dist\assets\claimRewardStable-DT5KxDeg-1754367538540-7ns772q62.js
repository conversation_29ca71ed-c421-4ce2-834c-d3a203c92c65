const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-C8294aMB-*************-wb8rvug16.js","assets/web3-uglCpOhK-*************-7ns772q62.js","assets/vendor-CgHzTxSQ-*************-7ns772q62.js","assets/ui-CbWGv3YI-*************-7ns772q62.js","assets/index-QZjJZq-p-*************-mniiclbh2.css"])))=>i.map(i=>d[i]);
import{_ as l}from"./web3-uglCpOhK-*************-7ns772q62.js";import{g as y}from"./index-C8294aMB-*************-wb8rvug16.js";import"./vendor-CgHzTxSQ-*************-7ns772q62.js";import"./ui-CbWGv3YI-*************-7ns772q62.js";async function G({chainId:r,roomId:s,signer:i}){try{if(r!==97)throw new Error(`不支持的链ID: ${r}`);if(!i||!i.account)throw new Error("无效的签名者对象");const{ABIS:t}=await l(async()=>{const{ABIS:c}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(u=>u.o);return{ABIS:c}},__vite__mapDeps([0,1,2,3,4])),e=y(r,"GroupBuyRoom"),_=t.GroupBuyRoom,C=await i.writeContract({address:e,abi:_,functionName:"claimParticipantRefund",args:[BigInt(s)]}),{createPublicClient:m,http:f}=await l(async()=>{const{createPublicClient:c,http:u}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(d=>d.aL);return{createPublicClient:c,http:u}},__vite__mapDeps([1,2,3])),{bscTestnet:o}=await l(async()=>{const{bscTestnet:c}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(u=>u.m);return{bscTestnet:c}},__vite__mapDeps([0,1,2,3,4])),n=await m({chain:o,transport:f()}).waitForTransactionReceipt({hash:C,timeout:12e4});if(!(n.status==="success"||n.status===1||n.status===!0))throw new Error(`交易未成功执行，状态: ${n.status}`);return{receipt:n,txHash:C,isHistoricalUser:!1,hasPaymentRecord:!0}}catch(t){console.error(`❌ [claimRewardStable] 领取失败 - 房间ID: ${s}:`,t);let e=t.message;throw t.message.includes("User has already claimed")?e="您已经领取过此房间的奖励了":t.message.includes("Room not successful")?e="此房间未成功，无法领取奖励":t.message.includes("Not a participant")?e="您不是此房间的参与者":t.message.includes("insufficient funds")&&(e="合约余额不足，请联系管理员"),new Error(e)}}async function O({chainId:r,roomId:s,userAddress:i}){try{if(r!==97)throw new Error(`不支持的链ID: ${r}`);if(!i)return{canClaim:!1,reason:"用户地址不能为空"};const t=y(r,"GroupBuyRoom"),{createPublicClient:e,http:_}=await l(async()=>{const{createPublicClient:a,http:p}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(B=>B.aL);return{createPublicClient:a,http:p}},__vite__mapDeps([1,2,3])),{bscTestnet:C}=await l(async()=>{const{bscTestnet:a}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(p=>p.m);return{bscTestnet:a}},__vite__mapDeps([0,1,2,3,4])),{ABIS:m}=await l(async()=>{const{ABIS:a}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(p=>p.o);return{ABIS:a}},__vite__mapDeps([0,1,2,3,4])),f=e({chain:C,transport:_()}),o=await f.readContract({address:t,abi:m.GroupBuyRoom,functionName:"rooms",args:[BigInt(s)]});if(!o||o[0]==="0x0000000000000000000000000000000000000000")return{canClaim:!1,reason:"房间不存在"};const[w,n,g,c]=await f.readContract({address:t,abi:m.GroupBuyRoom,functionName:"getUserClaimStatus",args:[BigInt(s),i]});if(w||n)return{canClaim:!1,reason:"您已经领取过此房间的奖励了"};const d=await f.readContract({address:t,abi:m.GroupBuyRoom,functionName:"getUserPermissions",args:[BigInt(s),i]}),[b,E,A,I,T,R,P]=d;if(!P){let a="无法领取奖励";return R?a="您已经领取过奖励":!E&&!b?a="您不是此房间的参与者或创建者":o[4]||(a="房间尚未关闭"),{canClaim:!1,reason:a}}const h=o[6];return{canClaim:!0,reason:h?"可以领取奖励":"可以申请退款",isSuccessful:h}}catch(t){return console.error(`❌ [checkClaimEligibility] 检查失败 - 房间ID: ${s}:`,t),{canClaim:!1,reason:`检查失败: ${t.message}`}}}export{O as checkClaimEligibility,G as claimRewardStable,G as default};
