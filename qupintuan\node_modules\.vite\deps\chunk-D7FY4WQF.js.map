{"version": 3, "sources": ["../../viem/utils/encoding/toRlp.ts", "../../viem/utils/chain/assertCurrentChain.ts", "../../viem/utils/formatters/transaction.ts", "../../viem/utils/formatters/block.ts", "../../viem/utils/blob/blobsToCommitments.ts", "../../viem/utils/blob/blobsToProofs.ts", "../../viem/node_modules/@noble/hashes/src/sha256.ts", "../../viem/utils/hash/sha256.ts", "../../viem/utils/blob/commitmentToVersionedHash.ts", "../../viem/utils/blob/commitmentsToVersionedHashes.ts", "../../viem/constants/blob.ts", "../../viem/constants/kzg.ts", "../../viem/errors/blob.ts", "../../viem/utils/blob/toBlobs.ts", "../../viem/utils/blob/toBlobSidecars.ts", "../../viem/utils/transaction/getTransactionType.ts", "../../viem/utils/formatters/log.ts", "../../viem/utils/formatters/transactionReceipt.ts", "../../viem/utils/chain/defineChain.ts", "../../viem/utils/chain/extractChain.ts", "../../viem/utils/transaction/assertTransaction.ts", "../../viem/utils/transaction/serializeAccessList.ts", "../../viem/utils/authorization/serializeAuthorizationList.ts", "../../viem/utils/transaction/serializeTransaction.ts", "../../viem/errors/account.ts"], "sourcesContent": ["import { BaseError } from '../../errors/base.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport {\n  type CreateCursorErrorType,\n  type Cursor,\n  createCursor,\n} from '../cursor.js'\n\nimport { type HexToBytesErrorType, hexToBytes } from './toBytes.js'\nimport { type BytesToHexErrorType, bytesToHex } from './toHex.js'\n\nexport type RecursiveArray<T> = T | readonly RecursiveArray<T>[]\n\ntype To = 'hex' | 'bytes'\n\ntype Encodable = {\n  length: number\n  encode(cursor: Cursor): void\n}\n\nexport type ToRlpReturnType<to extends To> =\n  | (to extends 'bytes' ? ByteArray : never)\n  | (to extends 'hex' ? Hex : never)\n\nexport type ToRlpErrorType =\n  | CreateCursorErrorType\n  | BytesToHexErrorType\n  | HexToBytesErrorType\n  | ErrorType\n\nexport function toRlp<to extends To = 'hex'>(\n  bytes: RecursiveArray<ByteArray> | RecursiveArray<Hex>,\n  to: to | To | undefined = 'hex',\n): ToRlpReturnType<to> {\n  const encodable = getEncodable(bytes)\n  const cursor = createCursor(new Uint8Array(encodable.length))\n  encodable.encode(cursor)\n\n  if (to === 'hex') return bytesToHex(cursor.bytes) as ToRlpReturnType<to>\n  return cursor.bytes as ToRlpReturnType<to>\n}\n\nexport type BytesToRlpErrorType = ToRlpErrorType | ErrorType\n\nexport function bytesToRlp<to extends To = 'bytes'>(\n  bytes: RecursiveArray<ByteArray>,\n  to: to | To | undefined = 'bytes',\n): ToRlpReturnType<to> {\n  return toRlp(bytes, to)\n}\n\nexport type HexToRlpErrorType = ToRlpErrorType | ErrorType\n\nexport function hexToRlp<to extends To = 'hex'>(\n  hex: RecursiveArray<Hex>,\n  to: to | To | undefined = 'hex',\n): ToRlpReturnType<to> {\n  return toRlp(hex, to)\n}\n\nfunction getEncodable(\n  bytes: RecursiveArray<ByteArray> | RecursiveArray<Hex>,\n): Encodable {\n  if (Array.isArray(bytes))\n    return getEncodableList(bytes.map((x) => getEncodable(x)))\n  return getEncodableBytes(bytes as any)\n}\n\nfunction getEncodableList(list: Encodable[]): Encodable {\n  const bodyLength = list.reduce((acc, x) => acc + x.length, 0)\n\n  const sizeOfBodyLength = getSizeOfLength(bodyLength)\n  const length = (() => {\n    if (bodyLength <= 55) return 1 + bodyLength\n    return 1 + sizeOfBodyLength + bodyLength\n  })()\n\n  return {\n    length,\n    encode(cursor: Cursor) {\n      if (bodyLength <= 55) {\n        cursor.pushByte(0xc0 + bodyLength)\n      } else {\n        cursor.pushByte(0xc0 + 55 + sizeOfBodyLength)\n        if (sizeOfBodyLength === 1) cursor.pushUint8(bodyLength)\n        else if (sizeOfBodyLength === 2) cursor.pushUint16(bodyLength)\n        else if (sizeOfBodyLength === 3) cursor.pushUint24(bodyLength)\n        else cursor.pushUint32(bodyLength)\n      }\n      for (const { encode } of list) {\n        encode(cursor)\n      }\n    },\n  }\n}\n\nfunction getEncodableBytes(bytesOrHex: ByteArray | Hex): Encodable {\n  const bytes =\n    typeof bytesOrHex === 'string' ? hexToBytes(bytesOrHex) : bytesOrHex\n\n  const sizeOfBytesLength = getSizeOfLength(bytes.length)\n  const length = (() => {\n    if (bytes.length === 1 && bytes[0] < 0x80) return 1\n    if (bytes.length <= 55) return 1 + bytes.length\n    return 1 + sizeOfBytesLength + bytes.length\n  })()\n\n  return {\n    length,\n    encode(cursor: Cursor) {\n      if (bytes.length === 1 && bytes[0] < 0x80) {\n        cursor.pushBytes(bytes)\n      } else if (bytes.length <= 55) {\n        cursor.pushByte(0x80 + bytes.length)\n        cursor.pushBytes(bytes)\n      } else {\n        cursor.pushByte(0x80 + 55 + sizeOfBytesLength)\n        if (sizeOfBytesLength === 1) cursor.pushUint8(bytes.length)\n        else if (sizeOfBytesLength === 2) cursor.pushUint16(bytes.length)\n        else if (sizeOfBytesLength === 3) cursor.pushUint24(bytes.length)\n        else cursor.pushUint32(bytes.length)\n        cursor.pushBytes(bytes)\n      }\n    },\n  }\n}\n\nfunction getSizeOfLength(length: number) {\n  if (length < 2 ** 8) return 1\n  if (length < 2 ** 16) return 2\n  if (length < 2 ** 24) return 3\n  if (length < 2 ** 32) return 4\n  throw new BaseError('Length is too large.')\n}\n", "import {\n  ChainMismatchError,\n  type ChainMismatchErrorType,\n  ChainNotFoundError,\n  type ChainNotFoundErrorType,\n} from '../../errors/chain.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\n\nexport type AssertCurrentChainParameters = {\n  chain?: Chain | undefined\n  currentChainId: number\n}\n\nexport type AssertCurrentChainErrorType =\n  | ChainNotFoundErrorType\n  | ChainMismatchErrorType\n  | ErrorType\n\nexport function assertCurrentChain({\n  chain,\n  currentChainId,\n}: AssertCurrentChainParameters): void {\n  if (!chain) throw new ChainNotFoundError()\n  if (currentChainId !== chain.id)\n    throw new ChainMismatchError({ chain, currentChainId })\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { SignedAuthorizationList } from '../../types/authorization.js'\nimport type { BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ExtractChainFormatterExclude,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { RpcAuthorizationList, RpcTransaction } from '../../types/rpc.js'\nimport type { Transaction, TransactionType } from '../../types/transaction.js'\nimport type { ExactPartial, UnionLooseOmit } from '../../types/utils.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\n\ntype TransactionPendingDependencies =\n  | 'blockHash'\n  | 'blockNumber'\n  | 'transactionIndex'\n\nexport type FormattedTransaction<\n  chain extends Chain | undefined = undefined,\n  blockTag extends BlockTag = BlockTag,\n  _FormatterReturnType = ExtractChainFormatterReturnType<\n    chain,\n    'transaction',\n    Transaction\n  >,\n  _ExcludedPendingDependencies extends string = TransactionPendingDependencies &\n    ExtractChainFormatterExclude<chain, 'transaction'>,\n> = UnionLooseOmit<_FormatterReturnType, TransactionPendingDependencies> & {\n  [_K in _ExcludedPendingDependencies]: never\n} & Pick<\n    Transaction<bigint, number, blockTag extends 'pending' ? true : false>,\n    TransactionPendingDependencies\n  >\n\nexport const transactionType = {\n  '0x0': 'legacy',\n  '0x1': 'eip2930',\n  '0x2': 'eip1559',\n  '0x3': 'eip4844',\n  '0x4': 'eip7702',\n} as const satisfies Record<Hex, TransactionType>\n\nexport type FormatTransactionErrorType = ErrorType\n\nexport function formatTransaction(transaction: ExactPartial<RpcTransaction>) {\n  const transaction_ = {\n    ...transaction,\n    blockHash: transaction.blockHash ? transaction.blockHash : null,\n    blockNumber: transaction.blockNumber\n      ? BigInt(transaction.blockNumber)\n      : null,\n    chainId: transaction.chainId ? hexToNumber(transaction.chainId) : undefined,\n    gas: transaction.gas ? BigInt(transaction.gas) : undefined,\n    gasPrice: transaction.gasPrice ? BigInt(transaction.gasPrice) : undefined,\n    maxFeePerBlobGas: transaction.maxFeePerBlobGas\n      ? BigInt(transaction.maxFeePerBlobGas)\n      : undefined,\n    maxFeePerGas: transaction.maxFeePerGas\n      ? BigInt(transaction.maxFeePerGas)\n      : undefined,\n    maxPriorityFeePerGas: transaction.maxPriorityFeePerGas\n      ? BigInt(transaction.maxPriorityFeePerGas)\n      : undefined,\n    nonce: transaction.nonce ? hexToNumber(transaction.nonce) : undefined,\n    to: transaction.to ? transaction.to : null,\n    transactionIndex: transaction.transactionIndex\n      ? Number(transaction.transactionIndex)\n      : null,\n    type: transaction.type\n      ? (transactionType as any)[transaction.type]\n      : undefined,\n    typeHex: transaction.type ? transaction.type : undefined,\n    value: transaction.value ? BigInt(transaction.value) : undefined,\n    v: transaction.v ? BigInt(transaction.v) : undefined,\n  } as Transaction\n\n  if (transaction.authorizationList)\n    transaction_.authorizationList = formatAuthorizationList(\n      transaction.authorizationList,\n    )\n\n  transaction_.yParity = (() => {\n    // If `yParity` is provided, we will use it.\n    if (transaction.yParity) return Number(transaction.yParity)\n\n    // If no `yParity` provided, try derive from `v`.\n    if (typeof transaction_.v === 'bigint') {\n      if (transaction_.v === 0n || transaction_.v === 27n) return 0\n      if (transaction_.v === 1n || transaction_.v === 28n) return 1\n      if (transaction_.v >= 35n) return transaction_.v % 2n === 0n ? 1 : 0\n    }\n\n    return undefined\n  })()\n\n  if (transaction_.type === 'legacy') {\n    delete transaction_.accessList\n    delete transaction_.maxFeePerBlobGas\n    delete transaction_.maxFeePerGas\n    delete transaction_.maxPriorityFeePerGas\n    delete transaction_.yParity\n  }\n  if (transaction_.type === 'eip2930') {\n    delete transaction_.maxFeePerBlobGas\n    delete transaction_.maxFeePerGas\n    delete transaction_.maxPriorityFeePerGas\n  }\n  if (transaction_.type === 'eip1559') {\n    delete transaction_.maxFeePerBlobGas\n  }\n  return transaction_\n}\n\nexport type DefineTransactionErrorType = DefineFormatterErrorType | ErrorType\n\nexport const defineTransaction = /*#__PURE__*/ defineFormatter(\n  'transaction',\n  formatTransaction,\n)\n\n//////////////////////////////////////////////////////////////////////////////\n\nfunction formatAuthorizationList(\n  authorizationList: RpcAuthorizationList,\n): SignedAuthorizationList {\n  return authorizationList.map((authorization) => ({\n    address: (authorization as any).address,\n    chainId: Number(authorization.chainId),\n    nonce: Number(authorization.nonce),\n    r: authorization.r,\n    s: authorization.s,\n    yParity: Number(authorization.yParity),\n  })) as SignedAuthorizationList\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Block, BlockTag } from '../../types/block.js'\nimport type { Chain } from '../../types/chain.js'\nimport type {\n  ExtractChainFormatterExclude,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { Hash } from '../../types/misc.js'\nimport type { RpcBlock } from '../../types/rpc.js'\nimport type { ExactPartial, Prettify } from '../../types/utils.js'\n\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\nimport { type FormattedTransaction, formatTransaction } from './transaction.js'\n\ntype BlockPendingDependencies = 'hash' | 'logsBloom' | 'nonce' | 'number'\n\nexport type FormattedBlock<\n  chain extends Chain | undefined = undefined,\n  includeTransactions extends boolean = boolean,\n  blockTag extends BlockTag = BlockTag,\n  _FormatterReturnType = ExtractChainFormatterReturnType<\n    chain,\n    'block',\n    Block<bigint, includeTransactions>\n  >,\n  _ExcludedPendingDependencies extends string = BlockPendingDependencies &\n    ExtractChainFormatterExclude<chain, 'block'>,\n  _Formatted = Omit<_FormatterReturnType, BlockPendingDependencies> & {\n    [_key in _ExcludedPendingDependencies]: never\n  } & Pick<\n      Block<bigint, includeTransactions, blockTag>,\n      BlockPendingDependencies\n    >,\n  _Transactions = includeTransactions extends true\n    ? Prettify<FormattedTransaction<chain, blockTag>>[]\n    : Hash[],\n> = Omit<_Formatted, 'transactions'> & {\n  transactions: _Transactions\n}\n\nexport type FormatBlockErrorType = ErrorType\n\nexport function formatBlock(block: ExactPartial<RpcBlock>) {\n  const transactions = (block.transactions ?? []).map((transaction) => {\n    if (typeof transaction === 'string') return transaction\n    return formatTransaction(transaction)\n  })\n  return {\n    ...block,\n    baseFeePerGas: block.baseFeePerGas ? BigInt(block.baseFeePerGas) : null,\n    blobGasUsed: block.blobGasUsed ? BigInt(block.blobGasUsed) : undefined,\n    difficulty: block.difficulty ? BigInt(block.difficulty) : undefined,\n    excessBlobGas: block.excessBlobGas\n      ? BigInt(block.excessBlobGas)\n      : undefined,\n    gasLimit: block.gasLimit ? BigInt(block.gasLimit) : undefined,\n    gasUsed: block.gasUsed ? BigInt(block.gasUsed) : undefined,\n    hash: block.hash ? block.hash : null,\n    logsBloom: block.logsBloom ? block.logsBloom : null,\n    nonce: block.nonce ? block.nonce : null,\n    number: block.number ? BigInt(block.number) : null,\n    size: block.size ? BigInt(block.size) : undefined,\n    timestamp: block.timestamp ? BigInt(block.timestamp) : undefined,\n    transactions,\n    totalDifficulty: block.totalDifficulty\n      ? BigInt(block.totalDifficulty)\n      : null,\n  } as Block\n}\n\nexport type DefineBlockErrorType = DefineFormatterErrorType | ErrorType\n\nexport const defineBlock = /*#__PURE__*/ defineFormatter('block', formatBlock)\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Kzg } from '../../types/kzg.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type HexToBytesErrorType, hexToBytes } from '../encoding/toBytes.js'\nimport { type BytesToHexErrorType, bytesToHex } from '../encoding/toHex.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type BlobsToCommitmentsParameters<\n  blobs extends readonly ByteArray[] | readonly Hex[] =\n    | readonly ByteArray[]\n    | readonly Hex[],\n  to extends To | undefined = undefined,\n> = {\n  /** Blobs to transform into commitments. */\n  blobs: blobs | readonly ByteArray[] | readonly Hex[]\n  /** KZG implementation. */\n  kzg: Pick<Kzg, 'blobToKzgCommitment'>\n  /** Return type. */\n  to?: to | To | undefined\n}\n\nexport type BlobsToCommitmentsReturnType<to extends To> =\n  | (to extends 'bytes' ? readonly ByteArray[] : never)\n  | (to extends 'hex' ? readonly Hex[] : never)\n\nexport type BlobsToCommitmentsErrorType =\n  | HexToBytesErrorType\n  | BytesToHexErrorType\n  | ErrorType\n\n/**\n * Compute commitments from a list of blobs.\n *\n * @example\n * ```ts\n * import { blobsToCommitments, toBlobs } from 'viem'\n * import { kzg } from './kzg'\n *\n * const blobs = toBlobs({ data: '0x1234' })\n * const commitments = blobsToCommitments({ blobs, kzg })\n * ```\n */\nexport function blobsToCommitments<\n  const blobs extends readonly ByteArray[] | readonly Hex[],\n  to extends To =\n    | (blobs extends readonly Hex[] ? 'hex' : never)\n    | (blobs extends readonly ByteArray[] ? 'bytes' : never),\n>(\n  parameters: BlobsToCommitmentsParameters<blobs, to>,\n): BlobsToCommitmentsReturnType<to> {\n  const { kzg } = parameters\n\n  const to =\n    parameters.to ?? (typeof parameters.blobs[0] === 'string' ? 'hex' : 'bytes')\n  const blobs = (\n    typeof parameters.blobs[0] === 'string'\n      ? parameters.blobs.map((x) => hexToBytes(x as any))\n      : parameters.blobs\n  ) as ByteArray[]\n\n  const commitments: ByteArray[] = []\n  for (const blob of blobs)\n    commitments.push(Uint8Array.from(kzg.blobToKzgCommitment(blob)))\n\n  return (to === 'bytes'\n    ? commitments\n    : commitments.map((x) =>\n        bytesToHex(x),\n      )) as {} as BlobsToCommitmentsReturnType<to>\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Kzg } from '../../types/kzg.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type HexToBytesErrorType, hexToBytes } from '../encoding/toBytes.js'\nimport { type BytesToHexErrorType, bytesToHex } from '../encoding/toHex.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type blobsToProofsParameters<\n  blobs extends readonly ByteArray[] | readonly Hex[],\n  commitments extends readonly ByteArray[] | readonly Hex[],\n  to extends To =\n    | (blobs extends readonly Hex[] ? 'hex' : never)\n    | (blobs extends readonly ByteArray[] ? 'bytes' : never),\n  ///\n  _blobsType =\n    | (blobs extends readonly Hex[] ? readonly Hex[] : never)\n    | (blobs extends readonly ByteArray[] ? readonly ByteArray[] : never),\n> = {\n  /** Blobs to transform into proofs. */\n  blobs: blobs\n  /** Commitments for the blobs. */\n  commitments: commitments &\n    (commitments extends _blobsType\n      ? {}\n      : `commitments must be the same type as blobs`)\n  /** KZG implementation. */\n  kzg: Pick<Kzg, 'computeBlobKzgProof'>\n  /** Return type. */\n  to?: to | To | undefined\n}\n\nexport type blobsToProofsReturnType<to extends To> =\n  | (to extends 'bytes' ? ByteArray[] : never)\n  | (to extends 'hex' ? Hex[] : never)\n\nexport type blobsToProofsErrorType =\n  | BytesToHexErrorType\n  | HexToBytesErrorType\n  | ErrorType\n\n/**\n * Compute the proofs for a list of blobs and their commitments.\n *\n * @example\n * ```ts\n * import {\n *   blobsToCommitments,\n *   toBlobs\n * } from 'viem'\n * import { kzg } from './kzg'\n *\n * const blobs = toBlobs({ data: '0x1234' })\n * const commitments = blobsToCommitments({ blobs, kzg })\n * const proofs = blobsToProofs({ blobs, commitments, kzg })\n * ```\n */\nexport function blobsToProofs<\n  const blobs extends readonly ByteArray[] | readonly Hex[],\n  const commitments extends readonly ByteArray[] | readonly Hex[],\n  to extends To =\n    | (blobs extends readonly Hex[] ? 'hex' : never)\n    | (blobs extends readonly ByteArray[] ? 'bytes' : never),\n>(\n  parameters: blobsToProofsParameters<blobs, commitments, to>,\n): blobsToProofsReturnType<to> {\n  const { kzg } = parameters\n\n  const to =\n    parameters.to ?? (typeof parameters.blobs[0] === 'string' ? 'hex' : 'bytes')\n\n  const blobs = (\n    typeof parameters.blobs[0] === 'string'\n      ? parameters.blobs.map((x) => hexToBytes(x as any))\n      : parameters.blobs\n  ) as ByteArray[]\n  const commitments = (\n    typeof parameters.commitments[0] === 'string'\n      ? parameters.commitments.map((x) => hexToBytes(x as any))\n      : parameters.commitments\n  ) as ByteArray[]\n\n  const proofs: ByteArray[] = []\n  for (let i = 0; i < blobs.length; i++) {\n    const blob = blobs[i]\n    const commitment = commitments[i]\n    proofs.push(Uint8Array.from(kzg.computeBlobKzgProof(blob, commitment)))\n  }\n\n  return (to === 'bytes'\n    ? proofs\n    : proofs.map((x) => bytesToHex(x))) as {} as blobsToProofsReturnType<to>\n}\n", "/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n * @deprecated\n */\nimport {\n  SHA224 as SHA224n,\n  sha224 as sha224n,\n  SHA256 as SHA256n,\n  sha256 as sha256n,\n} from './sha2.ts';\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA256: typeof SHA256n = SHA256n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha256: typeof sha256n = sha256n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA224: typeof SHA224n = SHA224n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha224: typeof sha224n = sha224n;\n", "import { sha256 as noble_sha256 } from '@noble/hashes/sha256'\n\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type IsHexErrorType, isHex } from '../data/isHex.js'\nimport { type ToBytesErrorType, toBytes } from '../encoding/toBytes.js'\nimport { type ToHexErrorType, toHex } from '../encoding/toHex.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type Sha256Hash<to extends To> =\n  | (to extends 'bytes' ? ByteArray : never)\n  | (to extends 'hex' ? Hex : never)\n\nexport type Sha256ErrorType =\n  | IsHexErrorType\n  | ToBytesErrorType\n  | ToHexErrorType\n  | ErrorType\n\nexport function sha256<to extends To = 'hex'>(\n  value: Hex | ByteArray,\n  to_?: to | undefined,\n): Sha256Hash<to> {\n  const to = to_ || 'hex'\n  const bytes = noble_sha256(\n    isHex(value, { strict: false }) ? toBytes(value) : value,\n  )\n  if (to === 'bytes') return bytes as Sha256Hash<to>\n  return toHex(bytes) as Sha256Hash<to>\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type BytesToHexErrorType, bytesToHex } from '../encoding/toHex.js'\nimport { type Sha256ErrorType, sha256 } from '../hash/sha256.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type CommitmentToVersionedHashParameters<\n  commitment extends Uint8Array | Hex = Uint8Array | Hex,\n  to extends To | undefined = undefined,\n> = {\n  /** Commitment from blob. */\n  commitment: commitment | Uint8Array | Hex\n  /** Return type. */\n  to?: to | To | undefined\n  /** Version to tag onto the hash. */\n  version?: number | undefined\n}\n\nexport type CommitmentToVersionedHashReturnType<to extends To> =\n  | (to extends 'bytes' ? ByteArray : never)\n  | (to extends 'hex' ? Hex : never)\n\nexport type CommitmentToVersionedHashErrorType =\n  | Sha256ErrorType\n  | BytesToHexErrorType\n  | ErrorType\n\n/**\n * Transform a commitment to it's versioned hash.\n *\n * @example\n * ```ts\n * import {\n *   blobsToCommitments,\n *   commitmentToVersionedHash,\n *   toBlobs\n * } from 'viem'\n * import { kzg } from './kzg'\n *\n * const blobs = toBlobs({ data: '0x1234' })\n * const [commitment] = blobsToCommitments({ blobs, kzg })\n * const versionedHash = commitmentToVersionedHash({ commitment })\n * ```\n */\nexport function commitmentToVersionedHash<\n  const commitment extends Hex | ByteArray,\n  to extends To =\n    | (commitment extends Hex ? 'hex' : never)\n    | (commitment extends ByteArray ? 'bytes' : never),\n>(\n  parameters: CommitmentToVersionedHashParameters<commitment, to>,\n): CommitmentToVersionedHashReturnType<to> {\n  const { commitment, version = 1 } = parameters\n  const to = parameters.to ?? (typeof commitment === 'string' ? 'hex' : 'bytes')\n\n  const versionedHash = sha256(commitment, 'bytes')\n  versionedHash.set([version], 0)\n  return (\n    to === 'bytes' ? versionedHash : bytesToHex(versionedHash)\n  ) as CommitmentToVersionedHashReturnType<to>\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport {\n  type CommitmentToVersionedHashErrorType,\n  commitmentToVersionedHash,\n} from './commitmentToVersionedHash.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type CommitmentsToVersionedHashesParameters<\n  commitments extends readonly Uint8Array[] | readonly Hex[] =\n    | readonly Uint8Array[]\n    | readonly Hex[],\n  to extends To | undefined = undefined,\n> = {\n  /** Commitments from blobs. */\n  commitments: commitments | readonly Uint8Array[] | readonly Hex[]\n  /** Return type. */\n  to?: to | To | undefined\n  /** Version to tag onto the hashes. */\n  version?: number | undefined\n}\n\nexport type CommitmentsToVersionedHashesReturnType<to extends To> =\n  | (to extends 'bytes' ? readonly ByteArray[] : never)\n  | (to extends 'hex' ? readonly Hex[] : never)\n\nexport type CommitmentsToVersionedHashesErrorType =\n  | CommitmentToVersionedHashErrorType\n  | ErrorType\n\n/**\n * Transform a list of commitments to their versioned hashes.\n *\n * @example\n * ```ts\n * import {\n *   blobsToCommitments,\n *   commitmentsToVersionedHashes,\n *   toBlobs\n * } from 'viem'\n * import { kzg } from './kzg'\n *\n * const blobs = toBlobs({ data: '0x1234' })\n * const commitments = blobsToCommitments({ blobs, kzg })\n * const versionedHashes = commitmentsToVersionedHashes({ commitments })\n * ```\n */\nexport function commitmentsToVersionedHashes<\n  const commitments extends readonly Uint8Array[] | readonly Hex[],\n  to extends To =\n    | (commitments extends readonly Hex[] ? 'hex' : never)\n    | (commitments extends readonly ByteArray[] ? 'bytes' : never),\n>(\n  parameters: CommitmentsToVersionedHashesParameters<commitments, to>,\n): CommitmentsToVersionedHashesReturnType<to> {\n  const { commitments, version } = parameters\n\n  const to =\n    parameters.to ?? (typeof commitments[0] === 'string' ? 'hex' : 'bytes')\n\n  const hashes: Uint8Array[] | Hex[] = []\n  for (const commitment of commitments) {\n    hashes.push(\n      commitmentToVersionedHash({\n        commitment,\n        to,\n        version,\n      }) as any,\n    )\n  }\n  return hashes as any\n}\n", "// https://github.com/ethereum/EIPs/blob/master/EIPS/eip-4844.md#parameters\n\n/** Blob limit per transaction. */\nconst blobsPerTransaction = 6\n\n/** The number of bytes in a BLS scalar field element. */\nexport const bytesPerFieldElement = 32\n\n/** The number of field elements in a blob. */\nexport const fieldElementsPerBlob = 4096\n\n/** The number of bytes in a blob. */\nexport const bytesPerBlob = bytesPerFieldElement * fieldElementsPerBlob\n\n/** Blob bytes limit per transaction. */\nexport const maxBytesPerTransaction =\n  bytesPerBlob * blobsPerTransaction -\n  // terminator byte (0x80).\n  1 -\n  // zero byte (0x00) appended to each field element.\n  1 * fieldElementsPerBlob * blobsPerTransaction\n", "// https://github.com/ethereum/EIPs/blob/master/EIPS/eip-4844.md#parameters\n\nexport const versionedHashVersionKzg = 1\n", "import { versionedHashVersionKzg } from '../constants/kzg.js'\nimport type { Hash } from '../types/misc.js'\n\nimport { BaseError } from './base.js'\n\nexport type BlobSizeTooLargeErrorType = BlobSizeTooLargeError & {\n  name: 'BlobSizeTooLargeError'\n}\nexport class BlobSizeTooLargeError extends BaseError {\n  constructor({ maxSize, size }: { maxSize: number; size: number }) {\n    super('Blob size is too large.', {\n      metaMessages: [`Max: ${maxSize} bytes`, `Given: ${size} bytes`],\n      name: 'BlobSizeTooLargeError',\n    })\n  }\n}\n\nexport type EmptyBlobErrorType = EmptyBlobError & {\n  name: 'EmptyBlobError'\n}\nexport class EmptyBlobError extends BaseError {\n  constructor() {\n    super('Blob data must not be empty.', { name: 'EmptyBlobError' })\n  }\n}\n\nexport type InvalidVersionedHashSizeErrorType =\n  InvalidVersionedHashSizeError & {\n    name: 'InvalidVersionedHashSizeError'\n  }\nexport class InvalidVersionedHashSizeError extends BaseError {\n  constructor({\n    hash,\n    size,\n  }: {\n    hash: Hash\n    size: number\n  }) {\n    super(`Versioned hash \"${hash}\" size is invalid.`, {\n      metaMessages: ['Expected: 32', `Received: ${size}`],\n      name: 'InvalidVersionedHashSizeError',\n    })\n  }\n}\n\nexport type InvalidVersionedHashVersionErrorType =\n  InvalidVersionedHashVersionError & {\n    name: 'InvalidVersionedHashVersionError'\n  }\nexport class InvalidVersionedHashVersionError extends BaseError {\n  constructor({\n    hash,\n    version,\n  }: {\n    hash: Hash\n    version: number\n  }) {\n    super(`Versioned hash \"${hash}\" version is invalid.`, {\n      metaMessages: [\n        `Expected: ${versionedHashVersionKzg}`,\n        `Received: ${version}`,\n      ],\n      name: 'InvalidVersionedHashVersionError',\n    })\n  }\n}\n", "import {\n  bytesPerBlob,\n  bytesPerFieldElement,\n  fieldElementsPerBlob,\n  maxBytesPerTransaction,\n} from '../../constants/blob.js'\nimport {\n  BlobSizeTooLargeError,\n  type BlobSizeTooLargeErrorType,\n  EmptyBlobError,\n  type EmptyBlobErrorType,\n} from '../../errors/blob.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport { type CreateCursorErrorType, createCursor } from '../cursor.js'\nimport { type SizeErrorType, size } from '../data/size.js'\nimport { type HexToBytesErrorType, hexToBytes } from '../encoding/toBytes.js'\nimport { type BytesToHexErrorType, bytesToHex } from '../encoding/toHex.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type ToBlobsParameters<\n  data extends Hex | ByteArray = Hex | ByteArray,\n  to extends To | undefined = undefined,\n> = {\n  /** Data to transform to a blob. */\n  data: data | Hex | ByteArray\n  /** Return type. */\n  to?: to | To | undefined\n}\n\nexport type ToBlobsReturnType<to extends To> =\n  | (to extends 'bytes' ? readonly ByteArray[] : never)\n  | (to extends 'hex' ? readonly Hex[] : never)\n\nexport type ToBlobsErrorType =\n  | BlobSizeTooLargeErrorType\n  | BytesToHexErrorType\n  | CreateCursorErrorType\n  | EmptyBlobErrorType\n  | HexToBytesErrorType\n  | SizeErrorType\n  | ErrorType\n\n/**\n * Transforms arbitrary data to blobs.\n *\n * @example\n * ```ts\n * import { toBlobs, stringToHex } from 'viem'\n *\n * const blobs = toBlobs({ data: stringToHex('hello world') })\n * ```\n */\nexport function toBlobs<\n  const data extends Hex | ByteArray,\n  to extends To =\n    | (data extends Hex ? 'hex' : never)\n    | (data extends ByteArray ? 'bytes' : never),\n>(parameters: ToBlobsParameters<data, to>): ToBlobsReturnType<to> {\n  const to =\n    parameters.to ?? (typeof parameters.data === 'string' ? 'hex' : 'bytes')\n  const data = (\n    typeof parameters.data === 'string'\n      ? hexToBytes(parameters.data)\n      : parameters.data\n  ) as ByteArray\n\n  const size_ = size(data)\n  if (!size_) throw new EmptyBlobError()\n  if (size_ > maxBytesPerTransaction)\n    throw new BlobSizeTooLargeError({\n      maxSize: maxBytesPerTransaction,\n      size: size_,\n    })\n\n  const blobs = []\n\n  let active = true\n  let position = 0\n  while (active) {\n    const blob = createCursor(new Uint8Array(bytesPerBlob))\n\n    let size = 0\n    while (size < fieldElementsPerBlob) {\n      const bytes = data.slice(position, position + (bytesPerFieldElement - 1))\n\n      // Push a zero byte so the field element doesn't overflow the BLS modulus.\n      blob.pushByte(0x00)\n\n      // Push the current segment of data bytes.\n      blob.pushBytes(bytes)\n\n      // If we detect that the current segment of data bytes is less than 31 bytes,\n      // we can stop processing and push a terminator byte to indicate the end of the blob.\n      if (bytes.length < 31) {\n        blob.pushByte(0x80)\n        active = false\n        break\n      }\n\n      size++\n      position += 31\n    }\n\n    blobs.push(blob)\n  }\n\n  return (\n    to === 'bytes'\n      ? blobs.map((x) => x.bytes)\n      : blobs.map((x) => bytesToHex(x.bytes))\n  ) as any\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { BlobSidecars } from '../../types/eip4844.js'\nimport type { Kzg } from '../../types/kzg.js'\nimport type { ByteArray, Hex } from '../../types/misc.js'\nimport type { OneOf } from '../../types/utils.js'\nimport {\n  type BlobsToCommitmentsErrorType,\n  blobsToCommitments,\n} from './blobsToCommitments.js'\nimport { blobsToProofs, type blobsToProofsErrorType } from './blobsToProofs.js'\nimport { type ToBlobsErrorType, toBlobs } from './toBlobs.js'\n\ntype To = 'hex' | 'bytes'\n\nexport type ToBlobSidecarsParameters<\n  data extends Hex | ByteArray | undefined = undefined,\n  blobs extends readonly Hex[] | readonly ByteArray[] | undefined = undefined,\n  to extends To =\n    | (blobs extends readonly Hex[] ? 'hex' : never)\n    | (blobs extends readonly ByteArray[] ? 'bytes' : never),\n  ///\n  _blobsType =\n    | (blobs extends readonly Hex[] ? readonly Hex[] : never)\n    | (blobs extends readonly ByteArray[] ? readonly ByteArray[] : never),\n> = {\n  /** Return type. */\n  to?: to | To | undefined\n} & OneOf<\n  | {\n      /** Data to transform into blobs. */\n      data: data | Hex | ByteArray\n      /** KZG implementation. */\n      kzg: Kzg\n    }\n  | {\n      /** Blobs. */\n      blobs: blobs | readonly Hex[] | readonly ByteArray[]\n      /** Commitment for each blob. */\n      commitments: _blobsType | readonly Hex[] | readonly ByteArray[]\n      /** Proof for each blob. */\n      proofs: _blobsType | readonly Hex[] | readonly ByteArray[]\n    }\n>\n\nexport type ToBlobSidecarsReturnType<to extends To> =\n  | (to extends 'bytes' ? BlobSidecars<ByteArray> : never)\n  | (to extends 'hex' ? BlobSidecars<Hex> : never)\n\nexport type ToBlobSidecarsErrorType =\n  | BlobsToCommitmentsErrorType\n  | ToBlobsErrorType\n  | blobsToProofsErrorType\n  | ErrorType\n\n/**\n * Transforms arbitrary data (or blobs, commitments, & proofs) into a sidecar array.\n *\n * @example\n * ```ts\n * import { toBlobSidecars, stringToHex } from 'viem'\n *\n * const sidecars = toBlobSidecars({ data: stringToHex('hello world') })\n * ```\n *\n * @example\n * ```ts\n * import {\n *   blobsToCommitments,\n *   toBlobs,\n *   blobsToProofs,\n *   toBlobSidecars,\n *   stringToHex\n * } from 'viem'\n *\n * const blobs = toBlobs({ data: stringToHex('hello world') })\n * const commitments = blobsToCommitments({ blobs, kzg })\n * const proofs = blobsToProofs({ blobs, commitments, kzg })\n *\n * const sidecars = toBlobSidecars({ blobs, commitments, proofs })\n * ```\n */\nexport function toBlobSidecars<\n  const data extends Hex | ByteArray | undefined = undefined,\n  const blobs extends\n    | readonly Hex[]\n    | readonly ByteArray[]\n    | undefined = undefined,\n  to extends To =\n    | (data extends Hex ? 'hex' : never)\n    | (data extends ByteArray ? 'bytes' : never)\n    | (blobs extends readonly Hex[] ? 'hex' : never)\n    | (blobs extends readonly ByteArray[] ? 'bytes' : never),\n>(\n  parameters: ToBlobSidecarsParameters<data, blobs, to>,\n): ToBlobSidecarsReturnType<to> {\n  const { data, kzg, to } = parameters\n  const blobs = parameters.blobs ?? toBlobs({ data: data!, to })\n  const commitments =\n    parameters.commitments ?? blobsToCommitments({ blobs, kzg: kzg!, to })\n  const proofs =\n    parameters.proofs ?? blobsToProofs({ blobs, commitments, kzg: kzg!, to })\n\n  const sidecars: BlobSidecars = []\n  for (let i = 0; i < blobs.length; i++)\n    sidecars.push({\n      blob: blobs[i],\n      commitment: commitments[i],\n      proof: proofs[i],\n    })\n\n  return sidecars as ToBlobSidecarsReturnType<to>\n}\n", "import {\n  InvalidSerializableTransactionError,\n  type InvalidSerializableTransactionErrorType,\n} from '../../errors/transaction.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type {\n  FeeValuesEIP1559,\n  FeeV<PERSON><PERSON>EIP4844,\n  FeeValuesLegacy,\n} from '../../index.js'\nimport type {\n  TransactionRequestGeneric,\n  TransactionSerializableEIP2930,\n  TransactionSerializableEIP4844,\n  TransactionSerializableEIP7702,\n  TransactionSerializableGeneric,\n} from '../../types/transaction.js'\nimport type { Assign, ExactPartial, IsNever, OneOf } from '../../types/utils.js'\n\nexport type GetTransactionType<\n  transaction extends OneOf<\n    TransactionSerializableGeneric | TransactionRequestGeneric\n  > = TransactionSerializableGeneric,\n  result =\n    | (transaction extends LegacyProperties ? 'legacy' : never)\n    | (transaction extends EIP1559Properties ? 'eip1559' : never)\n    | (transaction extends EIP2930Properties ? 'eip2930' : never)\n    | (transaction extends EIP4844Properties ? 'eip4844' : never)\n    | (transaction extends EIP7702Properties ? 'eip7702' : never)\n    | (transaction['type'] extends TransactionSerializableGeneric['type']\n        ? Extract<transaction['type'], string>\n        : never),\n> = IsNever<keyof transaction> extends true\n  ? string\n  : IsNever<result> extends false\n    ? result\n    : string\n\nexport type GetTransactionTypeErrorType =\n  | InvalidSerializableTransactionErrorType\n  | ErrorType\n\nexport function getTransactionType<\n  const transaction extends OneOf<\n    TransactionSerializableGeneric | TransactionRequestGeneric\n  >,\n>(transaction: transaction): GetTransactionType<transaction> {\n  if (transaction.type)\n    return transaction.type as GetTransactionType<transaction>\n\n  if (typeof transaction.authorizationList !== 'undefined')\n    return 'eip7702' as any\n\n  if (\n    typeof transaction.blobs !== 'undefined' ||\n    typeof transaction.blobVersionedHashes !== 'undefined' ||\n    typeof transaction.maxFeePerBlobGas !== 'undefined' ||\n    typeof transaction.sidecars !== 'undefined'\n  )\n    return 'eip4844' as any\n\n  if (\n    typeof transaction.maxFeePerGas !== 'undefined' ||\n    typeof transaction.maxPriorityFeePerGas !== 'undefined'\n  ) {\n    return 'eip1559' as any\n  }\n\n  if (typeof transaction.gasPrice !== 'undefined') {\n    if (typeof transaction.accessList !== 'undefined') return 'eip2930' as any\n    return 'legacy' as any\n  }\n\n  throw new InvalidSerializableTransactionError({ transaction })\n}\n\n////////////////////////////////////////////////////////////////////////////////////////////\n// Types\n\ntype BaseProperties = {\n  accessList?: undefined\n  authorizationList?: undefined\n  blobs?: undefined\n  blobVersionedHashes?: undefined\n  gasPrice?: undefined\n  maxFeePerBlobGas?: undefined\n  maxFeePerGas?: undefined\n  maxPriorityFeePerGas?: undefined\n  sidecars?: undefined\n}\n\ntype LegacyProperties = Assign<BaseProperties, FeeValuesLegacy>\ntype EIP1559Properties = Assign<\n  BaseProperties,\n  OneOf<\n    | {\n        maxFeePerGas: FeeValuesEIP1559['maxFeePerGas']\n      }\n    | {\n        maxPriorityFeePerGas: FeeValuesEIP1559['maxPriorityFeePerGas']\n      },\n    FeeValuesEIP1559\n  > & {\n    accessList?: TransactionSerializableEIP2930['accessList'] | undefined\n  }\n>\ntype EIP2930Properties = Assign<\n  ExactPartial<LegacyProperties>,\n  {\n    accessList: TransactionSerializableEIP2930['accessList']\n  }\n>\ntype EIP4844Properties = Assign<\n  ExactPartial<EIP1559Properties>,\n  ExactPartial<FeeValuesEIP4844> &\n    OneOf<\n      | {\n          blobs: TransactionSerializableEIP4844['blobs']\n        }\n      | {\n          blobVersionedHashes: TransactionSerializableEIP4844['blobVersionedHashes']\n        }\n      | {\n          sidecars: TransactionSerializableEIP4844['sidecars']\n        },\n      TransactionSerializableEIP4844\n    >\n>\ntype EIP7702Properties = Assign<\n  ExactPartial<EIP1559Properties>,\n  {\n    authorizationList: TransactionSerializableEIP7702['authorizationList']\n  }\n>\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Log } from '../../types/log.js'\nimport type { RpcLog } from '../../types/rpc.js'\nimport type { ExactPartial } from '../../types/utils.js'\n\nexport type FormatLogErrorType = ErrorType\n\nexport function formatLog(\n  log: ExactPartial<RpcLog>,\n  {\n    args,\n    eventName,\n  }: { args?: unknown | undefined; eventName?: string | undefined } = {},\n) {\n  return {\n    ...log,\n    blockHash: log.blockHash ? log.blockHash : null,\n    blockNumber: log.blockNumber ? BigInt(log.blockNumber) : null,\n    logIndex: log.logIndex ? Number(log.logIndex) : null,\n    transactionHash: log.transactionHash ? log.transactionHash : null,\n    transactionIndex: log.transactionIndex\n      ? Number(log.transactionIndex)\n      : null,\n    ...(eventName ? { args, eventName } : {}),\n  } as Log\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type {\n  Chain,\n  ExtractChainFormatterReturnType,\n} from '../../types/chain.js'\nimport type { RpcTransactionReceipt } from '../../types/rpc.js'\nimport type { TransactionReceipt } from '../../types/transaction.js'\nimport type { ExactPartial } from '../../types/utils.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\n\nimport { type DefineFormatterErrorType, defineFormatter } from './formatter.js'\nimport { formatLog } from './log.js'\nimport { transactionType } from './transaction.js'\n\nexport type FormattedTransactionReceipt<\n  chain extends Chain | undefined = undefined,\n> = ExtractChainFormatterReturnType<\n  chain,\n  'transactionReceipt',\n  TransactionReceipt\n>\n\nexport const receiptStatuses = {\n  '0x0': 'reverted',\n  '0x1': 'success',\n} as const\n\nexport type FormatTransactionReceiptErrorType = ErrorType\n\nexport function formatTransactionReceipt(\n  transactionReceipt: ExactPartial<RpcTransactionReceipt>,\n) {\n  const receipt = {\n    ...transactionReceipt,\n    blockNumber: transactionReceipt.blockNumber\n      ? BigInt(transactionReceipt.blockNumber)\n      : null,\n    contractAddress: transactionReceipt.contractAddress\n      ? transactionReceipt.contractAddress\n      : null,\n    cumulativeGasUsed: transactionReceipt.cumulativeGasUsed\n      ? BigInt(transactionReceipt.cumulativeGasUsed)\n      : null,\n    effectiveGasPrice: transactionReceipt.effectiveGasPrice\n      ? BigInt(transactionReceipt.effectiveGasPrice)\n      : null,\n    gasUsed: transactionReceipt.gasUsed\n      ? BigInt(transactionReceipt.gasUsed)\n      : null,\n    logs: transactionReceipt.logs\n      ? transactionReceipt.logs.map((log) => formatLog(log))\n      : null,\n    to: transactionReceipt.to ? transactionReceipt.to : null,\n    transactionIndex: transactionReceipt.transactionIndex\n      ? hexToNumber(transactionReceipt.transactionIndex)\n      : null,\n    status: transactionReceipt.status\n      ? receiptStatuses[transactionReceipt.status]\n      : null,\n    type: transactionReceipt.type\n      ? transactionType[\n          transactionReceipt.type as keyof typeof transactionType\n        ] || transactionReceipt.type\n      : null,\n  } as TransactionReceipt\n\n  if (transactionReceipt.blobGasPrice)\n    receipt.blobGasPrice = BigInt(transactionReceipt.blobGasPrice)\n  if (transactionReceipt.blobGasUsed)\n    receipt.blobGasUsed = BigInt(transactionReceipt.blobGasUsed)\n\n  return receipt\n}\n\nexport type DefineTransactionReceiptErrorType =\n  | DefineFormatterErrorType\n  | ErrorType\n\nexport const defineTransactionReceipt = /*#__PURE__*/ defineFormatter(\n  'transactionReceipt',\n  formatTransactionReceipt,\n)\n", "import type { Chain, ChainFormatters } from '../../types/chain.js'\nimport type { Assign, Prettify } from '../../types/utils.js'\n\nexport function define<PERSON><PERSON>n<\n  formatters extends ChainFormatters,\n  const chain extends Chain<formatters>,\n>(chain: chain): Prettify<Assign<Chain<undefined>, chain>> {\n  return {\n    formatters: undefined,\n    fees: undefined,\n    serializers: undefined,\n    ...chain,\n  } as Assign<Chain<undefined>, chain>\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type { Chain } from '../../types/chain.js'\n\nexport type ExtractChainParameters<\n  chains extends readonly Chain[],\n  chainId extends chains[number]['id'],\n> = {\n  chains: chains\n  id: chainId | chains[number]['id']\n}\n\nexport type ExtractChainReturnType<\n  chains extends readonly Chain[],\n  chainId extends chains[number]['id'],\n> = Extract<chains[number], { id: chainId }>\n\nexport type ExtractChainErrorType = ErrorType\n\nexport function extractChain<\n  const chains extends readonly Chain[],\n  chainId extends chains[number]['id'],\n>({\n  chains,\n  id,\n}: ExtractChainParameters<chains, chainId>): ExtractChainReturnType<\n  chains,\n  chainId\n> {\n  return chains.find((chain) => chain.id === id) as ExtractChainReturnType<\n    chains,\n    chainId\n  >\n}\n", "import { versionedHashVersionKzg } from '../../constants/kzg.js'\nimport { maxUint256 } from '../../constants/number.js'\nimport {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../../errors/address.js'\nimport { BaseError, type BaseErrorType } from '../../errors/base.js'\nimport {\n  EmptyBlobError,\n  type EmptyBlobErrorType,\n  InvalidVersionedHashSizeError,\n  type InvalidVersionedHashSizeErrorType,\n  InvalidVersionedHashVersionError,\n  type InvalidVersionedHashVersionErrorType,\n} from '../../errors/blob.js'\nimport {\n  InvalidChainIdError,\n  type InvalidChainIdErrorType,\n} from '../../errors/chain.js'\nimport {\n  FeeCapTooHighError,\n  type FeeCapTooHighErrorType,\n  TipAboveFeeCapError,\n  type TipAboveFeeCapErrorType,\n} from '../../errors/node.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type {\n  TransactionSerializableEIP1559,\n  TransactionSerializableEIP2930,\n  TransactionSerializableEIP4844,\n  TransactionSerializableEIP7702,\n  TransactionSerializableLegacy,\n} from '../../types/transaction.js'\nimport { type IsAddressErrorType, isAddress } from '../address/isAddress.js'\nimport { size } from '../data/size.js'\nimport { slice } from '../data/slice.js'\nimport { hexToNumber } from '../encoding/fromHex.js'\n\nexport type AssertTransactionEIP7702ErrorType =\n  | AssertTransactionEIP1559ErrorType\n  | InvalidAddressErrorType\n  | InvalidChainIdErrorType\n  | ErrorType\n\nexport function assertTransactionEIP7702(\n  transaction: TransactionSerializableEIP7702,\n) {\n  const { authorizationList } = transaction\n  if (authorizationList) {\n    for (const authorization of authorizationList) {\n      const { chainId } = authorization\n      const address = authorization.address\n      if (!isAddress(address)) throw new InvalidAddressError({ address })\n      if (chainId < 0) throw new InvalidChainIdError({ chainId })\n    }\n  }\n  assertTransactionEIP1559(transaction as {} as TransactionSerializableEIP1559)\n}\n\nexport type AssertTransactionEIP4844ErrorType =\n  | AssertTransactionEIP1559ErrorType\n  | EmptyBlobErrorType\n  | InvalidVersionedHashSizeErrorType\n  | InvalidVersionedHashVersionErrorType\n  | ErrorType\n\nexport function assertTransactionEIP4844(\n  transaction: TransactionSerializableEIP4844,\n) {\n  const { blobVersionedHashes } = transaction\n  if (blobVersionedHashes) {\n    if (blobVersionedHashes.length === 0) throw new EmptyBlobError()\n    for (const hash of blobVersionedHashes) {\n      const size_ = size(hash)\n      const version = hexToNumber(slice(hash, 0, 1))\n      if (size_ !== 32)\n        throw new InvalidVersionedHashSizeError({ hash, size: size_ })\n      if (version !== versionedHashVersionKzg)\n        throw new InvalidVersionedHashVersionError({\n          hash,\n          version,\n        })\n    }\n  }\n  assertTransactionEIP1559(transaction as {} as TransactionSerializableEIP1559)\n}\n\nexport type AssertTransactionEIP1559ErrorType =\n  | BaseErrorType\n  | IsAddressErrorType\n  | InvalidAddressErrorType\n  | InvalidChainIdErrorType\n  | FeeCapTooHighErrorType\n  | TipAboveFeeCapErrorType\n  | ErrorType\n\nexport function assertTransactionEIP1559(\n  transaction: TransactionSerializableEIP1559,\n) {\n  const { chainId, maxPriorityFeePerGas, maxFeePerGas, to } = transaction\n  if (chainId <= 0) throw new InvalidChainIdError({ chainId })\n  if (to && !isAddress(to)) throw new InvalidAddressError({ address: to })\n  if (maxFeePerGas && maxFeePerGas > maxUint256)\n    throw new FeeCapTooHighError({ maxFeePerGas })\n  if (\n    maxPriorityFeePerGas &&\n    maxFeePerGas &&\n    maxPriorityFeePerGas > maxFeePerGas\n  )\n    throw new TipAboveFeeCapError({ maxFeePerGas, maxPriorityFeePerGas })\n}\n\nexport type AssertTransactionEIP2930ErrorType =\n  | BaseErrorType\n  | IsAddressErrorType\n  | InvalidAddressErrorType\n  | InvalidChainIdErrorType\n  | FeeCapTooHighErrorType\n  | ErrorType\n\nexport function assertTransactionEIP2930(\n  transaction: TransactionSerializableEIP2930,\n) {\n  const { chainId, maxPriorityFeePerGas, gasPrice, maxFeePerGas, to } =\n    transaction\n  if (chainId <= 0) throw new InvalidChainIdError({ chainId })\n  if (to && !isAddress(to)) throw new InvalidAddressError({ address: to })\n  if (maxPriorityFeePerGas || maxFeePerGas)\n    throw new BaseError(\n      '`maxFeePerGas`/`maxPriorityFeePerGas` is not a valid EIP-2930 Transaction attribute.',\n    )\n  if (gasPrice && gasPrice > maxUint256)\n    throw new FeeCapTooHighError({ maxFeePerGas: gasPrice })\n}\n\nexport type AssertTransactionLegacyErrorType =\n  | BaseErrorType\n  | IsAddressErrorType\n  | InvalidAddressErrorType\n  | InvalidChainIdErrorType\n  | FeeCapTooHighErrorType\n  | ErrorType\n\nexport function assertTransactionLegacy(\n  transaction: TransactionSerializableLegacy,\n) {\n  const { chainId, maxPriorityFeePerGas, gasPrice, maxFeePerGas, to } =\n    transaction\n  if (to && !isAddress(to)) throw new InvalidAddressError({ address: to })\n  if (typeof chainId !== 'undefined' && chainId <= 0)\n    throw new InvalidChainIdError({ chainId })\n  if (maxPriorityFeePerGas || maxFeePerGas)\n    throw new BaseError(\n      '`maxFeePerGas`/`maxPriorityFeePerGas` is not a valid Legacy Transaction attribute.',\n    )\n  if (gasPrice && gasPrice > maxUint256)\n    throw new FeeCapTooHighError({ maxFeePerGas: gasPrice })\n}\n", "import {\n  InvalidAddressError,\n  type InvalidAddressErrorType,\n} from '../../errors/address.js'\nimport {\n  InvalidStorageKeySizeError,\n  type InvalidStorageKeySizeErrorType,\n} from '../../errors/transaction.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type { Hex } from '../../types/misc.js'\nimport type { AccessList } from '../../types/transaction.js'\nimport { type IsAddressErrorType, isAddress } from '../address/isAddress.js'\nimport type { RecursiveArray } from '../encoding/toRlp.js'\n\nexport type SerializeAccessListErrorType =\n  | InvalidStorageKeySizeErrorType\n  | InvalidAddressErrorType\n  | IsAddressErrorType\n  | ErrorType\n\n/*\n * Serialize an  EIP-2930 access list\n * @remarks\n * Use to create a transaction serializer with support for EIP-2930 access lists\n *\n * @param accessList - Array of objects of address and arrays of Storage Keys\n * @throws InvalidAddressError, InvalidStorageKeySizeError\n * @returns Array of hex strings\n */\nexport function serializeAccessList(\n  accessList?: AccessList | undefined,\n): RecursiveArray<Hex> {\n  if (!accessList || accessList.length === 0) return []\n\n  const serializedAccessList = []\n  for (let i = 0; i < accessList.length; i++) {\n    const { address, storageKeys } = accessList[i]\n\n    for (let j = 0; j < storageKeys.length; j++) {\n      if (storageKeys[j].length - 2 !== 64) {\n        throw new InvalidStorageKeySizeError({ storageKey: storageKeys[j] })\n      }\n    }\n\n    if (!isAddress(address, { strict: false })) {\n      throw new InvalidAddressError({ address })\n    }\n\n    serializedAccessList.push([address, storageKeys])\n  }\n  return serializedAccessList\n}\n", "import type { ErrorType } from '../../errors/utils.js'\nimport type {\n  AuthorizationList,\n  SerializedAuthorizationList,\n} from '../../types/authorization.js'\nimport { toHex } from '../encoding/toHex.js'\nimport { toYParitySignatureArray } from '../transaction/serializeTransaction.js'\n\nexport type SerializeAuthorizationListReturnType = SerializedAuthorizationList\n\nexport type SerializeAuthorizationListErrorType = ErrorType\n\n/*\n * Serializes an EIP-7702 authorization list.\n */\nexport function serializeAuthorizationList(\n  authorizationList?: AuthorizationList<number, true> | undefined,\n): SerializeAuthorizationListReturnType {\n  if (!authorizationList || authorizationList.length === 0) return []\n\n  const serializedAuthorizationList = []\n  for (const authorization of authorizationList) {\n    const { chainId, nonce, ...signature } = authorization\n    const contractAddress = authorization.address\n    serializedAuthorizationList.push([\n      chainId ? toHex(chainId) : '0x',\n      contractAddress,\n      nonce ? toHex(nonce) : '0x',\n      ...toYParitySignatureArray({}, signature),\n    ])\n  }\n\n  return serializedAuthorizationList as {} as SerializeAuthorizationListReturnType\n}\n", "import {\n  InvalidLegacyVError,\n  type InvalidLegacyVErrorType,\n} from '../../errors/transaction.js'\nimport type { ErrorType } from '../../errors/utils.js'\nimport type {\n  ByteArray,\n  Hex,\n  Signature,\n  SignatureLegacy,\n} from '../../types/misc.js'\nimport type {\n  TransactionSerializable,\n  TransactionSerializableEIP1559,\n  TransactionSerializableEIP2930,\n  TransactionSerializableEIP4844,\n  TransactionSerializableEIP7702,\n  TransactionSerializableGeneric,\n  TransactionSerializableLegacy,\n  TransactionSerialized,\n  TransactionSerializedEIP1559,\n  TransactionSerializedEIP2930,\n  TransactionSerializedEIP4844,\n  TransactionSerializedEIP7702,\n  TransactionSerializedLegacy,\n  TransactionType,\n} from '../../types/transaction.js'\nimport type { OneOf } from '../../types/utils.js'\nimport {\n  type SerializeAuthorizationListErrorType,\n  serializeAuthorizationList,\n} from '../authorization/serializeAuthorizationList.js'\nimport {\n  type BlobsToCommitmentsErrorType,\n  blobsToCommitments,\n} from '../blob/blobsToCommitments.js'\nimport {\n  blobsToProofs,\n  type blobsToProofsErrorType,\n} from '../blob/blobsToProofs.js'\nimport {\n  type CommitmentsToVersionedHashesErrorType,\n  commitmentsToVersionedHashes,\n} from '../blob/commitmentsToVersionedHashes.js'\nimport {\n  type ToBlobSidecarsErrorType,\n  toBlobSidecars,\n} from '../blob/toBlobSidecars.js'\nimport { type ConcatHexErrorType, concatHex } from '../data/concat.js'\nimport { trim } from '../data/trim.js'\nimport {\n  type NumberToHexErrorType,\n  bytesToHex,\n  numberToHex,\n} from '../encoding/toHex.js'\nimport { type ToRlpErrorType, toRlp } from '../encoding/toRlp.js'\n\nimport {\n  type AssertTransactionEIP1559ErrorType,\n  type AssertTransactionEIP2930ErrorType,\n  type AssertTransactionEIP4844ErrorType,\n  type AssertTransactionEIP7702ErrorType,\n  type AssertTransactionLegacyErrorType,\n  assertTransactionEIP1559,\n  assertTransactionEIP2930,\n  assertTransactionEIP4844,\n  assertTransactionEIP7702,\n  assertTransactionLegacy,\n} from './assertTransaction.js'\nimport {\n  type GetTransactionType,\n  type GetTransactionTypeErrorType,\n  getTransactionType,\n} from './getTransactionType.js'\nimport {\n  type SerializeAccessListErrorType,\n  serializeAccessList,\n} from './serializeAccessList.js'\n\nexport type SerializedTransactionReturnType<\n  transaction extends TransactionSerializable = TransactionSerializable,\n  ///\n  _transactionType extends TransactionType = GetTransactionType<transaction>,\n> = TransactionSerialized<_transactionType>\n\nexport type SerializeTransactionFn<\n  transaction extends TransactionSerializableGeneric = TransactionSerializable,\n  ///\n  _transactionType extends TransactionType = never,\n> = typeof serializeTransaction<\n  OneOf<TransactionSerializable | transaction>,\n  _transactionType\n>\n\nexport type SerializeTransactionErrorType =\n  | GetTransactionTypeErrorType\n  | SerializeTransactionEIP1559ErrorType\n  | SerializeTransactionEIP2930ErrorType\n  | SerializeTransactionEIP4844ErrorType\n  | SerializeTransactionEIP7702ErrorType\n  | SerializeTransactionLegacyErrorType\n  | ErrorType\n\nexport function serializeTransaction<\n  const transaction extends TransactionSerializable,\n  ///\n  _transactionType extends TransactionType = GetTransactionType<transaction>,\n>(\n  transaction: transaction,\n  signature?: Signature | undefined,\n): SerializedTransactionReturnType<transaction, _transactionType> {\n  const type = getTransactionType(transaction) as GetTransactionType\n\n  if (type === 'eip1559')\n    return serializeTransactionEIP1559(\n      transaction as TransactionSerializableEIP1559,\n      signature,\n    ) as SerializedTransactionReturnType<transaction>\n\n  if (type === 'eip2930')\n    return serializeTransactionEIP2930(\n      transaction as TransactionSerializableEIP2930,\n      signature,\n    ) as SerializedTransactionReturnType<transaction>\n\n  if (type === 'eip4844')\n    return serializeTransactionEIP4844(\n      transaction as TransactionSerializableEIP4844,\n      signature,\n    ) as SerializedTransactionReturnType<transaction>\n\n  if (type === 'eip7702')\n    return serializeTransactionEIP7702(\n      transaction as TransactionSerializableEIP7702,\n      signature,\n    ) as SerializedTransactionReturnType<transaction>\n\n  return serializeTransactionLegacy(\n    transaction as TransactionSerializableLegacy,\n    signature as SignatureLegacy,\n  ) as SerializedTransactionReturnType<transaction>\n}\n\ntype SerializeTransactionEIP7702ErrorType =\n  | AssertTransactionEIP7702ErrorType\n  | SerializeAuthorizationListErrorType\n  | ConcatHexErrorType\n  | InvalidLegacyVErrorType\n  | NumberToHexErrorType\n  | ToRlpErrorType\n  | SerializeAccessListErrorType\n  | ErrorType\n\nfunction serializeTransactionEIP7702(\n  transaction: TransactionSerializableEIP7702,\n  signature?: Signature | undefined,\n): TransactionSerializedEIP7702 {\n  const {\n    authorizationList,\n    chainId,\n    gas,\n    nonce,\n    to,\n    value,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    accessList,\n    data,\n  } = transaction\n\n  assertTransactionEIP7702(transaction)\n\n  const serializedAccessList = serializeAccessList(accessList)\n  const serializedAuthorizationList =\n    serializeAuthorizationList(authorizationList)\n\n  return concatHex([\n    '0x04',\n    toRlp([\n      numberToHex(chainId),\n      nonce ? numberToHex(nonce) : '0x',\n      maxPriorityFeePerGas ? numberToHex(maxPriorityFeePerGas) : '0x',\n      maxFeePerGas ? numberToHex(maxFeePerGas) : '0x',\n      gas ? numberToHex(gas) : '0x',\n      to ?? '0x',\n      value ? numberToHex(value) : '0x',\n      data ?? '0x',\n      serializedAccessList,\n      serializedAuthorizationList,\n      ...toYParitySignatureArray(transaction, signature),\n    ]),\n  ]) as TransactionSerializedEIP7702\n}\n\ntype SerializeTransactionEIP4844ErrorType =\n  | AssertTransactionEIP4844ErrorType\n  | BlobsToCommitmentsErrorType\n  | CommitmentsToVersionedHashesErrorType\n  | blobsToProofsErrorType\n  | ToBlobSidecarsErrorType\n  | ConcatHexErrorType\n  | InvalidLegacyVErrorType\n  | NumberToHexErrorType\n  | ToRlpErrorType\n  | SerializeAccessListErrorType\n  | ErrorType\n\nfunction serializeTransactionEIP4844(\n  transaction: TransactionSerializableEIP4844,\n  signature?: Signature | undefined,\n): TransactionSerializedEIP4844 {\n  const {\n    chainId,\n    gas,\n    nonce,\n    to,\n    value,\n    maxFeePerBlobGas,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    accessList,\n    data,\n  } = transaction\n\n  assertTransactionEIP4844(transaction)\n\n  let blobVersionedHashes = transaction.blobVersionedHashes\n  let sidecars = transaction.sidecars\n  // If `blobs` are passed, we will need to compute the KZG commitments & proofs.\n  if (\n    transaction.blobs &&\n    (typeof blobVersionedHashes === 'undefined' ||\n      typeof sidecars === 'undefined')\n  ) {\n    const blobs = (\n      typeof transaction.blobs[0] === 'string'\n        ? transaction.blobs\n        : (transaction.blobs as ByteArray[]).map((x) => bytesToHex(x))\n    ) as Hex[]\n    const kzg = transaction.kzg!\n    const commitments = blobsToCommitments({\n      blobs,\n      kzg,\n    })\n\n    if (typeof blobVersionedHashes === 'undefined')\n      blobVersionedHashes = commitmentsToVersionedHashes({\n        commitments,\n      })\n    if (typeof sidecars === 'undefined') {\n      const proofs = blobsToProofs({ blobs, commitments, kzg })\n      sidecars = toBlobSidecars({ blobs, commitments, proofs })\n    }\n  }\n\n  const serializedAccessList = serializeAccessList(accessList)\n\n  const serializedTransaction = [\n    numberToHex(chainId),\n    nonce ? numberToHex(nonce) : '0x',\n    maxPriorityFeePerGas ? numberToHex(maxPriorityFeePerGas) : '0x',\n    maxFeePerGas ? numberToHex(maxFeePerGas) : '0x',\n    gas ? numberToHex(gas) : '0x',\n    to ?? '0x',\n    value ? numberToHex(value) : '0x',\n    data ?? '0x',\n    serializedAccessList,\n    maxFeePerBlobGas ? numberToHex(maxFeePerBlobGas) : '0x',\n    blobVersionedHashes ?? [],\n    ...toYParitySignatureArray(transaction, signature),\n  ] as const\n\n  const blobs: Hex[] = []\n  const commitments: Hex[] = []\n  const proofs: Hex[] = []\n  if (sidecars)\n    for (let i = 0; i < sidecars.length; i++) {\n      const { blob, commitment, proof } = sidecars[i]\n      blobs.push(blob)\n      commitments.push(commitment)\n      proofs.push(proof)\n    }\n\n  return concatHex([\n    '0x03',\n    sidecars\n      ? // If sidecars are enabled, envelope turns into a \"wrapper\":\n        toRlp([serializedTransaction, blobs, commitments, proofs])\n      : // If sidecars are disabled, standard envelope is used:\n        toRlp(serializedTransaction),\n  ]) as TransactionSerializedEIP4844\n}\n\ntype SerializeTransactionEIP1559ErrorType =\n  | AssertTransactionEIP1559ErrorType\n  | ConcatHexErrorType\n  | InvalidLegacyVErrorType\n  | NumberToHexErrorType\n  | ToRlpErrorType\n  | SerializeAccessListErrorType\n  | ErrorType\n\nfunction serializeTransactionEIP1559(\n  transaction: TransactionSerializableEIP1559,\n  signature?: Signature | undefined,\n): TransactionSerializedEIP1559 {\n  const {\n    chainId,\n    gas,\n    nonce,\n    to,\n    value,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    accessList,\n    data,\n  } = transaction\n\n  assertTransactionEIP1559(transaction)\n\n  const serializedAccessList = serializeAccessList(accessList)\n\n  const serializedTransaction = [\n    numberToHex(chainId),\n    nonce ? numberToHex(nonce) : '0x',\n    maxPriorityFeePerGas ? numberToHex(maxPriorityFeePerGas) : '0x',\n    maxFeePerGas ? numberToHex(maxFeePerGas) : '0x',\n    gas ? numberToHex(gas) : '0x',\n    to ?? '0x',\n    value ? numberToHex(value) : '0x',\n    data ?? '0x',\n    serializedAccessList,\n    ...toYParitySignatureArray(transaction, signature),\n  ]\n\n  return concatHex([\n    '0x02',\n    toRlp(serializedTransaction),\n  ]) as TransactionSerializedEIP1559\n}\n\ntype SerializeTransactionEIP2930ErrorType =\n  | AssertTransactionEIP2930ErrorType\n  | ConcatHexErrorType\n  | InvalidLegacyVErrorType\n  | NumberToHexErrorType\n  | ToRlpErrorType\n  | SerializeAccessListErrorType\n  | ErrorType\n\nfunction serializeTransactionEIP2930(\n  transaction: TransactionSerializableEIP2930,\n  signature?: Signature | undefined,\n): TransactionSerializedEIP2930 {\n  const { chainId, gas, data, nonce, to, value, accessList, gasPrice } =\n    transaction\n\n  assertTransactionEIP2930(transaction)\n\n  const serializedAccessList = serializeAccessList(accessList)\n\n  const serializedTransaction = [\n    numberToHex(chainId),\n    nonce ? numberToHex(nonce) : '0x',\n    gasPrice ? numberToHex(gasPrice) : '0x',\n    gas ? numberToHex(gas) : '0x',\n    to ?? '0x',\n    value ? numberToHex(value) : '0x',\n    data ?? '0x',\n    serializedAccessList,\n    ...toYParitySignatureArray(transaction, signature),\n  ]\n\n  return concatHex([\n    '0x01',\n    toRlp(serializedTransaction),\n  ]) as TransactionSerializedEIP2930\n}\n\ntype SerializeTransactionLegacyErrorType =\n  | AssertTransactionLegacyErrorType\n  | InvalidLegacyVErrorType\n  | NumberToHexErrorType\n  | ToRlpErrorType\n  | ErrorType\n\nfunction serializeTransactionLegacy(\n  transaction: TransactionSerializableLegacy,\n  signature?: SignatureLegacy | undefined,\n): TransactionSerializedLegacy {\n  const { chainId = 0, gas, data, nonce, to, value, gasPrice } = transaction\n\n  assertTransactionLegacy(transaction)\n\n  let serializedTransaction = [\n    nonce ? numberToHex(nonce) : '0x',\n    gasPrice ? numberToHex(gasPrice) : '0x',\n    gas ? numberToHex(gas) : '0x',\n    to ?? '0x',\n    value ? numberToHex(value) : '0x',\n    data ?? '0x',\n  ]\n\n  if (signature) {\n    const v = (() => {\n      // EIP-155 (inferred chainId)\n      if (signature.v >= 35n) {\n        const inferredChainId = (signature.v - 35n) / 2n\n        if (inferredChainId > 0) return signature.v\n        return 27n + (signature.v === 35n ? 0n : 1n)\n      }\n\n      // EIP-155 (explicit chainId)\n      if (chainId > 0)\n        return BigInt(chainId * 2) + BigInt(35n + signature.v - 27n)\n\n      // Pre-EIP-155 (no chainId)\n      const v = 27n + (signature.v === 27n ? 0n : 1n)\n      if (signature.v !== v) throw new InvalidLegacyVError({ v: signature.v })\n      return v\n    })()\n\n    const r = trim(signature.r)\n    const s = trim(signature.s)\n\n    serializedTransaction = [\n      ...serializedTransaction,\n      numberToHex(v),\n      r === '0x00' ? '0x' : r,\n      s === '0x00' ? '0x' : s,\n    ]\n  } else if (chainId > 0) {\n    serializedTransaction = [\n      ...serializedTransaction,\n      numberToHex(chainId),\n      '0x',\n      '0x',\n    ]\n  }\n\n  return toRlp(serializedTransaction) as TransactionSerializedLegacy\n}\n\nexport function toYParitySignatureArray(\n  transaction: TransactionSerializableGeneric,\n  signature_?: Signature | undefined,\n) {\n  const signature = signature_ ?? transaction\n  const { v, yParity } = signature\n\n  if (typeof signature.r === 'undefined') return []\n  if (typeof signature.s === 'undefined') return []\n  if (typeof v === 'undefined' && typeof yParity === 'undefined') return []\n\n  const r = trim(signature.r)\n  const s = trim(signature.s)\n\n  const yParity_ = (() => {\n    if (typeof yParity === 'number') return yParity ? numberToHex(1) : '0x'\n    if (v === 0n) return '0x'\n    if (v === 1n) return numberToHex(1)\n\n    return v === 27n ? '0x' : numberToHex(1)\n  })()\n\n  return [yParity_, r === '0x00' ? '0x' : r, s === '0x00' ? '0x' : s]\n}\n", "import { BaseError } from './base.js'\n\nexport type AccountNotFoundErrorType = AccountNotFoundError & {\n  name: 'AccountNotFoundError'\n}\nexport class AccountNotFoundError extends BaseError {\n  constructor({ docsPath }: { docsPath?: string | undefined } = {}) {\n    super(\n      [\n        'Could not find an Account to execute with this Action.',\n        'Please provide an Account with the `account` argument on the Action, or by supplying an `account` to the Client.',\n      ].join('\\n'),\n      {\n        docsPath,\n        docsSlug: 'account',\n        name: 'AccountNotFoundError',\n      },\n    )\n  }\n}\n\nexport type AccountTypeNotSupportedErrorType = AccountTypeNotSupportedError & {\n  name: 'AccountTypeNotSupportedError'\n}\nexport class AccountTypeNotSupportedError extends BaseError {\n  constructor({\n    docsPath,\n    metaMessages,\n    type,\n  }: {\n    docsPath?: string | undefined\n    metaMessages?: string[] | undefined\n    type: string\n  }) {\n    super(`Account type \"${type}\" is not supported.`, {\n      docsPath,\n      metaMessages,\n      name: 'AccountTypeNotSupportedError',\n    })\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BM,SAAU,MACd,OACA,KAA0B,OAAK;AAE/B,QAAM,YAAY,aAAa,KAAK;AACpC,QAAM,SAAS,aAAa,IAAI,WAAW,UAAU,MAAM,CAAC;AAC5D,YAAU,OAAO,MAAM;AAEvB,MAAI,OAAO;AAAO,WAAO,WAAW,OAAO,KAAK;AAChD,SAAO,OAAO;AAChB;AAIM,SAAU,WACd,OACA,KAA0B,SAAO;AAEjC,SAAO,MAAM,OAAO,EAAE;AACxB;AAIM,SAAU,SACd,KACA,KAA0B,OAAK;AAE/B,SAAO,MAAM,KAAK,EAAE;AACtB;AAEA,SAAS,aACP,OAAsD;AAEtD,MAAI,MAAM,QAAQ,KAAK;AACrB,WAAO,iBAAiB,MAAM,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC,CAAC;AAC3D,SAAO,kBAAkB,KAAY;AACvC;AAEA,SAAS,iBAAiB,MAAiB;AACzC,QAAM,aAAa,KAAK,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,QAAQ,CAAC;AAE5D,QAAM,mBAAmB,gBAAgB,UAAU;AACnD,QAAM,UAAU,MAAK;AACnB,QAAI,cAAc;AAAI,aAAO,IAAI;AACjC,WAAO,IAAI,mBAAmB;EAChC,GAAE;AAEF,SAAO;IACL;IACA,OAAO,QAAc;AACnB,UAAI,cAAc,IAAI;AACpB,eAAO,SAAS,MAAO,UAAU;MACnC,OAAO;AACL,eAAO,SAAS,MAAO,KAAK,gBAAgB;AAC5C,YAAI,qBAAqB;AAAG,iBAAO,UAAU,UAAU;iBAC9C,qBAAqB;AAAG,iBAAO,WAAW,UAAU;iBACpD,qBAAqB;AAAG,iBAAO,WAAW,UAAU;;AACxD,iBAAO,WAAW,UAAU;MACnC;AACA,iBAAW,EAAE,OAAM,KAAM,MAAM;AAC7B,eAAO,MAAM;MACf;IACF;;AAEJ;AAEA,SAAS,kBAAkB,YAA2B;AACpD,QAAM,QACJ,OAAO,eAAe,WAAW,WAAW,UAAU,IAAI;AAE5D,QAAM,oBAAoB,gBAAgB,MAAM,MAAM;AACtD,QAAM,UAAU,MAAK;AACnB,QAAI,MAAM,WAAW,KAAK,MAAM,CAAC,IAAI;AAAM,aAAO;AAClD,QAAI,MAAM,UAAU;AAAI,aAAO,IAAI,MAAM;AACzC,WAAO,IAAI,oBAAoB,MAAM;EACvC,GAAE;AAEF,SAAO;IACL;IACA,OAAO,QAAc;AACnB,UAAI,MAAM,WAAW,KAAK,MAAM,CAAC,IAAI,KAAM;AACzC,eAAO,UAAU,KAAK;MACxB,WAAW,MAAM,UAAU,IAAI;AAC7B,eAAO,SAAS,MAAO,MAAM,MAAM;AACnC,eAAO,UAAU,KAAK;MACxB,OAAO;AACL,eAAO,SAAS,MAAO,KAAK,iBAAiB;AAC7C,YAAI,sBAAsB;AAAG,iBAAO,UAAU,MAAM,MAAM;iBACjD,sBAAsB;AAAG,iBAAO,WAAW,MAAM,MAAM;iBACvD,sBAAsB;AAAG,iBAAO,WAAW,MAAM,MAAM;;AAC3D,iBAAO,WAAW,MAAM,MAAM;AACnC,eAAO,UAAU,KAAK;MACxB;IACF;;AAEJ;AAEA,SAAS,gBAAgB,QAAc;AACrC,MAAI,SAAS,KAAK;AAAG,WAAO;AAC5B,MAAI,SAAS,KAAK;AAAI,WAAO;AAC7B,MAAI,SAAS,KAAK;AAAI,WAAO;AAC7B,MAAI,SAAS,KAAK;AAAI,WAAO;AAC7B,QAAM,IAAI,UAAU,sBAAsB;AAC5C;;;ACnHM,SAAU,mBAAmB,EACjC,OACA,eAAc,GACe;AAC7B,MAAI,CAAC;AAAO,UAAM,IAAI,mBAAkB;AACxC,MAAI,mBAAmB,MAAM;AAC3B,UAAM,IAAI,mBAAmB,EAAE,OAAO,eAAc,CAAE;AAC1D;;;ACWO,IAAM,kBAAkB;EAC7B,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;EACP,OAAO;;AAKH,SAAU,kBAAkB,aAAyC;AACzE,QAAM,eAAe;IACnB,GAAG;IACH,WAAW,YAAY,YAAY,YAAY,YAAY;IAC3D,aAAa,YAAY,cACrB,OAAO,YAAY,WAAW,IAC9B;IACJ,SAAS,YAAY,UAAU,YAAY,YAAY,OAAO,IAAI;IAClE,KAAK,YAAY,MAAM,OAAO,YAAY,GAAG,IAAI;IACjD,UAAU,YAAY,WAAW,OAAO,YAAY,QAAQ,IAAI;IAChE,kBAAkB,YAAY,mBAC1B,OAAO,YAAY,gBAAgB,IACnC;IACJ,cAAc,YAAY,eACtB,OAAO,YAAY,YAAY,IAC/B;IACJ,sBAAsB,YAAY,uBAC9B,OAAO,YAAY,oBAAoB,IACvC;IACJ,OAAO,YAAY,QAAQ,YAAY,YAAY,KAAK,IAAI;IAC5D,IAAI,YAAY,KAAK,YAAY,KAAK;IACtC,kBAAkB,YAAY,mBAC1B,OAAO,YAAY,gBAAgB,IACnC;IACJ,MAAM,YAAY,OACb,gBAAwB,YAAY,IAAI,IACzC;IACJ,SAAS,YAAY,OAAO,YAAY,OAAO;IAC/C,OAAO,YAAY,QAAQ,OAAO,YAAY,KAAK,IAAI;IACvD,GAAG,YAAY,IAAI,OAAO,YAAY,CAAC,IAAI;;AAG7C,MAAI,YAAY;AACd,iBAAa,oBAAoB,wBAC/B,YAAY,iBAAiB;AAGjC,eAAa,WAAW,MAAK;AAE3B,QAAI,YAAY;AAAS,aAAO,OAAO,YAAY,OAAO;AAG1D,QAAI,OAAO,aAAa,MAAM,UAAU;AACtC,UAAI,aAAa,MAAM,MAAM,aAAa,MAAM;AAAK,eAAO;AAC5D,UAAI,aAAa,MAAM,MAAM,aAAa,MAAM;AAAK,eAAO;AAC5D,UAAI,aAAa,KAAK;AAAK,eAAO,aAAa,IAAI,OAAO,KAAK,IAAI;IACrE;AAEA,WAAO;EACT,GAAE;AAEF,MAAI,aAAa,SAAS,UAAU;AAClC,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;EACtB;AACA,MAAI,aAAa,SAAS,WAAW;AACnC,WAAO,aAAa;AACpB,WAAO,aAAa;AACpB,WAAO,aAAa;EACtB;AACA,MAAI,aAAa,SAAS,WAAW;AACnC,WAAO,aAAa;EACtB;AACA,SAAO;AACT;AAIO,IAAM,oBAAkC,gBAC7C,eACA,iBAAiB;AAKnB,SAAS,wBACP,mBAAuC;AAEvC,SAAO,kBAAkB,IAAI,CAAC,mBAAmB;IAC/C,SAAU,cAAsB;IAChC,SAAS,OAAO,cAAc,OAAO;IACrC,OAAO,OAAO,cAAc,KAAK;IACjC,GAAG,cAAc;IACjB,GAAG,cAAc;IACjB,SAAS,OAAO,cAAc,OAAO;IACrC;AACJ;;;AC9FM,SAAU,YAAY,OAA6B;AACvD,QAAM,gBAAgB,MAAM,gBAAgB,CAAA,GAAI,IAAI,CAAC,gBAAe;AAClE,QAAI,OAAO,gBAAgB;AAAU,aAAO;AAC5C,WAAO,kBAAkB,WAAW;EACtC,CAAC;AACD,SAAO;IACL,GAAG;IACH,eAAe,MAAM,gBAAgB,OAAO,MAAM,aAAa,IAAI;IACnE,aAAa,MAAM,cAAc,OAAO,MAAM,WAAW,IAAI;IAC7D,YAAY,MAAM,aAAa,OAAO,MAAM,UAAU,IAAI;IAC1D,eAAe,MAAM,gBACjB,OAAO,MAAM,aAAa,IAC1B;IACJ,UAAU,MAAM,WAAW,OAAO,MAAM,QAAQ,IAAI;IACpD,SAAS,MAAM,UAAU,OAAO,MAAM,OAAO,IAAI;IACjD,MAAM,MAAM,OAAO,MAAM,OAAO;IAChC,WAAW,MAAM,YAAY,MAAM,YAAY;IAC/C,OAAO,MAAM,QAAQ,MAAM,QAAQ;IACnC,QAAQ,MAAM,SAAS,OAAO,MAAM,MAAM,IAAI;IAC9C,MAAM,MAAM,OAAO,OAAO,MAAM,IAAI,IAAI;IACxC,WAAW,MAAM,YAAY,OAAO,MAAM,SAAS,IAAI;IACvD;IACA,iBAAiB,MAAM,kBACnB,OAAO,MAAM,eAAe,IAC5B;;AAER;AAIO,IAAM,cAA4B,gBAAgB,SAAS,WAAW;;;AC7BvE,SAAU,mBAMd,YAAmD;AAEnD,QAAM,EAAE,IAAG,IAAK;AAEhB,QAAM,KACJ,WAAW,OAAO,OAAO,WAAW,MAAM,CAAC,MAAM,WAAW,QAAQ;AACtE,QAAM,QACJ,OAAO,WAAW,MAAM,CAAC,MAAM,WAC3B,WAAW,MAAM,IAAI,CAAC,MAAM,WAAW,CAAQ,CAAC,IAChD,WAAW;AAGjB,QAAM,cAA2B,CAAA;AACjC,aAAW,QAAQ;AACjB,gBAAY,KAAK,WAAW,KAAK,IAAI,oBAAoB,IAAI,CAAC,CAAC;AAEjE,SAAQ,OAAO,UACX,cACA,YAAY,IAAI,CAAC,MACf,WAAW,CAAC,CAAC;AAErB;;;ACbM,SAAU,cAOd,YAA2D;AAE3D,QAAM,EAAE,IAAG,IAAK;AAEhB,QAAM,KACJ,WAAW,OAAO,OAAO,WAAW,MAAM,CAAC,MAAM,WAAW,QAAQ;AAEtE,QAAM,QACJ,OAAO,WAAW,MAAM,CAAC,MAAM,WAC3B,WAAW,MAAM,IAAI,CAAC,MAAM,WAAW,CAAQ,CAAC,IAChD,WAAW;AAEjB,QAAM,cACJ,OAAO,WAAW,YAAY,CAAC,MAAM,WACjC,WAAW,YAAY,IAAI,CAAC,MAAM,WAAW,CAAQ,CAAC,IACtD,WAAW;AAGjB,QAAM,SAAsB,CAAA;AAC5B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,aAAa,YAAY,CAAC;AAChC,WAAO,KAAK,WAAW,KAAK,IAAI,oBAAoB,MAAM,UAAU,CAAC,CAAC;EACxE;AAEA,SAAQ,OAAO,UACX,SACA,OAAO,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;AACrC;;;ACzEO,IAAMA,UAAyB;;;ACChC,SAAUC,QACd,OACA,KAAoB;AAEpB,QAAM,KAAK,OAAO;AAClB,QAAM,QAAQA,QACZ,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE,IAAI,QAAQ,KAAK,IAAI,KAAK;AAE1D,MAAI,OAAO;AAAS,WAAO;AAC3B,SAAO,MAAM,KAAK;AACpB;;;ACeM,SAAU,0BAMd,YAA+D;AAE/D,QAAM,EAAE,YAAY,UAAU,EAAC,IAAK;AACpC,QAAM,KAAK,WAAW,OAAO,OAAO,eAAe,WAAW,QAAQ;AAEtE,QAAM,gBAAgBC,QAAO,YAAY,OAAO;AAChD,gBAAc,IAAI,CAAC,OAAO,GAAG,CAAC;AAC9B,SACE,OAAO,UAAU,gBAAgB,WAAW,aAAa;AAE7D;;;ACbM,SAAU,6BAMd,YAAmE;AAEnE,QAAM,EAAE,aAAa,QAAO,IAAK;AAEjC,QAAM,KACJ,WAAW,OAAO,OAAO,YAAY,CAAC,MAAM,WAAW,QAAQ;AAEjE,QAAM,SAA+B,CAAA;AACrC,aAAW,cAAc,aAAa;AACpC,WAAO,KACL,0BAA0B;MACxB;MACA;MACA;KACD,CAAQ;EAEb;AACA,SAAO;AACT;;;ACrEA,IAAM,sBAAsB;AAGrB,IAAM,uBAAuB;AAG7B,IAAM,uBAAuB;AAG7B,IAAM,eAAe,uBAAuB;AAG5C,IAAM,yBACX,eAAe;AAEf;AAEA,IAAI,uBAAuB;;;AClBtB,IAAM,0BAA0B;;;ACMjC,IAAO,wBAAP,cAAqC,UAAS;EAClD,YAAY,EAAE,SAAS,MAAAC,MAAI,GAAqC;AAC9D,UAAM,2BAA2B;MAC/B,cAAc,CAAC,QAAQ,OAAO,UAAU,UAAUA,KAAI,QAAQ;MAC9D,MAAM;KACP;EACH;;AAMI,IAAO,iBAAP,cAA8B,UAAS;EAC3C,cAAA;AACE,UAAM,gCAAgC,EAAE,MAAM,iBAAgB,CAAE;EAClE;;AAOI,IAAO,gCAAP,cAA6C,UAAS;EAC1D,YAAY,EACV,MACA,MAAAA,MAAI,GAIL;AACC,UAAM,mBAAmB,IAAI,sBAAsB;MACjD,cAAc,CAAC,gBAAgB,aAAaA,KAAI,EAAE;MAClD,MAAM;KACP;EACH;;AAOI,IAAO,mCAAP,cAAgD,UAAS;EAC7D,YAAY,EACV,MACA,QAAO,GAIR;AACC,UAAM,mBAAmB,IAAI,yBAAyB;MACpD,cAAc;QACZ,aAAa,uBAAuB;QACpC,aAAa,OAAO;;MAEtB,MAAM;KACP;EACH;;;;ACVI,SAAU,QAKd,YAAuC;AACvC,QAAM,KACJ,WAAW,OAAO,OAAO,WAAW,SAAS,WAAW,QAAQ;AAClE,QAAM,OACJ,OAAO,WAAW,SAAS,WACvB,WAAW,WAAW,IAAI,IAC1B,WAAW;AAGjB,QAAM,QAAQ,KAAK,IAAI;AACvB,MAAI,CAAC;AAAO,UAAM,IAAI,eAAc;AACpC,MAAI,QAAQ;AACV,UAAM,IAAI,sBAAsB;MAC9B,SAAS;MACT,MAAM;KACP;AAEH,QAAM,QAAQ,CAAA;AAEd,MAAI,SAAS;AACb,MAAI,WAAW;AACf,SAAO,QAAQ;AACb,UAAM,OAAO,aAAa,IAAI,WAAW,YAAY,CAAC;AAEtD,QAAIC,QAAO;AACX,WAAOA,QAAO,sBAAsB;AAClC,YAAM,QAAQ,KAAK,MAAM,UAAU,YAAY,uBAAuB,EAAE;AAGxE,WAAK,SAAS,CAAI;AAGlB,WAAK,UAAU,KAAK;AAIpB,UAAI,MAAM,SAAS,IAAI;AACrB,aAAK,SAAS,GAAI;AAClB,iBAAS;AACT;MACF;AAEA,MAAAA;AACA,kBAAY;IACd;AAEA,UAAM,KAAK,IAAI;EACjB;AAEA,SACE,OAAO,UACH,MAAM,IAAI,CAAC,MAAM,EAAE,KAAK,IACxB,MAAM,IAAI,CAAC,MAAM,WAAW,EAAE,KAAK,CAAC;AAE5C;;;AChCM,SAAU,eAYd,YAAqD;AAErD,QAAM,EAAE,MAAM,KAAK,GAAE,IAAK;AAC1B,QAAM,QAAQ,WAAW,SAAS,QAAQ,EAAE,MAAa,GAAE,CAAE;AAC7D,QAAM,cACJ,WAAW,eAAe,mBAAmB,EAAE,OAAO,KAAW,GAAE,CAAE;AACvE,QAAM,SACJ,WAAW,UAAU,cAAc,EAAE,OAAO,aAAa,KAAW,GAAE,CAAE;AAE1E,QAAM,WAAyB,CAAA;AAC/B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAChC,aAAS,KAAK;MACZ,MAAM,MAAM,CAAC;MACb,YAAY,YAAY,CAAC;MACzB,OAAO,OAAO,CAAC;KAChB;AAEH,SAAO;AACT;;;ACrEM,SAAU,mBAId,aAAwB;AACxB,MAAI,YAAY;AACd,WAAO,YAAY;AAErB,MAAI,OAAO,YAAY,sBAAsB;AAC3C,WAAO;AAET,MACE,OAAO,YAAY,UAAU,eAC7B,OAAO,YAAY,wBAAwB,eAC3C,OAAO,YAAY,qBAAqB,eACxC,OAAO,YAAY,aAAa;AAEhC,WAAO;AAET,MACE,OAAO,YAAY,iBAAiB,eACpC,OAAO,YAAY,yBAAyB,aAC5C;AACA,WAAO;EACT;AAEA,MAAI,OAAO,YAAY,aAAa,aAAa;AAC/C,QAAI,OAAO,YAAY,eAAe;AAAa,aAAO;AAC1D,WAAO;EACT;AAEA,QAAM,IAAI,oCAAoC,EAAE,YAAW,CAAE;AAC/D;;;ACnEM,SAAU,UACd,KACA,EACE,MACA,UAAS,IACyD,CAAA,GAAE;AAEtE,SAAO;IACL,GAAG;IACH,WAAW,IAAI,YAAY,IAAI,YAAY;IAC3C,aAAa,IAAI,cAAc,OAAO,IAAI,WAAW,IAAI;IACzD,UAAU,IAAI,WAAW,OAAO,IAAI,QAAQ,IAAI;IAChD,iBAAiB,IAAI,kBAAkB,IAAI,kBAAkB;IAC7D,kBAAkB,IAAI,mBAClB,OAAO,IAAI,gBAAgB,IAC3B;IACJ,GAAI,YAAY,EAAE,MAAM,UAAS,IAAK,CAAA;;AAE1C;;;ACHO,IAAM,kBAAkB;EAC7B,OAAO;EACP,OAAO;;AAKH,SAAU,yBACd,oBAAuD;AAEvD,QAAM,UAAU;IACd,GAAG;IACH,aAAa,mBAAmB,cAC5B,OAAO,mBAAmB,WAAW,IACrC;IACJ,iBAAiB,mBAAmB,kBAChC,mBAAmB,kBACnB;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,mBAAmB,mBAAmB,oBAClC,OAAO,mBAAmB,iBAAiB,IAC3C;IACJ,SAAS,mBAAmB,UACxB,OAAO,mBAAmB,OAAO,IACjC;IACJ,MAAM,mBAAmB,OACrB,mBAAmB,KAAK,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAC,IACnD;IACJ,IAAI,mBAAmB,KAAK,mBAAmB,KAAK;IACpD,kBAAkB,mBAAmB,mBACjC,YAAY,mBAAmB,gBAAgB,IAC/C;IACJ,QAAQ,mBAAmB,SACvB,gBAAgB,mBAAmB,MAAM,IACzC;IACJ,MAAM,mBAAmB,OACrB,gBACE,mBAAmB,IAAoC,KACpD,mBAAmB,OACxB;;AAGN,MAAI,mBAAmB;AACrB,YAAQ,eAAe,OAAO,mBAAmB,YAAY;AAC/D,MAAI,mBAAmB;AACrB,YAAQ,cAAc,OAAO,mBAAmB,WAAW;AAE7D,SAAO;AACT;AAMO,IAAM,2BAAyC,gBACpD,sBACA,wBAAwB;;;AC7EpB,SAAU,YAGd,OAAY;AACZ,SAAO;IACL,YAAY;IACZ,MAAM;IACN,aAAa;IACb,GAAG;;AAEP;;;ACKM,SAAU,aAGd,EACA,QACA,GAAE,GACsC;AAIxC,SAAO,OAAO,KAAK,CAAC,UAAU,MAAM,OAAO,EAAE;AAI/C;;;ACYM,SAAU,yBACd,aAA2C;AAE3C,QAAM,EAAE,kBAAiB,IAAK;AAC9B,MAAI,mBAAmB;AACrB,eAAW,iBAAiB,mBAAmB;AAC7C,YAAM,EAAE,QAAO,IAAK;AACpB,YAAM,UAAU,cAAc;AAC9B,UAAI,CAAC,UAAU,OAAO;AAAG,cAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;AAClE,UAAI,UAAU;AAAG,cAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;IAC5D;EACF;AACA,2BAAyB,WAAmD;AAC9E;AASM,SAAU,yBACd,aAA2C;AAE3C,QAAM,EAAE,oBAAmB,IAAK;AAChC,MAAI,qBAAqB;AACvB,QAAI,oBAAoB,WAAW;AAAG,YAAM,IAAI,eAAc;AAC9D,eAAW,QAAQ,qBAAqB;AACtC,YAAM,QAAQ,KAAK,IAAI;AACvB,YAAM,UAAU,YAAY,MAAM,MAAM,GAAG,CAAC,CAAC;AAC7C,UAAI,UAAU;AACZ,cAAM,IAAI,8BAA8B,EAAE,MAAM,MAAM,MAAK,CAAE;AAC/D,UAAI,YAAY;AACd,cAAM,IAAI,iCAAiC;UACzC;UACA;SACD;IACL;EACF;AACA,2BAAyB,WAAmD;AAC9E;AAWM,SAAU,yBACd,aAA2C;AAE3C,QAAM,EAAE,SAAS,sBAAsB,cAAc,GAAE,IAAK;AAC5D,MAAI,WAAW;AAAG,UAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;AAC3D,MAAI,MAAM,CAAC,UAAU,EAAE;AAAG,UAAM,IAAI,oBAAoB,EAAE,SAAS,GAAE,CAAE;AACvE,MAAI,gBAAgB,eAAe;AACjC,UAAM,IAAI,mBAAmB,EAAE,aAAY,CAAE;AAC/C,MACE,wBACA,gBACA,uBAAuB;AAEvB,UAAM,IAAI,oBAAoB,EAAE,cAAc,qBAAoB,CAAE;AACxE;AAUM,SAAU,yBACd,aAA2C;AAE3C,QAAM,EAAE,SAAS,sBAAsB,UAAU,cAAc,GAAE,IAC/D;AACF,MAAI,WAAW;AAAG,UAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;AAC3D,MAAI,MAAM,CAAC,UAAU,EAAE;AAAG,UAAM,IAAI,oBAAoB,EAAE,SAAS,GAAE,CAAE;AACvE,MAAI,wBAAwB;AAC1B,UAAM,IAAI,UACR,sFAAsF;AAE1F,MAAI,YAAY,WAAW;AACzB,UAAM,IAAI,mBAAmB,EAAE,cAAc,SAAQ,CAAE;AAC3D;AAUM,SAAU,wBACd,aAA0C;AAE1C,QAAM,EAAE,SAAS,sBAAsB,UAAU,cAAc,GAAE,IAC/D;AACF,MAAI,MAAM,CAAC,UAAU,EAAE;AAAG,UAAM,IAAI,oBAAoB,EAAE,SAAS,GAAE,CAAE;AACvE,MAAI,OAAO,YAAY,eAAe,WAAW;AAC/C,UAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;AAC3C,MAAI,wBAAwB;AAC1B,UAAM,IAAI,UACR,oFAAoF;AAExF,MAAI,YAAY,WAAW;AACzB,UAAM,IAAI,mBAAmB,EAAE,cAAc,SAAQ,CAAE;AAC3D;;;AChIM,SAAU,oBACd,YAAmC;AAEnC,MAAI,CAAC,cAAc,WAAW,WAAW;AAAG,WAAO,CAAA;AAEnD,QAAM,uBAAuB,CAAA;AAC7B,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAM,EAAE,SAAS,YAAW,IAAK,WAAW,CAAC;AAE7C,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAI,YAAY,CAAC,EAAE,SAAS,MAAM,IAAI;AACpC,cAAM,IAAI,2BAA2B,EAAE,YAAY,YAAY,CAAC,EAAC,CAAE;MACrE;IACF;AAEA,QAAI,CAAC,UAAU,SAAS,EAAE,QAAQ,MAAK,CAAE,GAAG;AAC1C,YAAM,IAAI,oBAAoB,EAAE,QAAO,CAAE;IAC3C;AAEA,yBAAqB,KAAK,CAAC,SAAS,WAAW,CAAC;EAClD;AACA,SAAO;AACT;;;ACpCM,SAAU,2BACd,mBAA+D;AAE/D,MAAI,CAAC,qBAAqB,kBAAkB,WAAW;AAAG,WAAO,CAAA;AAEjE,QAAM,8BAA8B,CAAA;AACpC,aAAW,iBAAiB,mBAAmB;AAC7C,UAAM,EAAE,SAAS,OAAO,GAAG,UAAS,IAAK;AACzC,UAAM,kBAAkB,cAAc;AACtC,gCAA4B,KAAK;MAC/B,UAAU,MAAM,OAAO,IAAI;MAC3B;MACA,QAAQ,MAAM,KAAK,IAAI;MACvB,GAAG,wBAAwB,CAAA,GAAI,SAAS;KACzC;EACH;AAEA,SAAO;AACT;;;ACsEM,SAAU,qBAKd,aACA,WAAiC;AAEjC,QAAM,OAAO,mBAAmB,WAAW;AAE3C,MAAI,SAAS;AACX,WAAO,4BACL,aACA,SAAS;AAGb,MAAI,SAAS;AACX,WAAO,4BACL,aACA,SAAS;AAGb,MAAI,SAAS;AACX,WAAO,4BACL,aACA,SAAS;AAGb,MAAI,SAAS;AACX,WAAO,4BACL,aACA,SAAS;AAGb,SAAO,2BACL,aACA,SAA4B;AAEhC;AAYA,SAAS,4BACP,aACA,WAAiC;AAEjC,QAAM,EACJ,mBACA,SACA,KACA,OACA,IACA,OACA,cACA,sBACA,YACA,KAAI,IACF;AAEJ,2BAAyB,WAAW;AAEpC,QAAM,uBAAuB,oBAAoB,UAAU;AAC3D,QAAM,8BACJ,2BAA2B,iBAAiB;AAE9C,SAAO,UAAU;IACf;IACA,MAAM;MACJ,YAAY,OAAO;MACnB,QAAQ,YAAY,KAAK,IAAI;MAC7B,uBAAuB,YAAY,oBAAoB,IAAI;MAC3D,eAAe,YAAY,YAAY,IAAI;MAC3C,MAAM,YAAY,GAAG,IAAI;MACzB,MAAM;MACN,QAAQ,YAAY,KAAK,IAAI;MAC7B,QAAQ;MACR;MACA;MACA,GAAG,wBAAwB,aAAa,SAAS;KAClD;GACF;AACH;AAeA,SAAS,4BACP,aACA,WAAiC;AAEjC,QAAM,EACJ,SACA,KACA,OACA,IACA,OACA,kBACA,cACA,sBACA,YACA,KAAI,IACF;AAEJ,2BAAyB,WAAW;AAEpC,MAAI,sBAAsB,YAAY;AACtC,MAAI,WAAW,YAAY;AAE3B,MACE,YAAY,UACX,OAAO,wBAAwB,eAC9B,OAAO,aAAa,cACtB;AACA,UAAMC,SACJ,OAAO,YAAY,MAAM,CAAC,MAAM,WAC5B,YAAY,QACX,YAAY,MAAsB,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;AAEjE,UAAM,MAAM,YAAY;AACxB,UAAMC,eAAc,mBAAmB;MACrC,OAAAD;MACA;KACD;AAED,QAAI,OAAO,wBAAwB;AACjC,4BAAsB,6BAA6B;QACjD,aAAAC;OACD;AACH,QAAI,OAAO,aAAa,aAAa;AACnC,YAAMC,UAAS,cAAc,EAAE,OAAAF,QAAO,aAAAC,cAAa,IAAG,CAAE;AACxD,iBAAW,eAAe,EAAE,OAAAD,QAAO,aAAAC,cAAa,QAAAC,QAAM,CAAE;IAC1D;EACF;AAEA,QAAM,uBAAuB,oBAAoB,UAAU;AAE3D,QAAM,wBAAwB;IAC5B,YAAY,OAAO;IACnB,QAAQ,YAAY,KAAK,IAAI;IAC7B,uBAAuB,YAAY,oBAAoB,IAAI;IAC3D,eAAe,YAAY,YAAY,IAAI;IAC3C,MAAM,YAAY,GAAG,IAAI;IACzB,MAAM;IACN,QAAQ,YAAY,KAAK,IAAI;IAC7B,QAAQ;IACR;IACA,mBAAmB,YAAY,gBAAgB,IAAI;IACnD,uBAAuB,CAAA;IACvB,GAAG,wBAAwB,aAAa,SAAS;;AAGnD,QAAM,QAAe,CAAA;AACrB,QAAM,cAAqB,CAAA;AAC3B,QAAM,SAAgB,CAAA;AACtB,MAAI;AACF,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,EAAE,MAAM,YAAY,MAAK,IAAK,SAAS,CAAC;AAC9C,YAAM,KAAK,IAAI;AACf,kBAAY,KAAK,UAAU;AAC3B,aAAO,KAAK,KAAK;IACnB;AAEF,SAAO,UAAU;IACf;IACA;;MAEI,MAAM,CAAC,uBAAuB,OAAO,aAAa,MAAM,CAAC;;;MAEzD,MAAM,qBAAqB;;GAChC;AACH;AAWA,SAAS,4BACP,aACA,WAAiC;AAEjC,QAAM,EACJ,SACA,KACA,OACA,IACA,OACA,cACA,sBACA,YACA,KAAI,IACF;AAEJ,2BAAyB,WAAW;AAEpC,QAAM,uBAAuB,oBAAoB,UAAU;AAE3D,QAAM,wBAAwB;IAC5B,YAAY,OAAO;IACnB,QAAQ,YAAY,KAAK,IAAI;IAC7B,uBAAuB,YAAY,oBAAoB,IAAI;IAC3D,eAAe,YAAY,YAAY,IAAI;IAC3C,MAAM,YAAY,GAAG,IAAI;IACzB,MAAM;IACN,QAAQ,YAAY,KAAK,IAAI;IAC7B,QAAQ;IACR;IACA,GAAG,wBAAwB,aAAa,SAAS;;AAGnD,SAAO,UAAU;IACf;IACA,MAAM,qBAAqB;GAC5B;AACH;AAWA,SAAS,4BACP,aACA,WAAiC;AAEjC,QAAM,EAAE,SAAS,KAAK,MAAM,OAAO,IAAI,OAAO,YAAY,SAAQ,IAChE;AAEF,2BAAyB,WAAW;AAEpC,QAAM,uBAAuB,oBAAoB,UAAU;AAE3D,QAAM,wBAAwB;IAC5B,YAAY,OAAO;IACnB,QAAQ,YAAY,KAAK,IAAI;IAC7B,WAAW,YAAY,QAAQ,IAAI;IACnC,MAAM,YAAY,GAAG,IAAI;IACzB,MAAM;IACN,QAAQ,YAAY,KAAK,IAAI;IAC7B,QAAQ;IACR;IACA,GAAG,wBAAwB,aAAa,SAAS;;AAGnD,SAAO,UAAU;IACf;IACA,MAAM,qBAAqB;GAC5B;AACH;AASA,SAAS,2BACP,aACA,WAAuC;AAEvC,QAAM,EAAE,UAAU,GAAG,KAAK,MAAM,OAAO,IAAI,OAAO,SAAQ,IAAK;AAE/D,0BAAwB,WAAW;AAEnC,MAAI,wBAAwB;IAC1B,QAAQ,YAAY,KAAK,IAAI;IAC7B,WAAW,YAAY,QAAQ,IAAI;IACnC,MAAM,YAAY,GAAG,IAAI;IACzB,MAAM;IACN,QAAQ,YAAY,KAAK,IAAI;IAC7B,QAAQ;;AAGV,MAAI,WAAW;AACb,UAAM,KAAK,MAAK;AAEd,UAAI,UAAU,KAAK,KAAK;AACtB,cAAM,mBAAmB,UAAU,IAAI,OAAO;AAC9C,YAAI,kBAAkB;AAAG,iBAAO,UAAU;AAC1C,eAAO,OAAO,UAAU,MAAM,MAAM,KAAK;MAC3C;AAGA,UAAI,UAAU;AACZ,eAAO,OAAO,UAAU,CAAC,IAAI,OAAO,MAAM,UAAU,IAAI,GAAG;AAG7D,YAAMC,KAAI,OAAO,UAAU,MAAM,MAAM,KAAK;AAC5C,UAAI,UAAU,MAAMA;AAAG,cAAM,IAAI,oBAAoB,EAAE,GAAG,UAAU,EAAC,CAAE;AACvE,aAAOA;IACT,GAAE;AAEF,UAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,UAAM,IAAI,KAAK,UAAU,CAAC;AAE1B,4BAAwB;MACtB,GAAG;MACH,YAAY,CAAC;MACb,MAAM,SAAS,OAAO;MACtB,MAAM,SAAS,OAAO;;EAE1B,WAAW,UAAU,GAAG;AACtB,4BAAwB;MACtB,GAAG;MACH,YAAY,OAAO;MACnB;MACA;;EAEJ;AAEA,SAAO,MAAM,qBAAqB;AACpC;AAEM,SAAU,wBACd,aACA,YAAkC;AAElC,QAAM,YAAY,cAAc;AAChC,QAAM,EAAE,GAAG,QAAO,IAAK;AAEvB,MAAI,OAAO,UAAU,MAAM;AAAa,WAAO,CAAA;AAC/C,MAAI,OAAO,UAAU,MAAM;AAAa,WAAO,CAAA;AAC/C,MAAI,OAAO,MAAM,eAAe,OAAO,YAAY;AAAa,WAAO,CAAA;AAEvE,QAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,QAAM,IAAI,KAAK,UAAU,CAAC;AAE1B,QAAM,YAAY,MAAK;AACrB,QAAI,OAAO,YAAY;AAAU,aAAO,UAAU,YAAY,CAAC,IAAI;AACnE,QAAI,MAAM;AAAI,aAAO;AACrB,QAAI,MAAM;AAAI,aAAO,YAAY,CAAC;AAElC,WAAO,MAAM,MAAM,OAAO,YAAY,CAAC;EACzC,GAAE;AAEF,SAAO,CAAC,UAAU,MAAM,SAAS,OAAO,GAAG,MAAM,SAAS,OAAO,CAAC;AACpE;;;AC7cM,IAAO,uBAAP,cAAoC,UAAS;EACjD,YAAY,EAAE,SAAQ,IAAwC,CAAA,GAAE;AAC9D,UACE;MACE;MACA;MACA,KAAK,IAAI,GACX;MACE;MACA,UAAU;MACV,MAAM;KACP;EAEL;;AAMI,IAAO,+BAAP,cAA4C,UAAS;EACzD,YAAY,EACV,UACA,cACA,KAAI,GAKL;AACC,UAAM,iBAAiB,IAAI,uBAAuB;MAChD;MACA;MACA,MAAM;KACP;EACH;;", "names": ["sha256", "sha256", "sha256", "size", "size", "blobs", "commitments", "proofs", "v"]}