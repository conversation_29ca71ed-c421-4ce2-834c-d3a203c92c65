import { proxy } from 'valtio/vanilla';
import { subscribeKey as subKey } from 'valtio/vanilla/utils';
import { withErrorBoundary } from '../utils/withErrorBoundary.js';
import { OptionsController } from './OptionsController.js';
// -- State --------------------------------------------- //
const state = proxy({
    message: '',
    variant: 'info',
    open: false
});
// -- Controller ---------------------------------------- //
const controller = {
    state,
    subscribeKey(key, callback) {
        return subKey(state, key, callback);
    },
    open(message, variant) {
        const { debug } = OptionsController.state;
        const { shortMessage, longMessage } = message;
        if (debug) {
            state.message = shortMessage;
            state.variant = variant;
            state.open = true;
        }
        if (longMessage) {
            // eslint-disable-next-line no-console
            console.error(typeof longMessage === 'function' ? longMessage() : longMessage);
        }
    },
    close() {
        state.open = false;
        state.message = '';
        state.variant = 'info';
    }
};
// Export the controller wrapped with our error boundary
export const AlertController = withErrorBoundary(controller);
//# sourceMappingURL=AlertController.js.map