{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../../src/sign-client/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,aAAa,EACb,YAAY,EACZ,cAAc,EACd,eAAe,EACf,aAAa,EACd,MAAM,8BAA8B,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,SAAS,CAAC;AACzE,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AAEzC,MAAM,CAAC,OAAO,WAAW,SAAS,CAAC;IACjC,KAAK,KAAK,GAAG,sBAAsB,CAAC;IAEpC,UAAU,oBAAoB;QAC5B,SAAS,EAAE,WAAW,CAAC;QACvB,WAAW,EAAE,aAAa,CAAC;QAC3B,eAAe,EAAE,MAAM,CAAC;QACxB,aAAa,CAAC,EAAE,YAAY,CAAC,aAAa,CAAC;KAC5C;IAED,KAAK,qBAAqB,GACtB;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAE,GACjC,aAAa,CAAC,KAAK,CAAC,GACpB,YAAY,CAAC;IAEjB,UAAU,aAAa,CAAC,CAAC,GAAG,OAAO;QACjC,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,CAAC,CAAC;QACV,aAAa,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC;KAChC;IAED,UAAU,cAAc;QACtB,YAAY,EAAE,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAClD,aAAa,EAAE,aAAa,CAAC,qBAAqB,CAAC,CAAC;QACpD,YAAY,EAAE,aAAa,CAAC;YAC1B,OAAO,EAAE;gBAAE,MAAM,EAAE,MAAM,CAAC;gBAAC,MAAM,EAAE,GAAG,CAAA;aAAE,CAAC;YACzC,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC,CAAC;QACH,aAAa,EAAE,aAAa,CAAC,aAAa,GAAG,YAAY,CAAC,CAAC;KAC5D;IAED,UAAU,OAAQ,SAAQ,SAAS,CAAC,OAAO;QACzC,QAAQ,EAAE,QAAQ,CAAC;QACnB,IAAI,CAAC,EAAE,KAAK,CAAC;QACb,SAAS,EAAE,MAAM,CAAC;KACnB;IAED,UAAU,QAAQ;QAChB,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,GAAG,EAAE,MAAM,CAAC;QACZ,KAAK,EAAE,MAAM,EAAE,CAAC;QAChB,QAAQ,CAAC,EAAE;YACT,MAAM,CAAC,EAAE,MAAM,CAAC;YAChB,SAAS,CAAC,EAAE,MAAM,CAAC;YACnB,QAAQ,CAAC,EAAE,OAAO,CAAC;SACpB,CAAC;QACF,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB;IAED,UAAU,aAAa,CAAC,CAAC,SAAS,cAAc,GAAG,eAAe;QAChE,KAAK,EAAE,MAAM,CAAC;QACd,OAAO,EAAE,CAAC,CAAC;KACZ;IAOD,KAAK,mBAAmB,GAAG;QACzB,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,GAAG,CAAC,EAAE,MAAM,CAAC;KACd,GAAG,IAAI,CAAC,qBAAqB,EAAE,KAAK,GAAG,SAAS,CAAC,CAAC;IAEnD,UAAU,qBAAqB;QAC7B,MAAM,EAAE,MAAM,CAAC;QACf,GAAG,EAAE,MAAM,CAAC;QACZ,KAAK,EAAE,MAAM,CAAC;QACd,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;QACrB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE,MAAM,CAAC;KACf;IAGD,KAAK,aAAa,GAAG;QACnB,MAAM,EAAE,MAAM,EAAE,CAAC;KAClB,GAAG,qBAAqB,CAAC;IAE1B,KAAK,yBAAyB,GAAG;QAC/B,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB,GAAG,EAAE,MAAM,CAAC;KACb,GAAG,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAE/B,KAAK,aAAa,GAAG;QACnB,OAAO,EAAE,MAAM,CAAC;QAChB,GAAG,EAAE,MAAM,CAAC;KACb,GAAG,aAAa,CAAC;IAElB,KAAK,YAAY,GAAG;QAClB,GAAG,EAAE,MAAM,CAAC;KACb,GAAG,qBAAqB,CAAC;IAE1B,UAAU,WAAW;QACnB,CAAC,EAAE,SAAS,CAAC;KACd;IAED,UAAU,cAAc;QACtB,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC;QACxB,CAAC,EAAE,MAAM,CAAC;QACV,CAAC,CAAC,EAAE,MAAM,CAAC;KACZ;IAED,UAAU,KAAK;QACb,CAAC,EAAE,WAAW,CAAC;QACf,CAAC,EAAE,YAAY,CAAC;QAChB,CAAC,EAAE,cAAc,CAAC;KACnB;IAED,UAAU,cAAc;QACtB,EAAE,EAAE,MAAM,CAAC;QACX,YAAY,EAAE,MAAM,CAAC;QACrB,SAAS,EAAE,WAAW,CAAC;QACvB,eAAe,EAAE,MAAM,CAAC;QACxB,WAAW,EAAE,aAAa,CAAC;QAC3B,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC;QAC9B,aAAa,CAAC,EAAE,YAAY,CAAC,aAAa,CAAC;KAC5C;IAED,UAAU,gCAAgC;QACxC,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,KAAK,EAAE,CAAC;KAChB;IAED,UAAU,iCAAiC;QACzC,SAAS,EAAE,WAAW,CAAC;QACvB,MAAM,EAAE,KAAK,EAAE,CAAC;KACjB;IAED,UAAU,iBAAiB;QACzB,EAAE,EAAE,MAAM,CAAC;QACX,KAAK,EAAE,aAAa,CAAC;KACtB;IAED,KAAK,YAAY,GAAG,iCAAiC,CAAC,QAAQ,CAAC,CAAC;IAEhE,UAAU,WAAW;QACnB,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,QAAQ,CAAC;KACpB;IAED,UAAU,gCAAgC;QACxC,SAAS,EAAE,WAAW,CAAC;QACvB,WAAW,EAAE,aAAa,CAAC;QAC3B,eAAe,EAAE,MAAM,CAAC;KACzB;IAED,UAAU,0BAA2B,SAAQ,gCAAgC;QAC3E,aAAa,EAAE,MAAM,CAAC,OAAO,CAAC;KAC/B;IAED,KAAK,0BAA0B,GAAG;QAChC,KAAK,CAAC,EAAE,SAAS,CAAC,YAAY,CAAC;QAC/B,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC;KAC9B,CAAC;CACH;AAED,MAAM,MAAM,KAAK,GAAG;IAClB,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IACtB,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE;QAAE,aAAa,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IACvE,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,YAAY,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IACvE,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;CACpD,CAAC"}