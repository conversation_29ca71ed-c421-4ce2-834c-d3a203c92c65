# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.32.0]
### Added
- feat: setup stencil development environment with component preview ([#1196](https://github.com/MetaMask/metamask-sdk/pull/1196))

### Fixed
- fix: user rejection bug ([#1202](https://github.com/MetaMask/metamask-sdk/pull/1202))

## [0.31.5]
### Added
- chore: add analytics to install modal ([#1189](https://github.com/MetaMask/metamask-sdk/pull/1189))

### Fixed
- fix: correct event handler binding in modal components ([#1191](https://github.com/MetaMask/metamask-sdk/pull/1191))
- fix: remove console log when render install modal ([#1190](https://github.com/MetaMask/metamask-sdk/pull/1190))

## [0.31.2]
### Fixed
- Set initial modal tab based on preferDesktop option ([#1158](https://github.com/MetaMask/metamask-sdk/pull/1158))

## [0.31.1]
### Changed
- fix: Tell webpack about dynamic import + fixed polyfills ([#1151](https://github.com/MetaMask/metamask-sdk/pull/1151))

## [0.31.0]
### Changed
- refactor(sdk-install-modal-web): migrate from i18next to custom SimpleI18n implementation ([#1141](https://github.com/MetaMask/metamask-sdk/pull/1141))
- refactor(sdk-install-modal-web): migrate to StencilJS + Lazy Loading ([#1139](https://github.com/MetaMask/metamask-sdk/pull/1139))
- style: add inline styles to div elements ([#1138](https://github.com/MetaMask/metamask-sdk/pull/1138))
- refactor: replace qr-code-styling with smaller library ([#1129](https://github.com/MetaMask/metamask-sdk/pull/1129))

## [0.30.2]
### Uncategorized
- align version with sdk

## [0.30.0]

## [0.29.2]
### Added
- chore: bump .change files to 0.29.2 ([#1071](https://github.com/MetaMask/metamask-sdk/pull/1071))

## [0.29.1]
### Added
- feat: bump versions for publishing ([#1068](https://github.com/MetaMask/metamask-sdk/pull/1068))

## [0.28.1]
### Added
- Release 85.0.0 ([#1006](https://github.com/MetaMask/metamask-sdk/pull/1006))

## [0.26.5]
### Added
- chore: update peerDependencies of ‘@metamask/sdk-install-modal-web’ to resolve installation warnings ([#927](https://github.com/MetaMask/metamask-sdk/pull/927))

## [0.26.4]
### Added
- chore: update SDK dependencies to resolve version conflicts ([#921](https://github.com/MetaMask/metamask-sdk/pull/921))

## [0.26.0]
### Added
- feat: add script to align version before publishing ([#902](https://github.com/MetaMask/metamask-sdk/pull/902))

## [0.20.4]
### Added
- chore: update prepare-preview-builds CI job ([#869](https://github.com/MetaMask/metamask-sdk/pull/869))

## [0.20.2]
### Added
- skip version because of publishing issue

## [0.20.1]
### Added
- feat: trigger new version ([#840](https://github.com/MetaMask/metamask-sdk/pull/840))
- Revert "Release 63.0.0" ([#839](https://github.com/MetaMask/metamask-sdk/pull/839))
- Release 62.0.0 ([#838](https://github.com/MetaMask/metamask-sdk/pull/838))
- Revert "feat: release 62.0.0" ([#837](https://github.com/MetaMask/metamask-sdk/pull/837))
- feat: release 62.0.0 ([#836](https://github.com/MetaMask/metamask-sdk/pull/836))

## [0.20.0]
### Added
- feat: align version ([#835](https://github.com/MetaMask/metamask-sdk/pull/835))

## [0.18.5]
### Added
- fix: preferDesktop and onboarding ([#807](https://github.com/MetaMask/metamask-sdk/pull/807))

## [0.17.0]
### Added
- chore: disable logs when running unit tests in CI ([#727](https://github.com/MetaMask/metamask-sdk/pull/727))

## [0.16.0]
### Added
- feat: activate sourcemap support in all packages and resolve issues ([#730](https://github.com/MetaMask/metamask-sdk/pull/730))
- chore: reduce the bundle size ([#725](https://github.com/MetaMask/metamask-sdk/pull/725))
- feat: sdk bundle size opt methodology ([#722](https://github.com/MetaMask/metamask-sdk/pull/722))

## [0.15.0]
### Added
- chore: sourcemaps ([#687](https://github.com/MetaMask/metamask-sdk/pull/687))
- Revert "Release 48.0.0" ([#686](https://github.com/MetaMask/metamask-sdk/pull/686))
- Release 48.0.0 ([#684](https://github.com/MetaMask/metamask-sdk/pull/684))
- feat: disable sourcemaps ([#678](https://github.com/MetaMask/metamask-sdk/pull/678))

## [0.14.1]
### Added
- Force align package version to sdk

## [0.13.0]
### Added
- Force align package version to sdk

## [0.12.2]
### Added
- feat: optimize rollup builds ([#496](https://github.com/MetaMask/metamask-sdk/pull/496))

## [0.12.1]
### Added
- Force align package version to sdk

## [0.12.0]
### Added
- Force align package version to sdk

## [0.11.1]
### Added
- Force align package version to sdk

## [0.11.0]
### Added
- feat: rename metamask_chainRPCs to metamask_batch and add unit tests ([#440](https://github.com/MetaMask/metamask-sdk/pull/440))

## [0.10.0]
### Added
- Force align package version to sdk

## [0.9.0]
### Added
- feat: add sdk version and change modal order ([#405](https://github.com/MetaMask/metamask-sdk/pull/405))
- feat: implementing internationalization via i18next package ([#403](https://github.com/MetaMask/metamask-sdk/pull/403))
- fix: modal cleanup and otp selection ([#402](https://github.com/MetaMask/metamask-sdk/pull/402))

## [0.7.0]
### Added
- feat: rpc read only calls and infura provider ([#353](https://github.com/MetaMask/metamask-sdk/pull/353))
- feat: extend the time we resume the session without showing OTP ([#348](https://github.com/MetaMask/metamask-sdk/pull/348))
- feat: add codecov to CI ([#343](https://github.com/MetaMask/metamask-sdk/pull/343))

## [0.6.2]
### Added
- fix: edge case when modal not fully resetting after termination ([#339](https://github.com/MetaMask/metamask-sdk/pull/339))

## [0.6.1]
### Added
- feat: align versions([#323](https://github.com/MetaMask/metamask-sdk/pull/323))

## [0.6.0]
### Added
- feat: trigger pipeline detection ([#307](https://github.com/MetaMask/metamask-sdk/pull/307))

## [0.5.1]
### Added
- feat: optimize modal rendering and re-use existing node ([#206](https://github.com/MetaMask/metamask-sdk/pull/206))

## [0.3.3]
### Added
- [FEAT] choose between extension and mobile wallet ([#146](https://github.com/MetaMask/metamask-sdk/pull/146))
- [fix] #145 missing css color ([#149](https://github.com/MetaMask/metamask-sdk/pull/149))

## [0.3.2]
### Added
- [FEAT] add nextjs demo project in examples/ ([#123](https://github.com/MetaMask/metamask-sdk/pull/123))

## [0.3.0]
### Added
- feat(sdk-install-modal): add umd support ([#122](https://github.com/MetaMask/metamask-sdk/pull/122))
- fix: invalid types for sdk-install-modal-web ([#117](https://github.com/MetaMask/metamask-sdk/pull/117))
- [FEAT] sdk react toolkits (Part1) ([#116](https://github.com/MetaMask/metamask-sdk/pull/116))
- [REFACTOR]: install modal web ([#114](https://github.com/MetaMask/metamask-sdk/pull/114))
- Release 1.0.0 ([#107](https://github.com/MetaMask/metamask-sdk/pull/107))
- Revert "Release 1.0.0" ([#105](https://github.com/MetaMask/metamask-sdk/pull/105))
- Release 1.0.0 ([#103](https://github.com/MetaMask/metamask-sdk/pull/103))
- Update GitHub actions workflows ([#102](https://github.com/MetaMask/metamask-sdk/pull/102))
- [FEAT] Yarn v3 migration ([#100](https://github.com/MetaMask/metamask-sdk/pull/100))

[Unreleased]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/<EMAIL>
[0.32.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.31.5...@metamask/sdk-install-modal-web@0.32.0
[0.31.5]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.31.2...@metamask/sdk-install-modal-web@0.31.5
[0.31.2]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.31.1...@metamask/sdk-install-modal-web@0.31.2
[0.31.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.31.0...@metamask/sdk-install-modal-web@0.31.1
[0.31.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.30.2...@metamask/sdk-install-modal-web@0.31.0
[0.30.2]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.30.0...@metamask/sdk-install-modal-web@0.30.2
[0.30.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.29.2...@metamask/sdk-install-modal-web@0.30.0
[0.29.2]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.29.1...@metamask/sdk-install-modal-web@0.29.2
[0.29.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.28.1...@metamask/sdk-install-modal-web@0.29.1
[0.28.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.26.5...@metamask/sdk-install-modal-web@0.28.1
[0.26.5]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.26.4...@metamask/sdk-install-modal-web@0.26.5
[0.26.4]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.26.0...@metamask/sdk-install-modal-web@0.26.4
[0.26.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.20.4...@metamask/sdk-install-modal-web@0.26.0
[0.20.4]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.20.2...@metamask/sdk-install-modal-web@0.20.4
[0.20.2]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.20.1...@metamask/sdk-install-modal-web@0.20.2
[0.20.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.20.0...@metamask/sdk-install-modal-web@0.20.1
[0.20.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.18.5...@metamask/sdk-install-modal-web@0.20.0
[0.18.5]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.17.0...@metamask/sdk-install-modal-web@0.18.5
[0.17.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.16.0...@metamask/sdk-install-modal-web@0.17.0
[0.16.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.15.0...@metamask/sdk-install-modal-web@0.16.0
[0.15.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.14.1...@metamask/sdk-install-modal-web@0.15.0
[0.14.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.13.0...@metamask/sdk-install-modal-web@0.14.1
[0.13.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.12.2...@metamask/sdk-install-modal-web@0.13.0
[0.12.2]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.12.1...@metamask/sdk-install-modal-web@0.12.2
[0.12.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.12.0...@metamask/sdk-install-modal-web@0.12.1
[0.12.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.11.1...@metamask/sdk-install-modal-web@0.12.0
[0.11.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.11.0...@metamask/sdk-install-modal-web@0.11.1
[0.11.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.10.0...@metamask/sdk-install-modal-web@0.11.0
[0.10.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.9.0...@metamask/sdk-install-modal-web@0.10.0
[0.9.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.7.0...@metamask/sdk-install-modal-web@0.9.0
[0.7.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.6.2...@metamask/sdk-install-modal-web@0.7.0
[0.6.2]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.6.1...@metamask/sdk-install-modal-web@0.6.2
[0.6.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.6.0...@metamask/sdk-install-modal-web@0.6.1
[0.6.0]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.5.1...@metamask/sdk-install-modal-web@0.6.0
[0.5.1]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.3.3...@metamask/sdk-install-modal-web@0.5.1
[0.3.3]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.3.2...@metamask/sdk-install-modal-web@0.3.3
[0.3.2]: https://github.com/MetaMask/metamask-sdk/compare/@metamask/sdk-install-modal-web@0.3.0...@metamask/sdk-install-modal-web@0.3.2
[0.3.0]: https://github.com/MetaMask/metamask-sdk/releases/tag/@metamask/sdk-install-modal-web@0.3.0
