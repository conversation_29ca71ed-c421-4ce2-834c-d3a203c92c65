{"version": 3, "sources": ["../../lit-html/src/lit-html.ts", "../../@lit/reactive-element/src/css-tag.ts", "../../@lit/reactive-element/src/reactive-element.ts", "../../lit-element/src/lit-element.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// IMPORTANT: these imports must be type-only\nimport type {Directive, DirectiveResult, PartInfo} from './directive.js';\nimport type {TrustedHTML, TrustedTypesWindow} from 'trusted-types/lib/index.js';\n\nconst DEV_MODE = true;\nconst ENABLE_EXTRA_SECURITY_HOOKS = true;\nconst ENABLE_SHADYDOM_NOPATCH = true;\nconst NODE_MODE = false;\n\n// Allows minifiers to rename references to globalThis\nconst global = globalThis;\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace LitUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry =\n      | TemplatePrep\n      | TemplateInstantiated\n      | TemplateInstantiatedAndUpdated\n      | TemplateUpdating\n      | BeginRender\n      | EndRender\n      | CommitPartEntry\n      | SetPartValue;\n    export interface TemplatePrep {\n      kind: 'template prep';\n      template: Template;\n      strings: TemplateStringsArray;\n      clonableTemplate: HTMLTemplateElement;\n      parts: TemplatePart[];\n    }\n    export interface BeginRender {\n      kind: 'begin render';\n      id: number;\n      value: unknown;\n      container: HTMLElement | DocumentFragment;\n      options: RenderOptions | undefined;\n      part: ChildPart | undefined;\n    }\n    export interface EndRender {\n      kind: 'end render';\n      id: number;\n      value: unknown;\n      container: HTMLElement | DocumentFragment;\n      options: RenderOptions | undefined;\n      part: ChildPart;\n    }\n    export interface TemplateInstantiated {\n      kind: 'template instantiated';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      fragment: Node;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface TemplateInstantiatedAndUpdated {\n      kind: 'template instantiated and updated';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      fragment: Node;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface TemplateUpdating {\n      kind: 'template updating';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface SetPartValue {\n      kind: 'set part';\n      part: Part;\n      value: unknown;\n      valueIndex: number;\n      values: unknown[];\n      templateInstance: TemplateInstance;\n    }\n\n    export type CommitPartEntry =\n      | CommitNothingToChildEntry\n      | CommitText\n      | CommitNode\n      | CommitAttribute\n      | CommitProperty\n      | CommitBooleanAttribute\n      | CommitEventListener\n      | CommitToElementBinding;\n\n    export interface CommitNothingToChildEntry {\n      kind: 'commit nothing to child';\n      start: ChildNode;\n      end: ChildNode | null;\n      parent: Disconnectable | undefined;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitText {\n      kind: 'commit text';\n      node: Text;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitNode {\n      kind: 'commit node';\n      start: Node;\n      parent: Disconnectable | undefined;\n      value: Node;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitAttribute {\n      kind: 'commit attribute';\n      element: Element;\n      name: string;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitProperty {\n      kind: 'commit property';\n      element: Element;\n      name: string;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitBooleanAttribute {\n      kind: 'commit boolean attribute';\n      element: Element;\n      name: string;\n      value: boolean;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitEventListener {\n      kind: 'commit event listener';\n      element: Element;\n      name: string;\n      value: unknown;\n      oldListener: unknown;\n      options: RenderOptions | undefined;\n      // True if we're removing the old event listener (e.g. because settings changed, or value is nothing)\n      removeListener: boolean;\n      // True if we're adding a new event listener (e.g. because first render, or settings changed)\n      addListener: boolean;\n    }\n\n    export interface CommitToElementBinding {\n      kind: 'commit to element binding';\n      element: Element;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: LitUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<LitUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n// Used for connecting beginRender and endRender events when there are nested\n// renders when errors are thrown preventing an endRender event from being\n// called.\nlet debugLogRenderId = 0;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  global.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += code\n      ? ` See https://lit.dev/msg/${code} for more information.`\n      : '';\n    if (\n      !global.litIssuedWarnings!.has(warning) &&\n      !global.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      global.litIssuedWarnings!.add(warning);\n    }\n  };\n\n  queueMicrotask(() => {\n    issueWarning(\n      'dev-mode',\n      `Lit is in dev mode. Not recommended for production!`\n    );\n  });\n}\n\nconst wrap =\n  ENABLE_SHADYDOM_NOPATCH &&\n  global.ShadyDOM?.inUse &&\n  global.ShadyDOM?.noPatch === true\n    ? (global.ShadyDOM!.wrap as <T extends Node>(node: T) => T)\n    : <T extends Node>(node: T) => node;\n\nconst trustedTypes = (global as unknown as TrustedTypesWindow).trustedTypes;\n\n/**\n * Our TrustedTypePolicy for HTML which is declared using the html template\n * tag function.\n *\n * That HTML is a developer-authored constant, and is parsed with innerHTML\n * before any untrusted expressions have been mixed in. Therefor it is\n * considered safe by construction.\n */\nconst policy = trustedTypes\n  ? trustedTypes.createPolicy('lit-html', {\n      createHTML: (s) => s,\n    })\n  : undefined;\n\n/**\n * Used to sanitize any value before it is written into the DOM. This can be\n * used to implement a security policy of allowed and disallowed values in\n * order to prevent XSS attacks.\n *\n * One way of using this callback would be to check attributes and properties\n * against a list of high risk fields, and require that values written to such\n * fields be instances of a class which is safe by construction. Closure's Safe\n * HTML Types is one implementation of this technique (\n * https://github.com/google/safe-html-types/blob/master/doc/safehtml-types.md).\n * The TrustedTypes polyfill in API-only mode could also be used as a basis\n * for this technique (https://github.com/WICG/trusted-types).\n *\n * @param node The HTML node (usually either a #text node or an Element) that\n *     is being written to. Note that this is just an exemplar node, the write\n *     may take place against another instance of the same class of node.\n * @param name The name of an attribute or property (for example, 'href').\n * @param type Indicates whether the write that's about to be performed will\n *     be to a property or a node.\n * @return A function that will sanitize this class of writes.\n */\nexport type SanitizerFactory = (\n  node: Node,\n  name: string,\n  type: 'property' | 'attribute'\n) => ValueSanitizer;\n\n/**\n * A function which can sanitize values that will be written to a specific kind\n * of DOM sink.\n *\n * See SanitizerFactory.\n *\n * @param value The value to sanitize. Will be the actual value passed into\n *     the lit-html template literal, so this could be of any type.\n * @return The value to write to the DOM. Usually the same as the input value,\n *     unless sanitization is needed.\n */\nexport type ValueSanitizer = (value: unknown) => unknown;\n\nconst identityFunction: ValueSanitizer = (value: unknown) => value;\nconst noopSanitizer: SanitizerFactory = (\n  _node: Node,\n  _name: string,\n  _type: 'property' | 'attribute'\n) => identityFunction;\n\n/** Sets the global sanitizer factory. */\nconst setSanitizer = (newSanitizer: SanitizerFactory) => {\n  if (!ENABLE_EXTRA_SECURITY_HOOKS) {\n    return;\n  }\n  if (sanitizerFactoryInternal !== noopSanitizer) {\n    throw new Error(\n      `Attempted to overwrite existing lit-html security policy.` +\n        ` setSanitizeDOMValueFactory should be called at most once.`\n    );\n  }\n  sanitizerFactoryInternal = newSanitizer;\n};\n\n/**\n * Only used in internal tests, not a part of the public API.\n */\nconst _testOnlyClearSanitizerFactoryDoNotCallOrElse = () => {\n  sanitizerFactoryInternal = noopSanitizer;\n};\n\nconst createSanitizer: SanitizerFactory = (node, name, type) => {\n  return sanitizerFactoryInternal(node, name, type);\n};\n\n// Added to an attribute name to mark the attribute as bound so we can find\n// it easily.\nconst boundAttributeSuffix = '$lit$';\n\n// This marker is used in many syntactic positions in HTML, so it must be\n// a valid element name and attribute name. We don't support dynamic names (yet)\n// but this at least ensures that the parse tree is closer to the template\n// intention.\nconst marker = `lit$${Math.random().toFixed(9).slice(2)}$`;\n\n// String used to tell if a comment is a marker comment\nconst markerMatch = '?' + marker;\n\n// Text used to insert a comment marker node. We use processing instruction\n// syntax because it's slightly smaller, but parses as a comment node.\nconst nodeMarker = `<${markerMatch}>`;\n\nconst d =\n  NODE_MODE && global.document === undefined\n    ? ({\n        createTreeWalker() {\n          return {};\n        },\n      } as unknown as Document)\n    : document;\n\n// Creates a dynamic marker. We never have to search for these in the DOM.\nconst createMarker = () => d.createComment('');\n\n// https://tc39.github.io/ecma262/#sec-typeof-operator\ntype Primitive = null | undefined | boolean | number | string | symbol | bigint;\nconst isPrimitive = (value: unknown): value is Primitive =>\n  value === null || (typeof value != 'object' && typeof value != 'function');\nconst isArray = Array.isArray;\nconst isIterable = (value: unknown): value is Iterable<unknown> =>\n  isArray(value) ||\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  typeof (value as any)?.[Symbol.iterator] === 'function';\n\nconst SPACE_CHAR = `[ \\t\\n\\f\\r]`;\nconst ATTR_VALUE_CHAR = `[^ \\t\\n\\f\\r\"'\\`<>=]`;\nconst NAME_CHAR = `[^\\\\s\"'>=/]`;\n\n// These regexes represent the five parsing states that we care about in the\n// Template's HTML scanner. They match the *end* of the state they're named\n// after.\n// Depending on the match, we transition to a new state. If there's no match,\n// we stay in the same state.\n// Note that the regexes are stateful. We utilize lastIndex and sync it\n// across the multiple regexes used. In addition to the five regexes below\n// we also dynamically create a regex to find the matching end tags for raw\n// text elements.\n\n/**\n * End of text is: `<` followed by:\n *   (comment start) or (tag) or (dynamic tag binding)\n */\nconst textEndRegex = /<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g;\nconst COMMENT_START = 1;\nconst TAG_NAME = 2;\nconst DYNAMIC_TAG_NAME = 3;\n\nconst commentEndRegex = /-->/g;\n/**\n * Comments not started with <!--, like </{, can be ended by a single `>`\n */\nconst comment2EndRegex = />/g;\n\n/**\n * The tagEnd regex matches the end of the \"inside an opening\" tag syntax\n * position. It either matches a `>`, an attribute-like sequence, or the end\n * of the string after a space (attribute-name position ending).\n *\n * See attributes in the HTML spec:\n * https://www.w3.org/TR/html5/syntax.html#elements-attributes\n *\n * \" \\t\\n\\f\\r\" are HTML space characters:\n * https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * So an attribute is:\n *  * The name: any character except a whitespace character, (\"), ('), \">\",\n *    \"=\", or \"/\". Note: this is different from the HTML spec which also excludes control characters.\n *  * Followed by zero or more space characters\n *  * Followed by \"=\"\n *  * Followed by zero or more space characters\n *  * Followed by:\n *    * Any character except space, ('), (\"), \"<\", \">\", \"=\", (`), or\n *    * (\") then any non-(\"), or\n *    * (') then any non-(')\n */\nconst tagEndRegex = new RegExp(\n  `>|${SPACE_CHAR}(?:(${NAME_CHAR}+)(${SPACE_CHAR}*=${SPACE_CHAR}*(?:${ATTR_VALUE_CHAR}|(\"|')|))|$)`,\n  'g'\n);\nconst ENTIRE_MATCH = 0;\nconst ATTRIBUTE_NAME = 1;\nconst SPACES_AND_EQUALS = 2;\nconst QUOTE_CHAR = 3;\n\nconst singleQuoteAttrEndRegex = /'/g;\nconst doubleQuoteAttrEndRegex = /\"/g;\n/**\n * Matches the raw text elements.\n *\n * Comments are not parsed within raw text elements, so we need to search their\n * text content for marker strings.\n */\nconst rawTextElement = /^(?:script|style|textarea|title)$/i;\n\n/** TemplateResult types */\nconst HTML_RESULT = 1;\nconst SVG_RESULT = 2;\nconst MATHML_RESULT = 3;\n\ntype ResultType = typeof HTML_RESULT | typeof SVG_RESULT | typeof MATHML_RESULT;\n\n// TemplatePart types\n// IMPORTANT: these must match the values in PartType\nconst ATTRIBUTE_PART = 1;\nconst CHILD_PART = 2;\nconst PROPERTY_PART = 3;\nconst BOOLEAN_ATTRIBUTE_PART = 4;\nconst EVENT_PART = 5;\nconst ELEMENT_PART = 6;\nconst COMMENT_PART = 7;\n\n/**\n * The return type of the template tag functions, {@linkcode html} and\n * {@linkcode svg} when it hasn't been compiled by @lit-labs/compiler.\n *\n * A `TemplateResult` object holds all the information about a template\n * expression required to render it: the template strings, expression values,\n * and type of template (html or svg).\n *\n * `TemplateResult` objects do not create any DOM on their own. To create or\n * update DOM you need to render the `TemplateResult`. See\n * [Rendering](https://lit.dev/docs/components/rendering) for more information.\n *\n */\nexport type UncompiledTemplateResult<T extends ResultType = ResultType> = {\n  // This property needs to remain unminified.\n  ['_$litType$']: T;\n  strings: TemplateStringsArray;\n  values: unknown[];\n};\n\n/**\n * This is a template result that may be either uncompiled or compiled.\n *\n * In the future, TemplateResult will be this type. If you want to explicitly\n * note that a template result is potentially compiled, you can reference this\n * type and it will continue to behave the same through the next major version\n * of Lit. This can be useful for code that wants to prepare for the next\n * major version of Lit.\n */\nexport type MaybeCompiledTemplateResult<T extends ResultType = ResultType> =\n  | UncompiledTemplateResult<T>\n  | CompiledTemplateResult;\n\n/**\n * The return type of the template tag functions, {@linkcode html} and\n * {@linkcode svg}.\n *\n * A `TemplateResult` object holds all the information about a template\n * expression required to render it: the template strings, expression values,\n * and type of template (html or svg).\n *\n * `TemplateResult` objects do not create any DOM on their own. To create or\n * update DOM you need to render the `TemplateResult`. See\n * [Rendering](https://lit.dev/docs/components/rendering) for more information.\n *\n * In Lit 4, this type will be an alias of\n * MaybeCompiledTemplateResult, so that code will get type errors if it assumes\n * that Lit templates are not compiled. When deliberately working with only\n * one, use either {@linkcode CompiledTemplateResult} or\n * {@linkcode UncompiledTemplateResult} explicitly.\n */\nexport type TemplateResult<T extends ResultType = ResultType> =\n  UncompiledTemplateResult<T>;\n\nexport type HTMLTemplateResult = TemplateResult<typeof HTML_RESULT>;\n\nexport type SVGTemplateResult = TemplateResult<typeof SVG_RESULT>;\n\nexport type MathMLTemplateResult = TemplateResult<typeof MATHML_RESULT>;\n\n/**\n * A TemplateResult that has been compiled by @lit-labs/compiler, skipping the\n * prepare step.\n */\nexport interface CompiledTemplateResult {\n  // This is a factory in order to make template initialization lazy\n  // and allow ShadyRenderOptions scope to be passed in.\n  // This property needs to remain unminified.\n  ['_$litType$']: CompiledTemplate;\n  values: unknown[];\n}\n\nexport interface CompiledTemplate extends Omit<Template, 'el'> {\n  // el is overridden to be optional. We initialize it on first render\n  el?: HTMLTemplateElement;\n\n  // The prepared HTML string to create a template element from.\n  // The type is a TemplateStringsArray to guarantee that the value came from\n  // source code, preventing a JSON injection attack.\n  h: TemplateStringsArray;\n}\n\n/**\n * Generates a template literal tag function that returns a TemplateResult with\n * the given result type.\n */\nconst tag =\n  <T extends ResultType>(type: T) =>\n  (strings: TemplateStringsArray, ...values: unknown[]): TemplateResult<T> => {\n    // Warn against templates octal escape sequences\n    // We do this here rather than in render so that the warning is closer to the\n    // template definition.\n    if (DEV_MODE && strings.some((s) => s === undefined)) {\n      console.warn(\n        'Some template strings are undefined.\\n' +\n          'This is probably caused by illegal octal escape sequences.'\n      );\n    }\n    if (DEV_MODE) {\n      // Import static-html.js results in a circular dependency which g3 doesn't\n      // handle. Instead we know that static values must have the field\n      // `_$litStatic$`.\n      if (\n        values.some((val) => (val as {_$litStatic$: unknown})?.['_$litStatic$'])\n      ) {\n        issueWarning(\n          '',\n          `Static values 'literal' or 'unsafeStatic' cannot be used as values to non-static templates.\\n` +\n            `Please use the static 'html' tag function. See https://lit.dev/docs/templates/expressions/#static-expressions`\n        );\n      }\n    }\n    return {\n      // This property needs to remain unminified.\n      ['_$litType$']: type,\n      strings,\n      values,\n    };\n  };\n\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const header = (title: string) => html`<h1>${title}</h1>`;\n * ```\n *\n * The `html` tag returns a description of the DOM to render as a value. It is\n * lazy, meaning no work is done until the template is rendered. When rendering,\n * if a template comes from the same expression as a previously rendered result,\n * it's efficiently updated instead of replaced.\n */\nexport const html = tag(HTML_RESULT);\n\n/**\n * Interprets a template literal as an SVG fragment that can efficiently render\n * to and update a container.\n *\n * ```ts\n * const rect = svg`<rect width=\"10\" height=\"10\"></rect>`;\n *\n * const myImage = html`\n *   <svg viewBox=\"0 0 10 10\" xmlns=\"http://www.w3.org/2000/svg\">\n *     ${rect}\n *   </svg>`;\n * ```\n *\n * The `svg` *tag function* should only be used for SVG fragments, or elements\n * that would be contained **inside** an `<svg>` HTML element. A common error is\n * placing an `<svg>` *element* in a template tagged with the `svg` tag\n * function. The `<svg>` element is an HTML element and should be used within a\n * template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an SVG fragment from the\n * `render()` method, as the SVG fragment will be contained within the element's\n * shadow root and thus not be properly contained within an `<svg>` HTML\n * element.\n */\nexport const svg = tag(SVG_RESULT);\n\n/**\n * Interprets a template literal as MathML fragment that can efficiently render\n * to and update a container.\n *\n * ```ts\n * const num = mathml`<mn>1</mn>`;\n *\n * const eq = html`\n *   <math>\n *     ${num}\n *   </math>`;\n * ```\n *\n * The `mathml` *tag function* should only be used for MathML fragments, or\n * elements that would be contained **inside** a `<math>` HTML element. A common\n * error is placing a `<math>` *element* in a template tagged with the `mathml`\n * tag function. The `<math>` element is an HTML element and should be used\n * within a template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an MathML fragment from the\n * `render()` method, as the MathML fragment will be contained within the\n * element's shadow root and thus not be properly contained within a `<math>`\n * HTML element.\n */\nexport const mathml = tag(MATHML_RESULT);\n\n/**\n * A sentinel value that signals that a value was handled by a directive and\n * should not be written to the DOM.\n */\nexport const noChange = Symbol.for('lit-noChange');\n\n/**\n * A sentinel value that signals a ChildPart to fully clear its content.\n *\n * ```ts\n * const button = html`${\n *  user.isAdmin\n *    ? html`<button>DELETE</button>`\n *    : nothing\n * }`;\n * ```\n *\n * Prefer using `nothing` over other falsy values as it provides a consistent\n * behavior between various expression binding contexts.\n *\n * In child expressions, `undefined`, `null`, `''`, and `nothing` all behave the\n * same and render no nodes. In attribute expressions, `nothing` _removes_ the\n * attribute, while `undefined` and `null` will render an empty string. In\n * property expressions `nothing` becomes `undefined`.\n */\nexport const nothing = Symbol.for('lit-nothing');\n\n/**\n * The cache of prepared templates, keyed by the tagged TemplateStringsArray\n * and _not_ accounting for the specific template tag used. This means that\n * template tags cannot be dynamic - they must statically be one of html, svg,\n * or attr. This restriction simplifies the cache lookup, which is on the hot\n * path for rendering.\n */\nconst templateCache = new WeakMap<TemplateStringsArray, Template>();\n\n/**\n * Object specifying options for controlling lit-html rendering. Note that\n * while `render` may be called multiple times on the same `container` (and\n * `renderBefore` reference node) to efficiently update the rendered content,\n * only the options passed in during the first render are respected during\n * the lifetime of renders to that unique `container` + `renderBefore`\n * combination.\n */\nexport interface RenderOptions {\n  /**\n   * An object to use as the `this` value for event listeners. It's often\n   * useful to set this to the host component rendering a template.\n   */\n  host?: object;\n  /**\n   * A DOM node before which to render content in the container.\n   */\n  renderBefore?: ChildNode | null;\n  /**\n   * Node used for cloning the template (`importNode` will be called on this\n   * node). This controls the `ownerDocument` of the rendered DOM, along with\n   * any inherited context. Defaults to the global `document`.\n   */\n  creationScope?: {importNode(node: Node, deep?: boolean): Node};\n  /**\n   * The initial connected state for the top-level part being rendered. If no\n   * `isConnected` option is set, `AsyncDirective`s will be connected by\n   * default. Set to `false` if the initial render occurs in a disconnected tree\n   * and `AsyncDirective`s should see `isConnected === false` for their initial\n   * render. The `part.setConnected()` method must be used subsequent to initial\n   * render to change the connected state of the part.\n   */\n  isConnected?: boolean;\n}\n\nconst walker = d.createTreeWalker(\n  d,\n  129 /* NodeFilter.SHOW_{ELEMENT|COMMENT} */\n);\n\nlet sanitizerFactoryInternal: SanitizerFactory = noopSanitizer;\n\n//\n// Classes only below here, const variable declarations only above here...\n//\n// Keeping variable declarations and classes together improves minification.\n// Interfaces and type aliases can be interleaved freely.\n//\n\n// Type for classes that have a `_directive` or `_directives[]` field, used by\n// `resolveDirective`\nexport interface DirectiveParent {\n  _$parent?: DirectiveParent;\n  _$isConnected: boolean;\n  __directive?: Directive;\n  __directives?: Array<Directive | undefined>;\n}\n\nfunction trustFromTemplateString(\n  tsa: TemplateStringsArray,\n  stringFromTSA: string\n): TrustedHTML {\n  // A security check to prevent spoofing of Lit template results.\n  // In the future, we may be able to replace this with Array.isTemplateObject,\n  // though we might need to make that check inside of the html and svg\n  // functions, because precompiled templates don't come in as\n  // TemplateStringArray objects.\n  if (!isArray(tsa) || !tsa.hasOwnProperty('raw')) {\n    let message = 'invalid template strings array';\n    if (DEV_MODE) {\n      message = `\n          Internal Error: expected template strings to be an array\n          with a 'raw' field. Faking a template strings array by\n          calling html or svg like an ordinary function is effectively\n          the same as calling unsafeHtml and can lead to major security\n          issues, e.g. opening your code up to XSS attacks.\n          If you're using the html or svg tagged template functions normally\n          and still seeing this error, please file a bug at\n          https://github.com/lit/lit/issues/new?template=bug_report.md\n          and include information about your build tooling, if any.\n        `\n        .trim()\n        .replace(/\\n */g, '\\n');\n    }\n    throw new Error(message);\n  }\n  return policy !== undefined\n    ? policy.createHTML(stringFromTSA)\n    : (stringFromTSA as unknown as TrustedHTML);\n}\n\n/**\n * Returns an HTML string for the given TemplateStringsArray and result type\n * (HTML or SVG), along with the case-sensitive bound attribute names in\n * template order. The HTML contains comment markers denoting the `ChildPart`s\n * and suffixes on bound attributes denoting the `AttributeParts`.\n *\n * @param strings template strings array\n * @param type HTML or SVG\n * @return Array containing `[html, attrNames]` (array returned for terseness,\n *     to avoid object fields since this code is shared with non-minified SSR\n *     code)\n */\nconst getTemplateHtml = (\n  strings: TemplateStringsArray,\n  type: ResultType\n): [TrustedHTML, Array<string>] => {\n  // Insert makers into the template HTML to represent the position of\n  // bindings. The following code scans the template strings to determine the\n  // syntactic position of the bindings. They can be in text position, where\n  // we insert an HTML comment, attribute value position, where we insert a\n  // sentinel string and re-write the attribute name, or inside a tag where\n  // we insert the sentinel string.\n  const l = strings.length - 1;\n  // Stores the case-sensitive bound attribute names in the order of their\n  // parts. ElementParts are also reflected in this array as undefined\n  // rather than a string, to disambiguate from attribute bindings.\n  const attrNames: Array<string> = [];\n  let html =\n    type === SVG_RESULT ? '<svg>' : type === MATHML_RESULT ? '<math>' : '';\n\n  // When we're inside a raw text tag (not it's text content), the regex\n  // will still be tagRegex so we can find attributes, but will switch to\n  // this regex when the tag ends.\n  let rawTextEndRegex: RegExp | undefined;\n\n  // The current parsing state, represented as a reference to one of the\n  // regexes\n  let regex = textEndRegex;\n\n  for (let i = 0; i < l; i++) {\n    const s = strings[i];\n    // The index of the end of the last attribute name. When this is\n    // positive at end of a string, it means we're in an attribute value\n    // position and need to rewrite the attribute name.\n    // We also use a special value of -2 to indicate that we encountered\n    // the end of a string in attribute name position.\n    let attrNameEndIndex = -1;\n    let attrName: string | undefined;\n    let lastIndex = 0;\n    let match!: RegExpExecArray | null;\n\n    // The conditions in this loop handle the current parse state, and the\n    // assignments to the `regex` variable are the state transitions.\n    while (lastIndex < s.length) {\n      // Make sure we start searching from where we previously left off\n      regex.lastIndex = lastIndex;\n      match = regex.exec(s);\n      if (match === null) {\n        break;\n      }\n      lastIndex = regex.lastIndex;\n      if (regex === textEndRegex) {\n        if (match[COMMENT_START] === '!--') {\n          regex = commentEndRegex;\n        } else if (match[COMMENT_START] !== undefined) {\n          // We started a weird comment, like </{\n          regex = comment2EndRegex;\n        } else if (match[TAG_NAME] !== undefined) {\n          if (rawTextElement.test(match[TAG_NAME])) {\n            // Record if we encounter a raw-text element. We'll switch to\n            // this regex at the end of the tag.\n            rawTextEndRegex = new RegExp(`</${match[TAG_NAME]}`, 'g');\n          }\n          regex = tagEndRegex;\n        } else if (match[DYNAMIC_TAG_NAME] !== undefined) {\n          if (DEV_MODE) {\n            throw new Error(\n              'Bindings in tag names are not supported. Please use static templates instead. ' +\n                'See https://lit.dev/docs/templates/expressions/#static-expressions'\n            );\n          }\n          regex = tagEndRegex;\n        }\n      } else if (regex === tagEndRegex) {\n        if (match[ENTIRE_MATCH] === '>') {\n          // End of a tag. If we had started a raw-text element, use that\n          // regex\n          regex = rawTextEndRegex ?? textEndRegex;\n          // We may be ending an unquoted attribute value, so make sure we\n          // clear any pending attrNameEndIndex\n          attrNameEndIndex = -1;\n        } else if (match[ATTRIBUTE_NAME] === undefined) {\n          // Attribute name position\n          attrNameEndIndex = -2;\n        } else {\n          attrNameEndIndex = regex.lastIndex - match[SPACES_AND_EQUALS].length;\n          attrName = match[ATTRIBUTE_NAME];\n          regex =\n            match[QUOTE_CHAR] === undefined\n              ? tagEndRegex\n              : match[QUOTE_CHAR] === '\"'\n                ? doubleQuoteAttrEndRegex\n                : singleQuoteAttrEndRegex;\n        }\n      } else if (\n        regex === doubleQuoteAttrEndRegex ||\n        regex === singleQuoteAttrEndRegex\n      ) {\n        regex = tagEndRegex;\n      } else if (regex === commentEndRegex || regex === comment2EndRegex) {\n        regex = textEndRegex;\n      } else {\n        // Not one of the five state regexes, so it must be the dynamically\n        // created raw text regex and we're at the close of that element.\n        regex = tagEndRegex;\n        rawTextEndRegex = undefined;\n      }\n    }\n\n    if (DEV_MODE) {\n      // If we have a attrNameEndIndex, which indicates that we should\n      // rewrite the attribute name, assert that we're in a valid attribute\n      // position - either in a tag, or a quoted attribute value.\n      console.assert(\n        attrNameEndIndex === -1 ||\n          regex === tagEndRegex ||\n          regex === singleQuoteAttrEndRegex ||\n          regex === doubleQuoteAttrEndRegex,\n        'unexpected parse state B'\n      );\n    }\n\n    // We have four cases:\n    //  1. We're in text position, and not in a raw text element\n    //     (regex === textEndRegex): insert a comment marker.\n    //  2. We have a non-negative attrNameEndIndex which means we need to\n    //     rewrite the attribute name to add a bound attribute suffix.\n    //  3. We're at the non-first binding in a multi-binding attribute, use a\n    //     plain marker.\n    //  4. We're somewhere else inside the tag. If we're in attribute name\n    //     position (attrNameEndIndex === -2), add a sequential suffix to\n    //     generate a unique attribute name.\n\n    // Detect a binding next to self-closing tag end and insert a space to\n    // separate the marker from the tag end:\n    const end =\n      regex === tagEndRegex && strings[i + 1].startsWith('/>') ? ' ' : '';\n    html +=\n      regex === textEndRegex\n        ? s + nodeMarker\n        : attrNameEndIndex >= 0\n          ? (attrNames.push(attrName!),\n            s.slice(0, attrNameEndIndex) +\n              boundAttributeSuffix +\n              s.slice(attrNameEndIndex)) +\n            marker +\n            end\n          : s + marker + (attrNameEndIndex === -2 ? i : end);\n  }\n\n  const htmlResult: string | TrustedHTML =\n    html +\n    (strings[l] || '<?>') +\n    (type === SVG_RESULT ? '</svg>' : type === MATHML_RESULT ? '</math>' : '');\n\n  // Returned as an array for terseness\n  return [trustFromTemplateString(strings, htmlResult), attrNames];\n};\n\n/** @internal */\nexport type {Template};\nclass Template {\n  /** @internal */\n  el!: HTMLTemplateElement;\n\n  parts: Array<TemplatePart> = [];\n\n  constructor(\n    // This property needs to remain unminified.\n    {strings, ['_$litType$']: type}: UncompiledTemplateResult,\n    options?: RenderOptions\n  ) {\n    let node: Node | null;\n    let nodeIndex = 0;\n    let attrNameIndex = 0;\n    const partCount = strings.length - 1;\n    const parts = this.parts;\n\n    // Create template element\n    const [html, attrNames] = getTemplateHtml(strings, type);\n    this.el = Template.createElement(html, options);\n    walker.currentNode = this.el.content;\n\n    // Re-parent SVG or MathML nodes into template root\n    if (type === SVG_RESULT || type === MATHML_RESULT) {\n      const wrapper = this.el.content.firstChild!;\n      wrapper.replaceWith(...wrapper.childNodes);\n    }\n\n    // Walk the template to find binding markers and create TemplateParts\n    while ((node = walker.nextNode()) !== null && parts.length < partCount) {\n      if (node.nodeType === 1) {\n        if (DEV_MODE) {\n          const tag = (node as Element).localName;\n          // Warn if `textarea` includes an expression and throw if `template`\n          // does since these are not supported. We do this by checking\n          // innerHTML for anything that looks like a marker. This catches\n          // cases like bindings in textarea there markers turn into text nodes.\n          if (\n            /^(?:textarea|template)$/i!.test(tag) &&\n            (node as Element).innerHTML.includes(marker)\n          ) {\n            const m =\n              `Expressions are not supported inside \\`${tag}\\` ` +\n              `elements. See https://lit.dev/msg/expression-in-${tag} for more ` +\n              `information.`;\n            if (tag === 'template') {\n              throw new Error(m);\n            } else issueWarning('', m);\n          }\n        }\n        // TODO (justinfagnani): for attempted dynamic tag names, we don't\n        // increment the bindingIndex, and it'll be off by 1 in the element\n        // and off by two after it.\n        if ((node as Element).hasAttributes()) {\n          for (const name of (node as Element).getAttributeNames()) {\n            if (name.endsWith(boundAttributeSuffix)) {\n              const realName = attrNames[attrNameIndex++];\n              const value = (node as Element).getAttribute(name)!;\n              const statics = value.split(marker);\n              const m = /([.?@])?(.*)/.exec(realName)!;\n              parts.push({\n                type: ATTRIBUTE_PART,\n                index: nodeIndex,\n                name: m[2],\n                strings: statics,\n                ctor:\n                  m[1] === '.'\n                    ? PropertyPart\n                    : m[1] === '?'\n                      ? BooleanAttributePart\n                      : m[1] === '@'\n                        ? EventPart\n                        : AttributePart,\n              });\n              (node as Element).removeAttribute(name);\n            } else if (name.startsWith(marker)) {\n              parts.push({\n                type: ELEMENT_PART,\n                index: nodeIndex,\n              });\n              (node as Element).removeAttribute(name);\n            }\n          }\n        }\n        // TODO (justinfagnani): benchmark the regex against testing for each\n        // of the 3 raw text element names.\n        if (rawTextElement.test((node as Element).tagName)) {\n          // For raw text elements we need to split the text content on\n          // markers, create a Text node for each segment, and create\n          // a TemplatePart for each marker.\n          const strings = (node as Element).textContent!.split(marker);\n          const lastIndex = strings.length - 1;\n          if (lastIndex > 0) {\n            (node as Element).textContent = trustedTypes\n              ? (trustedTypes.emptyScript as unknown as '')\n              : '';\n            // Generate a new text node for each literal section\n            // These nodes are also used as the markers for child parts\n            for (let i = 0; i < lastIndex; i++) {\n              (node as Element).append(strings[i], createMarker());\n              // Walk past the marker node we just added\n              walker.nextNode();\n              parts.push({type: CHILD_PART, index: ++nodeIndex});\n            }\n            // Note because this marker is added after the walker's current\n            // node, it will be walked to in the outer loop (and ignored), so\n            // we don't need to adjust nodeIndex here\n            (node as Element).append(strings[lastIndex], createMarker());\n          }\n        }\n      } else if (node.nodeType === 8) {\n        const data = (node as Comment).data;\n        if (data === markerMatch) {\n          parts.push({type: CHILD_PART, index: nodeIndex});\n        } else {\n          let i = -1;\n          while ((i = (node as Comment).data.indexOf(marker, i + 1)) !== -1) {\n            // Comment node has a binding marker inside, make an inactive part\n            // The binding won't work, but subsequent bindings will\n            parts.push({type: COMMENT_PART, index: nodeIndex});\n            // Move to the end of the match\n            i += marker.length - 1;\n          }\n        }\n      }\n      nodeIndex++;\n    }\n\n    if (DEV_MODE) {\n      // If there was a duplicate attribute on a tag, then when the tag is\n      // parsed into an element the attribute gets de-duplicated. We can detect\n      // this mismatch if we haven't precisely consumed every attribute name\n      // when preparing the template. This works because `attrNames` is built\n      // from the template string and `attrNameIndex` comes from processing the\n      // resulting DOM.\n      if (attrNames.length !== attrNameIndex) {\n        throw new Error(\n          `Detected duplicate attribute bindings. This occurs if your template ` +\n            `has duplicate attributes on an element tag. For example ` +\n            `\"<input ?disabled=\\${true} ?disabled=\\${false}>\" contains a ` +\n            `duplicate \"disabled\" attribute. The error was detected in ` +\n            `the following template: \\n` +\n            '`' +\n            strings.join('${...}') +\n            '`'\n        );\n      }\n    }\n\n    // We could set walker.currentNode to another node here to prevent a memory\n    // leak, but every time we prepare a template, we immediately render it\n    // and re-use the walker in new TemplateInstance._clone().\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'template prep',\n        template: this,\n        clonableTemplate: this.el,\n        parts: this.parts,\n        strings,\n      });\n  }\n\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @nocollapse */\n  static createElement(html: TrustedHTML, _options?: RenderOptions) {\n    const el = d.createElement('template');\n    el.innerHTML = html as unknown as string;\n    return el;\n  }\n}\n\nexport interface Disconnectable {\n  _$parent?: Disconnectable;\n  _$disconnectableChildren?: Set<Disconnectable>;\n  // Rather than hold connection state on instances, Disconnectables recursively\n  // fetch the connection state from the RootPart they are connected in via\n  // getters up the Disconnectable tree via _$parent references. This pushes the\n  // cost of tracking the isConnected state to `AsyncDirectives`, and avoids\n  // needing to pass all Disconnectables (parts, template instances, and\n  // directives) their connection state each time it changes, which would be\n  // costly for trees that have no AsyncDirectives.\n  _$isConnected: boolean;\n}\n\nfunction resolveDirective(\n  part: ChildPart | AttributePart | ElementPart,\n  value: unknown,\n  parent: DirectiveParent = part,\n  attributeIndex?: number\n): unknown {\n  // Bail early if the value is explicitly noChange. Note, this means any\n  // nested directive is still attached and is not run.\n  if (value === noChange) {\n    return value;\n  }\n  let currentDirective =\n    attributeIndex !== undefined\n      ? (parent as AttributePart).__directives?.[attributeIndex]\n      : (parent as ChildPart | ElementPart | Directive).__directive;\n  const nextDirectiveConstructor = isPrimitive(value)\n    ? undefined\n    : // This property needs to remain unminified.\n      (value as DirectiveResult)['_$litDirective$'];\n  if (currentDirective?.constructor !== nextDirectiveConstructor) {\n    // This property needs to remain unminified.\n    currentDirective?.['_$notifyDirectiveConnectionChanged']?.(false);\n    if (nextDirectiveConstructor === undefined) {\n      currentDirective = undefined;\n    } else {\n      currentDirective = new nextDirectiveConstructor(part as PartInfo);\n      currentDirective._$initialize(part, parent, attributeIndex);\n    }\n    if (attributeIndex !== undefined) {\n      ((parent as AttributePart).__directives ??= [])[attributeIndex] =\n        currentDirective;\n    } else {\n      (parent as ChildPart | Directive).__directive = currentDirective;\n    }\n  }\n  if (currentDirective !== undefined) {\n    value = resolveDirective(\n      part,\n      currentDirective._$resolve(part, (value as DirectiveResult).values),\n      currentDirective,\n      attributeIndex\n    );\n  }\n  return value;\n}\n\nexport type {TemplateInstance};\n/**\n * An updateable instance of a Template. Holds references to the Parts used to\n * update the template instance.\n */\nclass TemplateInstance implements Disconnectable {\n  _$template: Template;\n  _$parts: Array<Part | undefined> = [];\n\n  /** @internal */\n  _$parent: ChildPart;\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  constructor(template: Template, parent: ChildPart) {\n    this._$template = template;\n    this._$parent = parent;\n  }\n\n  // Called by ChildPart parentNode getter\n  get parentNode() {\n    return this._$parent.parentNode;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  // This method is separate from the constructor because we need to return a\n  // DocumentFragment and we don't want to hold onto it with an instance field.\n  _clone(options: RenderOptions | undefined) {\n    const {\n      el: {content},\n      parts: parts,\n    } = this._$template;\n    const fragment = (options?.creationScope ?? d).importNode(content, true);\n    walker.currentNode = fragment;\n\n    let node = walker.nextNode()!;\n    let nodeIndex = 0;\n    let partIndex = 0;\n    let templatePart = parts[0];\n\n    while (templatePart !== undefined) {\n      if (nodeIndex === templatePart.index) {\n        let part: Part | undefined;\n        if (templatePart.type === CHILD_PART) {\n          part = new ChildPart(\n            node as HTMLElement,\n            node.nextSibling,\n            this,\n            options\n          );\n        } else if (templatePart.type === ATTRIBUTE_PART) {\n          part = new templatePart.ctor(\n            node as HTMLElement,\n            templatePart.name,\n            templatePart.strings,\n            this,\n            options\n          );\n        } else if (templatePart.type === ELEMENT_PART) {\n          part = new ElementPart(node as HTMLElement, this, options);\n        }\n        this._$parts.push(part);\n        templatePart = parts[++partIndex];\n      }\n      if (nodeIndex !== templatePart?.index) {\n        node = walker.nextNode()!;\n        nodeIndex++;\n      }\n    }\n    // We need to set the currentNode away from the cloned tree so that we\n    // don't hold onto the tree even if the tree is detached and should be\n    // freed.\n    walker.currentNode = d;\n    return fragment;\n  }\n\n  _update(values: Array<unknown>) {\n    let i = 0;\n    for (const part of this._$parts) {\n      if (part !== undefined) {\n        debugLogEvent &&\n          debugLogEvent({\n            kind: 'set part',\n            part,\n            value: values[i],\n            valueIndex: i,\n            values,\n            templateInstance: this,\n          });\n        if ((part as AttributePart).strings !== undefined) {\n          (part as AttributePart)._$setValue(values, part as AttributePart, i);\n          // The number of values the part consumes is part.strings.length - 1\n          // since values are in between template spans. We increment i by 1\n          // later in the loop, so increment it by part.strings.length - 2 here\n          i += (part as AttributePart).strings!.length - 2;\n        } else {\n          part._$setValue(values[i]);\n        }\n      }\n      i++;\n    }\n  }\n}\n\n/*\n * Parts\n */\ntype AttributeTemplatePart = {\n  readonly type: typeof ATTRIBUTE_PART;\n  readonly index: number;\n  readonly name: string;\n  readonly ctor: typeof AttributePart;\n  readonly strings: ReadonlyArray<string>;\n};\ntype ChildTemplatePart = {\n  readonly type: typeof CHILD_PART;\n  readonly index: number;\n};\ntype ElementTemplatePart = {\n  readonly type: typeof ELEMENT_PART;\n  readonly index: number;\n};\ntype CommentTemplatePart = {\n  readonly type: typeof COMMENT_PART;\n  readonly index: number;\n};\n\n/**\n * A TemplatePart represents a dynamic part in a template, before the template\n * is instantiated. When a template is instantiated Parts are created from\n * TemplateParts.\n */\ntype TemplatePart =\n  | ChildTemplatePart\n  | AttributeTemplatePart\n  | ElementTemplatePart\n  | CommentTemplatePart;\n\nexport type Part =\n  | ChildPart\n  | AttributePart\n  | PropertyPart\n  | BooleanAttributePart\n  | ElementPart\n  | EventPart;\n\nexport type {ChildPart};\nclass ChildPart implements Disconnectable {\n  readonly type = CHILD_PART;\n  readonly options: RenderOptions | undefined;\n  _$committedValue: unknown = nothing;\n  /** @internal */\n  __directive?: Directive;\n  /** @internal */\n  _$startNode: ChildNode;\n  /** @internal */\n  _$endNode: ChildNode | null;\n  private _textSanitizer: ValueSanitizer | undefined;\n  /** @internal */\n  _$parent: Disconnectable | undefined;\n  /**\n   * Connection state for RootParts only (i.e. ChildPart without _$parent\n   * returned from top-level `render`). This field is unused otherwise. The\n   * intention would be clearer if we made `RootPart` a subclass of `ChildPart`\n   * with this field (and a different _$isConnected getter), but the subclass\n   * caused a perf regression, possibly due to making call sites polymorphic.\n   * @internal\n   */\n  __isConnected: boolean;\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    // ChildParts that are not at the root should always be created with a\n    // parent; only RootChildNode's won't, so they return the local isConnected\n    // state\n    return this._$parent?._$isConnected ?? this.__isConnected;\n  }\n\n  // The following fields will be patched onto ChildParts when required by\n  // AsyncDirective\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n  /** @internal */\n  _$notifyConnectionChanged?(\n    isConnected: boolean,\n    removeFromParent?: boolean,\n    from?: number\n  ): void;\n  /** @internal */\n  _$reparentDisconnectables?(parent: Disconnectable): void;\n\n  constructor(\n    startNode: ChildNode,\n    endNode: ChildNode | null,\n    parent: TemplateInstance | ChildPart | undefined,\n    options: RenderOptions | undefined\n  ) {\n    this._$startNode = startNode;\n    this._$endNode = endNode;\n    this._$parent = parent;\n    this.options = options;\n    // Note __isConnected is only ever accessed on RootParts (i.e. when there is\n    // no _$parent); the value on a non-root-part is \"don't care\", but checking\n    // for parent would be more code\n    this.__isConnected = options?.isConnected ?? true;\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      // Explicitly initialize for consistent class shape.\n      this._textSanitizer = undefined;\n    }\n  }\n\n  /**\n   * The parent node into which the part renders its content.\n   *\n   * A ChildPart's content consists of a range of adjacent child nodes of\n   * `.parentNode`, possibly bordered by 'marker nodes' (`.startNode` and\n   * `.endNode`).\n   *\n   * - If both `.startNode` and `.endNode` are non-null, then the part's content\n   * consists of all siblings between `.startNode` and `.endNode`, exclusively.\n   *\n   * - If `.startNode` is non-null but `.endNode` is null, then the part's\n   * content consists of all siblings following `.startNode`, up to and\n   * including the last child of `.parentNode`. If `.endNode` is non-null, then\n   * `.startNode` will always be non-null.\n   *\n   * - If both `.endNode` and `.startNode` are null, then the part's content\n   * consists of all child nodes of `.parentNode`.\n   */\n  get parentNode(): Node {\n    let parentNode: Node = wrap(this._$startNode).parentNode!;\n    const parent = this._$parent;\n    if (\n      parent !== undefined &&\n      parentNode?.nodeType === 11 /* Node.DOCUMENT_FRAGMENT */\n    ) {\n      // If the parentNode is a DocumentFragment, it may be because the DOM is\n      // still in the cloned fragment during initial render; if so, get the real\n      // parentNode the part will be committed into by asking the parent.\n      parentNode = (parent as ChildPart | TemplateInstance).parentNode;\n    }\n    return parentNode;\n  }\n\n  /**\n   * The part's leading marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get startNode(): Node | null {\n    return this._$startNode;\n  }\n\n  /**\n   * The part's trailing marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get endNode(): Node | null {\n    return this._$endNode;\n  }\n\n  _$setValue(value: unknown, directiveParent: DirectiveParent = this): void {\n    if (DEV_MODE && this.parentNode === null) {\n      throw new Error(\n        `This \\`ChildPart\\` has no \\`parentNode\\` and therefore cannot accept a value. This likely means the element containing the part was manipulated in an unsupported way outside of Lit's control such that the part's marker nodes were ejected from DOM. For example, setting the element's \\`innerHTML\\` or \\`textContent\\` can do this.`\n      );\n    }\n    value = resolveDirective(this, value, directiveParent);\n    if (isPrimitive(value)) {\n      // Non-rendering child values. It's important that these do not render\n      // empty text nodes to avoid issues with preventing default <slot>\n      // fallback content.\n      if (value === nothing || value == null || value === '') {\n        if (this._$committedValue !== nothing) {\n          debugLogEvent &&\n            debugLogEvent({\n              kind: 'commit nothing to child',\n              start: this._$startNode,\n              end: this._$endNode,\n              parent: this._$parent,\n              options: this.options,\n            });\n          this._$clear();\n        }\n        this._$committedValue = nothing;\n      } else if (value !== this._$committedValue && value !== noChange) {\n        this._commitText(value);\n      }\n      // This property needs to remain unminified.\n    } else if ((value as TemplateResult)['_$litType$'] !== undefined) {\n      this._commitTemplateResult(value as TemplateResult);\n    } else if ((value as Node).nodeType !== undefined) {\n      if (DEV_MODE && this.options?.host === value) {\n        this._commitText(\n          `[probable mistake: rendered a template's host in itself ` +\n            `(commonly caused by writing \\${this} in a template]`\n        );\n        console.warn(\n          `Attempted to render the template host`,\n          value,\n          `inside itself. This is almost always a mistake, and in dev mode `,\n          `we render some warning text. In production however, we'll `,\n          `render it, which will usually result in an error, and sometimes `,\n          `in the element disappearing from the DOM.`\n        );\n        return;\n      }\n      this._commitNode(value as Node);\n    } else if (isIterable(value)) {\n      this._commitIterable(value);\n    } else {\n      // Fallback, will render the string representation\n      this._commitText(value);\n    }\n  }\n\n  private _insert<T extends Node>(node: T) {\n    return wrap(wrap(this._$startNode).parentNode!).insertBefore(\n      node,\n      this._$endNode\n    );\n  }\n\n  private _commitNode(value: Node): void {\n    if (this._$committedValue !== value) {\n      this._$clear();\n      if (\n        ENABLE_EXTRA_SECURITY_HOOKS &&\n        sanitizerFactoryInternal !== noopSanitizer\n      ) {\n        const parentNodeName = this._$startNode.parentNode?.nodeName;\n        if (parentNodeName === 'STYLE' || parentNodeName === 'SCRIPT') {\n          let message = 'Forbidden';\n          if (DEV_MODE) {\n            if (parentNodeName === 'STYLE') {\n              message =\n                `Lit does not support binding inside style nodes. ` +\n                `This is a security risk, as style injection attacks can ` +\n                `exfiltrate data and spoof UIs. ` +\n                `Consider instead using css\\`...\\` literals ` +\n                `to compose styles, and do dynamic styling with ` +\n                `css custom properties, ::parts, <slot>s, ` +\n                `and by mutating the DOM rather than stylesheets.`;\n            } else {\n              message =\n                `Lit does not support binding inside script nodes. ` +\n                `This is a security risk, as it could allow arbitrary ` +\n                `code execution.`;\n            }\n          }\n          throw new Error(message);\n        }\n      }\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'commit node',\n          start: this._$startNode,\n          parent: this._$parent,\n          value: value,\n          options: this.options,\n        });\n      this._$committedValue = this._insert(value);\n    }\n  }\n\n  private _commitText(value: unknown): void {\n    // If the committed value is a primitive it means we called _commitText on\n    // the previous render, and we know that this._$startNode.nextSibling is a\n    // Text node. We can now just replace the text content (.data) of the node.\n    if (\n      this._$committedValue !== nothing &&\n      isPrimitive(this._$committedValue)\n    ) {\n      const node = wrap(this._$startNode).nextSibling as Text;\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(node, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n      }\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'commit text',\n          node,\n          value,\n          options: this.options,\n        });\n      (node as Text).data = value as string;\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        const textNode = d.createTextNode('');\n        this._commitNode(textNode);\n        // When setting text content, for security purposes it matters a lot\n        // what the parent is. For example, <style> and <script> need to be\n        // handled with care, while <span> does not. So first we need to put a\n        // text node into the document, then we can sanitize its content.\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(textNode, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n        debugLogEvent &&\n          debugLogEvent({\n            kind: 'commit text',\n            node: textNode,\n            value,\n            options: this.options,\n          });\n        textNode.data = value as string;\n      } else {\n        this._commitNode(d.createTextNode(value as string));\n        debugLogEvent &&\n          debugLogEvent({\n            kind: 'commit text',\n            node: wrap(this._$startNode).nextSibling as Text,\n            value,\n            options: this.options,\n          });\n      }\n    }\n    this._$committedValue = value;\n  }\n\n  private _commitTemplateResult(\n    result: TemplateResult | CompiledTemplateResult\n  ): void {\n    // This property needs to remain unminified.\n    const {values, ['_$litType$']: type} = result;\n    // If $litType$ is a number, result is a plain TemplateResult and we get\n    // the template from the template cache. If not, result is a\n    // CompiledTemplateResult and _$litType$ is a CompiledTemplate and we need\n    // to create the <template> element the first time we see it.\n    const template: Template | CompiledTemplate =\n      typeof type === 'number'\n        ? this._$getTemplate(result as UncompiledTemplateResult)\n        : (type.el === undefined &&\n            (type.el = Template.createElement(\n              trustFromTemplateString(type.h, type.h[0]),\n              this.options\n            )),\n          type);\n\n    if ((this._$committedValue as TemplateInstance)?._$template === template) {\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'template updating',\n          template,\n          instance: this._$committedValue as TemplateInstance,\n          parts: (this._$committedValue as TemplateInstance)._$parts,\n          options: this.options,\n          values,\n        });\n      (this._$committedValue as TemplateInstance)._update(values);\n    } else {\n      const instance = new TemplateInstance(template as Template, this);\n      const fragment = instance._clone(this.options);\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'template instantiated',\n          template,\n          instance,\n          parts: instance._$parts,\n          options: this.options,\n          fragment,\n          values,\n        });\n      instance._update(values);\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'template instantiated and updated',\n          template,\n          instance,\n          parts: instance._$parts,\n          options: this.options,\n          fragment,\n          values,\n        });\n      this._commitNode(fragment);\n      this._$committedValue = instance;\n    }\n  }\n\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @internal */\n  _$getTemplate(result: UncompiledTemplateResult) {\n    let template = templateCache.get(result.strings);\n    if (template === undefined) {\n      templateCache.set(result.strings, (template = new Template(result)));\n    }\n    return template;\n  }\n\n  private _commitIterable(value: Iterable<unknown>): void {\n    // For an Iterable, we create a new InstancePart per item, then set its\n    // value to the item. This is a little bit of overhead for every item in\n    // an Iterable, but it lets us recurse easily and efficiently update Arrays\n    // of TemplateResults that will be commonly returned from expressions like:\n    // array.map((i) => html`${i}`), by reusing existing TemplateInstances.\n\n    // If value is an array, then the previous render was of an\n    // iterable and value will contain the ChildParts from the previous\n    // render. If value is not an array, clear this part and make a new\n    // array for ChildParts.\n    if (!isArray(this._$committedValue)) {\n      this._$committedValue = [];\n      this._$clear();\n    }\n\n    // Lets us keep track of how many items we stamped so we can clear leftover\n    // items from a previous render\n    const itemParts = this._$committedValue as ChildPart[];\n    let partIndex = 0;\n    let itemPart: ChildPart | undefined;\n\n    for (const item of value) {\n      if (partIndex === itemParts.length) {\n        // If no existing part, create a new one\n        // TODO (justinfagnani): test perf impact of always creating two parts\n        // instead of sharing parts between nodes\n        // https://github.com/lit/lit/issues/1266\n        itemParts.push(\n          (itemPart = new ChildPart(\n            this._insert(createMarker()),\n            this._insert(createMarker()),\n            this,\n            this.options\n          ))\n        );\n      } else {\n        // Reuse an existing part\n        itemPart = itemParts[partIndex];\n      }\n      itemPart._$setValue(item);\n      partIndex++;\n    }\n\n    if (partIndex < itemParts.length) {\n      // itemParts always have end nodes\n      this._$clear(\n        itemPart && wrap(itemPart._$endNode!).nextSibling,\n        partIndex\n      );\n      // Truncate the parts array so _value reflects the current state\n      itemParts.length = partIndex;\n    }\n  }\n\n  /**\n   * Removes the nodes contained within this Part from the DOM.\n   *\n   * @param start Start node to clear from, for clearing a subset of the part's\n   *     DOM (used when truncating iterables)\n   * @param from  When `start` is specified, the index within the iterable from\n   *     which ChildParts are being removed, used for disconnecting directives\n   *     in those Parts.\n   *\n   * @internal\n   */\n  _$clear(\n    start: ChildNode | null = wrap(this._$startNode).nextSibling,\n    from?: number\n  ) {\n    this._$notifyConnectionChanged?.(false, true, from);\n    while (start !== this._$endNode) {\n      // The non-null assertion is safe because if _$startNode.nextSibling is\n      // null, then _$endNode is also null, and we would not have entered this\n      // loop.\n      const n = wrap(start!).nextSibling;\n      wrap(start!).remove();\n      start = n;\n    }\n  }\n\n  /**\n   * Implementation of RootPart's `isConnected`. Note that this method\n   * should only be called on `RootPart`s (the `ChildPart` returned from a\n   * top-level `render()` call). It has no effect on non-root ChildParts.\n   * @param isConnected Whether to set\n   * @internal\n   */\n  setConnected(isConnected: boolean) {\n    if (this._$parent === undefined) {\n      this.__isConnected = isConnected;\n      this._$notifyConnectionChanged?.(isConnected);\n    } else if (DEV_MODE) {\n      throw new Error(\n        'part.setConnected() may only be called on a ' +\n          'RootPart returned from render().'\n      );\n    }\n  }\n}\n\n/**\n * A top-level `ChildPart` returned from `render` that manages the connected\n * state of `AsyncDirective`s created throughout the tree below it.\n */\nexport interface RootPart extends ChildPart {\n  /**\n   * Sets the connection state for `AsyncDirective`s contained within this root\n   * ChildPart.\n   *\n   * lit-html does not automatically monitor the connectedness of DOM rendered;\n   * as such, it is the responsibility of the caller to `render` to ensure that\n   * `part.setConnected(false)` is called before the part object is potentially\n   * discarded, to ensure that `AsyncDirective`s have a chance to dispose of\n   * any resources being held. If a `RootPart` that was previously\n   * disconnected is subsequently re-connected (and its `AsyncDirective`s should\n   * re-connect), `setConnected(true)` should be called.\n   *\n   * @param isConnected Whether directives within this tree should be connected\n   * or not\n   */\n  setConnected(isConnected: boolean): void;\n}\n\nexport type {AttributePart};\nclass AttributePart implements Disconnectable {\n  readonly type:\n    | typeof ATTRIBUTE_PART\n    | typeof PROPERTY_PART\n    | typeof BOOLEAN_ATTRIBUTE_PART\n    | typeof EVENT_PART = ATTRIBUTE_PART;\n  readonly element: HTMLElement;\n  readonly name: string;\n  readonly options: RenderOptions | undefined;\n\n  /**\n   * If this attribute part represents an interpolation, this contains the\n   * static strings of the interpolation. For single-value, complete bindings,\n   * this is undefined.\n   */\n  readonly strings?: ReadonlyArray<string>;\n  /** @internal */\n  _$committedValue: unknown | Array<unknown> = nothing;\n  /** @internal */\n  __directives?: Array<Directive | undefined>;\n  /** @internal */\n  _$parent: Disconnectable;\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  protected _sanitizer: ValueSanitizer | undefined;\n\n  get tagName() {\n    return this.element.tagName;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  constructor(\n    element: HTMLElement,\n    name: string,\n    strings: ReadonlyArray<string>,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    this.element = element;\n    this.name = name;\n    this._$parent = parent;\n    this.options = options;\n    if (strings.length > 2 || strings[0] !== '' || strings[1] !== '') {\n      this._$committedValue = new Array(strings.length - 1).fill(new String());\n      this.strings = strings;\n    } else {\n      this._$committedValue = nothing;\n    }\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      this._sanitizer = undefined;\n    }\n  }\n\n  /**\n   * Sets the value of this part by resolving the value from possibly multiple\n   * values and static strings and committing it to the DOM.\n   * If this part is single-valued, `this._strings` will be undefined, and the\n   * method will be called with a single value argument. If this part is\n   * multi-value, `this._strings` will be defined, and the method is called\n   * with the value array of the part's owning TemplateInstance, and an offset\n   * into the value array from which the values should be read.\n   * This method is overloaded this way to eliminate short-lived array slices\n   * of the template instance values, and allow a fast-path for single-valued\n   * parts.\n   *\n   * @param value The part value, or an array of values for multi-valued parts\n   * @param valueIndex the index to start reading values from. `undefined` for\n   *   single-valued parts\n   * @param noCommit causes the part to not commit its value to the DOM. Used\n   *   in hydration to prime attribute parts with their first-rendered value,\n   *   but not set the attribute, and in SSR to no-op the DOM operation and\n   *   capture the value for serialization.\n   *\n   * @internal\n   */\n  _$setValue(\n    value: unknown | Array<unknown>,\n    directiveParent: DirectiveParent = this,\n    valueIndex?: number,\n    noCommit?: boolean\n  ) {\n    const strings = this.strings;\n\n    // Whether any of the values has changed, for dirty-checking\n    let change = false;\n\n    if (strings === undefined) {\n      // Single-value binding case\n      value = resolveDirective(this, value, directiveParent, 0);\n      change =\n        !isPrimitive(value) ||\n        (value !== this._$committedValue && value !== noChange);\n      if (change) {\n        this._$committedValue = value;\n      }\n    } else {\n      // Interpolation case\n      const values = value as Array<unknown>;\n      value = strings[0];\n\n      let i, v;\n      for (i = 0; i < strings.length - 1; i++) {\n        v = resolveDirective(this, values[valueIndex! + i], directiveParent, i);\n\n        if (v === noChange) {\n          // If the user-provided value is `noChange`, use the previous value\n          v = (this._$committedValue as Array<unknown>)[i];\n        }\n        change ||=\n          !isPrimitive(v) || v !== (this._$committedValue as Array<unknown>)[i];\n        if (v === nothing) {\n          value = nothing;\n        } else if (value !== nothing) {\n          value += (v ?? '') + strings[i + 1];\n        }\n        // We always record each value, even if one is `nothing`, for future\n        // change detection.\n        (this._$committedValue as Array<unknown>)[i] = v;\n      }\n    }\n    if (change && !noCommit) {\n      this._commitValue(value);\n    }\n  }\n\n  /** @internal */\n  _commitValue(value: unknown) {\n    if (value === nothing) {\n      (wrap(this.element) as Element).removeAttribute(this.name);\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._sanitizer === undefined) {\n          this._sanitizer = sanitizerFactoryInternal(\n            this.element,\n            this.name,\n            'attribute'\n          );\n        }\n        value = this._sanitizer(value ?? '');\n      }\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'commit attribute',\n          element: this.element,\n          name: this.name,\n          value,\n          options: this.options,\n        });\n      (wrap(this.element) as Element).setAttribute(\n        this.name,\n        (value ?? '') as string\n      );\n    }\n  }\n}\n\nexport type {PropertyPart};\nclass PropertyPart extends AttributePart {\n  override readonly type = PROPERTY_PART;\n\n  /** @internal */\n  override _commitValue(value: unknown) {\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      if (this._sanitizer === undefined) {\n        this._sanitizer = sanitizerFactoryInternal(\n          this.element,\n          this.name,\n          'property'\n        );\n      }\n      value = this._sanitizer(value);\n    }\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'commit property',\n        element: this.element,\n        name: this.name,\n        value,\n        options: this.options,\n      });\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (this.element as any)[this.name] = value === nothing ? undefined : value;\n  }\n}\n\nexport type {BooleanAttributePart};\nclass BooleanAttributePart extends AttributePart {\n  override readonly type = BOOLEAN_ATTRIBUTE_PART;\n\n  /** @internal */\n  override _commitValue(value: unknown) {\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'commit boolean attribute',\n        element: this.element,\n        name: this.name,\n        value: !!(value && value !== nothing),\n        options: this.options,\n      });\n    (wrap(this.element) as Element).toggleAttribute(\n      this.name,\n      !!value && value !== nothing\n    );\n  }\n}\n\ntype EventListenerWithOptions = EventListenerOrEventListenerObject &\n  Partial<AddEventListenerOptions>;\n\n/**\n * An AttributePart that manages an event listener via add/removeEventListener.\n *\n * This part works by adding itself as the event listener on an element, then\n * delegating to the value passed to it. This reduces the number of calls to\n * add/removeEventListener if the listener changes frequently, such as when an\n * inline function is used as a listener.\n *\n * Because event options are passed when adding listeners, we must take case\n * to add and remove the part as a listener when the event options change.\n */\nexport type {EventPart};\nclass EventPart extends AttributePart {\n  override readonly type = EVENT_PART;\n\n  constructor(\n    element: HTMLElement,\n    name: string,\n    strings: ReadonlyArray<string>,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    super(element, name, strings, parent, options);\n\n    if (DEV_MODE && this.strings !== undefined) {\n      throw new Error(\n        `A \\`<${element.localName}>\\` has a \\`@${name}=...\\` listener with ` +\n          'invalid content. Event listeners in templates must have exactly ' +\n          'one expression and no surrounding text.'\n      );\n    }\n  }\n\n  // EventPart does not use the base _$setValue/_resolveValue implementation\n  // since the dirty checking is more complex\n  /** @internal */\n  override _$setValue(\n    newListener: unknown,\n    directiveParent: DirectiveParent = this\n  ) {\n    newListener =\n      resolveDirective(this, newListener, directiveParent, 0) ?? nothing;\n    if (newListener === noChange) {\n      return;\n    }\n    const oldListener = this._$committedValue;\n\n    // If the new value is nothing or any options change we have to remove the\n    // part as a listener.\n    const shouldRemoveListener =\n      (newListener === nothing && oldListener !== nothing) ||\n      (newListener as EventListenerWithOptions).capture !==\n        (oldListener as EventListenerWithOptions).capture ||\n      (newListener as EventListenerWithOptions).once !==\n        (oldListener as EventListenerWithOptions).once ||\n      (newListener as EventListenerWithOptions).passive !==\n        (oldListener as EventListenerWithOptions).passive;\n\n    // If the new value is not nothing and we removed the listener, we have\n    // to add the part as a listener.\n    const shouldAddListener =\n      newListener !== nothing &&\n      (oldListener === nothing || shouldRemoveListener);\n\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'commit event listener',\n        element: this.element,\n        name: this.name,\n        value: newListener,\n        options: this.options,\n        removeListener: shouldRemoveListener,\n        addListener: shouldAddListener,\n        oldListener,\n      });\n    if (shouldRemoveListener) {\n      this.element.removeEventListener(\n        this.name,\n        this,\n        oldListener as EventListenerWithOptions\n      );\n    }\n    if (shouldAddListener) {\n      this.element.addEventListener(\n        this.name,\n        this,\n        newListener as EventListenerWithOptions\n      );\n    }\n    this._$committedValue = newListener;\n  }\n\n  handleEvent(event: Event) {\n    if (typeof this._$committedValue === 'function') {\n      this._$committedValue.call(this.options?.host ?? this.element, event);\n    } else {\n      (this._$committedValue as EventListenerObject).handleEvent(event);\n    }\n  }\n}\n\nexport type {ElementPart};\nclass ElementPart implements Disconnectable {\n  readonly type = ELEMENT_PART;\n\n  /** @internal */\n  __directive?: Directive;\n\n  // This is to ensure that every Part has a _$committedValue\n  _$committedValue: undefined;\n\n  /** @internal */\n  _$parent!: Disconnectable;\n\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  options: RenderOptions | undefined;\n\n  constructor(\n    public element: Element,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    this._$parent = parent;\n    this.options = options;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  _$setValue(value: unknown): void {\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'commit to element binding',\n        element: this.element,\n        value,\n        options: this.options,\n      });\n    resolveDirective(this, value);\n  }\n}\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-element, which re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LH = {\n  // Used in lit-ssr\n  _boundAttributeSuffix: boundAttributeSuffix,\n  _marker: marker,\n  _markerMatch: markerMatch,\n  _HTML_RESULT: HTML_RESULT,\n  _getTemplateHtml: getTemplateHtml,\n  // Used in tests and private-ssr-support\n  _TemplateInstance: TemplateInstance,\n  _isIterable: isIterable,\n  _resolveDirective: resolveDirective,\n  _ChildPart: ChildPart,\n  _AttributePart: AttributePart,\n  _BooleanAttributePart: BooleanAttributePart,\n  _EventPart: EventPart,\n  _PropertyPart: PropertyPart,\n  _ElementPart: ElementPart,\n};\n\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n  ? global.litHtmlPolyfillSupportDevMode\n  : global.litHtmlPolyfillSupport;\npolyfillSupport?.(Template, ChildPart);\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for lit-html usage.\n(global.litHtmlVersions ??= []).push('3.3.1');\nif (DEV_MODE && global.litHtmlVersions.length > 1) {\n  queueMicrotask(() => {\n    issueWarning!(\n      'multiple-versions',\n      `Multiple versions of Lit loaded. ` +\n        `Loading multiple versions is not recommended.`\n    );\n  });\n}\n\n/**\n * Renders a value, usually a lit-html TemplateResult, to the container.\n *\n * This example renders the text \"Hello, Zoe!\" inside a paragraph tag, appending\n * it to the container `document.body`.\n *\n * ```js\n * import {html, render} from 'lit';\n *\n * const name = \"Zoe\";\n * render(html`<p>Hello, ${name}!</p>`, document.body);\n * ```\n *\n * @param value Any [renderable\n *   value](https://lit.dev/docs/templates/expressions/#child-expressions),\n *   typically a {@linkcode TemplateResult} created by evaluating a template tag\n *   like {@linkcode html} or {@linkcode svg}.\n * @param container A DOM container to render to. The first render will append\n *   the rendered value to the container, and subsequent renders will\n *   efficiently update the rendered value if the same result type was\n *   previously rendered there.\n * @param options See {@linkcode RenderOptions} for options documentation.\n * @see\n * {@link https://lit.dev/docs/libraries/standalone-templates/#rendering-lit-html-templates| Rendering Lit HTML Templates}\n */\nexport const render = (\n  value: unknown,\n  container: HTMLElement | DocumentFragment,\n  options?: RenderOptions\n): RootPart => {\n  if (DEV_MODE && container == null) {\n    // Give a clearer error message than\n    //     Uncaught TypeError: Cannot read properties of null (reading\n    //     '_$litPart$')\n    // which reads like an internal Lit error.\n    throw new TypeError(`The container to render into may not be ${container}`);\n  }\n  const renderId = DEV_MODE ? debugLogRenderId++ : 0;\n  const partOwnerNode = options?.renderBefore ?? container;\n  // This property needs to remain unminified.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let part: ChildPart = (partOwnerNode as any)['_$litPart$'];\n  debugLogEvent &&\n    debugLogEvent({\n      kind: 'begin render',\n      id: renderId,\n      value,\n      container,\n      options,\n      part,\n    });\n  if (part === undefined) {\n    const endNode = options?.renderBefore ?? null;\n    // This property needs to remain unminified.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (partOwnerNode as any)['_$litPart$'] = part = new ChildPart(\n      container.insertBefore(createMarker(), endNode),\n      endNode,\n      undefined,\n      options ?? {}\n    );\n  }\n  part._$setValue(value);\n  debugLogEvent &&\n    debugLogEvent({\n      kind: 'end render',\n      id: renderId,\n      value,\n      container,\n      options,\n      part,\n    });\n  return part as RootPart;\n};\n\nif (ENABLE_EXTRA_SECURITY_HOOKS) {\n  render.setSanitizer = setSanitizer;\n  render.createSanitizer = createSanitizer;\n  if (DEV_MODE) {\n    render._testOnlyClearSanitizerFactoryDoNotCallOrElse =\n      _testOnlyClearSanitizerFactoryDoNotCallOrElse;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nconst NODE_MODE = false;\n\n// Allows minifiers to rename references to globalThis\nconst global = globalThis;\n\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nexport const supportsAdoptingStyleSheets: boolean =\n  global.ShadowRoot &&\n  (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) &&\n  'adoptedStyleSheets' in Document.prototype &&\n  'replace' in CSSStyleSheet.prototype;\n\n/**\n * A CSSResult or native CSSStyleSheet.\n *\n * In browsers that support constructible CSS style sheets, CSSStyleSheet\n * object can be used for styling along side CSSResult from the `css`\n * template tag.\n */\nexport type CSSResultOrNative = CSSResult | CSSStyleSheet;\n\nexport type CSSResultArray = Array<CSSResultOrNative | CSSResultArray>;\n\n/**\n * A single CSSResult, CSSStyleSheet, or an array or nested arrays of those.\n */\nexport type CSSResultGroup = CSSResultOrNative | CSSResultArray;\n\nconst constructionToken = Symbol();\n\nconst cssTagCache = new WeakMap<TemplateStringsArray, CSSStyleSheet>();\n\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nexport class CSSResult {\n  // This property needs to remain unminified.\n  ['_$cssResult$'] = true;\n  readonly cssText: string;\n  private _styleSheet?: CSSStyleSheet;\n  private _strings: TemplateStringsArray | undefined;\n\n  private constructor(\n    cssText: string,\n    strings: TemplateStringsArray | undefined,\n    safeToken: symbol\n  ) {\n    if (safeToken !== constructionToken) {\n      throw new Error(\n        'CSSResult is not constructable. Use `unsafeCSS` or `css` instead.'\n      );\n    }\n    this.cssText = cssText;\n    this._strings = strings;\n  }\n\n  // This is a getter so that it's lazy. In practice, this means stylesheets\n  // are not created until the first element instance is made.\n  get styleSheet(): CSSStyleSheet | undefined {\n    // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n    // constructable.\n    let styleSheet = this._styleSheet;\n    const strings = this._strings;\n    if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n      const cacheable = strings !== undefined && strings.length === 1;\n      if (cacheable) {\n        styleSheet = cssTagCache.get(strings);\n      }\n      if (styleSheet === undefined) {\n        (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(\n          this.cssText\n        );\n        if (cacheable) {\n          cssTagCache.set(strings, styleSheet);\n        }\n      }\n    }\n    return styleSheet;\n  }\n\n  toString(): string {\n    return this.cssText;\n  }\n}\n\ntype ConstructableCSSResult = CSSResult & {\n  new (\n    cssText: string,\n    strings: TemplateStringsArray | undefined,\n    safeToken: symbol\n  ): CSSResult;\n};\n\nconst textFromCSSResult = (value: CSSResultGroup | number) => {\n  // This property needs to remain unminified.\n  if ((value as CSSResult)['_$cssResult$'] === true) {\n    return (value as CSSResult).cssText;\n  } else if (typeof value === 'number') {\n    return value;\n  } else {\n    throw new Error(\n      `Value passed to 'css' function must be a 'css' function result: ` +\n        `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` +\n        `to ensure page security.`\n    );\n  }\n};\n\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nexport const unsafeCSS = (value: unknown) =>\n  new (CSSResult as ConstructableCSSResult)(\n    typeof value === 'string' ? value : String(value),\n    undefined,\n    constructionToken\n  );\n\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nexport const css = (\n  strings: TemplateStringsArray,\n  ...values: (CSSResultGroup | number)[]\n): CSSResult => {\n  const cssText =\n    strings.length === 1\n      ? strings[0]\n      : values.reduce(\n          (acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1],\n          strings[0]\n        );\n  return new (CSSResult as ConstructableCSSResult)(\n    cssText,\n    strings,\n    constructionToken\n  );\n};\n\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic the native feature](https://developer.mozilla.org/en-US/docs/Web/API/ShadowRoot/adoptedStyleSheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nexport const adoptStyles = (\n  renderRoot: ShadowRoot,\n  styles: Array<CSSResultOrNative>\n) => {\n  if (supportsAdoptingStyleSheets) {\n    (renderRoot as ShadowRoot).adoptedStyleSheets = styles.map((s) =>\n      s instanceof CSSStyleSheet ? s : s.styleSheet!\n    );\n  } else {\n    for (const s of styles) {\n      const style = document.createElement('style');\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const nonce = (global as any)['litNonce'];\n      if (nonce !== undefined) {\n        style.setAttribute('nonce', nonce);\n      }\n      style.textContent = (s as CSSResult).cssText;\n      renderRoot.appendChild(style);\n    }\n  }\n};\n\nconst cssResultFromStyleSheet = (sheet: CSSStyleSheet) => {\n  let cssText = '';\n  for (const rule of sheet.cssRules) {\n    cssText += rule.cssText;\n  }\n  return unsafeCSS(cssText);\n};\n\nexport const getCompatibleStyle =\n  supportsAdoptingStyleSheets ||\n  (NODE_MODE && global.CSSStyleSheet === undefined)\n    ? (s: CSSResultOrNative) => s\n    : (s: CSSResultOrNative) =>\n        s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Use this module if you want to create your own base class extending\n * {@link ReactiveElement}.\n * @packageDocumentation\n */\n\nimport {\n  getCompatibleStyle,\n  adoptStyles,\n  CSSResultGroup,\n  CSSResultOrNative,\n} from './css-tag.js';\nimport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\n// In the Node build, this import will be injected by Rollup:\n// import {HTMLElement, customElements} from '@lit-labs/ssr-dom-shim';\n\nexport * from './css-tag.js';\nexport type {\n  ReactiveController,\n  ReactiveControllerHost,\n} from './reactive-controller.js';\n\n/**\n * Removes the `readonly` modifier from properties in the union K.\n *\n * This is a safer way to cast a value to a type with a mutable version of a\n * readonly field, than casting to an interface with the field re-declared\n * because it preserves the type of all the fields and warns on typos.\n */\ntype Mutable<T, K extends keyof T> = Omit<T, K> & {\n  -readonly [P in keyof Pick<T, K>]: P extends K ? T[P] : never;\n};\n\n// TODO (justinfagnani): Add `hasOwn` here when we ship ES2022\nconst {\n  is,\n  defineProperty,\n  getOwnPropertyDescriptor,\n  getOwnPropertyNames,\n  getOwnPropertySymbols,\n  getPrototypeOf,\n} = Object;\n\nconst NODE_MODE = false;\n\n// Lets a minifier replace globalThis references with a minified name\nconst global = globalThis;\n\nif (NODE_MODE) {\n  global.customElements ??= customElements;\n}\n\nconst DEV_MODE = true;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nconst trustedTypes = (global as unknown as {trustedTypes?: {emptyScript: ''}})\n  .trustedTypes;\n\n// Temporary workaround for https://crbug.com/993268\n// Currently, any attribute starting with \"on\" is considered to be a\n// TrustedScript source. Such boolean attributes must be set to the equivalent\n// trusted emptyScript value.\nconst emptyStringForBooleanAttribute = trustedTypes\n  ? (trustedTypes.emptyScript as unknown as '')\n  : '';\n\nconst polyfillSupport = DEV_MODE\n  ? global.reactiveElementPolyfillSupportDevMode\n  : global.reactiveElementPolyfillSupport;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  global.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (\n      !global.litIssuedWarnings!.has(warning) &&\n      !global.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      global.litIssuedWarnings!.add(warning);\n    }\n  };\n\n  queueMicrotask(() => {\n    issueWarning(\n      'dev-mode',\n      `Lit is in dev mode. Not recommended for production!`\n    );\n\n    // Issue polyfill support warning.\n    if (global.ShadyDOM?.inUse && polyfillSupport === undefined) {\n      issueWarning(\n        'polyfill-support-missing',\n        `Shadow DOM is being polyfilled via \\`ShadyDOM\\` but ` +\n          `the \\`polyfill-support\\` module has not been loaded.`\n      );\n    }\n  });\n}\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace ReactiveUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry = Update;\n    export interface Update {\n      kind: 'update';\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: ReactiveUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<ReactiveUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = <P extends PropertyKey>(\n  prop: P,\n  _obj: unknown\n): P => prop;\n\n/**\n * Converts property values to and from attribute values.\n */\nexport interface ComplexAttributeConverter<Type = unknown, TypeHint = unknown> {\n  /**\n   * Called to convert an attribute value to a property\n   * value.\n   */\n  fromAttribute?(value: string | null, type?: TypeHint): Type;\n\n  /**\n   * Called to convert a property value to an attribute\n   * value.\n   *\n   * It returns unknown instead of string, to be compatible with\n   * https://github.com/WICG/trusted-types (and similar efforts).\n   */\n  toAttribute?(value: Type, type?: TypeHint): unknown;\n}\n\ntype AttributeConverter<Type = unknown, TypeHint = unknown> =\n  | ComplexAttributeConverter<Type>\n  | ((value: string | null, type?: TypeHint) => Type);\n\n/**\n * Defines options for a property accessor.\n */\nexport interface PropertyDeclaration<Type = unknown, TypeHint = unknown> {\n  /**\n   * When set to `true`, indicates the property is internal private state. The\n   * property should not be set by users. When using TypeScript, this property\n   * should be marked as `private` or `protected`, and it is also a common\n   * practice to use a leading `_` in the name. The property is not added to\n   * `observedAttributes`.\n   */\n  readonly state?: boolean;\n\n  /**\n   * Indicates how and whether the property becomes an observed attribute.\n   * If the value is `false`, the property is not added to `observedAttributes`.\n   * If true or absent, the lowercased property name is observed (e.g. `fooBar`\n   * becomes `foobar`). If a string, the string value is observed (e.g\n   * `attribute: 'foo-bar'`).\n   */\n  readonly attribute?: boolean | string;\n\n  /**\n   * Indicates the type of the property. This is used only as a hint for the\n   * `converter` to determine how to convert the attribute\n   * to/from a property.\n   */\n  readonly type?: TypeHint;\n\n  /**\n   * Indicates how to convert the attribute to/from a property. If this value\n   * is a function, it is used to convert the attribute value a the property\n   * value. If it's an object, it can have keys for `fromAttribute` and\n   * `toAttribute`. If no `toAttribute` function is provided and\n   * `reflect` is set to `true`, the property value is set directly to the\n   * attribute. A default `converter` is used if none is provided; it supports\n   * `Boolean`, `String`, `Number`, `Object`, and `Array`. Note,\n   * when a property changes and the converter is used to update the attribute,\n   * the property is never updated again as a result of the attribute changing,\n   * and vice versa.\n   */\n  readonly converter?: AttributeConverter<Type, TypeHint>;\n\n  /**\n   * Indicates if the property should reflect to an attribute.\n   * If `true`, when the property is set, the attribute is set using the\n   * attribute name determined according to the rules for the `attribute`\n   * property option and the value of the property converted using the rules\n   * from the `converter` property option.\n   */\n  readonly reflect?: boolean;\n\n  /**\n   * A function that indicates if a property should be considered changed when\n   * it is set. The function should take the `newValue` and `oldValue` and\n   * return `true` if an update should be requested.\n   */\n  hasChanged?(value: Type, oldValue: Type): boolean;\n\n  /**\n   * Indicates whether an accessor will be created for this property. By\n   * default, an accessor will be generated for this property that requests an\n   * update when set. If this flag is `true`, no accessor will be created, and\n   * it will be the user's responsibility to call\n   * `this.requestUpdate(propertyName, oldValue)` to request an update when\n   * the property changes.\n   */\n  readonly noAccessor?: boolean;\n\n  /**\n   * Whether this property is wrapping accessors. This is set by `@property`\n   * to control the initial value change and reflection logic.\n   *\n   * @internal\n   */\n  wrapped?: boolean;\n\n  /**\n   * When `true`, uses the initial value of the property as the default value,\n   * which changes how attributes are handled:\n   *  - The initial value does *not* reflect, even if the `reflect` option is `true`.\n   *    Subsequent changes to the property will reflect, even if they are equal to the\n   *     default value.\n   *  - When the attribute is removed, the property is set to the default value\n   *  - The initial value will not trigger an old value in the `changedProperties` map\n   *    argument to update lifecycle methods.\n   *\n   * When set, properties must be initialized, either with a field initializer, or an\n   * assignment in the constructor. Not initializing the property may lead to\n   * improper handling of subsequent property assignments.\n   *\n   * While this behavior is opt-in, most properties that reflect to attributes should\n   * use `useDefault: true` so that their initial values do not reflect.\n   */\n  useDefault?: boolean;\n}\n\n/**\n * Map of properties to PropertyDeclaration options. For each property an\n * accessor is made, and the property is processed according to the\n * PropertyDeclaration options.\n */\nexport interface PropertyDeclarations {\n  readonly [key: string]: PropertyDeclaration;\n}\n\ntype PropertyDeclarationMap = Map<PropertyKey, PropertyDeclaration>;\n\ntype AttributeMap = Map<string, PropertyKey>;\n\n/**\n * A Map of property keys to values.\n *\n * Takes an optional type parameter T, which when specified as a non-any,\n * non-unknown type, will make the Map more strongly-typed, associating the map\n * keys with their corresponding value type on T.\n *\n * Use `PropertyValues<this>` when overriding ReactiveElement.update() and\n * other lifecycle methods in order to get stronger type-checking on keys\n * and values.\n */\n// This type is conditional so that if the parameter T is not specified, or\n// is `any`, the type will include `Map<PropertyKey, unknown>`. Since T is not\n// given in the uses of PropertyValues in this file, all uses here fallback to\n// meaning `Map<PropertyKey, unknown>`, but if a developer uses\n// `PropertyValues<this>` (or any other value for T) they will get a\n// strongly-typed Map type.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type PropertyValues<T = any> = T extends object\n  ? PropertyValueMap<T>\n  : Map<PropertyKey, unknown>;\n\n/**\n * Do not use, instead prefer {@linkcode PropertyValues}.\n */\n// This type must be exported such that JavaScript generated by the Google\n// Closure Compiler can import a type reference.\nexport interface PropertyValueMap<T> extends Map<PropertyKey, unknown> {\n  get<K extends keyof T>(k: K): T[K] | undefined;\n  set<K extends keyof T>(key: K, value: T[K]): this;\n  has<K extends keyof T>(k: K): boolean;\n  delete<K extends keyof T>(k: K): boolean;\n}\n\nexport const defaultConverter: ComplexAttributeConverter = {\n  toAttribute(value: unknown, type?: unknown): unknown {\n    switch (type) {\n      case Boolean:\n        value = value ? emptyStringForBooleanAttribute : null;\n        break;\n      case Object:\n      case Array:\n        // if the value is `null` or `undefined` pass this through\n        // to allow removing/no change behavior.\n        value = value == null ? value : JSON.stringify(value);\n        break;\n    }\n    return value;\n  },\n\n  fromAttribute(value: string | null, type?: unknown) {\n    let fromValue: unknown = value;\n    switch (type) {\n      case Boolean:\n        fromValue = value !== null;\n        break;\n      case Number:\n        fromValue = value === null ? null : Number(value);\n        break;\n      case Object:\n      case Array:\n        // Do *not* generate exception when invalid JSON is set as elements\n        // don't normally complain on being mis-configured.\n        // TODO(sorvell): Do generate exception in *dev mode*.\n        try {\n          // Assert to adhere to Bazel's \"must type assert JSON parse\" rule.\n          fromValue = JSON.parse(value!) as unknown;\n        } catch (e) {\n          fromValue = null;\n        }\n        break;\n    }\n    return fromValue;\n  },\n};\n\nexport interface HasChanged {\n  (value: unknown, old: unknown): boolean;\n}\n\n/**\n * Change function that returns true if `value` is different from `oldValue`.\n * This method is used as the default for a property's `hasChanged` function.\n */\nexport const notEqual: HasChanged = (value: unknown, old: unknown): boolean =>\n  !is(value, old);\n\nconst defaultPropertyDeclaration: PropertyDeclaration = {\n  attribute: true,\n  type: String,\n  converter: defaultConverter,\n  reflect: false,\n  useDefault: false,\n  hasChanged: notEqual,\n};\n\n/**\n * A string representing one of the supported dev mode warning categories.\n */\nexport type WarningKind =\n  | 'change-in-update'\n  | 'migration'\n  | 'async-perform-update';\n\nexport type Initializer = (element: ReactiveElement) => void;\n\n// Temporary, until google3 is on TypeScript 5.2\ndeclare global {\n  interface SymbolConstructor {\n    readonly metadata: unique symbol;\n  }\n}\n\n// Ensure metadata is enabled. TypeScript does not polyfill\n// Symbol.metadata, so we must ensure that it exists.\n(Symbol as {metadata: symbol}).metadata ??= Symbol('metadata');\n\ndeclare global {\n  // This is public global API, do not change!\n  // eslint-disable-next-line no-var\n  var litPropertyMetadata: WeakMap<\n    object,\n    Map<PropertyKey, PropertyDeclaration>\n  >;\n}\n\n// Map from a class's metadata object to property options\n// Note that we must use nullish-coalescing assignment so that we only use one\n// map even if we load multiple version of this module.\nglobal.litPropertyMetadata ??= new WeakMap<\n  object,\n  Map<PropertyKey, PropertyDeclaration>\n>();\n\n/**\n * Base element class which manages element properties and attributes. When\n * properties change, the `update` method is asynchronously called. This method\n * should be supplied by subclasses to render updates as desired.\n * @noInheritDoc\n */\nexport abstract class ReactiveElement\n  // In the Node build, this `extends` clause will be substituted with\n  // `(globalThis.HTMLElement ?? HTMLElement)`.\n  //\n  // This way, we will first prefer any global `HTMLElement` polyfill that the\n  // user has assigned, and then fall back to the `HTMLElement` shim which has\n  // been imported (see note at the top of this file about how this import is\n  // generated by Rollup). Note that the `HTMLElement` variable has been\n  // shadowed by this import, so it no longer refers to the global.\n  extends HTMLElement\n  implements ReactiveControllerHost\n{\n  // Note: these are patched in only in DEV_MODE.\n  /**\n   * Read or set all the enabled warning categories for this class.\n   *\n   * This property is only used in development builds.\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enabledWarnings?: WarningKind[];\n\n  /**\n   * Enable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Enable for all ReactiveElement subclasses\n   * ReactiveElement.enableWarning?.('migration');\n   *\n   * // Enable for only MyElement and subclasses\n   * MyElement.enableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static enableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Disable the given warning category for this class.\n   *\n   * This method only exists in development builds, so it should be accessed\n   * with a guard like:\n   *\n   * ```ts\n   * // Disable for all ReactiveElement subclasses\n   * ReactiveElement.disableWarning?.('migration');\n   *\n   * // Disable for only MyElement and subclasses\n   * MyElement.disableWarning?.('migration');\n   * ```\n   *\n   * @nocollapse\n   * @category dev-mode\n   */\n  static disableWarning?: (warningKind: WarningKind) => void;\n\n  /**\n   * Adds an initializer function to the class that is called during instance\n   * construction.\n   *\n   * This is useful for code that runs against a `ReactiveElement`\n   * subclass, such as a decorator, that needs to do work for each\n   * instance, such as setting up a `ReactiveController`.\n   *\n   * ```ts\n   * const myDecorator = (target: typeof ReactiveElement, key: string) => {\n   *   target.addInitializer((instance: ReactiveElement) => {\n   *     // This is run during construction of the element\n   *     new MyController(instance);\n   *   });\n   * }\n   * ```\n   *\n   * Decorating a field will then cause each instance to run an initializer\n   * that adds a controller:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   @myDecorator foo;\n   * }\n   * ```\n   *\n   * Initializers are stored per-constructor. Adding an initializer to a\n   * subclass does not add it to a superclass. Since initializers are run in\n   * constructors, initializers will run in order of the class hierarchy,\n   * starting with superclasses and progressing to the instance's class.\n   *\n   * @nocollapse\n   */\n  static addInitializer(initializer: Initializer) {\n    this.__prepare();\n    (this._initializers ??= []).push(initializer);\n  }\n\n  static _initializers?: Initializer[];\n\n  /*\n   * Due to closure compiler ES6 compilation bugs, @nocollapse is required on\n   * all static methods and properties with initializers.  Reference:\n   * - https://github.com/google/closure-compiler/issues/1776\n   */\n\n  /**\n   * Maps attribute names to properties; for example `foobar` attribute to\n   * `fooBar` property. Created lazily on user subclasses when finalizing the\n   * class.\n   * @nocollapse\n   */\n  private static __attributeToPropertyMap: AttributeMap;\n\n  /**\n   * Marks class as having been finalized, which includes creating properties\n   * from `static properties`, but does *not* include all properties created\n   * from decorators.\n   * @nocollapse\n   */\n  protected static finalized: true | undefined;\n\n  /**\n   * Memoized list of all element properties, including any superclass\n   * properties. Created lazily on user subclasses when finalizing the class.\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static elementProperties: PropertyDeclarationMap;\n\n  /**\n   * User-supplied object that maps property names to `PropertyDeclaration`\n   * objects containing options for configuring reactive properties. When\n   * a reactive property is set the element will update and render.\n   *\n   * By default properties are public fields, and as such, they should be\n   * considered as primarily settable by element users, either via attribute or\n   * the property itself.\n   *\n   * Generally, properties that are changed by the element should be private or\n   * protected fields and should use the `state: true` option. Properties\n   * marked as `state` do not reflect from the corresponding attribute\n   *\n   * However, sometimes element code does need to set a public property. This\n   * should typically only be done in response to user interaction, and an event\n   * should be fired informing the user; for example, a checkbox sets its\n   * `checked` property when clicked and fires a `changed` event. Mutating\n   * public properties should typically not be done for non-primitive (object or\n   * array) properties. In other cases when an element needs to manage state, a\n   * private property set with the `state: true` option should be used. When\n   * needed, state properties can be initialized via public properties to\n   * facilitate complex interactions.\n   * @nocollapse\n   * @category properties\n   */\n  static properties: PropertyDeclarations;\n\n  /**\n   * Memoized list of all element styles.\n   * Created lazily on user subclasses when finalizing the class.\n   * @nocollapse\n   * @category styles\n   */\n  static elementStyles: Array<CSSResultOrNative> = [];\n\n  /**\n   * Array of styles to apply to the element. The styles should be defined\n   * using the {@linkcode css} tag function, via constructible stylesheets, or\n   * imported from native CSS module scripts.\n   *\n   * Note on Content Security Policy:\n   *\n   * Element styles are implemented with `<style>` tags when the browser doesn't\n   * support adopted StyleSheets. To use such `<style>` tags with the style-src\n   * CSP directive, the style-src value must either include 'unsafe-inline' or\n   * `nonce-<base64-value>` with `<base64-value>` replaced be a server-generated\n   * nonce.\n   *\n   * To provide a nonce to use on generated `<style>` elements, set\n   * `window.litNonce` to a server-generated nonce in your page's HTML, before\n   * loading application code:\n   *\n   * ```html\n   * <script>\n   *   // Generated and unique per request:\n   *   window.litNonce = 'a1b2c3d4';\n   * </script>\n   * ```\n   * @nocollapse\n   * @category styles\n   */\n  static styles?: CSSResultGroup;\n\n  /**\n   * Returns a list of attributes corresponding to the registered properties.\n   * @nocollapse\n   * @category attributes\n   */\n  static get observedAttributes() {\n    // Ensure we've created all properties\n    this.finalize();\n    // this.__attributeToPropertyMap is only undefined after finalize() in\n    // ReactiveElement itself. ReactiveElement.observedAttributes is only\n    // accessed with ReactiveElement as the receiver when a subclass or mixin\n    // calls super.observedAttributes\n    return (\n      this.__attributeToPropertyMap && [...this.__attributeToPropertyMap.keys()]\n    );\n  }\n\n  private __instanceProperties?: PropertyValues = undefined;\n\n  /**\n   * Creates a property accessor on the element prototype if one does not exist\n   * and stores a {@linkcode PropertyDeclaration} for the property with the\n   * given options. The property setter calls the property's `hasChanged`\n   * property option or uses a strict identity check to determine whether or not\n   * to request an update.\n   *\n   * This method may be overridden to customize properties; however,\n   * when doing so, it's important to call `super.createProperty` to ensure\n   * the property is setup correctly. This method calls\n   * `getPropertyDescriptor` internally to get a descriptor to install.\n   * To customize what properties do when they are get or set, override\n   * `getPropertyDescriptor`. To customize the options for a property,\n   * implement `createProperty` like this:\n   *\n   * ```ts\n   * static createProperty(name, options) {\n   *   options = Object.assign(options, {myOption: true});\n   *   super.createProperty(name, options);\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  static createProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration = defaultPropertyDeclaration\n  ) {\n    // If this is a state property, force the attribute to false.\n    if (options.state) {\n      (options as Mutable<PropertyDeclaration, 'attribute'>).attribute = false;\n    }\n    this.__prepare();\n    // Whether this property is wrapping accessors.\n    // Helps control the initial value change and reflection logic.\n    if (this.prototype.hasOwnProperty(name)) {\n      options = Object.create(options);\n      options.wrapped = true;\n    }\n    this.elementProperties.set(name, options);\n    if (!options.noAccessor) {\n      const key = DEV_MODE\n        ? // Use Symbol.for in dev mode to make it easier to maintain state\n          // when doing HMR.\n          Symbol.for(`${String(name)} (@property() cache)`)\n        : Symbol();\n      const descriptor = this.getPropertyDescriptor(name, key, options);\n      if (descriptor !== undefined) {\n        defineProperty(this.prototype, name, descriptor);\n      }\n    }\n  }\n\n  /**\n   * Returns a property descriptor to be defined on the given named property.\n   * If no descriptor is returned, the property will not become an accessor.\n   * For example,\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   static getPropertyDescriptor(name, key, options) {\n   *     const defaultDescriptor =\n   *         super.getPropertyDescriptor(name, key, options);\n   *     const setter = defaultDescriptor.set;\n   *     return {\n   *       get: defaultDescriptor.get,\n   *       set(value) {\n   *         setter.call(this, value);\n   *         // custom action.\n   *       },\n   *       configurable: true,\n   *       enumerable: true\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * @nocollapse\n   * @category properties\n   */\n  protected static getPropertyDescriptor(\n    name: PropertyKey,\n    key: string | symbol,\n    options: PropertyDeclaration\n  ): PropertyDescriptor | undefined {\n    const {get, set} = getOwnPropertyDescriptor(this.prototype, name) ?? {\n      get(this: ReactiveElement) {\n        return this[key as keyof typeof this];\n      },\n      set(this: ReactiveElement, v: unknown) {\n        (this as unknown as Record<string | symbol, unknown>)[key] = v;\n      },\n    };\n    if (DEV_MODE && get == null) {\n      if ('value' in (getOwnPropertyDescriptor(this.prototype, name) ?? {})) {\n        throw new Error(\n          `Field ${JSON.stringify(String(name))} on ` +\n            `${this.name} was declared as a reactive property ` +\n            `but it's actually declared as a value on the prototype. ` +\n            `Usually this is due to using @property or @state on a method.`\n        );\n      }\n      issueWarning(\n        'reactive-property-without-getter',\n        `Field ${JSON.stringify(String(name))} on ` +\n          `${this.name} was declared as a reactive property ` +\n          `but it does not have a getter. This will be an error in a ` +\n          `future version of Lit.`\n      );\n    }\n    return {\n      get,\n      set(this: ReactiveElement, value: unknown) {\n        const oldValue = get?.call(this);\n        set?.call(this, value);\n        this.requestUpdate(name, oldValue, options);\n      },\n      configurable: true,\n      enumerable: true,\n    };\n  }\n\n  /**\n   * Returns the property options associated with the given property.\n   * These options are defined with a `PropertyDeclaration` via the `properties`\n   * object or the `@property` decorator and are registered in\n   * `createProperty(...)`.\n   *\n   * Note, this method should be considered \"final\" and not overridden. To\n   * customize the options for a given property, override\n   * {@linkcode createProperty}.\n   *\n   * @nocollapse\n   * @final\n   * @category properties\n   */\n  static getPropertyOptions(name: PropertyKey) {\n    return this.elementProperties.get(name) ?? defaultPropertyDeclaration;\n  }\n\n  // Temporary, until google3 is on TypeScript 5.2\n  declare static [Symbol.metadata]: object & Record<PropertyKey, unknown>;\n\n  /**\n   * Initializes static own properties of the class used in bookkeeping\n   * for element properties, initializers, etc.\n   *\n   * Can be called multiple times by code that needs to ensure these\n   * properties exist before using them.\n   *\n   * This method ensures the superclass is finalized so that inherited\n   * property metadata can be copied down.\n   * @nocollapse\n   */\n  private static __prepare() {\n    if (\n      this.hasOwnProperty(JSCompiler_renameProperty('elementProperties', this))\n    ) {\n      // Already prepared\n      return;\n    }\n    // Finalize any superclasses\n    const superCtor = getPrototypeOf(this) as typeof ReactiveElement;\n    superCtor.finalize();\n\n    // Create own set of initializers for this class if any exist on the\n    // superclass and copy them down. Note, for a small perf boost, avoid\n    // creating initializers unless needed.\n    if (superCtor._initializers !== undefined) {\n      this._initializers = [...superCtor._initializers];\n    }\n    // Initialize elementProperties from the superclass\n    this.elementProperties = new Map(superCtor.elementProperties);\n  }\n\n  /**\n   * Finishes setting up the class so that it's ready to be registered\n   * as a custom element and instantiated.\n   *\n   * This method is called by the ReactiveElement.observedAttributes getter.\n   * If you override the observedAttributes getter, you must either call\n   * super.observedAttributes to trigger finalization, or call finalize()\n   * yourself.\n   *\n   * @nocollapse\n   */\n  protected static finalize() {\n    if (this.hasOwnProperty(JSCompiler_renameProperty('finalized', this))) {\n      return;\n    }\n    this.finalized = true;\n    this.__prepare();\n\n    // Create properties from the static properties block:\n    if (this.hasOwnProperty(JSCompiler_renameProperty('properties', this))) {\n      const props = this.properties;\n      const propKeys = [\n        ...getOwnPropertyNames(props),\n        ...getOwnPropertySymbols(props),\n      ] as Array<keyof typeof props>;\n      for (const p of propKeys) {\n        this.createProperty(p, props[p]);\n      }\n    }\n\n    // Create properties from standard decorator metadata:\n    const metadata = this[Symbol.metadata];\n    if (metadata !== null) {\n      const properties = litPropertyMetadata.get(metadata);\n      if (properties !== undefined) {\n        for (const [p, options] of properties) {\n          this.elementProperties.set(p, options);\n        }\n      }\n    }\n\n    // Create the attribute-to-property map\n    this.__attributeToPropertyMap = new Map();\n    for (const [p, options] of this.elementProperties) {\n      const attr = this.__attributeNameForProperty(p, options);\n      if (attr !== undefined) {\n        this.__attributeToPropertyMap.set(attr, p);\n      }\n    }\n\n    this.elementStyles = this.finalizeStyles(this.styles);\n\n    if (DEV_MODE) {\n      if (this.hasOwnProperty('createProperty')) {\n        issueWarning(\n          'no-override-create-property',\n          'Overriding ReactiveElement.createProperty() is deprecated. ' +\n            'The override will not be called with standard decorators'\n        );\n      }\n      if (this.hasOwnProperty('getPropertyDescriptor')) {\n        issueWarning(\n          'no-override-get-property-descriptor',\n          'Overriding ReactiveElement.getPropertyDescriptor() is deprecated. ' +\n            'The override will not be called with standard decorators'\n        );\n      }\n    }\n  }\n\n  /**\n   * Options used when calling `attachShadow`. Set this property to customize\n   * the options for the shadowRoot; for example, to create a closed\n   * shadowRoot: `{mode: 'closed'}`.\n   *\n   * Note, these options are used in `createRenderRoot`. If this method\n   * is customized, options should be respected if possible.\n   * @nocollapse\n   * @category rendering\n   */\n  static shadowRootOptions: ShadowRootInit = {mode: 'open'};\n\n  /**\n   * Takes the styles the user supplied via the `static styles` property and\n   * returns the array of styles to apply to the element.\n   * Override this method to integrate into a style management system.\n   *\n   * Styles are deduplicated preserving the _last_ instance in the list. This\n   * is a performance optimization to avoid duplicated styles that can occur\n   * especially when composing via subclassing. The last item is kept to try\n   * to preserve the cascade order with the assumption that it's most important\n   * that last added styles override previous styles.\n   *\n   * @nocollapse\n   * @category styles\n   */\n  protected static finalizeStyles(\n    styles?: CSSResultGroup\n  ): Array<CSSResultOrNative> {\n    const elementStyles = [];\n    if (Array.isArray(styles)) {\n      // Dedupe the flattened array in reverse order to preserve the last items.\n      // Casting to Array<unknown> works around TS error that\n      // appears to come from trying to flatten a type CSSResultArray.\n      const set = new Set((styles as Array<unknown>).flat(Infinity).reverse());\n      // Then preserve original order by adding the set items in reverse order.\n      for (const s of set) {\n        elementStyles.unshift(getCompatibleStyle(s as CSSResultOrNative));\n      }\n    } else if (styles !== undefined) {\n      elementStyles.push(getCompatibleStyle(styles));\n    }\n    return elementStyles;\n  }\n\n  /**\n   * Node or ShadowRoot into which element DOM should be rendered. Defaults\n   * to an open shadowRoot.\n   * @category rendering\n   */\n  readonly renderRoot!: HTMLElement | DocumentFragment;\n\n  /**\n   * Returns the property name for the given attribute `name`.\n   * @nocollapse\n   */\n  private static __attributeNameForProperty(\n    name: PropertyKey,\n    options: PropertyDeclaration\n  ) {\n    const attribute = options.attribute;\n    return attribute === false\n      ? undefined\n      : typeof attribute === 'string'\n        ? attribute\n        : typeof name === 'string'\n          ? name.toLowerCase()\n          : undefined;\n  }\n\n  // Initialize to an unresolved Promise so we can make sure the element has\n  // connected before first update.\n  private __updatePromise!: Promise<boolean>;\n\n  /**\n   * True if there is a pending update as a result of calling `requestUpdate()`.\n   * Should only be read.\n   * @category updates\n   */\n  isUpdatePending = false;\n\n  /**\n   * Is set to `true` after the first update. The element code cannot assume\n   * that `renderRoot` exists before the element `hasUpdated`.\n   * @category updates\n   */\n  hasUpdated = false;\n\n  /**\n   * Map with keys for any properties that have changed since the last\n   * update cycle with previous values.\n   *\n   * @internal\n   */\n  _$changedProperties!: PropertyValues;\n\n  /**\n   * Records property default values when the\n   * `useDefault` option is used.\n   */\n  private __defaultValues?: Map<PropertyKey, unknown>;\n\n  /**\n   * Properties that should be reflected when updated.\n   */\n  private __reflectingProperties?: Set<PropertyKey>;\n\n  /**\n   * Name of currently reflecting property\n   */\n  private __reflectingProperty: PropertyKey | null = null;\n\n  /**\n   * Set of controllers.\n   */\n  private __controllers?: Set<ReactiveController>;\n\n  constructor() {\n    super();\n    this.__initialize();\n  }\n\n  /**\n   * Internal only override point for customizing work done when elements\n   * are constructed.\n   */\n  private __initialize() {\n    this.__updatePromise = new Promise<boolean>(\n      (res) => (this.enableUpdating = res)\n    );\n    this._$changedProperties = new Map();\n    // This enqueues a microtask that must run before the first update, so it\n    // must be called before requestUpdate()\n    this.__saveInstanceProperties();\n    // ensures first update will be caught by an early access of\n    // `updateComplete`\n    this.requestUpdate();\n    (this.constructor as typeof ReactiveElement)._initializers?.forEach((i) =>\n      i(this)\n    );\n  }\n\n  /**\n   * Registers a `ReactiveController` to participate in the element's reactive\n   * update cycle. The element automatically calls into any registered\n   * controllers during its lifecycle callbacks.\n   *\n   * If the element is connected when `addController()` is called, the\n   * controller's `hostConnected()` callback will be immediately called.\n   * @category controllers\n   */\n  addController(controller: ReactiveController) {\n    (this.__controllers ??= new Set()).add(controller);\n    // If a controller is added after the element has been connected,\n    // call hostConnected. Note, re-using existence of `renderRoot` here\n    // (which is set in connectedCallback) to avoid the need to track a\n    // first connected state.\n    if (this.renderRoot !== undefined && this.isConnected) {\n      controller.hostConnected?.();\n    }\n  }\n\n  /**\n   * Removes a `ReactiveController` from the element.\n   * @category controllers\n   */\n  removeController(controller: ReactiveController) {\n    this.__controllers?.delete(controller);\n  }\n\n  /**\n   * Fixes any properties set on the instance before upgrade time.\n   * Otherwise these would shadow the accessor and break these properties.\n   * The properties are stored in a Map which is played back after the\n   * constructor runs.\n   */\n  private __saveInstanceProperties() {\n    const instanceProperties = new Map<PropertyKey, unknown>();\n    const elementProperties = (this.constructor as typeof ReactiveElement)\n      .elementProperties;\n    for (const p of elementProperties.keys() as IterableIterator<keyof this>) {\n      if (this.hasOwnProperty(p)) {\n        instanceProperties.set(p, this[p]);\n        delete this[p];\n      }\n    }\n    if (instanceProperties.size > 0) {\n      this.__instanceProperties = instanceProperties;\n    }\n  }\n\n  /**\n   * Returns the node into which the element should render and by default\n   * creates and returns an open shadowRoot. Implement to customize where the\n   * element's DOM is rendered. For example, to render into the element's\n   * childNodes, return `this`.\n   *\n   * @return Returns a node into which to render.\n   * @category rendering\n   */\n  protected createRenderRoot(): HTMLElement | DocumentFragment {\n    const renderRoot =\n      this.shadowRoot ??\n      this.attachShadow(\n        (this.constructor as typeof ReactiveElement).shadowRootOptions\n      );\n    adoptStyles(\n      renderRoot,\n      (this.constructor as typeof ReactiveElement).elementStyles\n    );\n    return renderRoot;\n  }\n\n  /**\n   * On first connection, creates the element's renderRoot, sets up\n   * element styling, and enables updating.\n   * @category lifecycle\n   */\n  connectedCallback() {\n    // Create renderRoot before controllers `hostConnected`\n    (this as Mutable<typeof this, 'renderRoot'>).renderRoot ??=\n      this.createRenderRoot();\n    this.enableUpdating(true);\n    this.__controllers?.forEach((c) => c.hostConnected?.());\n  }\n\n  /**\n   * Note, this method should be considered final and not overridden. It is\n   * overridden on the element instance with a function that triggers the first\n   * update.\n   * @category updates\n   */\n  protected enableUpdating(_requestedUpdate: boolean) {}\n\n  /**\n   * Allows for `super.disconnectedCallback()` in extensions while\n   * reserving the possibility of making non-breaking feature additions\n   * when disconnecting at some point in the future.\n   * @category lifecycle\n   */\n  disconnectedCallback() {\n    this.__controllers?.forEach((c) => c.hostDisconnected?.());\n  }\n\n  /**\n   * Synchronizes property values when attributes change.\n   *\n   * Specifically, when an attribute is set, the corresponding property is set.\n   * You should rarely need to implement this callback. If this method is\n   * overridden, `super.attributeChangedCallback(name, _old, value)` must be\n   * called.\n   *\n   * See [responding to attribute changes](https://developer.mozilla.org/en-US/docs/Web/API/Web_components/Using_custom_elements#responding_to_attribute_changes)\n   * on MDN for more information about the `attributeChangedCallback`.\n   * @category attributes\n   */\n  attributeChangedCallback(\n    name: string,\n    _old: string | null,\n    value: string | null\n  ) {\n    this._$attributeToProperty(name, value);\n  }\n\n  private __propertyToAttribute(name: PropertyKey, value: unknown) {\n    const elemProperties: PropertyDeclarationMap = (\n      this.constructor as typeof ReactiveElement\n    ).elementProperties;\n    const options = elemProperties.get(name)!;\n    const attr = (\n      this.constructor as typeof ReactiveElement\n    ).__attributeNameForProperty(name, options);\n    if (attr !== undefined && options.reflect === true) {\n      const converter =\n        (options.converter as ComplexAttributeConverter)?.toAttribute !==\n        undefined\n          ? (options.converter as ComplexAttributeConverter)\n          : defaultConverter;\n      const attrValue = converter.toAttribute!(value, options.type);\n      if (\n        DEV_MODE &&\n        (this.constructor as typeof ReactiveElement).enabledWarnings!.includes(\n          'migration'\n        ) &&\n        attrValue === undefined\n      ) {\n        issueWarning(\n          'undefined-attribute-value',\n          `The attribute value for the ${name as string} property is ` +\n            `undefined on element ${this.localName}. The attribute will be ` +\n            `removed, but in the previous version of \\`ReactiveElement\\`, ` +\n            `the attribute would not have changed.`\n        );\n      }\n      // Track if the property is being reflected to avoid\n      // setting the property again via `attributeChangedCallback`. Note:\n      // 1. this takes advantage of the fact that the callback is synchronous.\n      // 2. will behave incorrectly if multiple attributes are in the reaction\n      // stack at time of calling. However, since we process attributes\n      // in `update` this should not be possible (or an extreme corner case\n      // that we'd like to discover).\n      // mark state reflecting\n      this.__reflectingProperty = name;\n      if (attrValue == null) {\n        this.removeAttribute(attr);\n      } else {\n        this.setAttribute(attr, attrValue as string);\n      }\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /** @internal */\n  _$attributeToProperty(name: string, value: string | null) {\n    const ctor = this.constructor as typeof ReactiveElement;\n    // Note, hint this as an `AttributeMap` so closure clearly understands\n    // the type; it has issues with tracking types through statics\n    const propName = (ctor.__attributeToPropertyMap as AttributeMap).get(name);\n    // Use tracking info to avoid reflecting a property value to an attribute\n    // if it was just set because the attribute changed.\n    if (propName !== undefined && this.__reflectingProperty !== propName) {\n      const options = ctor.getPropertyOptions(propName);\n      const converter =\n        typeof options.converter === 'function'\n          ? {fromAttribute: options.converter}\n          : options.converter?.fromAttribute !== undefined\n            ? options.converter\n            : defaultConverter;\n      // mark state reflecting\n      this.__reflectingProperty = propName;\n      const convertedValue = converter.fromAttribute!(value, options.type);\n      this[propName as keyof this] =\n        convertedValue ??\n        this.__defaultValues?.get(propName) ??\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (convertedValue as any);\n      // mark state not reflecting\n      this.__reflectingProperty = null;\n    }\n  }\n\n  /**\n   * Requests an update which is processed asynchronously. This should be called\n   * when an element should update based on some state not triggered by setting\n   * a reactive property. In this case, pass no arguments. It should also be\n   * called when manually implementing a property setter. In this case, pass the\n   * property `name` and `oldValue` to ensure that any configured property\n   * options are honored.\n   *\n   * @param name name of requesting property\n   * @param oldValue old value of requesting property\n   * @param options property options to use instead of the previously\n   *     configured options\n   * @category updates\n   */\n  requestUpdate(\n    name?: PropertyKey,\n    oldValue?: unknown,\n    options?: PropertyDeclaration\n  ): void {\n    // If we have a property key, perform property update steps.\n    if (name !== undefined) {\n      if (DEV_MODE && (name as unknown) instanceof Event) {\n        issueWarning(\n          ``,\n          `The requestUpdate() method was called with an Event as the property name. This is probably a mistake caused by binding this.requestUpdate as an event listener. Instead bind a function that will call it with no arguments: () => this.requestUpdate()`\n        );\n      }\n      const ctor = this.constructor as typeof ReactiveElement;\n      const newValue = this[name as keyof this];\n      options ??= ctor.getPropertyOptions(name);\n      const changed =\n        (options.hasChanged ?? notEqual)(newValue, oldValue) ||\n        // When there is no change, check a corner case that can occur when\n        // 1. there's a initial value which was not reflected\n        // 2. the property is subsequently set to this value.\n        // For example, `prop: {useDefault: true, reflect: true}`\n        // and el.prop = 'foo'. This should be considered a change if the\n        // attribute is not set because we will now reflect the property to the attribute.\n        (options.useDefault &&\n          options.reflect &&\n          newValue === this.__defaultValues?.get(name) &&\n          !this.hasAttribute(ctor.__attributeNameForProperty(name, options)!));\n      if (changed) {\n        this._$changeProperty(name, oldValue, options);\n      } else {\n        // Abort the request if the property should not be considered changed.\n        return;\n      }\n    }\n    if (this.isUpdatePending === false) {\n      this.__updatePromise = this.__enqueueUpdate();\n    }\n  }\n\n  /**\n   * @internal\n   */\n  _$changeProperty(\n    name: PropertyKey,\n    oldValue: unknown,\n    {useDefault, reflect, wrapped}: PropertyDeclaration,\n    initializeValue?: unknown\n  ) {\n    // Record default value when useDefault is used. This allows us to\n    // restore this value when the attribute is removed.\n    if (useDefault && !(this.__defaultValues ??= new Map()).has(name)) {\n      this.__defaultValues.set(\n        name,\n        initializeValue ?? oldValue ?? this[name as keyof this]\n      );\n      // if this is not wrapping an accessor, it must be an initial setting\n      // and in this case we do not want to record the change or reflect.\n      if (wrapped !== true || initializeValue !== undefined) {\n        return;\n      }\n    }\n    // TODO (justinfagnani): Create a benchmark of Map.has() + Map.set(\n    // vs just Map.set()\n    if (!this._$changedProperties.has(name)) {\n      // On the initial change, the old value should be `undefined`, except\n      // with `useDefault`\n      if (!this.hasUpdated && !useDefault) {\n        oldValue = undefined;\n      }\n      this._$changedProperties.set(name, oldValue);\n    }\n    // Add to reflecting properties set.\n    // Note, it's important that every change has a chance to add the\n    // property to `__reflectingProperties`. This ensures setting\n    // attribute + property reflects correctly.\n    if (reflect === true && this.__reflectingProperty !== name) {\n      (this.__reflectingProperties ??= new Set<PropertyKey>()).add(name);\n    }\n  }\n\n  /**\n   * Sets up the element to asynchronously update.\n   */\n  private async __enqueueUpdate() {\n    this.isUpdatePending = true;\n    try {\n      // Ensure any previous update has resolved before updating.\n      // This `await` also ensures that property changes are batched.\n      await this.__updatePromise;\n    } catch (e) {\n      // Refire any previous errors async so they do not disrupt the update\n      // cycle. Errors are refired so developers have a chance to observe\n      // them, and this can be done by implementing\n      // `window.onunhandledrejection`.\n      Promise.reject(e);\n    }\n    const result = this.scheduleUpdate();\n    // If `scheduleUpdate` returns a Promise, we await it. This is done to\n    // enable coordinating updates with a scheduler. Note, the result is\n    // checked to avoid delaying an additional microtask unless we need to.\n    if (result != null) {\n      await result;\n    }\n    return !this.isUpdatePending;\n  }\n\n  /**\n   * Schedules an element update. You can override this method to change the\n   * timing of updates by returning a Promise. The update will await the\n   * returned Promise, and you should resolve the Promise to allow the update\n   * to proceed. If this method is overridden, `super.scheduleUpdate()`\n   * must be called.\n   *\n   * For instance, to schedule updates to occur just before the next frame:\n   *\n   * ```ts\n   * override protected async scheduleUpdate(): Promise<unknown> {\n   *   await new Promise((resolve) => requestAnimationFrame(() => resolve()));\n   *   super.scheduleUpdate();\n   * }\n   * ```\n   * @category updates\n   */\n  protected scheduleUpdate(): void | Promise<unknown> {\n    const result = this.performUpdate();\n    if (\n      DEV_MODE &&\n      (this.constructor as typeof ReactiveElement).enabledWarnings!.includes(\n        'async-perform-update'\n      ) &&\n      typeof (result as unknown as Promise<unknown> | undefined)?.then ===\n        'function'\n    ) {\n      issueWarning(\n        'async-perform-update',\n        `Element ${this.localName} returned a Promise from performUpdate(). ` +\n          `This behavior is deprecated and will be removed in a future ` +\n          `version of ReactiveElement.`\n      );\n    }\n    return result;\n  }\n\n  /**\n   * Performs an element update. Note, if an exception is thrown during the\n   * update, `firstUpdated` and `updated` will not be called.\n   *\n   * Call `performUpdate()` to immediately process a pending update. This should\n   * generally not be needed, but it can be done in rare cases when you need to\n   * update synchronously.\n   *\n   * @category updates\n   */\n  protected performUpdate(): void {\n    // Abort any update if one is not pending when this is called.\n    // This can happen if `performUpdate` is called early to \"flush\"\n    // the update.\n    if (!this.isUpdatePending) {\n      return;\n    }\n    debugLogEvent?.({kind: 'update'});\n    if (!this.hasUpdated) {\n      // Create renderRoot before first update. This occurs in `connectedCallback`\n      // but is done here to support out of tree calls to `enableUpdating`/`performUpdate`.\n      (this as Mutable<typeof this, 'renderRoot'>).renderRoot ??=\n        this.createRenderRoot();\n      if (DEV_MODE) {\n        // Produce warning if any reactive properties on the prototype are\n        // shadowed by class fields. Instance fields set before upgrade are\n        // deleted by this point, so any own property is caused by class field\n        // initialization in the constructor.\n        const ctor = this.constructor as typeof ReactiveElement;\n        const shadowedProperties = [...ctor.elementProperties.keys()].filter(\n          (p) => this.hasOwnProperty(p) && p in getPrototypeOf(this)\n        );\n        if (shadowedProperties.length) {\n          throw new Error(\n            `The following properties on element ${this.localName} will not ` +\n              `trigger updates as expected because they are set using class ` +\n              `fields: ${shadowedProperties.join(', ')}. ` +\n              `Native class fields and some compiled output will overwrite ` +\n              `accessors used for detecting changes. See ` +\n              `https://lit.dev/msg/class-field-shadowing ` +\n              `for more information.`\n          );\n        }\n      }\n      // Mixin instance properties once, if they exist.\n      if (this.__instanceProperties) {\n        // TODO (justinfagnani): should we use the stored value? Could a new value\n        // have been set since we stored the own property value?\n        for (const [p, value] of this.__instanceProperties) {\n          this[p as keyof this] = value as this[keyof this];\n        }\n        this.__instanceProperties = undefined;\n      }\n      // Trigger initial value reflection and populate the initial\n      // `changedProperties` map, but only for the case of properties created\n      // via `createProperty` on accessors, which will not have already\n      // populated the `changedProperties` map since they are not set.\n      // We can't know if these accessors had initializers, so we just set\n      // them anyway - a difference from experimental decorators on fields and\n      // standard decorators on auto-accessors.\n      // For context see:\n      // https://github.com/lit/lit/pull/4183#issuecomment-1711959635\n      const elementProperties = (this.constructor as typeof ReactiveElement)\n        .elementProperties;\n      if (elementProperties.size > 0) {\n        for (const [p, options] of elementProperties) {\n          const {wrapped} = options;\n          const value = this[p as keyof this];\n          if (\n            wrapped === true &&\n            !this._$changedProperties.has(p) &&\n            value !== undefined\n          ) {\n            this._$changeProperty(p, undefined, options, value);\n          }\n        }\n      }\n    }\n    let shouldUpdate = false;\n    const changedProperties = this._$changedProperties;\n    try {\n      shouldUpdate = this.shouldUpdate(changedProperties);\n      if (shouldUpdate) {\n        this.willUpdate(changedProperties);\n        this.__controllers?.forEach((c) => c.hostUpdate?.());\n        this.update(changedProperties);\n      } else {\n        this.__markUpdated();\n      }\n    } catch (e) {\n      // Prevent `firstUpdated` and `updated` from running when there's an\n      // update exception.\n      shouldUpdate = false;\n      // Ensure element can accept additional updates after an exception.\n      this.__markUpdated();\n      throw e;\n    }\n    // The update is no longer considered pending and further updates are now allowed.\n    if (shouldUpdate) {\n      this._$didUpdate(changedProperties);\n    }\n  }\n\n  /**\n   * Invoked before `update()` to compute values needed during the update.\n   *\n   * Implement `willUpdate` to compute property values that depend on other\n   * properties and are used in the rest of the update process.\n   *\n   * ```ts\n   * willUpdate(changedProperties) {\n   *   // only need to check changed properties for an expensive computation.\n   *   if (changedProperties.has('firstName') || changedProperties.has('lastName')) {\n   *     this.sha = computeSHA(`${this.firstName} ${this.lastName}`);\n   *   }\n   * }\n   *\n   * render() {\n   *   return html`SHA: ${this.sha}`;\n   * }\n   * ```\n   *\n   * @category updates\n   */\n  protected willUpdate(_changedProperties: PropertyValues): void {}\n\n  // Note, this is an override point for polyfill-support.\n  // @internal\n  _$didUpdate(changedProperties: PropertyValues) {\n    this.__controllers?.forEach((c) => c.hostUpdated?.());\n    if (!this.hasUpdated) {\n      this.hasUpdated = true;\n      this.firstUpdated(changedProperties);\n    }\n    this.updated(changedProperties);\n    if (\n      DEV_MODE &&\n      this.isUpdatePending &&\n      (this.constructor as typeof ReactiveElement).enabledWarnings!.includes(\n        'change-in-update'\n      )\n    ) {\n      issueWarning(\n        'change-in-update',\n        `Element ${this.localName} scheduled an update ` +\n          `(generally because a property was set) ` +\n          `after an update completed, causing a new update to be scheduled. ` +\n          `This is inefficient and should be avoided unless the next update ` +\n          `can only be scheduled as a side effect of the previous update.`\n      );\n    }\n  }\n\n  private __markUpdated() {\n    this._$changedProperties = new Map();\n    this.isUpdatePending = false;\n  }\n\n  /**\n   * Returns a Promise that resolves when the element has completed updating.\n   * The Promise value is a boolean that is `true` if the element completed the\n   * update without triggering another update. The Promise result is `false` if\n   * a property was set inside `updated()`. If the Promise is rejected, an\n   * exception was thrown during the update.\n   *\n   * To await additional asynchronous work, override the `getUpdateComplete`\n   * method. For example, it is sometimes useful to await a rendered element\n   * before fulfilling this Promise. To do this, first await\n   * `super.getUpdateComplete()`, then any subsequent state.\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  get updateComplete(): Promise<boolean> {\n    return this.getUpdateComplete();\n  }\n\n  /**\n   * Override point for the `updateComplete` promise.\n   *\n   * It is not safe to override the `updateComplete` getter directly due to a\n   * limitation in TypeScript which means it is not possible to call a\n   * superclass getter (e.g. `super.updateComplete.then(...)`) when the target\n   * language is ES5 (https://github.com/microsoft/TypeScript/issues/338).\n   * This method should be overridden instead. For example:\n   *\n   * ```ts\n   * class MyElement extends LitElement {\n   *   override async getUpdateComplete() {\n   *     const result = await super.getUpdateComplete();\n   *     await this._myChild.updateComplete;\n   *     return result;\n   *   }\n   * }\n   * ```\n   *\n   * @return A promise of a boolean that resolves to true if the update completed\n   *     without triggering another update.\n   * @category updates\n   */\n  protected getUpdateComplete(): Promise<boolean> {\n    return this.__updatePromise;\n  }\n\n  /**\n   * Controls whether or not `update()` should be called when the element requests\n   * an update. By default, this method always returns `true`, but this can be\n   * customized to control when to update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected shouldUpdate(_changedProperties: PropertyValues): boolean {\n    return true;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes.\n   * It can be overridden to render and keep updated element DOM.\n   * Setting properties inside this method will *not* trigger\n   * another update.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected update(_changedProperties: PropertyValues) {\n    // The forEach() expression will only run when __reflectingProperties is\n    // defined, and it returns undefined, setting __reflectingProperties to\n    // undefined\n    this.__reflectingProperties &&= this.__reflectingProperties.forEach((p) =>\n      this.__propertyToAttribute(p, this[p as keyof this])\n    ) as undefined;\n    this.__markUpdated();\n  }\n\n  /**\n   * Invoked whenever the element is updated. Implement to perform\n   * post-updating tasks via DOM APIs, for example, focusing an element.\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected updated(_changedProperties: PropertyValues) {}\n\n  /**\n   * Invoked when the element is first updated. Implement to perform one time\n   * work on the element after update.\n   *\n   * ```ts\n   * firstUpdated() {\n   *   this.renderRoot.getElementById('my-text-area').focus();\n   * }\n   * ```\n   *\n   * Setting properties inside this method will trigger the element to update\n   * again after this update cycle completes.\n   *\n   * @param _changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected firstUpdated(_changedProperties: PropertyValues) {}\n}\n// Assigned here to work around a jscompiler bug with static fields\n// when compiling to ES5.\n// https://github.com/google/closure-compiler/issues/3177\n(ReactiveElement as unknown as Record<string, unknown>)[\n  JSCompiler_renameProperty('elementProperties', ReactiveElement)\n] = new Map();\n(ReactiveElement as unknown as Record<string, unknown>)[\n  JSCompiler_renameProperty('finalized', ReactiveElement)\n] = new Map();\n\n// Apply polyfills if available\npolyfillSupport?.({ReactiveElement});\n\n// Dev mode warnings...\nif (DEV_MODE) {\n  // Default warning set.\n  ReactiveElement.enabledWarnings = [\n    'change-in-update',\n    'async-perform-update',\n  ];\n  const ensureOwnWarnings = function (ctor: typeof ReactiveElement) {\n    if (\n      !ctor.hasOwnProperty(JSCompiler_renameProperty('enabledWarnings', ctor))\n    ) {\n      ctor.enabledWarnings = ctor.enabledWarnings!.slice();\n    }\n  };\n  ReactiveElement.enableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    if (!this.enabledWarnings!.includes(warning)) {\n      this.enabledWarnings!.push(warning);\n    }\n  };\n  ReactiveElement.disableWarning = function (\n    this: typeof ReactiveElement,\n    warning: WarningKind\n  ) {\n    ensureOwnWarnings(this);\n    const i = this.enabledWarnings!.indexOf(warning);\n    if (i >= 0) {\n      this.enabledWarnings!.splice(i, 1);\n    }\n  };\n}\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for ReactiveElement usage.\n(global.reactiveElementVersions ??= []).push('2.1.1');\nif (DEV_MODE && global.reactiveElementVersions.length > 1) {\n  queueMicrotask(() => {\n    issueWarning!(\n      'multiple-versions',\n      `Multiple versions of Lit loaded. Loading multiple versions ` +\n        `is not recommended.`\n    );\n  });\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * The main LitElement module, which defines the {@linkcode LitElement} base\n * class and related APIs.\n *\n * LitElement components can define a template and a set of observed\n * properties. Changing an observed property triggers a re-render of the\n * element.\n *\n * Import {@linkcode LitElement} and {@linkcode html} from this module to\n * create a component:\n *\n *  ```js\n * import {LitElement, html} from 'lit-element';\n *\n * class MyElement extends LitElement {\n *\n *   // Declare observed properties\n *   static get properties() {\n *     return {\n *       adjective: {}\n *     }\n *   }\n *\n *   constructor() {\n *     this.adjective = 'awesome';\n *   }\n *\n *   // Define the element's template\n *   render() {\n *     return html`<p>your ${adjective} template here</p>`;\n *   }\n * }\n *\n * customElements.define('my-element', MyElement);\n * ```\n *\n * `LitElement` extends {@linkcode ReactiveElement} and adds lit-html\n * templating. The `ReactiveElement` class is provided for users that want to\n * build their own custom element base classes that don't use lit-html.\n *\n * @packageDocumentation\n */\nimport {PropertyValues, ReactiveElement} from '@lit/reactive-element';\nimport {render, RenderOptions, noChange, RootPart} from 'lit-html';\nexport * from '@lit/reactive-element';\nexport * from 'lit-html';\n\nimport {LitUnstable} from 'lit-html';\nimport {ReactiveUnstable} from '@lit/reactive-element';\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace Unstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry =\n      | LitUnstable.DebugLog.Entry\n      | ReactiveUnstable.DebugLog.Entry;\n  }\n}\n/*\n * When using Closure Compiler, JSCompiler_renameProperty(property, object) is\n * replaced at compile time by the munged name for object[property]. We cannot\n * alias this function, so we have to use a small shim that has the same\n * behavior when not compiling.\n */\n/*@__INLINE__*/\nconst JSCompiler_renameProperty = <P extends PropertyKey>(\n  prop: P,\n  _obj: unknown\n): P => prop;\n\nconst DEV_MODE = true;\n// Allows minifiers to rename references to globalThis\nconst global = globalThis;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  // Ensure warnings are issued only 1x, even if multiple versions of Lit\n  // are loaded.\n  global.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += ` See https://lit.dev/msg/${code} for more information.`;\n    if (\n      !global.litIssuedWarnings!.has(warning) &&\n      !global.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      global.litIssuedWarnings!.add(warning);\n    }\n  };\n}\n\n/**\n * Base element class that manages element properties and attributes, and\n * renders a lit-html template.\n *\n * To define a component, subclass `LitElement` and implement a\n * `render` method to provide the component's template. Define properties\n * using the {@linkcode LitElement.properties properties} property or the\n * {@linkcode property} decorator.\n */\nexport class LitElement extends ReactiveElement {\n  // This property needs to remain unminified.\n  static ['_$litElement$'] = true;\n\n  /**\n   * @category rendering\n   */\n  readonly renderOptions: RenderOptions = {host: this};\n\n  private __childPart: RootPart | undefined = undefined;\n\n  /**\n   * @category rendering\n   */\n  protected override createRenderRoot() {\n    const renderRoot = super.createRenderRoot();\n    // When adoptedStyleSheets are shimmed, they are inserted into the\n    // shadowRoot by createRenderRoot. Adjust the renderBefore node so that\n    // any styles in Lit content render before adoptedStyleSheets. This is\n    // important so that adoptedStyleSheets have precedence over styles in\n    // the shadowRoot.\n    this.renderOptions.renderBefore ??= renderRoot!.firstChild as ChildNode;\n    return renderRoot;\n  }\n\n  /**\n   * Updates the element. This method reflects property values to attributes\n   * and calls `render` to render DOM via lit-html. Setting properties inside\n   * this method will *not* trigger another update.\n   * @param changedProperties Map of changed properties with old values\n   * @category updates\n   */\n  protected override update(changedProperties: PropertyValues) {\n    // Setting properties in `render` should not trigger an update. Since\n    // updates are allowed after super.update, it's important to call `render`\n    // before that.\n    const value = this.render();\n    if (!this.hasUpdated) {\n      this.renderOptions.isConnected = this.isConnected;\n    }\n    super.update(changedProperties);\n    this.__childPart = render(value, this.renderRoot, this.renderOptions);\n  }\n\n  /**\n   * Invoked when the component is added to the document's DOM.\n   *\n   * In `connectedCallback()` you should setup tasks that should only occur when\n   * the element is connected to the document. The most common of these is\n   * adding event listeners to nodes external to the element, like a keydown\n   * event handler added to the window.\n   *\n   * ```ts\n   * connectedCallback() {\n   *   super.connectedCallback();\n   *   addEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * Typically, anything done in `connectedCallback()` should be undone when the\n   * element is disconnected, in `disconnectedCallback()`.\n   *\n   * @category lifecycle\n   */\n  override connectedCallback() {\n    super.connectedCallback();\n    this.__childPart?.setConnected(true);\n  }\n\n  /**\n   * Invoked when the component is removed from the document's DOM.\n   *\n   * This callback is the main signal to the element that it may no longer be\n   * used. `disconnectedCallback()` should ensure that nothing is holding a\n   * reference to the element (such as event listeners added to nodes external\n   * to the element), so that it is free to be garbage collected.\n   *\n   * ```ts\n   * disconnectedCallback() {\n   *   super.disconnectedCallback();\n   *   window.removeEventListener('keydown', this._handleKeydown);\n   * }\n   * ```\n   *\n   * An element may be re-connected after being disconnected.\n   *\n   * @category lifecycle\n   */\n  override disconnectedCallback() {\n    super.disconnectedCallback();\n    this.__childPart?.setConnected(false);\n  }\n\n  /**\n   * Invoked on each update to perform rendering tasks. This method may return\n   * any value renderable by lit-html's `ChildPart` - typically a\n   * `TemplateResult`. Setting properties inside this method will *not* trigger\n   * the element to update.\n   * @category rendering\n   */\n  protected render(): unknown {\n    return noChange;\n  }\n}\n\n/**\n * Ensure this class is marked as `finalized` as an optimization ensuring\n * it will not needlessly try to `finalize`.\n *\n * Note this property name is a string to prevent breaking Closure JS Compiler\n * optimizations. See @lit/reactive-element for more information.\n */\n(LitElement as unknown as Record<string, unknown>)[\n  JSCompiler_renameProperty('finalized', LitElement)\n] = true;\n\n// Install hydration if available\nglobal.litElementHydrateSupport?.({LitElement});\n\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n  ? global.litElementPolyfillSupportDevMode\n  : global.litElementPolyfillSupport;\npolyfillSupport?.({LitElement});\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports  mangled in the\n * client side code, we export a _$LE object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-html, since this module re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LE = {\n  _$attributeToProperty: (\n    el: LitElement,\n    name: string,\n    value: string | null\n  ) => {\n    // eslint-disable-next-line\n    (el as any)._$attributeToProperty(name, value);\n  },\n  // eslint-disable-next-line\n  _$changedProperties: (el: LitElement) => (el as any)._$changedProperties,\n};\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for LitElement usage.\n(global.litElementVersions ??= []).push('4.2.1');\nif (DEV_MODE && global.litElementVersions.length > 1) {\n  queueMicrotask(() => {\n    issueWarning!(\n      'multiple-versions',\n      `Multiple versions of Lit loaded. Loading multiple versions ` +\n        `is not recommended.`\n    );\n  });\n}\n"], "mappings": ";AAUA,IAAM,WAAW;AACjB,IAAM,8BAA8B;AACpC,IAAM,0BAA0B;AAChC,IAAM,YAAY;AAGlB,IAAM,SAAS;AAmLf,IAAM,gBAAgB,WAClB,CAAC,UAAqC;AACpC,QAAM,aAAc,OACjB;AACH,MAAI,CAAC,YAAY;AACf;EACF;AACA,SAAO,cACL,IAAI,YAAwC,aAAa;IACvD,QAAQ;GACT,CAAC;AAEN,IACA;AAIJ,IAAI,mBAAmB;AAEvB,IAAI;AAEJ,IAAI,UAAU;AACZ,SAAO,sBAAP,OAAO,oBAAsB,oBAAI,IAAG;AAOpC,iBAAe,CAAC,MAAc,YAAmB;AAC/C,eAAW,OACP,4BAA4B,IAAI,2BAChC;AACJ,QACE,CAAC,OAAO,kBAAmB,IAAI,OAAO,KACtC,CAAC,OAAO,kBAAmB,IAAI,IAAI,GACnC;AACA,cAAQ,KAAK,OAAO;AACpB,aAAO,kBAAmB,IAAI,OAAO;IACvC;EACF;AAEA,iBAAe,MAAK;AAClB,iBACE,YACA,qDAAqD;EAEzD,CAAC;AACH;AAEA,IAAM,OACJ,2BACA,OAAO,UAAU,SACjB,OAAO,UAAU,YAAY,OACxB,OAAO,SAAU,OAClB,CAAiB,SAAY;AAEnC,IAAM,eAAgB,OAAyC;AAU/D,IAAM,SAAS,eACX,aAAa,aAAa,YAAY;EACpC,YAAY,CAAC,MAAM;CACpB,IACD;AA0CJ,IAAM,mBAAmC,CAAC,UAAmB;AAC7D,IAAM,gBAAkC,CACtC,OACA,OACA,UACG;AAGL,IAAM,eAAe,CAAC,iBAAkC;AACtD,MAAI,CAAC,6BAA6B;AAChC;EACF;AACA,MAAI,6BAA6B,eAAe;AAC9C,UAAM,IAAI,MACR,qHAC8D;EAElE;AACA,6BAA2B;AAC7B;AAKA,IAAM,gDAAgD,MAAK;AACzD,6BAA2B;AAC7B;AAEA,IAAM,kBAAoC,CAAC,MAAM,MAAM,SAAQ;AAC7D,SAAO,yBAAyB,MAAM,MAAM,IAAI;AAClD;AAIA,IAAM,uBAAuB;AAM7B,IAAM,SAAS,OAAO,KAAK,OAAM,EAAG,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;AAGvD,IAAM,cAAc,MAAM;AAI1B,IAAM,aAAa,IAAI,WAAW;AAElC,IAAM,IACJ,aAAa,OAAO,aAAa,SAC5B;EACC,mBAAgB;AACd,WAAO,CAAA;EACT;IAEF;AAGN,IAAM,eAAe,MAAM,EAAE,cAAc,EAAE;AAI7C,IAAM,cAAc,CAAC,UACnB,UAAU,QAAS,OAAO,SAAS,YAAY,OAAO,SAAS;AACjE,IAAM,UAAU,MAAM;AACtB,IAAM,aAAa,CAAC,UAClB,QAAQ,KAAK;AAEb,OAAQ,QAAgB,OAAO,QAAQ,MAAM;AAE/C,IAAM,aAAa;;AACnB,IAAM,kBAAkB;;AACxB,IAAM,YAAY;AAgBlB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,WAAW;AACjB,IAAM,mBAAmB;AAEzB,IAAM,kBAAkB;AAIxB,IAAM,mBAAmB;AAwBzB,IAAM,cAAc,IAAI,OACtB,KAAK,UAAU,OAAO,SAAS,MAAM,UAAU,KAAK,UAAU,OAAO,eAAe,gBACpF,GAAG;AAEL,IAAM,eAAe;AACrB,IAAM,iBAAiB;AACvB,IAAM,oBAAoB;AAC1B,IAAM,aAAa;AAEnB,IAAM,0BAA0B;AAChC,IAAM,0BAA0B;AAOhC,IAAM,iBAAiB;AAGvB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,gBAAgB;AAMtB,IAAM,iBAAiB;AACvB,IAAM,aAAa;AACnB,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,eAAe;AAwFrB,IAAM,MACJ,CAAuB,SACvB,CAAC,YAAkC,WAAwC;AAIzE,MAAI,YAAY,QAAQ,KAAK,CAAC,MAAM,MAAM,MAAS,GAAG;AACpD,YAAQ,KACN,kGAC8D;EAElE;AACA,MAAI,UAAU;AAIZ,QACE,OAAO,KAAK,CAAC,QAAS,MAAkC,cAAc,CAAC,GACvE;AACA,mBACE,IACA;8GACiH;IAErH;EACF;AACA,SAAO;;IAEL,CAAC,YAAY,GAAG;IAChB;IACA;;AAEJ;AAeK,IAAM,OAAO,IAAI,WAAW;AA0B5B,IAAM,MAAM,IAAI,UAAU;AA0B1B,IAAM,SAAS,IAAI,aAAa;AAMhC,IAAM,WAAW,OAAO,IAAI,cAAc;AAqB1C,IAAM,UAAU,OAAO,IAAI,aAAa;AAS/C,IAAM,gBAAgB,oBAAI,QAAO;AAqCjC,IAAM,SAAS,EAAE;EACf;EACA;;AAA2C;AAG7C,IAAI,2BAA6C;AAkBjD,SAAS,wBACP,KACA,eAAqB;AAOrB,MAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,eAAe,KAAK,GAAG;AAC/C,QAAI,UAAU;AACd,QAAI,UAAU;AACZ,gBAAU;;;;;;;;;;UAWP,KAAI,EACJ,QAAQ,SAAS,IAAI;IAC1B;AACA,UAAM,IAAI,MAAM,OAAO;EACzB;AACA,SAAO,WAAW,SACd,OAAO,WAAW,aAAa,IAC9B;AACP;AAcA,IAAM,kBAAkB,CACtB,SACA,SACgC;AAOhC,QAAM,IAAI,QAAQ,SAAS;AAI3B,QAAM,YAA2B,CAAA;AACjC,MAAIA,QACF,SAAS,aAAa,UAAU,SAAS,gBAAgB,WAAW;AAKtE,MAAI;AAIJ,MAAI,QAAQ;AAEZ,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,IAAI,QAAQ,CAAC;AAMnB,QAAI,mBAAmB;AACvB,QAAI;AACJ,QAAI,YAAY;AAChB,QAAI;AAIJ,WAAO,YAAY,EAAE,QAAQ;AAE3B,YAAM,YAAY;AAClB,cAAQ,MAAM,KAAK,CAAC;AACpB,UAAI,UAAU,MAAM;AAClB;MACF;AACA,kBAAY,MAAM;AAClB,UAAI,UAAU,cAAc;AAC1B,YAAI,MAAM,aAAa,MAAM,OAAO;AAClC,kBAAQ;QACV,WAAW,MAAM,aAAa,MAAM,QAAW;AAE7C,kBAAQ;QACV,WAAW,MAAM,QAAQ,MAAM,QAAW;AACxC,cAAI,eAAe,KAAK,MAAM,QAAQ,CAAC,GAAG;AAGxC,8BAAkB,IAAI,OAAO,KAAK,MAAM,QAAQ,CAAC,IAAI,GAAG;UAC1D;AACA,kBAAQ;QACV,WAAW,MAAM,gBAAgB,MAAM,QAAW;AAChD,cAAI,UAAU;AACZ,kBAAM,IAAI,MACR,kJACsE;UAE1E;AACA,kBAAQ;QACV;MACF,WAAW,UAAU,aAAa;AAChC,YAAI,MAAM,YAAY,MAAM,KAAK;AAG/B,kBAAQ,mBAAmB;AAG3B,6BAAmB;QACrB,WAAW,MAAM,cAAc,MAAM,QAAW;AAE9C,6BAAmB;QACrB,OAAO;AACL,6BAAmB,MAAM,YAAY,MAAM,iBAAiB,EAAE;AAC9D,qBAAW,MAAM,cAAc;AAC/B,kBACE,MAAM,UAAU,MAAM,SAClB,cACA,MAAM,UAAU,MAAM,MACpB,0BACA;QACV;MACF,WACE,UAAU,2BACV,UAAU,yBACV;AACA,gBAAQ;MACV,WAAW,UAAU,mBAAmB,UAAU,kBAAkB;AAClE,gBAAQ;MACV,OAAO;AAGL,gBAAQ;AACR,0BAAkB;MACpB;IACF;AAEA,QAAI,UAAU;AAIZ,cAAQ,OACN,qBAAqB,MACnB,UAAU,eACV,UAAU,2BACV,UAAU,yBACZ,0BAA0B;IAE9B;AAeA,UAAM,MACJ,UAAU,eAAe,QAAQ,IAAI,CAAC,EAAE,WAAW,IAAI,IAAI,MAAM;AACnE,IAAAA,SACE,UAAU,eACN,IAAI,aACJ,oBAAoB,KACjB,UAAU,KAAK,QAAS,GACzB,EAAE,MAAM,GAAG,gBAAgB,IACzB,uBACA,EAAE,MAAM,gBAAgB,KAC1B,SACA,MACA,IAAI,UAAU,qBAAqB,KAAK,IAAI;EACtD;AAEA,QAAM,aACJA,SACC,QAAQ,CAAC,KAAK,UACd,SAAS,aAAa,WAAW,SAAS,gBAAgB,YAAY;AAGzE,SAAO,CAAC,wBAAwB,SAAS,UAAU,GAAG,SAAS;AACjE;AAIA,IAAM,WAAN,MAAM,UAAQ;EAMZ,YAEE,EAAC,SAAS,CAAC,YAAY,GAAG,KAAI,GAC9B,SAAuB;AALzB,SAAA,QAA6B,CAAA;AAO3B,QAAI;AACJ,QAAI,YAAY;AAChB,QAAI,gBAAgB;AACpB,UAAM,YAAY,QAAQ,SAAS;AACnC,UAAM,QAAQ,KAAK;AAGnB,UAAM,CAACA,OAAM,SAAS,IAAI,gBAAgB,SAAS,IAAI;AACvD,SAAK,KAAK,UAAS,cAAcA,OAAM,OAAO;AAC9C,WAAO,cAAc,KAAK,GAAG;AAG7B,QAAI,SAAS,cAAc,SAAS,eAAe;AACjD,YAAM,UAAU,KAAK,GAAG,QAAQ;AAChC,cAAQ,YAAY,GAAG,QAAQ,UAAU;IAC3C;AAGA,YAAQ,OAAO,OAAO,SAAQ,OAAQ,QAAQ,MAAM,SAAS,WAAW;AACtE,UAAI,KAAK,aAAa,GAAG;AACvB,YAAI,UAAU;AACZ,gBAAMC,OAAO,KAAiB;AAK9B,cACE,2BAA4B,KAAKA,IAAG,KACnC,KAAiB,UAAU,SAAS,MAAM,GAC3C;AACA,kBAAM,IACJ,0CAA0CA,IAAG,sDACMA,IAAG;AAExD,gBAAIA,SAAQ,YAAY;AACtB,oBAAM,IAAI,MAAM,CAAC;YACnB;AAAO,2BAAa,IAAI,CAAC;UAC3B;QACF;AAIA,YAAK,KAAiB,cAAa,GAAI;AACrC,qBAAW,QAAS,KAAiB,kBAAiB,GAAI;AACxD,gBAAI,KAAK,SAAS,oBAAoB,GAAG;AACvC,oBAAM,WAAW,UAAU,eAAe;AAC1C,oBAAM,QAAS,KAAiB,aAAa,IAAI;AACjD,oBAAM,UAAU,MAAM,MAAM,MAAM;AAClC,oBAAM,IAAI,eAAe,KAAK,QAAQ;AACtC,oBAAM,KAAK;gBACT,MAAM;gBACN,OAAO;gBACP,MAAM,EAAE,CAAC;gBACT,SAAS;gBACT,MACE,EAAE,CAAC,MAAM,MACL,eACA,EAAE,CAAC,MAAM,MACP,uBACA,EAAE,CAAC,MAAM,MACP,YACA;eACX;AACA,mBAAiB,gBAAgB,IAAI;YACxC,WAAW,KAAK,WAAW,MAAM,GAAG;AAClC,oBAAM,KAAK;gBACT,MAAM;gBACN,OAAO;eACR;AACA,mBAAiB,gBAAgB,IAAI;YACxC;UACF;QACF;AAGA,YAAI,eAAe,KAAM,KAAiB,OAAO,GAAG;AAIlD,gBAAMC,WAAW,KAAiB,YAAa,MAAM,MAAM;AAC3D,gBAAM,YAAYA,SAAQ,SAAS;AACnC,cAAI,YAAY,GAAG;AAChB,iBAAiB,cAAc,eAC3B,aAAa,cACd;AAGJ,qBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AACjC,mBAAiB,OAAOA,SAAQ,CAAC,GAAG,aAAY,CAAE;AAEnD,qBAAO,SAAQ;AACf,oBAAM,KAAK,EAAC,MAAM,YAAY,OAAO,EAAE,UAAS,CAAC;YACnD;AAIC,iBAAiB,OAAOA,SAAQ,SAAS,GAAG,aAAY,CAAE;UAC7D;QACF;MACF,WAAW,KAAK,aAAa,GAAG;AAC9B,cAAM,OAAQ,KAAiB;AAC/B,YAAI,SAAS,aAAa;AACxB,gBAAM,KAAK,EAAC,MAAM,YAAY,OAAO,UAAS,CAAC;QACjD,OAAO;AACL,cAAI,IAAI;AACR,kBAAQ,IAAK,KAAiB,KAAK,QAAQ,QAAQ,IAAI,CAAC,OAAO,IAAI;AAGjE,kBAAM,KAAK,EAAC,MAAM,cAAc,OAAO,UAAS,CAAC;AAEjD,iBAAK,OAAO,SAAS;UACvB;QACF;MACF;AACA;IACF;AAEA,QAAI,UAAU;AAOZ,UAAI,UAAU,WAAW,eAAe;AACtC,cAAM,IAAI,MACR;MAME,QAAQ,KAAK,QAAQ,IACrB,GAAG;MAET;IACF;AAKA,qBACE,cAAc;MACZ,MAAM;MACN,UAAU;MACV,kBAAkB,KAAK;MACvB,OAAO,KAAK;MACZ;KACD;EACL;;;EAIA,OAAO,cAAcF,OAAmB,UAAwB;AAC9D,UAAM,KAAK,EAAE,cAAc,UAAU;AACrC,OAAG,YAAYA;AACf,WAAO;EACT;;AAgBF,SAAS,iBACP,MACA,OACA,SAA0B,MAC1B,gBAAuB;AAIvB,MAAI,UAAU,UAAU;AACtB,WAAO;EACT;AACA,MAAI,mBACF,mBAAmB,SACd,OAAyB,eAAe,cAAc,IACtD,OAA+C;AACtD,QAAM,2BAA2B,YAAY,KAAK,IAC9C;;IAEC,MAA0B,iBAAiB;;AAChD,MAAI,kBAAkB,gBAAgB,0BAA0B;AAE9D,uBAAmB,oCAAoC,IAAI,KAAK;AAChE,QAAI,6BAA6B,QAAW;AAC1C,yBAAmB;IACrB,OAAO;AACL,yBAAmB,IAAI,yBAAyB,IAAgB;AAChE,uBAAiB,aAAa,MAAM,QAAQ,cAAc;IAC5D;AACA,QAAI,mBAAmB,QAAW;AAChC,OAAE,OAAyB,iBAAzB,OAAyB,eAAiB,CAAA,IAAI,cAAc,IAC5D;IACJ,OAAO;AACJ,aAAiC,cAAc;IAClD;EACF;AACA,MAAI,qBAAqB,QAAW;AAClC,YAAQ,iBACN,MACA,iBAAiB,UAAU,MAAO,MAA0B,MAAM,GAClE,kBACA,cAAc;EAElB;AACA,SAAO;AACT;AAOA,IAAM,mBAAN,MAAsB;EASpB,YAAY,UAAoB,QAAiB;AAPjD,SAAA,UAAmC,CAAA;AAKnC,SAAA,2BAAiD;AAG/C,SAAK,aAAa;AAClB,SAAK,WAAW;EAClB;;EAGA,IAAI,aAAU;AACZ,WAAO,KAAK,SAAS;EACvB;;EAGA,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;;;EAIA,OAAO,SAAkC;AACvC,UAAM,EACJ,IAAI,EAAC,QAAO,GACZ,MAAY,IACV,KAAK;AACT,UAAM,YAAY,SAAS,iBAAiB,GAAG,WAAW,SAAS,IAAI;AACvE,WAAO,cAAc;AAErB,QAAI,OAAO,OAAO,SAAQ;AAC1B,QAAI,YAAY;AAChB,QAAI,YAAY;AAChB,QAAI,eAAe,MAAM,CAAC;AAE1B,WAAO,iBAAiB,QAAW;AACjC,UAAI,cAAc,aAAa,OAAO;AACpC,YAAI;AACJ,YAAI,aAAa,SAAS,YAAY;AACpC,iBAAO,IAAI,UACT,MACA,KAAK,aACL,MACA,OAAO;QAEX,WAAW,aAAa,SAAS,gBAAgB;AAC/C,iBAAO,IAAI,aAAa,KACtB,MACA,aAAa,MACb,aAAa,SACb,MACA,OAAO;QAEX,WAAW,aAAa,SAAS,cAAc;AAC7C,iBAAO,IAAI,YAAY,MAAqB,MAAM,OAAO;QAC3D;AACA,aAAK,QAAQ,KAAK,IAAI;AACtB,uBAAe,MAAM,EAAE,SAAS;MAClC;AACA,UAAI,cAAc,cAAc,OAAO;AACrC,eAAO,OAAO,SAAQ;AACtB;MACF;IACF;AAIA,WAAO,cAAc;AACrB,WAAO;EACT;EAEA,QAAQ,QAAsB;AAC5B,QAAI,IAAI;AACR,eAAW,QAAQ,KAAK,SAAS;AAC/B,UAAI,SAAS,QAAW;AACtB,yBACE,cAAc;UACZ,MAAM;UACN;UACA,OAAO,OAAO,CAAC;UACf,YAAY;UACZ;UACA,kBAAkB;SACnB;AACH,YAAK,KAAuB,YAAY,QAAW;AAChD,eAAuB,WAAW,QAAQ,MAAuB,CAAC;AAInE,eAAM,KAAuB,QAAS,SAAS;QACjD,OAAO;AACL,eAAK,WAAW,OAAO,CAAC,CAAC;QAC3B;MACF;AACA;IACF;EACF;;AA8CF,IAAM,YAAN,MAAM,WAAS;;EAwBb,IAAI,gBAAa;AAIf,WAAO,KAAK,UAAU,iBAAiB,KAAK;EAC9C;EAeA,YACE,WACA,SACA,QACA,SAAkC;AA/C3B,SAAA,OAAO;AAEhB,SAAA,mBAA4B;AA+B5B,SAAA,2BAAiD;AAgB/C,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,UAAU;AAIf,SAAK,gBAAgB,SAAS,eAAe;AAC7C,QAAI,6BAA6B;AAE/B,WAAK,iBAAiB;IACxB;EACF;;;;;;;;;;;;;;;;;;;EAoBA,IAAI,aAAU;AACZ,QAAI,aAAmB,KAAK,KAAK,WAAW,EAAE;AAC9C,UAAM,SAAS,KAAK;AACpB,QACE,WAAW,UACX,YAAY,aAAa,IACzB;AAIA,mBAAc,OAAwC;IACxD;AACA,WAAO;EACT;;;;;EAMA,IAAI,YAAS;AACX,WAAO,KAAK;EACd;;;;;EAMA,IAAI,UAAO;AACT,WAAO,KAAK;EACd;EAEA,WAAW,OAAgB,kBAAmC,MAAI;AAChE,QAAI,YAAY,KAAK,eAAe,MAAM;AACxC,YAAM,IAAI,MACR,0UAA0U;IAE9U;AACA,YAAQ,iBAAiB,MAAM,OAAO,eAAe;AACrD,QAAI,YAAY,KAAK,GAAG;AAItB,UAAI,UAAU,WAAW,SAAS,QAAQ,UAAU,IAAI;AACtD,YAAI,KAAK,qBAAqB,SAAS;AACrC,2BACE,cAAc;YACZ,MAAM;YACN,OAAO,KAAK;YACZ,KAAK,KAAK;YACV,QAAQ,KAAK;YACb,SAAS,KAAK;WACf;AACH,eAAK,QAAO;QACd;AACA,aAAK,mBAAmB;MAC1B,WAAW,UAAU,KAAK,oBAAoB,UAAU,UAAU;AAChE,aAAK,YAAY,KAAK;MACxB;IAEF,WAAY,MAAyB,YAAY,MAAM,QAAW;AAChE,WAAK,sBAAsB,KAAuB;IACpD,WAAY,MAAe,aAAa,QAAW;AACjD,UAAI,YAAY,KAAK,SAAS,SAAS,OAAO;AAC5C,aAAK,YACH,6GACuD;AAEzD,gBAAQ,KACN,yCACA,OACA,oEACA,8DACA,oEACA,2CAA2C;AAE7C;MACF;AACA,WAAK,YAAY,KAAa;IAChC,WAAW,WAAW,KAAK,GAAG;AAC5B,WAAK,gBAAgB,KAAK;IAC5B,OAAO;AAEL,WAAK,YAAY,KAAK;IACxB;EACF;EAEQ,QAAwB,MAAO;AACrC,WAAO,KAAK,KAAK,KAAK,WAAW,EAAE,UAAW,EAAE,aAC9C,MACA,KAAK,SAAS;EAElB;EAEQ,YAAY,OAAW;AAC7B,QAAI,KAAK,qBAAqB,OAAO;AACnC,WAAK,QAAO;AACZ,UACE,+BACA,6BAA6B,eAC7B;AACA,cAAM,iBAAiB,KAAK,YAAY,YAAY;AACpD,YAAI,mBAAmB,WAAW,mBAAmB,UAAU;AAC7D,cAAI,UAAU;AACd,cAAI,UAAU;AACZ,gBAAI,mBAAmB,SAAS;AAC9B,wBACE;YAOJ,OAAO;AACL,wBACE;YAGJ;UACF;AACA,gBAAM,IAAI,MAAM,OAAO;QACzB;MACF;AACA,uBACE,cAAc;QACZ,MAAM;QACN,OAAO,KAAK;QACZ,QAAQ,KAAK;QACb;QACA,SAAS,KAAK;OACf;AACH,WAAK,mBAAmB,KAAK,QAAQ,KAAK;IAC5C;EACF;EAEQ,YAAY,OAAc;AAIhC,QACE,KAAK,qBAAqB,WAC1B,YAAY,KAAK,gBAAgB,GACjC;AACA,YAAM,OAAO,KAAK,KAAK,WAAW,EAAE;AACpC,UAAI,6BAA6B;AAC/B,YAAI,KAAK,mBAAmB,QAAW;AACrC,eAAK,iBAAiB,gBAAgB,MAAM,QAAQ,UAAU;QAChE;AACA,gBAAQ,KAAK,eAAe,KAAK;MACnC;AACA,uBACE,cAAc;QACZ,MAAM;QACN;QACA;QACA,SAAS,KAAK;OACf;AACF,WAAc,OAAO;IACxB,OAAO;AACL,UAAI,6BAA6B;AAC/B,cAAM,WAAW,EAAE,eAAe,EAAE;AACpC,aAAK,YAAY,QAAQ;AAKzB,YAAI,KAAK,mBAAmB,QAAW;AACrC,eAAK,iBAAiB,gBAAgB,UAAU,QAAQ,UAAU;QACpE;AACA,gBAAQ,KAAK,eAAe,KAAK;AACjC,yBACE,cAAc;UACZ,MAAM;UACN,MAAM;UACN;UACA,SAAS,KAAK;SACf;AACH,iBAAS,OAAO;MAClB,OAAO;AACL,aAAK,YAAY,EAAE,eAAe,KAAe,CAAC;AAClD,yBACE,cAAc;UACZ,MAAM;UACN,MAAM,KAAK,KAAK,WAAW,EAAE;UAC7B;UACA,SAAS,KAAK;SACf;MACL;IACF;AACA,SAAK,mBAAmB;EAC1B;EAEQ,sBACN,QAA+C;AAG/C,UAAM,EAAC,QAAQ,CAAC,YAAY,GAAG,KAAI,IAAI;AAKvC,UAAM,WACJ,OAAO,SAAS,WACZ,KAAK,cAAc,MAAkC,KACpD,KAAK,OAAO,WACV,KAAK,KAAK,SAAS,cAClB,wBAAwB,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,GACzC,KAAK,OAAO,IAEhB;AAEN,QAAK,KAAK,kBAAuC,eAAe,UAAU;AACxE,uBACE,cAAc;QACZ,MAAM;QACN;QACA,UAAU,KAAK;QACf,OAAQ,KAAK,iBAAsC;QACnD,SAAS,KAAK;QACd;OACD;AACF,WAAK,iBAAsC,QAAQ,MAAM;IAC5D,OAAO;AACL,YAAM,WAAW,IAAI,iBAAiB,UAAsB,IAAI;AAChE,YAAM,WAAW,SAAS,OAAO,KAAK,OAAO;AAC7C,uBACE,cAAc;QACZ,MAAM;QACN;QACA;QACA,OAAO,SAAS;QAChB,SAAS,KAAK;QACd;QACA;OACD;AACH,eAAS,QAAQ,MAAM;AACvB,uBACE,cAAc;QACZ,MAAM;QACN;QACA;QACA,OAAO,SAAS;QAChB,SAAS,KAAK;QACd;QACA;OACD;AACH,WAAK,YAAY,QAAQ;AACzB,WAAK,mBAAmB;IAC1B;EACF;;;EAIA,cAAc,QAAgC;AAC5C,QAAI,WAAW,cAAc,IAAI,OAAO,OAAO;AAC/C,QAAI,aAAa,QAAW;AAC1B,oBAAc,IAAI,OAAO,SAAU,WAAW,IAAI,SAAS,MAAM,CAAE;IACrE;AACA,WAAO;EACT;EAEQ,gBAAgB,OAAwB;AAW9C,QAAI,CAAC,QAAQ,KAAK,gBAAgB,GAAG;AACnC,WAAK,mBAAmB,CAAA;AACxB,WAAK,QAAO;IACd;AAIA,UAAM,YAAY,KAAK;AACvB,QAAI,YAAY;AAChB,QAAI;AAEJ,eAAW,QAAQ,OAAO;AACxB,UAAI,cAAc,UAAU,QAAQ;AAKlC,kBAAU,KACP,WAAW,IAAI,WACd,KAAK,QAAQ,aAAY,CAAE,GAC3B,KAAK,QAAQ,aAAY,CAAE,GAC3B,MACA,KAAK,OAAO,CACZ;MAEN,OAAO;AAEL,mBAAW,UAAU,SAAS;MAChC;AACA,eAAS,WAAW,IAAI;AACxB;IACF;AAEA,QAAI,YAAY,UAAU,QAAQ;AAEhC,WAAK,QACH,YAAY,KAAK,SAAS,SAAU,EAAE,aACtC,SAAS;AAGX,gBAAU,SAAS;IACrB;EACF;;;;;;;;;;;;EAaA,QACE,QAA0B,KAAK,KAAK,WAAW,EAAE,aACjD,MAAa;AAEb,SAAK,4BAA4B,OAAO,MAAM,IAAI;AAClD,WAAO,UAAU,KAAK,WAAW;AAI/B,YAAM,IAAI,KAAK,KAAM,EAAE;AACvB,WAAK,KAAM,EAAE,OAAM;AACnB,cAAQ;IACV;EACF;;;;;;;;EASA,aAAa,aAAoB;AAC/B,QAAI,KAAK,aAAa,QAAW;AAC/B,WAAK,gBAAgB;AACrB,WAAK,4BAA4B,WAAW;IAC9C,WAAW,UAAU;AACnB,YAAM,IAAI,MACR,8EACoC;IAExC;EACF;;AA2BF,IAAM,gBAAN,MAAmB;EA2BjB,IAAI,UAAO;AACT,WAAO,KAAK,QAAQ;EACtB;;EAGA,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;EAEA,YACE,SACA,MACA,SACA,QACA,SAAkC;AAxC3B,SAAA,OAIe;AAYxB,SAAA,mBAA6C;AAM7C,SAAA,2BAAiD;AAoB/C,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,QAAI,QAAQ,SAAS,KAAK,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,MAAM,IAAI;AAChE,WAAK,mBAAmB,IAAI,MAAM,QAAQ,SAAS,CAAC,EAAE,KAAK,IAAI,OAAM,CAAE;AACvE,WAAK,UAAU;IACjB,OAAO;AACL,WAAK,mBAAmB;IAC1B;AACA,QAAI,6BAA6B;AAC/B,WAAK,aAAa;IACpB;EACF;;;;;;;;;;;;;;;;;;;;;;;EAwBA,WACE,OACA,kBAAmC,MACnC,YACA,UAAkB;AAElB,UAAM,UAAU,KAAK;AAGrB,QAAI,SAAS;AAEb,QAAI,YAAY,QAAW;AAEzB,cAAQ,iBAAiB,MAAM,OAAO,iBAAiB,CAAC;AACxD,eACE,CAAC,YAAY,KAAK,KACjB,UAAU,KAAK,oBAAoB,UAAU;AAChD,UAAI,QAAQ;AACV,aAAK,mBAAmB;MAC1B;IACF,OAAO;AAEL,YAAM,SAAS;AACf,cAAQ,QAAQ,CAAC;AAEjB,UAAI,GAAG;AACP,WAAK,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,KAAK;AACvC,YAAI,iBAAiB,MAAM,OAAO,aAAc,CAAC,GAAG,iBAAiB,CAAC;AAEtE,YAAI,MAAM,UAAU;AAElB,cAAK,KAAK,iBAAoC,CAAC;QACjD;AACA,4BACE,CAAC,YAAY,CAAC,KAAK,MAAO,KAAK,iBAAoC,CAAC;AACtE,YAAI,MAAM,SAAS;AACjB,kBAAQ;QACV,WAAW,UAAU,SAAS;AAC5B,oBAAU,KAAK,MAAM,QAAQ,IAAI,CAAC;QACpC;AAGC,aAAK,iBAAoC,CAAC,IAAI;MACjD;IACF;AACA,QAAI,UAAU,CAAC,UAAU;AACvB,WAAK,aAAa,KAAK;IACzB;EACF;;EAGA,aAAa,OAAc;AACzB,QAAI,UAAU,SAAS;AACpB,WAAK,KAAK,OAAO,EAAc,gBAAgB,KAAK,IAAI;IAC3D,OAAO;AACL,UAAI,6BAA6B;AAC/B,YAAI,KAAK,eAAe,QAAW;AACjC,eAAK,aAAa,yBAChB,KAAK,SACL,KAAK,MACL,WAAW;QAEf;AACA,gBAAQ,KAAK,WAAW,SAAS,EAAE;MACrC;AACA,uBACE,cAAc;QACZ,MAAM;QACN,SAAS,KAAK;QACd,MAAM,KAAK;QACX;QACA,SAAS,KAAK;OACf;AACF,WAAK,KAAK,OAAO,EAAc,aAC9B,KAAK,MACJ,SAAS,EAAa;IAE3B;EACF;;AAIF,IAAM,eAAN,cAA2B,cAAa;EAAxC,cAAA;;AACoB,SAAA,OAAO;EAyB3B;;EAtBW,aAAa,OAAc;AAClC,QAAI,6BAA6B;AAC/B,UAAI,KAAK,eAAe,QAAW;AACjC,aAAK,aAAa,yBAChB,KAAK,SACL,KAAK,MACL,UAAU;MAEd;AACA,cAAQ,KAAK,WAAW,KAAK;IAC/B;AACA,qBACE,cAAc;MACZ,MAAM;MACN,SAAS,KAAK;MACd,MAAM,KAAK;MACX;MACA,SAAS,KAAK;KACf;AAEF,SAAK,QAAgB,KAAK,IAAI,IAAI,UAAU,UAAU,SAAY;EACrE;;AAIF,IAAM,uBAAN,cAAmC,cAAa;EAAhD,cAAA;;AACoB,SAAA,OAAO;EAiB3B;;EAdW,aAAa,OAAc;AAClC,qBACE,cAAc;MACZ,MAAM;MACN,SAAS,KAAK;MACd,MAAM,KAAK;MACX,OAAO,CAAC,EAAE,SAAS,UAAU;MAC7B,SAAS,KAAK;KACf;AACF,SAAK,KAAK,OAAO,EAAc,gBAC9B,KAAK,MACL,CAAC,CAAC,SAAS,UAAU,OAAO;EAEhC;;AAkBF,IAAM,YAAN,cAAwB,cAAa;EAGnC,YACE,SACA,MACA,SACA,QACA,SAAkC;AAElC,UAAM,SAAS,MAAM,SAAS,QAAQ,OAAO;AAT7B,SAAA,OAAO;AAWvB,QAAI,YAAY,KAAK,YAAY,QAAW;AAC1C,YAAM,IAAI,MACR,QAAQ,QAAQ,SAAS,gBAAgB,IAAI,8HAEF;IAE/C;EACF;;;;EAKS,WACP,aACA,kBAAmC,MAAI;AAEvC,kBACE,iBAAiB,MAAM,aAAa,iBAAiB,CAAC,KAAK;AAC7D,QAAI,gBAAgB,UAAU;AAC5B;IACF;AACA,UAAM,cAAc,KAAK;AAIzB,UAAM,uBACH,gBAAgB,WAAW,gBAAgB,WAC3C,YAAyC,YACvC,YAAyC,WAC3C,YAAyC,SACvC,YAAyC,QAC3C,YAAyC,YACvC,YAAyC;AAI9C,UAAM,oBACJ,gBAAgB,YACf,gBAAgB,WAAW;AAE9B,qBACE,cAAc;MACZ,MAAM;MACN,SAAS,KAAK;MACd,MAAM,KAAK;MACX,OAAO;MACP,SAAS,KAAK;MACd,gBAAgB;MAChB,aAAa;MACb;KACD;AACH,QAAI,sBAAsB;AACxB,WAAK,QAAQ,oBACX,KAAK,MACL,MACA,WAAuC;IAE3C;AACA,QAAI,mBAAmB;AACrB,WAAK,QAAQ,iBACX,KAAK,MACL,MACA,WAAuC;IAE3C;AACA,SAAK,mBAAmB;EAC1B;EAEA,YAAY,OAAY;AACtB,QAAI,OAAO,KAAK,qBAAqB,YAAY;AAC/C,WAAK,iBAAiB,KAAK,KAAK,SAAS,QAAQ,KAAK,SAAS,KAAK;IACtE,OAAO;AACJ,WAAK,iBAAyC,YAAY,KAAK;IAClE;EACF;;AAIF,IAAM,cAAN,MAAiB;EAiBf,YACS,SACP,QACA,SAAkC;AAF3B,SAAA,UAAA;AAjBA,SAAA,OAAO;AAYhB,SAAA,2BAAiD;AAS/C,SAAK,WAAW;AAChB,SAAK,UAAU;EACjB;;EAGA,IAAI,gBAAa;AACf,WAAO,KAAK,SAAS;EACvB;EAEA,WAAW,OAAc;AACvB,qBACE,cAAc;MACZ,MAAM;MACN,SAAS,KAAK;MACd;MACA,SAAS,KAAK;KACf;AACH,qBAAiB,MAAM,KAAK;EAC9B;;AAqBK,IAAM,OAAO;;EAElB,uBAAuB;EACvB,SAAS;EACT,cAAc;EACd,cAAc;EACd,kBAAkB;;EAElB,mBAAmB;EACnB,aAAa;EACb,mBAAmB;EACnB,YAAY;EACZ,gBAAgB;EAChB,uBAAuB;EACvB,YAAY;EACZ,eAAe;EACf,cAAc;;AAIhB,IAAM,kBAAkB,WACpB,OAAO,gCACP,OAAO;AACX,kBAAkB,UAAU,SAAS;CAIpC,OAAO,oBAAP,OAAO,kBAAoB,CAAA,IAAI,KAAK,OAAO;AAC5C,IAAI,YAAY,OAAO,gBAAgB,SAAS,GAAG;AACjD,iBAAe,MAAK;AAClB,iBACE,qBACA,gFACiD;EAErD,CAAC;AACH;AA2BO,IAAM,SAAS,CACpB,OACA,WACA,YACY;AACZ,MAAI,YAAY,aAAa,MAAM;AAKjC,UAAM,IAAI,UAAU,2CAA2C,SAAS,EAAE;EAC5E;AACA,QAAM,WAAW,WAAW,qBAAqB;AACjD,QAAM,gBAAgB,SAAS,gBAAgB;AAG/C,MAAI,OAAmB,cAAsB,YAAY;AACzD,mBACE,cAAc;IACZ,MAAM;IACN,IAAI;IACJ;IACA;IACA;IACA;GACD;AACH,MAAI,SAAS,QAAW;AACtB,UAAM,UAAU,SAAS,gBAAgB;AAGxC,kBAAsB,YAAY,IAAI,OAAO,IAAI,UAChD,UAAU,aAAa,aAAY,GAAI,OAAO,GAC9C,SACA,QACA,WAAW,CAAA,CAAE;EAEjB;AACA,OAAK,WAAW,KAAK;AACrB,mBACE,cAAc;IACZ,MAAM;IACN,IAAI;IACJ;IACA;IACA;IACA;GACD;AACH,SAAO;AACT;AAEA,IAAI,6BAA6B;AAC/B,SAAO,eAAe;AACtB,SAAO,kBAAkB;AACzB,MAAI,UAAU;AACZ,WAAO,gDACL;EACJ;AACF;;;ACnvEA,IAAMG,aAAY;AAGlB,IAAMC,UAAS;AAKR,IAAM,8BACXA,QAAO,eACNA,QAAO,aAAa,UAAaA,QAAO,SAAS,iBAClD,wBAAwB,SAAS,aACjC,aAAa,cAAc;AAkB7B,IAAM,oBAAoB,OAAM;AAEhC,IAAM,cAAc,oBAAI,QAAO;AASzB,IAAO,YAAP,MAAgB;EAOpB,YACE,SACA,SACA,WAAiB;AARnB,SAAC,cAAc,IAAI;AAUjB,QAAI,cAAc,mBAAmB;AACnC,YAAM,IAAI,MACR,mEAAmE;IAEvE;AACA,SAAK,UAAU;AACf,SAAK,WAAW;EAClB;;;EAIA,IAAI,aAAU;AAGZ,QAAI,aAAa,KAAK;AACtB,UAAM,UAAU,KAAK;AACrB,QAAI,+BAA+B,eAAe,QAAW;AAC3D,YAAM,YAAY,YAAY,UAAa,QAAQ,WAAW;AAC9D,UAAI,WAAW;AACb,qBAAa,YAAY,IAAI,OAAO;MACtC;AACA,UAAI,eAAe,QAAW;AAC5B,SAAC,KAAK,cAAc,aAAa,IAAI,cAAa,GAAI,YACpD,KAAK,OAAO;AAEd,YAAI,WAAW;AACb,sBAAY,IAAI,SAAS,UAAU;QACrC;MACF;IACF;AACA,WAAO;EACT;EAEA,WAAQ;AACN,WAAO,KAAK;EACd;;AAWF,IAAM,oBAAoB,CAAC,UAAkC;AAE3D,MAAK,MAAoB,cAAc,MAAM,MAAM;AACjD,WAAQ,MAAoB;EAC9B,WAAW,OAAO,UAAU,UAAU;AACpC,WAAO;EACT,OAAO;AACL,UAAM,IAAI,MACR,mEACK,KAAK,sFACkB;EAEhC;AACF;AASO,IAAM,YAAY,CAAC,UACxB,IAAK,UACH,OAAO,UAAU,WAAW,QAAQ,OAAO,KAAK,GAChD,QACA,iBAAiB;AAWd,IAAM,MAAM,CACjB,YACG,WACU;AACb,QAAM,UACJ,QAAQ,WAAW,IACf,QAAQ,CAAC,IACT,OAAO,OACL,CAAC,KAAK,GAAG,QAAQ,MAAM,kBAAkB,CAAC,IAAI,QAAQ,MAAM,CAAC,GAC7D,QAAQ,CAAC,CAAC;AAElB,SAAO,IAAK,UACV,SACA,SACA,iBAAiB;AAErB;AAWO,IAAM,cAAc,CACzB,YACA,WACE;AACF,MAAI,6BAA6B;AAC9B,eAA0B,qBAAqB,OAAO,IAAI,CAAC,MAC1D,aAAa,gBAAgB,IAAI,EAAE,UAAW;EAElD,OAAO;AACL,eAAW,KAAK,QAAQ;AACtB,YAAM,QAAQ,SAAS,cAAc,OAAO;AAE5C,YAAM,QAASA,QAAe,UAAU;AACxC,UAAI,UAAU,QAAW;AACvB,cAAM,aAAa,SAAS,KAAK;MACnC;AACA,YAAM,cAAe,EAAgB;AACrC,iBAAW,YAAY,KAAK;IAC9B;EACF;AACF;AAEA,IAAM,0BAA0B,CAAC,UAAwB;AACvD,MAAI,UAAU;AACd,aAAW,QAAQ,MAAM,UAAU;AACjC,eAAW,KAAK;EAClB;AACA,SAAO,UAAU,OAAO;AAC1B;AAEO,IAAM,qBACX,+BACCD,cAAaC,QAAO,kBAAkB,SACnC,CAAC,MAAyB,IAC1B,CAAC,MACC,aAAa,gBAAgB,wBAAwB,CAAC,IAAI;;;AChKlE,IAAM,EACJ,IACA,gBACA,0BACA,qBACA,uBACA,eAAc,IACZ;AAEJ,IAAMC,aAAY;AAGlB,IAAMC,UAAS;AAEf,IAAID,YAAW;AACb,EAAAC,QAAO,mBAAPA,QAAO,iBAAmB;AAC5B;AAEA,IAAMC,YAAW;AAEjB,IAAIC;AAEJ,IAAMC,gBAAgBH,QACnB;AAMH,IAAM,iCAAiCG,gBAClCA,cAAa,cACd;AAEJ,IAAMC,mBAAkBH,YACpBD,QAAO,wCACPA,QAAO;AAEX,IAAIC,WAAU;AAGZ,EAAAD,QAAO,sBAAPA,QAAO,oBAAsB,oBAAI,IAAG;AAOpC,EAAAE,gBAAe,CAAC,MAAc,YAAmB;AAC/C,eAAW,4BAA4B,IAAI;AAC3C,QACE,CAACF,QAAO,kBAAmB,IAAI,OAAO,KACtC,CAACA,QAAO,kBAAmB,IAAI,IAAI,GACnC;AACA,cAAQ,KAAK,OAAO;AACpB,MAAAA,QAAO,kBAAmB,IAAI,OAAO;IACvC;EACF;AAEA,iBAAe,MAAK;AAClB,IAAAE,cACE,YACA,qDAAqD;AAIvD,QAAIF,QAAO,UAAU,SAASI,qBAAoB,QAAW;AAC3D,MAAAF,cACE,4BACA,0GACwD;IAE5D;EACF,CAAC;AACH;AAwCA,IAAMG,iBAAgBJ,YAClB,CAAC,UAA0C;AACzC,QAAM,aAAcD,QACjB;AACH,MAAI,CAAC,YAAY;AACf;EACF;AACA,EAAAA,QAAO,cACL,IAAI,YAA6C,aAAa;IAC5D,QAAQ;GACT,CAAC;AAEN,IACA;AASJ,IAAM,4BAA4B,CAChC,MACA,SACM;AA0KD,IAAM,mBAA8C;EACzD,YAAY,OAAgB,MAAc;AACxC,YAAQ,MAAM;MACZ,KAAK;AACH,gBAAQ,QAAQ,iCAAiC;AACjD;MACF,KAAK;MACL,KAAK;AAGH,gBAAQ,SAAS,OAAO,QAAQ,KAAK,UAAU,KAAK;AACpD;IACJ;AACA,WAAO;EACT;EAEA,cAAc,OAAsB,MAAc;AAChD,QAAI,YAAqB;AACzB,YAAQ,MAAM;MACZ,KAAK;AACH,oBAAY,UAAU;AACtB;MACF,KAAK;AACH,oBAAY,UAAU,OAAO,OAAO,OAAO,KAAK;AAChD;MACF,KAAK;MACL,KAAK;AAIH,YAAI;AAEF,sBAAY,KAAK,MAAM,KAAM;QAC/B,SAAS,GAAG;AACV,sBAAY;QACd;AACA;IACJ;AACA,WAAO;EACT;;AAWK,IAAM,WAAuB,CAAC,OAAgB,QACnD,CAAC,GAAG,OAAO,GAAG;AAEhB,IAAM,6BAAkD;EACtD,WAAW;EACX,MAAM;EACN,WAAW;EACX,SAAS;EACT,YAAY;EACZ,YAAY;;AAsBb,OAA8B,aAA9B,OAA8B,WAAa,OAAO,UAAU;AAc7DA,QAAO,wBAAPA,QAAO,sBAAwB,oBAAI,QAAO;AAWpC,IAAgB,kBAAhB,cASI,YAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAqFnB,OAAO,eAAe,aAAwB;AAC5C,SAAK,UAAS;AACd,KAAC,KAAK,kBAAL,KAAK,gBAAkB,CAAA,IAAI,KAAK,WAAW;EAC9C;;;;;;EAuGA,WAAW,qBAAkB;AAE3B,SAAK,SAAQ;AAKb,WACE,KAAK,4BAA4B,CAAC,GAAG,KAAK,yBAAyB,KAAI,CAAE;EAE7E;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BA,OAAO,eACL,MACA,UAA+B,4BAA0B;AAGzD,QAAI,QAAQ,OAAO;AAChB,cAAsD,YAAY;IACrE;AACA,SAAK,UAAS;AAGd,QAAI,KAAK,UAAU,eAAe,IAAI,GAAG;AACvC,gBAAU,OAAO,OAAO,OAAO;AAC/B,cAAQ,UAAU;IACpB;AACA,SAAK,kBAAkB,IAAI,MAAM,OAAO;AACxC,QAAI,CAAC,QAAQ,YAAY;AACvB,YAAM,MAAMC;;;QAGR,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,sBAAsB;UAChD,OAAM;AACV,YAAM,aAAa,KAAK,sBAAsB,MAAM,KAAK,OAAO;AAChE,UAAI,eAAe,QAAW;AAC5B,uBAAe,KAAK,WAAW,MAAM,UAAU;MACjD;IACF;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BU,OAAO,sBACf,MACA,KACA,SAA4B;AAE5B,UAAM,EAAC,KAAK,IAAG,IAAI,yBAAyB,KAAK,WAAW,IAAI,KAAK;MACnE,MAAG;AACD,eAAO,KAAK,GAAwB;MACtC;MACA,IAA2B,GAAU;AAClC,aAAqD,GAAG,IAAI;MAC/D;;AAEF,QAAIA,aAAY,OAAO,MAAM;AAC3B,UAAI,YAAY,yBAAyB,KAAK,WAAW,IAAI,KAAK,CAAA,IAAK;AACrE,cAAM,IAAI,MACR,SAAS,KAAK,UAAU,OAAO,IAAI,CAAC,CAAC,OAChC,KAAK,IAAI,4JAEmD;MAErE;AACA,MAAAC,cACE,oCACA,SAAS,KAAK,UAAU,OAAO,IAAI,CAAC,CAAC,OAChC,KAAK,IAAI,uHAEY;IAE9B;AACA,WAAO;MACL;MACA,IAA2B,OAAc;AACvC,cAAM,WAAW,KAAK,KAAK,IAAI;AAC/B,aAAK,KAAK,MAAM,KAAK;AACrB,aAAK,cAAc,MAAM,UAAU,OAAO;MAC5C;MACA,cAAc;MACd,YAAY;;EAEhB;;;;;;;;;;;;;;;EAgBA,OAAO,mBAAmB,MAAiB;AACzC,WAAO,KAAK,kBAAkB,IAAI,IAAI,KAAK;EAC7C;;;;;;;;;;;;EAgBQ,OAAO,YAAS;AACtB,QACE,KAAK,eAAe,0BAA0B,qBAAqB,IAAI,CAAC,GACxE;AAEA;IACF;AAEA,UAAM,YAAY,eAAe,IAAI;AACrC,cAAU,SAAQ;AAKlB,QAAI,UAAU,kBAAkB,QAAW;AACzC,WAAK,gBAAgB,CAAC,GAAG,UAAU,aAAa;IAClD;AAEA,SAAK,oBAAoB,IAAI,IAAI,UAAU,iBAAiB;EAC9D;;;;;;;;;;;;EAaU,OAAO,WAAQ;AACvB,QAAI,KAAK,eAAe,0BAA0B,aAAa,IAAI,CAAC,GAAG;AACrE;IACF;AACA,SAAK,YAAY;AACjB,SAAK,UAAS;AAGd,QAAI,KAAK,eAAe,0BAA0B,cAAc,IAAI,CAAC,GAAG;AACtE,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAW;QACf,GAAG,oBAAoB,KAAK;QAC5B,GAAG,sBAAsB,KAAK;;AAEhC,iBAAW,KAAK,UAAU;AACxB,aAAK,eAAe,GAAG,MAAM,CAAC,CAAC;MACjC;IACF;AAGA,UAAM,WAAW,KAAK,OAAO,QAAQ;AACrC,QAAI,aAAa,MAAM;AACrB,YAAM,aAAa,oBAAoB,IAAI,QAAQ;AACnD,UAAI,eAAe,QAAW;AAC5B,mBAAW,CAAC,GAAG,OAAO,KAAK,YAAY;AACrC,eAAK,kBAAkB,IAAI,GAAG,OAAO;QACvC;MACF;IACF;AAGA,SAAK,2BAA2B,oBAAI,IAAG;AACvC,eAAW,CAAC,GAAG,OAAO,KAAK,KAAK,mBAAmB;AACjD,YAAM,OAAO,KAAK,2BAA2B,GAAG,OAAO;AACvD,UAAI,SAAS,QAAW;AACtB,aAAK,yBAAyB,IAAI,MAAM,CAAC;MAC3C;IACF;AAEA,SAAK,gBAAgB,KAAK,eAAe,KAAK,MAAM;AAEpD,QAAID,WAAU;AACZ,UAAI,KAAK,eAAe,gBAAgB,GAAG;AACzC,QAAAC,cACE,+BACA,qHAC4D;MAEhE;AACA,UAAI,KAAK,eAAe,uBAAuB,GAAG;AAChD,QAAAA,cACE,uCACA,4HAC4D;MAEhE;IACF;EACF;;;;;;;;;;;;;;;EA4BU,OAAO,eACf,QAAuB;AAEvB,UAAM,gBAAgB,CAAA;AACtB,QAAI,MAAM,QAAQ,MAAM,GAAG;AAIzB,YAAM,MAAM,IAAI,IAAK,OAA0B,KAAK,QAAQ,EAAE,QAAO,CAAE;AAEvE,iBAAW,KAAK,KAAK;AACnB,sBAAc,QAAQ,mBAAmB,CAAsB,CAAC;MAClE;IACF,WAAW,WAAW,QAAW;AAC/B,oBAAc,KAAK,mBAAmB,MAAM,CAAC;IAC/C;AACA,WAAO;EACT;;;;;EAaQ,OAAO,2BACb,MACA,SAA4B;AAE5B,UAAM,YAAY,QAAQ;AAC1B,WAAO,cAAc,QACjB,SACA,OAAO,cAAc,WACnB,YACA,OAAO,SAAS,WACd,KAAK,YAAW,IAChB;EACV;EAiDA,cAAA;AACE,UAAK;AA9WC,SAAA,uBAAwC;AAuUhD,SAAA,kBAAkB;AAOlB,SAAA,aAAa;AAwBL,SAAA,uBAA2C;AASjD,SAAK,aAAY;EACnB;;;;;EAMQ,eAAY;AAClB,SAAK,kBAAkB,IAAI,QACzB,CAAC,QAAS,KAAK,iBAAiB,GAAI;AAEtC,SAAK,sBAAsB,oBAAI,IAAG;AAGlC,SAAK,yBAAwB;AAG7B,SAAK,cAAa;AACjB,SAAK,YAAuC,eAAe,QAAQ,CAAC,MACnE,EAAE,IAAI,CAAC;EAEX;;;;;;;;;;EAWA,cAAc,YAA8B;AAC1C,KAAC,KAAK,kBAAL,KAAK,gBAAkB,oBAAI,IAAG,IAAI,IAAI,UAAU;AAKjD,QAAI,KAAK,eAAe,UAAa,KAAK,aAAa;AACrD,iBAAW,gBAAe;IAC5B;EACF;;;;;EAMA,iBAAiB,YAA8B;AAC7C,SAAK,eAAe,OAAO,UAAU;EACvC;;;;;;;EAQQ,2BAAwB;AAC9B,UAAM,qBAAqB,oBAAI,IAAG;AAClC,UAAM,oBAAqB,KAAK,YAC7B;AACH,eAAW,KAAK,kBAAkB,KAAI,GAAoC;AACxE,UAAI,KAAK,eAAe,CAAC,GAAG;AAC1B,2BAAmB,IAAI,GAAG,KAAK,CAAC,CAAC;AACjC,eAAO,KAAK,CAAC;MACf;IACF;AACA,QAAI,mBAAmB,OAAO,GAAG;AAC/B,WAAK,uBAAuB;IAC9B;EACF;;;;;;;;;;EAWU,mBAAgB;AACxB,UAAM,aACJ,KAAK,cACL,KAAK,aACF,KAAK,YAAuC,iBAAiB;AAElE,gBACE,YACC,KAAK,YAAuC,aAAa;AAE5D,WAAO;EACT;;;;;;EAOA,oBAAiB;AAEd,SAA4C,eAA5C,KAA4C,aAC3C,KAAK,iBAAgB;AACvB,SAAK,eAAe,IAAI;AACxB,SAAK,eAAe,QAAQ,CAAC,MAAM,EAAE,gBAAe,CAAE;EACxD;;;;;;;EAQU,eAAe,kBAAyB;EAAG;;;;;;;EAQrD,uBAAoB;AAClB,SAAK,eAAe,QAAQ,CAAC,MAAM,EAAE,mBAAkB,CAAE;EAC3D;;;;;;;;;;;;;EAcA,yBACE,MACA,MACA,OAAoB;AAEpB,SAAK,sBAAsB,MAAM,KAAK;EACxC;EAEQ,sBAAsB,MAAmB,OAAc;AAC7D,UAAM,iBACJ,KAAK,YACL;AACF,UAAM,UAAU,eAAe,IAAI,IAAI;AACvC,UAAM,OACJ,KAAK,YACL,2BAA2B,MAAM,OAAO;AAC1C,QAAI,SAAS,UAAa,QAAQ,YAAY,MAAM;AAClD,YAAM,YACH,QAAQ,WAAyC,gBAClD,SACK,QAAQ,YACT;AACN,YAAM,YAAY,UAAU,YAAa,OAAO,QAAQ,IAAI;AAC5D,UACED,aACC,KAAK,YAAuC,gBAAiB,SAC5D,WAAW,KAEb,cAAc,QACd;AACA,QAAAC,cACE,6BACA,+BAA+B,IAAc,qCACnB,KAAK,SAAS,4HAEC;MAE7C;AASA,WAAK,uBAAuB;AAC5B,UAAI,aAAa,MAAM;AACrB,aAAK,gBAAgB,IAAI;MAC3B,OAAO;AACL,aAAK,aAAa,MAAM,SAAmB;MAC7C;AAEA,WAAK,uBAAuB;IAC9B;EACF;;EAGA,sBAAsB,MAAc,OAAoB;AACtD,UAAM,OAAO,KAAK;AAGlB,UAAM,WAAY,KAAK,yBAA0C,IAAI,IAAI;AAGzE,QAAI,aAAa,UAAa,KAAK,yBAAyB,UAAU;AACpE,YAAM,UAAU,KAAK,mBAAmB,QAAQ;AAChD,YAAM,YACJ,OAAO,QAAQ,cAAc,aACzB,EAAC,eAAe,QAAQ,UAAS,IACjC,QAAQ,WAAW,kBAAkB,SACnC,QAAQ,YACR;AAER,WAAK,uBAAuB;AAC5B,YAAM,iBAAiB,UAAU,cAAe,OAAO,QAAQ,IAAI;AACnE,WAAK,QAAsB,IACzB,kBACA,KAAK,iBAAiB,IAAI,QAAQ;MAEjC;AAEH,WAAK,uBAAuB;IAC9B;EACF;;;;;;;;;;;;;;;EAgBA,cACE,MACA,UACA,SAA6B;AAG7B,QAAI,SAAS,QAAW;AACtB,UAAID,aAAa,gBAA4B,OAAO;AAClD,QAAAC,cACE,IACA,yPAAyP;MAE7P;AACA,YAAM,OAAO,KAAK;AAClB,YAAM,WAAW,KAAK,IAAkB;AACxC,4BAAY,KAAK,mBAAmB,IAAI;AACxC,YAAM,WACH,QAAQ,cAAc,UAAU,UAAU,QAAQ;;;;;;MAOlD,QAAQ,cACP,QAAQ,WACR,aAAa,KAAK,iBAAiB,IAAI,IAAI,KAC3C,CAAC,KAAK,aAAa,KAAK,2BAA2B,MAAM,OAAO,CAAE;AACtE,UAAI,SAAS;AACX,aAAK,iBAAiB,MAAM,UAAU,OAAO;MAC/C,OAAO;AAEL;MACF;IACF;AACA,QAAI,KAAK,oBAAoB,OAAO;AAClC,WAAK,kBAAkB,KAAK,gBAAe;IAC7C;EACF;;;;EAKA,iBACE,MACA,UACA,EAAC,YAAY,SAAS,QAAO,GAC7B,iBAAyB;AAIzB,QAAI,cAAc,EAAE,KAAK,oBAAL,KAAK,kBAAoB,oBAAI,IAAG,IAAI,IAAI,IAAI,GAAG;AACjE,WAAK,gBAAgB,IACnB,MACA,mBAAmB,YAAY,KAAK,IAAkB,CAAC;AAIzD,UAAI,YAAY,QAAQ,oBAAoB,QAAW;AACrD;MACF;IACF;AAGA,QAAI,CAAC,KAAK,oBAAoB,IAAI,IAAI,GAAG;AAGvC,UAAI,CAAC,KAAK,cAAc,CAAC,YAAY;AACnC,mBAAW;MACb;AACA,WAAK,oBAAoB,IAAI,MAAM,QAAQ;IAC7C;AAKA,QAAI,YAAY,QAAQ,KAAK,yBAAyB,MAAM;AAC1D,OAAC,KAAK,2BAAL,KAAK,yBAA2B,oBAAI,IAAG,IAAiB,IAAI,IAAI;IACnE;EACF;;;;EAKQ,MAAM,kBAAe;AAC3B,SAAK,kBAAkB;AACvB,QAAI;AAGF,YAAM,KAAK;IACb,SAAS,GAAG;AAKV,cAAQ,OAAO,CAAC;IAClB;AACA,UAAM,SAAS,KAAK,eAAc;AAIlC,QAAI,UAAU,MAAM;AAClB,YAAM;IACR;AACA,WAAO,CAAC,KAAK;EACf;;;;;;;;;;;;;;;;;;EAmBU,iBAAc;AACtB,UAAM,SAAS,KAAK,cAAa;AACjC,QACED,aACC,KAAK,YAAuC,gBAAiB,SAC5D,sBAAsB,KAExB,OAAQ,QAAoD,SAC1D,YACF;AACA,MAAAC,cACE,wBACA,WAAW,KAAK,SAAS,mIAEM;IAEnC;AACA,WAAO;EACT;;;;;;;;;;;EAYU,gBAAa;AAIrB,QAAI,CAAC,KAAK,iBAAiB;AACzB;IACF;AACA,IAAAG,iBAAgB,EAAC,MAAM,SAAQ,CAAC;AAChC,QAAI,CAAC,KAAK,YAAY;AAGnB,WAA4C,eAA5C,KAA4C,aAC3C,KAAK,iBAAgB;AACvB,UAAIJ,WAAU;AAKZ,cAAM,OAAO,KAAK;AAClB,cAAM,qBAAqB,CAAC,GAAG,KAAK,kBAAkB,KAAI,CAAE,EAAE,OAC5D,CAAC,MAAM,KAAK,eAAe,CAAC,KAAK,KAAK,eAAe,IAAI,CAAC;AAE5D,YAAI,mBAAmB,QAAQ;AAC7B,gBAAM,IAAI,MACR,uCAAuC,KAAK,SAAS,kFAExC,mBAAmB,KAAK,IAAI,CAAC,yKAIjB;QAE7B;MACF;AAEA,UAAI,KAAK,sBAAsB;AAG7B,mBAAW,CAAC,GAAG,KAAK,KAAK,KAAK,sBAAsB;AAClD,eAAK,CAAe,IAAI;QAC1B;AACA,aAAK,uBAAuB;MAC9B;AAUA,YAAM,oBAAqB,KAAK,YAC7B;AACH,UAAI,kBAAkB,OAAO,GAAG;AAC9B,mBAAW,CAAC,GAAG,OAAO,KAAK,mBAAmB;AAC5C,gBAAM,EAAC,QAAO,IAAI;AAClB,gBAAM,QAAQ,KAAK,CAAe;AAClC,cACE,YAAY,QACZ,CAAC,KAAK,oBAAoB,IAAI,CAAC,KAC/B,UAAU,QACV;AACA,iBAAK,iBAAiB,GAAG,QAAW,SAAS,KAAK;UACpD;QACF;MACF;IACF;AACA,QAAI,eAAe;AACnB,UAAM,oBAAoB,KAAK;AAC/B,QAAI;AACF,qBAAe,KAAK,aAAa,iBAAiB;AAClD,UAAI,cAAc;AAChB,aAAK,WAAW,iBAAiB;AACjC,aAAK,eAAe,QAAQ,CAAC,MAAM,EAAE,aAAY,CAAE;AACnD,aAAK,OAAO,iBAAiB;MAC/B,OAAO;AACL,aAAK,cAAa;MACpB;IACF,SAAS,GAAG;AAGV,qBAAe;AAEf,WAAK,cAAa;AAClB,YAAM;IACR;AAEA,QAAI,cAAc;AAChB,WAAK,YAAY,iBAAiB;IACpC;EACF;;;;;;;;;;;;;;;;;;;;;;EAuBU,WAAW,oBAAkC;EAAS;;;EAIhE,YAAY,mBAAiC;AAC3C,SAAK,eAAe,QAAQ,CAAC,MAAM,EAAE,cAAa,CAAE;AACpD,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa;AAClB,WAAK,aAAa,iBAAiB;IACrC;AACA,SAAK,QAAQ,iBAAiB;AAC9B,QACEA,aACA,KAAK,mBACJ,KAAK,YAAuC,gBAAiB,SAC5D,kBAAkB,GAEpB;AACA,MAAAC,cACE,oBACA,WAAW,KAAK,SAAS,8PAIyC;IAEtE;EACF;EAEQ,gBAAa;AACnB,SAAK,sBAAsB,oBAAI,IAAG;AAClC,SAAK,kBAAkB;EACzB;;;;;;;;;;;;;;;;;EAkBA,IAAI,iBAAc;AAChB,WAAO,KAAK,kBAAiB;EAC/B;;;;;;;;;;;;;;;;;;;;;;;;EAyBU,oBAAiB;AACzB,WAAO,KAAK;EACd;;;;;;;;;EAUU,aAAa,oBAAkC;AACvD,WAAO;EACT;;;;;;;;;;EAWU,OAAO,oBAAkC;AAIjD,SAAK,2BAAL,KAAK,yBAA2B,KAAK,uBAAuB,QAAQ,CAAC,MACnE,KAAK,sBAAsB,GAAG,KAAK,CAAe,CAAC,CAAC;AAEtD,SAAK,cAAa;EACpB;;;;;;;;;;;EAYU,QAAQ,oBAAkC;EAAG;;;;;;;;;;;;;;;;;EAkB7C,aAAa,oBAAkC;EAAG;;AAniCrD,gBAAA,gBAA0C,CAAA;AAiT1C,gBAAA,oBAAoC,EAAC,MAAM,OAAM;AAuvBzD,gBACC,0BAA0B,qBAAqB,eAAe,CAAC,IAC7D,oBAAI,IAAG;AACV,gBACC,0BAA0B,aAAa,eAAe,CAAC,IACrD,oBAAI,IAAG;AAGXE,mBAAkB,EAAC,gBAAe,CAAC;AAGnC,IAAIH,WAAU;AAEZ,kBAAgB,kBAAkB;IAChC;IACA;;AAEF,QAAM,oBAAoB,SAAU,MAA4B;AAC9D,QACE,CAAC,KAAK,eAAe,0BAA0B,mBAAmB,IAAI,CAAC,GACvE;AACA,WAAK,kBAAkB,KAAK,gBAAiB,MAAK;IACpD;EACF;AACA,kBAAgB,gBAAgB,SAE9B,SAAoB;AAEpB,sBAAkB,IAAI;AACtB,QAAI,CAAC,KAAK,gBAAiB,SAAS,OAAO,GAAG;AAC5C,WAAK,gBAAiB,KAAK,OAAO;IACpC;EACF;AACA,kBAAgB,iBAAiB,SAE/B,SAAoB;AAEpB,sBAAkB,IAAI;AACtB,UAAM,IAAI,KAAK,gBAAiB,QAAQ,OAAO;AAC/C,QAAI,KAAK,GAAG;AACV,WAAK,gBAAiB,OAAO,GAAG,CAAC;IACnC;EACF;AACF;CAICD,QAAO,4BAAPA,QAAO,0BAA4B,CAAA,IAAI,KAAK,OAAO;AACpD,IAAIC,aAAYD,QAAO,wBAAwB,SAAS,GAAG;AACzD,iBAAe,MAAK;AAClB,IAAAE,cACE,qBACA,gFACuB;EAE3B,CAAC;AACH;;;ACxnDA,IAAMI,6BAA4B,CAChC,MACA,SACM;AAER,IAAMC,YAAW;AAEjB,IAAMC,UAAS;AAEf,IAAIC;AAEJ,IAAIF,WAAU;AAGZ,EAAAC,QAAO,sBAAPA,QAAO,oBAAsB,oBAAI,IAAG;AAOpC,EAAAC,gBAAe,CAAC,MAAc,YAAmB;AAC/C,eAAW,4BAA4B,IAAI;AAC3C,QACE,CAACD,QAAO,kBAAmB,IAAI,OAAO,KACtC,CAACA,QAAO,kBAAmB,IAAI,IAAI,GACnC;AACA,cAAQ,KAAK,OAAO;AACpB,MAAAA,QAAO,kBAAmB,IAAI,OAAO;IACvC;EACF;AACF;AAWM,IAAO,aAAP,cAA0B,gBAAe;EAA/C,cAAA;;AAOW,SAAA,gBAA+B,EAAC,MAAM,KAAI;AAE3C,SAAA,cAAoC;EA8F9C;;;;EAzFqB,mBAAgB;AA/IrC;AAgJI,UAAM,aAAa,MAAM,iBAAgB;AAMzC,eAAK,eAAc,iBAAnB,GAAmB,eAAiB,WAAY;AAChD,WAAO;EACT;;;;;;;;EASmB,OAAO,mBAAiC;AAIzD,UAAM,QAAQ,KAAK,OAAM;AACzB,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,cAAc,cAAc,KAAK;IACxC;AACA,UAAM,OAAO,iBAAiB;AAC9B,SAAK,cAAc,OAAO,OAAO,KAAK,YAAY,KAAK,aAAa;EACtE;;;;;;;;;;;;;;;;;;;;;EAsBS,oBAAiB;AACxB,UAAM,kBAAiB;AACvB,SAAK,aAAa,aAAa,IAAI;EACrC;;;;;;;;;;;;;;;;;;;;EAqBS,uBAAoB;AAC3B,UAAM,qBAAoB;AAC1B,SAAK,aAAa,aAAa,KAAK;EACtC;;;;;;;;EASU,SAAM;AACd,WAAO;EACT;;AApGO,WAAC,eAAe,IAAI;AA8G5B,WACCF,2BAA0B,aAAa,UAAU,CAAC,IAChD;AAGJE,QAAO,2BAA2B,EAAC,WAAU,CAAC;AAG9C,IAAME,mBAAkBH,YACpBC,QAAO,mCACPA,QAAO;AACXE,mBAAkB,EAAC,WAAU,CAAC;CAmC7BC,QAAO,uBAAPA,QAAO,qBAAuB,CAAA,IAAI,KAAK,OAAO;AAC/C,IAAIC,aAAYD,QAAO,mBAAmB,SAAS,GAAG;AACpD,iBAAe,MAAK;AAClB,IAAAE,cACE,qBACA,gFACuB;EAE3B,CAAC;AACH;", "names": ["html", "tag", "strings", "NODE_MODE", "global", "NODE_MODE", "global", "DEV_MODE", "issueWarning", "trustedTypes", "polyfillSupport", "debugLogEvent", "JSCompiler_renameProperty", "DEV_MODE", "global", "issueWarning", "polyfillSupport", "global", "DEV_MODE", "issueWarning"]}