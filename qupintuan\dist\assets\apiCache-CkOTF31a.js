class a{constructor(){this.cache=new Map,this.pendingRequests=new Map}get(e,t=300*1e3){const s=this.cache.get(e);if(!s)return null;const{data:n,timestamp:c}=s;return Date.now()-c>t?(this.cache.delete(e),null):n}set(e,t){this.cache.set(e,{data:t,timestamp:Date.now()})}async execute(e,t,s=300*1e3){const n=this.get(e,s);if(n!==null)return n;if(this.pendingRequests.has(e))return this.pendingRequests.get(e);const c=t().then(i=>(this.set(e,i),this.pendingRequests.delete(e),i)).catch(i=>{throw this.pendingRequests.delete(e),i});return this.pendingRequests.set(e,c),c}clear(e){this.cache.delete(e),this.pendingRequests.delete(e)}clearAll(){this.cache.clear(),this.pendingRequests.clear()}getStats(){return{cacheSize:this.cache.size,pendingRequests:this.pendingRequests.size,cacheKeys:Array.from(this.cache.keys()),pendingKeys:Array.from(this.pendingRequests.keys())}}}const r=new a,u=(h,e,t)=>r.execute(h,e,t);export{u as cacheAPI,r as default};
