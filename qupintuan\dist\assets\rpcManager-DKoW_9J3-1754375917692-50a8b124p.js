const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web3-NCUyEZtP-1754375917692-50a8b124p.js","assets/vendor-D7uqzx8C-1754375917692-50a8b124p.js"])))=>i.map(i=>d[i]);
import{_ as b}from"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";const h=["https://bsc-testnet.public.blastapi.io","https://data-seed-prebsc-1-s1.binance.org:8545","https://data-seed-prebsc-2-s1.binance.org:8545","https://bsc-testnet.blockpi.network/v1/rpc/public","https://data-seed-prebsc-1-s2.binance.org:8545","https://data-seed-prebsc-2-s2.binance.org:8545","https://data-seed-prebsc-1-s3.binance.org:8545","https://data-seed-prebsc-2-s3.binance.org:8545"],f=["https://bsc-dataseed1.binance.org","https://bsc-dataseed2.binance.org","https://bsc-dataseed3.binance.org","https://bsc-dataseed4.binance.org","https://bsc-dataseed1.defibit.io","https://bsc-dataseed2.defibit.io","https://bsc-dataseed3.defibit.io","https://bsc-dataseed4.defibit.io"];async function l(e,s=5e3){try{const r=new AbortController,n=setTimeout(()=>r.abort(),s),a=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({jsonrpc:"2.0",method:"eth_blockNumber",params:[],id:1}),signal:r.signal});if(clearTimeout(n),a.ok){const t=await a.json();return t.result&&t.result.startsWith("0x")}return!1}catch{return!1}}async function g(e){const s=e===97?h:f,r=s.map(async(n,a)=>await l(n,3e3)?{rpc:n,priority:a}:null);try{const a=(await Promise.allSettled(r)).map(t=>t.status==="fulfilled"?t.value:null).filter(Boolean).sort((t,c)=>t.priority-c.priority);if(a.length>0)return a[0].rpc}catch{}return s[0]}async function y(e){const s=e===97?"https://bsc-testnet.public.blastapi.io":"https://bsc-dataseed1.binance.org";return await l(s,3e3)?s:await g(e)}async function m(e=97,s={}){const{timeout:r=1e4,retryCount:n=3,retryDelay:a=1e3}=s,{createPublicClient:t,http:c}=await b(async()=>{const{createPublicClient:i,http:p}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(u=>u.x);return{createPublicClient:i,http:p}},__vite__mapDeps([0,1])),{bscTestnet:d}=await b(async()=>{const{bscTestnet:i}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(p=>p.A);return{bscTestnet:i}},__vite__mapDeps([0,1]));let o;try{o=await y(e)}catch{o=e===97?"https://data-seed-prebsc-1-s1.binance.org:8545":"https://bsc-dataseed1.binance.org"}return t({chain:d,transport:c(o,{timeout:r,retryCount:n,retryDelay:a})})}export{m as createRpcClient,g as getFastestRpc,y as getRpcUrl};
