const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-CaV4ohF9-1754375917692-opz9ain10.js","assets/vendor-D7uqzx8C-1754375917692-50a8b124p.js","assets/web3-NCUyEZtP-1754375917692-50a8b124p.js","assets/index-QZjJZq-p-1754375917692-9oqipqqma.css"])))=>i.map(i=>d[i]);
import{_ as u}from"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";const O=new Map,x=new Map,U=15e3,j=8e3;async function K({chainId:h}){try{const{getContractAddress:c}=await u(async()=>{const{getContractAddress:n}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(o=>o.j);return{getContractAddress:n}},__vite__mapDeps([0,1,2,3])),r=c(h,"GroupBuyRoom"),{ABIS:a}=await u(async()=>{const{ABIS:n}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(o=>o.k);return{ABIS:n}},__vite__mapDeps([0,1,2,3])),p=a.GroupBuyRoom,{createPublicClient:f,http:E}=await u(async()=>{const{createPublicClient:n,http:o}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(R=>R.x);return{createPublicClient:n,http:o}},__vite__mapDeps([2,1])),{bscTestnet:w}=await u(async()=>{const{bscTestnet:n}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(o=>o.A);return{bscTestnet:n}},__vite__mapDeps([2,1]));return await f({chain:w,transport:E()}).readContract({address:r,abi:p,functionName:"totalRooms"})}catch(c){return console.error("获取总房间数量失败:",c),0n}}async function J({chainId:h,roomId:c}){try{const r=BigInt(c),a=`room_${h}_${c}`,p=O.get(a);if(p&&Date.now()-p.timestamp<U)return p.data;if(x.has(a))return await x.get(a);if(r<0)throw new Error(`无效的房间ID: ${c}，房间ID应该大于等于0`);const f=(async()=>{try{const{getContractAddress:E}=await u(async()=>{const{getContractAddress:t}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(e=>e.j);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3])),w=E(h,"GroupBuyRoom"),{ABIS:b}=await u(async()=>{const{ABIS:t}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(e=>e.k);return{ABIS:t}},__vite__mapDeps([0,1,2,3])),T=b.GroupBuyRoom,{createPublicClient:n,http:o}=await u(async()=>{const{createPublicClient:t,http:e}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(m=>m.x);return{createPublicClient:t,http:e}},__vite__mapDeps([2,1])),{bscTestnet:R}=await u(async()=>{const{bscTestnet:t}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(e=>e.A);return{bscTestnet:t}},__vite__mapDeps([2,1])),g=n({chain:R,transport:o()}),G=new Promise((t,e)=>{setTimeout(()=>e(new Error("请求超时")),j)}),H=await Promise.race([g.readContract({address:w,abi:T,functionName:"getRoom",args:[r]}),G]),[M,v,F,V,A,s,N,W,S]=H;let l=null;try{A&&(l=await g.readContract({address:w,abi:T,functionName:"getLotteryInfo",args:[r]}))}catch(t){t.message?.includes("limit exceeded")||console.warn(`获取房间 ${r} 开奖信息失败:`,t.message)}let $=!1;try{A&&($=await g.readContract({address:w,abi:T,functionName:"roomReadyForWinner",args:[r]}))}catch(t){t.message?.includes("limit exceeded")||console.warn(`获取房间 ${r} readyForWinner状态失败:`,t.message)}const y=F.map(t=>t.toLowerCase());let d=null;try{const t=localStorage.getItem(`lottery_${r}`);t&&(d=JSON.parse(t))}catch{}let i=0,_=!1,P=Number(V),D=null;try{D=await g.readContract({address:w,abi:T,functionName:"getRoomTimeInfo",args:[r]})}catch(t){t.message?.includes("limit exceeded")||console.warn(`获取房间 ${r} 时间信息失败:`,t.message)}let C=!1;if(D){const[t,e,m,B,k]=D;P=Number(t),i=Number(k),C=B}else{const t=Math.floor(Date.now()/1e3),e=1440*60,m=Number(V);if(m>1e9){const B=m+e;i=Math.max(0,B-t),C=i===0,P=m}else C=!1,i=0}A?s!=="0x0000000000000000000000000000000000000000"?(_=!1,i=0):d&&d.txHash||y.length>=8?(_=!1,i=i>0?i:3600):(_=!0,i=0):y.length>=8?_=!1:C?(_=!0,i=0):_=!1;const I={id:Number(r),creator:M.toLowerCase(),tier:Number(v),tierAmount:Number(v),participants:y,participantsCount:y.length,maxParticipants:8,createTime:P,timeLeft:i,isExpired:_,isClosed:A,isSuccessful:s,winnerIndex:Number(N),lotteryTxHash:l&&l[2]?l[2]:s!=="0x0000000000000000000000000000000000000000"&&W?W:d?.txHash||null,lotteryTimestamp:l&&l[3]?Number(l[3]):s!=="0x0000000000000000000000000000000000000000"&&S?Number(S):d?.timestamp||null,winner:(()=>{if(s!=="0x0000000000000000000000000000000000000000"){const t=y.findIndex(e=>e===s.toLowerCase());return{index:t>=0?t+1:Number(N)+1,address:s}}return null})(),winnerAddress:s!=="0x0000000000000000000000000000000000000000"?s:null,calculatedWinner:d?.winner||null,winnerInfo:(()=>{if(s!=="0x0000000000000000000000000000000000000000"){const t=y.findIndex(e=>e===s.toLowerCase());return{address:s,winnerAddress:s,index:t>=0?t+1:Number(N)+1,source:"contract"}}if(l&&l[1]!=="0x0000000000000000000000000000000000000000"){const t=l[1],e=y.findIndex(m=>m===t.toLowerCase());return{address:t,winnerAddress:t,index:e>=0?e+1:Number(l[4])+1,source:"contract-lottery"}}return d?.winner?{...d.winner,source:"local"}:null})(),readyForWinner:$||d&&d.txHash,status:A&&s?"completed":A&&!s?"failed":_?"expired":y.length>=8?"full":"active"};let L=I;try{if(I.isClosed||I.participants&&I.participants.length>=8){const{enhanceRoomWithLotteryInfo:e}=await u(async()=>{const{enhanceRoomWithLotteryInfo:m}=await import("./lotteryInfoPersistence-FEnoUD8d-1754375917692-50a8b124p.js");return{enhanceRoomWithLotteryInfo:m}},[]);L=e(I)}else L={...I,readyForWinner:!1,lotteryTxHash:null,lotteryTimestamp:null,calculatedWinner:null,lotteryInfo:null}}catch(t){console.warn(`增强房间 #${I.id} 开奖信息失败:`,t)}return O.set(a,{data:L,timestamp:Date.now()}),L}finally{x.delete(a)}})();return x.set(a,f),await f}catch(r){x.delete(cacheKey);const a=r.message||"";if(a.includes("limit exceeded")||a.includes("Failed to fetch")||a.includes("CORS")||a.includes("403")||a.includes("请求超时")){const f=O.get(cacheKey);return f?f.data:null}throw console.error(`获取房间 ${c} 信息失败:`,r),r}}async function Q(h){try{const{createPublicClient:c,http:r}=await u(async()=>{const{createPublicClient:n,http:o}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(R=>R.x);return{createPublicClient:n,http:o}},__vite__mapDeps([2,1])),{bscTestnet:a}=await u(async()=>{const{bscTestnet:n}=await import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(o=>o.A);return{bscTestnet:n}},__vite__mapDeps([2,1])),{getContractAddress:p}=await u(async()=>{const{getContractAddress:n}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(o=>o.j);return{getContractAddress:n}},__vite__mapDeps([0,1,2,3])),{ABIS:f}=await u(async()=>{const{ABIS:n}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(o=>o.k);return{ABIS:n}},__vite__mapDeps([0,1,2,3])),E=c({chain:a,transport:r()}),w=p(97,"GroupBuyRoom"),b=f.GroupBuyRoom;return{lotteryInfo:await E.readContract({address:w,abi:b,functionName:"getLotteryInfo",args:[BigInt(h)]})}}catch(c){throw console.error(`验证房间#${h} 合约状态失败:`,c),c}}export{Q as debugRoomContractState,J as fetchRoom,K as fetchTotalRooms};
