import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const hemiSepolia = /*#__PURE__*/ define<PERSON>hain({
  id: 743111,
  name: '<PERSON><PERSON>',
  network: '<PERSON><PERSON>',
  nativeCurrency: {
    name: '<PERSON><PERSON>',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://testnet.rpc.hemi.network/rpc'],
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON><PERSON> explorer',
      url: 'https://testnet.explorer.hemi.xyz',
    },
  },
  testnet: true,
})
