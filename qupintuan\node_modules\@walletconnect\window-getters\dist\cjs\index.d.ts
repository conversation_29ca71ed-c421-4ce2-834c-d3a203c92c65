export declare function getFromWindow<T>(name: string): T | undefined;
export declare function getFromWindowOrThrow<T>(name: string): T;
export declare function getDocumentOrThrow(): Document;
export declare function getDocument(): Document | undefined;
export declare function getNavigatorOrThrow(): Navigator;
export declare function getNavigator(): Navigator | undefined;
export declare function getLocationOrThrow(): Location;
export declare function getLocation(): Location | undefined;
export declare function getCryptoOrThrow(): Crypto;
export declare function getCrypto(): Crypto | undefined;
export declare function getLocalStorageOrThrow(): Storage;
export declare function getLocalStorage(): Storage | undefined;
//# sourceMappingURL=index.d.ts.map