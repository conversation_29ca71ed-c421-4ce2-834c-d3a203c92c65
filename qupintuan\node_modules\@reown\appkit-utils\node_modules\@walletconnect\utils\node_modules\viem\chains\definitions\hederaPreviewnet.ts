import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const hederaPreviewnet = /*#__PURE__*/ define<PERSON>hain({
  id: 297,
  name: 'Hedera Previewnet',
  network: 'hedera-previewnet',
  nativeCurrency: {
    symbol: 'HBAR',
    name: '<PERSON>BA<PERSON>',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://previewnet.hashio.io/api'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Hashscan',
      url: 'https://hashscan.io/previewnet',
    },
  },
  testnet: true,
})
