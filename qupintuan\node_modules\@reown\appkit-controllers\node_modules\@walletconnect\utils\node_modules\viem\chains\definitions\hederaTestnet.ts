import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const hederaTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 296,
  name: 'Hedera Testnet',
  network: 'hedera-testnet',
  nativeCurrency: {
    symbol: 'HBAR',
    name: 'HBA<PERSON>',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://testnet.hashio.io/api'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Hashscan',
      url: 'https://hashscan.io/testnet',
    },
  },
  testnet: true,
})
