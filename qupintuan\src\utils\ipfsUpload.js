// src/utils/ipfsUpload.js
// 多种 IPFS 上传服务的备用方案

/**
 * 上传到 Web3.Storage (免费替代方案)
 */
async function uploadToWeb3Storage(file) {
  // Web3.Storage 是一个免费的 IPFS 服务
  // 需要注册获取 API Token: https://web3.storage
  
  const WEB3_STORAGE_TOKEN = import.meta.env?.VITE_WEB3_STORAGE_TOKEN;
  
  if (!WEB3_STORAGE_TOKEN) {
    throw new Error('未配置 Web3.Storage Token');
  }
  
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('https://api.web3.storage/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${WEB3_STORAGE_TOKEN}`,
    },
    body: formData
  });
  
  if (!response.ok) {
    throw new Error(`Web3.Storage 上传失败: ${response.status}`);
  }
  
  const result = await response.json();
  return result.cid; // 返回 IPFS 哈希
}

/**
 * 上传到 Infura IPFS
 */
async function uploadToInfura(file) {
  const INFURA_PROJECT_ID = import.meta.env?.VITE_INFURA_PROJECT_ID;
  const INFURA_PROJECT_SECRET = import.meta.env?.VITE_INFURA_PROJECT_SECRET;
  
  if (!INFURA_PROJECT_ID || !INFURA_PROJECT_SECRET) {
    throw new Error('未配置 Infura 凭据');
  }
  
  const auth = btoa(`${INFURA_PROJECT_ID}:${INFURA_PROJECT_SECRET}`);
  
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('https://ipfs.infura.io:5001/api/v0/add', {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
    },
    body: formData
  });
  
  if (!response.ok) {
    throw new Error(`Infura 上传失败: ${response.status}`);
  }
  
  const result = await response.json();
  return result.Hash; // 返回 IPFS 哈希
}

/**
 * 多账号 Pinata JWT 轮询管理
 */
class PinataJWTManager {
  constructor() {
    this.jwts = [
      import.meta.env?.VITE_PINATA_JWT_1,
      import.meta.env?.VITE_PINATA_JWT_2,
      import.meta.env?.VITE_PINATA_JWT_3,
      import.meta.env?.VITE_PINATA_JWT // 向后兼容
    ].filter(Boolean); // 过滤掉空值

    this.currentIndex = 0;
    this.failedJWTs = new Set(); // 记录失败的JWT

    console.log(`🔑 [PinataJWTManager] 已配置 ${this.jwts.length} 个 Pinata 账号`);
  }

  getNextJWT() {
    if (this.jwts.length === 0) {
      throw new Error('未配置任何 Pinata JWT Token');
    }

    // 找到下一个可用的JWT
    let attempts = 0;
    while (attempts < this.jwts.length) {
      const jwt = this.jwts[this.currentIndex];
      this.currentIndex = (this.currentIndex + 1) % this.jwts.length;

      // 如果这个JWT没有失败过，或者所有JWT都失败过，则使用它
      if (!this.failedJWTs.has(jwt) || this.failedJWTs.size >= this.jwts.length) {
        console.log(`🔄 [PinataJWTManager] 使用账号 ${this.currentIndex} (共${this.jwts.length}个)`);
        return jwt;
      }

      attempts++;
    }

    // 如果所有JWT都失败过，重置失败记录并返回第一个
    this.failedJWTs.clear();
    console.log(`🔄 [PinataJWTManager] 重置失败记录，使用账号 1`);
    return this.jwts[0];
  }

  markJWTAsFailed(jwt) {
    this.failedJWTs.add(jwt);
    console.warn(`⚠️ [PinataJWTManager] 标记账号为失败: ${jwt.substring(0, 20)}...`);
  }

  resetFailedJWTs() {
    this.failedJWTs.clear();
    console.log(`🔄 [PinataJWTManager] 重置所有失败记录`);
  }
}

// 创建全局JWT管理器实例
const pinataJWTManager = new PinataJWTManager();

/**
 * 上传到 Pinata (多账号轮询版本)
 */
async function uploadToPinata(file, options = {}) {
  const PINATA_JWT = pinataJWTManager.getNextJWT();

  if (!PINATA_JWT) {
    throw new Error('未配置 Pinata JWT Token');
  }

  // 验证文件
  if (!file || !file.type.startsWith('image/')) {
    throw new Error('请选择有效的图片文件');
  }

  // 文件大小检查 (5MB限制)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    throw new Error('图片文件不能超过 5MB');
  }

  const formData = new FormData();
  formData.append('file', file);

  // 优化的元数据
  const metadata = {
    name: `qupintuan-product-${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`,
    keyvalues: {
      app: 'qupintuan',
      type: 'product-image',
      uploadTime: new Date().toISOString(),
      fileSize: file.size.toString(),
      ...(options.productId && { productId: options.productId.toString() }),
      ...(options.merchantAddress && { merchant: options.merchantAddress })
    }
  };

  formData.append('pinataMetadata', JSON.stringify(metadata));

  // 添加选项配置
  if (options.pinataOptions) {
    formData.append('pinataOptions', JSON.stringify(options.pinataOptions));
  }

  // 开始 Pinata 上传

  const response = await fetch('https://api.pinata.cloud/pinning/pinFileToIPFS', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${PINATA_JWT}`
    },
    body: formData
  });

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}`;
    try {
      const errorData = await response.json();
      errorMessage = errorData.error?.details || errorData.error?.message || errorMessage;

      // 如果是配额超限或认证失败，标记这个JWT为失败
      if (response.status === 402 || response.status === 401 ||
          errorMessage.includes('quota') || errorMessage.includes('limit')) {
        pinataJWTManager.markJWTAsFailed(PINATA_JWT);
        console.warn(`⚠️ [uploadToPinata] 账号配额或认证问题，切换到下一个账号`);
      }
    } catch (e) {
      errorMessage = await response.text() || errorMessage;
    }
    throw new Error(`Pinata 上传失败: ${errorMessage}`);
  }

  const result = await response.json();
  // Pinata 上传成功

  return result.IpfsHash;
}

/**
 * 智能 IPFS 上传 - 自动尝试多个服务 (优化版本)
 */
export async function uploadToIPFS(file, options = {}) {
  // 开始智能 IPFS 上传

  // 按优先级尝试不同的服务
  const services = [
    { name: 'Pinata', upload: uploadToPinata },
    { name: 'Web3.Storage', upload: uploadToWeb3Storage },
    { name: 'Infura', upload: uploadToInfura }
  ];

  let lastError = null;

  for (const service of services) {
    try {
      // 尝试使用 IPFS 服务上传

      // 添加重试机制 - Pinata 支持多账号轮询
      const maxRetries = service.name === 'Pinata' ? pinataJWTManager.jwts.length : 1;
      let retryCount = 0;

      while (retryCount <= maxRetries) {
        try {
          const hash = await service.upload(file, options);

          // 验证哈希格式
          if (!hash || !hash.startsWith('Qm')) {
            throw new Error('返回的IPFS哈希格式无效');
          }

          // IPFS 上传成功
          return {
            hash,
            service: service.name,
            url: getIPFSImageUrl(hash),
            retries: retryCount,
            account: service.name === 'Pinata' ? `账号${pinataJWTManager.currentIndex}` : undefined
          };
        } catch (error) {
          lastError = error;
          retryCount++;

          if (retryCount <= maxRetries) {
            if (service.name === 'Pinata') {
              console.warn(`⚠️ [ipfsUpload] Pinata 切换到下一个账号 (第${retryCount}次尝试)...`);
            } else {
              console.warn(`⚠️ [ipfsUpload] ${service.name} 第${retryCount}次重试...`);
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // 递增延迟
            }
          }
        }
      }

      console.warn(`⚠️ [ipfsUpload] ${service.name} 上传失败 (${maxRetries + 1}次尝试): ${lastError?.message}`);

    } catch (error) {
      // IPFS 服务不可用
      lastError = error;
      continue;
    }
  }

  // 所有服务都失败的处理

  // 在开发环境下生成模拟哈希，生产环境抛出错误
  if (import.meta.env.DEV) {
    const mockHash = `QmMock${Date.now()}${Math.random().toString(36).substring(2, 8)}`;
    // 开发环境：使用模拟哈希
    return {
      hash: mockHash,
      service: 'Mock',
      url: `https://via.placeholder.com/400x400/f0f0f0/666?text=Mock+Image`,
      isMock: true
    };
  } else {
    throw new Error(`IPFS上传失败: ${lastError?.message || '所有服务都不可用'}`);
  }
}

/**
 * 获取 IPFS 图片 URL (优化版本)
 */
export function getIPFSImageUrl(hash, options = {}) {
  if (!hash) {
    return null;
  }

  // 处理模拟哈希
  if (hash.startsWith('QmMock')) {
    return `https://via.placeholder.com/400x400/f0f0f0/666?text=Mock+Image`;
  }

  // 验证真实的IPFS哈希格式
  if (!hash.startsWith('Qm') || hash.length !== 46) {
    console.warn('⚠️ [getIPFSImageUrl] 无效的IPFS哈希格式:', hash);
    return null;
  }

  // 使用多个 IPFS 网关，提高访问成功率
  const gateways = [
    'https://gateway.pinata.cloud/ipfs/',      // Pinata 官方网关 (最快)
    'https://ipfs.io/ipfs/',                   // IPFS 官方网关
    'https://cloudflare-ipfs.com/ipfs/',       // Cloudflare 网关 (稳定)
    'https://dweb.link/ipfs/',                 // Protocol Labs 网关
    'https://gateway.ipfs.io/ipfs/'            // 备用官方网关
  ];

  // 优先使用指定的网关
  if (options.gateway && options.gateway.endsWith('/')) {
    return `${options.gateway}ipfs/${hash}`;
  }

  // 根据用途选择最佳网关
  let selectedGateway;
  if (options.priority === 'speed') {
    selectedGateway = gateways[0]; // Pinata 最快
  } else if (options.priority === 'stability') {
    selectedGateway = gateways[2]; // Cloudflare 最稳定
  } else {
    // 随机选择网关，分散负载
    selectedGateway = gateways[Math.floor(Math.random() * gateways.length)];
  }

  const url = `${selectedGateway}${hash}`;

  // 添加图片优化参数 (如果网关支持)
  if (selectedGateway.includes('pinata') && options.optimize) {
    const params = new URLSearchParams();
    if (options.width) params.append('img-width', options.width);
    if (options.height) params.append('img-height', options.height);
    if (options.quality) params.append('img-quality', options.quality);

    if (params.toString()) {
      return `${url}?${params.toString()}`;
    }
  }

  return url;
}

/**
 * 批量上传图片到 IPFS
 */
export async function uploadMultipleToIPFS(files, options = {}) {
  console.log(`🔗 [ipfsUpload] 开始批量上传 ${files.length} 张图片`);

  const results = [];
  const errors = [];

  // 并发控制：最多同时上传3张图片
  const concurrency = 3;
  const chunks = [];

  for (let i = 0; i < files.length; i += concurrency) {
    chunks.push(files.slice(i, i + concurrency));
  }

  for (const chunk of chunks) {
    const chunkPromises = chunk.map(async (file, index) => {
      try {
        const result = await uploadToIPFS(file, {
          ...options,
          productId: options.productId,
          merchantAddress: options.merchantAddress
        });

        return {
          index: results.length + index,
          file: file.name,
          success: true,
          ...result
        };
      } catch (error) {
        console.error(`❌ [ipfsUpload] 文件 ${file.name} 上传失败:`, error);
        return {
          index: results.length + index,
          file: file.name,
          success: false,
          error: error.message
        };
      }
    });

    const chunkResults = await Promise.all(chunkPromises);

    chunkResults.forEach(result => {
      if (result.success) {
        results.push(result);
      } else {
        errors.push(result);
      }
    });

    // 批次间短暂延迟，避免API限制
    if (chunks.indexOf(chunk) < chunks.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  console.log(`✅ [ipfsUpload] 批量上传完成: ${results.length}成功, ${errors.length}失败`);

  return {
    success: results,
    errors,
    total: files.length,
    successCount: results.length,
    errorCount: errors.length
  };
}

/**
 * 重置 Pinata 账号失败记录 (管理员功能)
 */
export function resetPinataAccounts() {
  pinataJWTManager.resetFailedJWTs();
  console.log('✅ [ipfsUpload] Pinata 账号失败记录已重置');
}

/**
 * 获取 Pinata 账号状态信息
 */
export function getPinataAccountsStatus() {
  return {
    totalAccounts: pinataJWTManager.jwts.length,
    currentAccount: pinataJWTManager.currentIndex + 1,
    failedAccounts: pinataJWTManager.failedJWTs.size,
    availableAccounts: pinataJWTManager.jwts.length - pinataJWTManager.failedJWTs.size
  };
}
