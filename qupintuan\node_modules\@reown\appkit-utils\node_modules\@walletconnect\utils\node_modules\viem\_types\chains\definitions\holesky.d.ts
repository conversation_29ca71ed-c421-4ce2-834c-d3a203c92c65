export declare const holesky: {
    blockExplorers: {
        readonly default: {
            readonly name: "Etherscan";
            readonly url: "https://holesky.etherscan.io";
            readonly apiUrl: "https://api-holesky.etherscan.io/api";
        };
    };
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
            readonly blockCreated: 77;
        };
        readonly ensRegistry: {
            readonly address: "******************************************";
            readonly blockCreated: 801613;
        };
        readonly ensUniversalResolver: {
            readonly address: "******************************************";
            readonly blockCreated: 973484;
        };
    };
    id: 17000;
    name: "<PERSON><PERSON>";
    nativeCurrency: {
        readonly name: "<PERSON><PERSON> Ether";
        readonly symbol: "ETH";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://ethereum-holesky-rpc.publicnode.com"];
        };
    };
    sourceId?: number | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=holesky.d.ts.map