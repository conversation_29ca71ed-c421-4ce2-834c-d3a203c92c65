const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web3-NCUyEZtP-1754375917692-50a8b124p.js","assets/vendor-D7uqzx8C-1754375917692-50a8b124p.js"])))=>i.map(i=>d[i]);
import{E as f,l as T,i as d}from"./index-CaV4ohF9-1754375917692-opz9ain10.js";import{_ as l}from"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";async function m(o,e){if(typeof o!="function")throw new Error("txFn必须是一个返回Promise的函数");const{onTxHash:t,onReceipt:n,onError:s,timeout:h=12e4}=e||{};if(typeof t!="function"||typeof n!="function"||typeof s!="function")throw new Error("sendTx: onTxHash, onReceipt和onOnError回调都是必需的");if(typeof h!="number"||h<=0)throw new Error("timeout必须是大于0的数字");let r;try{if(r=await o(),typeof r=="string"){t(r);const i=await l(()=>import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(p=>p.F),__vite__mapDeps([0,1])),{BrowserProvider:c}=i,a=await new c(window.ethereum).waitForTransaction(r,1,h);return n(a),a}if(r&&r.receipt&&!r.wait)return console.log("🔍 [sendTx] 处理包含 receipt 的结果对象:",{tx:r,hasRoomId:r.roomId!==void 0,hasReceipt:r.receipt!==void 0,receiptHash:r.receipt?.hash}),t(r.receipt.hash),n(r.receipt),r;if(!r?.wait&&r?.hash){t(r.hash);const i=await l(()=>import("./web3-NCUyEZtP-1754375917692-50a8b124p.js").then(p=>p.F),__vite__mapDeps([0,1])),{BrowserProvider:c}=i,a=await new c(window.ethereum).waitForTransaction(r.hash,1,h);return n(a),a}if(typeof r?.wait!="function")throw new Error("交易对象缺少 .wait() 方法，且不是有效的交易哈希")}catch(i){throw console.error("交易发送失败",i),s({code:"TX_SEND_FAILED",message:"无法发送交易，请检查网络连接或钱包配置",originalError:i}),{code:"TX_SEND_FAILED",message:"无法发送交易，请检查网络连接或钱包配置",originalError:i}}t(r.hash);let _;try{const i=new AbortController,c=setTimeout(()=>i.abort(),h);try{_=await r.wait();const E={..._,logs:_.logs||[]};return clearTimeout(c),n(E),E}catch(E){if(clearTimeout(c),E.name==="AbortError"){const p={code:"TX_CONFIRM_TIMEOUT",message:`交易确认超时(${h}ms)，请稍后手动检查交易状态。交易哈希: ${r?.hash||"未知"}`,originalError:E};throw console.error(p.message,E),s(p),p}const a={code:"TX_EXECUTION_FAILURE",message:`交易执行失败，请检查链上状态或 gas 配置。交易哈希: ${r?.hash||"未知"}`,originalError:E};throw console.error(a.message,a.originalError),s(a),a}}catch(i){console.error("交易执行失败",i);const c={code:"TX_EXECUTION_FAILED",message:"交易执行失败，请检查交易是否被拒绝或gas不足",originalError:i};throw s(c),c}}function w(o,e,u=null){const t=new Error(e);throw t.code=o,u&&(t.original=u),t}function O(o){!o&&(typeof o!="number"||typeof o?.toNumber!="function")&&w(f.MISSING_CHAIN_ID,"未检测到有效的链 ID，请检查钱包连接状态")}function v(o){/^[+]?((\d+(\.\d*)?)|(\.\d+))$/.test(o)||w(f.INVALID_TIER_AMOUNT,"无效的 tierAmount 字符串，必须为非负数字符串");const e=BigInt(o);return e<=0n&&w(f.INVALID_TIER_AMOUNT,"tierAmount 必须大于 0"),e}function D(o){o||w(f.MISSING_SIGNER,"未检测到钱包签名者，请安装 MetaMask 或其他以太坊兼容钱包")}function I(o,e){return m(o,{onTxHash:u=>T(`${e} tx hash`),onReceipt:u=>T(`${e} receipt`),onError:u=>d(`${e} error`)})}async function g(o,e=3,u=3e3){let t;for(let n=1;n<=e;n++)try{T(`交易尝试 ${n}/${e}`);const s=await o();return T(`交易成功，尝试次数: ${n}`),s}catch(s){t=s,d(`交易失败，尝试 ${n}/${e}:`),n<e&&await new Promise(h=>setTimeout(h,u))}w(f.NETWORK_ERROR,`交易失败，已重试 ${e} 次`,t)}const R=Object.freeze(Object.defineProperty({__proto__:null,executeTransaction:I,retryTransaction:g},Symbol.toStringTag,{value:"Module"}));export{O as a,R as b,I as e,v as p,w as t,D as v};
