{"version": 3, "file": "index.cjs.js", "sources": ["../src/node-js/lib/db.ts", "../src/node-js/lib/nodeMigration.ts", "../src/node-js/index.ts", "../src/shared/types.ts", "../src/shared/utils.ts"], "sourcesContent": ["import { safeJsonStringify } from \"@walletconnect/safe-json\";\n\nfunction importLib() {\n  try {\n    const db = require(\"unstorage\");\n    const driver = require(\"unstorage/drivers/fs-lite\");\n    return {\n      db,\n      driver,\n    };\n  } catch (e) {\n    // User didn't install db dependency, show detailed error\n    throw new Error(\n      `To use WalletConnect server side, you'll need to install the \"unstorage\" dependency. If you are seeing this error during a build / in an SSR environment, you can add \"unstorage\" as a devDependency to make this error go away.`,\n    );\n  }\n}\n\ninterface DbKeyValueStorageOptions {\n  dbName: string;\n}\n\nlet StorageLib: any;\ntype ActionType = \"setItem\" | \"removeItem\";\ntype UpdateType = {\n  state: \"active\" | \"idle\";\n  actions: {\n    key: string;\n    value: unknown;\n    action: ActionType;\n    callback: (value: void | PromiseLike<void>) => void;\n  }[];\n};\nexport const MEMORY_DB = \":memory:\";\n\nexport default class Db {\n  private static instances: Record<string, Db> = {};\n  public database;\n  private writeActionsQueue: UpdateType = {\n    state: \"idle\",\n    actions: [],\n  };\n\n  private constructor(opts: DbKeyValueStorageOptions) {\n    if (!StorageLib) {\n      StorageLib = importLib();\n    }\n\n    if (opts?.dbName === MEMORY_DB) {\n      this.database = StorageLib.db.createStorage();\n    } else {\n      this.database = StorageLib.db.createStorage({\n        driver: StorageLib.driver({\n          base: opts?.dbName,\n        }),\n      });\n    }\n  }\n\n  public static create(opts: DbKeyValueStorageOptions): Db {\n    const dbName = opts.dbName;\n    if (dbName === MEMORY_DB) {\n      return new Db(opts);\n    }\n\n    if (!Db.instances[dbName]) {\n      Db.instances[dbName] = new Db(opts);\n    }\n    return Db.instances[dbName];\n  }\n\n  public async getKeys(): Promise<string[]> {\n    return this.database.getKeys();\n  }\n\n  public async getEntries<T = any>(): Promise<[string, T][]> {\n    const entries = await this.database.getItems(await this.database.getKeys());\n    return entries.map((item: any) => [item.key, item.value] as [string, T]);\n  }\n\n  private async onWriteAction(params: {\n    key: string;\n    value?: unknown;\n    action: ActionType;\n  }): Promise<void> {\n    const { key, value, action } = params;\n    let resolveLock: (value: void | PromiseLike<void>) => void = () => ({});\n    const lock = new Promise<void>((resolve) => (resolveLock = resolve));\n    this.writeActionsQueue.actions.push({ key, value, action, callback: resolveLock });\n    if (this.writeActionsQueue.state === \"idle\") {\n      this.startWriteActions();\n    }\n    await lock;\n  }\n\n  private async startWriteActions(): Promise<void> {\n    if (this.writeActionsQueue.actions.length === 0) {\n      this.writeActionsQueue.state = \"idle\";\n      return;\n    }\n    this.writeActionsQueue.state = \"active\";\n    while (this.writeActionsQueue.actions.length > 0) {\n      const writeAction = this.writeActionsQueue.actions.shift();\n      if (!writeAction) continue;\n      const { key, value, action, callback } = writeAction;\n      switch (action) {\n        case \"setItem\":\n          await this.database.setItem(key);\n          await this.database.setItem(key, safeJsonStringify(value));\n          break;\n        case \"removeItem\":\n          await this.database.removeItem(key);\n          break;\n      }\n      callback();\n    }\n    this.writeActionsQueue.state = \"idle\";\n  }\n\n  public async getItem<T = any>(key: string): Promise<T | undefined> {\n    const item = await this.database.getItem(key);\n    if (item === null) {\n      return undefined;\n    }\n    return item as T;\n  }\n\n  public async setItem<_T = any>(key: string, value: any): Promise<void> {\n    await this.onWriteAction({ key, value, action: \"setItem\" });\n  }\n\n  public async removeItem(key: string): Promise<void> {\n    await this.onWriteAction({ key, action: \"removeItem\" });\n  }\n}\n", "import { safeJsonParse } from \"@walletconnect/safe-json\";\nimport { IKeyValueStorage } from \"../../shared\";\nimport { MEMORY_DB } from \"./db\";\nimport fs from \"fs\";\nconst VERSION_KEY = \"wc_storage_version\";\nconst TO_MIGRATE_SUFFIX = \".to_migrate\";\nconst MIGRATED_SUFFIX = \".migrated\";\nconst DB_VERSION = 1;\n\nexport const migrate = async (\n  fromStore: string,\n  toStore: IKeyValueStorage,\n  onCompleteCallback: () => void,\n) => {\n  if (fromStore === MEMORY_DB) {\n    onCompleteCallback();\n    return;\n  }\n  const versionKey = VERSION_KEY;\n  const currentVersion = await toStore.getItem<number>(versionKey);\n  if (currentVersion && currentVersion >= DB_VERSION) {\n    onCompleteCallback();\n    return;\n  }\n  const rawContents = await readFile(`${fromStore}${TO_MIGRATE_SUFFIX}`);\n  if (!rawContents) {\n    onCompleteCallback();\n    return;\n  }\n  const contents = safeJsonParse(rawContents);\n  if (!contents) {\n    onCompleteCallback();\n    return;\n  }\n  const collection = contents?.collections?.[0];\n\n  const items = collection?.data;\n  if (!items || !items.length) {\n    onCompleteCallback();\n    return;\n  }\n\n  while (items.length) {\n    const item = items.shift();\n    if (!item) {\n      continue;\n    }\n    const { id, value } = item;\n    await toStore.setItem(id, safeJsonParse(value));\n  }\n\n  await toStore.setItem(versionKey, DB_VERSION);\n  renameFile(`${fromStore}${TO_MIGRATE_SUFFIX}`, `${fromStore}${MIGRATED_SUFFIX}`);\n  onCompleteCallback();\n};\n\nconst readFile = async (path: string) => {\n  return await new Promise<string | undefined>((resolve) => {\n    fs.readFile(path, { encoding: \"utf8\" }, (err, data) => {\n      if (err) {\n        resolve(undefined);\n      }\n      resolve(data);\n    });\n  });\n};\n\nexport const beforeMigrate = (fromStore: string) => {\n  if (fromStore === MEMORY_DB) return;\n  if (!fs.existsSync(fromStore)) return;\n  if (fs.lstatSync(fromStore).isDirectory()) return;\n  renameFile(fromStore, `${fromStore}${TO_MIGRATE_SUFFIX}`);\n};\n\nconst renameFile = (from: string, to: string) => {\n  try {\n    fs.renameSync(from, to);\n  } catch (e) {}\n};\n", "import { IKeyValueStorage, KeyValueStorageOptions } from \"../shared\";\n\nimport Db from \"./lib/db\";\nimport { beforeMigrate, migrate } from \"./lib/nodeMigration\";\nconst DB_NAME = \"walletconnect.db\";\nexport class KeyValueStorage implements IKeyValueStorage {\n  private database: typeof Db.prototype;\n  private initialized = false;\n\n  constructor(opts?: KeyValueStorageOptions) {\n    const dbName = opts?.database || opts?.table || DB_NAME;\n    beforeMigrate(dbName);\n    this.database = Db.create({\n      dbName,\n    });\n    migrate(dbName, this.database, this.setInitialized);\n  }\n\n  private setInitialized = () => {\n    this.initialized = true;\n  };\n\n  public async getKeys(): Promise<string[]> {\n    await this.initialize();\n    return this.database.getKeys();\n  }\n\n  public async getEntries<T = any>(): Promise<[string, T][]> {\n    await this.initialize();\n    return this.database.getEntries();\n  }\n\n  public async getItem<T = any>(key: string): Promise<T | undefined> {\n    await this.initialize();\n    return this.database.getItem(key);\n  }\n\n  public async setItem<_T = any>(key: string, value: any): Promise<void> {\n    await this.initialize();\n    await this.database.setItem(key, value);\n  }\n\n  public async removeItem(key: string): Promise<void> {\n    await this.initialize();\n    await this.database.removeItem(key);\n  }\n\n  private async initialize() {\n    if (this.initialized) {\n      return;\n    }\n    await new Promise<void>((resolve) => {\n      const interval = setInterval(() => {\n        if (this.initialized) {\n          clearInterval(interval);\n          resolve();\n        }\n      }, 20);\n    });\n  }\n}\n\nexport default KeyValueStorage;\n", "export interface KeyValueStorageOptions {\n  database?: string;\n  table?: string;\n}\n\nexport abstract class IKeyValueStorage {\n  public abstract getKeys(): Promise<string[]>;\n  public abstract getEntries<T = any>(): Promise<[string, T][]>;\n  public abstract getItem<T = any>(key: string): Promise<T | undefined>;\n  public abstract setItem<T = any>(key: string, value: T): Promise<void>;\n  public abstract removeItem(key: string): Promise<void>;\n}\n", "import { safeJsonParse } from \"@walletconnect/safe-json\";\n\nexport function parseEntry(entry: [string, string | null]): [string, any] {\n  return [entry[0], safeJsonParse(entry[1] ?? \"\")];\n}\n"], "names": ["importLib", "db", "driver", "e", "StorageLib", "_Db", "opts", "dbN<PERSON>", "item", "params", "key", "value", "action", "resolveLock", "lock", "resolve", "writeAction", "callback", "safeJsonStringify", "Db", "VERSION_KEY", "TO_MIGRATE_SUFFIX", "MIGRATED_SUFFIX", "DB_VERSION", "fromStore", "toStore", "onCompleteCallback", "_a", "MEMORY_DB", "version<PERSON>ey", "currentVersion", "rawContents", "readFile", "contents", "safeJsonParse", "collection", "items", "id", "renameFile", "path", "fs", "err", "data", "from", "to", "DB_NAME", "beforeMigrate", "migrate", "interval", "entry"], "mappings": ";;;;;;;;;;;AAEA,SAASA,CAAY,EAAA,CACnB,GAAI,CACF,MAAMC,CAAK,CAAA,OAAA,CAAQ,WAAW,CACxBC,CAAAA,CAAAA,CAAS,QAAQ,2BAA2B,CAAA,CAClD,OAAO,CACL,EAAA,CAAAD,CACA,CAAA,MAAA,CAAAC,CACF,CACF,CAAA,MAASC,EAAP,CAEA,MAAM,IAAI,KACR,CAAA,CAAA,gOAAA,CACF,CACF,CACF,CAMA,IAAIC,CAAAA,OAWS,SAAY,CAAA,UAAA,CAEzB,MAAqBC,CAArB,CAAA,KAAwB,CAQd,WAAA,CAAYC,EAAgC,CALpD,IAAA,CAAQ,kBAAgC,CACtC,KAAA,CAAO,OACP,OAAS,CAAA,EACX,CAAA,CAGOF,IACHA,CAAaJ,CAAAA,CAAAA,KAGXM,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,EAAM,MAAW,IAAA,SAAA,CACnB,IAAK,CAAA,QAAA,CAAWF,EAAW,EAAG,CAAA,aAAA,GAE9B,IAAK,CAAA,QAAA,CAAWA,EAAW,EAAG,CAAA,aAAA,CAAc,CAC1C,MAAQA,CAAAA,CAAAA,CAAW,OAAO,CACxB,IAAA,CAAME,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,MACd,CAAC,CACH,CAAC,EAEL,CAEA,OAAc,MAAA,CAAOA,EAAoC,CACvD,MAAMC,EAASD,CAAK,CAAA,MAAA,CACpB,OAAIC,CAAAA,GAAW,UACN,IAAIF,CAAAA,CAAGC,CAAI,CAGfD,EAAAA,CAAAA,CAAG,UAAUE,CAAM,CAAA,GACtBF,CAAG,CAAA,SAAA,CAAUE,CAAM,CAAI,CAAA,IAAIF,EAAGC,CAAI,CAAA,CAAA,CAE7BD,EAAG,SAAUE,CAAAA,CAAM,EAC5B,CAEA,MAAa,SAA6B,CACxC,OAAO,KAAK,QAAS,CAAA,OAAA,EACvB,CAEA,MAAa,UAA8C,EAAA,CAEzD,QADgB,MAAM,IAAA,CAAK,SAAS,QAAS,CAAA,MAAM,KAAK,QAAS,CAAA,OAAA,EAAS,CAAA,EAC3D,IAAKC,CAAc,EAAA,CAACA,EAAK,GAAKA,CAAAA,CAAAA,CAAK,KAAK,CAAgB,CACzE,CAEA,MAAc,cAAcC,CAIV,CAAA,CAChB,KAAM,CAAE,GAAA,CAAAC,EAAK,KAAAC,CAAAA,CAAAA,CAAO,OAAAC,CAAO,CAAA,CAAIH,EAC/B,IAAII,CAAAA,CAAyD,KAAO,EAAC,CAAA,CACrE,MAAMC,CAAO,CAAA,IAAI,OAAeC,CAAAA,CAAAA,EAAaF,EAAcE,CAAQ,CAAA,CACnE,KAAK,iBAAkB,CAAA,OAAA,CAAQ,KAAK,CAAE,GAAA,CAAAL,CAAK,CAAA,KAAA,CAAAC,EAAO,MAAAC,CAAAA,CAAAA,CAAQ,SAAUC,CAAY,CAAC,EAC7E,IAAK,CAAA,iBAAA,CAAkB,KAAU,GAAA,MAAA,EACnC,KAAK,iBAAkB,EAAA,CAEzB,MAAMC,EACR,CAEA,MAAc,iBAAmC,EAAA,CAC/C,GAAI,IAAA,CAAK,kBAAkB,OAAQ,CAAA,MAAA,GAAW,EAAG,CAC/C,IAAA,CAAK,kBAAkB,KAAQ,CAAA,MAAA,CAC/B,MAGF,CAAA,IADA,KAAK,iBAAkB,CAAA,KAAA,CAAQ,SACxB,IAAK,CAAA,iBAAA,CAAkB,QAAQ,MAAS,CAAA,CAAA,EAAG,CAChD,MAAME,EAAc,IAAK,CAAA,iBAAA,CAAkB,QAAQ,KAAM,EAAA,CACzD,GAAI,CAACA,CAAAA,CAAa,SAClB,KAAM,CAAE,GAAAN,CAAAA,CAAAA,CAAK,MAAAC,CAAO,CAAA,MAAA,CAAAC,EAAQ,QAAAK,CAAAA,CAAS,EAAID,CACzC,CAAA,OAAQJ,GACN,IAAK,UACH,MAAM,IAAA,CAAK,SAAS,OAAQF,CAAAA,CAAG,CAC/B,CAAA,MAAM,KAAK,QAAS,CAAA,OAAA,CAAQA,EAAKQ,0BAAkBP,CAAAA,CAAK,CAAC,CACzD,CAAA,MACF,IAAK,YAAA,CACH,MAAM,IAAK,CAAA,QAAA,CAAS,WAAWD,CAAG,CAAA,CAClC,KACJ,CACAO,CAAAA,GAEF,CAAA,IAAA,CAAK,kBAAkB,KAAQ,CAAA,OACjC,CAEA,MAAa,OAAA,CAAiBP,EAAqC,CACjE,MAAMF,EAAO,MAAM,IAAA,CAAK,SAAS,OAAQE,CAAAA,CAAG,EAC5C,GAAIF,CAAAA,GAAS,KAGb,OAAOA,CACT,CAEA,MAAa,QAAkBE,CAAaC,CAAAA,CAAAA,CAA2B,CACrE,MAAM,IAAA,CAAK,cAAc,CAAE,GAAA,CAAAD,CAAK,CAAA,KAAA,CAAAC,EAAO,MAAQ,CAAA,SAAU,CAAC,EAC5D,CAEA,MAAa,UAAWD,CAAAA,CAAAA,CAA4B,CAClD,MAAM,KAAK,aAAc,CAAA,CAAE,IAAAA,CAAK,CAAA,MAAA,CAAQ,YAAa,CAAC,EACxD,CACF,CAnGA,CAAA,IAAqBS,EAArBd,CAAqBc,CAAAA,CAAAA,CACJ,UAAgC,EAAC;;AChClD,MAAMC,CAAAA,CAAc,qBACdC,CAAoB,CAAA,aAAA,CACpBC,CAAkB,CAAA,WAAA,CAClBC,CAAa,CAAA,CAAA,OAEN,OAAU,CAAA,MACrBC,CACAC,CAAAA,CAAAA,CACAC,CACG,GAAA,CAbL,IAAAC,CAcE,CAAA,GAAIH,CAAcI,GAAAA,SAAAA,CAAW,CAC3BF,CAAAA,GACA,MAEF,CAAA,MAAMG,CAAaT,CAAAA,CAAAA,CACbU,CAAiB,CAAA,MAAML,EAAQ,OAAgBI,CAAAA,CAAU,CAC/D,CAAA,GAAIC,CAAkBA,EAAAA,CAAAA,EAAkBP,EAAY,CAClDG,CAAAA,EACA,CAAA,MAAA,CAEF,MAAMK,CAAAA,CAAc,MAAMC,CAAS,CAAA,CAAA,EAAGR,CAAYH,CAAAA,EAAAA,CAAAA,CAAAA,CAAmB,CACrE,CAAA,GAAI,CAACU,CAAa,CAAA,CAChBL,CAAmB,EAAA,CACnB,MAEF,CAAA,MAAMO,EAAWC,sBAAcH,CAAAA,CAAW,CAC1C,CAAA,GAAI,CAACE,CAAAA,CAAU,CACbP,CAAmB,EAAA,CACnB,MAEF,CAAA,MAAMS,CAAaR,CAAAA,CAAAA,CAAAA,CAAAM,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAU,WAAV,GAAA,IAAA,CAAA,KAAA,CAAA,CAAAN,CAAwB,CAAA,CAAA,CAAA,CAErCS,EAAQD,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,CAAAA,CAAY,CAAA,IAAA,CAC1B,GAAI,CAACC,GAAS,CAACA,CAAAA,CAAM,MAAQ,CAAA,CAC3BV,CAAmB,EAAA,CACnB,OAGF,KAAOU,CAAAA,CAAM,MAAQ,EAAA,CACnB,MAAM5B,CAAAA,CAAO4B,EAAM,KAAM,EAAA,CACzB,GAAI,CAAC5B,CACH,CAAA,SAEF,KAAM,CAAE,EAAA,CAAA6B,CAAI,CAAA,KAAA,CAAA1B,CAAM,CAAA,CAAIH,EACtB,MAAMiB,CAAAA,CAAQ,OAAQY,CAAAA,CAAAA,CAAIH,sBAAcvB,CAAAA,CAAK,CAAC,EAGhD,CAAA,MAAMc,CAAQ,CAAA,OAAA,CAAQI,CAAYN,CAAAA,CAAU,EAC5Ce,CAAW,CAAA,CAAA,EAAGd,CAAYH,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAqB,CAAGG,EAAAA,CAAAA,CAAAA,EAAYF,GAAiB,CAC/EI,CAAAA,CAAAA,GACF,CAAA,CAEA,MAAMM,CAAAA,CAAW,MAAOO,CACf,EAAA,MAAM,IAAI,OAAA,CAA6BxB,CAAY,EAAA,CACxDyB,sBAAG,QAASD,CAAAA,CAAAA,CAAM,CAAE,QAAA,CAAU,MAAO,CAAA,CAAG,CAACE,CAAKC,CAAAA,CAAAA,GAAS,CACjDD,CAAAA,EACF1B,CAAQ,CAAA,KAAA,CAAS,EAEnBA,CAAQ2B,CAAAA,CAAI,EACd,CAAC,EACH,CAAC,EAGU,MAAA,aAAA,CAAiBlB,CAAsB,EAAA,CAC9CA,CAAcI,GAAAA,SAAAA,EACbY,sBAAG,UAAWhB,CAAAA,CAAS,CACxBgB,GAAAA,qBAAAA,CAAG,SAAUhB,CAAAA,CAAS,EAAE,WAAY,EAAA,EACxCc,CAAWd,CAAAA,CAAAA,CAAW,CAAGA,EAAAA,CAAAA,CAAAA,EAAYH,GAAmB,CAC1D,EAAA,CAAA,CAEA,MAAMiB,CAAAA,CAAa,CAACK,CAAAA,CAAcC,IAAe,CAC/C,GAAI,CACFJ,qBAAAA,CAAG,UAAWG,CAAAA,CAAAA,CAAMC,CAAE,EACxB,CAAA,MAASzC,CAAP,CAAA,EACJ,CAAA;;AC1EA,MAAM0C,CAAAA,CAAU,mBACH,MAAA,eAA4C,CAIvD,WAAA,CAAYvC,EAA+B,CAF3C,IAAA,CAAQ,WAAc,CAAA,CAAA,CAAA,CAWtB,KAAQ,cAAiB,CAAA,IAAM,CAC7B,IAAA,CAAK,YAAc,CACrB,EAAA,CAAA,CAVE,MAAMC,CAAAA,CAAAA,CAASD,GAAA,IAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAM,QAAYA,IAAAA,CAAAA,EAAA,YAAAA,CAAM,CAAA,KAAA,CAAA,EAASuC,CAChDC,CAAAA,aAAAA,CAAcvC,CAAM,CACpB,CAAA,IAAA,CAAK,QAAWY,CAAAA,CAAAA,CAAG,OAAO,CACxB,MAAA,CAAAZ,CACF,CAAC,EACDwC,OAAQxC,CAAAA,CAAAA,CAAQ,IAAK,CAAA,QAAA,CAAU,KAAK,cAAc,EACpD,CAMA,MAAa,SAA6B,CACxC,OAAA,MAAM,IAAK,CAAA,UAAA,GACJ,IAAK,CAAA,QAAA,CAAS,OAAQ,EAC/B,CAEA,MAAa,UAAA,EAA8C,CACzD,OAAM,MAAA,IAAA,CAAK,YACJ,CAAA,IAAA,CAAK,QAAS,CAAA,UAAA,EACvB,CAEA,MAAa,OAAiBG,CAAAA,CAAAA,CAAqC,CACjE,OAAM,MAAA,IAAA,CAAK,UAAW,EAAA,CACf,KAAK,QAAS,CAAA,OAAA,CAAQA,CAAG,CAClC,CAEA,MAAa,OAAA,CAAkBA,CAAaC,CAAAA,CAAAA,CAA2B,CACrE,MAAM,IAAA,CAAK,UAAW,EAAA,CACtB,MAAM,IAAK,CAAA,QAAA,CAAS,OAAQD,CAAAA,CAAAA,CAAKC,CAAK,EACxC,CAEA,MAAa,UAAA,CAAWD,EAA4B,CAClD,MAAM,IAAK,CAAA,UAAA,GACX,MAAM,IAAA,CAAK,QAAS,CAAA,UAAA,CAAWA,CAAG,EACpC,CAEA,MAAc,UAAA,EAAa,CACrB,IAAK,CAAA,WAAA,EAGT,MAAM,IAAI,QAAeK,CAAY,EAAA,CACnC,MAAMiC,CAAAA,CAAW,YAAY,IAAM,CAC7B,IAAK,CAAA,WAAA,GACP,cAAcA,CAAQ,CAAA,CACtBjC,CAAQ,EAAA,EAEZ,EAAG,EAAE,EACP,CAAC,EACH,CACF;;ACvDO,MAAe,gBAAiB;;ACHhC,SAAS,WAAWkC,CAA+C,CAAA,CAF1E,IAAAtB,CAAAA,CAGE,OAAO,CAACsB,EAAM,CAAC,CAAA,CAAGf,sBAAcP,CAAAA,CAAAA,CAAAA,CAAAsB,CAAM,CAAA,CAAC,IAAP,IAAAtB,CAAAA,CAAAA,CAAY,EAAE,CAAC,CACjD;;;;;;;"}