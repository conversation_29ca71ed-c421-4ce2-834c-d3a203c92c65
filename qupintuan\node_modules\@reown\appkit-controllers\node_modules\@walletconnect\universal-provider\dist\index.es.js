import $t from"@walletconnect/sign-client";import{isValidObject as G,isCaipNamespace as Z,parseNamespaceKey as _,mergeArrays as J,parse<PERSON>hainId as T,getSdkError as tt,isValidArray as z}from"@walletconnect/utils";import{pino as Ot,getDefaultLoggerOptions as At}from"@walletconnect/logger";import g,{HttpConnection as Ct}from"@walletconnect/jsonrpc-http-connection";import{JsonRpcProvider as f}from"@walletconnect/jsonrpc-provider";import{formatJsonRpcRequest as Ht,formatJsonRpcResult as Et}from"@walletconnect/jsonrpc-utils";import Nt from"events";const et="error",St="wss://relay.walletconnect.org",Dt="wc",qt="universal_provider",U=`${Dt}@2:${qt}:`,st="https://rpc.walletconnect.org/v1/",I="generic",jt=`${st}bundler`,u={DEFAULT_CHAIN_CHANGED:"default_chain_changed"};function Rt(){}function k(s){return s==null||typeof s!="object"&&typeof s!="function"}function W(s){return ArrayBuffer.isView(s)&&!(s instanceof DataView)}function _t(s){if(k(s))return s;if(Array.isArray(s)||W(s)||s instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&s instanceof SharedArrayBuffer)return s.slice(0);const t=Object.getPrototypeOf(s),e=t.constructor;if(s instanceof Date||s instanceof Map||s instanceof Set)return new e(s);if(s instanceof RegExp){const i=new e(s);return i.lastIndex=s.lastIndex,i}if(s instanceof DataView)return new e(s.buffer.slice(0));if(s instanceof Error){const i=new e(s.message);return i.stack=s.stack,i.name=s.name,i.cause=s.cause,i}if(typeof File<"u"&&s instanceof File)return new e([s],s.name,{type:s.type,lastModified:s.lastModified});if(typeof s=="object"){const i=Object.create(t);return Object.assign(i,s)}return s}function it(s){return typeof s=="object"&&s!==null}function rt(s){return Object.getOwnPropertySymbols(s).filter(t=>Object.prototype.propertyIsEnumerable.call(s,t))}function nt(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(s)}const Ut="[object RegExp]",at="[object String]",ct="[object Number]",ot="[object Boolean]",ht="[object Arguments]",Ft="[object Symbol]",Lt="[object Date]",Mt="[object Map]",xt="[object Set]",Bt="[object Array]",Gt="[object ArrayBuffer]",Jt="[object Object]",zt="[object DataView]",kt="[object Uint8Array]",Wt="[object Uint8ClampedArray]",Kt="[object Uint16Array]",Vt="[object Uint32Array]",Xt="[object Int8Array]",Yt="[object Int16Array]",Qt="[object Int32Array]",Zt="[object Float32Array]",Tt="[object Float64Array]";function te(s,t){return $(s,void 0,s,new Map,t)}function $(s,t,e,i=new Map,n=void 0){const a=n?.(s,t,e,i);if(a!=null)return a;if(k(s))return s;if(i.has(s))return i.get(s);if(Array.isArray(s)){const r=new Array(s.length);i.set(s,r);for(let c=0;c<s.length;c++)r[c]=$(s[c],c,e,i,n);return Object.hasOwn(s,"index")&&(r.index=s.index),Object.hasOwn(s,"input")&&(r.input=s.input),r}if(s instanceof Date)return new Date(s.getTime());if(s instanceof RegExp){const r=new RegExp(s.source,s.flags);return r.lastIndex=s.lastIndex,r}if(s instanceof Map){const r=new Map;i.set(s,r);for(const[c,o]of s)r.set(c,$(o,c,e,i,n));return r}if(s instanceof Set){const r=new Set;i.set(s,r);for(const c of s)r.add($(c,void 0,e,i,n));return r}if(typeof Buffer<"u"&&Buffer.isBuffer(s))return s.subarray();if(W(s)){const r=new(Object.getPrototypeOf(s)).constructor(s.length);i.set(s,r);for(let c=0;c<s.length;c++)r[c]=$(s[c],c,e,i,n);return r}if(s instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&s instanceof SharedArrayBuffer)return s.slice(0);if(s instanceof DataView){const r=new DataView(s.buffer.slice(0),s.byteOffset,s.byteLength);return i.set(s,r),y(r,s,e,i,n),r}if(typeof File<"u"&&s instanceof File){const r=new File([s],s.name,{type:s.type});return i.set(s,r),y(r,s,e,i,n),r}if(s instanceof Blob){const r=new Blob([s],{type:s.type});return i.set(s,r),y(r,s,e,i,n),r}if(s instanceof Error){const r=new s.constructor;return i.set(s,r),r.message=s.message,r.name=s.name,r.stack=s.stack,r.cause=s.cause,y(r,s,e,i,n),r}if(typeof s=="object"&&ee(s)){const r=Object.create(Object.getPrototypeOf(s));return i.set(s,r),y(r,s,e,i,n),r}return s}function y(s,t,e=s,i,n){const a=[...Object.keys(t),...rt(t)];for(let r=0;r<a.length;r++){const c=a[r],o=Object.getOwnPropertyDescriptor(s,c);(o==null||o.writable)&&(s[c]=$(t[c],c,e,i,n))}}function ee(s){switch(nt(s)){case ht:case Bt:case Gt:case zt:case ot:case Lt:case Zt:case Tt:case Xt:case Yt:case Qt:case Mt:case ct:case Jt:case Ut:case xt:case at:case Ft:case kt:case Wt:case Kt:case Vt:return!0;default:return!1}}function se(s,t){return te(s,(e,i,n,a)=>{const r=t?.(e,i,n,a);if(r!=null)return r;if(typeof s=="object")switch(Object.prototype.toString.call(s)){case ct:case at:case ot:{const c=new s.constructor(s?.valueOf());return y(c,s),c}case ht:{const c={};return y(c,s),c.length=s.length,c[Symbol.iterator]=s[Symbol.iterator],c}default:return}})}function pt(s){return se(s)}function dt(s){return s!==null&&typeof s=="object"&&nt(s)==="[object Arguments]"}function ie(s){return W(s)}function re(s){if(typeof s!="object"||s==null)return!1;if(Object.getPrototypeOf(s)===null)return!0;if(Object.prototype.toString.call(s)!=="[object Object]"){const e=s[Symbol.toStringTag];return e==null||!Object.getOwnPropertyDescriptor(s,Symbol.toStringTag)?.writable?!1:s.toString()===`[object ${e}]`}let t=s;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(s)===t}function ne(s,...t){const e=t.slice(0,-1),i=t[t.length-1];let n=s;for(let a=0;a<e.length;a++){const r=e[a];n=F(n,r,i,new Map)}return n}function F(s,t,e,i){if(k(s)&&(s=Object(s)),t==null||typeof t!="object")return s;if(i.has(t))return _t(i.get(t));if(i.set(t,s),Array.isArray(t)){t=t.slice();for(let a=0;a<t.length;a++)t[a]=t[a]??void 0}const n=[...Object.keys(t),...rt(t)];for(let a=0;a<n.length;a++){const r=n[a];let c=t[r],o=s[r];if(dt(c)&&(c={...c}),dt(o)&&(o={...o}),typeof Buffer<"u"&&Buffer.isBuffer(c)&&(c=pt(c)),Array.isArray(c))if(typeof o=="object"&&o!=null){const w=[],v=Reflect.ownKeys(o);for(let P=0;P<v.length;P++){const p=v[P];w[p]=o[p]}o=w}else o=[];const m=e(o,c,r,s,t,i);m!=null?s[r]=m:Array.isArray(c)||it(o)&&it(c)?s[r]=F(o,c,e,i):o==null&&re(c)?s[r]=F({},c,e,i):o==null&&ie(c)?s[r]=pt(c):(o===void 0||c!==void 0)&&(s[r]=c)}return s}function ae(s,...t){return ne(s,...t,Rt)}var ce=Object.defineProperty,oe=Object.defineProperties,he=Object.getOwnPropertyDescriptors,ut=Object.getOwnPropertySymbols,pe=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable,lt=(s,t,e)=>t in s?ce(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,L=(s,t)=>{for(var e in t||(t={}))pe.call(t,e)&&lt(s,e,t[e]);if(ut)for(var e of ut(t))de.call(t,e)&&lt(s,e,t[e]);return s},ue=(s,t)=>oe(s,he(t));function d(s,t,e){var i;const n=T(s);return((i=t.rpcMap)==null?void 0:i[n.reference])||`${st}?chainId=${n.namespace}:${n.reference}&projectId=${e}`}function b(s){return s.includes(":")?s.split(":")[1]:s}function ft(s){return s.map(t=>`${t.split(":")[0]}:${t.split(":")[1]}`)}function le(s,t){const e=Object.keys(t.namespaces).filter(n=>n.includes(s));if(!e.length)return[];const i=[];return e.forEach(n=>{const a=t.namespaces[n].accounts;i.push(...a)}),i}function M(s={},t={}){const e=mt(s),i=mt(t);return ae(e,i)}function mt(s){var t,e,i,n,a;const r={};if(!G(s))return r;for(const[c,o]of Object.entries(s)){const m=Z(c)?[c]:o.chains,w=o.methods||[],v=o.events||[],P=o.rpcMap||{},p=_(c);r[p]=ue(L(L({},r[p]),o),{chains:J(m,(t=r[p])==null?void 0:t.chains),methods:J(w,(e=r[p])==null?void 0:e.methods),events:J(v,(i=r[p])==null?void 0:i.events)}),(G(P)||G(((n=r[p])==null?void 0:n.rpcMap)||{}))&&(r[p].rpcMap=L(L({},P),(a=r[p])==null?void 0:a.rpcMap))}return r}function vt(s){return s.includes(":")?s.split(":")[2]:s}function gt(s){const t={};for(const[e,i]of Object.entries(s)){const n=i.methods||[],a=i.events||[],r=i.accounts||[],c=Z(e)?[e]:i.chains?i.chains:ft(i.accounts);t[e]={chains:c,methods:n,events:a,accounts:r}}return t}function K(s){return typeof s=="number"?s:s.includes("0x")?parseInt(s,16):(s=s.includes(":")?s.split(":")[1]:s,isNaN(Number(s))?s:Number(s))}const Pt={},h=s=>Pt[s],V=(s,t)=>{Pt[s]=t};var fe=Object.defineProperty,me=(s,t,e)=>t in s?fe(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,O=(s,t,e)=>me(s,typeof t!="symbol"?t+"":t,e);class ve{constructor(t){O(this,"name","polkadot"),O(this,"client"),O(this,"httpProviders"),O(this,"events"),O(this,"namespace"),O(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${t}`)}getAccounts(){const t=this.namespace.accounts;return t?t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2])||[]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{var i;const n=b(e);t[n]=this.createHttpProvider(n,(i=this.namespace.rpcMap)==null?void 0:i[e])}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);return new f(new g(i,h("disableProviderPing")))}}var ge=Object.defineProperty,Pe=Object.defineProperties,we=Object.getOwnPropertyDescriptors,wt=Object.getOwnPropertySymbols,ye=Object.prototype.hasOwnProperty,be=Object.prototype.propertyIsEnumerable,X=(s,t,e)=>t in s?ge(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,yt=(s,t)=>{for(var e in t||(t={}))ye.call(t,e)&&X(s,e,t[e]);if(wt)for(var e of wt(t))be.call(t,e)&&X(s,e,t[e]);return s},bt=(s,t)=>Pe(s,we(t)),A=(s,t,e)=>X(s,typeof t!="symbol"?t+"":t,e);class Ie{constructor(t){A(this,"name","eip155"),A(this,"client"),A(this,"chainId"),A(this,"namespace"),A(this,"httpProviders"),A(this,"events"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.httpProviders=this.createHttpProviders(),this.chainId=parseInt(this.getDefaultChain())}async request(t){switch(t.request.method){case"eth_requestAccounts":return this.getAccounts();case"eth_accounts":return this.getAccounts();case"wallet_switchEthereumChain":return await this.handleSwitchChain(t);case"eth_chainId":return parseInt(this.getDefaultChain());case"wallet_getCapabilities":return await this.getCapabilities(t);case"wallet_getCallsStatus":return await this.getCallStatus(t)}return this.namespace.methods.includes(t.request.method)?await this.client.request(t):this.getHttpProvider().request(t.request)}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(parseInt(t),e),this.chainId=parseInt(t),this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${t}`)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId.toString();if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}createHttpProvider(t,e){const i=e||d(`${this.name}:${t}`,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);return new f(new Ct(i,h("disableProviderPing")))}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{var i;const n=parseInt(b(e));t[n]=this.createHttpProvider(n,(i=this.namespace.rpcMap)==null?void 0:i[e])}),t}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2]))]:[]}getHttpProvider(){const t=this.chainId,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}async handleSwitchChain(t){var e,i;let n=t.request.params?(e=t.request.params[0])==null?void 0:e.chainId:"0x0";n=n.startsWith("0x")?n:`0x${n}`;const a=parseInt(n,16);if(this.isChainApproved(a))this.setDefaultChain(`${a}`);else if(this.namespace.methods.includes("wallet_switchEthereumChain"))await this.client.request({topic:t.topic,request:{method:t.request.method,params:[{chainId:n}]},chainId:(i=this.namespace.chains)==null?void 0:i[0]}),this.setDefaultChain(`${a}`);else throw new Error(`Failed to switch to chain 'eip155:${a}'. The chain is not approved or the wallet does not support 'wallet_switchEthereumChain' method.`);return null}isChainApproved(t){return this.namespace.chains.includes(`${this.name}:${t}`)}async getCapabilities(t){var e,i,n,a,r;const c=(i=(e=t.request)==null?void 0:e.params)==null?void 0:i[0],o=((a=(n=t.request)==null?void 0:n.params)==null?void 0:a[1])||[],m=`${c}${o.join(",")}`;if(!c)throw new Error("Missing address parameter in `wallet_getCapabilities` request");const w=this.client.session.get(t.topic),v=((r=w?.sessionProperties)==null?void 0:r.capabilities)||{};if(v!=null&&v[m])return v?.[m];const P=await this.client.request(t);try{await this.client.session.update(t.topic,{sessionProperties:bt(yt({},w.sessionProperties||{}),{capabilities:bt(yt({},v||{}),{[m]:P})})})}catch(p){console.warn("Failed to update session with capabilities",p)}return P}async getCallStatus(t){var e,i;const n=this.client.session.get(t.topic),a=(e=n.sessionProperties)==null?void 0:e.bundler_name;if(a){const c=this.getBundlerUrl(t.chainId,a);try{return await this.getUserOperationReceipt(c,t)}catch(o){console.warn("Failed to fetch call status from bundler",o,c)}}const r=(i=n.sessionProperties)==null?void 0:i.bundler_url;if(r)try{return await this.getUserOperationReceipt(r,t)}catch(c){console.warn("Failed to fetch call status from custom bundler",c,r)}if(this.namespace.methods.includes(t.request.method))return await this.client.request(t);throw new Error("Fetching call status not approved by the wallet.")}async getUserOperationReceipt(t,e){var i;const n=new URL(t),a=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(Ht("eth_getUserOperationReceipt",[(i=e.request.params)==null?void 0:i[0]]))});if(!a.ok)throw new Error(`Failed to fetch user operation receipt - ${a.status}`);return await a.json()}getBundlerUrl(t,e){return`${jt}?projectId=${this.client.core.projectId}&chainId=${t}&bundler=${e}`}}var $e=Object.defineProperty,Oe=(s,t,e)=>t in s?$e(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,C=(s,t,e)=>Oe(s,typeof t!="symbol"?t+"":t,e);class Ae{constructor(t){C(this,"name","solana"),C(this,"client"),C(this,"httpProviders"),C(this,"events"),C(this,"namespace"),C(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${t}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2]))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{var i;const n=b(e);t[n]=this.createHttpProvider(n,(i=this.namespace.rpcMap)==null?void 0:i[e])}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);return new f(new g(i,h("disableProviderPing")))}}var Ce=Object.defineProperty,He=(s,t,e)=>t in s?Ce(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,H=(s,t,e)=>He(s,typeof t!="symbol"?t+"":t,e);class Ee{constructor(t){H(this,"name","cosmos"),H(this,"client"),H(this,"httpProviders"),H(this,"events"),H(this,"namespace"),H(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2]))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{var i;const n=b(e);t[n]=this.createHttpProvider(n,(i=this.namespace.rpcMap)==null?void 0:i[e])}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);return new f(new g(i,h("disableProviderPing")))}}var Ne=Object.defineProperty,Se=(s,t,e)=>t in s?Ne(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,E=(s,t,e)=>Se(s,typeof t!="symbol"?t+"":t,e);class De{constructor(t){E(this,"name","algorand"),E(this,"client"),E(this,"httpProviders"),E(this,"events"),E(this,"namespace"),E(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){if(!this.httpProviders[t]){const i=e||d(`${this.name}:${t}`,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);this.setHttpProvider(t,i)}this.chainId=t,this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2]))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{var i;t[e]=this.createHttpProvider(e,(i=this.namespace.rpcMap)==null?void 0:i[e])}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace,this.client.core.projectId);return typeof i>"u"?void 0:new f(new g(i,h("disableProviderPing")))}}var qe=Object.defineProperty,je=(s,t,e)=>t in s?qe(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,N=(s,t,e)=>je(s,typeof t!="symbol"?t+"":t,e);class Re{constructor(t){N(this,"name","cip34"),N(this,"client"),N(this,"httpProviders"),N(this,"events"),N(this,"namespace"),N(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2]))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{const i=this.getCardanoRPCUrl(e),n=b(e);t[n]=this.createHttpProvider(n,i)}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}getCardanoRPCUrl(t){const e=this.namespace.rpcMap;if(e)return e[t]}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||this.getCardanoRPCUrl(t);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);return new f(new g(i,h("disableProviderPing")))}}var _e=Object.defineProperty,Ue=(s,t,e)=>t in s?_e(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,S=(s,t,e)=>Ue(s,typeof t!="symbol"?t+"":t,e);class Fe{constructor(t){S(this,"name","elrond"),S(this,"client"),S(this,"httpProviders"),S(this,"events"),S(this,"namespace"),S(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${t}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2]))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{var i;const n=b(e);t[n]=this.createHttpProvider(n,(i=this.namespace.rpcMap)==null?void 0:i[e])}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);return new f(new g(i,h("disableProviderPing")))}}var Le=Object.defineProperty,Me=(s,t,e)=>t in s?Le(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,D=(s,t,e)=>Me(s,typeof t!="symbol"?t+"":t,e);class xe{constructor(t){D(this,"name","multiversx"),D(this,"client"),D(this,"httpProviders"),D(this,"events"),D(this,"namespace"),D(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${t}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2]))]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{var i;const n=b(e);t[n]=this.createHttpProvider(n,(i=this.namespace.rpcMap)==null?void 0:i[e])}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);return new f(new g(i,h("disableProviderPing")))}}var Be=Object.defineProperty,Ge=(s,t,e)=>t in s?Be(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,q=(s,t,e)=>Ge(s,typeof t!="symbol"?t+"":t,e);class Je{constructor(t){q(this,"name","near"),q(this,"client"),q(this,"httpProviders"),q(this,"events"),q(this,"namespace"),q(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){if(this.chainId=t,!this.httpProviders[t]){const i=e||d(`${this.name}:${t}`,this.namespace);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);this.setHttpProvider(t,i)}this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const t=this.namespace.accounts;return t?t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2])||[]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{var i;t[e]=this.createHttpProvider(e,(i=this.namespace.rpcMap)==null?void 0:i[e])}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace);return typeof i>"u"?void 0:new f(new g(i,h("disableProviderPing")))}}var ze=Object.defineProperty,ke=(s,t,e)=>t in s?ze(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,j=(s,t,e)=>ke(s,typeof t!="symbol"?t+"":t,e);class We{constructor(t){j(this,"name","tezos"),j(this,"client"),j(this,"httpProviders"),j(this,"events"),j(this,"namespace"),j(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace=Object.assign(this.namespace,t)}requestAccounts(){return this.getAccounts()}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider().request(t.request)}setDefaultChain(t,e){if(this.chainId=t,!this.httpProviders[t]){const i=e||d(`${this.name}:${t}`,this.namespace);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);this.setHttpProvider(t,i)}this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${this.chainId}`)}getAccounts(){const t=this.namespace.accounts;return t?t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2])||[]:[]}createHttpProviders(){const t={};return this.namespace.chains.forEach(e=>{t[e]=this.createHttpProvider(e)}),t}getHttpProvider(){const t=`${this.name}:${this.chainId}`,e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace);return typeof i>"u"?void 0:new f(new g(i))}}var Ke=Object.defineProperty,Ve=(s,t,e)=>t in s?Ke(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,R=(s,t,e)=>Ve(s,typeof t!="symbol"?t+"":t,e);class Xe{constructor(t){R(this,"name",I),R(this,"client"),R(this,"httpProviders"),R(this,"events"),R(this,"namespace"),R(this,"chainId"),this.namespace=t.namespace,this.events=h("events"),this.client=h("client"),this.chainId=this.getDefaultChain(),this.httpProviders=this.createHttpProviders()}updateNamespace(t){this.namespace.chains=[...new Set((this.namespace.chains||[]).concat(t.chains||[]))],this.namespace.accounts=[...new Set((this.namespace.accounts||[]).concat(t.accounts||[]))],this.namespace.methods=[...new Set((this.namespace.methods||[]).concat(t.methods||[]))],this.namespace.events=[...new Set((this.namespace.events||[]).concat(t.events||[]))],this.httpProviders=this.createHttpProviders()}requestAccounts(){return this.getAccounts()}request(t){return this.namespace.methods.includes(t.request.method)?this.client.request(t):this.getHttpProvider(t.chainId).request(t.request)}setDefaultChain(t,e){this.httpProviders[t]||this.setHttpProvider(t,e),this.chainId=t,this.events.emit(u.DEFAULT_CHAIN_CHANGED,`${this.name}:${t}`)}getDefaultChain(){if(this.chainId)return this.chainId;if(this.namespace.defaultChain)return this.namespace.defaultChain;const t=this.namespace.chains[0];if(!t)throw new Error("ChainId not found");return t.split(":")[1]}getAccounts(){const t=this.namespace.accounts;return t?[...new Set(t.filter(e=>e.split(":")[1]===this.chainId.toString()).map(e=>e.split(":")[2]))]:[]}createHttpProviders(){var t,e;const i={};return(e=(t=this.namespace)==null?void 0:t.accounts)==null||e.forEach(n=>{const a=T(n);i[`${a.namespace}:${a.reference}`]=this.createHttpProvider(n)}),i}getHttpProvider(t){const e=this.httpProviders[t];if(typeof e>"u")throw new Error(`JSON-RPC provider for ${t} not found`);return e}setHttpProvider(t,e){const i=this.createHttpProvider(t,e);i&&(this.httpProviders[t]=i)}createHttpProvider(t,e){const i=e||d(t,this.namespace,this.client.core.projectId);if(!i)throw new Error(`No RPC url provided for chainId: ${t}`);return new f(new g(i,h("disableProviderPing")))}}var Ye=Object.defineProperty,Qe=Object.defineProperties,Ze=Object.getOwnPropertyDescriptors,It=Object.getOwnPropertySymbols,Te=Object.prototype.hasOwnProperty,ts=Object.prototype.propertyIsEnumerable,Y=(s,t,e)=>t in s?Ye(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,x=(s,t)=>{for(var e in t||(t={}))Te.call(t,e)&&Y(s,e,t[e]);if(It)for(var e of It(t))ts.call(t,e)&&Y(s,e,t[e]);return s},Q=(s,t)=>Qe(s,Ze(t)),l=(s,t,e)=>Y(s,typeof t!="symbol"?t+"":t,e);class B{constructor(t){l(this,"client"),l(this,"namespaces"),l(this,"optionalNamespaces"),l(this,"sessionProperties"),l(this,"scopedProperties"),l(this,"events",new Nt),l(this,"rpcProviders",{}),l(this,"session"),l(this,"providerOpts"),l(this,"logger"),l(this,"uri"),l(this,"disableProviderPing",!1),this.providerOpts=t,this.logger=typeof t?.logger<"u"&&typeof t?.logger!="string"?t.logger:Ot(At({level:t?.logger||et})),this.disableProviderPing=t?.disableProviderPing||!1}static async init(t){const e=new B(t);return await e.initialize(),e}async request(t,e,i){const[n,a]=this.validateChain(e);if(!this.session)throw new Error("Please call connect() before request()");return await this.getProvider(n).request({request:x({},t),chainId:`${n}:${a}`,topic:this.session.topic,expiry:i})}sendAsync(t,e,i,n){const a=new Date().getTime();this.request(t,i,n).then(r=>e(null,Et(a,r))).catch(r=>e(r,void 0))}async enable(){if(!this.client)throw new Error("Sign Client not initialized");return this.session||await this.connect({namespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties,scopedProperties:this.scopedProperties}),await this.requestAccounts()}async disconnect(){var t;if(!this.session)throw new Error("Please call connect() before enable()");await this.client.disconnect({topic:(t=this.session)==null?void 0:t.topic,reason:tt("USER_DISCONNECTED")}),await this.cleanup()}async connect(t){if(!this.client)throw new Error("Sign Client not initialized");if(this.setNamespaces(t),await this.cleanupPendingPairings(),!t.skipPairing)return await this.pair(t.pairingTopic)}async authenticate(t,e){if(!this.client)throw new Error("Sign Client not initialized");this.setNamespaces(t),await this.cleanupPendingPairings();const{uri:i,response:n}=await this.client.authenticate(t,e);i&&(this.uri=i,this.events.emit("display_uri",i));const a=await n();if(this.session=a.session,this.session){const r=gt(this.session.namespaces);this.namespaces=M(this.namespaces,r),await this.persist("namespaces",this.namespaces),this.onConnect()}return a}on(t,e){this.events.on(t,e)}once(t,e){this.events.once(t,e)}removeListener(t,e){this.events.removeListener(t,e)}off(t,e){this.events.off(t,e)}get isWalletConnect(){return!0}async pair(t){const{uri:e,approval:i}=await this.client.connect({pairingTopic:t,requiredNamespaces:this.namespaces,optionalNamespaces:this.optionalNamespaces,sessionProperties:this.sessionProperties,scopedProperties:this.scopedProperties});e&&(this.uri=e,this.events.emit("display_uri",e));const n=await i();this.session=n;const a=gt(n.namespaces);return this.namespaces=M(this.namespaces,a),await this.persist("namespaces",this.namespaces),await this.persist("optionalNamespaces",this.optionalNamespaces),this.onConnect(),this.session}setDefaultChain(t,e){try{if(!this.session)return;const[i,n]=this.validateChain(t),a=this.getProvider(i);a.name===I?a.setDefaultChain(`${i}:${n}`,e):a.setDefaultChain(n,e)}catch(i){if(!/Please call connect/.test(i.message))throw i}}async cleanupPendingPairings(t={}){this.logger.info("Cleaning up inactive pairings...");const e=this.client.pairing.getAll();if(z(e)){for(const i of e)t.deletePairings?this.client.core.expirer.set(i.topic,0):await this.client.core.relayer.subscriber.unsubscribe(i.topic);this.logger.info(`Inactive pairings cleared: ${e.length}`)}}abortPairingAttempt(){this.logger.warn("abortPairingAttempt is deprecated. This is now a no-op.")}async checkStorage(){this.namespaces=await this.getFromStore("namespaces")||{},this.optionalNamespaces=await this.getFromStore("optionalNamespaces")||{},this.session&&this.createProviders()}async initialize(){this.logger.trace("Initialized"),await this.createClient(),await this.checkStorage(),this.registerEventListeners()}async createClient(){var t,e;if(this.client=this.providerOpts.client||await $t.init({core:this.providerOpts.core,logger:this.providerOpts.logger||et,relayUrl:this.providerOpts.relayUrl||St,projectId:this.providerOpts.projectId,metadata:this.providerOpts.metadata,storageOptions:this.providerOpts.storageOptions,storage:this.providerOpts.storage,name:this.providerOpts.name,customStoragePrefix:this.providerOpts.customStoragePrefix,telemetryEnabled:this.providerOpts.telemetryEnabled}),this.providerOpts.session)try{this.session=this.client.session.get(this.providerOpts.session.topic)}catch(i){throw this.logger.error("Failed to get session",i),new Error(`The provided session: ${(e=(t=this.providerOpts)==null?void 0:t.session)==null?void 0:e.topic} doesn't exist in the Sign client`)}else{const i=this.client.session.getAll();this.session=i[0]}this.logger.trace("SignClient Initialized")}createProviders(){if(!this.client)throw new Error("Sign Client not initialized");if(!this.session)throw new Error("Session not initialized. Please call connect() before enable()");const t=[...new Set(Object.keys(this.session.namespaces).map(e=>_(e)))];V("client",this.client),V("events",this.events),V("disableProviderPing",this.disableProviderPing),t.forEach(e=>{if(!this.session)return;const i=le(e,this.session),n=ft(i),a=M(this.namespaces,this.optionalNamespaces),r=Q(x({},a[e]),{accounts:i,chains:n});switch(e){case"eip155":this.rpcProviders[e]=new Ie({namespace:r});break;case"algorand":this.rpcProviders[e]=new De({namespace:r});break;case"solana":this.rpcProviders[e]=new Ae({namespace:r});break;case"cosmos":this.rpcProviders[e]=new Ee({namespace:r});break;case"polkadot":this.rpcProviders[e]=new ve({namespace:r});break;case"cip34":this.rpcProviders[e]=new Re({namespace:r});break;case"elrond":this.rpcProviders[e]=new Fe({namespace:r});break;case"multiversx":this.rpcProviders[e]=new xe({namespace:r});break;case"near":this.rpcProviders[e]=new Je({namespace:r});break;case"tezos":this.rpcProviders[e]=new We({namespace:r});break;default:this.rpcProviders[I]?this.rpcProviders[I].updateNamespace(r):this.rpcProviders[I]=new Xe({namespace:r})}})}registerEventListeners(){if(typeof this.client>"u")throw new Error("Sign Client is not initialized");this.client.on("session_ping",t=>{var e;const{topic:i}=t;i===((e=this.session)==null?void 0:e.topic)&&this.events.emit("session_ping",t)}),this.client.on("session_event",t=>{var e;const{params:i,topic:n}=t;if(n!==((e=this.session)==null?void 0:e.topic))return;const{event:a}=i;if(a.name==="accountsChanged"){const r=a.data;r&&z(r)&&this.events.emit("accountsChanged",r.map(vt))}else if(a.name==="chainChanged"){const r=i.chainId,c=i.event.data,o=_(r),m=K(r)!==K(c)?`${o}:${K(c)}`:r;this.onChainChanged(m)}else this.events.emit(a.name,a.data);this.events.emit("session_event",t)}),this.client.on("session_update",({topic:t,params:e})=>{var i,n;if(t!==((i=this.session)==null?void 0:i.topic))return;const{namespaces:a}=e,r=(n=this.client)==null?void 0:n.session.get(t);this.session=Q(x({},r),{namespaces:a}),this.onSessionUpdate(),this.events.emit("session_update",{topic:t,params:e})}),this.client.on("session_delete",async t=>{var e;t.topic===((e=this.session)==null?void 0:e.topic)&&(await this.cleanup(),this.events.emit("session_delete",t),this.events.emit("disconnect",Q(x({},tt("USER_DISCONNECTED")),{data:t.topic})))}),this.on(u.DEFAULT_CHAIN_CHANGED,t=>{this.onChainChanged(t,!0)})}getProvider(t){return this.rpcProviders[t]||this.rpcProviders[I]}onSessionUpdate(){Object.keys(this.rpcProviders).forEach(t=>{var e;this.getProvider(t).updateNamespace((e=this.session)==null?void 0:e.namespaces[t])})}setNamespaces(t){const{namespaces:e={},optionalNamespaces:i={},sessionProperties:n,scopedProperties:a}=t;this.optionalNamespaces=M(e,i),this.sessionProperties=n,this.scopedProperties=a}validateChain(t){const[e,i]=t?.split(":")||["",""];if(!this.namespaces||!Object.keys(this.namespaces).length)return[e,i];if(e&&!Object.keys(this.namespaces||{}).map(r=>_(r)).includes(e))throw new Error(`Namespace '${e}' is not configured. Please call connect() first with namespace config.`);if(e&&i)return[e,i];const n=_(Object.keys(this.namespaces)[0]),a=this.rpcProviders[n].getDefaultChain();return[n,a]}async requestAccounts(){const[t]=this.validateChain();return await this.getProvider(t).requestAccounts()}async onChainChanged(t,e=!1){if(!this.namespaces)return;const[i,n]=this.validateChain(t);if(!n)return;this.updateNamespaceChain(i,n),this.events.emit("chainChanged",n);const a=this.getProvider(i).getDefaultChain();e||this.getProvider(i).setDefaultChain(n),this.emitAccountsChangedOnChainChange({namespace:i,previousChainId:a,newChainId:t}),await this.persist("namespaces",this.namespaces)}emitAccountsChangedOnChainChange({namespace:t,previousChainId:e,newChainId:i}){var n,a;try{if(e===i)return;const r=(a=(n=this.session)==null?void 0:n.namespaces[t])==null?void 0:a.accounts;if(!r)return;const c=r.filter(o=>o.includes(`${i}:`)).map(vt);if(!z(c))return;this.events.emit("accountsChanged",c)}catch(r){this.logger.warn("Failed to emit accountsChanged on chain change",r)}}updateNamespaceChain(t,e){if(!this.namespaces)return;const i=this.namespaces[t]?t:`${t}:${e}`,n={chains:[],methods:[],events:[],defaultChain:e};this.namespaces[i]?this.namespaces[i]&&(this.namespaces[i].defaultChain=e):this.namespaces[i]=n}onConnect(){this.createProviders(),this.events.emit("connect",{session:this.session})}async cleanup(){this.namespaces=void 0,this.optionalNamespaces=void 0,this.sessionProperties=void 0,await this.deleteFromStore("namespaces"),await this.deleteFromStore("optionalNamespaces"),await this.deleteFromStore("sessionProperties"),this.session=void 0,await this.cleanupPendingPairings({deletePairings:!0}),await this.cleanupStorage()}async persist(t,e){var i;const n=((i=this.session)==null?void 0:i.topic)||"";await this.client.core.storage.setItem(`${U}/${t}${n}`,e)}async getFromStore(t){var e;const i=((e=this.session)==null?void 0:e.topic)||"";return await this.client.core.storage.getItem(`${U}/${t}${i}`)}async deleteFromStore(t){var e;const i=((e=this.session)==null?void 0:e.topic)||"";await this.client.core.storage.removeItem(`${U}/${t}${i}`)}async cleanupStorage(){var t;try{if(((t=this.client)==null?void 0:t.session.length)>0)return;const e=await this.client.core.storage.getKeys();for(const i of e)i.startsWith(U)&&await this.client.core.storage.removeItem(i)}catch(e){this.logger.warn("Failed to cleanup storage",e)}}}const es=B;export{es as UniversalProvider,B as default};
//# sourceMappingURL=index.es.js.map
