{"version": 3, "file": "error.mjs", "sourceRoot": "", "sources": ["../src/error.ts"], "names": [], "mappings": "AAAA;;GAEG;AAaH;;;;;;;GAOG;AAEH,MAAM,OAAO,WAAY,SAAQ,SAAS;IAiBxC,YAAY,OAAgB,EAAE,QAAkC;QAC9D,IAAI,MAA6B,CAAC;QAClC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QAClD,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;QACzB,MAAM,KAAK,GACT,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,OAAO,EAAE,CAAC;QAC3E,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,CAAC;QAE5B,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,SAAS,EAAE;YACrD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;QAED,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,GAAG,EAAE;YACnB,OAAO,CAAC,MAAM,KAAN,MAAM,GAAK,CAAC,OAAO,EAAE,GAAG,QAAQ,EAAE,CAAC,EAAC,CAAC;QAC/C,CAAC,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["/**\n * A `StructFailure` represents a single specific failure in validation.\n */\n\nexport type Failure = {\n  value: any;\n  key: any;\n  type: string;\n  refinement: string | undefined;\n  message: string;\n  explanation?: string | undefined;\n  branch: any[];\n  path: any[];\n};\n\n/**\n * `StructError` objects are thrown (or returned) when validation fails.\n *\n * Validation logic is design to exit early for maximum performance. The error\n * represents the first error encountered during validation. For more detail,\n * the `error.failures` property is a generator function that can be run to\n * continue validation and receive all the failures in the data.\n */\n\nexport class StructError extends TypeError {\n  value: any;\n\n  key!: any;\n\n  type!: string;\n\n  refinement!: string | undefined;\n\n  path!: any[];\n\n  branch!: any[];\n\n  failures: () => Failure[];\n\n  [x: string]: any;\n\n  constructor(failure: Failure, failures: () => Generator<Failure>) {\n    let cached: Failure[] | undefined;\n    const { message, explanation, ...rest } = failure;\n    const { path } = failure;\n    const cause =\n      path.length === 0 ? message : `At path: ${path.join('.')} -- ${message}`;\n    super(explanation ?? cause);\n\n    if (explanation !== null && explanation !== undefined) {\n      this.cause = cause;\n    }\n\n    Object.assign(this, rest);\n    this.name = this.constructor.name;\n    this.failures = () => {\n      return (cached ??= [failure, ...failures()]);\n    };\n  }\n}\n"]}