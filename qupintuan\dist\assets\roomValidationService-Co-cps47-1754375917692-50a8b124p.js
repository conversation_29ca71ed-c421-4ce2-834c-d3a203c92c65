const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-CaV4ohF9-1754375917692-opz9ain10.js","assets/vendor-D7uqzx8C-1754375917692-50a8b124p.js","assets/web3-NCUyEZtP-1754375917692-50a8b124p.js","assets/index-QZjJZq-p-1754375917692-9oqipqqma.css"])))=>i.map(i=>d[i]);
import{_ as L}from"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";import{f as R}from"./web3-NCUyEZtP-1754375917692-50a8b124p.js";const d=new Map;function C(t,a){const i=`room_${t}`;d.set(i,{validation:a,timestamp:Date.now()})}function I(t){const a=`room_${t}`,i=d.get(a);return i?Date.now()-i.timestamp>300*1e3?(d.delete(a),null):i.validation:null}function v(t){const a=`room_${t}`;d.delete(a)}async function P(t){try{if(!t||t.id===void 0||t.id===null||!t.creator)return{isValid:!1,reason:"房间信息不完整"};const a=I(t.id);if(a)return a;const{getRoomLockInfo:i}=await L(async()=>{const{getRoomLockInfo:r}=await import("./index-CaV4ohF9-1754375917692-opz9ain10.js").then(l=>l.q);return{getRoomLockInfo:r}},__vite__mapDeps([0,1,2,3])),n=await i({chainId:97,roomId:t.id});if(!n||!n.creator||n.creator==="0x0000000000000000000000000000000000000000")return{isValid:!1,reason:"未找到QPT锁仓记录",lockInfo:null};if(!(n.creator.toLowerCase()===t.creator.toLowerCase()))return{isValid:!1,reason:"锁仓地址与房间创建者不匹配",lockInfo:n};const s=w(t.tierDisplay),c=parseFloat(R(n.amount,18));if(!(Math.abs(c-s)<.01))return{isValid:!1,reason:`锁仓金额不匹配，期望: ${s} QPT，实际: ${c.toFixed(2)} QPT`,lockInfo:{...n,actualAmount:c,expectedAmount:s}};const e={isValid:!0,reason:"锁仓验证通过",lockInfo:{...n,actualAmount:c,expectedAmount:s}};return C(t.id,e),e}catch(a){return console.error(`验证房间 ${t.id} QPT锁仓失败:`,a),{isValid:!1,reason:`验证失败: ${a.message}`}}}async function y(t,a={}){const{filterInvalid:i=!0,maxConcurrent:n=5,isMyRoomsPage:f=!1}=a;if(!t||t.length===0)return[];try{const s=[];for(let e=0;e<t.length;e+=n){const r=t.slice(e,e+n),l=await Promise.all(r.map(async o=>{if(o.qptLockValidation&&o.qptLockValidation.isValid!==void 0)return o;const u=await P(o);return{...o,qptLockValidation:u}}));s.push(...l)}const c=s.filter(e=>{if(!(e.qptLockValidation&&e.qptLockValidation.isValid)||!(e.creator&&e.creator!=="0x0000000000000000000000000000000000000000"&&e.tier&&e.createdAt))return!1;if(f)return!0;{const o=e.participantsCount>=8||e.participants&&e.participants.length>=8||e.isFull,u=e.isClosed||e.isCompleted,h=Date.now()/1e3,V=1440*60,k=e.createdAt&&h-e.createdAt>V;return!o&&!u&&!k}}),p=s.filter(e=>!c.includes(e));return i?c:s}catch(s){return console.error("批量验证房间QPT锁仓失败:",s),i?[]:t}}function w(t){const a=parseFloat(t);return{30:100,50:150,100:200,200:300,500:400,1e3:500}[a]||0}export{v as clearValidationCache,P as validateRoomQPTLock,y as validateRoomsQPTLock};
