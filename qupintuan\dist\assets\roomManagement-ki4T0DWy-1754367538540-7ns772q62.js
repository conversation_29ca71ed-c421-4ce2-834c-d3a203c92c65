const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-C8294aMB-*************-wb8rvug16.js","assets/web3-uglCpOhK-*************-7ns772q62.js","assets/vendor-CgHzTxSQ-*************-7ns772q62.js","assets/ui-CbWGv3YI-*************-7ns772q62.js","assets/index-QZjJZq-p-*************-mniiclbh2.css"])))=>i.map(i=>d[i]);
import{_ as n}from"./web3-uglCpOhK-*************-7ns772q62.js";import"./vendor-CgHzTxSQ-*************-7ns772q62.js";import"./ui-CbWGv3YI-*************-7ns772q62.js";async function H({chainId:w,tier:_,signer:u}){try{if(!u)throw new Error("未检测到钱包签名者");const e=u.account?.address;if(!e)throw new Error("无法获取用户地址");const{getContractAddress:s}=await n(async()=>{const{getContractAddress:o}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(c=>c.n);return{getContractAddress:o}},__vite__mapDeps([0,1,2,3,4])),{ABIS:g}=await n(async()=>{const{ABIS:o}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(c=>c.o);return{ABIS:o}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:p,http:b}=await n(async()=>{const{createPublicClient:o,http:c}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(m=>m.aL);return{createPublicClient:o,http:c}},__vite__mapDeps([1,2,3])),{bscTestnet:f}=await n(async()=>{const{bscTestnet:o}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(c=>c.m);return{bscTestnet:o}},__vite__mapDeps([0,1,2,3,4])),A=s(w,"QPTLocker"),h=s(w,"QPTToken"),R=p({chain:f,transport:b()}),E=Number(_),i=BigInt(E*1e6),T=(await R.readContract({address:A,abi:g.QPTLocker,functionName:"amountMappings",args:[i]}))[1],C=Number(T)/10**18,y=await R.readContract({address:h,abi:g.QPTToken,functionName:"balanceOf",args:[e]}),l=BigInt(y),r=Number(l)/1e18;if(l<T)throw new Error(`QPT余额不足。需要: ${C} QPT，当前: ${r} QPT`);const a=await u.writeContract({address:h,abi:g.QPTToken,functionName:"approve",args:[A,T]});return{success:!0,receipt:await R.waitForTransactionReceipt({hash:a,timeout:6e4}),txHash:a,approvedAmount:C,userBalance:r,message:`第2步完成：授权 ${C} QPT`}}catch(e){throw e}}async function M({chainId:w,tier:_,roomId:u,signer:e}){try{if(!e)throw new Error("未检测到钱包签名者");const s=e.account?.address;if(!s)throw new Error("无法获取用户地址");const{getContractAddress:g}=await n(async()=>{const{getContractAddress:c}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(m=>m.n);return{getContractAddress:c}},__vite__mapDeps([0,1,2,3,4])),{ABIS:p}=await n(async()=>{const{ABIS:c}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(m=>m.o);return{ABIS:c}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:b,http:f}=await n(async()=>{const{createPublicClient:c,http:m}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(P=>P.aL);return{createPublicClient:c,http:m}},__vite__mapDeps([1,2,3])),{bscTestnet:A}=await n(async()=>{const{bscTestnet:c}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(m=>m.m);return{bscTestnet:c}},__vite__mapDeps([0,1,2,3,4])),h=g(w,"QPTLocker"),R=g(w,"QPTToken"),E=b({chain:A,transport:f()}),i=Number(_),d=BigInt(i*1e6),C=(await E.readContract({address:h,abi:p.QPTLocker,functionName:"amountMappings",args:[d]}))[1],y=Number(C)/10**18,l=await E.readContract({address:R,abi:p.QPTToken,functionName:"allowance",args:[s,h]}),r=BigInt(l),a=Number(r)/1e18;if(r<C)throw new Error(`QPT授权不足。需要: ${y} QPT，当前授权: ${a} QPT。请先完成第2步授权操作。`);const t=await e.writeContract({address:h,abi:p.QPTLocker,functionName:"lockForRoom",args:[BigInt(u),d]});return{success:!0,receipt:await E.waitForTransactionReceipt({hash:t,timeout:6e4}),txHash:t,roomId:u,lockedAmount:y,message:`第3步完成：为房间 #${u} 锁仓 ${y} QPT`}}catch(s){throw s}}async function W({chainId:w,tier:_,signer:u}){try{if(console.log("🚀 [createRoomWithQPTVerification] 发起人第1步：创建拼团房间:",{chainId:w,tier:_,signerAddress:u?.account?.address}),!u)throw new Error("未检测到钱包签名者，请安装 MetaMask 或其他以太坊兼容钱包");const{getContractAddress:e}=await n(async()=>{const{getContractAddress:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(o=>o.n);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3,4])),s=e(w,"GroupBuyRoom"),{ABIS:g}=await n(async()=>{const{ABIS:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(o=>o.o);return{ABIS:t}},__vite__mapDeps([0,1,2,3,4])),p=g.GroupBuyRoom;if(console.log("📋 [apiCreateRoom] 合约信息:",{contractAddress:s,hasABI:!!p,abiLength:p?.length,chainId:w,tier:_.toString()}),!s||s==="0x0000000000000000000000000000000000000000")throw new Error(`无效的合约地址: ${s}，链ID: ${w}`);if(!p||p.length===0)throw new Error("GroupBuyRoom ABI 未找到或为空");const b=p.find(t=>t.type==="function"&&t.name==="createRoom");if(!b)throw new Error("createRoom 函数在 ABI 中不存在");const f=p.find(t=>t.type==="event"&&t.name==="RoomCreated");if(!f)throw new Error("RoomCreated 事件在 ABI 中不存在");console.log("✅ [apiCreateRoom] ABI 验证通过:",{hasCreateRoomFunction:!!b,hasRoomCreatedEvent:!!f});const A=u.account.address;console.log("👤 [createRoomWithQPTVerification] 签名者地址:",A);const{createPublicClient:h,http:R}=await n(async()=>{const{createPublicClient:t,http:o}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(c=>c.aL);return{createPublicClient:t,http:o}},__vite__mapDeps([1,2,3])),{bscTestnet:E}=await n(async()=>{const{bscTestnet:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(o=>o.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4])),i=h({chain:E,transport:R()}),d=Number(_);console.log("🔢 [createRoomWithQPTVerification] 档位信息:",{originalTier:_.toString(),tierNum:d,tierType:typeof _}),console.log("🏠 [createRoomWithQPTVerification] 第1步：创建拼团房间（不验证QPT锁仓）");const T=[30,50,100,200,500,1e3];if(!T.includes(d))throw new Error(`不支持的档位: ${d}。支持的档位: ${T.join(", ")}`);let C;try{const t=`TIER_${d}`;console.log("📞 [apiCreateRoom] 调用合约函数:",t),C=await i.readContract({address:s,abi:p,functionName:t}),console.log("✅ [apiCreateRoom] 获取合约档位值成功:",{tierFunctionName:t,contractTierValue:C.toString()})}catch(t){throw console.error("❌ [apiCreateRoom] 获取合约档位值失败:",t.message),new Error(`不支持的档位: ${d}。支持的档位: 30, 50, 100, 200, 500, 1000。错误详情: ${t.message}`)}let y;try{y=await i.estimateContractGas({address:s,abi:p,functionName:"createRoom",args:[C],account:A}),console.log("⛽ [apiCreateRoom] Gas 估算成功:",y.toString())}catch(t){throw console.error("❌ [apiCreateRoom] Gas 估算失败:",t.message),console.error("可能的原因："),console.error("1. 用户 USDT 余额不足"),console.error("2. 用户未授权合约使用 USDT"),console.error("3. 合约参数错误"),console.error("4. 合约逻辑错误"),new Error(`Gas 估算失败，交易可能会失败。请检查：1) USDT余额 2) USDT授权 3) 网络连接。详细错误: ${t.message}`)}console.log("📤 [apiCreateRoom] 发送交易:",{contractAddress:s,functionName:"createRoom",args:[C.toString()],estimatedGas:y.toString(),signerAddress:A,originalTier:_.toString(),tierNum:d});const l=await u.writeContract({address:s,abi:p,functionName:"createRoom",args:[C],gas:y*120n/100n});console.log("✅ [apiCreateRoom] 交易已发送:",l);const r=await i.waitForTransactionReceipt({hash:l,timeout:6e4});if(console.log("📄 [apiCreateRoom] 交易收据:",{hash:r.hash,status:r.status,logsCount:r.logs?.length||0,blockNumber:r.blockNumber,gasUsed:r.gasUsed?.toString()}),r.status==="reverted")throw console.error("❌ [apiCreateRoom] 交易被回滚，可能的原因:"),console.error("1. 用户余额不足（USDT 或 Gas）"),console.error("2. 合约函数调用失败（参数错误、权限问题等）"),console.error("3. 合约内部逻辑错误"),console.error("4. 网络拥堵或Gas价格过低"),new Error(`交易被回滚，请检查：1) USDT余额是否足够 2) Gas费用是否足够 3) 网络是否正常。交易哈希: ${r.transactionHash||r.hash}`);let a=null;console.log("🔍 [apiCreateRoom] 开始解析事件日志，日志数量:",r.logs.length),console.log("🔍 [apiCreateRoom] 合约地址:",s);for(let t=0;t<r.logs.length;t++){const o=r.logs[t];if(console.log(`📝 [apiCreateRoom] 解析日志 ${t+1}:`,{address:o.address,contractAddress:s,isFromContract:o.address.toLowerCase()===s.toLowerCase(),topics:o.topics,topicsLength:o.topics?.length||0,dataLength:o.data?.length||0,data:o.data}),o.address.toLowerCase()!==s.toLowerCase()){console.log(`⏭️ [apiCreateRoom] 跳过非目标合约的日志 ${t+1}`);continue}try{const{decodeEventLog:c}=await n(async()=>{const{decodeEventLog:P}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(D=>D.aL);return{decodeEventLog:P}},__vite__mapDeps([1,2,3])),m=c({abi:p,data:o.data,topics:o.topics});if(console.log(`✅ [apiCreateRoom] 成功解码事件 ${t+1}:`,{eventName:m.eventName,args:m.args,argsKeys:Object.keys(m.args||{}),roomIdRaw:m.args?.roomId,roomIdType:typeof m.args?.roomId}),m.eventName==="RoomCreated"){const P=m.args.roomId;a=Number(P),console.log("🎯 [apiCreateRoom] 找到 RoomCreated 事件，房间ID:",{originalValue:P,originalType:typeof P,convertedValue:a,convertedType:typeof a,isValid:a>=0,isNumber:!isNaN(a)});break}}catch(c){console.warn(`⚠️ [apiCreateRoom] 无法解码日志 ${t+1}:`,{error:c.message,logAddress:o.address,topicsLength:o.topics?.length||0,dataLength:o.data?.length||0});continue}}if(a==null)throw console.error("❌ [apiCreateRoom] 无法提取房间ID，详细信息:",{receiptStatus:r.status,logsCount:r.logs.length,contractAddress:s,txHash:r.transactionHash,blockNumber:r.blockNumber,gasUsed:r.gasUsed?.toString(),roomIdValue:a,roomIdType:typeof a,logs:r.logs.map((t,o)=>({index:o,address:t.address,topicsCount:t.topics?.length||0,dataLength:t.data?.length||0}))}),new Error(`无法从交易收据中提取房间ID。交易哈希: ${r.transactionHash}，日志数量: ${r.logs.length}`);console.log("✅ [createRoomWithQPTVerification] 第1步完成 - 拼团房间创建成功:",{roomId:a,receiptHash:r.hash,receiptStatus:r.status,logsCount:r.logs?.length||0,blockNumber:r.blockNumber,timestamp:new Date().toISOString(),message:`第1步完成：创建拼团房间 #${a}`});try{console.log("🔍 [apiCreateRoom] 验证新创建的房间...");const t=await i.readContract({address:s,abi:p,functionName:"getRoom",args:[BigInt(a)]});console.log("✅ [apiCreateRoom] 房间验证成功:",{roomId:a,creator:t[0],tier:t[1]?.toString(),createTime:t[3]?.toString()})}catch(t){console.warn("⚠️ [apiCreateRoom] 房间验证失败:",t.message)}return{roomId:a,receipt:r}}catch(e){throw console.error("创建房间失败:",e),e}}async function j({chainId:w,roomId:_,signer:u}){try{if(!u)throw new Error("未检测到钱包签名者");const e=u.account?.address;if(!e)throw new Error("无法获取用户地址");const{getContractAddress:s}=await n(async()=>{const{getContractAddress:o}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(c=>c.n);return{getContractAddress:o}},__vite__mapDeps([0,1,2,3,4])),{ABIS:g}=await n(async()=>{const{ABIS:o}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(c=>c.o);return{ABIS:o}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:p,http:b}=await n(async()=>{const{createPublicClient:o,http:c}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(m=>m.aL);return{createPublicClient:o,http:c}},__vite__mapDeps([1,2,3])),{bscTestnet:f}=await n(async()=>{const{bscTestnet:o}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(c=>c.m);return{bscTestnet:o}},__vite__mapDeps([0,1,2,3,4])),A=s(w,"GroupBuyRoom"),h=s(w,"USDT"),R=p({chain:f,transport:b()}),E=await R.readContract({address:A,abi:g.GroupBuyRoom,functionName:"getRoom",args:[BigInt(_)]}),[i,d]=E,T=Number(d)/1e6,C=BigInt(d),y=await R.readContract({address:h,abi:g.USDT,functionName:"balanceOf",args:[e]}),l=BigInt(y),r=Number(l)/1e6;if(l<C)throw new Error(`USDT余额不足。需要: ${T} USDT，当前: ${r} USDT`);const a=await u.writeContract({address:h,abi:g.USDT,functionName:"approve",args:[A,C]});return{success:!0,receipt:await R.waitForTransactionReceipt({hash:a,timeout:6e4}),txHash:a,approvedAmount:T,userBalance:r,message:`第1步完成：重新授权 ${T} USDT`}}catch(e){throw e}}async function K({chainId:w,roomId:_,signer:u}){try{if(!u)throw new Error("未检测到钱包签名者");const e=u.account?.address;if(!e)throw new Error("无法获取用户地址");const{getContractAddress:s}=await n(async()=>{const{getContractAddress:I}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(B=>B.n);return{getContractAddress:I}},__vite__mapDeps([0,1,2,3,4])),{ABIS:g}=await n(async()=>{const{ABIS:I}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(B=>B.o);return{ABIS:I}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:p,http:b}=await n(async()=>{const{createPublicClient:I,http:B}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(k=>k.aL);return{createPublicClient:I,http:B}},__vite__mapDeps([1,2,3])),{bscTestnet:f}=await n(async()=>{const{bscTestnet:I}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(B=>B.m);return{bscTestnet:I}},__vite__mapDeps([0,1,2,3,4])),A=s(w,"GroupBuyRoom"),h=s(w,"USDT"),R=g.GroupBuyRoom,E=p({chain:f,transport:b()}),i=await E.readContract({address:A,abi:R,functionName:"getRoom",args:[BigInt(_)]}),[d,T,C,y,l,r]=i,a=Number(T)/1e6,t=BigInt(T);if(l)throw new Error("房间已关闭，无法参与");if(C.length>=8)throw new Error("房间已满员，无法参与");if(C.some(I=>I.toLowerCase()===e.toLowerCase()))throw new Error("您已参与此房间");const c=Math.floor(Date.now()/1e3),m=Number(i[12]);if(c>m)throw new Error("房间已过期，无法参与");const[P,D]=await Promise.all([E.readContract({address:h,abi:g.USDT,functionName:"balanceOf",args:[e]}),E.readContract({address:h,abi:g.USDT,functionName:"allowance",args:[e,A]})]),L=BigInt(P),v=BigInt(D),S=Number(L)/1e6,$=Number(v)/1e6;if(L<t)throw new Error(`USDT余额不足。需要: ${a} USDT，当前: ${S} USDT`);if(v<t)throw new Error(`USDT授权不足。需要: ${a} USDT，当前授权: ${$} USDT。请先完成第1步授权操作。`);const N=await u.writeContract({address:A,abi:R,functionName:"joinRoom",args:[BigInt(_)]}),U=await E.waitForTransactionReceipt({hash:N,timeout:6e4}),Q=await E.readContract({address:h,abi:g.USDT,functionName:"balanceOf",args:[e]}),V=Number(Q)/1e6,O=S-V;return{receipt:U,txHash:N,roomId:_,paymentVerification:{expectedAmount:a,actualPaid:O,remainingBalance:V,paymentVerified:Math.abs(O-a)<1e-6}}}catch(e){throw e}}async function G({chainId:w,roomId:_}){try{const{getContractAddress:u}=await n(async()=>{const{getContractAddress:r}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(a=>a.n);return{getContractAddress:r}},__vite__mapDeps([0,1,2,3,4])),{ABIS:e}=await n(async()=>{const{ABIS:r}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(a=>a.o);return{ABIS:r}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:s,http:g}=await n(async()=>{const{createPublicClient:r,http:a}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(t=>t.aL);return{createPublicClient:r,http:a}},__vite__mapDeps([1,2,3])),{bscTestnet:p}=await n(async()=>{const{bscTestnet:r}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(a=>a.m);return{bscTestnet:r}},__vite__mapDeps([0,1,2,3,4])),b=u(w,"GroupBuyRoom"),f=u(w,"QPTLocker"),A=s({chain:p,transport:g()}),h=await A.readContract({address:b,abi:e.GroupBuyRoom,functionName:"getRoom",args:[BigInt(_)]}),[R]=h,E=await A.readContract({address:f,abi:e.QPTLocker,functionName:"getRoomInfo",args:[BigInt(_)]}),[i,d,T,C,y]=E,l=i.toLowerCase()===R.toLowerCase();return{isValid:l,creator:R,lockerCreator:i,amount:Number(d)/1e18,unlockTime:Number(T),isSuccess:C,isClaimed:y,message:l?"QPT锁仓验证通过":"发起人未锁仓QPT"}}catch(u){return{isValid:!1,message:`QPT锁仓验证失败: ${u.message}`}}}async function J({chainId:w,roomId:_,userAddress:u}){try{const{getContractAddress:e}=await n(async()=>{const{getContractAddress:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(o=>o.n);return{getContractAddress:t}},__vite__mapDeps([0,1,2,3,4])),{ABIS:s}=await n(async()=>{const{ABIS:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(o=>o.o);return{ABIS:t}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:g,http:p}=await n(async()=>{const{createPublicClient:t,http:o}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(c=>c.aL);return{createPublicClient:t,http:o}},__vite__mapDeps([1,2,3])),{bscTestnet:b}=await n(async()=>{const{bscTestnet:t}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(o=>o.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4])),f=e(w,"GroupBuyRoom"),A=e(w,"USDT"),h=g({chain:b,transport:p()}),R=await h.readContract({address:f,abi:s.GroupBuyRoom,functionName:"getRoom",args:[BigInt(_)]}),[E,i]=R,d=Number(i)/1e6,T=BigInt(i),[C,y]=await Promise.all([h.readContract({address:A,abi:s.USDT,functionName:"balanceOf",args:[u]}),h.readContract({address:A,abi:s.USDT,functionName:"allowance",args:[u,f]})]),l=BigInt(C),r=BigInt(y),a={tierAmount:d,balance:Number(l)/1e6,allowance:Number(r)/1e6,hasEnoughBalance:l>=T,hasEnoughAllowance:r>=T,needsApproval:r<T,errors:[]};return a.hasEnoughBalance||a.errors.push(`USDT余额不足 (需要: ${d}, 当前: ${a.balance})`),a.hasEnoughAllowance||a.errors.push(`需要授权USDT (需要: ${d}, 当前授权: ${a.allowance})`),a.canProceed=a.hasEnoughBalance&&a.hasEnoughAllowance,a}catch(e){return{canProceed:!1,errors:[`USDT验证失败: ${e.message}`]}}}async function z({chainId:w,roomId:_,userAddress:u}){try{const{getContractAddress:e}=await n(async()=>{const{getContractAddress:m}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(P=>P.n);return{getContractAddress:m}},__vite__mapDeps([0,1,2,3,4])),{ABIS:s}=await n(async()=>{const{ABIS:m}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(P=>P.o);return{ABIS:m}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:g,http:p}=await n(async()=>{const{createPublicClient:m,http:P}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(D=>D.aL);return{createPublicClient:m,http:P}},__vite__mapDeps([1,2,3])),{bscTestnet:b}=await n(async()=>{const{bscTestnet:m}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(P=>P.m);return{bscTestnet:m}},__vite__mapDeps([0,1,2,3,4])),f=e(w,"GroupBuyRoom"),h=await g({chain:b,transport:p()}).readContract({address:f,abi:s.GroupBuyRoom,functionName:"getRoom",args:[BigInt(_)]}),[R,E,i,d,T,C]=h,y=Number(E)/1e6,l={canJoin:!0,errors:[],warnings:[],roomInfo:{creator:R,tierAmount:y,participantsCount:i.length,isClosed:T,isSuccessful:C},steps:{needsApproval:!1,canJoinRoom:!1}};T&&(l.canJoin=!1,l.errors.push("房间已关闭")),i.length>=8&&(l.canJoin=!1,l.errors.push("房间已满员")),i.some(m=>m.toLowerCase()===u.toLowerCase())&&(l.canJoin=!1,l.errors.push("您已参与此房间"));const a=Math.floor(Date.now()/1e3),t=Number(h[12]);a>t&&(l.canJoin=!1,l.errors.push("房间已过期")),(await G({chainId:w,roomId:_})).isValid||(l.canJoin=!1,l.errors.push("发起人未锁仓QPT"));const c=await J({chainId:w,roomId:_,userAddress:u});return l.usdtInfo=c,c.hasEnoughBalance||(l.canJoin=!1,l.errors.push(...c.errors.filter(m=>m.includes("余额不足")))),l.steps.needsApproval=c.needsApproval,l.steps.canJoinRoom=l.canJoin&&!c.needsApproval,l}catch(e){return{canJoin:!1,errors:[`验证过程出错: ${e.message}`],warnings:[],steps:{needsApproval:!1,canJoinRoom:!1}}}}async function X({chainId:w,roomId:_,signer:u}){try{if(!u)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:e}=await n(async()=>{const{getContractAddress:i}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(d=>d.n);return{getContractAddress:i}},__vite__mapDeps([0,1,2,3,4])),s=e(w,"GroupBuyRoom"),{ABIS:g}=await n(async()=>{const{ABIS:i}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(d=>d.o);return{ABIS:i}},__vite__mapDeps([0,1,2,3,4])),p=g.GroupBuyRoom,b=await u.writeContract({address:s,abi:p,functionName:"closeRoom",args:[BigInt(_)]}),{createPublicClient:f,http:A}=await n(async()=>{const{createPublicClient:i,http:d}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(T=>T.aL);return{createPublicClient:i,http:d}},__vite__mapDeps([1,2,3])),{bscTestnet:h}=await n(async()=>{const{bscTestnet:i}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(d=>d.m);return{bscTestnet:i}},__vite__mapDeps([0,1,2,3,4]));return{receipt:await f({chain:h,transport:A()}).waitForTransactionReceipt({hash:b,timeout:6e4}),txHash:b}}catch(e){throw console.error("关闭房间失败:",e),e}}async function Y({chainId:w,roomId:_,signer:u}){try{if(!u)throw new Error("签名者未定义，请确保钱包已连接");const{getContractAddress:e}=await n(async()=>{const{getContractAddress:i}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(d=>d.n);return{getContractAddress:i}},__vite__mapDeps([0,1,2,3,4])),s=e(w,"GroupBuyRoom"),{ABIS:g}=await n(async()=>{const{ABIS:i}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(d=>d.o);return{ABIS:i}},__vite__mapDeps([0,1,2,3,4])),p=g.GroupBuyRoom,b=await u.writeContract({address:s,abi:p,functionName:"expireRoom",args:[BigInt(_)]}),{createPublicClient:f,http:A}=await n(async()=>{const{createPublicClient:i,http:d}=await import("./web3-uglCpOhK-*************-7ns772q62.js").then(T=>T.aL);return{createPublicClient:i,http:d}},__vite__mapDeps([1,2,3])),{bscTestnet:h}=await n(async()=>{const{bscTestnet:i}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(d=>d.m);return{bscTestnet:i}},__vite__mapDeps([0,1,2,3,4]));return{receipt:await f({chain:h,transport:A()}).waitForTransactionReceipt({hash:b,timeout:6e4}),txHash:b}}catch(e){throw console.error("过期房间处理失败:",e),e}}export{H as approveQPTForCreate,j as approveUSDTForJoin,X as closeRoom,W as createRoom,W as createRoomWithQPTVerification,Y as expireRoom,K as joinRoom,K as joinRoomWithPaymentVerification,M as lockQPTForCreate,G as validateCreatorQPTLock,z as validateJoinRoomConditions,J as validateParticipantUSDT};
