import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const kardia<PERSON>hain = /*#__PURE__*/ defineChain({
  id: 24,
  name: 'KardiaChain Mainnet',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: '<PERSON>A<PERSON>', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.kardiachain.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'KardiaChain Explorer',
      url: 'https://explorer.kardiachain.io',
    },
  },
  testnet: false,
})
