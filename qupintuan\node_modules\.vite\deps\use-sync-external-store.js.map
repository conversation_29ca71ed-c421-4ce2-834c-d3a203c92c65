{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store.development.js", "../../use-sync-external-store/index.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nif (\"production\" !== process.env.NODE_ENV) {\n  \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n    \"function\" ===\n      typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n  var useSyncExternalStore$jscomp$inline_1 =\n    require(\"react\").useSyncExternalStore;\n  console.error(\n    \"The main 'use-sync-external-store' entry point is not supported; all it does is re-export useSyncExternalStore from the 'react' package, so it only works with React 18+.\\n\\nIf you wish to support React 16 and 17, import from 'use-sync-external-store/shim' instead. It will fall back to a shimmed implementation when the native one is not available.\\n\\nIf you only support React 18+, you can import directly from 'react'.\"\n  );\n  exports.useSyncExternalStore = useSyncExternalStore$jscomp$inline_1;\n  \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n    \"function\" ===\n      typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/use-sync-external-store.production.js');\n} else {\n  module.exports = require('./cjs/use-sync-external-store.development.js');\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAWA,QAAI,MAAuC;AACzC,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AAChE,6CACF,gBAAiB;AACnB,cAAQ;AAAA,QACN;AAAA,MACF;AACA,cAAQ,uBAAuB;AAC/B,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE;AAVM;AAAA;AAAA;;;AChBN;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}