{"version": 3, "sources": ["../../@wagmi/connectors/src/baseAccount.ts", "../../@wagmi/connectors/src/coinbaseWallet.ts", "../../@wagmi/connectors/src/metaMask.ts", "../../@wagmi/connectors/src/safe.ts", "../../@wagmi/connectors/src/version.ts", "../../@wagmi/connectors/src/walletConnect.ts"], "sourcesContent": ["import type { createBaseAccountSDK, ProviderInterface } from '@base-org/account'\nimport {\n  ChainNotConfiguredError,\n  type Connector,\n  createConnector,\n} from '@wagmi/core'\nimport type { Mutable, Omit } from '@wagmi/core/internal'\nimport {\n  type AddEthereumChainParameter,\n  type Address,\n  getAddress,\n  type Hex,\n  numberToHex,\n  type ProviderRpcError,\n  SwitchChainError,\n  UserRejectedRequestError,\n} from 'viem'\n\nexport type BaseAccountParameters = Mutable<\n  Omit<\n    Parameters<typeof createBaseAccountSDK>[0],\n    'appChainIds' // set via wagmi config\n  >\n>\n\nexport function baseAccount(parameters: BaseAccountParameters = {}) {\n  type Provider = ProviderInterface\n  type Properties = {\n    connect(parameters?: {\n      chainId?: number | undefined\n      isReconnecting?: boolean | undefined\n    }): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n  }\n\n  let walletProvider: Provider | undefined\n\n  let accountsChanged: Connector['onAccountsChanged'] | undefined\n  let chainChanged: Connector['onChainChanged'] | undefined\n  let disconnect: Connector['onDisconnect'] | undefined\n\n  return createConnector<Provider, Properties>((config) => ({\n    id: 'baseAccount',\n    name: 'Base Account',\n    rdns: 'app.base.account',\n    type: 'baseAccount',\n    async connect({ chainId } = {}) {\n      try {\n        const provider = await this.getProvider()\n        const accounts = (\n          (await provider.request({\n            method: 'eth_requestAccounts',\n            params: [],\n          })) as string[]\n        ).map((x) => getAddress(x))\n\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n\n        // Switch to chain if provided\n        let currentChainId = await this.getChainId()\n        if (chainId && currentChainId !== chainId) {\n          const chain = await this.switchChain!({ chainId }).catch((error) => {\n            if (error.code === UserRejectedRequestError.code) throw error\n            return { id: currentChainId }\n          })\n          currentChainId = chain?.id ?? currentChainId\n        }\n\n        return { accounts, chainId: currentChainId }\n      } catch (error) {\n        if (\n          /(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(\n            (error as Error).message,\n          )\n        )\n          throw new UserRejectedRequestError(error as Error)\n        throw error\n      }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n\n      if (accountsChanged) {\n        provider.removeListener('accountsChanged', accountsChanged)\n        accountsChanged = undefined\n      }\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n\n      provider.disconnect()\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      return (\n        (await provider.request({\n          method: 'eth_accounts',\n        })) as string[]\n      ).map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      const chainId = (await provider.request({\n        method: 'eth_chainId',\n      })) as Hex\n      return Number(chainId)\n    },\n    async getProvider() {\n      if (!walletProvider) {\n        const preference = (() => {\n          if (typeof parameters.preference === 'string')\n            return { options: parameters.preference }\n          return {\n            ...parameters.preference,\n            options: parameters.preference?.options ?? 'all',\n          }\n        })()\n\n        const { createBaseAccountSDK } = await import('@base-org/account')\n        const sdk = createBaseAccountSDK({\n          ...parameters,\n          appChainIds: config.chains.map((x) => x.id),\n          preference,\n        })\n\n        walletProvider = sdk.getProvider()\n      }\n\n      return walletProvider\n    },\n    async isAuthorized() {\n      try {\n        const accounts = await this.getAccounts()\n        return !!accounts.length\n      } catch {\n        return false\n      }\n    },\n    async switchChain({ addEthereumChainParameter, chainId }) {\n      const chain = config.chains.find((chain) => chain.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      const provider = await this.getProvider()\n\n      try {\n        await provider.request({\n          method: 'wallet_switchEthereumChain',\n          params: [{ chainId: numberToHex(chain.id) }],\n        })\n        return chain\n      } catch (error) {\n        // Indicates chain is not added to provider\n        if ((error as ProviderRpcError).code === 4902) {\n          try {\n            let blockExplorerUrls: string[] | undefined\n            if (addEthereumChainParameter?.blockExplorerUrls)\n              blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls\n            else\n              blockExplorerUrls = chain.blockExplorers?.default.url\n                ? [chain.blockExplorers?.default.url]\n                : []\n\n            let rpcUrls: readonly string[]\n            if (addEthereumChainParameter?.rpcUrls?.length)\n              rpcUrls = addEthereumChainParameter.rpcUrls\n            else rpcUrls = [chain.rpcUrls.default?.http[0] ?? '']\n\n            const addEthereumChain = {\n              blockExplorerUrls,\n              chainId: numberToHex(chainId),\n              chainName: addEthereumChainParameter?.chainName ?? chain.name,\n              iconUrls: addEthereumChainParameter?.iconUrls,\n              nativeCurrency:\n                addEthereumChainParameter?.nativeCurrency ??\n                chain.nativeCurrency,\n              rpcUrls,\n            } satisfies AddEthereumChainParameter\n\n            await provider.request({\n              method: 'wallet_addEthereumChain',\n              params: [addEthereumChain],\n            })\n\n            return chain\n          } catch (error) {\n            throw new UserRejectedRequestError(error as Error)\n          }\n        }\n\n        throw new SwitchChainError(error as Error)\n      }\n    },\n    onAccountsChanged(accounts) {\n      if (accounts.length === 0) this.onDisconnect()\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onDisconnect(_error) {\n      config.emitter.emit('disconnect')\n\n      const provider = await this.getProvider()\n      if (accountsChanged) {\n        provider.removeListener('accountsChanged', accountsChanged)\n        accountsChanged = undefined\n      }\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n    },\n  }))\n}\n", "import type {\n  createCoinbaseWalletSDK,\n  Preference,\n  ProviderInterface,\n} from '@coinbase/wallet-sdk'\nimport {\n  ChainNotConfiguredError,\n  type Connector,\n  createConnector,\n} from '@wagmi/core'\nimport type { Compute, Mutable, Omit } from '@wagmi/core/internal'\nimport type {\n  CoinbaseWalletProvider as CBW_Provider,\n  CoinbaseWalletSDK as CBW_SDK,\n} from 'cbw-sdk'\nimport {\n  type AddEthereumChainParameter,\n  type Address,\n  getAddress,\n  type Hex,\n  numberToHex,\n  type ProviderRpcError,\n  SwitchChainError,\n  UserRejectedRequestError,\n} from 'viem'\n\ntype Version = '3' | '4'\n\nexport type CoinbaseWalletParameters<version extends Version = '3'> =\n  version extends '4'\n    ? Compute<\n        {\n          headlessMode?: false | undefined\n          /** Coinbase Wallet SDK version */\n          version?: version | '3' | undefined\n        } & Version4Parameters\n      >\n    : Compute<\n        {\n          /**\n           * @deprecated `headlessMode` will be removed in the next major version. Upgrade to `version: '4'`.\n           */\n          headlessMode?: true | undefined\n          /**\n           * Coinbase Wallet SDK version\n           * @deprecated Version 3 will be removed in the next major version. Upgrade to `version: '4'`.\n           * @default '4'\n           */\n          version?: version | '4' | undefined\n        } & Version3Parameters\n      >\n\ncoinbaseWallet.type = 'coinbaseWallet' as const\nexport function coinbaseWallet<version extends Version>(\n  parameters: CoinbaseWalletParameters<version> = {} as any,\n): version extends '4'\n  ? ReturnType<typeof version4>\n  : ReturnType<typeof version3> {\n  if (parameters.version === '3' || parameters.headlessMode)\n    return version3(parameters as Version3Parameters) as any\n  return version4(parameters as Version4Parameters) as any\n}\n\ntype Version4Parameters = Mutable<\n  Omit<\n    Parameters<typeof createCoinbaseWalletSDK>[0],\n    | 'appChainIds' // set via wagmi config\n    | 'preference'\n  > & {\n    // TODO(v3): Remove `Preference['options']`\n    /**\n     * Preference for the type of wallet to display.\n     * @default 'all'\n     */\n    preference?: Preference['options'] | Compute<Preference> | undefined\n  }\n>\n\nfunction version4(parameters: Version4Parameters) {\n  type Provider = ProviderInterface & {\n    // for backwards compatibility\n    close?(): void\n  }\n  type Properties = {\n    connect(parameters?: {\n      chainId?: number | undefined\n      instantOnboarding?: boolean | undefined\n      isReconnecting?: boolean | undefined\n    }): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n  }\n\n  let walletProvider: Provider | undefined\n\n  let accountsChanged: Connector['onAccountsChanged'] | undefined\n  let chainChanged: Connector['onChainChanged'] | undefined\n  let disconnect: Connector['onDisconnect'] | undefined\n\n  return createConnector<Provider, Properties>((config) => ({\n    id: 'coinbaseWalletSDK',\n    name: 'Coinbase Wallet',\n    rdns: 'com.coinbase.wallet',\n    type: coinbaseWallet.type,\n    async connect({ chainId, ...rest } = {}) {\n      try {\n        const provider = await this.getProvider()\n        const accounts = (\n          (await provider.request({\n            method: 'eth_requestAccounts',\n            params:\n              'instantOnboarding' in rest && rest.instantOnboarding\n                ? [{ onboarding: 'instant' }]\n                : [],\n          })) as string[]\n        ).map((x) => getAddress(x))\n\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n\n        // Switch to chain if provided\n        let currentChainId = await this.getChainId()\n        if (chainId && currentChainId !== chainId) {\n          const chain = await this.switchChain!({ chainId }).catch((error) => {\n            if (error.code === UserRejectedRequestError.code) throw error\n            return { id: currentChainId }\n          })\n          currentChainId = chain?.id ?? currentChainId\n        }\n\n        return { accounts, chainId: currentChainId }\n      } catch (error) {\n        if (\n          /(user closed modal|accounts received is empty|user denied account|request rejected)/i.test(\n            (error as Error).message,\n          )\n        )\n          throw new UserRejectedRequestError(error as Error)\n        throw error\n      }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n\n      if (accountsChanged) {\n        provider.removeListener('accountsChanged', accountsChanged)\n        accountsChanged = undefined\n      }\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n\n      provider.disconnect()\n      provider.close?.()\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      return (\n        (await provider.request({\n          method: 'eth_accounts',\n        })) as string[]\n      ).map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      const chainId = (await provider.request({\n        method: 'eth_chainId',\n      })) as Hex\n      return Number(chainId)\n    },\n    async getProvider() {\n      if (!walletProvider) {\n        const preference = (() => {\n          if (typeof parameters.preference === 'string')\n            return { options: parameters.preference }\n          return {\n            ...parameters.preference,\n            options: parameters.preference?.options ?? 'all',\n          }\n        })()\n\n        const { createCoinbaseWalletSDK } = await import('@coinbase/wallet-sdk')\n        const sdk = createCoinbaseWalletSDK({\n          ...parameters,\n          appChainIds: config.chains.map((x) => x.id),\n          preference,\n        })\n\n        walletProvider = sdk.getProvider()\n      }\n\n      return walletProvider\n    },\n    async isAuthorized() {\n      try {\n        const accounts = await this.getAccounts()\n        return !!accounts.length\n      } catch {\n        return false\n      }\n    },\n    async switchChain({ addEthereumChainParameter, chainId }) {\n      const chain = config.chains.find((chain) => chain.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      const provider = await this.getProvider()\n\n      try {\n        await provider.request({\n          method: 'wallet_switchEthereumChain',\n          params: [{ chainId: numberToHex(chain.id) }],\n        })\n        return chain\n      } catch (error) {\n        // Indicates chain is not added to provider\n        if ((error as ProviderRpcError).code === 4902) {\n          try {\n            let blockExplorerUrls: string[] | undefined\n            if (addEthereumChainParameter?.blockExplorerUrls)\n              blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls\n            else\n              blockExplorerUrls = chain.blockExplorers?.default.url\n                ? [chain.blockExplorers?.default.url]\n                : []\n\n            let rpcUrls: readonly string[]\n            if (addEthereumChainParameter?.rpcUrls?.length)\n              rpcUrls = addEthereumChainParameter.rpcUrls\n            else rpcUrls = [chain.rpcUrls.default?.http[0] ?? '']\n\n            const addEthereumChain = {\n              blockExplorerUrls,\n              chainId: numberToHex(chainId),\n              chainName: addEthereumChainParameter?.chainName ?? chain.name,\n              iconUrls: addEthereumChainParameter?.iconUrls,\n              nativeCurrency:\n                addEthereumChainParameter?.nativeCurrency ??\n                chain.nativeCurrency,\n              rpcUrls,\n            } satisfies AddEthereumChainParameter\n\n            await provider.request({\n              method: 'wallet_addEthereumChain',\n              params: [addEthereumChain],\n            })\n\n            return chain\n          } catch (error) {\n            throw new UserRejectedRequestError(error as Error)\n          }\n        }\n\n        throw new SwitchChainError(error as Error)\n      }\n    },\n    onAccountsChanged(accounts) {\n      if (accounts.length === 0) this.onDisconnect()\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onDisconnect(_error) {\n      config.emitter.emit('disconnect')\n\n      const provider = await this.getProvider()\n      if (accountsChanged) {\n        provider.removeListener('accountsChanged', accountsChanged)\n        accountsChanged = undefined\n      }\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n    },\n  }))\n}\n\ntype Version3Parameters = Mutable<\n  Omit<\n    ConstructorParameters<typeof CBW_SDK>[0],\n    'reloadOnDisconnect' // remove property since TSDoc says default is `true`\n  >\n> & {\n  /**\n   * Fallback Ethereum JSON RPC URL\n   * @default \"\"\n   */\n  jsonRpcUrl?: string | undefined\n  /**\n   * Fallback Ethereum Chain ID\n   * @default 1\n   */\n  chainId?: number | undefined\n  /**\n   * Whether or not to reload dapp automatically after disconnect.\n   * @default false\n   */\n  reloadOnDisconnect?: boolean | undefined\n}\n\nfunction version3(parameters: Version3Parameters) {\n  const reloadOnDisconnect = false\n\n  type Provider = CBW_Provider\n\n  let sdk: CBW_SDK | undefined\n  let walletProvider: Provider | undefined\n\n  let accountsChanged: Connector['onAccountsChanged'] | undefined\n  let chainChanged: Connector['onChainChanged'] | undefined\n  let disconnect: Connector['onDisconnect'] | undefined\n\n  return createConnector<Provider>((config) => ({\n    id: 'coinbaseWalletSDK',\n    name: 'Coinbase Wallet',\n    rdns: 'com.coinbase.wallet',\n    type: coinbaseWallet.type,\n    async connect({ chainId } = {}) {\n      try {\n        const provider = await this.getProvider()\n        const accounts = (\n          (await provider.request({\n            method: 'eth_requestAccounts',\n          })) as string[]\n        ).map((x) => getAddress(x))\n\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n\n        // Switch to chain if provided\n        let currentChainId = await this.getChainId()\n        if (chainId && currentChainId !== chainId) {\n          const chain = await this.switchChain!({ chainId }).catch((error) => {\n            if (error.code === UserRejectedRequestError.code) throw error\n            return { id: currentChainId }\n          })\n          currentChainId = chain?.id ?? currentChainId\n        }\n\n        return { accounts, chainId: currentChainId }\n      } catch (error) {\n        if (\n          /(user closed modal|accounts received is empty|user denied account)/i.test(\n            (error as Error).message,\n          )\n        )\n          throw new UserRejectedRequestError(error as Error)\n        throw error\n      }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n\n      if (accountsChanged) {\n        provider.removeListener('accountsChanged', accountsChanged)\n        accountsChanged = undefined\n      }\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n\n      provider.disconnect()\n      provider.close()\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      return (\n        await provider.request<string[]>({\n          method: 'eth_accounts',\n        })\n      ).map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      const chainId = await provider.request<Hex>({\n        method: 'eth_chainId',\n      })\n      return Number(chainId)\n    },\n    async getProvider() {\n      if (!walletProvider) {\n        // Unwrapping import for Vite compatibility.\n        // See: https://github.com/vitejs/vite/issues/9703\n        const CoinbaseWalletSDK = await (async () => {\n          const { default: SDK } = await import('cbw-sdk')\n          if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n            return SDK.default\n          return SDK as unknown as typeof SDK.default\n        })()\n\n        sdk = new CoinbaseWalletSDK({ ...parameters, reloadOnDisconnect })\n\n        // Force types to retrieve private `walletExtension` method from the Coinbase Wallet SDK.\n        const walletExtensionChainId = (\n          sdk as unknown as {\n            get walletExtension(): { getChainId(): number } | undefined\n          }\n        ).walletExtension?.getChainId()\n\n        const chain =\n          config.chains.find((chain) =>\n            parameters.chainId\n              ? chain.id === parameters.chainId\n              : chain.id === walletExtensionChainId,\n          ) || config.chains[0]\n        const chainId = parameters.chainId || chain?.id\n        const jsonRpcUrl =\n          parameters.jsonRpcUrl || chain?.rpcUrls.default.http[0]\n\n        walletProvider = sdk.makeWeb3Provider(jsonRpcUrl, chainId)\n      }\n\n      return walletProvider\n    },\n    async isAuthorized() {\n      try {\n        const accounts = await this.getAccounts()\n        return !!accounts.length\n      } catch {\n        return false\n      }\n    },\n    async switchChain({ addEthereumChainParameter, chainId }) {\n      const chain = config.chains.find((chain) => chain.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      const provider = await this.getProvider()\n\n      try {\n        await provider.request({\n          method: 'wallet_switchEthereumChain',\n          params: [{ chainId: numberToHex(chain.id) }],\n        })\n        return chain\n      } catch (error) {\n        // Indicates chain is not added to provider\n        if ((error as ProviderRpcError).code === 4902) {\n          try {\n            let blockExplorerUrls: string[] | undefined\n            if (addEthereumChainParameter?.blockExplorerUrls)\n              blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls\n            else\n              blockExplorerUrls = chain.blockExplorers?.default.url\n                ? [chain.blockExplorers?.default.url]\n                : []\n\n            let rpcUrls: readonly string[]\n            if (addEthereumChainParameter?.rpcUrls?.length)\n              rpcUrls = addEthereumChainParameter.rpcUrls\n            else rpcUrls = [chain.rpcUrls.default?.http[0] ?? '']\n\n            const addEthereumChain = {\n              blockExplorerUrls,\n              chainId: numberToHex(chainId),\n              chainName: addEthereumChainParameter?.chainName ?? chain.name,\n              iconUrls: addEthereumChainParameter?.iconUrls,\n              nativeCurrency:\n                addEthereumChainParameter?.nativeCurrency ??\n                chain.nativeCurrency,\n              rpcUrls,\n            } satisfies AddEthereumChainParameter\n\n            await provider.request({\n              method: 'wallet_addEthereumChain',\n              params: [addEthereumChain],\n            })\n\n            return chain\n          } catch (error) {\n            throw new UserRejectedRequestError(error as Error)\n          }\n        }\n\n        throw new SwitchChainError(error as Error)\n      }\n    },\n    onAccountsChanged(accounts) {\n      if (accounts.length === 0) this.onDisconnect()\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onDisconnect(_error) {\n      config.emitter.emit('disconnect')\n\n      const provider = await this.getProvider()\n      if (accountsChanged) {\n        provider.removeListener('accountsChanged', accountsChanged)\n        accountsChanged = undefined\n      }\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n    },\n  }))\n}\n", "import type {\n  MetaMaskSDK,\n  MetaMaskSDKOptions,\n  RPC_URLS_MAP,\n  SDKProvider,\n} from '@metamask/sdk'\nimport {\n  ChainNotConfiguredError,\n  type Connector,\n  createConnector,\n  extractRpcUrls,\n  ProviderNotFoundError,\n} from '@wagmi/core'\nimport type {\n  Compute,\n  ExactPartial,\n  OneOf,\n  RemoveUndefined,\n  UnionCompute,\n} from '@wagmi/core/internal'\nimport {\n  type AddEthereumChainParameter,\n  type Address,\n  getAddress,\n  type Hex,\n  hexToNumber,\n  numberToHex,\n  type ProviderConnectInfo,\n  type ProviderRpcError,\n  ResourceUnavailableRpcError,\n  type RpcError,\n  SwitchChainError,\n  UserRejectedRequestError,\n  withRetry,\n  withTimeout,\n} from 'viem'\n\nexport type MetaMaskParameters = UnionCompute<\n  WagmiMetaMaskSDKOptions &\n    OneOf<\n      | {\n          /* Shortcut to connect and sign a message */\n          connectAndSign?: string | undefined\n        }\n      | {\n          // TODO: Strongly type `method` and `params`\n          /* Allow `connectWith` any rpc method */\n          connectWith?: { method: string; params: unknown[] } | undefined\n        }\n    >\n>\n\ntype WagmiMetaMaskSDKOptions = Compute<\n  ExactPartial<\n    Omit<\n      MetaMaskSDKOptions,\n      | '_source'\n      | 'forceDeleteProvider'\n      | 'forceInjectProvider'\n      | 'injectProvider'\n      | 'useDeeplink'\n      | 'readonlyRPCMap'\n    >\n  > & {\n    /** @deprecated */\n    forceDeleteProvider?: MetaMaskSDKOptions['forceDeleteProvider']\n    /** @deprecated */\n    forceInjectProvider?: MetaMaskSDKOptions['forceInjectProvider']\n    /** @deprecated */\n    injectProvider?: MetaMaskSDKOptions['injectProvider']\n    /** @deprecated */\n    useDeeplink?: MetaMaskSDKOptions['useDeeplink']\n  }\n>\n\nmetaMask.type = 'metaMask' as const\nexport function metaMask(parameters: MetaMaskParameters = {}) {\n  type Provider = SDKProvider\n  type Properties = {\n    onConnect(connectInfo: ProviderConnectInfo): void\n    onDisplayUri(uri: string): void\n  }\n  type Listener = Parameters<Provider['on']>[1]\n\n  let sdk: MetaMaskSDK\n  let provider: Provider | undefined\n  let providerPromise: Promise<typeof provider>\n\n  let accountsChanged: Connector['onAccountsChanged'] | undefined\n  let chainChanged: Connector['onChainChanged'] | undefined\n  let connect: Connector['onConnect'] | undefined\n  let displayUri: ((uri: string) => void) | undefined\n  let disconnect: Connector['onDisconnect'] | undefined\n\n  return createConnector<Provider, Properties>((config) => ({\n    id: 'metaMaskSDK',\n    name: 'MetaMask',\n    rdns: ['io.metamask', 'io.metamask.mobile'],\n    type: metaMask.type,\n    async setup() {\n      const provider = await this.getProvider()\n      if (provider?.on) {\n        if (!connect) {\n          connect = this.onConnect.bind(this)\n          provider.on('connect', connect as Listener)\n        }\n\n        // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n        // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged as Listener)\n        }\n      }\n    },\n    async connect({ chainId, isReconnecting } = {}) {\n      const provider = await this.getProvider()\n      if (!displayUri) {\n        displayUri = this.onDisplayUri\n        provider.on('display_uri', displayUri as Listener)\n      }\n\n      let accounts: readonly Address[] = []\n      if (isReconnecting) accounts = await this.getAccounts().catch(() => [])\n\n      try {\n        let signResponse: string | undefined\n        let connectWithResponse: unknown | undefined\n        if (!accounts?.length) {\n          if (parameters.connectAndSign || parameters.connectWith) {\n            if (parameters.connectAndSign)\n              signResponse = await sdk.connectAndSign({\n                msg: parameters.connectAndSign,\n              })\n            else if (parameters.connectWith)\n              connectWithResponse = await sdk.connectWith({\n                method: parameters.connectWith.method,\n                params: parameters.connectWith.params,\n              })\n\n            accounts = await this.getAccounts()\n          } else {\n            const requestedAccounts = (await sdk.connect()) as string[]\n            accounts = requestedAccounts.map((x) => getAddress(x))\n          }\n        }\n        // Switch to chain if provided\n        let currentChainId = (await this.getChainId()) as number\n        if (chainId && currentChainId !== chainId) {\n          const chain = await this.switchChain!({ chainId }).catch((error) => {\n            if (error.code === UserRejectedRequestError.code) throw error\n            return { id: currentChainId }\n          })\n          currentChainId = chain?.id ?? currentChainId\n        }\n\n        if (displayUri) {\n          provider.removeListener('display_uri', displayUri)\n          displayUri = undefined\n        }\n\n        if (signResponse)\n          provider.emit('connectAndSign', {\n            accounts,\n            chainId: currentChainId,\n            signResponse,\n          })\n        else if (connectWithResponse)\n          provider.emit('connectWith', {\n            accounts,\n            chainId: currentChainId,\n            connectWithResponse,\n          })\n\n        // Manage EIP-1193 event listeners\n        // https://eips.ethereum.org/EIPS/eip-1193#events\n        if (connect) {\n          provider.removeListener('connect', connect)\n          connect = undefined\n        }\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged as Listener)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged as Listener)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect as Listener)\n        }\n\n        return { accounts, chainId: currentChainId }\n      } catch (err) {\n        const error = err as RpcError\n        if (error.code === UserRejectedRequestError.code)\n          throw new UserRejectedRequestError(error)\n        if (error.code === ResourceUnavailableRpcError.code)\n          throw new ResourceUnavailableRpcError(error)\n        throw error\n      }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n\n      // Manage EIP-1193 event listeners\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n      if (!connect) {\n        connect = this.onConnect.bind(this)\n        provider.on('connect', connect as Listener)\n      }\n\n      await sdk.terminate()\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      const accounts = (await provider.request({\n        method: 'eth_accounts',\n      })) as string[]\n      return accounts.map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      const chainId =\n        provider.getChainId() ||\n        (await provider?.request({ method: 'eth_chainId' }))\n      return Number(chainId)\n    },\n    async getProvider() {\n      async function initProvider() {\n        // Unwrapping import for Vite compatibility.\n        // See: https://github.com/vitejs/vite/issues/9703\n        const MetaMaskSDK = await (async () => {\n          const { default: SDK } = await import('@metamask/sdk')\n          if (typeof SDK !== 'function' && typeof SDK.default === 'function')\n            return SDK.default\n          return SDK as unknown as typeof SDK.default\n        })()\n\n        const readonlyRPCMap: RPC_URLS_MAP = {}\n        for (const chain of config.chains)\n          readonlyRPCMap[numberToHex(chain.id)] = extractRpcUrls({\n            chain,\n            transports: config.transports,\n          })?.[0]\n\n        sdk = new MetaMaskSDK({\n          _source: 'wagmi',\n          forceDeleteProvider: false,\n          forceInjectProvider: false,\n          injectProvider: false,\n          // Workaround cast since MetaMask SDK does not support `'exactOptionalPropertyTypes'`\n          ...(parameters as RemoveUndefined<typeof parameters>),\n          readonlyRPCMap,\n          dappMetadata: {\n            ...parameters.dappMetadata,\n            // Test if name and url are set AND not empty\n            name: parameters.dappMetadata?.name\n              ? parameters.dappMetadata?.name\n              : 'wagmi',\n            url: parameters.dappMetadata?.url\n              ? parameters.dappMetadata?.url\n              : typeof window !== 'undefined'\n                ? window.location.origin\n                : 'https://wagmi.sh',\n          },\n          useDeeplink: parameters.useDeeplink ?? true,\n        })\n        const result = await sdk.init()\n        // On initial load, sometimes `sdk.getProvider` does not return provider.\n        // https://github.com/wevm/wagmi/issues/4367\n        // Use result of `init` call if available.\n        const provider = (() => {\n          if (result?.activeProvider) return result.activeProvider\n          return sdk.getProvider()\n        })()\n        if (!provider) throw new ProviderNotFoundError()\n        return provider\n      }\n\n      if (!provider) {\n        if (!providerPromise) providerPromise = initProvider()\n        provider = await providerPromise\n      }\n      return provider!\n    },\n    async isAuthorized() {\n      try {\n        // MetaMask mobile provider sometimes fails to immediately resolve\n        // JSON-RPC requests on page load\n        const timeout = 200\n        const accounts = await withRetry(\n          () => withTimeout(() => this.getAccounts(), { timeout }),\n          {\n            delay: timeout + 1,\n            retryCount: 3,\n          },\n        )\n        return !!accounts.length\n      } catch {\n        return false\n      }\n    },\n    async switchChain({ addEthereumChainParameter, chainId }) {\n      const provider = await this.getProvider()\n\n      const chain = config.chains.find((x) => x.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      try {\n        await provider.request({\n          method: 'wallet_switchEthereumChain',\n          params: [{ chainId: numberToHex(chainId) }],\n        })\n\n        // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n        // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n        // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n        // this callback or an externally emitted `'chainChanged'` event.\n        // https://github.com/MetaMask/metamask-extension/issues/24247\n        await waitForChainIdToSync()\n        await sendAndWaitForChangeEvent(chainId)\n\n        return chain\n      } catch (err) {\n        const error = err as RpcError\n\n        if (error.code === UserRejectedRequestError.code)\n          throw new UserRejectedRequestError(error)\n\n        // Indicates chain is not added to provider\n        if (\n          error.code === 4902 ||\n          // Unwrapping for MetaMask Mobile\n          // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n          (error as ProviderRpcError<{ originalError?: { code: number } }>)\n            ?.data?.originalError?.code === 4902\n        ) {\n          try {\n            await provider.request({\n              method: 'wallet_addEthereumChain',\n              params: [\n                {\n                  blockExplorerUrls: (() => {\n                    const { default: blockExplorer, ...blockExplorers } =\n                      chain.blockExplorers ?? {}\n                    if (addEthereumChainParameter?.blockExplorerUrls)\n                      return addEthereumChainParameter.blockExplorerUrls\n                    if (blockExplorer)\n                      return [\n                        blockExplorer.url,\n                        ...Object.values(blockExplorers).map((x) => x.url),\n                      ]\n                    return\n                  })(),\n                  chainId: numberToHex(chainId),\n                  chainName: addEthereumChainParameter?.chainName ?? chain.name,\n                  iconUrls: addEthereumChainParameter?.iconUrls,\n                  nativeCurrency:\n                    addEthereumChainParameter?.nativeCurrency ??\n                    chain.nativeCurrency,\n                  rpcUrls: (() => {\n                    if (addEthereumChainParameter?.rpcUrls?.length)\n                      return addEthereumChainParameter.rpcUrls\n                    return [chain.rpcUrls.default?.http[0] ?? '']\n                  })(),\n                } satisfies AddEthereumChainParameter,\n              ],\n            })\n\n            await waitForChainIdToSync()\n            await sendAndWaitForChangeEvent(chainId)\n\n            return chain\n          } catch (err) {\n            const error = err as RpcError\n            if (error.code === UserRejectedRequestError.code)\n              throw new UserRejectedRequestError(error)\n            throw new SwitchChainError(error)\n          }\n        }\n\n        throw new SwitchChainError(error)\n      }\n\n      async function waitForChainIdToSync() {\n        // On mobile, there is a race condition between the result of `'wallet_addEthereumChain'` and `'eth_chainId'`.\n        // To avoid this, we wait for `'eth_chainId'` to return the expected chain ID with a retry loop.\n        await withRetry(\n          async () => {\n            const value = hexToNumber(\n              // `'eth_chainId'` is cached by the MetaMask SDK side to avoid unnecessary deeplinks\n              (await provider.request({ method: 'eth_chainId' })) as Hex,\n            )\n            // `value` doesn't match expected `chainId`, throw to trigger retry\n            if (value !== chainId)\n              throw new Error('User rejected switch after adding network.')\n            return value\n          },\n          {\n            delay: 50,\n            retryCount: 20, // android device encryption is slower\n          },\n        )\n      }\n\n      async function sendAndWaitForChangeEvent(chainId: number) {\n        await new Promise<void>((resolve) => {\n          const listener = ((data) => {\n            if ('chainId' in data && data.chainId === chainId) {\n              config.emitter.off('change', listener)\n              resolve()\n            }\n          }) satisfies Parameters<typeof config.emitter.on>[1]\n          config.emitter.on('change', listener)\n          config.emitter.emit('change', { chainId })\n        })\n      }\n    },\n    async onAccountsChanged(accounts) {\n      // Disconnect if there are no accounts\n      if (accounts.length === 0) {\n        // ... and using browser extension\n        if (sdk.isExtensionActive()) this.onDisconnect()\n        // FIXME(upstream): Mobile app sometimes emits invalid `accountsChanged` event with empty accounts array\n        else return\n      }\n      // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n      else if (config.emitter.listenerCount('connect')) {\n        const chainId = (await this.getChainId()).toString()\n        this.onConnect({ chainId })\n      }\n      // Regular change event\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onConnect(connectInfo) {\n      const accounts = await this.getAccounts()\n      if (accounts.length === 0) return\n\n      const chainId = Number(connectInfo.chainId)\n      config.emitter.emit('connect', { accounts, chainId })\n\n      const provider = await this.getProvider()\n      if (connect) {\n        provider.removeListener('connect', connect)\n        connect = undefined\n      }\n      if (!accountsChanged) {\n        accountsChanged = this.onAccountsChanged.bind(this)\n        provider.on('accountsChanged', accountsChanged as Listener)\n      }\n      if (!chainChanged) {\n        chainChanged = this.onChainChanged.bind(this)\n        provider.on('chainChanged', chainChanged as Listener)\n      }\n      if (!disconnect) {\n        disconnect = this.onDisconnect.bind(this)\n        provider.on('disconnect', disconnect as Listener)\n      }\n    },\n    async onDisconnect(error) {\n      const provider = await this.getProvider()\n\n      // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n      // https://github.com/MetaMask/providers/pull/120\n      if (error && (error as RpcError<1013>).code === 1013) {\n        if (provider && !!(await this.getAccounts()).length) return\n      }\n\n      config.emitter.emit('disconnect')\n\n      // Manage EIP-1193 event listeners\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n      if (!connect) {\n        connect = this.onConnect.bind(this)\n        provider.on('connect', connect as Listener)\n      }\n    },\n    onDisplayUri(uri) {\n      config.emitter.emit('message', { type: 'display_uri', data: uri })\n    },\n  }))\n}\n", "import type { SafeAppProvider } from '@safe-global/safe-apps-provider'\nimport type { Opts } from '@safe-global/safe-apps-sdk'\nimport {\n  type Connector,\n  createConnector,\n  ProviderNotFoundError,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport { getAddress, withTimeout } from 'viem'\n\nexport type SafeParameters = Compute<\n  Opts & {\n    /**\n     * Connector automatically connects when used as Safe App.\n     *\n     * This flag simulates the disconnect behavior by keeping track of connection status in storage\n     * and only autoconnecting when previously connected by user action (e.g. explicitly choosing to connect).\n     *\n     * @default false\n     */\n    shimDisconnect?: boolean | undefined\n    /**\n     * Timeout in milliseconds for `getInfo` (from the Safe SDK) to resolve.\n     *\n     * `getInfo` does not resolve when not used in Safe App iFrame. This allows the connector to force a timeout.\n     * @default 10\n     */\n    unstable_getInfoTimeout?: number | undefined\n  }\n>\n\nsafe.type = 'safe' as const\nexport function safe(parameters: SafeParameters = {}) {\n  const { shimDisconnect = false } = parameters\n\n  type Provider = SafeAppProvider | undefined\n  type Properties = Record<string, unknown>\n  type StorageItem = { 'safe.disconnected': true }\n\n  let provider_: Provider | undefined\n\n  let disconnect: Connector['onDisconnect'] | undefined\n\n  return createConnector<Provider, Properties, StorageItem>((config) => ({\n    id: 'safe',\n    name: 'Safe',\n    type: safe.type,\n    async connect() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      const accounts = await this.getAccounts()\n      const chainId = await this.getChainId()\n\n      if (!disconnect) {\n        disconnect = this.onDisconnect.bind(this)\n        provider.on('disconnect', disconnect)\n      }\n\n      // Remove disconnected shim if it exists\n      if (shimDisconnect) await config.storage?.removeItem('safe.disconnected')\n\n      return { accounts, chainId }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n\n      // Add shim signalling connector is disconnected\n      if (shimDisconnect)\n        await config.storage?.setItem('safe.disconnected', true)\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n      return (await provider.request({ method: 'eth_accounts' })).map(\n        getAddress,\n      )\n    },\n    async getProvider() {\n      // Only allowed in iframe context\n      const isIframe =\n        typeof window !== 'undefined' && window?.parent !== window\n      if (!isIframe) return\n\n      if (!provider_) {\n        const { default: SDK } = await import('@safe-global/safe-apps-sdk')\n        const sdk = new SDK(parameters)\n\n        // `getInfo` hangs when not used in Safe App iFrame\n        // https://github.com/safe-global/safe-apps-sdk/issues/263#issuecomment-**********\n        const safe = await withTimeout(() => sdk.safe.getInfo(), {\n          timeout: parameters.unstable_getInfoTimeout ?? 10,\n        })\n        if (!safe) throw new Error('Could not load Safe information')\n        // Unwrapping import for Vite compatibility.\n        // See: https://github.com/vitejs/vite/issues/9703\n        const SafeAppProvider = await (async () => {\n          const Provider = await import('@safe-global/safe-apps-provider')\n          if (\n            typeof Provider.SafeAppProvider !== 'function' &&\n            typeof Provider.default.SafeAppProvider === 'function'\n          )\n            return Provider.default.SafeAppProvider\n          return Provider.SafeAppProvider\n        })()\n        provider_ = new SafeAppProvider(safe, sdk)\n      }\n      return provider_\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n      return Number(provider.chainId)\n    },\n    async isAuthorized() {\n      try {\n        const isDisconnected =\n          shimDisconnect &&\n          // If shim exists in storage, connector is disconnected\n          (await config.storage?.getItem('safe.disconnected'))\n        if (isDisconnected) return false\n\n        const accounts = await this.getAccounts()\n        return !!accounts.length\n      } catch {\n        return false\n      }\n    },\n    onAccountsChanged() {\n      // Not relevant for Safe because changing account requires app reload.\n    },\n    onChainChanged() {\n      // Not relevant for Safe because Safe smart contract wallets only exist on single chain.\n    },\n    onDisconnect() {\n      config.emitter.emit('disconnect')\n    },\n  }))\n}\n", "export const version = '5.9.1'\n", "import {\n  ChainNotConfiguredError,\n  type Connector,\n  createConnector,\n  extractRpcUrls,\n  ProviderNotFoundError,\n} from '@wagmi/core'\nimport type { Compute, ExactPartial, Omit } from '@wagmi/core/internal'\nimport type { EthereumProvider } from '@walletconnect/ethereum-provider'\nimport {\n  type AddEthereumChainParameter,\n  type Address,\n  getAddress,\n  numberToHex,\n  type ProviderConnectInfo,\n  type ProviderRpcError,\n  type RpcError,\n  SwitchChainError,\n  UserRejectedRequestError,\n} from 'viem'\n\ntype WalletConnectConnector = Connector & {\n  onDisplayUri(uri: string): void\n  onSessionDelete(data: { topic: string }): void\n}\n\ntype EthereumProviderOptions = Parameters<(typeof EthereumProvider)['init']>[0]\n\nexport type WalletConnectParameters = Compute<\n  {\n    /**\n     * If a new chain is added to a previously existing configured connector `chains`, this flag\n     * will determine if that chain should be considered as stale. A stale chain is a chain that\n     * WalletConnect has yet to establish a relationship with (e.g. the user has not approved or\n     * rejected the chain).\n     *\n     * This flag mainly affects the behavior when a wallet does not support dynamic chain authorization\n     * with WalletConnect v2.\n     *\n     * If `true` (default), the new chain will be treated as a stale chain. If the user\n     * has yet to establish a relationship (approved/rejected) with this chain in their WalletConnect\n     * session, the connector will disconnect upon the dapp auto-connecting, and the user will have to\n     * reconnect to the dapp (revalidate the chain) in order to approve the newly added chain.\n     * This is the default behavior to avoid an unexpected error upon switching chains which may\n     * be a confusing user experience (e.g. the user will not know they have to reconnect\n     * unless the dapp handles these types of errors).\n     *\n     * If `false`, the new chain will be treated as a potentially valid chain. This means that if the user\n     * has yet to establish a relationship with the chain in their WalletConnect session, wagmi will successfully\n     * auto-connect the user. This comes with the trade-off that the connector will throw an error\n     * when attempting to switch to the unapproved chain if the wallet does not support dynamic session updates.\n     * This may be useful in cases where a dapp constantly\n     * modifies their configured chains, and they do not want to disconnect the user upon\n     * auto-connecting. If the user decides to switch to the unapproved chain, it is important that the\n     * dapp handles this error and prompts the user to reconnect to the dapp in order to approve\n     * the newly added chain.\n     *\n     * @default true\n     */\n    isNewChainsStale?: boolean\n  } & Omit<\n    EthereumProviderOptions,\n    | 'chains'\n    | 'events'\n    | 'optionalChains'\n    | 'optionalEvents'\n    | 'optionalMethods'\n    | 'methods'\n    | 'rpcMap'\n    | 'showQrModal'\n  > &\n    ExactPartial<Pick<EthereumProviderOptions, 'showQrModal'>>\n>\n\nwalletConnect.type = 'walletConnect' as const\nexport function walletConnect(parameters: WalletConnectParameters) {\n  const isNewChainsStale = parameters.isNewChainsStale ?? true\n\n  type Provider = Awaited<ReturnType<(typeof EthereumProvider)['init']>>\n  type Properties = {\n    connect(parameters?: {\n      chainId?: number | undefined\n      isReconnecting?: boolean | undefined\n      pairingTopic?: string | undefined\n    }): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n    getNamespaceChainsIds(): number[]\n    getRequestedChainsIds(): Promise<number[]>\n    isChainsStale(): Promise<boolean>\n    onConnect(connectInfo: ProviderConnectInfo): void\n    onDisplayUri(uri: string): void\n    onSessionDelete(data: { topic: string }): void\n    setRequestedChainsIds(chains: number[]): void\n    requestedChainsStorageKey: `${string}.requestedChains`\n  }\n  type StorageItem = {\n    [_ in Properties['requestedChainsStorageKey']]: number[]\n  }\n\n  let provider_: Provider | undefined\n  let providerPromise: Promise<typeof provider_>\n  const NAMESPACE = 'eip155'\n\n  let accountsChanged: WalletConnectConnector['onAccountsChanged'] | undefined\n  let chainChanged: WalletConnectConnector['onChainChanged'] | undefined\n  let connect: WalletConnectConnector['onConnect'] | undefined\n  let displayUri: WalletConnectConnector['onDisplayUri'] | undefined\n  let sessionDelete: WalletConnectConnector['onSessionDelete'] | undefined\n  let disconnect: WalletConnectConnector['onDisconnect'] | undefined\n\n  return createConnector<Provider, Properties, StorageItem>((config) => ({\n    id: 'walletConnect',\n    name: 'WalletConnect',\n    type: walletConnect.type,\n    async setup() {\n      const provider = await this.getProvider().catch(() => null)\n      if (!provider) return\n      if (!connect) {\n        connect = this.onConnect.bind(this)\n        provider.on('connect', connect)\n      }\n      if (!sessionDelete) {\n        sessionDelete = this.onSessionDelete.bind(this)\n        provider.on('session_delete', sessionDelete)\n      }\n    },\n    async connect({ chainId, ...rest } = {}) {\n      try {\n        const provider = await this.getProvider()\n        if (!provider) throw new ProviderNotFoundError()\n        if (!displayUri) {\n          displayUri = this.onDisplayUri\n          provider.on('display_uri', displayUri)\n        }\n\n        let targetChainId = chainId\n        if (!targetChainId) {\n          const state = (await config.storage?.getItem('state')) ?? {}\n          const isChainSupported = config.chains.some(\n            (x) => x.id === state.chainId,\n          )\n          if (isChainSupported) targetChainId = state.chainId\n          else targetChainId = config.chains[0]?.id\n        }\n        if (!targetChainId) throw new Error('No chains found on connector.')\n\n        const isChainsStale = await this.isChainsStale()\n        // If there is an active session with stale chains, disconnect current session.\n        if (provider.session && isChainsStale) await provider.disconnect()\n\n        // If there isn't an active session or chains are stale, connect.\n        if (!provider.session || isChainsStale) {\n          const optionalChains = config.chains\n            .filter((chain) => chain.id !== targetChainId)\n            .map((optionalChain) => optionalChain.id)\n          await provider.connect({\n            optionalChains: [targetChainId, ...optionalChains],\n            ...('pairingTopic' in rest\n              ? { pairingTopic: rest.pairingTopic }\n              : {}),\n          })\n\n          this.setRequestedChainsIds(config.chains.map((x) => x.id))\n        }\n\n        // If session exists and chains are authorized, enable provider for required chain\n        const accounts = (await provider.enable()).map((x) => getAddress(x))\n        const currentChainId = await this.getChainId()\n\n        if (displayUri) {\n          provider.removeListener('display_uri', displayUri)\n          displayUri = undefined\n        }\n        if (connect) {\n          provider.removeListener('connect', connect)\n          connect = undefined\n        }\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n        if (!sessionDelete) {\n          sessionDelete = this.onSessionDelete.bind(this)\n          provider.on('session_delete', sessionDelete)\n        }\n\n        return { accounts, chainId: currentChainId }\n      } catch (error) {\n        if (\n          /(user rejected|connection request reset)/i.test(\n            (error as ProviderRpcError)?.message,\n          )\n        ) {\n          throw new UserRejectedRequestError(error as Error)\n        }\n        throw error\n      }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n      try {\n        await provider?.disconnect()\n      } catch (error) {\n        if (!/No matching key/i.test((error as Error).message)) throw error\n      } finally {\n        if (chainChanged) {\n          provider?.removeListener('chainChanged', chainChanged)\n          chainChanged = undefined\n        }\n        if (disconnect) {\n          provider?.removeListener('disconnect', disconnect)\n          disconnect = undefined\n        }\n        if (!connect) {\n          connect = this.onConnect.bind(this)\n          provider?.on('connect', connect)\n        }\n        if (accountsChanged) {\n          provider?.removeListener('accountsChanged', accountsChanged)\n          accountsChanged = undefined\n        }\n        if (sessionDelete) {\n          provider?.removeListener('session_delete', sessionDelete)\n          sessionDelete = undefined\n        }\n\n        this.setRequestedChainsIds([])\n      }\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      return provider.accounts.map((x) => getAddress(x))\n    },\n    async getProvider({ chainId } = {}) {\n      async function initProvider() {\n        const optionalChains = config.chains.map((x) => x.id) as [number]\n        if (!optionalChains.length) return\n        const { EthereumProvider } = await import(\n          '@walletconnect/ethereum-provider'\n        )\n        return await EthereumProvider.init({\n          ...parameters,\n          disableProviderPing: true,\n          optionalChains,\n          projectId: parameters.projectId,\n          rpcMap: Object.fromEntries(\n            config.chains.map((chain) => {\n              const [url] = extractRpcUrls({\n                chain,\n                transports: config.transports,\n              })\n              return [chain.id, url]\n            }),\n          ),\n          showQrModal: parameters.showQrModal ?? true,\n        })\n      }\n\n      if (!provider_) {\n        if (!providerPromise) providerPromise = initProvider()\n        provider_ = await providerPromise\n        provider_?.events.setMaxListeners(Number.POSITIVE_INFINITY)\n      }\n      if (chainId) await this.switchChain?.({ chainId })\n      return provider_!\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      return provider.chainId\n    },\n    async isAuthorized() {\n      try {\n        const [accounts, provider] = await Promise.all([\n          this.getAccounts(),\n          this.getProvider(),\n        ])\n\n        // If an account does not exist on the session, then the connector is unauthorized.\n        if (!accounts.length) return false\n\n        // If the chains are stale on the session, then the connector is unauthorized.\n        const isChainsStale = await this.isChainsStale()\n        if (isChainsStale && provider.session) {\n          await provider.disconnect().catch(() => {})\n          return false\n        }\n        return true\n      } catch {\n        return false\n      }\n    },\n    async switchChain({ addEthereumChainParameter, chainId }) {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      const chain = config.chains.find((x) => x.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      try {\n        await Promise.all([\n          new Promise<void>((resolve) => {\n            const listener = ({\n              chainId: currentChainId,\n            }: {\n              chainId?: number | undefined\n            }) => {\n              if (currentChainId === chainId) {\n                config.emitter.off('change', listener)\n                resolve()\n              }\n            }\n            config.emitter.on('change', listener)\n          }),\n          provider.request({\n            method: 'wallet_switchEthereumChain',\n            params: [{ chainId: numberToHex(chainId) }],\n          }),\n        ])\n\n        const requestedChains = await this.getRequestedChainsIds()\n        this.setRequestedChainsIds([...requestedChains, chainId])\n\n        return chain\n      } catch (err) {\n        const error = err as RpcError\n\n        if (/(user rejected)/i.test(error.message))\n          throw new UserRejectedRequestError(error)\n\n        // Indicates chain is not added to provider\n        try {\n          let blockExplorerUrls: string[] | undefined\n          if (addEthereumChainParameter?.blockExplorerUrls)\n            blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls\n          else\n            blockExplorerUrls = chain.blockExplorers?.default.url\n              ? [chain.blockExplorers?.default.url]\n              : []\n\n          let rpcUrls: readonly string[]\n          if (addEthereumChainParameter?.rpcUrls?.length)\n            rpcUrls = addEthereumChainParameter.rpcUrls\n          else rpcUrls = [...chain.rpcUrls.default.http]\n\n          const addEthereumChain = {\n            blockExplorerUrls,\n            chainId: numberToHex(chainId),\n            chainName: addEthereumChainParameter?.chainName ?? chain.name,\n            iconUrls: addEthereumChainParameter?.iconUrls,\n            nativeCurrency:\n              addEthereumChainParameter?.nativeCurrency ?? chain.nativeCurrency,\n            rpcUrls,\n          } satisfies AddEthereumChainParameter\n\n          await provider.request({\n            method: 'wallet_addEthereumChain',\n            params: [addEthereumChain],\n          })\n\n          const requestedChains = await this.getRequestedChainsIds()\n          this.setRequestedChainsIds([...requestedChains, chainId])\n          return chain\n        } catch (error) {\n          throw new UserRejectedRequestError(error as Error)\n        }\n      }\n    },\n    onAccountsChanged(accounts) {\n      if (accounts.length === 0) this.onDisconnect()\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onConnect(connectInfo) {\n      const chainId = Number(connectInfo.chainId)\n      const accounts = await this.getAccounts()\n      config.emitter.emit('connect', { accounts, chainId })\n    },\n    async onDisconnect(_error) {\n      this.setRequestedChainsIds([])\n      config.emitter.emit('disconnect')\n\n      const provider = await this.getProvider()\n      if (accountsChanged) {\n        provider.removeListener('accountsChanged', accountsChanged)\n        accountsChanged = undefined\n      }\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n      if (sessionDelete) {\n        provider.removeListener('session_delete', sessionDelete)\n        sessionDelete = undefined\n      }\n      if (!connect) {\n        connect = this.onConnect.bind(this)\n        provider.on('connect', connect)\n      }\n    },\n    onDisplayUri(uri) {\n      config.emitter.emit('message', { type: 'display_uri', data: uri })\n    },\n    onSessionDelete() {\n      this.onDisconnect()\n    },\n    getNamespaceChainsIds() {\n      if (!provider_) return []\n      const chainIds = provider_.session?.namespaces[NAMESPACE]?.accounts?.map(\n        (account) => Number.parseInt(account.split(':')[1] || ''),\n      )\n      return chainIds ?? []\n    },\n    async getRequestedChainsIds() {\n      return (\n        (await config.storage?.getItem(this.requestedChainsStorageKey)) ?? []\n      )\n    },\n    /**\n     * Checks if the target chains match the chains that were\n     * initially requested by the connector for the WalletConnect session.\n     * If there is a mismatch, this means that the chains on the connector\n     * are considered stale, and need to be revalidated at a later point (via\n     * connection).\n     *\n     * There may be a scenario where a dapp adds a chain to the\n     * connector later on, however, this chain will not have been approved or rejected\n     * by the wallet. In this case, the chain is considered stale.\n     */\n    async isChainsStale() {\n      if (!isNewChainsStale) return false\n\n      const connectorChains = config.chains.map((x) => x.id)\n      const namespaceChains = this.getNamespaceChainsIds()\n      if (\n        namespaceChains.length &&\n        !namespaceChains.some((id) => connectorChains.includes(id))\n      )\n        return false\n\n      const requestedChains = await this.getRequestedChainsIds()\n      return !connectorChains.every((id) => requestedChains.includes(id))\n    },\n    async setRequestedChainsIds(chains) {\n      await config.storage?.setItem(this.requestedChainsStorageKey, chains)\n    },\n    get requestedChainsStorageKey() {\n      return `${this.id}.requestedChains` as Properties['requestedChainsStorageKey']\n    },\n  }))\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBM,SAAU,YAAY,aAAoC,CAAA,GAAE;AAYhE,MAAI;AAEJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,SAAO,gBAAsC,CAAC,YAAY;IACxD,IAAI;IACJ,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM,QAAQ,EAAE,QAAO,IAAK,CAAA,GAAE;AAC5B,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,cAAM,YACH,MAAM,SAAS,QAAQ;UACtB,QAAQ;UACR,QAAQ,CAAA;SACT,GACD,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;AAE1B,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAc,UAAU;QACtC;AAGA,YAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,YAAI,WAAW,mBAAmB,SAAS;AACzC,gBAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE,EAAE,MAAM,CAAC,UAAS;AACjE,gBAAI,MAAM,SAAS,yBAAyB;AAAM,oBAAM;AACxD,mBAAO,EAAE,IAAI,eAAc;UAC7B,CAAC;AACD,2BAAiB,OAAO,MAAM;QAChC;AAEA,eAAO,EAAE,UAAU,SAAS,eAAc;MAC5C,SAAS,OAAO;AACd,YACE,uFAAuF,KACpF,MAAgB,OAAO;AAG1B,gBAAM,IAAI,yBAAyB,KAAc;AACnD,cAAM;MACR;IACF;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,UAAI,iBAAiB;AACnB,iBAAS,eAAe,mBAAmB,eAAe;AAC1D,0BAAkB;MACpB;AACA,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,iBAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;AAEA,eAAS,WAAU;IACrB;IACA,MAAM,cAAW;AACf,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,cACG,MAAM,SAAS,QAAQ;QACtB,QAAQ;OACT,GACD,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC5B;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,UAAW,MAAM,SAAS,QAAQ;QACtC,QAAQ;OACT;AACD,aAAO,OAAO,OAAO;IACvB;IACA,MAAM,cAAW;AACf,UAAI,CAAC,gBAAgB;AACnB,cAAM,cAAc,MAAK;AACvB,cAAI,OAAO,WAAW,eAAe;AACnC,mBAAO,EAAE,SAAS,WAAW,WAAU;AACzC,iBAAO;YACL,GAAG,WAAW;YACd,SAAS,WAAW,YAAY,WAAW;;QAE/C,GAAE;AAEF,cAAM,EAAE,qBAAoB,IAAK,MAAM,OAAO,oBAAmB;AACjE,cAAM,MAAM,qBAAqB;UAC/B,GAAG;UACH,aAAa,OAAO,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE;UAC1C;SACD;AAED,yBAAiB,IAAI,YAAW;MAClC;AAEA,aAAO;IACT;IACA,MAAM,eAAY;AAChB,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,eAAO,CAAC,CAAC,SAAS;MACpB,QAAQ;AACN,eAAO;MACT;IACF;IACA,MAAM,YAAY,EAAE,2BAA2B,QAAO,GAAE;AACtD,YAAM,QAAQ,OAAO,OAAO,KAAK,CAACA,WAAUA,OAAM,OAAO,OAAO;AAChE,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,UAAI;AACF,cAAM,SAAS,QAAQ;UACrB,QAAQ;UACR,QAAQ,CAAC,EAAE,SAAS,YAAY,MAAM,EAAE,EAAC,CAAE;SAC5C;AACD,eAAO;MACT,SAAS,OAAO;AAEd,YAAK,MAA2B,SAAS,MAAM;AAC7C,cAAI;AACF,gBAAI;AACJ,gBAAI,2BAA2B;AAC7B,kCAAoB,0BAA0B;;AAE9C,kCAAoB,MAAM,gBAAgB,QAAQ,MAC9C,CAAC,MAAM,gBAAgB,QAAQ,GAAG,IAClC,CAAA;AAEN,gBAAI;AACJ,gBAAI,2BAA2B,SAAS;AACtC,wBAAU,0BAA0B;;AACjC,wBAAU,CAAC,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,EAAE;AAEpD,kBAAM,mBAAmB;cACvB;cACA,SAAS,YAAY,OAAO;cAC5B,WAAW,2BAA2B,aAAa,MAAM;cACzD,UAAU,2BAA2B;cACrC,gBACE,2BAA2B,kBAC3B,MAAM;cACR;;AAGF,kBAAM,SAAS,QAAQ;cACrB,QAAQ;cACR,QAAQ,CAAC,gBAAgB;aAC1B;AAED,mBAAO;UACT,SAASC,QAAO;AACd,kBAAM,IAAI,yBAAyBA,MAAc;UACnD;QACF;AAEA,cAAM,IAAI,iBAAiB,KAAc;MAC3C;IACF;IACA,kBAAkB,UAAQ;AACxB,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;;AAE1C,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,aAAa,QAAM;AACvB,aAAO,QAAQ,KAAK,YAAY;AAEhC,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,iBAAiB;AACnB,iBAAS,eAAe,mBAAmB,eAAe;AAC1D,0BAAkB;MACpB;AACA,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,iBAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;IACF;IACA;AACJ;;;AC3LA,eAAe,OAAO;AAChB,SAAU,eACd,aAAgD,CAAA,GAAS;AAIzD,MAAI,WAAW,YAAY,OAAO,WAAW;AAC3C,WAAO,SAAS,UAAgC;AAClD,SAAO,SAAS,UAAgC;AAClD;AAiBA,SAAS,SAAS,YAA8B;AAgB9C,MAAI;AAEJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,SAAO,gBAAsC,CAAC,YAAY;IACxD,IAAI;IACJ,MAAM;IACN,MAAM;IACN,MAAM,eAAe;IACrB,MAAM,QAAQ,EAAE,SAAS,GAAG,KAAI,IAAK,CAAA,GAAE;AACrC,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,cAAM,YACH,MAAM,SAAS,QAAQ;UACtB,QAAQ;UACR,QACE,uBAAuB,QAAQ,KAAK,oBAChC,CAAC,EAAE,YAAY,UAAS,CAAE,IAC1B,CAAA;SACP,GACD,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;AAE1B,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAc,UAAU;QACtC;AAGA,YAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,YAAI,WAAW,mBAAmB,SAAS;AACzC,gBAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE,EAAE,MAAM,CAAC,UAAS;AACjE,gBAAI,MAAM,SAAS,yBAAyB;AAAM,oBAAM;AACxD,mBAAO,EAAE,IAAI,eAAc;UAC7B,CAAC;AACD,2BAAiB,OAAO,MAAM;QAChC;AAEA,eAAO,EAAE,UAAU,SAAS,eAAc;MAC5C,SAAS,OAAO;AACd,YACE,uFAAuF,KACpF,MAAgB,OAAO;AAG1B,gBAAM,IAAI,yBAAyB,KAAc;AACnD,cAAM;MACR;IACF;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,UAAI,iBAAiB;AACnB,iBAAS,eAAe,mBAAmB,eAAe;AAC1D,0BAAkB;MACpB;AACA,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,iBAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;AAEA,eAAS,WAAU;AACnB,eAAS,QAAO;IAClB;IACA,MAAM,cAAW;AACf,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,cACG,MAAM,SAAS,QAAQ;QACtB,QAAQ;OACT,GACD,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC5B;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,UAAW,MAAM,SAAS,QAAQ;QACtC,QAAQ;OACT;AACD,aAAO,OAAO,OAAO;IACvB;IACA,MAAM,cAAW;AACf,UAAI,CAAC,gBAAgB;AACnB,cAAM,cAAc,MAAK;AACvB,cAAI,OAAO,WAAW,eAAe;AACnC,mBAAO,EAAE,SAAS,WAAW,WAAU;AACzC,iBAAO;YACL,GAAG,WAAW;YACd,SAAS,WAAW,YAAY,WAAW;;QAE/C,GAAE;AAEF,cAAM,EAAE,wBAAuB,IAAK,MAAM,OAAO,oBAAsB;AACvE,cAAM,MAAM,wBAAwB;UAClC,GAAG;UACH,aAAa,OAAO,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE;UAC1C;SACD;AAED,yBAAiB,IAAI,YAAW;MAClC;AAEA,aAAO;IACT;IACA,MAAM,eAAY;AAChB,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,eAAO,CAAC,CAAC,SAAS;MACpB,QAAQ;AACN,eAAO;MACT;IACF;IACA,MAAM,YAAY,EAAE,2BAA2B,QAAO,GAAE;AACtD,YAAM,QAAQ,OAAO,OAAO,KAAK,CAACC,WAAUA,OAAM,OAAO,OAAO;AAChE,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,UAAI;AACF,cAAM,SAAS,QAAQ;UACrB,QAAQ;UACR,QAAQ,CAAC,EAAE,SAAS,YAAY,MAAM,EAAE,EAAC,CAAE;SAC5C;AACD,eAAO;MACT,SAAS,OAAO;AAEd,YAAK,MAA2B,SAAS,MAAM;AAC7C,cAAI;AACF,gBAAI;AACJ,gBAAI,2BAA2B;AAC7B,kCAAoB,0BAA0B;;AAE9C,kCAAoB,MAAM,gBAAgB,QAAQ,MAC9C,CAAC,MAAM,gBAAgB,QAAQ,GAAG,IAClC,CAAA;AAEN,gBAAI;AACJ,gBAAI,2BAA2B,SAAS;AACtC,wBAAU,0BAA0B;;AACjC,wBAAU,CAAC,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,EAAE;AAEpD,kBAAM,mBAAmB;cACvB;cACA,SAAS,YAAY,OAAO;cAC5B,WAAW,2BAA2B,aAAa,MAAM;cACzD,UAAU,2BAA2B;cACrC,gBACE,2BAA2B,kBAC3B,MAAM;cACR;;AAGF,kBAAM,SAAS,QAAQ;cACrB,QAAQ;cACR,QAAQ,CAAC,gBAAgB;aAC1B;AAED,mBAAO;UACT,SAASC,QAAO;AACd,kBAAM,IAAI,yBAAyBA,MAAc;UACnD;QACF;AAEA,cAAM,IAAI,iBAAiB,KAAc;MAC3C;IACF;IACA,kBAAkB,UAAQ;AACxB,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;;AAE1C,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,aAAa,QAAM;AACvB,aAAO,QAAQ,KAAK,YAAY;AAEhC,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,iBAAiB;AACnB,iBAAS,eAAe,mBAAmB,eAAe;AAC1D,0BAAkB;MACpB;AACA,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,iBAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;IACF;IACA;AACJ;AAyBA,SAAS,SAAS,YAA8B;AAC9C,QAAM,qBAAqB;AAI3B,MAAI;AACJ,MAAI;AAEJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,SAAO,gBAA0B,CAAC,YAAY;IAC5C,IAAI;IACJ,MAAM;IACN,MAAM;IACN,MAAM,eAAe;IACrB,MAAM,QAAQ,EAAE,QAAO,IAAK,CAAA,GAAE;AAC5B,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,cAAM,YACH,MAAM,SAAS,QAAQ;UACtB,QAAQ;SACT,GACD,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;AAE1B,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAc,UAAU;QACtC;AAGA,YAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,YAAI,WAAW,mBAAmB,SAAS;AACzC,gBAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE,EAAE,MAAM,CAAC,UAAS;AACjE,gBAAI,MAAM,SAAS,yBAAyB;AAAM,oBAAM;AACxD,mBAAO,EAAE,IAAI,eAAc;UAC7B,CAAC;AACD,2BAAiB,OAAO,MAAM;QAChC;AAEA,eAAO,EAAE,UAAU,SAAS,eAAc;MAC5C,SAAS,OAAO;AACd,YACE,sEAAsE,KACnE,MAAgB,OAAO;AAG1B,gBAAM,IAAI,yBAAyB,KAAc;AACnD,cAAM;MACR;IACF;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,UAAI,iBAAiB;AACnB,iBAAS,eAAe,mBAAmB,eAAe;AAC1D,0BAAkB;MACpB;AACA,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,iBAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;AAEA,eAAS,WAAU;AACnB,eAAS,MAAK;IAChB;IACA,MAAM,cAAW;AACf,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,cACE,MAAM,SAAS,QAAkB;QAC/B,QAAQ;OACT,GACD,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC5B;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,UAAU,MAAM,SAAS,QAAa;QAC1C,QAAQ;OACT;AACD,aAAO,OAAO,OAAO;IACvB;IACA,MAAM,cAAW;AACf,UAAI,CAAC,gBAAgB;AAGnB,cAAM,oBAAoB,OAAO,YAAW;AAC1C,gBAAM,EAAE,SAAS,IAAG,IAAK,MAAM,OAAO,oBAAS;AAC/C,cAAI,OAAO,QAAQ,cAAc,OAAO,IAAI,YAAY;AACtD,mBAAO,IAAI;AACb,iBAAO;QACT,GAAE;AAEF,cAAM,IAAI,kBAAkB,EAAE,GAAG,YAAY,mBAAkB,CAAE;AAGjE,cAAM,yBACJ,IAGA,iBAAiB,WAAU;AAE7B,cAAM,QACJ,OAAO,OAAO,KAAK,CAACD,WAClB,WAAW,UACPA,OAAM,OAAO,WAAW,UACxBA,OAAM,OAAO,sBAAsB,KACpC,OAAO,OAAO,CAAC;AACtB,cAAM,UAAU,WAAW,WAAW,OAAO;AAC7C,cAAM,aACJ,WAAW,cAAc,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAExD,yBAAiB,IAAI,iBAAiB,YAAY,OAAO;MAC3D;AAEA,aAAO;IACT;IACA,MAAM,eAAY;AAChB,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,eAAO,CAAC,CAAC,SAAS;MACpB,QAAQ;AACN,eAAO;MACT;IACF;IACA,MAAM,YAAY,EAAE,2BAA2B,QAAO,GAAE;AACtD,YAAM,QAAQ,OAAO,OAAO,KAAK,CAACA,WAAUA,OAAM,OAAO,OAAO;AAChE,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,UAAI;AACF,cAAM,SAAS,QAAQ;UACrB,QAAQ;UACR,QAAQ,CAAC,EAAE,SAAS,YAAY,MAAM,EAAE,EAAC,CAAE;SAC5C;AACD,eAAO;MACT,SAAS,OAAO;AAEd,YAAK,MAA2B,SAAS,MAAM;AAC7C,cAAI;AACF,gBAAI;AACJ,gBAAI,2BAA2B;AAC7B,kCAAoB,0BAA0B;;AAE9C,kCAAoB,MAAM,gBAAgB,QAAQ,MAC9C,CAAC,MAAM,gBAAgB,QAAQ,GAAG,IAClC,CAAA;AAEN,gBAAI;AACJ,gBAAI,2BAA2B,SAAS;AACtC,wBAAU,0BAA0B;;AACjC,wBAAU,CAAC,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,EAAE;AAEpD,kBAAM,mBAAmB;cACvB;cACA,SAAS,YAAY,OAAO;cAC5B,WAAW,2BAA2B,aAAa,MAAM;cACzD,UAAU,2BAA2B;cACrC,gBACE,2BAA2B,kBAC3B,MAAM;cACR;;AAGF,kBAAM,SAAS,QAAQ;cACrB,QAAQ;cACR,QAAQ,CAAC,gBAAgB;aAC1B;AAED,mBAAO;UACT,SAASC,QAAO;AACd,kBAAM,IAAI,yBAAyBA,MAAc;UACnD;QACF;AAEA,cAAM,IAAI,iBAAiB,KAAc;MAC3C;IACF;IACA,kBAAkB,UAAQ;AACxB,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;;AAE1C,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,aAAa,QAAM;AACvB,aAAO,QAAQ,KAAK,YAAY;AAEhC,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,iBAAiB;AACnB,iBAAS,eAAe,mBAAmB,eAAe;AAC1D,0BAAkB;MACpB;AACA,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,iBAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;IACF;IACA;AACJ;;;ACtdA,SAAS,OAAO;AACV,SAAU,SAAS,aAAiC,CAAA,GAAE;AAQ1D,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,SAAO,gBAAsC,CAAC,YAAY;IACxD,IAAI;IACJ,MAAM;IACN,MAAM,CAAC,eAAe,oBAAoB;IAC1C,MAAM,SAAS;IACf,MAAM,QAAK;AACT,YAAMC,YAAW,MAAM,KAAK,YAAW;AACvC,UAAIA,WAAU,IAAI;AAChB,YAAI,CAAC,SAAS;AACZ,oBAAU,KAAK,UAAU,KAAK,IAAI;AAClC,UAAAA,UAAS,GAAG,WAAW,OAAmB;QAC5C;AAIA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,UAAAA,UAAS,GAAG,mBAAmB,eAA2B;QAC5D;MACF;IACF;IACA,MAAM,QAAQ,EAAE,SAAS,eAAc,IAAK,CAAA,GAAE;AAC5C,YAAMA,YAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC,YAAY;AACf,qBAAa,KAAK;AAClB,QAAAA,UAAS,GAAG,eAAe,UAAsB;MACnD;AAEA,UAAI,WAA+B,CAAA;AACnC,UAAI;AAAgB,mBAAW,MAAM,KAAK,YAAW,EAAG,MAAM,MAAM,CAAA,CAAE;AAEtE,UAAI;AACF,YAAI;AACJ,YAAI;AACJ,YAAI,CAAC,UAAU,QAAQ;AACrB,cAAI,WAAW,kBAAkB,WAAW,aAAa;AACvD,gBAAI,WAAW;AACb,6BAAe,MAAM,IAAI,eAAe;gBACtC,KAAK,WAAW;eACjB;qBACM,WAAW;AAClB,oCAAsB,MAAM,IAAI,YAAY;gBAC1C,QAAQ,WAAW,YAAY;gBAC/B,QAAQ,WAAW,YAAY;eAChC;AAEH,uBAAW,MAAM,KAAK,YAAW;UACnC,OAAO;AACL,kBAAM,oBAAqB,MAAM,IAAI,QAAO;AAC5C,uBAAW,kBAAkB,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;UACvD;QACF;AAEA,YAAI,iBAAkB,MAAM,KAAK,WAAU;AAC3C,YAAI,WAAW,mBAAmB,SAAS;AACzC,gBAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE,EAAE,MAAM,CAAC,UAAS;AACjE,gBAAI,MAAM,SAAS,yBAAyB;AAAM,oBAAM;AACxD,mBAAO,EAAE,IAAI,eAAc;UAC7B,CAAC;AACD,2BAAiB,OAAO,MAAM;QAChC;AAEA,YAAI,YAAY;AACd,UAAAA,UAAS,eAAe,eAAe,UAAU;AACjD,uBAAa;QACf;AAEA,YAAI;AACF,UAAAA,UAAS,KAAK,kBAAkB;YAC9B;YACA,SAAS;YACT;WACD;iBACM;AACP,UAAAA,UAAS,KAAK,eAAe;YAC3B;YACA,SAAS;YACT;WACD;AAIH,YAAI,SAAS;AACX,UAAAA,UAAS,eAAe,WAAW,OAAO;AAC1C,oBAAU;QACZ;AACA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,UAAAA,UAAS,GAAG,mBAAmB,eAA2B;QAC5D;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,UAAAA,UAAS,GAAG,gBAAgB,YAAwB;QACtD;AACA,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,aAAa,KAAK,IAAI;AACxC,UAAAA,UAAS,GAAG,cAAc,UAAsB;QAClD;AAEA,eAAO,EAAE,UAAU,SAAS,eAAc;MAC5C,SAAS,KAAK;AACZ,cAAM,QAAQ;AACd,YAAI,MAAM,SAAS,yBAAyB;AAC1C,gBAAM,IAAI,yBAAyB,KAAK;AAC1C,YAAI,MAAM,SAAS,4BAA4B;AAC7C,gBAAM,IAAI,4BAA4B,KAAK;AAC7C,cAAM;MACR;IACF;IACA,MAAM,aAAU;AACd,YAAMA,YAAW,MAAM,KAAK,YAAW;AAGvC,UAAI,cAAc;AAChB,QAAAA,UAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,QAAAA,UAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;AACA,UAAI,CAAC,SAAS;AACZ,kBAAU,KAAK,UAAU,KAAK,IAAI;AAClC,QAAAA,UAAS,GAAG,WAAW,OAAmB;MAC5C;AAEA,YAAM,IAAI,UAAS;IACrB;IACA,MAAM,cAAW;AACf,YAAMA,YAAW,MAAM,KAAK,YAAW;AACvC,YAAM,WAAY,MAAMA,UAAS,QAAQ;QACvC,QAAQ;OACT;AACD,aAAO,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC1C;IACA,MAAM,aAAU;AACd,YAAMA,YAAW,MAAM,KAAK,YAAW;AACvC,YAAM,UACJA,UAAS,WAAU,KAClB,MAAMA,WAAU,QAAQ,EAAE,QAAQ,cAAa,CAAE;AACpD,aAAO,OAAO,OAAO;IACvB;IACA,MAAM,cAAW;AACf,qBAAe,eAAY;AAGzB,cAAM,cAAc,OAAO,YAAW;AACpC,gBAAM,EAAE,SAAS,IAAG,IAAK,MAAM,OAAO,4BAAe;AACrD,cAAI,OAAO,QAAQ,cAAc,OAAO,IAAI,YAAY;AACtD,mBAAO,IAAI;AACb,iBAAO;QACT,GAAE;AAEF,cAAM,iBAA+B,CAAA;AACrC,mBAAW,SAAS,OAAO;AACzB,yBAAe,YAAY,MAAM,EAAE,CAAC,IAAI,eAAe;YACrD;YACA,YAAY,OAAO;WACpB,IAAI,CAAC;AAER,cAAM,IAAI,YAAY;UACpB,SAAS;UACT,qBAAqB;UACrB,qBAAqB;UACrB,gBAAgB;;UAEhB,GAAI;UACJ;UACA,cAAc;YACZ,GAAG,WAAW;;YAEd,MAAM,WAAW,cAAc,OAC3B,WAAW,cAAc,OACzB;YACJ,KAAK,WAAW,cAAc,MAC1B,WAAW,cAAc,MACzB,OAAO,WAAW,cAChB,OAAO,SAAS,SAChB;;UAER,aAAa,WAAW,eAAe;SACxC;AACD,cAAM,SAAS,MAAM,IAAI,KAAI;AAI7B,cAAMA,aAAY,MAAK;AACrB,cAAI,QAAQ;AAAgB,mBAAO,OAAO;AAC1C,iBAAO,IAAI,YAAW;QACxB,GAAE;AACF,YAAI,CAACA;AAAU,gBAAM,IAAI,sBAAqB;AAC9C,eAAOA;MACT;AAEA,UAAI,CAAC,UAAU;AACb,YAAI,CAAC;AAAiB,4BAAkB,aAAY;AACpD,mBAAW,MAAM;MACnB;AACA,aAAO;IACT;IACA,MAAM,eAAY;AAChB,UAAI;AAGF,cAAM,UAAU;AAChB,cAAM,WAAW,MAAM,UACrB,MAAM,YAAY,MAAM,KAAK,YAAW,GAAI,EAAE,QAAO,CAAE,GACvD;UACE,OAAO,UAAU;UACjB,YAAY;SACb;AAEH,eAAO,CAAC,CAAC,SAAS;MACpB,QAAQ;AACN,eAAO;MACT;IACF;IACA,MAAM,YAAY,EAAE,2BAA2B,QAAO,GAAE;AACtD,YAAMA,YAAW,MAAM,KAAK,YAAW;AAEvC,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,UAAI;AACF,cAAMA,UAAS,QAAQ;UACrB,QAAQ;UACR,QAAQ,CAAC,EAAE,SAAS,YAAY,OAAO,EAAC,CAAE;SAC3C;AAOD,cAAM,qBAAoB;AAC1B,cAAM,0BAA0B,OAAO;AAEvC,eAAO;MACT,SAAS,KAAK;AACZ,cAAM,QAAQ;AAEd,YAAI,MAAM,SAAS,yBAAyB;AAC1C,gBAAM,IAAI,yBAAyB,KAAK;AAG1C,YACE,MAAM,SAAS;;QAGd,OACG,MAAM,eAAe,SAAS,MAClC;AACA,cAAI;AACF,kBAAMA,UAAS,QAAQ;cACrB,QAAQ;cACR,QAAQ;gBACN;kBACE,oBAAoB,MAAK;AACvB,0BAAM,EAAE,SAAS,eAAe,GAAG,eAAc,IAC/C,MAAM,kBAAkB,CAAA;AAC1B,wBAAI,2BAA2B;AAC7B,6BAAO,0BAA0B;AACnC,wBAAI;AACF,6BAAO;wBACL,cAAc;wBACd,GAAG,OAAO,OAAO,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG;;AAErD;kBACF,GAAE;kBACF,SAAS,YAAY,OAAO;kBAC5B,WAAW,2BAA2B,aAAa,MAAM;kBACzD,UAAU,2BAA2B;kBACrC,gBACE,2BAA2B,kBAC3B,MAAM;kBACR,UAAU,MAAK;AACb,wBAAI,2BAA2B,SAAS;AACtC,6BAAO,0BAA0B;AACnC,2BAAO,CAAC,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,EAAE;kBAC9C,GAAE;;;aAGP;AAED,kBAAM,qBAAoB;AAC1B,kBAAM,0BAA0B,OAAO;AAEvC,mBAAO;UACT,SAASC,MAAK;AACZ,kBAAMC,SAAQD;AACd,gBAAIC,OAAM,SAAS,yBAAyB;AAC1C,oBAAM,IAAI,yBAAyBA,MAAK;AAC1C,kBAAM,IAAI,iBAAiBA,MAAK;UAClC;QACF;AAEA,cAAM,IAAI,iBAAiB,KAAK;MAClC;AAEA,qBAAe,uBAAoB;AAGjC,cAAM,UACJ,YAAW;AACT,gBAAM,QAAQ;;YAEX,MAAMF,UAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE;UAAS;AAG5D,cAAI,UAAU;AACZ,kBAAM,IAAI,MAAM,4CAA4C;AAC9D,iBAAO;QACT,GACA;UACE,OAAO;UACP,YAAY;;SACb;MAEL;AAEA,qBAAe,0BAA0BG,UAAe;AACtD,cAAM,IAAI,QAAc,CAAC,YAAW;AAClC,gBAAM,WAAY,CAAC,SAAQ;AACzB,gBAAI,aAAa,QAAQ,KAAK,YAAYA,UAAS;AACjD,qBAAO,QAAQ,IAAI,UAAU,QAAQ;AACrC,sBAAO;YACT;UACF;AACA,iBAAO,QAAQ,GAAG,UAAU,QAAQ;AACpC,iBAAO,QAAQ,KAAK,UAAU,EAAE,SAAAA,SAAO,CAAE;QAC3C,CAAC;MACH;IACF;IACA,MAAM,kBAAkB,UAAQ;AAE9B,UAAI,SAAS,WAAW,GAAG;AAEzB,YAAI,IAAI,kBAAiB;AAAI,eAAK,aAAY;;AAEzC;MACP,WAES,OAAO,QAAQ,cAAc,SAAS,GAAG;AAChD,cAAM,WAAW,MAAM,KAAK,WAAU,GAAI,SAAQ;AAClD,aAAK,UAAU,EAAE,QAAO,CAAE;MAC5B;AAGE,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,UAAU,aAAW;AACzB,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,SAAS,WAAW;AAAG;AAE3B,YAAM,UAAU,OAAO,YAAY,OAAO;AAC1C,aAAO,QAAQ,KAAK,WAAW,EAAE,UAAU,QAAO,CAAE;AAEpD,YAAMH,YAAW,MAAM,KAAK,YAAW;AACvC,UAAI,SAAS;AACX,QAAAA,UAAS,eAAe,WAAW,OAAO;AAC1C,kBAAU;MACZ;AACA,UAAI,CAAC,iBAAiB;AACpB,0BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,QAAAA,UAAS,GAAG,mBAAmB,eAA2B;MAC5D;AACA,UAAI,CAAC,cAAc;AACjB,uBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,QAAAA,UAAS,GAAG,gBAAgB,YAAwB;MACtD;AACA,UAAI,CAAC,YAAY;AACf,qBAAa,KAAK,aAAa,KAAK,IAAI;AACxC,QAAAA,UAAS,GAAG,cAAc,UAAsB;MAClD;IACF;IACA,MAAM,aAAa,OAAK;AACtB,YAAMA,YAAW,MAAM,KAAK,YAAW;AAIvC,UAAI,SAAU,MAAyB,SAAS,MAAM;AACpD,YAAIA,aAAY,CAAC,EAAE,MAAM,KAAK,YAAW,GAAI;AAAQ;MACvD;AAEA,aAAO,QAAQ,KAAK,YAAY;AAGhC,UAAI,cAAc;AAChB,QAAAA,UAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,QAAAA,UAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;AACA,UAAI,CAAC,SAAS;AACZ,kBAAU,KAAK,UAAU,KAAK,IAAI;AAClC,QAAAA,UAAS,GAAG,WAAW,OAAmB;MAC5C;IACF;IACA,aAAa,KAAG;AACd,aAAO,QAAQ,KAAK,WAAW,EAAE,MAAM,eAAe,MAAM,IAAG,CAAE;IACnE;IACA;AACJ;;;ACzdA,KAAK,OAAO;AACN,SAAU,KAAK,aAA6B,CAAA,GAAE;AAClD,QAAM,EAAE,iBAAiB,MAAK,IAAK;AAMnC,MAAI;AAEJ,MAAI;AAEJ,SAAO,gBAAmD,CAAC,YAAY;IACrE,IAAI;IACJ,MAAM;IACN,MAAM,KAAK;IACX,MAAM,UAAO;AACX,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,UAAU,MAAM,KAAK,WAAU;AAErC,UAAI,CAAC,YAAY;AACf,qBAAa,KAAK,aAAa,KAAK,IAAI;AACxC,iBAAS,GAAG,cAAc,UAAU;MACtC;AAGA,UAAI;AAAgB,cAAM,OAAO,SAAS,WAAW,mBAAmB;AAExE,aAAO,EAAE,UAAU,QAAO;IAC5B;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,UAAI,YAAY;AACd,iBAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;AAGA,UAAI;AACF,cAAM,OAAO,SAAS,QAAQ,qBAAqB,IAAI;IAC3D;IACA,MAAM,cAAW;AACf,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAC9C,cAAQ,MAAM,SAAS,QAAQ,EAAE,QAAQ,eAAc,CAAE,GAAG,IAC1D,UAAU;IAEd;IACA,MAAM,cAAW;AAEf,YAAM,WACJ,OAAO,WAAW,eAAe,QAAQ,WAAW;AACtD,UAAI,CAAC;AAAU;AAEf,UAAI,CAAC,WAAW;AACd,cAAM,EAAE,SAAS,IAAG,IAAK,MAAM,OAAO,mBAA4B;AAClE,cAAM,MAAM,IAAI,IAAI,UAAU;AAI9B,cAAMI,QAAO,MAAM,YAAY,MAAM,IAAI,KAAK,QAAO,GAAI;UACvD,SAAS,WAAW,2BAA2B;SAChD;AACD,YAAI,CAACA;AAAM,gBAAM,IAAI,MAAM,iCAAiC;AAG5D,cAAM,kBAAkB,OAAO,YAAW;AACxC,gBAAM,WAAW,MAAM,OAAO,oBAAiC;AAC/D,cACE,OAAO,SAAS,oBAAoB,cACpC,OAAO,SAAS,QAAQ,oBAAoB;AAE5C,mBAAO,SAAS,QAAQ;AAC1B,iBAAO,SAAS;QAClB,GAAE;AACF,oBAAY,IAAI,gBAAgBA,OAAM,GAAG;MAC3C;AACA,aAAO;IACT;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAC9C,aAAO,OAAO,SAAS,OAAO;IAChC;IACA,MAAM,eAAY;AAChB,UAAI;AACF,cAAM,iBACJ;QAEC,MAAM,OAAO,SAAS,QAAQ,mBAAmB;AACpD,YAAI;AAAgB,iBAAO;AAE3B,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,eAAO,CAAC,CAAC,SAAS;MACpB,QAAQ;AACN,eAAO;MACT;IACF;IACA,oBAAiB;IAEjB;IACA,iBAAc;IAEd;IACA,eAAY;AACV,aAAO,QAAQ,KAAK,YAAY;IAClC;IACA;AACJ;;;AChJO,IAAM,UAAU;;;AC0EvB,cAAc,OAAO;AACf,SAAU,cAAc,YAAmC;AAC/D,QAAM,mBAAmB,WAAW,oBAAoB;AAyBxD,MAAI;AACJ,MAAI;AACJ,QAAM,YAAY;AAElB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,SAAO,gBAAmD,CAAC,YAAY;IACrE,IAAI;IACJ,MAAM;IACN,MAAM,cAAc;IACpB,MAAM,QAAK;AACT,YAAM,WAAW,MAAM,KAAK,YAAW,EAAG,MAAM,MAAM,IAAI;AAC1D,UAAI,CAAC;AAAU;AACf,UAAI,CAAC,SAAS;AACZ,kBAAU,KAAK,UAAU,KAAK,IAAI;AAClC,iBAAS,GAAG,WAAW,OAAO;MAChC;AACA,UAAI,CAAC,eAAe;AAClB,wBAAgB,KAAK,gBAAgB,KAAK,IAAI;AAC9C,iBAAS,GAAG,kBAAkB,aAAa;MAC7C;IACF;IACA,MAAM,QAAQ,EAAE,SAAS,GAAG,KAAI,IAAK,CAAA,GAAE;AACrC,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAI,CAAC;AAAU,gBAAM,IAAI,sBAAqB;AAC9C,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK;AAClB,mBAAS,GAAG,eAAe,UAAU;QACvC;AAEA,YAAI,gBAAgB;AACpB,YAAI,CAAC,eAAe;AAClB,gBAAM,QAAS,MAAM,OAAO,SAAS,QAAQ,OAAO,KAAM,CAAA;AAC1D,gBAAM,mBAAmB,OAAO,OAAO,KACrC,CAAC,MAAM,EAAE,OAAO,MAAM,OAAO;AAE/B,cAAI;AAAkB,4BAAgB,MAAM;;AACvC,4BAAgB,OAAO,OAAO,CAAC,GAAG;QACzC;AACA,YAAI,CAAC;AAAe,gBAAM,IAAI,MAAM,+BAA+B;AAEnE,cAAM,gBAAgB,MAAM,KAAK,cAAa;AAE9C,YAAI,SAAS,WAAW;AAAe,gBAAM,SAAS,WAAU;AAGhE,YAAI,CAAC,SAAS,WAAW,eAAe;AACtC,gBAAM,iBAAiB,OAAO,OAC3B,OAAO,CAAC,UAAU,MAAM,OAAO,aAAa,EAC5C,IAAI,CAAC,kBAAkB,cAAc,EAAE;AAC1C,gBAAM,SAAS,QAAQ;YACrB,gBAAgB,CAAC,eAAe,GAAG,cAAc;YACjD,GAAI,kBAAkB,OAClB,EAAE,cAAc,KAAK,aAAY,IACjC,CAAA;WACL;AAED,eAAK,sBAAsB,OAAO,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;QAC3D;AAGA,cAAM,YAAY,MAAM,SAAS,OAAM,GAAI,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;AACnE,cAAM,iBAAiB,MAAM,KAAK,WAAU;AAE5C,YAAI,YAAY;AACd,mBAAS,eAAe,eAAe,UAAU;AACjD,uBAAa;QACf;AACA,YAAI,SAAS;AACX,mBAAS,eAAe,WAAW,OAAO;AAC1C,oBAAU;QACZ;AACA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAAC,YAAY;AACf,uBAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAc,UAAU;QACtC;AACA,YAAI,CAAC,eAAe;AAClB,0BAAgB,KAAK,gBAAgB,KAAK,IAAI;AAC9C,mBAAS,GAAG,kBAAkB,aAAa;QAC7C;AAEA,eAAO,EAAE,UAAU,SAAS,eAAc;MAC5C,SAAS,OAAO;AACd,YACE,4CAA4C,KACzC,OAA4B,OAAO,GAEtC;AACA,gBAAM,IAAI,yBAAyB,KAAc;QACnD;AACA,cAAM;MACR;IACF;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI;AACF,cAAM,UAAU,WAAU;MAC5B,SAAS,OAAO;AACd,YAAI,CAAC,mBAAmB,KAAM,MAAgB,OAAO;AAAG,gBAAM;MAChE;AACE,YAAI,cAAc;AAChB,oBAAU,eAAe,gBAAgB,YAAY;AACrD,yBAAe;QACjB;AACA,YAAI,YAAY;AACd,oBAAU,eAAe,cAAc,UAAU;AACjD,uBAAa;QACf;AACA,YAAI,CAAC,SAAS;AACZ,oBAAU,KAAK,UAAU,KAAK,IAAI;AAClC,oBAAU,GAAG,WAAW,OAAO;QACjC;AACA,YAAI,iBAAiB;AACnB,oBAAU,eAAe,mBAAmB,eAAe;AAC3D,4BAAkB;QACpB;AACA,YAAI,eAAe;AACjB,oBAAU,eAAe,kBAAkB,aAAa;AACxD,0BAAgB;QAClB;AAEA,aAAK,sBAAsB,CAAA,CAAE;MAC/B;IACF;IACA,MAAM,cAAW;AACf,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,aAAO,SAAS,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IACnD;IACA,MAAM,YAAY,EAAE,QAAO,IAAK,CAAA,GAAE;AAChC,qBAAe,eAAY;AACzB,cAAM,iBAAiB,OAAO,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE;AACpD,YAAI,CAAC,eAAe;AAAQ;AAC5B,cAAM,EAAE,iBAAgB,IAAK,MAAM,OACjC,wBAAkC;AAEpC,eAAO,MAAM,iBAAiB,KAAK;UACjC,GAAG;UACH,qBAAqB;UACrB;UACA,WAAW,WAAW;UACtB,QAAQ,OAAO,YACb,OAAO,OAAO,IAAI,CAAC,UAAS;AAC1B,kBAAM,CAAC,GAAG,IAAI,eAAe;cAC3B;cACA,YAAY,OAAO;aACpB;AACD,mBAAO,CAAC,MAAM,IAAI,GAAG;UACvB,CAAC,CAAC;UAEJ,aAAa,WAAW,eAAe;SACxC;MACH;AAEA,UAAI,CAAC,WAAW;AACd,YAAI,CAAC;AAAiB,4BAAkB,aAAY;AACpD,oBAAY,MAAM;AAClB,mBAAW,OAAO,gBAAgB,OAAO,iBAAiB;MAC5D;AACA,UAAI;AAAS,cAAM,KAAK,cAAc,EAAE,QAAO,CAAE;AACjD,aAAO;IACT;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,aAAO,SAAS;IAClB;IACA,MAAM,eAAY;AAChB,UAAI;AACF,cAAM,CAAC,UAAU,QAAQ,IAAI,MAAM,QAAQ,IAAI;UAC7C,KAAK,YAAW;UAChB,KAAK,YAAW;SACjB;AAGD,YAAI,CAAC,SAAS;AAAQ,iBAAO;AAG7B,cAAM,gBAAgB,MAAM,KAAK,cAAa;AAC9C,YAAI,iBAAiB,SAAS,SAAS;AACrC,gBAAM,SAAS,WAAU,EAAG,MAAM,MAAK;UAAE,CAAC;AAC1C,iBAAO;QACT;AACA,eAAO;MACT,QAAQ;AACN,eAAO;MACT;IACF;IACA,MAAM,YAAY,EAAE,2BAA2B,QAAO,GAAE;AACtD,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,UAAI;AACF,cAAM,QAAQ,IAAI;UAChB,IAAI,QAAc,CAAC,YAAW;AAC5B,kBAAM,WAAW,CAAC,EAChB,SAAS,eAAc,MAGpB;AACH,kBAAI,mBAAmB,SAAS;AAC9B,uBAAO,QAAQ,IAAI,UAAU,QAAQ;AACrC,wBAAO;cACT;YACF;AACA,mBAAO,QAAQ,GAAG,UAAU,QAAQ;UACtC,CAAC;UACD,SAAS,QAAQ;YACf,QAAQ;YACR,QAAQ,CAAC,EAAE,SAAS,YAAY,OAAO,EAAC,CAAE;WAC3C;SACF;AAED,cAAM,kBAAkB,MAAM,KAAK,sBAAqB;AACxD,aAAK,sBAAsB,CAAC,GAAG,iBAAiB,OAAO,CAAC;AAExD,eAAO;MACT,SAAS,KAAK;AACZ,cAAM,QAAQ;AAEd,YAAI,mBAAmB,KAAK,MAAM,OAAO;AACvC,gBAAM,IAAI,yBAAyB,KAAK;AAG1C,YAAI;AACF,cAAI;AACJ,cAAI,2BAA2B;AAC7B,gCAAoB,0BAA0B;;AAE9C,gCAAoB,MAAM,gBAAgB,QAAQ,MAC9C,CAAC,MAAM,gBAAgB,QAAQ,GAAG,IAClC,CAAA;AAEN,cAAI;AACJ,cAAI,2BAA2B,SAAS;AACtC,sBAAU,0BAA0B;;AACjC,sBAAU,CAAC,GAAG,MAAM,QAAQ,QAAQ,IAAI;AAE7C,gBAAM,mBAAmB;YACvB;YACA,SAAS,YAAY,OAAO;YAC5B,WAAW,2BAA2B,aAAa,MAAM;YACzD,UAAU,2BAA2B;YACrC,gBACE,2BAA2B,kBAAkB,MAAM;YACrD;;AAGF,gBAAM,SAAS,QAAQ;YACrB,QAAQ;YACR,QAAQ,CAAC,gBAAgB;WAC1B;AAED,gBAAM,kBAAkB,MAAM,KAAK,sBAAqB;AACxD,eAAK,sBAAsB,CAAC,GAAG,iBAAiB,OAAO,CAAC;AACxD,iBAAO;QACT,SAASC,QAAO;AACd,gBAAM,IAAI,yBAAyBA,MAAc;QACnD;MACF;IACF;IACA,kBAAkB,UAAQ;AACxB,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;;AAE1C,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,UAAU,aAAW;AACzB,YAAM,UAAU,OAAO,YAAY,OAAO;AAC1C,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,aAAO,QAAQ,KAAK,WAAW,EAAE,UAAU,QAAO,CAAE;IACtD;IACA,MAAM,aAAa,QAAM;AACvB,WAAK,sBAAsB,CAAA,CAAE;AAC7B,aAAO,QAAQ,KAAK,YAAY;AAEhC,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,iBAAiB;AACnB,iBAAS,eAAe,mBAAmB,eAAe;AAC1D,0BAAkB;MACpB;AACA,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAI,YAAY;AACd,iBAAS,eAAe,cAAc,UAAU;AAChD,qBAAa;MACf;AACA,UAAI,eAAe;AACjB,iBAAS,eAAe,kBAAkB,aAAa;AACvD,wBAAgB;MAClB;AACA,UAAI,CAAC,SAAS;AACZ,kBAAU,KAAK,UAAU,KAAK,IAAI;AAClC,iBAAS,GAAG,WAAW,OAAO;MAChC;IACF;IACA,aAAa,KAAG;AACd,aAAO,QAAQ,KAAK,WAAW,EAAE,MAAM,eAAe,MAAM,IAAG,CAAE;IACnE;IACA,kBAAe;AACb,WAAK,aAAY;IACnB;IACA,wBAAqB;AACnB,UAAI,CAAC;AAAW,eAAO,CAAA;AACvB,YAAM,WAAW,UAAU,SAAS,WAAW,SAAS,GAAG,UAAU,IACnE,CAAC,YAAY,OAAO,SAAS,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AAE3D,aAAO,YAAY,CAAA;IACrB;IACA,MAAM,wBAAqB;AACzB,aACG,MAAM,OAAO,SAAS,QAAQ,KAAK,yBAAyB,KAAM,CAAA;IAEvE;;;;;;;;;;;;IAYA,MAAM,gBAAa;AACjB,UAAI,CAAC;AAAkB,eAAO;AAE9B,YAAM,kBAAkB,OAAO,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE;AACrD,YAAM,kBAAkB,KAAK,sBAAqB;AAClD,UACE,gBAAgB,UAChB,CAAC,gBAAgB,KAAK,CAAC,OAAO,gBAAgB,SAAS,EAAE,CAAC;AAE1D,eAAO;AAET,YAAM,kBAAkB,MAAM,KAAK,sBAAqB;AACxD,aAAO,CAAC,gBAAgB,MAAM,CAAC,OAAO,gBAAgB,SAAS,EAAE,CAAC;IACpE;IACA,MAAM,sBAAsB,QAAM;AAChC,YAAM,OAAO,SAAS,QAAQ,KAAK,2BAA2B,MAAM;IACtE;IACA,IAAI,4BAAyB;AAC3B,aAAO,GAAG,KAAK,EAAE;IACnB;IACA;AACJ;", "names": ["chain", "error", "chain", "error", "provider", "err", "error", "chainId", "safe", "error"]}