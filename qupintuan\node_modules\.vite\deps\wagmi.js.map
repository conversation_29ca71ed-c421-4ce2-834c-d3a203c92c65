{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../use-sync-external-store/shim/index.js", "../../use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../use-sync-external-store/shim/with-selector.js", "../../wagmi/src/context.ts", "../../wagmi/src/hydrate.ts", "../../wagmi/src/version.ts", "../../wagmi/src/utils/getVersion.ts", "../../wagmi/src/errors/base.ts", "../../wagmi/src/errors/context.ts", "../../wagmi/src/hooks/useConfig.ts", "../../@wagmi/core/src/actions/watchChains.ts", "../../wagmi/src/hooks/useSyncExternalStoreWithTracked.ts", "../../wagmi/src/hooks/useAccount.ts", "../../wagmi/src/hooks/useAccountEffect.ts", "../../@wagmi/core/src/query/utils.ts", "../../@wagmi/core/src/query/call.ts", "../../@wagmi/core/src/query/connect.ts", "../../@wagmi/core/src/query/deployContract.ts", "../../@wagmi/core/src/query/disconnect.ts", "../../@wagmi/core/src/query/estimateFeesPerGas.ts", "../../@wagmi/core/src/query/estimateGas.ts", "../../@wagmi/core/src/query/estimateMaxPriorityFeePerGas.ts", "../../@wagmi/core/src/query/getBalance.ts", "../../@wagmi/core/src/query/getBlock.ts", "../../@wagmi/core/src/query/getBlockNumber.ts", "../../@wagmi/core/src/query/getBlockTransactionCount.ts", "../../@wagmi/core/src/query/getBytecode.ts", "../../@wagmi/core/src/query/getCallsStatus.ts", "../../@wagmi/core/src/query/getCapabilities.ts", "../../@wagmi/core/src/query/getConnectorClient.ts", "../../@wagmi/core/src/query/getEnsAddress.ts", "../../@wagmi/core/src/query/getEnsAvatar.ts", "../../@wagmi/core/src/query/getEnsName.ts", "../../@wagmi/core/src/query/getEnsResolver.ts", "../../@wagmi/core/src/query/getEnsText.ts", "../../@wagmi/core/src/query/getFeeHistory.ts", "../../@wagmi/core/src/query/getGasPrice.ts", "../../@wagmi/core/src/query/getProof.ts", "../../@wagmi/core/src/query/getStorageAt.ts", "../../@wagmi/core/src/query/getToken.ts", "../../@wagmi/core/src/query/getTransaction.ts", "../../@wagmi/core/src/query/getTransactionConfirmations.ts", "../../@wagmi/core/src/query/getTransactionCount.ts", "../../@wagmi/core/src/query/getTransactionReceipt.ts", "../../@wagmi/core/src/query/getWalletClient.ts", "../../@wagmi/core/src/query/infiniteReadContracts.ts", "../../@wagmi/core/src/query/prepareTransactionRequest.ts", "../../@wagmi/core/src/query/readContract.ts", "../../@wagmi/core/src/query/readContracts.ts", "../../@wagmi/core/src/query/reconnect.ts", "../../@wagmi/core/src/query/sendCalls.ts", "../../@wagmi/core/src/query/sendTransaction.ts", "../../@wagmi/core/src/query/showCallsStatus.ts", "../../@wagmi/core/src/query/signMessage.ts", "../../@wagmi/core/src/query/signTypedData.ts", "../../@wagmi/core/src/query/simulateContract.ts", "../../@wagmi/core/src/query/switchAccount.ts", "../../@wagmi/core/src/query/switchChain.ts", "../../@wagmi/core/src/query/verifyMessage.ts", "../../@wagmi/core/src/query/verifyTypedData.ts", "../../@wagmi/core/src/query/waitForCallsStatus.ts", "../../@wagmi/core/src/query/waitForTransactionReceipt.ts", "../../@wagmi/core/src/query/watchAsset.ts", "../../@wagmi/core/src/query/writeContract.ts", "../../wagmi/src/utils/query.ts", "../../wagmi/src/hooks/useChainId.ts", "../../wagmi/src/hooks/useBalance.ts", "../../wagmi/src/hooks/useWatchBlocks.ts", "../../wagmi/src/hooks/useBlock.ts", "../../wagmi/src/hooks/useWatchBlockNumber.ts", "../../wagmi/src/hooks/useBlockNumber.ts", "../../wagmi/src/hooks/useBlockTransactionCount.ts", "../../wagmi/src/hooks/useBytecode.ts", "../../wagmi/src/hooks/useCall.ts", "../../wagmi/src/hooks/useCallsStatus.ts", "../../wagmi/src/hooks/useCapabilities.ts", "../../wagmi/src/hooks/useChains.ts", "../../wagmi/src/hooks/useClient.ts", "../../wagmi/src/hooks/useConnect.ts", "../../wagmi/src/hooks/useConnectors.ts", "../../wagmi/src/hooks/useConnections.ts", "../../wagmi/src/hooks/useConnectorClient.ts", "../../wagmi/src/hooks/useDeployContract.ts", "../../wagmi/src/hooks/useDisconnect.ts", "../../wagmi/src/hooks/useEnsAddress.ts", "../../wagmi/src/hooks/useEnsAvatar.ts", "../../wagmi/src/hooks/useEnsName.ts", "../../wagmi/src/hooks/useEnsResolver.ts", "../../wagmi/src/hooks/useEnsText.ts", "../../wagmi/src/hooks/useEstimateFeesPerGas.ts", "../../wagmi/src/hooks/useEstimateGas.ts", "../../wagmi/src/hooks/useEstimateMaxPriorityFeePerGas.ts", "../../wagmi/src/hooks/useFeeHistory.ts", "../../wagmi/src/hooks/useGasPrice.ts", "../../wagmi/src/hooks/useInfiniteReadContracts.ts", "../../wagmi/src/hooks/usePrepareTransactionRequest.ts", "../../wagmi/src/hooks/useProof.ts", "../../wagmi/src/hooks/usePublicClient.ts", "../../wagmi/src/hooks/useReadContract.ts", "../../wagmi/src/hooks/useReadContracts.ts", "../../wagmi/src/hooks/useReconnect.ts", "../../wagmi/src/hooks/useSendCalls.ts", "../../wagmi/src/hooks/useSendTransaction.ts", "../../wagmi/src/hooks/useShowCallsStatus.ts", "../../wagmi/src/hooks/useSignMessage.ts", "../../wagmi/src/hooks/useSignTypedData.ts", "../../wagmi/src/hooks/useSimulateContract.ts", "../../wagmi/src/hooks/useStorageAt.ts", "../../wagmi/src/hooks/useSwitchAccount.ts", "../../wagmi/src/hooks/useSwitchChain.ts", "../../wagmi/src/hooks/useToken.ts", "../../wagmi/src/hooks/useTransaction.ts", "../../wagmi/src/hooks/useTransactionConfirmations.ts", "../../wagmi/src/hooks/useTransactionCount.ts", "../../wagmi/src/hooks/useTransactionReceipt.ts", "../../wagmi/src/hooks/useVerifyMessage.ts", "../../wagmi/src/hooks/useVerifyTypedData.ts", "../../wagmi/src/hooks/useWaitForCallsStatus.ts", "../../wagmi/src/hooks/useWaitForTransactionReceipt.ts", "../../wagmi/src/hooks/useWalletClient.ts", "../../wagmi/src/hooks/useWatchAsset.ts", "../../wagmi/src/hooks/useWatchContractEvent.ts", "../../wagmi/src/hooks/useWatchPendingTransactions.ts", "../../wagmi/src/hooks/useWriteContract.ts"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "'use client'\n\nimport type { ResolvedRegister, State } from '@wagmi/core'\nimport { createContext, createElement } from 'react'\nimport { Hydrate } from './hydrate.js'\n\nexport const WagmiContext = createContext<\n  ResolvedRegister['config'] | undefined\n>(undefined)\n\nexport type WagmiProviderProps = {\n  config: ResolvedRegister['config']\n  initialState?: State | undefined\n  reconnectOnMount?: boolean | undefined\n}\n\nexport function WagmiProvider(\n  parameters: React.PropsWithChildren<WagmiProviderProps>,\n) {\n  const { children, config } = parameters\n\n  const props = { value: config }\n  return createElement(\n    Hydrate,\n    parameters,\n    createElement(WagmiContext.Provider, props, children),\n  )\n}\n", "'use client'\n\nimport { hydrate, type ResolvedRegister, type State } from '@wagmi/core'\nimport { type ReactElement, useEffect, useRef } from 'react'\n\nexport type HydrateProps = {\n  config: ResolvedRegister['config']\n  initialState?: State | undefined\n  reconnectOnMount?: boolean | undefined\n}\n\nexport function Hydrate(parameters: React.PropsWithChildren<HydrateProps>) {\n  const { children, config, initialState, reconnectOnMount = true } = parameters\n\n  const { onMount } = hydrate(config, {\n    initialState,\n    reconnectOnMount,\n  })\n\n  // Hydrate for non-SSR\n  if (!config._internal.ssr) onMount()\n\n  // Hydrate for SSR\n  const active = useRef(true)\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `queryKey` not required\n  useEffect(() => {\n    if (!active.current) return\n    if (!config._internal.ssr) return\n    onMount()\n    return () => {\n      active.current = false\n    }\n  }, [])\n\n  return children as ReactElement\n}\n", "export const version = '2.16.1'\n", "import { version } from '../version.js'\n\nexport const getVersion = () => `wagmi@${version}`\n", "import { BaseError as CoreError } from '@wagmi/core'\n\nimport { getVersion } from '../utils/getVersion.js'\n\nexport type BaseErrorType = BaseError & { name: 'WagmiError' }\nexport class BaseError extends CoreError {\n  override name = 'WagmiError'\n  override get docsBaseUrl() {\n    return 'https://wagmi.sh/react'\n  }\n  override get version() {\n    return getVersion()\n  }\n}\n", "import { BaseError } from './base.js'\n\nexport type WagmiProviderNotFoundErrorType = WagmiProviderNotFoundError & {\n  name: 'WagmiProviderNotFoundError'\n}\nexport class WagmiProviderNotFoundError extends BaseError {\n  override name = 'WagmiProviderNotFoundError'\n  constructor() {\n    super('`useConfig` must be used within `WagmiProvider`.', {\n      docsPath: '/api/WagmiProvider',\n    })\n  }\n}\n", "'use client'\n\nimport type { Config, ResolvedRegister } from '@wagmi/core'\nimport { useContext } from 'react'\n\nimport { WagmiContext } from '../context.js'\nimport { WagmiProviderNotFoundError } from '../errors/context.js'\nimport type { ConfigParameter } from '../types/properties.js'\n\nexport type UseConfigParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseConfigReturnType<config extends Config = Config> = config\n\n/** https://wagmi.sh/react/api/hooks/useConfig */\nexport function useConfig<config extends Config = ResolvedRegister['config']>(\n  parameters: UseConfigParameters<config> = {},\n): UseConfigReturnType<config> {\n  // biome-ignore lint/correctness/useHookAtTopLevel: false alarm\n  const config = parameters.config ?? useContext(WagmiContext)\n  if (!config) throw new WagmiProviderNotFoundError()\n  return config as UseConfigReturnType<config>\n}\n", "import type { Config } from '../createConfig.js'\nimport type { GetChainsReturnType } from './getChains.js'\n\nexport type WatchChainsParameters<config extends Config = Config> = {\n  onChange(\n    chains: GetChainsReturnType<config>,\n    prevChains: GetChainsReturnType<config>,\n  ): void\n}\n\nexport type WatchChainsReturnType = () => void\n\n/**\n * @internal\n * We don't expose this because as far as consumers know, you can't chainge (lol) `config.chains` at runtime.\n * Setting `config.chains` via `config._internal.chains.setState(...)` is an extremely advanced use case that's not worth documenting or supporting in the public API at this time.\n */\nexport function watchChains<config extends Config>(\n  config: config,\n  parameters: WatchChainsParameters<config>,\n): WatchChainsReturnType {\n  const { onChange } = parameters\n  return config._internal.chains.subscribe((chains, prevChains) => {\n    onChange(\n      chains as unknown as GetChainsReturnType<config>,\n      prevChains as unknown as GetChainsReturnType<config>,\n    )\n  })\n}\n", "'use client'\n\nimport { deepEqual } from '@wagmi/core/internal'\nimport { useMemo, useRef } from 'react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\n\nconst isPlainObject = (obj: unknown) =>\n  typeof obj === 'object' && !Array.isArray(obj)\n\nexport function useSyncExternalStoreWithTracked<\n  snapshot extends selection,\n  selection = snapshot,\n>(\n  subscribe: (onStoreChange: () => void) => () => void,\n  getSnapshot: () => snapshot,\n  getServerSnapshot: undefined | null | (() => snapshot) = getSnapshot,\n  isEqual: (a: selection, b: selection) => boolean = deepEqual,\n) {\n  const trackedKeys = useRef<string[]>([])\n  const result = useSyncExternalStoreWithSelector(\n    subscribe,\n    getSnapshot,\n    getServerSnapshot,\n    (x) => x,\n    (a, b) => {\n      if (isPlainObject(a) && isPlainObject(b) && trackedKeys.current.length) {\n        for (const key of trackedKeys.current) {\n          const equal = isEqual(\n            (a as { [_a: string]: any })[key],\n            (b as { [_b: string]: any })[key],\n          )\n          if (!equal) return false\n        }\n        return true\n      }\n      return isEqual(a, b)\n    },\n  )\n\n  return useMemo(() => {\n    if (isPlainObject(result)) {\n      const trackedResult = { ...result }\n      let properties = {}\n      for (const [key, value] of Object.entries(\n        trackedResult as { [key: string]: any },\n      )) {\n        properties = {\n          ...properties,\n          [key]: {\n            configurable: false,\n            enumerable: true,\n            get: () => {\n              if (!trackedKeys.current.includes(key)) {\n                trackedKeys.current.push(key)\n              }\n              return value\n            },\n          },\n        }\n      }\n      Object.defineProperties(trackedResult, properties)\n      return trackedResult\n    }\n\n    return result\n  }, [result])\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetAccountReturnType,\n  getAccount,\n  type ResolvedRegister,\n  watchAccount,\n} from '@wagmi/core'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\nimport { useSyncExternalStoreWithTracked } from './useSyncExternalStoreWithTracked.js'\n\nexport type UseAccountParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseAccountReturnType<config extends Config = Config> =\n  GetAccountReturnType<config>\n\n/** https://wagmi.sh/react/api/hooks/useAccount */\nexport function useAccount<config extends Config = ResolvedRegister['config']>(\n  parameters: UseAccountParameters<config> = {},\n): UseAccountReturnType<config> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStoreWithTracked(\n    (onChange) => watchAccount(config, { onChange }),\n    () => getAccount(config),\n  )\n}\n", "'use client'\n\nimport { type GetAccountReturnType, watchAccount } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseAccountEffectParameters = Compute<\n  {\n    onConnect?(\n      data: Compute<\n        Pick<\n          Extract<GetAccountReturnType, { status: 'connected' }>,\n          'address' | 'addresses' | 'chain' | 'chainId' | 'connector'\n        > & {\n          isReconnected: boolean\n        }\n      >,\n    ): void\n    onDisconnect?(): void\n  } & ConfigParameter\n>\n\n/** https://wagmi.sh/react/api/hooks/useAccountEffect */\nexport function useAccountEffect(parameters: UseAccountEffectParameters = {}) {\n  const { onConnect, onDisconnect } = parameters\n\n  const config = useConfig(parameters)\n\n  useEffect(() => {\n    return watchAccount(config, {\n      onChange(data, prevData) {\n        if (\n          (prevData.status === 'reconnecting' ||\n            (prevData.status === 'connecting' &&\n              prevData.address === undefined)) &&\n          data.status === 'connected'\n        ) {\n          const { address, addresses, chain, chainId, connector } = data\n          const isReconnected =\n            prevData.status === 'reconnecting' ||\n            // if `previousAccount.status` is `undefined`, the connector connected immediately.\n            prevData.status === undefined\n          onConnect?.({\n            address,\n            addresses,\n            chain,\n            chainId,\n            connector,\n            isReconnected,\n          })\n        } else if (\n          prevData.status === 'connected' &&\n          data.status === 'disconnected'\n        )\n          onDisconnect?.()\n      },\n    })\n  }, [config, onConnect, onDisconnect])\n}\n", "import { type QueryKey, replaceEqualDeep } from '@tanstack/query-core'\n\nexport function structuralSharing<data>(\n  oldData: data | undefined,\n  newData: data,\n): data {\n  return replaceEqualDeep(oldData, newData)\n}\n\nexport function hashFn(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, value) => {\n    if (isPlainObject(value))\n      return Object.keys(value)\n        .sort()\n        .reduce((result, key) => {\n          result[key] = value[key]\n          return result\n        }, {} as any)\n    if (typeof value === 'bigint') return value.toString()\n    return value\n  })\n}\n\n// biome-ignore lint/complexity/noBannedTypes: using\nfunction isPlainObject(value: any): value is Object {\n  if (!hasObjectPrototype(value)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = value.constructor\n  if (typeof ctor === 'undefined') return true\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) return false\n\n  // If constructor does not have an Object-specific method\n  // biome-ignore lint/suspicious/noPrototypeBuiltins: using\n  if (!prot.hasOwnProperty('isPrototypeOf')) return false\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function filterQueryOptions<type extends Record<string, unknown>>(\n  options: type,\n): type {\n  // destructuring is super fast\n  // biome-ignore format: no formatting\n  const {\n    // import('@tanstack/query-core').QueryOptions\n    // biome-ignore lint/correctness/noUnusedVariables: tossing\n    _defaulted, behavior, gcTime, initialData, initialDataUpdatedAt, maxPages, meta, networkMode, queryFn, queryHash, queryKey, queryKeyHashFn, retry, retryDelay, structuralSharing,\n\n    // import('@tanstack/query-core').InfiniteQueryObserverOptions\n    // biome-ignore lint/correctness/noUnusedVariables: tossing\n    getPreviousPageParam, getNextPageParam, initialPageParam,\n\n    // import('@tanstack/react-query').UseQueryOptions\n    // biome-ignore lint/correctness/noUnusedVariables: tossing\n    _optimisticResults, enabled, notifyOnChangeProps, placeholderData, refetchInterval, refetchIntervalInBackground, refetchOnMount, refetchOnReconnect, refetchOnWindowFocus, retryOnMount, select, staleTime, suspense, throwOnError,\n\n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    // wagmi\n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    // biome-ignore lint/correctness/noUnusedVariables: tossing\n    config, connector, query,\n    ...rest\n  } = options\n\n  return rest as type\n}\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type CallErrorType,\n  type CallParameters,\n  type CallReturnType,\n  call,\n} from '../actions/call.js'\nimport type { Config } from '../createConfig.js'\nimport type { Scope<PERSON>eyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type CallOptions<config extends Config> = Compute<\n  ExactPartial<CallParameters<config>> & ScopeKeyParameter\n>\n\nexport function callQueryOptions<config extends Config>(\n  config: config,\n  options: CallOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const data = await call(config, {\n        ...parameters,\n      } as CallParameters)\n      return data ?? null\n    },\n    queryKey: callQueryKey(options),\n  } as const satisfies QueryOptions<\n    CallQueryFnData,\n    CallErrorType,\n    CallData,\n    CallQueryKey<config>\n  >\n}\n\nexport type CallQueryFnData = CallReturnType\n\nexport type CallData = CallQueryFnData\n\nexport function callQueryKey<config extends Config>(\n  options: CallOptions<config>,\n) {\n  return ['call', filterQueryOptions(options)] as const\n}\n\nexport type CallQueryKey<config extends Config> = ReturnType<\n  typeof callQueryKey<config>\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type ConnectErrorType,\n  type ConnectParameters,\n  type ConnectReturnType,\n  connect,\n} from '../actions/connect.js'\nimport type { CreateConnectorFn } from '../connectors/createConnector.js'\nimport type { Config, Connector } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function connectMutationOptions<config extends Config>(config: config) {\n  return {\n    mutationFn(variables) {\n      return connect(config, variables)\n    },\n    mutationKey: ['connect'],\n  } as const satisfies MutationOptions<\n    ConnectData<config>,\n    ConnectErrorType,\n    ConnectVariables<config, Connector | CreateConnectorFn>\n  >\n}\n\nexport type ConnectData<config extends Config> = ConnectReturnType<config>\n\nexport type ConnectVariables<\n  config extends Config,\n  connector extends Connector | CreateConnectorFn,\n> = ConnectParameters<config, connector>\n\nexport type ConnectMutate<config extends Config, context = unknown> = <\n  connector extends\n    | config['connectors'][number]\n    | Connector\n    | CreateConnectorFn,\n>(\n  variables: ConnectVariables<config, connector>,\n  options?:\n    | Compute<\n        MutateOptions<\n          ConnectData<config>,\n          ConnectErrorType,\n          Compute<ConnectVariables<config, connector>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type ConnectMutateAsync<config extends Config, context = unknown> = <\n  connector extends\n    | config['connectors'][number]\n    | Connector\n    | CreateConnectorFn,\n>(\n  variables: ConnectVariables<config, connector>,\n  options?:\n    | Compute<\n        MutateOptions<\n          ConnectData<config>,\n          ConnectErrorType,\n          Compute<ConnectVariables<config, connector>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<ConnectData<config>>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\nimport type { Abi, ContractConstructorArgs } from 'viem'\n\nimport {\n  type DeployContractErrorType,\n  type DeployContractParameters,\n  type DeployContractReturnType,\n  deployContract,\n} from '../actions/deployContract.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function deployContractMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return deployContract(config, variables)\n    },\n    mutationKey: ['deployContract'],\n  } as const satisfies MutationOptions<\n    DeployContractData,\n    DeployContractErrorType,\n    DeployContractVariables<Abi, config, config['chains'][number]['id']>\n  >\n}\n\nexport type DeployContractData = Compute<DeployContractReturnType>\n\nexport type DeployContractVariables<\n  abi extends Abi | readonly unknown[],\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  ///\n  allArgs = ContractConstructorArgs<abi>,\n> = DeployContractParameters<abi, config, chainId, allArgs>\n\nexport type DeployContractMutate<config extends Config, context = unknown> = <\n  abi extends Abi | readonly unknown[],\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: DeployContractVariables<abi, config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          DeployContractData,\n          DeployContractErrorType,\n          Compute<DeployContractVariables<abi, config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type DeployContractMutateAsync<\n  config extends Config,\n  context = unknown,\n> = <\n  abi extends Abi | readonly unknown[],\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: DeployContractVariables<abi, config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          DeployContractData,\n          DeployContractErrorType,\n          Compute<DeployContractVariables<abi, config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<DeployContractData>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type DisconnectErrorType,\n  type DisconnectParameters,\n  type DisconnectReturnType,\n  disconnect,\n} from '../actions/disconnect.js'\nimport type { Config } from '../createConfig.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function disconnectMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return disconnect(config, variables)\n    },\n    mutationKey: ['disconnect'],\n  } as const satisfies MutationOptions<\n    DisconnectData,\n    DisconnectErrorType,\n    DisconnectVariables\n  >\n}\n\nexport type DisconnectData = DisconnectReturnType\n\nexport type DisconnectVariables = DisconnectParameters | undefined\n\nexport type DisconnectMutate<context = unknown> = Mutate<\n  DisconnectData,\n  DisconnectErrorType,\n  DisconnectVariables,\n  context\n>\n\nexport type DisconnectMutateAsync<context = unknown> = MutateAsync<\n  DisconnectData,\n  DisconnectErrorType,\n  DisconnectVariables,\n  context\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { FeeValuesType } from 'viem'\n\nimport {\n  type EstimateFeesPerGasErrorType,\n  type EstimateFeesPerGasParameters,\n  type EstimateFeesPerGasReturnType,\n  estimateFeesPerGas,\n} from '../actions/estimateFeesPerGas.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type EstimateFeesPerGasOptions<\n  type extends FeeValuesType,\n  config extends Config,\n> = Compute<\n  ExactPartial<EstimateFeesPerGasParameters<type, config>> & ScopeKeyParameter\n>\n\nexport function estimateFeesPerGasQueryOptions<\n  config extends Config,\n  type extends FeeValuesType = 'eip1559',\n>(config: config, options: EstimateFeesPerGasOptions<type, config> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      return estimateFeesPerGas(config, parameters)\n    },\n    queryKey: estimateFeesPerGasQueryKey(options),\n  } as const satisfies QueryOptions<\n    EstimateFeesPerGasQueryFnData<type>,\n    EstimateFeesPerGasErrorType,\n    EstimateFeesPerGasData<type>,\n    EstimateFeesPerGasQueryKey<config, type>\n  >\n}\n\nexport type EstimateFeesPerGasQueryFnData<type extends FeeValuesType> =\n  EstimateFeesPerGasReturnType<type>\n\nexport type EstimateFeesPerGasData<type extends FeeValuesType> =\n  EstimateFeesPerGasQueryFnData<type>\n\nexport function estimateFeesPerGasQueryKey<\n  config extends Config,\n  type extends FeeValuesType = 'eip1559',\n>(options: EstimateFeesPerGasOptions<type, config> = {}) {\n  return ['estimateFeesPerGas', filterQueryOptions(options)] as const\n}\n\nexport type EstimateFeesPerGasQueryKey<\n  config extends Config,\n  type extends FeeValuesType,\n> = ReturnType<typeof estimateFeesPerGasQueryKey<config, type>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type EstimateGasErrorType,\n  type EstimateGasParameters,\n  type EstimateGasReturnType,\n  estimateGas,\n} from '../actions/estimateGas.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type EstimateGasOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = UnionExactPartial<EstimateGasParameters<config, chainId>> &\n  ScopeKeyParameter\n\nexport function estimateGasQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: EstimateGasOptions<config, chainId> = {} as any) {\n  return {\n    async queryFn({ queryKey }) {\n      const { connector } = options\n      const { account, scopeKey: _, ...parameters } = queryKey[1]\n      if (!account && !connector)\n        throw new Error('account or connector is required')\n      return estimateGas(config, { account, connector, ...(parameters as any) })\n    },\n    queryKey: estimateGasQueryKey(options),\n  } as const satisfies QueryOptions<\n    EstimateGasQueryFnData,\n    EstimateGasErrorType,\n    EstimateGasData,\n    EstimateGasQueryKey<config, chainId>\n  >\n}\n\nexport type EstimateGasQueryFnData = EstimateGasReturnType\n\nexport type EstimateGasData = EstimateGasQueryFnData\n\nexport function estimateGasQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n>(options: EstimateGasOptions<config, chainId> = {} as any) {\n  const { connector: _, ...rest } = options\n  return ['estimateGas', filterQueryOptions(rest)] as const\n}\n\nexport type EstimateGasQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = ReturnType<typeof estimateGasQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type EstimateMaxPriorityFeePerGasErrorType,\n  type EstimateMaxPriorityFeePerGasParameters,\n  type EstimateMaxPriorityFeePerGasReturnType,\n  estimateMaxPriorityFeePerGas,\n} from '../actions/estimateMaxPriorityFeePerGas.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type EstimateMaxPriorityFeePerGasOptions<config extends Config> =\n  Compute<\n    ExactPartial<EstimateMaxPriorityFeePerGasParameters<config>> &\n      ScopeKeyParameter\n  >\n\nexport function estimateMaxPriorityFeePerGasQueryOptions<config extends Config>(\n  config: config,\n  options: EstimateMaxPriorityFeePerGasOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      return estimateMaxPriorityFeePerGas(config, parameters)\n    },\n    queryKey: estimateMaxPriorityFeePerGasQueryKey(options),\n  } as const satisfies QueryOptions<\n    EstimateMaxPriorityFeePerGasQueryFnData,\n    EstimateMaxPriorityFeePerGasErrorType,\n    EstimateMaxPriorityFeePerGasData,\n    EstimateMaxPriorityFeePerGasQueryKey<config>\n  >\n}\n\nexport type EstimateMaxPriorityFeePerGasQueryFnData =\n  EstimateMaxPriorityFeePerGasReturnType\n\nexport type EstimateMaxPriorityFeePerGasData =\n  EstimateMaxPriorityFeePerGasQueryFnData\n\nexport function estimateMaxPriorityFeePerGasQueryKey<config extends Config>(\n  options: EstimateMaxPriorityFeePerGasOptions<config> = {},\n) {\n  return ['estimateMaxPriorityFeePerGas', filterQueryOptions(options)] as const\n}\n\nexport type EstimateMaxPriorityFeePerGasQueryKey<config extends Config> =\n  ReturnType<typeof estimateMaxPriorityFeePerGasQueryKey<config>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetBalanceErrorType,\n  type GetBalanceParameters,\n  type GetBalanceReturnType,\n  getBalance,\n} from '../actions/getBalance.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, PartialBy } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBalanceOptions<config extends Config> = Compute<\n  PartialBy<GetBalanceParameters<config>, 'address'> & ScopeKeyParameter\n>\n\nexport function getBalanceQueryOptions<config extends Config>(\n  config: config,\n  options: GetBalanceOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      const balance = await getBalance(config, {\n        ...(parameters as GetBalanceParameters),\n        address,\n      })\n      return balance ?? null\n    },\n    queryKey: getBalanceQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBalanceQueryFnData,\n    GetBalanceErrorType,\n    GetBalanceData,\n    GetBalanceQueryKey<config>\n  >\n}\n\nexport type GetBalanceQueryFnData = Compute<GetBalanceReturnType>\n\nexport type GetBalanceData = GetBalanceQueryFnData\n\nexport function getBalanceQueryKey<config extends Config>(\n  options: GetBalanceOptions<config> = {},\n) {\n  return ['balance', filterQueryOptions(options)] as const\n}\n\nexport type GetBalanceQueryKey<config extends Config> = ReturnType<\n  typeof getBalanceQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { BlockTag } from 'viem'\n\nimport {\n  type GetBlockErrorType,\n  type GetBlockParameters,\n  type GetBlockReturnType,\n  getBlock,\n} from '../actions/getBlock.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBlockOptions<\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<\n    GetBlockParameters<includeTransactions, blockTag, config, chainId>\n  > &\n    ScopeKeyParameter\n>\n\nexport function getBlockQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n>(\n  config: config,\n  options: GetBlockOptions<includeTransactions, blockTag, config, chainId> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const block = await getBlock(config, parameters)\n      return (block ?? null) as any\n    },\n    queryKey: getBlockQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBlockQueryFnData<includeTransactions, blockTag, config, chainId>,\n    GetBlockErrorType,\n    GetBlockData<includeTransactions, blockTag, config, chainId>,\n    GetBlockQueryKey<includeTransactions, blockTag, config, chainId>\n  >\n}\n\nexport type GetBlockQueryFnData<\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetBlockReturnType<includeTransactions, blockTag, config, chainId>\n\nexport type GetBlockData<\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetBlockQueryFnData<includeTransactions, blockTag, config, chainId>\n\nexport function getBlockQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n>(\n  options: GetBlockOptions<includeTransactions, blockTag, config, chainId> = {},\n) {\n  return ['block', filterQueryOptions(options)] as const\n}\n\nexport type GetBlockQueryKey<\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<\n  typeof getBlockQueryKey<config, chainId, includeTransactions, blockTag>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetBlockNumberErrorType,\n  type GetBlockNumberParameters,\n  type GetBlockNumberReturnType,\n  getBlockNumber,\n} from '../actions/getBlockNumber.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBlockNumberOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetBlockNumberParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getBlockNumberQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetBlockNumberOptions<config, chainId> = {}) {\n  return {\n    gcTime: 0,\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const blockNumber = await getBlockNumber(config, parameters)\n      return blockNumber ?? null\n    },\n    queryKey: getBlockNumberQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBlockNumberQueryFnData,\n    GetBlockNumberErrorType,\n    GetBlockNumberData,\n    GetBlockNumberQueryKey<config, chainId>\n  >\n}\n\nexport type GetBlockNumberQueryFnData = GetBlockNumberReturnType\n\nexport type GetBlockNumberData = GetBlockNumberQueryFnData\n\nexport function getBlockNumberQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetBlockNumberOptions<config, chainId> = {}) {\n  return ['blockNumber', filterQueryOptions(options)] as const\n}\n\nexport type GetBlockNumberQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getBlockNumberQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetBlockTransactionCountErrorType,\n  type GetBlockTransactionCountParameters,\n  type GetBlockTransactionCountReturnType,\n  getBlockTransactionCount,\n} from '../actions/getBlockTransactionCount.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { ExactPartial, UnionCompute } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBlockTransactionCountOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = UnionCompute<\n  ExactPartial<GetBlockTransactionCountParameters<config, chainId>> &\n    ScopeKeyParameter\n>\n\nexport function getBlockTransactionCountQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  options: GetBlockTransactionCountOptions<config, chainId> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const blockTransactionCount = await getBlockTransactionCount(\n        config,\n        parameters,\n      )\n      return blockTransactionCount ?? null\n    },\n    queryKey: getBlockTransactionCountQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBlockTransactionCountQueryFnData,\n    GetBlockTransactionCountErrorType,\n    GetBlockTransactionCountData,\n    GetBlockTransactionCountQueryKey<config, chainId>\n  >\n}\n\nexport type GetBlockTransactionCountQueryFnData =\n  GetBlockTransactionCountReturnType\n\nexport type GetBlockTransactionCountData = GetBlockTransactionCountQueryFnData\n\nexport function getBlockTransactionCountQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetBlockTransactionCountOptions<config, chainId> = {}) {\n  return ['blockTransactionCount', filterQueryOptions(options)] as const\n}\n\nexport type GetBlockTransactionCountQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getBlockTransactionCountQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetBytecodeErrorType,\n  type GetBytecodeParameters,\n  type GetBytecodeReturnType,\n  getBytecode,\n} from '../actions/getBytecode.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBytecodeOptions<config extends Config> = Compute<\n  ExactPartial<GetBytecodeParameters<config>> & ScopeKeyParameter\n>\n\nexport function getBytecodeQueryOptions<config extends Config>(\n  config: config,\n  options: GetBytecodeOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      const bytecode = await getBytecode(config, { ...parameters, address })\n      return (bytecode ?? null) as any\n    },\n    queryKey: getBytecodeQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBytecodeQueryFnData,\n    GetBytecodeErrorType,\n    GetBytecodeData,\n    GetBytecodeQueryKey<config>\n  >\n}\nexport type GetBytecodeQueryFnData = GetBytecodeReturnType\n\nexport type GetBytecodeData = GetBytecodeQueryFnData\n\nexport function getBytecodeQueryKey<config extends Config>(\n  options: GetBytecodeOptions<config>,\n) {\n  return ['getBytecode', filterQueryOptions(options)] as const\n}\n\nexport type GetBytecodeQueryKey<config extends Config> = ReturnType<\n  typeof getBytecodeQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetCallsStatusErrorType,\n  type GetCallsStatusParameters,\n  type GetCallsStatusReturnType,\n  getCallsStatus,\n} from '../actions/getCallsStatus.js'\nimport type { Config } from '../createConfig.js'\nimport { ConnectorNotConnectedError } from '../errors/config.js'\nimport { filterQueryOptions } from '../query/utils.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\n\nexport type GetCallsStatusOptions = Compute<\n  GetCallsStatusParameters & ScopeKeyParameter\n>\n\nexport function getCallsStatusQueryOptions<config extends Config>(\n  config: config,\n  options: GetCallsStatusOptions,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const status = await getCallsStatus(config, parameters)\n      return status\n    },\n    queryKey: getCallsStatusQueryKey(options),\n    retry(failureCount, error) {\n      if (error instanceof ConnectorNotConnectedError) return false\n      return failureCount < 3\n    },\n  } as const satisfies QueryOptions<\n    GetCallsStatusQueryFnData,\n    GetCallsStatusErrorType,\n    GetCallsStatusData,\n    GetCallsStatusQueryKey\n  >\n}\n\nexport type GetCallsStatusQueryFnData = GetCallsStatusReturnType\n\nexport type GetCallsStatusData = GetCallsStatusQueryFnData\n\nexport function getCallsStatusQueryKey(options: GetCallsStatusOptions) {\n  return ['callsStatus', filterQueryOptions(options)] as const\n}\n\nexport type GetCallsStatusQueryKey = ReturnType<typeof getCallsStatusQueryKey>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetCapabilitiesErrorType,\n  type GetCapabilitiesParameters,\n  type GetCapabilitiesReturnType,\n  getCapabilities,\n} from '../actions/getCapabilities.js'\nimport type { Config } from '../createConfig.js'\nimport { ConnectorNotConnectedError } from '../errors/config.js'\nimport { filterQueryOptions } from '../query/utils.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\n\nexport type GetCapabilitiesOptions<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = Compute<\n  ExactPartial<GetCapabilitiesParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getCapabilitiesQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(config: config, options: GetCapabilitiesOptions<config, chainId> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const capabilities = await getCapabilities(config, parameters)\n      return capabilities\n    },\n    queryKey: getCapabilitiesQueryKey(options),\n    retry(failureCount, error) {\n      if (error instanceof ConnectorNotConnectedError) return false\n      return failureCount < 3\n    },\n  } as const satisfies QueryOptions<\n    GetCapabilitiesQueryFnData<config, chainId>,\n    GetCapabilitiesErrorType,\n    GetCapabilitiesData<config, chainId>,\n    GetCapabilitiesQueryKey<config, chainId>\n  >\n}\n\nexport type GetCapabilitiesQueryFnData<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = GetCapabilitiesReturnType<config, chainId>\n\nexport type GetCapabilitiesData<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = GetCapabilitiesQueryFnData<config, chainId>\n\nexport function getCapabilitiesQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(options: GetCapabilitiesOptions<config, chainId> = {}) {\n  return ['capabilities', filterQueryOptions(options)] as const\n}\n\nexport type GetCapabilitiesQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = ReturnType<typeof getCapabilitiesQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetConnectorClientErrorType,\n  type GetConnectorClientParameters,\n  type GetConnectorClientReturnType,\n  getConnectorClient,\n} from '../actions/getConnectorClient.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetConnectorClientOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetConnectorClientParameters<config, chainId>> &\n    ScopeKeyParameter\n>\n\nexport function getConnectorClientQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetConnectorClientOptions<config, chainId> = {}) {\n  return {\n    gcTime: 0,\n    async queryFn({ queryKey }) {\n      const { connector } = options\n      const { connectorUid: _, scopeKey: _s, ...parameters } = queryKey[1]\n      return getConnectorClient(config, {\n        ...parameters,\n        connector,\n      }) as unknown as Promise<GetConnectorClientReturnType<config, chainId>>\n    },\n    queryKey: getConnectorClientQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetConnectorClientQueryFnData<config, chainId>,\n    GetConnectorClientErrorType,\n    GetConnectorClientData<config, chainId>,\n    GetConnectorClientQueryKey<config, chainId>\n  >\n}\n\nexport type GetConnectorClientQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetConnectorClientReturnType<config, chainId>\n\nexport type GetConnectorClientData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetConnectorClientQueryFnData<config, chainId>\n\nexport function getConnectorClientQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetConnectorClientOptions<config, chainId> = {}) {\n  const { connector, ...parameters } = options\n  return [\n    'connectorClient',\n    { ...filterQueryOptions(parameters), connectorUid: connector?.uid },\n  ] as const\n}\n\nexport type GetConnectorClientQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getConnectorClientQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsAddressErrorType,\n  type GetEnsAddressParameters,\n  type GetEnsAddressReturnType,\n  getEnsAddress,\n} from '../actions/getEnsAddress.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsAddressOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsAddressParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsAddressQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsAddressOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { name, scopeKey: _, ...parameters } = queryKey[1]\n      if (!name) throw new Error('name is required')\n      return getEnsAddress(config, { ...parameters, name })\n    },\n    queryKey: getEnsAddressQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsAddressQueryFnData,\n    GetEnsAddressErrorType,\n    GetEnsAddressData,\n    GetEnsAddressQueryKey<config>\n  >\n}\n\nexport type GetEnsAddressQueryFnData = GetEnsAddressReturnType\n\nexport type GetEnsAddressData = GetEnsAddressQueryFnData\n\nexport function getEnsAddressQueryKey<config extends Config>(\n  options: GetEnsAddressOptions<config> = {},\n) {\n  return ['ensAddress', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsAddressQueryKey<config extends Config> = ReturnType<\n  typeof getEnsAddressQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsAvatarErrorType,\n  type GetEnsAvatarParameters,\n  type GetEnsAvatarReturnType,\n  getEnsAvatar,\n} from '../actions/getEnsAvatar.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsAvatarOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsAvatarParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsAvatarQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsAvatarOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { name, scopeKey: _, ...parameters } = queryKey[1]\n      if (!name) throw new Error('name is required')\n      return getEnsAvatar(config, { ...parameters, name })\n    },\n    queryKey: getEnsAvatarQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsAvatarQueryFnData,\n    GetEnsAvatarErrorType,\n    GetEnsAvatarData,\n    GetEnsAvatarQueryKey<config>\n  >\n}\n\nexport type GetEnsAvatarQueryFnData = GetEnsAvatarReturnType\n\nexport type GetEnsAvatarData = GetEnsAvatarQueryFnData\n\nexport function getEnsAvatarQueryKey<config extends Config>(\n  options: GetEnsAvatarOptions<config> = {},\n) {\n  return ['ensAvatar', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsAvatarQueryKey<config extends Config> = ReturnType<\n  typeof getEnsAvatarQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsNameErrorType,\n  type GetEnsNameParameters,\n  type GetEnsNameReturnType,\n  getEnsName,\n} from '../actions/getEnsName.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsNameOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsNameParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsNameQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsNameOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      return getEnsName(config, { ...parameters, address })\n    },\n    queryKey: getEnsNameQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsNameQueryFnData,\n    GetEnsNameErrorType,\n    GetEnsNameData,\n    GetEnsNameQueryKey<config>\n  >\n}\n\nexport type GetEnsNameQueryFnData = GetEnsNameReturnType\n\nexport type GetEnsNameData = GetEnsNameQueryFnData\n\nexport function getEnsNameQueryKey<config extends Config>(\n  options: GetEnsNameOptions<config> = {},\n) {\n  return ['ensName', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsNameQueryKey<config extends Config> = ReturnType<\n  typeof getEnsNameQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsResolverErrorType,\n  type GetEnsResolverParameters,\n  type GetEnsResolverReturnType,\n  getEnsResolver,\n} from '../actions/getEnsResolver.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsResolverOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsResolverParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsResolverQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsResolverOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { name, scopeKey: _, ...parameters } = queryKey[1]\n      if (!name) throw new Error('name is required')\n      return getEnsResolver(config, { ...parameters, name })\n    },\n    queryKey: getEnsResolverQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsResolverQueryFnData,\n    GetEnsResolverErrorType,\n    GetEnsResolverData,\n    GetEnsResolverQueryKey<config>\n  >\n}\n\nexport type GetEnsResolverQueryFnData = GetEnsResolverReturnType\n\nexport type GetEnsResolverData = GetEnsResolverQueryFnData\n\nexport function getEnsResolverQueryKey<config extends Config>(\n  options: GetEnsResolverOptions<config> = {},\n) {\n  return ['ensResolver', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsResolverQueryKey<config extends Config> = ReturnType<\n  typeof getEnsResolverQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsTextErrorType,\n  type GetEnsTextParameters,\n  type GetEnsTextReturnType,\n  getEnsText,\n} from '../actions/getEnsText.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsTextOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsTextParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsTextQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsTextOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { key, name, scopeKey: _, ...parameters } = queryKey[1]\n      if (!key || !name) throw new Error('key and name are required')\n      return getEnsText(config, { ...parameters, key, name })\n    },\n    queryKey: getEnsTextQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsTextQueryFnData,\n    GetEnsTextErrorType,\n    GetEnsTextData,\n    GetEnsTextQueryKey<config>\n  >\n}\n\nexport type GetEnsTextQueryFnData = GetEnsTextReturnType\n\nexport type GetEnsTextData = GetEnsTextQueryFnData\n\nexport function getEnsTextQueryKey<config extends Config>(\n  options: GetEnsTextOptions<config> = {},\n) {\n  return ['ensText', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsTextQueryKey<config extends Config> = ReturnType<\n  typeof getEnsTextQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetFeeHistoryErrorType,\n  type GetFeeHistoryParameters,\n  type GetFeeHistoryReturnType,\n  getFeeHistory,\n} from '../actions/getFeeHistory.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, PartialBy } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetFeeHistoryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  PartialBy<\n    GetFeeHistoryParameters<config, chainId>,\n    'blockCount' | 'rewardPercentiles'\n  > &\n    ScopeKeyParameter\n>\n\nexport function getFeeHistoryQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetFeeHistoryOptions<config, chainId> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const {\n        blockCount,\n        rewardPercentiles,\n        scopeKey: _,\n        ...parameters\n      } = queryKey[1]\n      if (!blockCount) throw new Error('blockCount is required')\n      if (!rewardPercentiles) throw new Error('rewardPercentiles is required')\n      const feeHistory = await getFeeHistory(config, {\n        ...(parameters as GetFeeHistoryParameters),\n        blockCount,\n        rewardPercentiles,\n      })\n      return feeHistory ?? null\n    },\n    queryKey: getFeeHistoryQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetFeeHistoryQueryFnData,\n    GetFeeHistoryErrorType,\n    GetFeeHistoryData,\n    GetFeeHistoryQueryKey<config, chainId>\n  >\n}\n\nexport type GetFeeHistoryQueryFnData = GetFeeHistoryReturnType\n\nexport type GetFeeHistoryData = GetFeeHistoryQueryFnData\n\nexport function getFeeHistoryQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetFeeHistoryOptions<config, chainId> = {}) {\n  return ['feeHistory', filterQueryOptions(options)] as const\n}\n\nexport type GetFeeHistoryQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getFeeHistoryQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetGasPriceErrorType,\n  type GetGasPriceParameters,\n  type GetGasPriceReturnType,\n  getGasPrice,\n} from '../actions/getGasPrice.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetGasPriceOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetGasPriceParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getGasPriceQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetGasPriceOptions<config, chainId> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const gasPrice = await getGasPrice(config, parameters)\n      return gasPrice ?? null\n    },\n    queryKey: getGasPriceQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetGasPriceQueryFnData,\n    GetGasPriceErrorType,\n    GetGasPriceData,\n    GetGasPriceQueryKey<config, chainId>\n  >\n}\n\nexport type GetGasPriceQueryFnData = GetGasPriceReturnType\n\nexport type GetGasPriceData = GetGasPriceQueryFnData\n\nexport function getGasPriceQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetGasPriceOptions<config, chainId> = {}) {\n  return ['gasPrice', filterQueryOptions(options)] as const\n}\n\nexport type GetGasPriceQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getGasPriceQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetProofErrorType,\n  type GetProofParameters,\n  type GetProofReturnType,\n  getProof,\n} from '../actions/getProof.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetProofOptions<config extends Config> = Compute<\n  ExactPartial<GetProofParameters<config>> & ScopeKeyParameter\n>\n\nexport function getProofQueryOptions<config extends Config>(\n  config: config,\n  options: GetProofOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, storageKeys, ...parameters } = queryKey[1]\n      if (!address || !storageKeys)\n        throw new Error('address and storageKeys are required')\n      return getProof(config, { ...parameters, address, storageKeys })\n    },\n    queryKey: getProofQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetProofQueryFnData,\n    GetProofErrorType,\n    GetProofData,\n    GetProofQueryKey<config>\n  >\n}\n\nexport type GetProofQueryFnData = GetProofReturnType\n\nexport type GetProofData = GetProofQueryFnData\n\nexport function getProofQueryKey<config extends Config>(\n  options: GetProofOptions<config>,\n) {\n  return ['getProof', filterQueryOptions(options)] as const\n}\n\nexport type GetProofQueryKey<config extends Config> = ReturnType<\n  typeof getProofQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetStorageAtErrorType,\n  type GetStorageAtParameters,\n  type GetStorageAtReturnType,\n  getStorageAt,\n} from '../actions/getStorageAt.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetStorageAtOptions<config extends Config> = Compute<\n  ExactPartial<GetStorageAtParameters<config>> & ScopeKeyParameter\n>\n\nexport function getStorageAtQueryOptions<config extends Config>(\n  config: config,\n  options: GetStorageAtOptions<config> = {},\n) {\n  return {\n    queryFn({ queryKey }) {\n      const { address, slot, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address || !slot) throw new Error('address and slot are required')\n      return getStorageAt(config, { ...parameters, address, slot })\n    },\n    queryKey: getStorageAtQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetStorageAtQueryFnData,\n    GetStorageAtErrorType,\n    GetStorageAtData,\n    GetStorageAtQueryKey<config>\n  >\n}\n\nexport type GetStorageAtQueryFnData = GetStorageAtReturnType\n\nexport type GetStorageAtData = GetStorageAtQueryFnData\n\nexport function getStorageAtQueryKey<config extends Config>(\n  options: GetStorageAtOptions<config>,\n) {\n  return ['getStorageAt', filterQueryOptions(options)] as const\n}\n\nexport type GetStorageAtQueryKey<config extends Config> = ReturnType<\n  typeof getStorageAtQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTokenErrorType,\n  type GetTokenParameters,\n  type GetTokenReturnType,\n  getToken,\n} from '../actions/getToken.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTokenOptions<config extends Config> = Compute<\n  ExactPartial<GetTokenParameters<config>> & ScopeKeyParameter\n>\n\nexport function getTokenQueryOptions<config extends Config>(\n  config: config,\n  options: GetTokenOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      return getToken(config, { ...parameters, address })\n    },\n    queryKey: getTokenQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTokenQueryFnData,\n    GetTokenErrorType,\n    GetTokenData,\n    GetTokenQueryKey<config>\n  >\n}\n\nexport type GetTokenQueryFnData = GetTokenReturnType\n\nexport type GetTokenData = GetTokenQueryFnData\n\nexport function getTokenQueryKey<config extends Config>(\n  options: GetTokenOptions<config> = {},\n) {\n  return ['token', filterQueryOptions(options)] as const\n}\n\nexport type GetTokenQueryKey<config extends Config> = ReturnType<\n  typeof getTokenQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTransactionErrorType,\n  type GetTransactionParameters,\n  type GetTransactionReturnType,\n  getTransaction,\n} from '../actions/getTransaction.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTransactionOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetTransactionParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getTransactionQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetTransactionOptions<config, chainId> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const { blockHash, blockNumber, blockTag, hash, index } = queryKey[1]\n      if (!blockHash && !blockNumber && !blockTag && !hash)\n        throw new Error('blockHash, blockNumber, blockTag, or hash is required')\n      if (!hash && !index)\n        throw new Error(\n          'index is required for blockHash, blockNumber, or blockTag',\n        )\n      const { scopeKey: _, ...rest } = queryKey[1]\n      return getTransaction(\n        config,\n        rest as GetTransactionParameters,\n      ) as unknown as Promise<GetTransactionQueryFnData<config, chainId>>\n    },\n    queryKey: getTransactionQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTransactionQueryFnData<config, chainId>,\n    GetTransactionErrorType,\n    GetTransactionData<config, chainId>,\n    GetTransactionQueryKey<config, chainId>\n  >\n}\n\nexport type GetTransactionQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetTransactionReturnType<config, chainId>\n\nexport type GetTransactionData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetTransactionQueryFnData<config, chainId>\n\nexport function getTransactionQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetTransactionOptions<config, chainId> = {}) {\n  return ['transaction', filterQueryOptions(options)] as const\n}\n\nexport type GetTransactionQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getTransactionQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTransactionConfirmationsErrorType,\n  type GetTransactionConfirmationsParameters,\n  type GetTransactionConfirmationsReturnType,\n  getTransactionConfirmations,\n} from '../actions/getTransactionConfirmations.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTransactionConfirmationsOptions<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n> = UnionExactPartial<GetTransactionConfirmationsParameters<config, chainId>> &\n  ScopeKeyParameter\n\nexport function getTransactionConfirmationsQueryOptions<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n>(\n  config: config,\n  options: GetTransactionConfirmationsOptions<config, chainId> = {} as any,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const {\n        hash,\n        transactionReceipt,\n        scopeKey: _,\n        ...parameters\n      } = queryKey[1]\n      if (!hash && !transactionReceipt)\n        throw new Error('hash or transactionReceipt is required')\n\n      const confirmations = await getTransactionConfirmations(config, {\n        hash,\n        transactionReceipt,\n        ...(parameters as any),\n      })\n      return confirmations ?? null\n    },\n    queryKey: getTransactionConfirmationsQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTransactionConfirmationsQueryFnData,\n    GetTransactionConfirmationsErrorType,\n    GetTransactionConfirmationsData,\n    GetTransactionConfirmationsQueryKey<config, chainId>\n  >\n}\n\nexport type GetTransactionConfirmationsQueryFnData =\n  GetTransactionConfirmationsReturnType\n\nexport type GetTransactionConfirmationsData =\n  GetTransactionConfirmationsQueryFnData\n\nexport function getTransactionConfirmationsQueryKey<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n>(options: GetTransactionConfirmationsOptions<config, chainId> = {} as any) {\n  return ['transactionConfirmations', filterQueryOptions(options)] as const\n}\n\nexport type GetTransactionConfirmationsQueryKey<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n> = ReturnType<typeof getTransactionConfirmationsQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTransactionCountErrorType,\n  type GetTransactionCountParameters,\n  type GetTransactionCountReturnType,\n  getTransactionCount,\n} from '../actions/getTransactionCount.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, PartialBy } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTransactionCountOptions<config extends Config> = Compute<\n  PartialBy<GetTransactionCountParameters<config>, 'address'> &\n    ScopeKeyParameter\n>\n\nexport function getTransactionCountQueryOptions<config extends Config>(\n  config: config,\n  options: GetTransactionCountOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      const transactionCount = await getTransactionCount(config, {\n        ...(parameters as GetTransactionCountParameters),\n        address,\n      })\n      return transactionCount ?? null\n    },\n    queryKey: getTransactionCountQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTransactionCountQueryFnData,\n    GetTransactionCountErrorType,\n    GetTransactionCountData,\n    GetTransactionCountQueryKey<config>\n  >\n}\n\nexport type GetTransactionCountQueryFnData =\n  Compute<GetTransactionCountReturnType>\n\nexport type GetTransactionCountData = GetTransactionCountQueryFnData\n\nexport function getTransactionCountQueryKey<config extends Config>(\n  options: GetTransactionCountOptions<config> = {},\n) {\n  return ['transactionCount', filterQueryOptions(options)] as const\n}\n\nexport type GetTransactionCountQueryKey<config extends Config> = ReturnType<\n  typeof getTransactionCountQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { GetTransactionReceiptReturnType } from '../actions/getTransactionReceipt.js'\nimport {\n  type GetTransactionReceiptErrorType,\n  type GetTransactionReceiptParameters,\n  getTransactionReceipt,\n} from '../actions/getTransactionReceipt.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTransactionReceiptOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetTransactionReceiptParameters<config, chainId>> &\n    ScopeKeyParameter\n>\n\nexport function getTransactionReceiptQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetTransactionReceiptOptions<config, chainId> = {}) {\n  return {\n    queryFn({ queryKey }) {\n      const { hash, scopeKey: _, ...parameters } = queryKey[1]\n      if (!hash) throw new Error('hash is required')\n      return getTransactionReceipt(config, { ...parameters, hash })\n    },\n    queryKey: getTransactionReceiptQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTransactionReceiptQueryFnData<config, chainId>,\n    GetTransactionReceiptErrorType,\n    GetTransactionReceiptData<config, chainId>,\n    GetTransactionReceiptQueryKey<config, chainId>\n  >\n}\nexport type GetTransactionReceiptQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetTransactionReceiptReturnType<config, chainId>\n\nexport type GetTransactionReceiptData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetTransactionReceiptQueryFnData<config, chainId>\n\nexport function getTransactionReceiptQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetTransactionReceiptOptions<config, chainId>) {\n  return ['getTransactionReceipt', filterQueryOptions(options)] as const\n}\n\nexport type GetTransactionReceiptQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getTransactionReceiptQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetWalletClientErrorType,\n  type GetWalletClientParameters,\n  type GetWalletClientReturnType,\n  getWalletClient,\n} from '../actions/getWalletClient.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetWalletClientOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetWalletClientParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getWalletClientQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetWalletClientOptions<config, chainId> = {}) {\n  return {\n    gcTime: 0,\n    async queryFn({ queryKey }) {\n      const { connector } = options\n      const { connectorUid: _, scopeKey: _s, ...parameters } = queryKey[1]\n      return getWalletClient(config, { ...parameters, connector })\n    },\n    queryKey: getWalletClientQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetWalletClientQueryFnData<config, chainId>,\n    GetWalletClientErrorType,\n    GetWalletClientData<config, chainId>,\n    GetWalletClientQueryKey<config, chainId>\n  >\n}\n\nexport type GetWalletClientQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetWalletClientReturnType<config, chainId>\n\nexport type GetWalletClientData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetWalletClientQueryFnData<config, chainId>\n\nexport function getWalletClientQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetWalletClientOptions<config, chainId> = {}) {\n  const { connector, ...parameters } = options\n  return [\n    'walletClient',\n    { ...filterQueryOptions(parameters), connectorUid: connector?.uid },\n  ] as const\n}\n\nexport type GetWalletClientQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getWalletClientQueryKey<config, chainId>>\n", "import type { ContractFunctionParameters } from 'viem'\nimport {\n  type ReadContractsErrorType,\n  type ReadContractsParameters,\n  type ReadContractsReturnType,\n  readContracts,\n} from '../actions/readContracts.js'\nimport type { Config } from '../createConfig.js'\nimport type {\n  ChainIdParameter,\n  ScopeKeyParameter,\n} from '../types/properties.js'\nimport type { StrictOmit } from '../types/utils.js'\nimport type { InfiniteQueryOptions } from './types.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type InfiniteReadContractsOptions<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  pageParam,\n  config extends Config,\n> = {\n  cacheKey: string\n  contracts(\n    pageParam: pageParam,\n  ): ReadContractsParameters<contracts, allowFailure, config>['contracts']\n} & StrictOmit<\n  ReadContractsParameters<contracts, allowFailure, config>,\n  'contracts'\n> &\n  ScopeKeyParameter\n\nexport function infiniteReadContractsQueryOptions<\n  config extends Config,\n  const contracts extends readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  pageParam = unknown,\n>(\n  config: config,\n  options: InfiniteReadContractsOptions<\n    contracts,\n    allowFailure,\n    pageParam,\n    config\n  > &\n    ChainIdParameter<config> &\n    RequiredPageParamsParameters<contracts, allowFailure, pageParam>,\n) {\n  return {\n    ...options.query,\n    async queryFn({ pageParam, queryKey }) {\n      const { contracts } = options\n      const { cacheKey: _, scopeKey: _s, ...parameters } = queryKey[1]\n      return (await readContracts(config, {\n        ...parameters,\n        contracts: contracts(pageParam as any),\n      })) as ReadContractsReturnType<contracts, allowFailure>\n    },\n    queryKey: infiniteReadContractsQueryKey(options),\n  } as const satisfies InfiniteQueryOptions<\n    InfiniteReadContractsQueryFnData<contracts, allowFailure>,\n    ReadContractsErrorType,\n    InfiniteReadContractsData<contracts, allowFailure>,\n    InfiniteReadContractsData<contracts, allowFailure>,\n    InfiniteReadContractsQueryKey<contracts, allowFailure, pageParam, config>,\n    pageParam\n  >\n}\n\ntype RequiredPageParamsParameters<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  pageParam,\n> = {\n  query: {\n    initialPageParam: pageParam\n    getNextPageParam(\n      lastPage: InfiniteReadContractsQueryFnData<contracts, allowFailure>,\n      allPages: InfiniteReadContractsQueryFnData<contracts, allowFailure>[],\n      lastPageParam: pageParam,\n      allPageParams: pageParam[],\n    ): pageParam | undefined | null\n  }\n}\n\nexport type InfiniteReadContractsQueryFnData<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n> = ReadContractsReturnType<contracts, allowFailure>\n\nexport type InfiniteReadContractsData<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n> = InfiniteReadContractsQueryFnData<contracts, allowFailure>\n\nexport function infiniteReadContractsQueryKey<\n  config extends Config,\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  pageParam,\n>(\n  options: InfiniteReadContractsOptions<\n    contracts,\n    allowFailure,\n    pageParam,\n    config\n  > &\n    ChainIdParameter<config> &\n    RequiredPageParamsParameters<contracts, allowFailure, pageParam>,\n) {\n  const { contracts: _, query: _q, ...parameters } = options\n  return ['infiniteReadContracts', filterQueryOptions(parameters)] as const\n}\n\nexport type InfiniteReadContractsQueryKey<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  pageParam,\n  config extends Config,\n> = ReturnType<\n  typeof infiniteReadContractsQueryKey<\n    config,\n    contracts,\n    allowFailure,\n    pageParam\n  >\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport type { PrepareTransactionRequestRequest as viem_PrepareTransactionRequestRequest } from 'viem'\n\nimport {\n  type PrepareTransactionRequestErrorType,\n  type PrepareTransactionRequestParameters,\n  type PrepareTransactionRequestReturnType,\n  prepareTransactionRequest,\n} from '../actions/prepareTransactionRequest.js'\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type PrepareTransactionRequestOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n> = UnionExactPartial<\n  PrepareTransactionRequestParameters<config, chainId, request>\n> &\n  ScopeKeyParameter\n\nexport function prepareTransactionRequestQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n>(\n  config: config,\n  options: PrepareTransactionRequestOptions<\n    config,\n    chainId,\n    request\n  > = {} as any,\n) {\n  return {\n    queryFn({ queryKey }) {\n      const { scopeKey: _, to, ...parameters } = queryKey[1]\n      if (!to) throw new Error('to is required')\n      return prepareTransactionRequest(config, {\n        to,\n        ...(parameters as any),\n      }) as unknown as Promise<\n        PrepareTransactionRequestQueryFnData<config, chainId, request>\n      >\n    },\n    queryKey: prepareTransactionRequestQueryKey(options),\n  } as const satisfies QueryOptions<\n    PrepareTransactionRequestQueryFnData<config, chainId, request>,\n    PrepareTransactionRequestErrorType,\n    PrepareTransactionRequestData<config, chainId, request>,\n    PrepareTransactionRequestQueryKey<config, chainId, request>\n  >\n}\nexport type PrepareTransactionRequestQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n> = PrepareTransactionRequestReturnType<config, chainId, request>\n\nexport type PrepareTransactionRequestData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n> = PrepareTransactionRequestQueryFnData<config, chainId, request>\n\nexport function prepareTransactionRequestQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n>(options: PrepareTransactionRequestOptions<config, chainId, request>) {\n  return ['prepareTransactionRequest', filterQueryOptions(options)] as const\n}\n\nexport type PrepareTransactionRequestQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n> = ReturnType<\n  typeof prepareTransactionRequestQueryKey<config, chainId, request>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\n\nimport {\n  type ReadContractErrorType,\n  type ReadContractParameters,\n  type ReadContractReturnType,\n  readContract,\n} from '../actions/readContract.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type ReadContractOptions<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config,\n> = UnionExactPartial<ReadContractParameters<abi, functionName, args, config>> &\n  ScopeKeyParameter\n\nexport function readContractQueryOptions<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n>(\n  config: config,\n  options: ReadContractOptions<abi, functionName, args, config> = {} as any,\n) {\n  return {\n    // TODO: Support `signal` once Viem actions allow passthrough\n    // https://tkdodo.eu/blog/why-you-want-react-query#bonus-cancellation\n    async queryFn({ queryKey }) {\n      const abi = options.abi as Abi\n      if (!abi) throw new Error('abi is required')\n\n      const { functionName, scopeKey: _, ...parameters } = queryKey[1]\n      const addressOrCodeParams = (() => {\n        const params = queryKey[1] as unknown as ReadContractParameters\n        if (params.address) return { address: params.address }\n        if (params.code) return { code: params.code }\n        throw new Error('address or code is required')\n      })()\n\n      if (!functionName) throw new Error('functionName is required')\n\n      return readContract(config, {\n        abi,\n        functionName,\n        args: parameters.args as readonly unknown[],\n        ...addressOrCodeParams,\n        ...parameters,\n      }) as Promise<ReadContractData<abi, functionName, args>>\n    },\n    queryKey: readContractQueryKey(options as any) as any,\n  } as const satisfies QueryOptions<\n    ReadContractQueryFnData<abi, functionName, args>,\n    ReadContractErrorType,\n    ReadContractData<abi, functionName, args>,\n    ReadContractQueryKey<abi, functionName, args, config>\n  >\n}\n\nexport type ReadContractQueryFnData<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n> = ReadContractReturnType<abi, functionName, args>\n\nexport type ReadContractData<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n> = ReadContractQueryFnData<abi, functionName, args>\n\nexport function readContractQueryKey<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n>(options: ReadContractOptions<abi, functionName, args, config> = {} as any) {\n  const { abi: _, ...rest } = options\n  return ['readContract', filterQueryOptions(rest)] as const\n}\n\nexport type ReadContractQueryKey<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config,\n> = ReturnType<typeof readContractQueryKey<config, abi, functionName, args>>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type {\n  ContractFunctionParameters,\n  MulticallParameters as viem_MulticallParameters,\n} from 'viem'\n\nimport {\n  type ReadContractsErrorType,\n  type ReadContractsReturnType,\n  readContracts,\n} from '../actions/readContracts.js'\nimport type { Config } from '../createConfig.js'\nimport type {\n  ChainIdParameter,\n  ScopeKeyParameter,\n} from '../types/properties.js'\nimport type { ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type ReadContractsOptions<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  config extends Config,\n> = ExactPartial<\n  viem_MulticallParameters<\n    contracts,\n    allowFailure,\n    { optional: true; properties: ChainIdParameter<config> }\n  >\n> &\n  ScopeKeyParameter\n\nexport function readContractsQueryOptions<\n  config extends Config,\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean = true,\n>(\n  config: config,\n  options: ReadContractsOptions<contracts, allowFailure, config> &\n    ChainIdParameter<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const contracts: ContractFunctionParameters[] = []\n      const length = queryKey[1].contracts.length\n      for (let i = 0; i < length; i++) {\n        const contract = queryKey[1].contracts[i]!\n        const abi = (options.contracts?.[i] as ContractFunctionParameters).abi\n        contracts.push({ ...contract, abi })\n      }\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      return readContracts(config, {\n        ...parameters,\n        contracts,\n      }) as Promise<ReadContractsReturnType<contracts, allowFailure>>\n    },\n    queryKey: readContractsQueryKey(options),\n  } as const satisfies QueryOptions<\n    ReadContractsQueryFnData<contracts, allowFailure>,\n    ReadContractsErrorType,\n    ReadContractsData<contracts, allowFailure>,\n    ReadContractsQueryKey<contracts, allowFailure, config>\n  >\n}\n\nexport type ReadContractsQueryFnData<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n> = ReadContractsReturnType<contracts, allowFailure>\n\nexport type ReadContractsData<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n> = ReadContractsQueryFnData<contracts, allowFailure>\n\nexport function readContractsQueryKey<\n  config extends Config,\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean,\n>(\n  options: ReadContractsOptions<contracts, allowFailure, config> &\n    ChainIdParameter<config> = {},\n) {\n  const contracts = []\n  for (const contract of (options.contracts ??\n    []) as (ContractFunctionParameters & { chainId: number })[]) {\n    const { abi: _, ...rest } = contract\n    contracts.push({ ...rest, chainId: rest.chainId ?? options.chainId })\n  }\n  return [\n    'readContracts',\n    filterQueryOptions({ ...options, contracts }),\n  ] as const\n}\n\nexport type ReadContractsQueryKey<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  config extends Config,\n> = ReturnType<typeof readContractsQueryKey<config, contracts, allowFailure>>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type ReconnectErrorType,\n  type ReconnectParameters,\n  type ReconnectReturnType,\n  reconnect,\n} from '../actions/reconnect.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function reconnectMutationOptions(config: Config) {\n  return {\n    mutationFn(variables) {\n      return reconnect(config, variables)\n    },\n    mutationKey: ['reconnect'],\n  } as const satisfies MutationOptions<\n    ReconnectData,\n    ReconnectErrorType,\n    ReconnectVariables\n  >\n}\n\nexport type ReconnectData = Compute<ReconnectReturnType>\n\nexport type ReconnectVariables = ReconnectParameters | undefined\n\nexport type ReconnectMutate<context = unknown> = Mutate<\n  ReconnectData,\n  ReconnectErrorType,\n  ReconnectVariables,\n  context\n>\n\nexport type ReconnectMutateAsync<context = unknown> = MutateAsync<\n  ReconnectData,\n  ReconnectErrorType,\n  ReconnectVariables,\n  context\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SendCallsErrorType,\n  type SendCallsParameters,\n  type SendCallsReturnType,\n  sendCalls,\n} from '../actions/sendCalls.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function sendCallsMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return sendCalls(config, variables)\n    },\n    mutationKey: ['sendCalls'],\n  } as const satisfies MutationOptions<\n    SendCallsData,\n    SendCallsErrorType,\n    SendCallsVariables<config, config['chains'][number]['id']>\n  >\n}\n\nexport type SendCallsData = Compute<SendCallsReturnType>\n\nexport type SendCallsVariables<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  calls extends readonly unknown[] = readonly unknown[],\n> = SendCallsParameters<config, chainId, calls>\n\nexport type SendCallsMutate<config extends Config, context = unknown> = <\n  const calls extends readonly unknown[],\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SendCallsVariables<config, chainId, calls>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SendCallsData,\n          SendCallsErrorType,\n          Compute<SendCallsVariables<config, chainId, calls>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type SendCallsMutateAsync<config extends Config, context = unknown> = <\n  const calls extends readonly unknown[],\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SendCallsVariables<config, chainId, calls>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SendCallsData,\n          SendCallsErrorType,\n          Compute<SendCallsVariables<config, chainId, calls>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<SendCallsData>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SendTransactionErrorType,\n  type SendTransactionParameters,\n  type SendTransactionReturnType,\n  sendTransaction,\n} from '../actions/sendTransaction.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function sendTransactionMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return sendTransaction(config, variables)\n    },\n    mutationKey: ['sendTransaction'],\n  } as const satisfies MutationOptions<\n    SendTransactionData,\n    SendTransactionErrorType,\n    SendTransactionVariables<config, config['chains'][number]['id']>\n  >\n}\n\nexport type SendTransactionData = Compute<SendTransactionReturnType>\n\nexport type SendTransactionVariables<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = SendTransactionParameters<config, chainId>\n\nexport type SendTransactionMutate<config extends Config, context = unknown> = <\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SendTransactionVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SendTransactionData,\n          SendTransactionErrorType,\n          Compute<SendTransactionVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type SendTransactionMutateAsync<\n  config extends Config,\n  context = unknown,\n> = <chainId extends config['chains'][number]['id']>(\n  variables: SendTransactionVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SendTransactionData,\n          SendTransactionErrorType,\n          Compute<SendTransactionVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<SendTransactionData>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type ShowCallsStatusErrorType,\n  type ShowCallsStatusParameters,\n  type ShowCallsStatusReturnType,\n  showCallsStatus,\n} from '../actions/showCallsStatus.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function showCallsStatusMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return showCallsStatus(config, variables)\n    },\n    mutationKey: ['showCallsStatus'],\n  } as const satisfies MutationOptions<\n    ShowCallsStatusData,\n    ShowCallsStatusErrorType,\n    ShowCallsStatusVariables\n  >\n}\n\nexport type ShowCallsStatusData = Compute<ShowCallsStatusReturnType>\n\nexport type ShowCallsStatusVariables = ShowCallsStatusParameters\n\nexport type ShowCallsStatusMutate<context = unknown> = (\n  variables: ShowCallsStatusVariables,\n  options?:\n    | Compute<\n        MutateOptions<\n          ShowCallsStatusData,\n          ShowCallsStatusErrorType,\n          Compute<ShowCallsStatusVariables>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type ShowCallsStatusMutateAsync<context = unknown> = (\n  variables: ShowCallsStatusVariables,\n  options?:\n    | Compute<\n        MutateOptions<\n          ShowCallsStatusData,\n          ShowCallsStatusErrorType,\n          Compute<ShowCallsStatusVariables>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<ShowCallsStatusData>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SignMessageErrorType,\n  type SignMessageParameters,\n  type SignMessageReturnType,\n  signMessage,\n} from '../actions/signMessage.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function signMessageMutationOptions(config: Config) {\n  return {\n    mutationFn(variables) {\n      return signMessage(config, variables)\n    },\n    mutationKey: ['signMessage'],\n  } as const satisfies MutationOptions<\n    SignMessageData,\n    SignMessageErrorType,\n    SignMessageVariables\n  >\n}\n\nexport type SignMessageData = SignMessageReturnType\n\nexport type SignMessageVariables = Compute<SignMessageParameters>\n\nexport type SignMessageMutate<context = unknown> = Mutate<\n  SignMessageData,\n  SignMessageErrorType,\n  SignMessageVariables,\n  context\n>\n\nexport type SignMessageMutateAsync<context = unknown> = MutateAsync<\n  SignMessageData,\n  SignMessageErrorType,\n  SignMessageVariables,\n  context\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport type { TypedData } from 'viem'\nimport {\n  type SignTypedDataErrorType,\n  type SignTypedDataParameters,\n  type SignTypedDataReturnType,\n  signTypedData,\n} from '../actions/signTypedData.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function signTypedDataMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return signTypedData(config, variables)\n    },\n    mutationKey: ['signTypedData'],\n  } as const satisfies MutationOptions<\n    SignTypedDataData,\n    SignTypedDataErrorType,\n    SignTypedDataVariables\n  >\n}\n\nexport type SignTypedDataData = Compute<SignTypedDataReturnType>\n\nexport type SignTypedDataVariables<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  ///\n  primaryTypes = typedData extends TypedData ? keyof typedData : string,\n> = SignTypedDataParameters<typedData, primaryType, primaryTypes>\n\nexport type SignTypedDataMutate<context = unknown> = <\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  variables: SignTypedDataVariables<typedData, primaryType>,\n  options?:\n    | MutateOptions<\n        SignTypedDataData,\n        SignTypedDataErrorType,\n        SignTypedDataVariables<\n          typedData,\n          primaryType,\n          // use `primaryType` to make sure it's not union of all possible primary types\n          primaryType\n        >,\n        context\n      >\n    | undefined,\n) => void\n\nexport type SignTypedDataMutateAsync<context = unknown> = <\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  variables: SignTypedDataVariables<typedData, primaryType>,\n  options?:\n    | MutateOptions<\n        SignTypedDataData,\n        SignTypedDataErrorType,\n        SignTypedDataVariables<\n          typedData,\n          primaryType,\n          // use `primaryType` to make sure it's not union of all possible primary types\n          primaryType\n        >,\n        context\n      >\n    | undefined,\n) => Promise<SignTypedDataData>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\n\nimport {\n  type SimulateContractErrorType,\n  type SimulateContractParameters,\n  type SimulateContractReturnType,\n  simulateContract,\n} from '../actions/simulateContract.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type SimulateContractOptions<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = UnionExactPartial<\n  SimulateContractParameters<abi, functionName, args, config, chainId>\n> &\n  ScopeKeyParameter\n\nexport function simulateContractQueryOptions<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'] | undefined,\n>(\n  config: config,\n  options: SimulateContractOptions<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId\n  > = {} as any,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { abi, connector } = options\n      if (!abi) throw new Error('abi is required')\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const { address, functionName } = parameters\n      if (!address) throw new Error('address is required')\n      if (!functionName) throw new Error('functionName is required')\n      return simulateContract(config, {\n        abi,\n        connector,\n        ...(parameters as any),\n      })\n    },\n    queryKey: simulateContractQueryKey(options),\n  } as const satisfies QueryOptions<\n    SimulateContractQueryFnData<abi, functionName, args, config, chainId>,\n    SimulateContractErrorType,\n    SimulateContractData<abi, functionName, args, config, chainId>,\n    SimulateContractQueryKey<abi, functionName, args, config, chainId>\n  >\n}\n\nexport type SimulateContractQueryFnData<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = SimulateContractReturnType<abi, functionName, args, config, chainId>\n\nexport type SimulateContractData<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = SimulateContractQueryFnData<abi, functionName, args, config, chainId>\n\nexport function simulateContractQueryKey<\n  config extends Config,\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'] | undefined,\n>(\n  options: SimulateContractOptions<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId\n  > = {} as any,\n) {\n  const { abi: _, connector: _c, ...rest } = options\n  return ['simulateContract', filterQueryOptions(rest)] as const\n}\n\nexport type SimulateContractQueryKey<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = ReturnType<\n  typeof simulateContractQueryKey<config, abi, functionName, args, chainId>\n>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SwitchAccountErrorType,\n  type SwitchAccountParameters,\n  type SwitchAccountReturnType,\n  switchAccount,\n} from '../actions/switchAccount.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function switchAccountMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return switchAccount(config, variables)\n    },\n    mutationKey: ['switchAccount'],\n  } as const satisfies MutationOptions<\n    SwitchAccountData<config>,\n    SwitchAccountErrorType,\n    SwitchAccountVariables\n  >\n}\n\nexport type SwitchAccountData<config extends Config> = Compute<\n  SwitchAccountReturnType<config>\n>\n\nexport type SwitchAccountVariables = Compute<SwitchAccountParameters>\n\nexport type SwitchAccountMutate<\n  config extends Config,\n  context = unknown,\n> = Mutate<\n  SwitchAccountData<config>,\n  SwitchAccountErrorType,\n  SwitchAccountVariables,\n  context\n>\n\nexport type SwitchAccountMutateAsync<\n  config extends Config,\n  context = unknown,\n> = MutateAsync<\n  SwitchAccountData<config>,\n  SwitchAccountErrorType,\n  SwitchAccountVariables,\n  context\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SwitchChainErrorType,\n  type SwitchChainParameters,\n  type SwitchChainReturnType,\n  switchChain,\n} from '../actions/switchChain.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function switchChainMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return switchChain(config, variables)\n    },\n    mutationKey: ['switchChain'],\n  } as const satisfies MutationOptions<\n    SwitchChainData<config, config['chains'][number]['id']>,\n    SwitchChainErrorType,\n    SwitchChainVariables<config, config['chains'][number]['id']>\n  >\n}\n\nexport type SwitchChainData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<SwitchChainReturnType<config, chainId>>\n\nexport type SwitchChainVariables<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<SwitchChainParameters<config, chainId>>\n\nexport type SwitchChainMutate<config extends Config, context = unknown> = <\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SwitchChainVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SwitchChainData<config, chainId>,\n          SwitchChainErrorType,\n          Compute<SwitchChainVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type SwitchChainMutateAsync<config extends Config, context = unknown> = <\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SwitchChainVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SwitchChainData<config, chainId>,\n          SwitchChainErrorType,\n          Compute<SwitchChainVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<SwitchChainData<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type VerifyMessageErrorType,\n  type VerifyMessageParameters,\n  type VerifyMessageReturnType,\n  verifyMessage,\n} from '../actions/verifyMessage.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type VerifyMessageOptions<config extends Config> = Compute<\n  ExactPartial<VerifyMessageParameters<config>> & ScopeKeyParameter\n>\n\nexport function verifyMessageQueryOptions<config extends Config>(\n  config: config,\n  options: VerifyMessageOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, message, signature } = queryKey[1]\n      if (!address || !message || !signature)\n        throw new Error('address, message, and signature are required')\n\n      const { scopeKey: _, ...parameters } = queryKey[1]\n\n      const verified = await verifyMessage(\n        config,\n        parameters as VerifyMessageParameters,\n      )\n      return verified ?? null\n    },\n    queryKey: verifyMessageQueryKey(options),\n  } as const satisfies QueryOptions<\n    VerifyMessageQueryFnData,\n    VerifyMessageErrorType,\n    VerifyMessageData,\n    VerifyMessageQueryKey<config>\n  >\n}\nexport type VerifyMessageQueryFnData = VerifyMessageReturnType\n\nexport type VerifyMessageData = VerifyMessageQueryFnData\n\nexport function verifyMessageQueryKey<config extends Config>(\n  options: VerifyMessageOptions<config>,\n) {\n  return ['verifyMessage', filterQueryOptions(options)] as const\n}\n\nexport type VerifyMessageQueryKey<config extends Config> = ReturnType<\n  typeof verifyMessageQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { TypedData } from 'viem'\n\nimport {\n  type VerifyTypedDataErrorType,\n  type VerifyTypedDataParameters,\n  type VerifyTypedDataReturnType,\n  verifyTypedData,\n} from '../actions/verifyTypedData.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type VerifyTypedDataOptions<\n  typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n  config extends Config,\n> = ExactPartial<VerifyTypedDataParameters<typedData, primaryType, config>> &\n  ScopeKeyParameter\n\nexport function verifyTypedDataQueryOptions<\n  config extends Config,\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  config: config,\n  options: VerifyTypedDataOptions<typedData, primaryType, config> = {} as any,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const {\n        address,\n        message,\n        primaryType,\n        signature,\n        types,\n        scopeKey: _,\n        ...parameters\n      } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      if (!message) throw new Error('message is required')\n      if (!primaryType) throw new Error('primaryType is required')\n      if (!signature) throw new Error('signature is required')\n      if (!types) throw new Error('types is required')\n\n      const verified = await verifyTypedData(config, {\n        ...parameters,\n        address,\n        message,\n        primaryType,\n        signature,\n        types,\n      } as VerifyTypedDataParameters)\n      return verified ?? null\n    },\n    queryKey: verifyTypedDataQueryKey(options),\n  } as const satisfies QueryOptions<\n    VerifyTypedDataQueryFnData,\n    VerifyTypedDataErrorType,\n    VerifyTypedDataData,\n    VerifyTypedDataQueryKey<typedData, primaryType, config>\n  >\n}\n\nexport type VerifyTypedDataQueryFnData = VerifyTypedDataReturnType\n\nexport type VerifyTypedDataData = VerifyTypedDataQueryFnData\n\nexport function verifyTypedDataQueryKey<\n  config extends Config,\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(options: VerifyTypedDataOptions<typedData, primaryType, config>) {\n  return ['verifyTypedData', filterQueryOptions(options)] as const\n}\n\nexport type VerifyTypedDataQueryKey<\n  typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n  config extends Config,\n> = ReturnType<typeof verifyTypedDataQueryKey<config, typedData, primaryType>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type WaitForCallsStatusErrorType,\n  type WaitForCallsStatusParameters,\n  type WaitForCallsStatusReturnType,\n  waitForCallsStatus,\n} from '../actions/waitForCallsStatus.js'\nimport type { Config } from '../createConfig.js'\nimport { ConnectorNotConnectedError } from '../errors/config.js'\nimport { filterQueryOptions } from '../query/utils.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, PartialBy } from '../types/utils.js'\n\nexport type WaitForCallsStatusOptions = Compute<\n  PartialBy<WaitForCallsStatusParameters, 'id'> & ScopeKeyParameter\n>\n\nexport function waitForCallsStatusQueryOptions<config extends Config>(\n  config: config,\n  options: WaitForCallsStatusOptions,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, id, ...parameters } = queryKey[1]\n      if (!id) throw new Error('id is required')\n      const status = await waitForCallsStatus(config, { ...parameters, id })\n      return status\n    },\n    queryKey: waitForCallsStatusQueryKey(options),\n    retry(failureCount, error) {\n      if (error instanceof ConnectorNotConnectedError) return false\n      return failureCount < 3\n    },\n  } as const satisfies QueryOptions<\n    WaitForCallsStatusQueryFnData,\n    WaitForCallsStatusErrorType,\n    WaitForCallsStatusData,\n    WaitForCallsStatusQueryKey\n  >\n}\n\nexport type WaitForCallsStatusQueryFnData = WaitForCallsStatusReturnType\n\nexport type WaitForCallsStatusData = WaitForCallsStatusQueryFnData\n\nexport function waitForCallsStatusQueryKey(options: WaitForCallsStatusOptions) {\n  return ['callsStatus', filterQueryOptions(options)] as const\n}\n\nexport type WaitForCallsStatusQueryKey = ReturnType<\n  typeof waitForCallsStatusQueryKey\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type WaitForTransactionReceiptErrorType,\n  type WaitForTransactionReceiptParameters,\n  type WaitForTransactionReceiptReturnType,\n  waitForTransactionReceipt,\n} from '../actions/waitForTransactionReceipt.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type WaitForTransactionReceiptOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<WaitForTransactionReceiptParameters<config, chainId>> &\n    ScopeKeyParameter\n>\n\nexport function waitForTransactionReceiptQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  options: WaitForTransactionReceiptOptions<config, chainId> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { hash, ...parameters } = queryKey[1]\n      if (!hash) throw new Error('hash is required')\n      return waitForTransactionReceipt(config, {\n        ...parameters,\n        onReplaced: options.onReplaced,\n        hash,\n      }) as unknown as Promise<\n        WaitForTransactionReceiptReturnType<config, chainId>\n      >\n    },\n    queryKey: waitForTransactionReceiptQueryKey(options),\n  } as const satisfies QueryOptions<\n    WaitForTransactionReceiptQueryFnData<config, chainId>,\n    WaitForTransactionReceiptErrorType,\n    WaitForTransactionReceiptData<config, chainId>,\n    WaitForTransactionReceiptQueryKey<config, chainId>\n  >\n}\n\nexport type WaitForTransactionReceiptQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = WaitForTransactionReceiptReturnType<config, chainId>\n\nexport type WaitForTransactionReceiptData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = WaitForTransactionReceiptQueryFnData<config, chainId>\n\nexport function waitForTransactionReceiptQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: WaitForTransactionReceiptOptions<config, chainId> = {}) {\n  const { onReplaced: _, ...rest } = options\n  return ['waitForTransactionReceipt', filterQueryOptions(rest)] as const\n}\n\nexport type WaitForTransactionReceiptQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof waitForTransactionReceiptQueryKey<config, chainId>>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type WatchAssetErrorType,\n  type WatchAssetParameters,\n  type WatchAssetReturnType,\n  watchAsset,\n} from '../actions/watchAsset.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function watchAssetMutationOptions(config: Config) {\n  return {\n    mutationFn(variables) {\n      return watchAsset(config, variables)\n    },\n    mutationKey: ['watchAsset'],\n  } as const satisfies MutationOptions<\n    WatchAssetData,\n    WatchAssetErrorType,\n    WatchAssetVariables\n  >\n}\n\nexport type WatchAssetData = WatchAssetReturnType\n\nexport type WatchAssetVariables = Compute<WatchAssetParameters>\n\nexport type WatchAssetMutate<context = unknown> = Mutate<\n  WatchAssetData,\n  WatchAssetErrorType,\n  WatchAssetVariables,\n  context\n>\n\nexport type WatchAssetMutateAsync<context = unknown> = MutateAsync<\n  WatchAssetData,\n  WatchAssetErrorType,\n  WatchAssetVariables,\n  context\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\n\nimport {\n  type WriteContractErrorType,\n  type WriteContractParameters,\n  type WriteContractReturnType,\n  writeContract,\n} from '../actions/writeContract.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function writeContractMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return writeContract(config, variables)\n    },\n    mutationKey: ['writeContract'],\n  } as const satisfies MutationOptions<\n    WriteContractData,\n    WriteContractErrorType,\n    WriteContractVariables<\n      Abi,\n      string,\n      readonly unknown[],\n      config,\n      config['chains'][number]['id']\n    >\n  >\n}\n\nexport type WriteContractData = Compute<WriteContractReturnType>\n\nexport type WriteContractVariables<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  ///\n  allFunctionNames = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n> = WriteContractParameters<\n  abi,\n  functionName,\n  args,\n  config,\n  chainId,\n  allFunctionNames\n>\n\nexport type WriteContractMutate<config extends Config, context = unknown> = <\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: WriteContractVariables<abi, functionName, args, config, chainId>,\n  options?:\n    | MutateOptions<\n        WriteContractData,\n        WriteContractErrorType,\n        WriteContractVariables<\n          abi,\n          functionName,\n          args,\n          config,\n          chainId,\n          // use `functionName` to make sure it's not union of all possible function names\n          functionName\n        >,\n        context\n      >\n    | undefined,\n) => void\n\nexport type WriteContractMutateAsync<\n  config extends Config,\n  context = unknown,\n> = <\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: WriteContractVariables<abi, functionName, args, config, chainId>,\n  options?:\n    | MutateOptions<\n        WriteContractData,\n        WriteContractErrorType,\n        WriteContractVariables<\n          abi,\n          functionName,\n          args,\n          config,\n          chainId,\n          // use `functionName` to make sure it's not union of all possible function names\n          functionName\n        >,\n        context\n      >\n    | undefined,\n) => Promise<WriteContractData>\n", "import {\n  type DefaultError,\n  type QueryKey,\n  useInfiniteQuery as tanstack_useInfiniteQuery,\n  useQuery as tanstack_useQuery,\n  type UseInfiniteQueryOptions,\n  type UseInfiniteQueryResult,\n  type UseMutationOptions,\n  type UseMutationResult,\n  type UseQueryOptions,\n  type UseQueryResult,\n  useMutation,\n} from '@tanstack/react-query'\nimport type {\n  Compute,\n  ExactPartial,\n  Omit,\n  UnionStrictOmit,\n} from '@wagmi/core/internal'\nimport { hashFn } from '@wagmi/core/query'\n\nexport type UseMutationParameters<\n  data = unknown,\n  error = Error,\n  variables = void,\n  context = unknown,\n> = Compute<\n  Omit<\n    UseMutationOptions<data, error, Compute<variables>, context>,\n    'mutationFn' | 'mutationKey' | 'throwOnError'\n  >\n>\n\nexport type UseMutationReturnType<\n  data = unknown,\n  error = Error,\n  variables = void,\n  context = unknown,\n> = Compute<\n  UnionStrictOmit<\n    UseMutationResult<data, error, variables, context>,\n    'mutate' | 'mutateAsync'\n  >\n>\n\nexport { useMutation }\n\n////////////////////////////////////////////////////////////////////////////////\n\nexport type UseQueryParameters<\n  queryFnData = unknown,\n  error = DefaultError,\n  data = queryFnData,\n  queryKey extends QueryKey = QueryKey,\n> = Compute<\n  ExactPartial<\n    Omit<UseQueryOptions<queryFnData, error, data, queryKey>, 'initialData'>\n  > & {\n    // Fix `initialData` type\n    initialData?:\n      | UseQueryOptions<queryFnData, error, data, queryKey>['initialData']\n      | undefined\n  }\n>\n\nexport type UseQueryReturnType<data = unknown, error = DefaultError> = Compute<\n  UseQueryResult<data, error> & {\n    queryKey: QueryKey\n  }\n>\n\n// Adding some basic customization.\n// Ideally we don't have this function, but `import('@tanstack/react-query').useQuery` currently has some quirks where it is super hard to\n// pass down the inferred `initialData` type because of it's discriminated overload in the on `useQuery`.\nexport function useQuery<queryFnData, error, data, queryKey extends QueryKey>(\n  parameters: UseQueryParameters<queryFnData, error, data, queryKey> & {\n    queryKey: QueryKey\n  },\n): UseQueryReturnType<data, error> {\n  const result = tanstack_useQuery({\n    ...(parameters as any),\n    queryKeyHashFn: hashFn, // for bigint support\n  }) as UseQueryReturnType<data, error>\n  result.queryKey = parameters.queryKey\n  return result\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nexport type UseInfiniteQueryParameters<\n  queryFnData = unknown,\n  error = DefaultError,\n  data = queryFnData,\n  queryData = queryFnData,\n  queryKey extends QueryKey = QueryKey,\n  pageParam = unknown,\n> = Compute<\n  Omit<\n    UseInfiniteQueryOptions<\n      queryFnData,\n      error,\n      data,\n      queryData,\n      queryKey,\n      pageParam\n    >,\n    'initialData'\n  > & {\n    // Fix `initialData` type\n    initialData?:\n      | UseInfiniteQueryOptions<\n          queryFnData,\n          error,\n          data,\n          queryKey\n        >['initialData']\n      | undefined\n  }\n>\n\nexport type UseInfiniteQueryReturnType<\n  data = unknown,\n  error = DefaultError,\n> = UseInfiniteQueryResult<data, error> & {\n  queryKey: QueryKey\n}\n\n// Adding some basic customization.\nexport function useInfiniteQuery<\n  queryFnData,\n  error,\n  data,\n  queryKey extends QueryKey,\n>(\n  parameters: UseInfiniteQueryParameters<queryFnData, error, data, queryKey> & {\n    queryKey: QueryKey\n  },\n): UseInfiniteQueryReturnType<data, error> {\n  const result = tanstack_useInfiniteQuery({\n    ...(parameters as any),\n    queryKeyHashFn: hashFn, // for bigint support\n  }) as UseInfiniteQueryReturnType<data, error>\n  result.queryKey = parameters.queryKey\n  return result\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetChainIdReturnType,\n  getChainId,\n  type ResolvedRegister,\n  watchChainId,\n} from '@wagmi/core'\nimport { useSyncExternalStore } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseChainIdParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseChainIdReturnType<config extends Config = Config> =\n  GetChainIdReturnType<config>\n\n/** https://wagmi.sh/react/api/hooks/useChainId */\nexport function useChainId<config extends Config = ResolvedRegister['config']>(\n  parameters: UseChainIdParameters<config> = {},\n): UseChainIdReturnType<config> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStore(\n    (onChange) => watchChainId(config, { onChange }),\n    () => getChainId(config),\n    () => getChainId(config),\n  )\n}\n", "'use client'\n\nimport type { Config, GetBalanceErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { GetBalanceQueryFnData } from '@wagmi/core/query'\nimport {\n  type GetBalanceData,\n  type GetBalanceOptions,\n  type GetBalanceQueryKey,\n  getBalanceQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseBalanceParameters<\n  config extends Config = Config,\n  selectData = GetBalanceData,\n> = Compute<\n  GetBalanceOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBalanceQueryFnData,\n      GetBalanceErrorType,\n      selectData,\n      GetBalanceQueryKey<config>\n    >\n>\n\nexport type UseBalanceReturnType<selectData = GetBalanceData> =\n  UseQueryReturnType<selectData, GetBalanceErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useBalance */\nexport function useBalance<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetBalanceData,\n>(\n  parameters: UseBalanceParameters<config, selectData> = {},\n): UseBalanceReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getBalanceQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport {\n  type Config,\n  type ResolvedRegister,\n  type WatchBlocksParameters,\n  watchBlocks,\n} from '@wagmi/core'\nimport type { UnionCompute, UnionExactPartial } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\nimport type { BlockTag } from 'viem'\n\nimport type { ConfigParameter, EnabledParameter } from '../types/properties.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchBlocksParameters<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  UnionExactPartial<\n    WatchBlocksParameters<includeTransactions, blockTag, config, chainId>\n  > &\n    ConfigParameter<config> &\n    EnabledParameter\n>\n\nexport type UseWatchBlocksReturnType = void\n\n/** https://wagmi.sh/react/hooks/useWatchBlocks */\nexport function useWatchBlocks<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n>(\n  parameters: UseWatchBlocksParameters<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId\n  > = {} as any,\n): UseWatchBlocksReturnType {\n  const { enabled = true, onBlock, config: _, ...rest } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  // TODO(react@19): cleanup\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `rest` changes every render so only including properties in dependency array\n  useEffect(() => {\n    if (!enabled) return\n    if (!onBlock) return\n    return watchBlocks(config, {\n      ...(rest as any),\n      chainId,\n      onBlock,\n    })\n  }, [\n    chainId,\n    config,\n    enabled,\n    onBlock,\n    ///\n    rest.blockTag,\n    rest.emitMissed,\n    rest.emitOnBegin,\n    rest.includeTransactions,\n    rest.onError,\n    rest.poll,\n    rest.pollingInterval,\n    rest.syncConnectedChain,\n  ])\n}\n", "'use client'\n\nimport { useQueryClient } from '@tanstack/react-query'\nimport type { Config, GetBlockErrorType, ResolvedRegister } from '@wagmi/core'\nimport type {\n  Compute,\n  UnionCompute,\n  UnionStrictOmit,\n} from '@wagmi/core/internal'\nimport {\n  type GetBlockData,\n  type GetBlockOptions,\n  type GetBlockQueryFnData,\n  type GetBlockQueryKey,\n  getBlockQueryOptions,\n} from '@wagmi/core/query'\nimport type { BlockTag } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\nimport {\n  type UseWatchBlocksParameters,\n  useWatchBlocks,\n} from './useWatchBlocks.js'\n\nexport type UseBlockParameters<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockData<includeTransactions, blockTag, config, chainId>,\n> = Compute<\n  GetBlockOptions<includeTransactions, blockTag, config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBlockQueryFnData<includeTransactions, blockTag, config, chainId>,\n      GetBlockErrorType,\n      selectData,\n      GetBlockQueryKey<includeTransactions, blockTag, config, chainId>\n    > & {\n      watch?:\n        | boolean\n        | UnionCompute<\n            UnionStrictOmit<\n              UseWatchBlocksParameters<\n                includeTransactions,\n                blockTag,\n                config,\n                chainId\n              >,\n              'chainId' | 'config' | 'onBlock' | 'onError'\n            >\n          >\n        | undefined\n    }\n>\n\nexport type UseBlockReturnType<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockData<includeTransactions, blockTag, config, chainId>,\n> = UseQueryReturnType<selectData, GetBlockErrorType>\n\n/** https://wagmi.sh/react/hooks/useBlock */\nexport function useBlock<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockData<includeTransactions, blockTag, config, chainId>,\n>(\n  parameters: UseBlockParameters<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId,\n    selectData\n  > = {},\n): UseBlockReturnType<\n  includeTransactions,\n  blockTag,\n  config,\n  chainId,\n  selectData\n> {\n  const { query = {}, watch } = parameters\n\n  const config = useConfig(parameters)\n  const queryClient = useQueryClient()\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  const options = getBlockQueryOptions(config, {\n    ...parameters,\n    chainId,\n  })\n  const enabled = Boolean(query.enabled ?? true)\n\n  useWatchBlocks({\n    ...({\n      config: parameters.config,\n      chainId: parameters.chainId!,\n      ...(typeof watch === 'object' ? watch : {}),\n    } as UseWatchBlocksParameters),\n    enabled: Boolean(\n      enabled && (typeof watch === 'object' ? watch.enabled : watch),\n    ),\n    onBlock(block) {\n      queryClient.setQueryData(options.queryKey, block)\n    },\n  })\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UseBlockReturnType<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId,\n    selectData\n  >\n}\n", "'use client'\n\nimport {\n  type Config,\n  type ResolvedRegister,\n  type WatchBlockNumberParameters,\n  watchBlockNumber,\n} from '@wagmi/core'\nimport type { UnionCompute, UnionExactPartial } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\n\nimport type { ConfigParameter, EnabledParameter } from '../types/properties.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchBlockNumberParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  UnionExactPartial<WatchBlockNumberParameters<config, chainId>> &\n    ConfigParameter<config> &\n    EnabledParameter\n>\n\nexport type UseWatchBlockNumberReturnType = void\n\n/** https://wagmi.sh/react/api/hooks/useWatchBlockNumber */\nexport function useWatchBlockNumber<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  parameters: UseWatchBlockNumberParameters<config, chainId> = {} as any,\n): UseWatchBlockNumberReturnType {\n  const { enabled = true, onBlockNumber, config: _, ...rest } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  // TODO(react@19): cleanup\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `rest` changes every render so only including properties in dependency array\n  useEffect(() => {\n    if (!enabled) return\n    if (!onBlockNumber) return\n    return watchBlockNumber(config, {\n      ...(rest as any),\n      chainId,\n      onBlockNumber,\n    })\n  }, [\n    chainId,\n    config,\n    enabled,\n    onBlockNumber,\n    ///\n    rest.onError,\n    rest.emitMissed,\n    rest.emitOnBegin,\n    rest.poll,\n    rest.pollingInterval,\n    rest.syncConnectedChain,\n  ])\n}\n", "'use client'\n\nimport { useQueryClient } from '@tanstack/react-query'\nimport type {\n  Config,\n  GetBlockNumberErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type {\n  Compute,\n  UnionCompute,\n  UnionStrictOmit,\n} from '@wagmi/core/internal'\nimport {\n  type GetBlockNumberData,\n  type GetBlockNumberOptions,\n  type GetBlockNumberQueryFnData,\n  type GetBlockNumberQueryKey,\n  getBlockNumberQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\nimport {\n  type UseWatchBlockNumberParameters,\n  useWatchBlockNumber,\n} from './useWatchBlockNumber.js'\n\nexport type UseBlockNumberParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockNumberData,\n> = Compute<\n  GetBlockNumberOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBlockNumberQueryFnData,\n      GetBlockNumberErrorType,\n      selectData,\n      GetBlockNumberQueryKey<config, chainId>\n    > & {\n      watch?:\n        | boolean\n        | UnionCompute<\n            UnionStrictOmit<\n              UseWatchBlockNumberParameters<config, chainId>,\n              'chainId' | 'config' | 'onBlockNumber' | 'onError'\n            >\n          >\n        | undefined\n    }\n>\n\nexport type UseBlockNumberReturnType<selectData = GetBlockNumberData> =\n  UseQueryReturnType<selectData, GetBlockNumberErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useBlockNumber */\nexport function useBlockNumber<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockNumberData,\n>(\n  parameters: UseBlockNumberParameters<config, chainId, selectData> = {},\n): UseBlockNumberReturnType<selectData> {\n  const { query = {}, watch } = parameters\n\n  const config = useConfig(parameters)\n  const queryClient = useQueryClient()\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  const options = getBlockNumberQueryOptions(config, {\n    ...parameters,\n    chainId,\n  })\n\n  useWatchBlockNumber({\n    ...({\n      config: parameters.config,\n      chainId: parameters.chainId,\n      ...(typeof watch === 'object' ? watch : {}),\n    } as UseWatchBlockNumberParameters),\n    enabled: Boolean(\n      (query.enabled ?? true) &&\n        (typeof watch === 'object' ? watch.enabled : watch),\n    ),\n    onBlockNumber(blockNumber) {\n      queryClient.setQueryData(options.queryKey, blockNumber)\n    },\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetBlockTransactionCountErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { UnionCompute } from '@wagmi/core/internal'\nimport {\n  type GetBlockTransactionCountData,\n  type GetBlockTransactionCountOptions,\n  type GetBlockTransactionCountQueryFnData,\n  type GetBlockTransactionCountQueryKey,\n  getBlockTransactionCountQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseBlockTransactionCountParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockTransactionCountData,\n> = UnionCompute<\n  GetBlockTransactionCountOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBlockTransactionCountQueryFnData,\n      GetBlockTransactionCountErrorType,\n      selectData,\n      GetBlockTransactionCountQueryKey<config, chainId>\n    >\n>\n\nexport type UseBlockTransactionCountReturnType<\n  selectData = GetBlockTransactionCountData,\n> = UseQueryReturnType<selectData, GetBlockTransactionCountErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useBlockTransactionCount */\nexport function useBlockTransactionCount<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockTransactionCountData,\n>(\n  parameters: UseBlockTransactionCountParameters<\n    config,\n    chainId,\n    selectData\n  > = {},\n): UseBlockTransactionCountReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  const options = getBlockTransactionCountQueryOptions(config, {\n    ...parameters,\n    chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetBytecodeErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { GetBytecodeQueryFnData } from '@wagmi/core/query'\nimport {\n  type GetBytecodeData,\n  type GetBytecodeOptions,\n  type GetBytecodeQueryKey,\n  getBytecodeQueryOptions,\n} from '@wagmi/core/query'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseBytecodeParameters<\n  config extends Config = Config,\n  selectData = GetBytecodeData,\n> = Compute<\n  GetBytecodeOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBytecodeQueryFnData,\n      GetBytecodeErrorType,\n      selectData,\n      GetBytecodeQueryKey<config>\n    >\n>\n\nexport type UseBytecodeReturnType<selectData = GetBytecodeData> =\n  UseQueryReturnType<selectData, GetBytecodeErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useBytecode */\nexport function useBytecode<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetBytecodeData,\n>(\n  parameters: UseBytecodeParameters<config, selectData> = {},\n): UseBytecodeReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getBytecodeQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type { CallErrorType, Config, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { CallQueryFnData } from '@wagmi/core/query'\nimport {\n  type CallData,\n  type CallOptions,\n  type CallQueryKey,\n  callQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseCallParameters<\n  config extends Config = Config,\n  selectData = CallData,\n> = Compute<\n  CallOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      CallQueryFnData,\n      CallErrorType,\n      selectData,\n      CallQueryKey<config>\n    >\n>\n\nexport type UseCallReturnType<selectData = CallData> = UseQueryReturnType<\n  selectData,\n  CallErrorType\n>\n\n/** https://wagmi.sh/react/api/hooks/useCall */\nexport function useCall<\n  config extends Config = ResolvedRegister['config'],\n  selectData = CallData,\n>(\n  parameters: UseCallParameters<config, selectData> = {},\n): UseCallReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = callQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetCallsStatusErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetCallsStatusData,\n  type GetCallsStatusOptions,\n  type GetCallsStatusQueryFnData,\n  type GetCallsStatusQueryKey,\n  getCallsStatusQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseCallsStatusParameters<\n  config extends Config = Config,\n  selectData = GetCallsStatusData,\n> = Compute<\n  GetCallsStatusOptions &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetCallsStatusQueryFnData,\n      GetCallsStatusErrorType,\n      selectData,\n      GetCallsStatusQueryKey\n    >\n>\n\nexport type UseCallsStatusReturnType<selectData = GetCallsStatusData> =\n  UseQueryReturnType<selectData, GetCallsStatusErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useCallsStatus */\nexport function useCallsStatus<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetCallsStatusData,\n>(\n  parameters: UseCallsStatusParameters<config, selectData>,\n): UseCallsStatusReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n\n  const options = getCallsStatusQueryOptions(config, parameters)\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetCapabilitiesErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetCapabilitiesData,\n  type GetCapabilitiesOptions,\n  type GetCapabilitiesQueryFnData,\n  type GetCapabilitiesQueryKey,\n  getCapabilitiesQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useAccount } from './useAccount.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseCapabilitiesParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetCapabilitiesData<config, chainId>,\n> = Compute<\n  GetCapabilitiesOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetCapabilitiesQueryFnData<config, chainId>,\n      GetCapabilitiesErrorType,\n      selectData,\n      GetCapabilitiesQueryKey<config, chainId>\n    >\n>\n\nexport type UseCapabilitiesReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetCapabilitiesData<config, chainId>,\n> = UseQueryReturnType<selectData, GetCapabilitiesErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useCapabilities */\nexport function useCapabilities<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetCapabilitiesData<config, chainId>,\n>(\n  parameters: UseCapabilitiesParameters<config, chainId, selectData> = {},\n): UseCapabilitiesReturnType<config, chainId, selectData> {\n  const { account, query = {} } = parameters\n\n  const { address } = useAccount()\n  const config = useConfig(parameters)\n\n  const options = getCapabilitiesQueryOptions(config, {\n    ...parameters,\n    account: account ?? address,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetChainsReturnType,\n  getChains,\n  type ResolvedRegister,\n} from '@wagmi/core'\nimport { watchChains } from '@wagmi/core/internal'\nimport { useSyncExternalStore } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseChainsParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseChainsReturnType<config extends Config = Config> =\n  GetChainsReturnType<config>\n\n/** https://wagmi.sh/react/api/hooks/useChains */\nexport function useChains<config extends Config = ResolvedRegister['config']>(\n  parameters: UseChainsParameters<config> = {},\n): UseChainsReturnType<config> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStore(\n    (onChange) => watchChains(config, { onChange }),\n    () => getChains(config),\n    () => getChains(config),\n  )\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetClientParameters,\n  type GetClientReturnType,\n  getClient,\n  type ResolvedRegister,\n  watchClient,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseClientParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n> = Compute<GetClientParameters<config, chainId> & ConfigParameter<config>>\n\nexport type UseClientReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n> = GetClientReturnType<config, chainId>\n\n/** https://wagmi.sh/react/api/hooks/useClient */\nexport function useClient<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n>(\n  parameters: UseClientParameters<config, chainId> = {},\n): UseClientReturnType<config, chainId> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStoreWithSelector(\n    (onChange) => watchClient(config, { onChange }),\n    () => getClient(config, parameters),\n    () => getClient(config, parameters),\n    (x) => x,\n    (a, b) => a?.uid === b?.uid,\n  ) as any\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { Config, ConnectErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type ConnectData,\n  type ConnectMutate,\n  type ConnectMutateAsync,\n  type ConnectVariables,\n  connectMutationOptions,\n} from '@wagmi/core/query'\nimport { useEffect } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\nimport { type UseConnectorsReturnType, useConnectors } from './useConnectors.js'\n\nexport type UseConnectParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          ConnectData<config>,\n          ConnectErrorType,\n          ConnectVariables<config, config['connectors'][number]>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseConnectReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    ConnectData<config>,\n    ConnectErrorType,\n    ConnectVariables<config, config['connectors'][number]>,\n    context\n  > & {\n    connect: ConnectMutate<config, context>\n    connectAsync: ConnectMutateAsync<config, context>\n    connectors: Compute<UseConnectorsReturnType> | config['connectors']\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useConnect */\nexport function useConnect<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseConnectParameters<config, context> = {},\n): UseConnectReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = connectMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  // Reset mutation back to an idle state when the connector disconnects.\n  useEffect(() => {\n    return config.subscribe(\n      ({ status }) => status,\n      (status, previousStatus) => {\n        if (previousStatus === 'connected' && status === 'disconnected')\n          result.reset()\n      },\n    )\n  }, [config, result.reset])\n\n  type Return = UseConnectReturnType<config, context>\n  return {\n    ...(result as Return),\n    connect: mutate as Return['connect'],\n    connectAsync: mutateAsync as Return['connectAsync'],\n    connectors: useConnectors({ config }),\n  }\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetConnectorsReturnType,\n  getConnectors,\n  type ResolvedRegister,\n  watchConnectors,\n} from '@wagmi/core'\nimport { useSyncExternalStore } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseConnectorsParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseConnectorsReturnType<config extends Config = Config> =\n  GetConnectorsReturnType<config>\n\n/** https://wagmi.sh/react/api/hooks/useConnectors */\nexport function useConnectors<\n  config extends Config = ResolvedRegister['config'],\n>(\n  parameters: UseConnectorsParameters<config> = {},\n): UseConnectorsReturnType<config> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStore(\n    (onChange) => watchConnectors(config, { onChange }),\n    () => getConnectors(config),\n    () => getConnectors(config),\n  )\n}\n", "'use client'\n\nimport {\n  type GetConnectionsReturnType,\n  getConnections,\n  watchConnections,\n} from '@wagmi/core'\nimport { useSyncExternalStore } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseConnectionsParameters = ConfigParameter\n\nexport type UseConnectionsReturnType = GetConnectionsReturnType\n\n/** https://wagmi.sh/react/api/hooks/useConnections */\nexport function useConnections(\n  parameters: UseConnectionsParameters = {},\n): UseConnectionsReturnType {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStore(\n    (onChange) => watchConnections(config, { onChange }),\n    () => getConnections(config),\n    () => getConnections(config),\n  )\n}\n", "'use client'\n\nimport { useQueryClient } from '@tanstack/react-query'\nimport type {\n  Config,\n  GetConnectorClientErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute, Omit } from '@wagmi/core/internal'\nimport {\n  type GetConnectorClientData,\n  type GetConnectorClientOptions,\n  type GetConnectorClientQueryFnData,\n  type GetConnectorClientQueryKey,\n  getConnectorClientQueryOptions,\n} from '@wagmi/core/query'\nimport { useEffect, useRef } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport {\n  type UseQueryParameters,\n  type UseQueryReturnType,\n  useQuery,\n} from '../utils/query.js'\nimport { useAccount } from './useAccount.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseConnectorClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetConnectorClientData<config, chainId>,\n> = Compute<\n  GetConnectorClientOptions<config, chainId> &\n    ConfigParameter<config> & {\n      query?:\n        | Compute<\n            Omit<\n              UseQueryParameters<\n                GetConnectorClientQueryFnData<config, chainId>,\n                GetConnectorClientErrorType,\n                selectData,\n                GetConnectorClientQueryKey<config, chainId>\n              >,\n              'gcTime' | 'staleTime'\n            >\n          >\n        | undefined\n    }\n>\n\nexport type UseConnectorClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetConnectorClientData<config, chainId>,\n> = UseQueryReturnType<selectData, GetConnectorClientErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useConnectorClient */\nexport function useConnectorClient<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetConnectorClientData<config, chainId>,\n>(\n  parameters: UseConnectorClientParameters<config, chainId, selectData> = {},\n): UseConnectorClientReturnType<config, chainId, selectData> {\n  const { query = {}, ...rest } = parameters\n\n  const config = useConfig(rest)\n  const queryClient = useQueryClient()\n  const { address, connector, status } = useAccount({ config })\n  const chainId = useChainId({ config })\n  const activeConnector = parameters.connector ?? connector\n\n  const { queryKey, ...options } = getConnectorClientQueryOptions<\n    config,\n    chainId\n  >(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n    connector: activeConnector,\n  })\n  const enabled = Boolean(\n    (status === 'connected' ||\n      (status === 'reconnecting' && activeConnector?.getProvider)) &&\n      (query.enabled ?? true),\n  )\n\n  const addressRef = useRef(address)\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `queryKey` not required\n  useEffect(() => {\n    const previousAddress = addressRef.current\n    if (!address && previousAddress) {\n      // remove when account is disconnected\n      queryClient.removeQueries({ queryKey })\n      addressRef.current = undefined\n    } else if (address !== previousAddress) {\n      // invalidate when address changes\n      queryClient.invalidateQueries({ queryKey })\n      addressRef.current = address\n    }\n  }, [address, queryClient])\n\n  return useQuery({\n    ...query,\n    ...options,\n    queryKey,\n    enabled,\n    staleTime: Number.POSITIVE_INFINITY,\n  })\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  DeployContractErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type DeployContractData,\n  type DeployContractMutate,\n  type DeployContractMutateAsync,\n  type DeployContractVariables,\n  deployContractMutationOptions,\n} from '@wagmi/core/query'\nimport type { Abi } from 'viem'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseDeployContractParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          DeployContractData,\n          DeployContractErrorType,\n          DeployContractVariables<Abi, config, config['chains'][number]['id']>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseDeployContractReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = UseMutationReturnType<\n  DeployContractData,\n  DeployContractErrorType,\n  DeployContractVariables<Abi, config, config['chains'][number]['id']>,\n  context\n> & {\n  deployContract: DeployContractMutate<config, context>\n  deployContractAsync: DeployContractMutateAsync<config, context>\n}\n\n/** https://wagmi.sh/react/api/hooks/useDeployContract */\nexport function useDeployContract<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseDeployContractParameters<config, context> = {},\n): UseDeployContractReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = deployContractMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseDeployContractReturnType<config, context>\n  return {\n    ...result,\n    deployContract: mutate as Return['deployContract'],\n    deployContractAsync: mutateAsync as Return['deployContractAsync'],\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { Connector, DisconnectErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type DisconnectData,\n  type DisconnectMutate,\n  type DisconnectMutateAsync,\n  type DisconnectVariables,\n  disconnectMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\nimport { useConnections } from './useConnections.js'\n\nexport type UseDisconnectParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          DisconnectData,\n          DisconnectErrorType,\n          DisconnectVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseDisconnectReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    DisconnectData,\n    DisconnectErrorType,\n    DisconnectVariables,\n    context\n  > & {\n    connectors: readonly Connector[]\n    disconnect: DisconnectMutate<context>\n    disconnectAsync: DisconnectMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useDisconnect */\nexport function useDisconnect<context = unknown>(\n  parameters: UseDisconnectParameters<context> = {},\n): UseDisconnectReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = disconnectMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    connectors: useConnections({ config }).map(\n      (connection) => connection.connector,\n    ),\n    disconnect: mutate,\n    disconnectAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetEnsAddressErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsAddressData,\n  type GetEnsAddressOptions,\n  type GetEnsAddressQueryFnData,\n  type GetEnsAddressQueryKey,\n  getEnsAddressQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsAddressParameters<\n  config extends Config = Config,\n  selectData = GetEnsAddressData,\n> = Compute<\n  GetEnsAddressOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsAddressQueryFnData,\n      GetEnsAddressErrorType,\n      selectData,\n      GetEnsAddressQueryKey<config>\n    >\n>\n\nexport type UseEnsAddressReturnType<selectData = GetEnsAddressData> =\n  UseQueryReturnType<selectData, GetEnsAddressErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsAddress */\nexport function useEnsAddress<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsAddressData,\n>(\n  parameters: UseEnsAddressParameters<config, selectData> = {},\n): UseEnsAddressReturnType<selectData> {\n  const { name, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsAddressQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(name && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetEnsAvatarErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsAvatarData,\n  type GetEnsAvatarOptions,\n  type GetEnsAvatarQueryFnData,\n  type GetEnsAvatarQueryKey,\n  getEnsAvatarQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsAvatarParameters<\n  config extends Config = Config,\n  selectData = GetEnsAvatarData,\n> = Compute<\n  GetEnsAvatarOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsAvatarQueryFnData,\n      GetEnsAvatarErrorType,\n      selectData,\n      GetEnsAvatarQueryKey<config>\n    >\n>\n\nexport type UseEnsAvatarReturnType<selectData = GetEnsAvatarData> =\n  UseQueryReturnType<selectData, GetEnsAvatarErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsAvatar */\nexport function useEnsAvatar<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsAvatarData,\n>(\n  parameters: UseEnsAvatarParameters<config, selectData> = {},\n): UseEnsAvatarReturnType<selectData> {\n  const { name, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsAvatarQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(name && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type { Config, GetEnsNameErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsNameData,\n  type GetEnsNameOptions,\n  type GetEnsNameQueryFnData,\n  type GetEnsNameQueryKey,\n  getEnsNameQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsNameParameters<\n  config extends Config = Config,\n  selectData = GetEnsNameData,\n> = Compute<\n  GetEnsNameOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsNameQueryFnData,\n      GetEnsNameErrorType,\n      selectData,\n      GetEnsNameQueryKey<config>\n    >\n>\n\nexport type UseEnsNameReturnType<selectData = GetEnsNameData> =\n  UseQueryReturnType<selectData, GetEnsNameErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsName */\nexport function useEnsName<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsNameData,\n>(\n  parameters: UseEnsNameParameters<config, selectData> = {},\n): UseEnsNameReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsNameQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetEnsResolverErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsResolverData,\n  type GetEnsResolverOptions,\n  type GetEnsResolverQueryFnData,\n  type GetEnsResolverQueryKey,\n  getEnsResolverQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsResolverParameters<\n  config extends Config = Config,\n  selectData = GetEnsResolverData,\n> = Compute<\n  GetEnsResolverOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsResolverQueryFnData,\n      GetEnsResolverErrorType,\n      selectData,\n      GetEnsResolverQueryKey<config>\n    >\n>\n\nexport type UseEnsResolverReturnType<selectData = GetEnsResolverData> =\n  UseQueryReturnType<selectData, GetEnsResolverErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsResolver */\nexport function useEnsResolver<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsResolverData,\n>(\n  parameters: UseEnsResolverParameters<config, selectData> = {},\n): UseEnsResolverReturnType<selectData> {\n  const { name, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsResolverQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(name && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type { Config, GetEnsTextErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsTextData,\n  type GetEnsTextOptions,\n  type GetEnsTextQueryFnData,\n  type GetEnsTextQueryKey,\n  getEnsTextQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsTextParameters<\n  config extends Config = Config,\n  selectData = GetEnsTextData,\n> = Compute<\n  GetEnsTextOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsTextQueryFnData,\n      GetEnsTextErrorType,\n      selectData,\n      GetEnsTextQueryKey<config>\n    >\n>\n\nexport type UseEnsTextReturnType<selectData = GetEnsTextData> =\n  UseQueryReturnType<selectData, GetEnsTextErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsText */\nexport function useEnsText<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsTextData,\n>(\n  parameters: UseEnsTextParameters<config, selectData> = {},\n): UseEnsTextReturnType<selectData> {\n  const { key, name, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsTextQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(key && name && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  EstimateFeesPerGasErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type EstimateFeesPerGasData,\n  type EstimateFeesPerGasOptions,\n  type EstimateFeesPerGasQueryFnData,\n  type EstimateFeesPerGasQueryKey,\n  estimateFeesPerGasQueryOptions,\n} from '@wagmi/core/query'\nimport type { FeeValuesType } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEstimateFeesPerGasParameters<\n  type extends FeeValuesType = FeeValuesType,\n  config extends Config = Config,\n  selectData = EstimateFeesPerGasData<type>,\n> = Compute<\n  EstimateFeesPerGasOptions<type, config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      EstimateFeesPerGasQueryFnData<type>,\n      EstimateFeesPerGasErrorType,\n      selectData,\n      EstimateFeesPerGasQueryKey<config, type>\n    >\n>\n\nexport type UseEstimateFeesPerGasReturnType<\n  type extends FeeValuesType = FeeValuesType,\n  selectData = EstimateFeesPerGasData<type>,\n> = UseQueryReturnType<selectData, EstimateFeesPerGasErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEstimateFeesPerGas */\nexport function useEstimateFeesPerGas<\n  config extends Config = ResolvedRegister['config'],\n  type extends FeeValuesType = 'eip1559',\n  selectData = EstimateFeesPerGasData<type>,\n>(\n  parameters: UseEstimateFeesPerGasParameters<type, config, selectData> = {},\n): UseEstimateFeesPerGasReturnType<type, selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = estimateFeesPerGasQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  EstimateGasErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport {\n  type EstimateGasData,\n  type EstimateGasOptions,\n  type EstimateGasQueryFnData,\n  type EstimateGasQueryKey,\n  estimateGasQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\nimport { useConnectorClient } from './useConnectorClient.js'\n\nexport type UseEstimateGasParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = EstimateGasData,\n> = EstimateGasOptions<config, chainId> &\n  ConfigParameter<config> &\n  QueryParameter<\n    EstimateGasQueryFnData,\n    EstimateGasErrorType,\n    selectData,\n    EstimateGasQueryKey<config, chainId>\n  >\n\nexport type UseEstimateGasReturnType<selectData = EstimateGasData> =\n  UseQueryReturnType<selectData, EstimateGasErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEstimateGas */\nexport function useEstimateGas<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = EstimateGasData,\n>(\n  parameters?: UseEstimateGasParameters<config, chainId, selectData>,\n): UseEstimateGasReturnType<selectData>\n\nexport function useEstimateGas(\n  parameters: UseEstimateGasParameters = {},\n): UseEstimateGasReturnType {\n  const { connector, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const { data: connectorClient } = useConnectorClient({\n    config,\n    connector,\n    query: { enabled: parameters.account === undefined },\n  })\n  const account = parameters.account ?? connectorClient?.account\n  const chainId = useChainId({ config })\n\n  const options = estimateGasQueryOptions(config, {\n    ...parameters,\n    account,\n    chainId: parameters.chainId ?? chainId,\n    connector,\n  })\n  const enabled = Boolean((account || connector) && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  EstimateMaxPriorityFeePerGasErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type EstimateMaxPriorityFeePerGasData,\n  type EstimateMaxPriorityFeePerGasOptions,\n  type EstimateMaxPriorityFeePerGasQueryFnData,\n  type EstimateMaxPriorityFeePerGasQueryKey,\n  estimateMaxPriorityFeePerGasQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEstimateMaxPriorityFeePerGasParameters<\n  config extends Config = Config,\n  selectData = EstimateMaxPriorityFeePerGasData,\n> = Compute<\n  EstimateMaxPriorityFeePerGasOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      EstimateMaxPriorityFeePerGasQueryFnData,\n      EstimateMaxPriorityFeePerGasErrorType,\n      selectData,\n      EstimateMaxPriorityFeePerGasQueryKey<config>\n    >\n>\n\nexport type UseEstimateMaxPriorityFeePerGasReturnType<\n  selectData = EstimateMaxPriorityFeePerGasData,\n> = UseQueryReturnType<selectData, EstimateMaxPriorityFeePerGasErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEstimateMaxPriorityFeePerGas */\nexport function useEstimateMaxPriorityFeePerGas<\n  config extends Config = ResolvedRegister['config'],\n  selectData = EstimateMaxPriorityFeePerGasData,\n>(\n  parameters: UseEstimateMaxPriorityFeePerGasParameters<\n    config,\n    selectData\n  > = {},\n): UseEstimateMaxPriorityFeePerGasReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = estimateMaxPriorityFeePerGasQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetFeeHistoryErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetFeeHistoryData,\n  type GetFeeHistoryOptions,\n  type GetFeeHistoryQueryFnData,\n  type GetFeeHistoryQueryKey,\n  getFeeHistoryQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseFeeHistoryParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetFeeHistoryData,\n> = Compute<\n  GetFeeHistoryOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetFeeHistoryQueryFnData,\n      GetFeeHistoryErrorType,\n      selectData,\n      GetFeeHistoryQueryKey<config, chainId>\n    >\n>\n\nexport type UseFeeHistoryReturnType<selectData = GetFeeHistoryData> =\n  UseQueryReturnType<selectData, GetFeeHistoryErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useFeeHistory */\nexport function useFeeHistory<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetFeeHistoryData,\n>(\n  parameters: UseFeeHistoryParameters<config, chainId, selectData> = {},\n): UseFeeHistoryReturnType<selectData> {\n  const { blockCount, rewardPercentiles, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getFeeHistoryQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    blockCount && rewardPercentiles && (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetGasPriceErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetGasPriceData,\n  type GetGasPriceOptions,\n  type GetGasPriceQueryFnData,\n  type GetGasPriceQueryKey,\n  getGasPriceQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseGasPriceParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetGasPriceData,\n> = Compute<\n  GetGasPriceOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetGasPriceQueryFnData,\n      GetGasPriceErrorType,\n      selectData,\n      GetGasPriceQueryKey<config, chainId>\n    >\n>\n\nexport type UseGasPriceReturnType<selectData = GetGasPriceData> =\n  UseQueryReturnType<selectData, GetGasPriceErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useGasPrice */\nexport function useGasPrice<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetGasPriceData,\n>(\n  parameters: UseGasPriceParameters<config, chainId, selectData> = {},\n): UseGasPriceReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  const options = getGasPriceQueryOptions(config, {\n    ...parameters,\n    chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ReadContractsErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport {\n  type InfiniteReadContractsQueryFnData,\n  type InfiniteReadContractsQueryKey,\n  infiniteReadContractsQueryOptions,\n  structuralSharing,\n} from '@wagmi/core/query'\nimport type { ContractFunctionParameters } from 'viem'\n\nimport type {\n  InfiniteReadContractsData,\n  InfiniteReadContractsOptions,\n} from '../exports/query.js'\nimport type {\n  ConfigParameter,\n  InfiniteQueryParameter,\n} from '../types/properties.js'\nimport {\n  type UseInfiniteQueryParameters,\n  type UseInfiniteQueryReturnType,\n  useInfiniteQuery,\n} from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseInfiniteContractReadsParameters<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  config extends Config = Config,\n  pageParam = unknown,\n  selectData = InfiniteReadContractsData<contracts, allowFailure>,\n> = InfiniteReadContractsOptions<contracts, allowFailure, pageParam, config> &\n  ConfigParameter<config> &\n  InfiniteQueryParameter<\n    InfiniteReadContractsQueryFnData<contracts, allowFailure>,\n    ReadContractsErrorType,\n    selectData,\n    InfiniteReadContractsData<contracts, allowFailure>,\n    InfiniteReadContractsQueryKey<contracts, allowFailure, pageParam, config>,\n    pageParam\n  >\n\nexport type UseInfiniteContractReadsReturnType<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  selectData = InfiniteReadContractsData<contracts, allowFailure>,\n> = UseInfiniteQueryReturnType<selectData, ReadContractsErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useInfiniteReadContracts */\nexport function useInfiniteReadContracts<\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean = true,\n  config extends Config = ResolvedRegister['config'],\n  pageParam = unknown,\n  selectData = InfiniteReadContractsData<contracts, allowFailure>,\n>(\n  parameters: UseInfiniteContractReadsParameters<\n    contracts,\n    allowFailure,\n    config,\n    pageParam,\n    selectData\n  >,\n): UseInfiniteContractReadsReturnType<contracts, allowFailure, selectData> {\n  const { contracts = [], query } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = infiniteReadContractsQueryOptions(config, {\n    ...parameters,\n    chainId,\n    contracts: contracts as UseInfiniteContractReadsParameters['contracts'],\n    query: query as UseInfiniteQueryParameters,\n  })\n\n  return useInfiniteQuery({\n    ...(query as any),\n    ...options,\n    initialPageParam: options.initialPageParam,\n    structuralSharing: query.structuralSharing ?? structuralSharing,\n  })\n}\n", "'use client'\n\nimport type {\n  Config,\n  PrepareTransactionRequestErrorType,\n  ResolvedRegister,\n  SelectChains,\n} from '@wagmi/core'\nimport type { PrepareTransactionRequestQueryFnData } from '@wagmi/core/query'\nimport {\n  type PrepareTransactionRequestData,\n  type PrepareTransactionRequestOptions,\n  type PrepareTransactionRequestQueryKey,\n  prepareTransactionRequestQueryOptions,\n} from '@wagmi/core/query'\nimport type { PrepareTransactionRequestRequest as viem_PrepareTransactionRequestRequest } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UsePrepareTransactionRequestParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  selectData = PrepareTransactionRequestData<config, chainId, request>,\n> = PrepareTransactionRequestOptions<config, chainId, request> &\n  ConfigParameter<config> &\n  QueryParameter<\n    PrepareTransactionRequestQueryFnData<config, chainId, request>,\n    PrepareTransactionRequestErrorType,\n    selectData,\n    PrepareTransactionRequestQueryKey<config, chainId, request>\n  >\n\nexport type UsePrepareTransactionRequestReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  selectData = PrepareTransactionRequestData<config, chainId, request>,\n> = UseQueryReturnType<selectData, PrepareTransactionRequestErrorType>\n\n/** https://wagmi.sh/react/api/hooks/usePrepareTransactionRequest */\nexport function usePrepareTransactionRequest<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  selectData = PrepareTransactionRequestData<config, chainId, request>,\n>(\n  parameters: UsePrepareTransactionRequestParameters<\n    config,\n    chainId,\n    request,\n    selectData\n  > = {} as any,\n): UsePrepareTransactionRequestReturnType<\n  config,\n  chainId,\n  request,\n  selectData\n> {\n  const { to, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = prepareTransactionRequestQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  } as PrepareTransactionRequestOptions<config, chainId, request>)\n  const enabled = Boolean(to && (query.enabled ?? true))\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UsePrepareTransactionRequestReturnType<\n    config,\n    chainId,\n    request,\n    selectData\n  >\n}\n", "'use client'\n\nimport type { Config, GetProofErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { GetProofQueryFnData } from '@wagmi/core/query'\nimport {\n  type GetProofData,\n  type GetProofOptions,\n  type GetProofQueryKey,\n  getProofQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseProofParameters<\n  config extends Config = Config,\n  selectData = GetProofData,\n> = Compute<\n  GetProofOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetProofQueryFnData,\n      GetProofErrorType,\n      selectData,\n      GetProofQueryKey<config>\n    >\n>\n\nexport type UseProofReturnType<selectData = GetProofData> = UseQueryReturnType<\n  selectData,\n  GetProofErrorType\n>\n\n/** https://wagmi.sh/react/api/hooks/useProof */\nexport function useProof<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetProofData,\n>(\n  parameters: UseProofParameters<config, selectData> = {},\n): UseProofReturnType<selectData> {\n  const { address, storageKeys, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getProofQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && storageKeys && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetPublicClientParameters,\n  type GetPublicClientReturnType,\n  getPublicClient,\n  type ResolvedRegister,\n  watchPublicClient,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UsePublicClientParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n> = Compute<\n  GetPublicClientParameters<config, chainId> & ConfigParameter<config>\n>\n\nexport type UsePublicClientReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n> = GetPublicClientReturnType<config, chainId>\n\n/** https://wagmi.sh/react/api/hooks/usePublicClient */\nexport function usePublicClient<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n>(\n  parameters: UsePublicClientParameters<config, chainId> = {},\n): UsePublicClientReturnType<config, chainId> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStoreWithSelector(\n    (onChange) => watchPublicClient(config, { onChange }),\n    () => getPublicClient(config, parameters),\n    () => getPublicClient(config, parameters),\n    (x) => x,\n    (a, b) => a?.uid === b?.uid,\n  ) as any\n}\n", "'use client'\n\nimport type {\n  Config,\n  ReadContractErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { UnionCompute } from '@wagmi/core/internal'\nimport {\n  type ReadContractData,\n  type ReadContractOptions,\n  type ReadContractQueryFnData,\n  type ReadContractQueryKey,\n  readContractQueryOptions,\n  structuralSharing,\n} from '@wagmi/core/query'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName, Hex } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseReadContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'pure' | 'view'\n  > = ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'pure' | 'view',\n    functionName\n  > = ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config = Config,\n  selectData = ReadContractData<abi, functionName, args>,\n> = UnionCompute<\n  ReadContractOptions<abi, functionName, args, config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      ReadContractQueryFnData<abi, functionName, args>,\n      ReadContractErrorType,\n      selectData,\n      ReadContractQueryKey<abi, functionName, args, config>\n    >\n>\n\nexport type UseReadContractReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'pure' | 'view'\n  > = ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'pure' | 'view',\n    functionName\n  > = ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  selectData = ReadContractData<abi, functionName, args>,\n> = UseQueryReturnType<selectData, ReadContractErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useReadContract */\nexport function useReadContract<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config = ResolvedRegister['config'],\n  selectData = ReadContractData<abi, functionName, args>,\n>(\n  parameters: UseReadContractParameters<\n    abi,\n    functionName,\n    args,\n    config,\n    selectData\n  > = {} as any,\n): UseReadContractReturnType<abi, functionName, args, selectData> {\n  const { abi, address, functionName, query = {} } = parameters\n  // @ts-ignore\n  const code = parameters.code as Hex | undefined\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = readContractQueryOptions<config, abi, functionName, args>(\n    config,\n    { ...(parameters as any), chainId: parameters.chainId ?? chainId },\n  )\n  const enabled = Boolean(\n    (address || code) && abi && functionName && (query.enabled ?? true),\n  )\n\n  return useQuery({\n    ...query,\n    ...options,\n    enabled,\n    structuralSharing: query.structuralSharing ?? structuralSharing,\n  })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ReadContractsErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type ReadContractsData,\n  type ReadContractsOptions,\n  type ReadContractsQueryFnData,\n  type ReadContractsQueryKey,\n  readContractsQueryOptions,\n  structuralSharing,\n} from '@wagmi/core/query'\nimport { useMemo } from 'react'\nimport type { ContractFunctionParameters } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseReadContractsParameters<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  config extends Config = Config,\n  selectData = ReadContractsData<contracts, allowFailure>,\n> = Compute<\n  ReadContractsOptions<contracts, allowFailure, config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      ReadContractsQueryFnData<contracts, allowFailure>,\n      ReadContractsErrorType,\n      selectData,\n      ReadContractsQueryKey<contracts, allowFailure, config>\n    >\n>\n\nexport type UseReadContractsReturnType<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  selectData = ReadContractsData<contracts, allowFailure>,\n> = UseQueryReturnType<selectData, ReadContractsErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useReadContracts */\nexport function useReadContracts<\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean = true,\n  config extends Config = ResolvedRegister['config'],\n  selectData = ReadContractsData<contracts, allowFailure>,\n>(\n  parameters: UseReadContractsParameters<\n    contracts,\n    allowFailure,\n    config,\n    selectData\n  > = {},\n): UseReadContractsReturnType<contracts, allowFailure, selectData> {\n  const { contracts = [], query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = readContractsQueryOptions<config, contracts, allowFailure>(\n    config,\n    { ...parameters, chainId },\n  )\n\n  const enabled = useMemo(() => {\n    let isContractsValid = false\n    for (const contract of contracts) {\n      const { abi, address, functionName } =\n        contract as ContractFunctionParameters\n      if (!abi || !address || !functionName) {\n        isContractsValid = false\n        break\n      }\n      isContractsValid = true\n    }\n    return Boolean(isContractsValid && (query.enabled ?? true))\n  }, [contracts, query.enabled])\n\n  return useQuery({\n    ...options,\n    ...query,\n    enabled,\n    structuralSharing: query.structuralSharing ?? structuralSharing,\n  })\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { Connector, ReconnectErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type ReconnectData,\n  type ReconnectMutate,\n  type ReconnectMutateAsync,\n  type ReconnectVariables,\n  reconnectMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseReconnectParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          ReconnectData,\n          ReconnectErrorType,\n          ReconnectVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseReconnectReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    ReconnectData,\n    ReconnectErrorType,\n    ReconnectVariables,\n    context\n  > & {\n    connectors: readonly Connector[]\n    reconnect: ReconnectMutate<context>\n    reconnectAsync: ReconnectMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useReconnect */\nexport function useReconnect<context = unknown>(\n  parameters: UseReconnectParameters<context> = {},\n): UseReconnectReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = reconnectMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    connectors: config.connectors,\n    reconnect: mutate,\n    reconnectAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { Config, ResolvedRegister, SendCallsErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SendCallsData,\n  type SendCallsMutate,\n  type SendCallsMutateAsync,\n  type SendCallsVariables,\n  sendCallsMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSendCallsParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          SendCallsData,\n          SendCallsErrorType,\n          SendCallsVariables<config, config['chains'][number]['id']>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSendCallsReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    SendCallsData,\n    SendCallsErrorType,\n    SendCallsVariables<config, config['chains'][number]['id']>,\n    context\n  > & {\n    sendCalls: SendCallsMutate<config, context>\n    sendCallsAsync: SendCallsMutateAsync<config, context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSendCalls */\nexport function useSendCalls<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseSendCallsParameters<config, context> = {},\n): UseSendCallsReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = sendCallsMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseSendCallsReturnType<config, context>\n  return {\n    ...result,\n    sendCalls: mutate as Return['sendCalls'],\n    sendCallsAsync: mutateAsync as Return['sendCallsAsync'],\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  ResolvedRegister,\n  SendTransactionErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SendTransactionData,\n  type SendTransactionMutate,\n  type SendTransactionMutateAsync,\n  type SendTransactionVariables,\n  sendTransactionMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSendTransactionParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          SendTransactionData,\n          SendTransactionErrorType,\n          SendTransactionVariables<config, config['chains'][number]['id']>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSendTransactionReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    SendTransactionData,\n    SendTransactionErrorType,\n    SendTransactionVariables<config, config['chains'][number]['id']>,\n    context\n  > & {\n    sendTransaction: SendTransactionMutate<config, context>\n    sendTransactionAsync: SendTransactionMutateAsync<config, context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSendTransaction */\nexport function useSendTransaction<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseSendTransactionParameters<config, context> = {},\n): UseSendTransactionReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = sendTransactionMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseSendTransactionReturnType<config, context>\n  return {\n    ...result,\n    sendTransaction: mutate as Return['sendTransaction'],\n    sendTransactionAsync: mutateAsync as Return['sendTransactionAsync'],\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  ResolvedRegister,\n  ShowCallsStatusErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type ShowCallsStatusData,\n  type ShowCallsStatusMutate,\n  type ShowCallsStatusMutateAsync,\n  type ShowCallsStatusVariables,\n  showCallsStatusMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseShowCallsStatusParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          ShowCallsStatusData,\n          ShowCallsStatusErrorType,\n          ShowCallsStatusVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseShowCallsStatusReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    ShowCallsStatusData,\n    ShowCallsStatusErrorType,\n    ShowCallsStatusVariables,\n    context\n  > & {\n    showCallsStatus: ShowCallsStatusMutate\n    showCallsStatusAsync: ShowCallsStatusMutateAsync\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useShowCallsStatus */\nexport function useShowCallsStatus<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseShowCallsStatusParameters<config, context> = {},\n): UseShowCallsStatusReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = showCallsStatusMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseShowCallsStatusReturnType\n  return {\n    ...result,\n    showCallsStatus: mutate as Return['showCallsStatus'],\n    showCallsStatusAsync: mutateAsync as Return['showCallsStatusAsync'],\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { SignMessageErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SignMessageData,\n  type SignMessageMutate,\n  type SignMessageMutateAsync,\n  type SignMessageVariables,\n  signMessageMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSignMessageParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          SignMessageData,\n          SignMessageErrorType,\n          SignMessageVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSignMessageReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    SignMessageData,\n    SignMessageErrorType,\n    SignMessageVariables,\n    context\n  > & {\n    signMessage: SignMessageMutate<context>\n    signMessageAsync: SignMessageMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSignMessage */\nexport function useSignMessage<context = unknown>(\n  parameters: UseSignMessageParameters<context> = {},\n): UseSignMessageReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = signMessageMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    signMessage: mutate,\n    signMessageAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { SignTypedDataErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SignTypedDataData,\n  type SignTypedDataMutate,\n  type SignTypedDataMutateAsync,\n  type SignTypedDataVariables,\n  signTypedDataMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSignTypedDataParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          SignTypedDataData,\n          SignTypedDataErrorType,\n          SignTypedDataVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSignTypedDataReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    SignTypedDataData,\n    SignTypedDataErrorType,\n    SignTypedDataVariables,\n    context\n  > & {\n    signTypedData: SignTypedDataMutate<context>\n    signTypedDataAsync: SignTypedDataMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSignTypedData */\nexport function useSignTypedData<context = unknown>(\n  parameters: UseSignTypedDataParameters<context> = {},\n): UseSignTypedDataReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = signTypedDataMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseSignTypedDataReturnType<context>\n  return {\n    ...result,\n    signTypedData: mutate as Return['signTypedData'],\n    signTypedDataAsync: mutateAsync as Return['signTypedDataAsync'],\n  }\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  SimulateContractErrorType,\n} from '@wagmi/core'\nimport {\n  type SimulateContractData,\n  type SimulateContractOptions,\n  type SimulateContractQueryFnData,\n  type SimulateContractQueryKey,\n  simulateContractQueryOptions,\n} from '@wagmi/core/query'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\nimport { useConnectorClient } from './useConnectorClient.js'\n\nexport type UseSimulateContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = SimulateContractData<abi, functionName, args, config, chainId>,\n> = SimulateContractOptions<abi, functionName, args, config, chainId> &\n  ConfigParameter<config> &\n  QueryParameter<\n    SimulateContractQueryFnData<abi, functionName, args, config, chainId>,\n    SimulateContractErrorType,\n    selectData,\n    SimulateContractQueryKey<abi, functionName, args, config, chainId>\n  >\n\nexport type UseSimulateContractReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = SimulateContractData<abi, functionName, args, config, chainId>,\n> = UseQueryReturnType<selectData, SimulateContractErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useSimulateContract */\nexport function useSimulateContract<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = SimulateContractData<abi, functionName, args, config, chainId>,\n>(\n  parameters: UseSimulateContractParameters<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId,\n    selectData\n  > = {} as any,\n): UseSimulateContractReturnType<\n  abi,\n  functionName,\n  args,\n  config,\n  chainId,\n  selectData\n> {\n  const { abi, address, connector, functionName, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const { data: connectorClient } = useConnectorClient({\n    config,\n    connector,\n    query: { enabled: parameters.account === undefined },\n  })\n  const chainId = useChainId({ config })\n\n  const options = simulateContractQueryOptions<\n    config,\n    abi,\n    functionName,\n    args,\n    chainId\n  >(config, {\n    ...parameters,\n    account: parameters.account ?? connectorClient?.account,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    abi && address && functionName && (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetStorageAtErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { GetStorageAtQueryFnData } from '@wagmi/core/query'\nimport {\n  type GetStorageAtData,\n  type GetStorageAtOptions,\n  type GetStorageAtQueryKey,\n  getStorageAtQueryOptions,\n} from '@wagmi/core/query'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseStorageAtParameters<\n  config extends Config = Config,\n  selectData = GetStorageAtData,\n> = Compute<\n  GetStorageAtOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetStorageAtQueryFnData,\n      GetStorageAtErrorType,\n      selectData,\n      GetStorageAtQueryKey<config>\n    >\n>\n\nexport type UseStorageAtReturnType<selectData = GetStorageAtData> =\n  UseQueryReturnType<selectData, GetStorageAtErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useStorageAt */\nexport function useStorageAt<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetStorageAtData,\n>(\n  parameters: UseStorageAtParameters<config, selectData> = {},\n): UseStorageAtReturnType<selectData> {\n  const { address, slot, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getStorageAtQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && slot && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  Connector,\n  ResolvedRegister,\n  SwitchAccountErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SwitchAccountData,\n  type SwitchAccountMutate,\n  type SwitchAccountMutateAsync,\n  type SwitchAccountVariables,\n  switchAccountMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\nimport { useConnections } from './useConnections.js'\n\nexport type UseSwitchAccountParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          SwitchAccountData<config>,\n          SwitchAccountErrorType,\n          SwitchAccountVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSwitchAccountReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    SwitchAccountData<config>,\n    SwitchAccountErrorType,\n    SwitchAccountVariables,\n    context\n  > & {\n    connectors: readonly Connector[]\n    switchAccount: SwitchAccountMutate<config, context>\n    switchAccountAsync: SwitchAccountMutateAsync<config, context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSwitchAccount */\nexport function useSwitchAccount<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseSwitchAccountParameters<config, context> = {},\n): UseSwitchAccountReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = switchAccountMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    connectors: useConnections({ config }).map(\n      (connection) => connection.connector,\n    ),\n    switchAccount: mutate,\n    switchAccountAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  ResolvedRegister,\n  SwitchChainErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SwitchChainData,\n  type SwitchChainMutate,\n  type SwitchChainMutateAsync,\n  type SwitchChainVariables,\n  switchChainMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useChains } from './useChains.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSwitchChainParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          SwitchChainData<config, config['chains'][number]['id']>,\n          SwitchChainErrorType,\n          SwitchChainVariables<config, config['chains'][number]['id']>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSwitchChainReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    SwitchChainData<config, config['chains'][number]['id']>,\n    SwitchChainErrorType,\n    SwitchChainVariables<config, config['chains'][number]['id']>,\n    context\n  > & {\n    chains: config['chains']\n    switchChain: SwitchChainMutate<config, context>\n    switchChainAsync: SwitchChainMutateAsync<config, context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSwitchChain */\nexport function useSwitchChain<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseSwitchChainParameters<config, context> = {},\n): UseSwitchChainReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = switchChainMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseSwitchChainReturnType<config, context>\n  return {\n    ...result,\n    chains: useChains({ config }) as unknown as config['chains'],\n    switchChain: mutate as Return['switchChain'],\n    switchChainAsync: mutateAsync as Return['switchChainAsync'],\n  }\n}\n", "'use client'\n\nimport type { Config, GetTokenErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetTokenData,\n  type GetTokenOptions,\n  type GetTokenQueryFnData,\n  type GetTokenQueryKey,\n  getTokenQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTokenParameters<\n  config extends Config = Config,\n  selectData = GetTokenData,\n> = Compute<\n  GetTokenOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetTokenQueryFnData,\n      GetTokenErrorType,\n      selectData,\n      GetTokenQueryKey<config>\n    >\n>\n\nexport type UseTokenReturnType<selectData = GetTokenData> = UseQueryReturnType<\n  selectData,\n  GetTokenErrorType\n>\n\n/**\n * @deprecated\n *\n * https://wagmi.sh/react/api/hooks/useToken\n */\nexport function useToken<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetTokenData,\n>(\n  parameters: UseTokenParameters<config, selectData> = {},\n): UseTokenReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTokenQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetTransactionErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetTransactionData,\n  type GetTransactionOptions,\n  type GetTransactionQueryFnData,\n  type GetTransactionQueryKey,\n  getTransactionQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTransactionParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionData<config, chainId>,\n> = Compute<\n  GetTransactionOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetTransactionQueryFnData<config, chainId>,\n      GetTransactionErrorType,\n      selectData,\n      GetTransactionQueryKey<config, chainId>\n    >\n>\n\nexport type UseTransactionReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionData<config, chainId>,\n> = UseQueryReturnType<selectData, GetTransactionErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useTransaction */\nexport function useTransaction<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionData<config, chainId>,\n>(\n  parameters: UseTransactionParameters<config, chainId, selectData> = {},\n): UseTransactionReturnType<config, chainId, selectData> {\n  const { blockHash, blockNumber, blockTag, hash, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTransactionQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    !(blockHash && blockNumber && blockTag && hash) && (query.enabled ?? true),\n  )\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UseTransactionReturnType<config, chainId, selectData>\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetTransactionConfirmationsErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport {\n  type GetTransactionConfirmationsData,\n  type GetTransactionConfirmationsOptions,\n  type GetTransactionConfirmationsQueryFnData,\n  type GetTransactionConfirmationsQueryKey,\n  getTransactionConfirmationsQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTransactionConfirmationsParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetTransactionConfirmationsData,\n> = GetTransactionConfirmationsOptions<config, chainId> &\n  ConfigParameter<config> &\n  QueryParameter<\n    GetTransactionConfirmationsQueryFnData,\n    GetTransactionConfirmationsErrorType,\n    selectData,\n    GetTransactionConfirmationsQueryKey<config, chainId>\n  >\n\nexport type UseTransactionConfirmationsReturnType<\n  selectData = GetTransactionConfirmationsData,\n> = UseQueryReturnType<selectData, GetTransactionConfirmationsErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useTransactionConfirmations */\nexport function useTransactionConfirmations<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetTransactionConfirmationsData,\n>(\n  parameters: UseTransactionConfirmationsParameters<\n    config,\n    chainId,\n    selectData\n  > = {} as any,\n): UseTransactionConfirmationsReturnType<selectData> {\n  const { hash, transactionReceipt, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTransactionConfirmationsQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    !(hash && transactionReceipt) &&\n      (hash || transactionReceipt) &&\n      (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetTransactionCountErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { GetTransactionCountQueryFnData } from '@wagmi/core/query'\nimport {\n  type GetTransactionCountData,\n  type GetTransactionCountOptions,\n  type GetTransactionCountQueryKey,\n  getTransactionCountQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTransactionCountParameters<\n  config extends Config = Config,\n  selectData = GetTransactionCountData,\n> = Compute<\n  GetTransactionCountOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetTransactionCountQueryFnData,\n      GetTransactionCountErrorType,\n      selectData,\n      GetTransactionCountQueryKey<config>\n    >\n>\n\nexport type UseTransactionCountReturnType<\n  selectData = GetTransactionCountData,\n> = UseQueryReturnType<selectData, GetTransactionCountErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useTransactionCount */\nexport function useTransactionCount<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetTransactionCountData,\n>(\n  parameters: UseTransactionCountParameters<config, selectData> = {},\n): UseTransactionCountReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTransactionCountQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetTransactionReceiptErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { GetTransactionReceiptQueryFnData } from '@wagmi/core/query'\nimport {\n  type GetTransactionReceiptData,\n  type GetTransactionReceiptOptions,\n  type GetTransactionReceiptQueryKey,\n  getTransactionReceiptQueryOptions,\n} from '@wagmi/core/query'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTransactionReceiptParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionReceiptData<config, chainId>,\n> = Compute<\n  GetTransactionReceiptOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetTransactionReceiptQueryFnData<config, chainId>,\n      GetTransactionReceiptErrorType,\n      selectData,\n      GetTransactionReceiptQueryKey<config, chainId>\n    >\n>\n\nexport type UseTransactionReceiptReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionReceiptData<config, chainId>,\n> = UseQueryReturnType<selectData, GetTransactionReceiptErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useTransactionReceipt */\nexport function useTransactionReceipt<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionReceiptData<config, chainId>,\n>(\n  parameters: UseTransactionReceiptParameters<config, chainId, selectData> = {},\n): UseTransactionReceiptReturnType<config, chainId, selectData> {\n  const { hash, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTransactionReceiptQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(hash && (query.enabled ?? true))\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UseTransactionReceiptReturnType<config, chainId, selectData>\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  VerifyMessageErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { VerifyMessageQueryFnData } from '@wagmi/core/query'\nimport {\n  type VerifyMessageData,\n  type VerifyMessageOptions,\n  type VerifyMessageQueryKey,\n  verifyMessageQueryOptions,\n} from '@wagmi/core/query'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseVerifyMessageParameters<\n  config extends Config = Config,\n  selectData = VerifyMessageData,\n> = Compute<\n  VerifyMessageOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      VerifyMessageQueryFnData,\n      VerifyMessageErrorType,\n      selectData,\n      VerifyMessageQueryKey<config>\n    >\n>\n\nexport type UseVerifyMessageReturnType<selectData = VerifyMessageData> =\n  UseQueryReturnType<selectData, VerifyMessageErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useVerifyMessage */\nexport function useVerifyMessage<\n  config extends Config = ResolvedRegister['config'],\n  selectData = VerifyMessageData,\n>(\n  parameters: UseVerifyMessageParameters<config, selectData> = {},\n): UseVerifyMessageReturnType<selectData> {\n  const { address, message, signature, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = verifyMessageQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    address && message && signature && (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  VerifyTypedDataErrorType,\n} from '@wagmi/core'\nimport type { VerifyTypedDataQueryFnData } from '@wagmi/core/query'\nimport {\n  type VerifyTypedDataData,\n  type VerifyTypedDataOptions,\n  type VerifyTypedDataQueryKey,\n  verifyTypedDataQueryOptions,\n} from '@wagmi/core/query'\nimport type { TypedData } from 'viem'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseVerifyTypedDataParameters<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  config extends Config = Config,\n  selectData = VerifyTypedDataData,\n> = VerifyTypedDataOptions<typedData, primaryType, config> &\n  ConfigParameter<config> &\n  QueryParameter<\n    VerifyTypedDataQueryFnData,\n    VerifyTypedDataErrorType,\n    selectData,\n    VerifyTypedDataQueryKey<typedData, primaryType, config>\n  >\n\nexport type UseVerifyTypedDataReturnType<selectData = VerifyTypedDataData> =\n  UseQueryReturnType<selectData, VerifyTypedDataErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useVerifyTypedData */\nexport function useVerifyTypedData<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n  config extends Config = ResolvedRegister['config'],\n  selectData = VerifyTypedDataData,\n>(\n  parameters: UseVerifyTypedDataParameters<\n    typedData,\n    primaryType,\n    config,\n    selectData\n  > = {} as any,\n): UseVerifyTypedDataReturnType<selectData> {\n  const {\n    address,\n    message,\n    primaryType,\n    signature,\n    types,\n    query = {},\n  } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = verifyTypedDataQueryOptions<config, typedData, primaryType>(\n    config,\n    {\n      ...parameters,\n      chainId: parameters.chainId ?? chainId,\n    },\n  )\n  const enabled = Boolean(\n    address &&\n      message &&\n      primaryType &&\n      signature &&\n      types &&\n      (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  WaitForCallsStatusErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type WaitForCallsStatusData,\n  type WaitForCallsStatusOptions,\n  type WaitForCallsStatusQueryFnData,\n  type WaitForCallsStatusQueryKey,\n  waitForCallsStatusQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWaitForCallsStatusParameters<\n  config extends Config = Config,\n  selectData = WaitForCallsStatusData,\n> = Compute<\n  WaitForCallsStatusOptions &\n    ConfigParameter<config> &\n    QueryParameter<\n      WaitForCallsStatusQueryFnData,\n      WaitForCallsStatusErrorType,\n      selectData,\n      WaitForCallsStatusQueryKey\n    >\n>\n\nexport type UseWaitForCallsStatusReturnType<\n  selectData = WaitForCallsStatusData,\n> = UseQueryReturnType<selectData, WaitForCallsStatusErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useWaitForCallsStatus */\nexport function useWaitForCallsStatus<\n  config extends Config = ResolvedRegister['config'],\n  selectData = WaitForCallsStatusData,\n>(\n  parameters: UseWaitForCallsStatusParameters<config, selectData>,\n): UseWaitForCallsStatusReturnType<selectData> {\n  const { id, query = {} } = parameters\n\n  const config = useConfig(parameters)\n\n  const options = waitForCallsStatusQueryOptions(config, parameters)\n  const enabled = Boolean(id && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  WaitForTransactionReceiptErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type WaitForTransactionReceiptData,\n  type WaitForTransactionReceiptOptions,\n  type WaitForTransactionReceiptQueryFnData,\n  type WaitForTransactionReceiptQueryKey,\n  waitForTransactionReceiptQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWaitForTransactionReceiptParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = WaitForTransactionReceiptData<config, chainId>,\n> = Compute<\n  WaitForTransactionReceiptOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      WaitForTransactionReceiptQueryFnData<config, chainId>,\n      WaitForTransactionReceiptErrorType,\n      selectData,\n      WaitForTransactionReceiptQueryKey<config, chainId>\n    >\n>\n\nexport type UseWaitForTransactionReceiptReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = WaitForTransactionReceiptData<config, chainId>,\n> = UseQueryReturnType<selectData, WaitForTransactionReceiptErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useWaitForTransactionReceipt */\nexport function useWaitForTransactionReceipt<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = WaitForTransactionReceiptData<config, chainId>,\n>(\n  parameters: UseWaitForTransactionReceiptParameters<\n    config,\n    chainId,\n    selectData\n  > = {},\n): UseWaitForTransactionReceiptReturnType<config, chainId, selectData> {\n  const { hash, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = waitForTransactionReceiptQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(hash && (query.enabled ?? true))\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UseWaitForTransactionReceiptReturnType<config, chainId, selectData>\n}\n", "'use client'\n\n// Almost identical implementation to `useConnectorClient` (except for return type)\n// Should update both in tandem\n\nimport { useQueryClient } from '@tanstack/react-query'\nimport type {\n  Config,\n  GetWalletClientErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute, Omit } from '@wagmi/core/internal'\nimport {\n  type GetWalletClientData,\n  type GetWalletClientOptions,\n  type GetWalletClientQueryFnData,\n  type GetWalletClientQueryKey,\n  getWalletClientQueryOptions,\n} from '@wagmi/core/query'\nimport { useEffect, useRef } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport {\n  type UseQueryParameters,\n  type UseQueryReturnType,\n  useQuery,\n} from '../utils/query.js'\nimport { useAccount } from './useAccount.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWalletClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetWalletClientData<config, chainId>,\n> = Compute<\n  GetWalletClientOptions<config, chainId> &\n    ConfigParameter<config> & {\n      query?:\n        | Compute<\n            Omit<\n              UseQueryParameters<\n                GetWalletClientQueryFnData<config, chainId>,\n                GetWalletClientErrorType,\n                selectData,\n                GetWalletClientQueryKey<config, chainId>\n              >,\n              'gcTime' | 'staleTime'\n            >\n          >\n        | undefined\n    }\n>\n\nexport type UseWalletClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetWalletClientData<config, chainId>,\n> = UseQueryReturnType<selectData, GetWalletClientErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useWalletClient */\nexport function useWalletClient<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetWalletClientData<config, chainId>,\n>(\n  parameters: UseWalletClientParameters<config, chainId, selectData> = {},\n): UseWalletClientReturnType<config, chainId, selectData> {\n  const { query = {}, ...rest } = parameters\n\n  const config = useConfig(rest)\n  const queryClient = useQueryClient()\n  const { address, connector, status } = useAccount({ config })\n  const chainId = useChainId({ config })\n  const activeConnector = parameters.connector ?? connector\n\n  const { queryKey, ...options } = getWalletClientQueryOptions<config, chainId>(\n    config,\n    {\n      ...parameters,\n      chainId: parameters.chainId ?? chainId,\n      connector: parameters.connector ?? connector,\n    },\n  )\n  const enabled = Boolean(\n    (status === 'connected' ||\n      (status === 'reconnecting' && activeConnector?.getProvider)) &&\n      (query.enabled ?? true),\n  )\n\n  const addressRef = useRef(address)\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `queryKey` not required\n  useEffect(() => {\n    const previousAddress = addressRef.current\n    if (!address && previousAddress) {\n      // remove when account is disconnected\n      queryClient.removeQueries({ queryKey })\n      addressRef.current = undefined\n    } else if (address !== previousAddress) {\n      // invalidate when address changes\n      queryClient.invalidateQueries({ queryKey })\n      addressRef.current = address\n    }\n  }, [address, queryClient])\n\n  return useQuery({\n    ...query,\n    ...options,\n    queryKey,\n    enabled,\n    staleTime: Number.POSITIVE_INFINITY,\n  } as any) as UseWalletClientReturnType<config, chainId, selectData>\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { WatchAssetErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type WatchAssetData,\n  type WatchAssetMutate,\n  type WatchAssetMutateAsync,\n  type WatchAssetVariables,\n  watchAssetMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchAssetParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          WatchAssetData,\n          WatchAssetErrorType,\n          WatchAssetVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseWatchAssetReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    WatchAssetData,\n    WatchAssetErrorType,\n    WatchAssetVariables,\n    context\n  > & {\n    watchAsset: WatchAssetMutate<context>\n    watchAssetAsync: WatchAssetMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useWatchAsset */\nexport function useWatchAsset<context = unknown>(\n  parameters: UseWatchAssetParameters<context> = {},\n): UseWatchAssetReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = watchAssetMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    watchAsset: mutate,\n    watchAssetAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport {\n  type Config,\n  type ResolvedRegister,\n  type WatchContractEventParameters,\n  watchContractEvent,\n} from '@wagmi/core'\nimport type { UnionCompute, UnionExactPartial } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\nimport type { Abi, ContractEventName } from 'viem'\n\nimport type { ConfigParameter, EnabledParameter } from '../types/properties.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchContractEventParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  eventName extends ContractEventName<abi> = ContractEventName<abi>,\n  strict extends boolean | undefined = undefined,\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  UnionExactPartial<\n    WatchContractEventParameters<abi, eventName, strict, config, chainId>\n  > &\n    ConfigParameter<config> &\n    EnabledParameter\n>\n\nexport type UseWatchContractEventReturnType = void\n\n/** https://wagmi.sh/react/api/hooks/useWatchContractEvent */\nexport function useWatchContractEvent<\n  const abi extends Abi | readonly unknown[],\n  eventName extends ContractEventName<abi>,\n  strict extends boolean | undefined = undefined,\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  parameters: UseWatchContractEventParameters<\n    abi,\n    eventName,\n    strict,\n    config,\n    chainId\n  > = {} as any,\n): UseWatchContractEventReturnType {\n  const { enabled = true, onLogs, config: _, ...rest } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  // TODO(react@19): cleanup\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `rest` changes every render so only including properties in dependency array\n  useEffect(() => {\n    if (!enabled) return\n    if (!onLogs) return\n    return watchContractEvent(config, {\n      ...(rest as any),\n      chainId,\n      onLogs,\n    })\n  }, [\n    chainId,\n    config,\n    enabled,\n    onLogs,\n    ///\n    rest.abi,\n    rest.address,\n    rest.args,\n    rest.batch,\n    rest.eventName,\n    rest.fromBlock,\n    rest.onError,\n    rest.poll,\n    rest.pollingInterval,\n    rest.strict,\n    rest.syncConnectedChain,\n  ])\n}\n", "'use client'\n\nimport {\n  type Config,\n  type ResolvedRegister,\n  type WatchPendingTransactionsParameters,\n  watchPendingTransactions,\n} from '@wagmi/core'\nimport type { UnionCompute, UnionExactPartial } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\n\nimport type { ConfigParameter, EnabledParameter } from '../types/properties.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchPendingTransactionsParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  UnionExactPartial<WatchPendingTransactionsParameters<config, chainId>> &\n    ConfigParameter<config> &\n    EnabledParameter\n>\n\nexport type UseWatchPendingTransactionsReturnType = void\n\n/** https://wagmi.sh/react/api/hooks/useWatchPendingTransactions */\nexport function useWatchPendingTransactions<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  parameters: UseWatchPendingTransactionsParameters<\n    config,\n    chainId\n  > = {} as any,\n): UseWatchPendingTransactionsReturnType {\n  const { enabled = true, onTransactions, config: _, ...rest } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  // TODO(react@19): cleanup\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `rest` changes every render so only including properties in dependency array\n  useEffect(() => {\n    if (!enabled) return\n    if (!onTransactions) return\n    return watchPendingTransactions(config, {\n      ...(rest as any),\n      chainId,\n      onTransactions,\n    })\n  }, [\n    chainId,\n    config,\n    enabled,\n    onTransactions,\n    ///\n    rest.batch,\n    rest.onError,\n    rest.poll,\n    rest.pollingInterval,\n    rest.syncConnectedChain,\n  ])\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  ResolvedRegister,\n  WriteContractErrorType,\n} from '@wagmi/core'\nimport {\n  type WriteContractData,\n  type WriteContractMutate,\n  type WriteContractMutateAsync,\n  type WriteContractVariables,\n  writeContractMutationOptions,\n} from '@wagmi/core/query'\nimport type { Abi } from 'viem'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWriteContractParameters<\n  config extends Config = Config,\n  context = unknown,\n> = ConfigParameter<config> & {\n  mutation?:\n    | UseMutationParameters<\n        WriteContractData,\n        WriteContractErrorType,\n        WriteContractVariables<\n          Abi,\n          string,\n          readonly unknown[],\n          config,\n          config['chains'][number]['id']\n        >,\n        context\n      >\n    | undefined\n}\n\nexport type UseWriteContractReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = UseMutationReturnType<\n  WriteContractData,\n  WriteContractErrorType,\n  WriteContractVariables<\n    Abi,\n    string,\n    readonly unknown[],\n    config,\n    config['chains'][number]['id']\n  >,\n  context\n> & {\n  writeContract: WriteContractMutate<config, context>\n  writeContractAsync: WriteContractMutateAsync<config, context>\n}\n\n/** https://wagmi.sh/react/api/hooks/useWriteContract */\nexport function useWriteContract<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseWriteContractParameters<config, context> = {},\n): UseWriteContractReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = writeContractMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseWriteContractReturnType<config, context>\n  return {\n    ...result,\n    writeContract: mutate as Return['writeContract'],\n    writeContractAsync: mutateAsync as Return['writeContractAsync'],\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,6BACE,WAAW,MAAM,oBACf,oBAAoB,MACtB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,YAAI,QAAQ,YAAY;AACxB,YAAI,CAAC,4BAA4B;AAC/B,cAAI,cAAc,YAAY;AAC9B,mBAAS,OAAO,WAAW,MACxB,QAAQ;AAAA,YACP;AAAA,UACF,GACC,6BAA6B;AAAA,QAClC;AACA,sBAAc,SAAS;AAAA,UACrB,MAAM,EAAE,OAAc,YAAyB;AAAA,QACjD,CAAC;AACD,YAAI,OAAO,YAAY,CAAC,EAAE,MACxB,cAAc,YAAY,CAAC;AAC7B;AAAA,UACE,WAAY;AACV,iBAAK,QAAQ;AACb,iBAAK,cAAc;AACnB,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,UAC5D;AAAA,UACA,CAAC,WAAW,OAAO,WAAW;AAAA,QAChC;AACA,QAAAA;AAAA,UACE,WAAY;AACV,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAC1D,mBAAO,UAAU,WAAY;AAC3B,qCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH;AAAA,UACA,CAAC,SAAS;AAAA,QACZ;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,eAAO,KAAK;AACZ,YAAI;AACF,cAAI,YAAY,kBAAkB;AAClC,iBAAO,CAAC,SAAS,MAAM,SAAS;AAAA,QAClC,SAAS,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,eAAO,YAAY;AAAA,MACrB;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAI,QAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzD,WAAW,MAAM,UACjBA,cAAY,MAAM,WAClB,kBAAkB,MAAM,iBACxB,gBAAgB,MAAM,eACtB,oBAAoB,OACpB,6BAA6B,OAC7B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,YAC9B,gBAAgB,OAAO,OAAO,SAAS,gBACnC,yBACA;AACR,cAAQ,uBACN,WAAW,MAAM,uBAAuB,MAAM,uBAAuB;AACvE,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC9FL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAI,QAAQ,iBACV,OAAO,gBACP,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzDC,wBAAuB,KAAK,sBAC5BC,UAAS,MAAM,QACfC,cAAY,MAAM,WAClBC,WAAU,MAAM,SAChB,gBAAgB,MAAM;AACxB,cAAQ,mCAAmC,SACzC,WACA,aACA,mBACA,UACA,SACA;AACA,YAAI,UAAUF,QAAO,IAAI;AACzB,YAAI,SAAS,QAAQ,SAAS;AAC5B,cAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,kBAAQ,UAAU;AAAA,QACpB,MAAO,QAAO,QAAQ;AACtB,kBAAUE;AAAA,UACR,WAAY;AACV,qBAAS,iBAAiB,cAAc;AACtC,kBAAI,CAAC,SAAS;AACZ,0BAAU;AACV,mCAAmB;AACnB,+BAAe,SAAS,YAAY;AACpC,oBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,sBAAI,mBAAmB,KAAK;AAC5B,sBAAI,QAAQ,kBAAkB,YAAY;AACxC,2BAAQ,oBAAoB;AAAA,gBAChC;AACA,uBAAQ,oBAAoB;AAAA,cAC9B;AACA,iCAAmB;AACnB,kBAAI,SAAS,kBAAkB,YAAY;AACzC,uBAAO;AACT,kBAAI,gBAAgB,SAAS,YAAY;AACzC,kBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,uBAAQ,mBAAmB,cAAe;AAC5C,iCAAmB;AACnB,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,gBAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,mBAAO;AAAA,cACL,WAAY;AACV,uBAAO,iBAAiB,YAAY,CAAC;AAAA,cACvC;AAAA,cACA,SAAS,yBACL,SACA,WAAY;AACV,uBAAO,iBAAiB,uBAAuB,CAAC;AAAA,cAClD;AAAA,YACN;AAAA,UACF;AAAA,UACA,CAAC,aAAa,mBAAmB,UAAU,OAAO;AAAA,QACpD;AACA,YAAI,QAAQH,sBAAqB,WAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE,QAAAE;AAAA,UACE,WAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf;AAAA,UACA,CAAC,KAAK;AAAA,QACR;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AChGL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACHA,IAAAE,gBAA6C;;;ACA7C,mBAAqD;AAQ/C,SAAU,QAAQ,YAAiD;AACvE,QAAM,EAAE,UAAU,QAAQ,cAAc,mBAAmB,KAAI,IAAK;AAEpE,QAAM,EAAE,QAAO,IAAK,QAAQ,QAAQ;IAClC;IACA;GACD;AAGD,MAAI,CAAC,OAAO,UAAU;AAAK,YAAO;AAGlC,QAAM,aAAS,qBAAO,IAAI;AAE1B,8BAAU,MAAK;AACb,QAAI,CAAC,OAAO;AAAS;AACrB,QAAI,CAAC,OAAO,UAAU;AAAK;AAC3B,YAAO;AACP,WAAO,MAAK;AACV,aAAO,UAAU;IACnB;EACF,GAAG,CAAA,CAAE;AAEL,SAAO;AACT;;;AD7BO,IAAM,mBAAe,6BAE1B,MAAS;AAQL,SAAU,cACd,YAAuD;AAEvD,QAAM,EAAE,UAAU,OAAM,IAAK;AAE7B,QAAM,QAAQ,EAAE,OAAO,OAAM;AAC7B,aAAO,6BACL,SACA,gBACA,6BAAc,aAAa,UAAU,OAAO,QAAQ,CAAC;AAEzD;;;AE3BO,IAAM,UAAU;;;ACEhB,IAAM,aAAa,MAAM,SAAS,OAAO;;;ACG1C,IAAOC,aAAP,cAAyB,UAAS;EAAxC,cAAA;;AACW,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOlB;EANE,IAAa,cAAW;AACtB,WAAO;EACT;EACA,IAAa,UAAO;AAClB,WAAO,WAAU;EACnB;;;;ACPI,IAAO,6BAAP,cAA0CC,WAAS;EAEvD,cAAA;AACE,UAAM,oDAAoD;MACxD,UAAU;KACX;AAJM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;;;ACRF,IAAAC,gBAA2B;AAYrB,SAAU,UACd,aAA0C,CAAA,GAAE;AAG5C,QAAM,SAAS,WAAW,cAAU,0BAAW,YAAY;AAC3D,MAAI,CAAC;AAAQ,UAAM,IAAI,2BAA0B;AACjD,SAAO;AACT;;;ACLM,SAAU,YACd,QACA,YAAyC;AAEzC,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UAAU,OAAO,UAAU,CAAC,QAAQ,eAAc;AAC9D,aACE,QACA,UAAoD;EAExD,CAAC;AACH;;;ACzBA,IAAAC,gBAAgC;AAChC,2BAAiD;AAEjD,IAAM,gBAAgB,CAAC,QACrB,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG;AAEzC,SAAU,gCAId,WACA,aACA,oBAAyD,aACzD,UAAmD,WAAS;AAE5D,QAAM,kBAAc,sBAAiB,CAAA,CAAE;AACvC,QAAM,aAAS,uDACb,WACA,aACA,mBACA,CAAC,MAAM,GACP,CAAC,GAAG,MAAK;AACP,QAAI,cAAc,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY,QAAQ,QAAQ;AACtE,iBAAW,OAAO,YAAY,SAAS;AACrC,cAAM,QAAQ,QACX,EAA4B,GAAG,GAC/B,EAA4B,GAAG,CAAC;AAEnC,YAAI,CAAC;AAAO,iBAAO;MACrB;AACA,aAAO;IACT;AACA,WAAO,QAAQ,GAAG,CAAC;EACrB,CAAC;AAGH,aAAO,uBAAQ,MAAK;AAClB,QAAI,cAAc,MAAM,GAAG;AACzB,YAAM,gBAAgB,EAAE,GAAG,OAAM;AACjC,UAAI,aAAa,CAAA;AACjB,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAChC,aAAuC,GACtC;AACD,qBAAa;UACX,GAAG;UACH,CAAC,GAAG,GAAG;YACL,cAAc;YACd,YAAY;YACZ,KAAK,MAAK;AACR,kBAAI,CAAC,YAAY,QAAQ,SAAS,GAAG,GAAG;AACtC,4BAAY,QAAQ,KAAK,GAAG;cAC9B;AACA,qBAAO;YACT;;;MAGN;AACA,aAAO,iBAAiB,eAAe,UAAU;AACjD,aAAO;IACT;AAEA,WAAO;EACT,GAAG,CAAC,MAAM,CAAC;AACb;;;AC7CM,SAAU,WACd,aAA2C,CAAA,GAAE;AAE7C,QAAM,SAAS,UAAU,UAAU;AAEnC,SAAO,gCACL,CAAC,aAAa,aAAa,QAAQ,EAAE,SAAQ,CAAE,GAC/C,MAAM,WAAW,MAAM,CAAC;AAE5B;;;AC1BA,IAAAC,gBAA0B;AAsBpB,SAAU,iBAAiB,aAAyC,CAAA,GAAE;AAC1E,QAAM,EAAE,WAAW,aAAY,IAAK;AAEpC,QAAM,SAAS,UAAU,UAAU;AAEnC,+BAAU,MAAK;AACb,WAAO,aAAa,QAAQ;MAC1B,SAAS,MAAM,UAAQ;AACrB,aACG,SAAS,WAAW,kBAClB,SAAS,WAAW,gBACnB,SAAS,YAAY,WACzB,KAAK,WAAW,aAChB;AACA,gBAAM,EAAE,SAAS,WAAW,OAAO,SAAS,UAAS,IAAK;AAC1D,gBAAM,gBACJ,SAAS,WAAW;UAEpB,SAAS,WAAW;AACtB,sBAAY;YACV;YACA;YACA;YACA;YACA;YACA;WACD;QACH,WACE,SAAS,WAAW,eACpB,KAAK,WAAW;AAEhB,yBAAc;MAClB;KACD;EACH,GAAG,CAAC,QAAQ,WAAW,YAAY,CAAC;AACtC;;;AC3DM,SAAU,kBACd,SACA,SAAa;AAEb,SAAO,iBAAiB,SAAS,OAAO;AAC1C;AAEM,SAAU,OAAO,UAAkB;AACvC,SAAO,KAAK,UAAU,UAAU,CAAC,GAAG,UAAS;AAC3C,QAAIC,eAAc,KAAK;AACrB,aAAO,OAAO,KAAK,KAAK,EACrB,KAAI,EACJ,OAAO,CAAC,QAAQ,QAAO;AACtB,eAAO,GAAG,IAAI,MAAM,GAAG;AACvB,eAAO;MACT,GAAG,CAAA,CAAS;AAChB,QAAI,OAAO,UAAU;AAAU,aAAO,MAAM,SAAQ;AACpD,WAAO;EACT,CAAC;AACH;AAGA,SAASA,eAAc,OAAU;AAC/B,MAAI,CAAC,mBAAmB,KAAK,GAAG;AAC9B,WAAO;EACT;AAGA,QAAM,OAAO,MAAM;AACnB,MAAI,OAAO,SAAS;AAAa,WAAO;AAGxC,QAAM,OAAO,KAAK;AAClB,MAAI,CAAC,mBAAmB,IAAI;AAAG,WAAO;AAItC,MAAI,CAAC,KAAK,eAAe,eAAe;AAAG,WAAO;AAGlD,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAM;AAChC,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AAEM,SAAU,mBACd,SAAa;AAIb,QAAM;;;IAGJ;IAAY;IAAU;IAAQ;IAAa;IAAsB;IAAU;IAAM;IAAa;IAAS;IAAW;IAAU;IAAgB;IAAO;IAAY,mBAAAC;;;IAI/J;IAAsB;IAAkB;;;IAIxC;IAAoB;IAAS;IAAqB;IAAiB;IAAiB;IAA6B;IAAgB;IAAoB;IAAsB;IAAc;IAAQ;IAAW;IAAU;;;;;IAMtN;IAAQ;IAAW;IACnB,GAAG;EAAI,IACL;AAEJ,SAAO;AACT;;;AC3DM,SAAU,iBACd,QACA,UAA+B,CAAA,GAAE;AAEjC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,OAAO,MAAM,KAAK,QAAQ;QAC9B,GAAG;OACc;AACnB,aAAO,QAAQ;IACjB;IACA,UAAU,aAAa,OAAO;;AAOlC;AAMM,SAAU,aACd,SAA4B;AAE5B,SAAO,CAAC,QAAQ,mBAAmB,OAAO,CAAC;AAC7C;;;AClCM,SAAU,uBAA8C,QAAc;AAC1E,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,QAAQ,QAAQ,SAAS;IAClC;IACA,aAAa,CAAC,SAAS;;AAM3B;;;ACXM,SAAU,8BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,eAAe,QAAQ,SAAS;IACzC;IACA,aAAa,CAAC,gBAAgB;;AAMlC;;;ACdM,SAAU,0BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,WAAW,QAAQ,SAAS;IACrC;IACA,aAAa,CAAC,YAAY;;AAM9B;;;ACHM,SAAU,+BAGd,QAAgB,UAAmD,CAAA,GAAE;AACrE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,aAAO,mBAAmB,QAAQ,UAAU;IAC9C;IACA,UAAU,2BAA2B,OAAO;;AAOhD;AAQM,SAAU,2BAGd,UAAmD,CAAA,GAAE;AACrD,SAAO,CAAC,sBAAsB,mBAAmB,OAAO,CAAC;AAC3D;;;AC/BM,SAAU,wBAGd,QAAgB,UAA+C,CAAA,GAAS;AACxE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC,WAAW,CAAC;AACf,cAAM,IAAI,MAAM,kCAAkC;AACpD,aAAO,YAAY,QAAQ,EAAE,SAAS,WAAW,GAAI,WAAkB,CAAE;IAC3E;IACA,UAAU,oBAAoB,OAAO;;AAOzC;AAMM,SAAU,oBAGd,UAA+C,CAAA,GAAS;AACxD,QAAM,EAAE,WAAW,GAAG,GAAG,KAAI,IAAK;AAClC,SAAO,CAAC,eAAe,mBAAmB,IAAI,CAAC;AACjD;;;AC/BM,SAAU,yCACd,QACA,UAAuD,CAAA,GAAE;AAEzD,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,aAAO,6BAA6B,QAAQ,UAAU;IACxD;IACA,UAAU,qCAAqC,OAAO;;AAO1D;AAQM,SAAU,qCACd,UAAuD,CAAA,GAAE;AAEzD,SAAO,CAAC,gCAAgC,mBAAmB,OAAO,CAAC;AACrE;;;AC9BM,SAAU,uBACd,QACA,UAAqC,CAAA,GAAE;AAEvC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,YAAM,UAAU,MAAM,WAAW,QAAQ;QACvC,GAAI;QACJ;OACD;AACD,aAAO,WAAW;IACpB;IACA,UAAU,mBAAmB,OAAO;;AAOxC;AAMM,SAAU,mBACd,UAAqC,CAAA,GAAE;AAEvC,SAAO,CAAC,WAAW,mBAAmB,OAAO,CAAC;AAChD;;;ACrBM,SAAU,qBAMd,QACA,UAA2E,CAAA,GAAE;AAE7E,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,QAAQ,MAAM,SAAS,QAAQ,UAAU;AAC/C,aAAQ,SAAS;IACnB;IACA,UAAU,iBAAiB,OAAO;;AAOtC;AAgBM,SAAU,iBAMd,UAA2E,CAAA,GAAE;AAE7E,SAAO,CAAC,SAAS,mBAAmB,OAAO,CAAC;AAC9C;;;ACtDM,SAAU,2BAGd,QAAgB,UAAkD,CAAA,GAAE;AACpE,SAAO;IACL,QAAQ;IACR,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,cAAc,MAAM,eAAe,QAAQ,UAAU;AAC3D,aAAO,eAAe;IACxB;IACA,UAAU,uBAAuB,OAAO;;AAO5C;AAMM,SAAU,uBAGd,UAAkD,CAAA,GAAE;AACpD,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC5BM,SAAU,qCAId,QACA,UAA4D,CAAA,GAAE;AAE9D,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,wBAAwB,MAAM,yBAClC,QACA,UAAU;AAEZ,aAAO,yBAAyB;IAClC;IACA,UAAU,iCAAiC,OAAO;;AAOtD;AAOM,SAAU,iCAGd,UAA4D,CAAA,GAAE;AAC9D,SAAO,CAAC,yBAAyB,mBAAmB,OAAO,CAAC;AAC9D;;;ACvCM,SAAU,wBACd,QACA,UAAsC,CAAA,GAAE;AAExC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,YAAM,WAAW,MAAM,YAAY,QAAQ,EAAE,GAAG,YAAY,QAAO,CAAE;AACrE,aAAQ,YAAY;IACtB;IACA,UAAU,oBAAoB,OAAO;;AAOzC;AAKM,SAAU,oBACd,SAAmC;AAEnC,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC1BM,SAAU,2BACd,QACA,SAA8B;AAE9B,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,SAAS,MAAM,eAAe,QAAQ,UAAU;AACtD,aAAO;IACT;IACA,UAAU,uBAAuB,OAAO;IACxC,MAAM,cAAc,OAAK;AACvB,UAAI,iBAAiB;AAA4B,eAAO;AACxD,aAAO,eAAe;IACxB;;AAOJ;AAMM,SAAU,uBAAuB,SAA8B;AACnE,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC1BM,SAAU,4BAGd,QAAgB,UAAmD,CAAA,GAAE;AACrE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,eAAe,MAAM,gBAAgB,QAAQ,UAAU;AAC7D,aAAO;IACT;IACA,UAAU,wBAAwB,OAAO;IACzC,MAAM,cAAc,OAAK;AACvB,UAAI,iBAAiB;AAA4B,eAAO;AACxD,aAAO,eAAe;IACxB;;AAOJ;AAYM,SAAU,wBAGd,UAAmD,CAAA,GAAE;AACrD,SAAO,CAAC,gBAAgB,mBAAmB,OAAO,CAAC;AACrD;;;ACtCM,SAAU,+BAGd,QAAgB,UAAsD,CAAA,GAAE;AACxE,SAAO;IACL,QAAQ;IACR,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,cAAc,GAAG,UAAU,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AACnE,aAAO,mBAAmB,QAAQ;QAChC,GAAG;QACH;OACD;IACH;IACA,UAAU,2BAA2B,OAAO;;AAOhD;AAYM,SAAU,2BAGd,UAAsD,CAAA,GAAE;AACxD,QAAM,EAAE,WAAW,GAAG,WAAU,IAAK;AACrC,SAAO;IACL;IACA,EAAE,GAAG,mBAAmB,UAAU,GAAG,cAAc,WAAW,IAAG;;AAErE;;;AC9CM,SAAU,0BACd,QACA,UAAwC,CAAA,GAAE;AAE1C,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACvD,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,cAAc,QAAQ,EAAE,GAAG,YAAY,KAAI,CAAE;IACtD;IACA,UAAU,sBAAsB,OAAO;;AAO3C;AAMM,SAAU,sBACd,UAAwC,CAAA,GAAE;AAE1C,SAAO,CAAC,cAAc,mBAAmB,OAAO,CAAC;AACnD;;;AC3BM,SAAU,yBACd,QACA,UAAuC,CAAA,GAAE;AAEzC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACvD,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,aAAa,QAAQ,EAAE,GAAG,YAAY,KAAI,CAAE;IACrD;IACA,UAAU,qBAAqB,OAAO;;AAO1C;AAMM,SAAU,qBACd,UAAuC,CAAA,GAAE;AAEzC,SAAO,CAAC,aAAa,mBAAmB,OAAO,CAAC;AAClD;;;AC3BM,SAAU,uBACd,QACA,UAAqC,CAAA,GAAE;AAEvC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,aAAO,WAAW,QAAQ,EAAE,GAAG,YAAY,QAAO,CAAE;IACtD;IACA,UAAU,mBAAmB,OAAO;;AAOxC;AAMM,SAAU,mBACd,UAAqC,CAAA,GAAE;AAEvC,SAAO,CAAC,WAAW,mBAAmB,OAAO,CAAC;AAChD;;;AC3BM,SAAU,2BACd,QACA,UAAyC,CAAA,GAAE;AAE3C,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACvD,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,eAAe,QAAQ,EAAE,GAAG,YAAY,KAAI,CAAE;IACvD;IACA,UAAU,uBAAuB,OAAO;;AAO5C;AAMM,SAAU,uBACd,UAAyC,CAAA,GAAE;AAE3C,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC3BM,SAAU,uBACd,QACA,UAAqC,CAAA,GAAE;AAEvC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,KAAK,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC5D,UAAI,CAAC,OAAO,CAAC;AAAM,cAAM,IAAI,MAAM,2BAA2B;AAC9D,aAAO,WAAW,QAAQ,EAAE,GAAG,YAAY,KAAK,KAAI,CAAE;IACxD;IACA,UAAU,mBAAmB,OAAO;;AAOxC;AAMM,SAAU,mBACd,UAAqC,CAAA,GAAE;AAEvC,SAAO,CAAC,WAAW,mBAAmB,OAAO,CAAC;AAChD;;;ACpBM,SAAU,0BAGd,QAAgB,UAAiD,CAAA,GAAE;AACnE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EACJ,YACA,mBACA,UAAU,GACV,GAAG,WAAU,IACX,SAAS,CAAC;AACd,UAAI,CAAC;AAAY,cAAM,IAAI,MAAM,wBAAwB;AACzD,UAAI,CAAC;AAAmB,cAAM,IAAI,MAAM,+BAA+B;AACvE,YAAM,aAAa,MAAM,cAAc,QAAQ;QAC7C,GAAI;QACJ;QACA;OACD;AACD,aAAO,cAAc;IACvB;IACA,UAAU,sBAAsB,OAAO;;AAO3C;AAMM,SAAU,sBAGd,UAAiD,CAAA,GAAE;AACnD,SAAO,CAAC,cAAc,mBAAmB,OAAO,CAAC;AACnD;;;AC3CM,SAAU,wBAGd,QAAgB,UAA+C,CAAA,GAAE;AACjE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,WAAW,MAAM,YAAY,QAAQ,UAAU;AACrD,aAAO,YAAY;IACrB;IACA,UAAU,oBAAoB,OAAO;;AAOzC;AAMM,SAAU,oBAGd,UAA+C,CAAA,GAAE;AACjD,SAAO,CAAC,YAAY,mBAAmB,OAAO,CAAC;AACjD;;;AC/BM,SAAU,qBACd,QACA,UAAmC,CAAA,GAAE;AAErC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,aAAa,GAAG,WAAU,IAAK,SAAS,CAAC;AACvE,UAAI,CAAC,WAAW,CAAC;AACf,cAAM,IAAI,MAAM,sCAAsC;AACxD,aAAO,SAAS,QAAQ,EAAE,GAAG,YAAY,SAAS,YAAW,CAAE;IACjE;IACA,UAAU,iBAAiB,OAAO;;AAOtC;AAMM,SAAU,iBACd,SAAgC;AAEhC,SAAO,CAAC,YAAY,mBAAmB,OAAO,CAAC;AACjD;;;AC5BM,SAAU,yBACd,QACA,UAAuC,CAAA,GAAE;AAEzC,SAAO;IACL,QAAQ,EAAE,SAAQ,GAAE;AAClB,YAAM,EAAE,SAAS,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAChE,UAAI,CAAC,WAAW,CAAC;AAAM,cAAM,IAAI,MAAM,+BAA+B;AACtE,aAAO,aAAa,QAAQ,EAAE,GAAG,YAAY,SAAS,KAAI,CAAE;IAC9D;IACA,UAAU,qBAAqB,OAAO;;AAO1C;AAMM,SAAU,qBACd,SAAoC;AAEpC,SAAO,CAAC,gBAAgB,mBAAmB,OAAO,CAAC;AACrD;;;AC3BM,SAAU,qBACd,QACA,UAAmC,CAAA,GAAE;AAErC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,aAAO,SAAS,QAAQ,EAAE,GAAG,YAAY,QAAO,CAAE;IACpD;IACA,UAAU,iBAAiB,OAAO;;AAOtC;AAMM,SAAU,iBACd,UAAmC,CAAA,GAAE;AAErC,SAAO,CAAC,SAAS,mBAAmB,OAAO,CAAC;AAC9C;;;ACxBM,SAAU,2BAGd,QAAgB,UAAkD,CAAA,GAAE;AACpE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,WAAW,aAAa,UAAU,MAAM,MAAK,IAAK,SAAS,CAAC;AACpE,UAAI,CAAC,aAAa,CAAC,eAAe,CAAC,YAAY,CAAC;AAC9C,cAAM,IAAI,MAAM,uDAAuD;AACzE,UAAI,CAAC,QAAQ,CAAC;AACZ,cAAM,IAAI,MACR,2DAA2D;AAE/D,YAAM,EAAE,UAAU,GAAG,GAAG,KAAI,IAAK,SAAS,CAAC;AAC3C,aAAO,eACL,QACA,IAAgC;IAEpC;IACA,UAAU,uBAAuB,OAAO;;AAO5C;AAYM,SAAU,uBAGd,UAAkD,CAAA,GAAE;AACpD,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC1CM,SAAU,wCAMd,QACA,UAA+D,CAAA,GAAS;AAExE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EACJ,MACA,oBACA,UAAU,GACV,GAAG,WAAU,IACX,SAAS,CAAC;AACd,UAAI,CAAC,QAAQ,CAAC;AACZ,cAAM,IAAI,MAAM,wCAAwC;AAE1D,YAAM,gBAAgB,MAAM,4BAA4B,QAAQ;QAC9D;QACA;QACA,GAAI;OACL;AACD,aAAO,iBAAiB;IAC1B;IACA,UAAU,oCAAoC,OAAO;;AAOzD;AAQM,SAAU,oCAKd,UAA+D,CAAA,GAAS;AACxE,SAAO,CAAC,4BAA4B,mBAAmB,OAAO,CAAC;AACjE;;;ACpDM,SAAU,gCACd,QACA,UAA8C,CAAA,GAAE;AAEhD,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,YAAM,mBAAmB,MAAM,oBAAoB,QAAQ;QACzD,GAAI;QACJ;OACD;AACD,aAAO,oBAAoB;IAC7B;IACA,UAAU,4BAA4B,OAAO;;AAOjD;AAOM,SAAU,4BACd,UAA8C,CAAA,GAAE;AAEhD,SAAO,CAAC,oBAAoB,mBAAmB,OAAO,CAAC;AACzD;;;AC9BM,SAAU,kCAGd,QAAgB,UAAyD,CAAA,GAAE;AAC3E,SAAO;IACL,QAAQ,EAAE,SAAQ,GAAE;AAClB,YAAM,EAAE,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACvD,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,sBAAsB,QAAQ,EAAE,GAAG,YAAY,KAAI,CAAE;IAC9D;IACA,UAAU,8BAA8B,OAAO;;AAOnD;AAWM,SAAU,8BAGd,SAAsD;AACtD,SAAO,CAAC,yBAAyB,mBAAmB,OAAO,CAAC;AAC9D;;;ACjCM,SAAU,4BAGd,QAAgB,UAAmD,CAAA,GAAE;AACrE,SAAO;IACL,QAAQ;IACR,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,cAAc,GAAG,UAAU,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AACnE,aAAO,gBAAgB,QAAQ,EAAE,GAAG,YAAY,UAAS,CAAE;IAC7D;IACA,UAAU,wBAAwB,OAAO;;AAO7C;AAYM,SAAU,wBAGd,UAAmD,CAAA,GAAE;AACrD,QAAM,EAAE,WAAW,GAAG,WAAU,IAAK;AACrC,SAAO;IACL;IACA,EAAE,GAAG,mBAAmB,UAAU,GAAG,cAAc,WAAW,IAAG;;AAErE;;;AC3BM,SAAU,kCAMd,QACA,SAOkE;AAElE,SAAO;IACL,GAAG,QAAQ;IACX,MAAM,QAAQ,EAAE,WAAW,SAAQ,GAAE;AACnC,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,UAAU,GAAG,UAAU,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AAC/D,aAAQ,MAAM,cAAc,QAAQ;QAClC,GAAG;QACH,WAAW,UAAU,SAAgB;OACtC;IACH;IACA,UAAU,8BAA8B,OAAO;;AASnD;AA4BM,SAAU,8BAMd,SAOkE;AAElE,QAAM,EAAE,WAAW,GAAG,OAAO,IAAI,GAAG,WAAU,IAAK;AACnD,SAAO,CAAC,yBAAyB,mBAAmB,UAAU,CAAC;AACjE;;;ACpFM,SAAU,sCAQd,QACA,UAII,CAAA,GAAS;AAEb,SAAO;IACL,QAAQ,EAAE,SAAQ,GAAE;AAClB,YAAM,EAAE,UAAU,GAAG,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AACrD,UAAI,CAAC;AAAI,cAAM,IAAI,MAAM,gBAAgB;AACzC,aAAO,0BAA0B,QAAQ;QACvC;QACA,GAAI;OACL;IAGH;IACA,UAAU,kCAAkC,OAAO;;AAOvD;AAmBM,SAAU,kCAOd,SAAmE;AACnE,SAAO,CAAC,6BAA6B,mBAAmB,OAAO,CAAC;AAClE;;;ACnEM,SAAU,yBAMd,QACA,UAAgE,CAAA,GAAS;AAEzE,SAAO;;;IAGL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,MAAM,QAAQ;AACpB,UAAI,CAAC;AAAK,cAAM,IAAI,MAAM,iBAAiB;AAE3C,YAAM,EAAE,cAAc,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC/D,YAAM,uBAAuB,MAAK;AAChC,cAAM,SAAS,SAAS,CAAC;AACzB,YAAI,OAAO;AAAS,iBAAO,EAAE,SAAS,OAAO,QAAO;AACpD,YAAI,OAAO;AAAM,iBAAO,EAAE,MAAM,OAAO,KAAI;AAC3C,cAAM,IAAI,MAAM,6BAA6B;MAC/C,GAAE;AAEF,UAAI,CAAC;AAAc,cAAM,IAAI,MAAM,0BAA0B;AAE7D,aAAO,aAAa,QAAQ;QAC1B;QACA;QACA,MAAM,WAAW;QACjB,GAAG;QACH,GAAG;OACJ;IACH;IACA,UAAU,qBAAqB,OAAc;;AAOjD;AAcM,SAAU,qBAKd,UAAgE,CAAA,GAAS;AACzE,QAAM,EAAE,KAAK,GAAG,GAAG,KAAI,IAAK;AAC5B,SAAO,CAAC,gBAAgB,mBAAmB,IAAI,CAAC;AAClD;;;ACrDM,SAAU,0BAKd,QACA,UAC6B,CAAA,GAAE;AAE/B,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,YAA0C,CAAA;AAChD,YAAM,SAAS,SAAS,CAAC,EAAE,UAAU;AACrC,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,WAAW,SAAS,CAAC,EAAE,UAAU,CAAC;AACxC,cAAM,OAAO,QAAQ,YAAY,CAAC,GAAiC;AACnE,kBAAU,KAAK,EAAE,GAAG,UAAU,IAAG,CAAE;MACrC;AACA,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,aAAO,cAAc,QAAQ;QAC3B,GAAG;QACH;OACD;IACH;IACA,UAAU,sBAAsB,OAAO;;AAO3C;AAYM,SAAU,sBAKd,UAC6B,CAAA,GAAE;AAE/B,QAAM,YAAY,CAAA;AAClB,aAAW,YAAa,QAAQ,aAC9B,CAAA,GAA6D;AAC7D,UAAM,EAAE,KAAK,GAAG,GAAG,KAAI,IAAK;AAC5B,cAAU,KAAK,EAAE,GAAG,MAAM,SAAS,KAAK,WAAW,QAAQ,QAAO,CAAE;EACtE;AACA,SAAO;IACL;IACA,mBAAmB,EAAE,GAAG,SAAS,UAAS,CAAE;;AAEhD;;;ACjFM,SAAU,yBAAyB,QAAc;AACrD,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,UAAU,QAAQ,SAAS;IACpC;IACA,aAAa,CAAC,WAAW;;AAM7B;;;ACZM,SAAU,yBACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,UAAU,QAAQ,SAAS;IACpC;IACA,aAAa,CAAC,WAAW;;AAM7B;;;ACbM,SAAU,+BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,gBAAgB,QAAQ,SAAS;IAC1C;IACA,aAAa,CAAC,iBAAiB;;AAMnC;;;ACbM,SAAU,+BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,gBAAgB,QAAQ,SAAS;IAC1C;IACA,aAAa,CAAC,iBAAiB;;AAMnC;;;ACZM,SAAU,2BAA2B,QAAc;AACvD,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,YAAY,QAAQ,SAAS;IACtC;IACA,aAAa,CAAC,aAAa;;AAM/B;;;ACXM,SAAU,6BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,cAAc,QAAQ,SAAS;IACxC;IACA,aAAa,CAAC,eAAe;;AAMjC;;;ACIM,SAAU,6BAWd,QACA,UAMI,CAAA,GAAS;AAEb,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,UAAI,CAAC;AAAK,cAAM,IAAI,MAAM,iBAAiB;AAC3C,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,EAAE,SAAS,aAAY,IAAK;AAClC,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,UAAI,CAAC;AAAc,cAAM,IAAI,MAAM,0BAA0B;AAC7D,aAAO,iBAAiB,QAAQ;QAC9B;QACA;QACA,GAAI;OACL;IACH;IACA,UAAU,yBAAyB,OAAO;;AAO9C;AA0BM,SAAU,yBAWd,UAMI,CAAA,GAAS;AAEb,QAAM,EAAE,KAAK,GAAG,WAAW,IAAI,GAAG,KAAI,IAAK;AAC3C,SAAO,CAAC,oBAAoB,mBAAmB,IAAI,CAAC;AACtD;;;ACzGM,SAAU,6BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,cAAc,QAAQ,SAAS;IACxC;IACA,aAAa,CAAC,eAAe;;AAMjC;;;ACdM,SAAU,2BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,YAAY,QAAQ,SAAS;IACtC;IACA,aAAa,CAAC,aAAa;;AAM/B;;;ACPM,SAAU,0BACd,QACA,UAAwC,CAAA,GAAE;AAE1C,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,SAAS,UAAS,IAAK,SAAS,CAAC;AAClD,UAAI,CAAC,WAAW,CAAC,WAAW,CAAC;AAC3B,cAAM,IAAI,MAAM,8CAA8C;AAEhE,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAEjD,YAAM,WAAW,MAAM,cACrB,QACA,UAAqC;AAEvC,aAAO,YAAY;IACrB;IACA,UAAU,sBAAsB,OAAO;;AAO3C;AAKM,SAAU,sBACd,SAAqC;AAErC,SAAO,CAAC,iBAAiB,mBAAmB,OAAO,CAAC;AACtD;;;AC9BM,SAAU,4BAKd,QACA,UAAkE,CAAA,GAAS;AAE3E,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EACJ,SACA,SACA,aACA,WACA,OACA,UAAU,GACV,GAAG,WAAU,IACX,SAAS,CAAC;AACd,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,UAAI,CAAC;AAAa,cAAM,IAAI,MAAM,yBAAyB;AAC3D,UAAI,CAAC;AAAW,cAAM,IAAI,MAAM,uBAAuB;AACvD,UAAI,CAAC;AAAO,cAAM,IAAI,MAAM,mBAAmB;AAE/C,YAAM,WAAW,MAAM,gBAAgB,QAAQ;QAC7C,GAAG;QACH;QACA;QACA;QACA;QACA;OAC4B;AAC9B,aAAO,YAAY;IACrB;IACA,UAAU,wBAAwB,OAAO;;AAO7C;AAMM,SAAU,wBAId,SAA+D;AAC/D,SAAO,CAAC,mBAAmB,mBAAmB,OAAO,CAAC;AACxD;;;ACzDM,SAAU,+BACd,QACA,SAAkC;AAElC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AACrD,UAAI,CAAC;AAAI,cAAM,IAAI,MAAM,gBAAgB;AACzC,YAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,GAAG,YAAY,GAAE,CAAE;AACrE,aAAO;IACT;IACA,UAAU,2BAA2B,OAAO;IAC5C,MAAM,cAAc,OAAK;AACvB,UAAI,iBAAiB;AAA4B,eAAO;AACxD,aAAO,eAAe;IACxB;;AAOJ;AAMM,SAAU,2BAA2B,SAAkC;AAC3E,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC3BM,SAAU,sCAId,QACA,UAA6D,CAAA,GAAE;AAE/D,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,MAAM,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1C,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,0BAA0B,QAAQ;QACvC,GAAG;QACH,YAAY,QAAQ;QACpB;OACD;IAGH;IACA,UAAU,kCAAkC,OAAO;;AAOvD;AAYM,SAAU,kCAGd,UAA6D,CAAA,GAAE;AAC/D,QAAM,EAAE,YAAY,GAAG,GAAG,KAAI,IAAK;AACnC,SAAO,CAAC,6BAA6B,mBAAmB,IAAI,CAAC;AAC/D;;;ACrDM,SAAU,0BAA0B,QAAc;AACtD,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,WAAW,QAAQ,SAAS;IACrC;IACA,aAAa,CAAC,YAAY;;AAM9B;;;ACXM,SAAU,6BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,cAAc,QAAQ,SAAS;IACxC;IACA,aAAa,CAAC,eAAe;;AAYjC;;;AC2CM,SAAUC,UACd,YAEC;AAED,QAAM,SAAS,SAAkB;IAC/B,GAAI;IACJ,gBAAgB;;GACjB;AACD,SAAO,WAAW,WAAW;AAC7B,SAAO;AACT;AA2CM,SAAUC,kBAMd,YAEC;AAED,QAAM,SAAS,iBAA0B;IACvC,GAAI;IACJ,gBAAgB;;GACjB;AACD,SAAO,WAAW,WAAW;AAC7B,SAAO;AACT;;;ACvIA,IAAAC,gBAAqC;AAY/B,SAAU,WACd,aAA2C,CAAA,GAAE;AAE7C,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,oCACL,CAAC,aAAa,aAAa,QAAQ,EAAE,SAAQ,CAAE,GAC/C,MAAM,WAAW,MAAM,GACvB,MAAM,WAAW,MAAM,CAAC;AAE5B;;;ACIM,SAAU,WAId,aAAuD,CAAA,GAAE;AAEzD,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,uBAAuB,QAAQ;IAC7C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC5CA,IAAAC,gBAA0B;AAwBpB,SAAU,eAOd,aAKI,CAAA,GAAS;AAEb,QAAM,EAAE,UAAU,MAAM,SAAS,QAAQ,GAAG,GAAG,KAAI,IAAK;AAExD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAItC,+BAAU,MAAK;AACb,QAAI,CAAC;AAAS;AACd,QAAI,CAAC;AAAS;AACd,WAAO,YAAY,QAAQ;MACzB,GAAI;MACJ;MACA;KACD;EACH,GAAG;IACD;IACA;IACA;IACA;;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;GACN;AACH;;;ACRM,SAAU,SAQd,aAMI,CAAA,GAAE;AAQN,QAAM,EAAE,QAAQ,CAAA,GAAI,MAAK,IAAK;AAE9B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,cAAc,eAAc;AAClC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAEtC,QAAM,UAAU,qBAAqB,QAAQ;IAC3C,GAAG;IACH;GACD;AACD,QAAM,UAAU,QAAQ,MAAM,WAAW,IAAI;AAE7C,iBAAe;IACb,GAAI;MACF,QAAQ,WAAW;MACnB,SAAS,WAAW;MACpB,GAAI,OAAO,UAAU,WAAW,QAAQ,CAAA;;IAE1C,SAAS,QACP,YAAY,OAAO,UAAU,WAAW,MAAM,UAAU,MAAM;IAEhE,QAAQ,OAAK;AACX,kBAAY,aAAa,QAAQ,UAAU,KAAK;IAClD;GACD;AAED,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AAOH;;;ACzHA,IAAAC,gBAA0B;AAmBpB,SAAU,oBAKd,aAA6D,CAAA,GAAS;AAEtE,QAAM,EAAE,UAAU,MAAM,eAAe,QAAQ,GAAG,GAAG,KAAI,IAAK;AAE9D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAItC,+BAAU,MAAK;AACb,QAAI,CAAC;AAAS;AACd,QAAI,CAAC;AAAe;AACpB,WAAO,iBAAiB,QAAQ;MAC9B,GAAI;MACJ;MACA;KACD;EACH,GAAG;IACD;IACA;IACA;IACA;;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;GACN;AACH;;;ACJM,SAAU,eAMd,aAAoE,CAAA,GAAE;AAEtE,QAAM,EAAE,QAAQ,CAAA,GAAI,MAAK,IAAK;AAE9B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,cAAc,eAAc;AAClC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAEtC,QAAM,UAAU,2BAA2B,QAAQ;IACjD,GAAG;IACH;GACD;AAED,sBAAoB;IAClB,GAAI;MACF,QAAQ,WAAW;MACnB,SAAS,WAAW;MACpB,GAAI,OAAO,UAAU,WAAW,QAAQ,CAAA;;IAE1C,SAAS,SACN,MAAM,WAAW,UACf,OAAO,UAAU,WAAW,MAAM,UAAU,MAAM;IAEvD,cAAc,aAAW;AACvB,kBAAY,aAAa,QAAQ,UAAU,WAAW;IACxD;GACD;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACtDM,SAAU,yBAMd,aAII,CAAA,GAAE;AAEN,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAEtC,QAAM,UAAU,qCAAqC,QAAQ;IAC3D,GAAG;IACH;GACD;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;AC5BM,SAAU,YAId,aAAwD,CAAA,GAAE;AAE1D,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,wBAAwB,QAAQ;IAC9C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACnBM,SAAU,QAId,aAAoD,CAAA,GAAE;AAEtD,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,iBAAiB,QAAQ;IACvC,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;AChBM,SAAU,eAId,YAAwD;AAExD,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,UAAU,2BAA2B,QAAQ,UAAU;AAE7D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACRM,SAAU,gBAKd,aAAqE,CAAA,GAAE;AAEvE,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,EAAE,QAAO,IAAK,WAAU;AAC9B,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,UAAU,4BAA4B,QAAQ;IAClD,GAAG;IACH,SAAS,WAAW;GACrB;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACpDA,IAAAC,gBAAqC;AAY/B,SAAU,UACd,aAA0C,CAAA,GAAE;AAE5C,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,oCACL,CAAC,aAAa,YAAY,QAAQ,EAAE,SAAQ,CAAE,GAC9C,MAAM,UAAU,MAAM,GACtB,MAAM,UAAU,MAAM,CAAC;AAE3B;;;ACpBA,IAAAC,wBAAiD;AAoB3C,SAAU,UAMd,aAAmD,CAAA,GAAE;AAErD,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,wDACL,CAAC,aAAa,YAAY,QAAQ,EAAE,SAAQ,CAAE,GAC9C,MAAM,UAAU,QAAQ,UAAU,GAClC,MAAM,UAAU,QAAQ,UAAU,GAClC,CAAC,MAAM,GACP,CAAC,GAAG,MAAM,GAAG,QAAQ,GAAG,GAAG;AAE/B;;;ACpCA,IAAAC,iBAA0B;;;ACH1B,IAAAC,iBAAqC;AAY/B,SAAU,cAGd,aAA8C,CAAA,GAAE;AAEhD,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,qCACL,CAAC,aAAa,gBAAgB,QAAQ,EAAE,SAAQ,CAAE,GAClD,MAAM,cAAc,MAAM,GAC1B,MAAM,cAAc,MAAM,CAAC;AAE/B;;;ADsBM,SAAU,WAId,aAAoD,CAAA,GAAE;AAEtD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,uBAAuB,MAAM;AACrD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,gCAAU,MAAK;AACb,WAAO,OAAO,UACZ,CAAC,EAAE,OAAM,MAAO,QAChB,CAAC,QAAQ,mBAAkB;AACzB,UAAI,mBAAmB,eAAe,WAAW;AAC/C,eAAO,MAAK;IAChB,CAAC;EAEL,GAAG,CAAC,QAAQ,OAAO,KAAK,CAAC;AAGzB,SAAO;IACL,GAAI;IACJ,SAAS;IACT,cAAc;IACd,YAAY,cAAc,EAAE,OAAM,CAAE;;AAExC;;;AElFA,IAAAC,iBAAqC;AAU/B,SAAU,eACd,aAAuC,CAAA,GAAE;AAEzC,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,qCACL,CAAC,aAAa,iBAAiB,QAAQ,EAAE,SAAQ,CAAE,GACnD,MAAM,eAAe,MAAM,GAC3B,MAAM,eAAe,MAAM,CAAC;AAEhC;;;ACXA,IAAAC,iBAAkC;AA4C5B,SAAU,mBAMd,aAAwE,CAAA,GAAE;AAE1E,QAAM,EAAE,QAAQ,CAAA,GAAI,GAAG,KAAI,IAAK;AAEhC,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,cAAc,eAAc;AAClC,QAAM,EAAE,SAAS,WAAW,OAAM,IAAK,WAAW,EAAE,OAAM,CAAE;AAC5D,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AACrC,QAAM,kBAAkB,WAAW,aAAa;AAEhD,QAAM,EAAE,UAAU,GAAG,QAAO,IAAK,+BAG/B,QAAQ;IACR,GAAG;IACH,SAAS,WAAW,WAAW;IAC/B,WAAW;GACZ;AACD,QAAM,UAAU,SACb,WAAW,eACT,WAAW,kBAAkB,iBAAiB,iBAC9C,MAAM,WAAW,KAAK;AAG3B,QAAM,iBAAa,uBAAO,OAAO;AAEjC,gCAAU,MAAK;AACb,UAAM,kBAAkB,WAAW;AACnC,QAAI,CAAC,WAAW,iBAAiB;AAE/B,kBAAY,cAAc,EAAE,SAAQ,CAAE;AACtC,iBAAW,UAAU;IACvB,WAAW,YAAY,iBAAiB;AAEtC,kBAAY,kBAAkB,EAAE,SAAQ,CAAE;AAC1C,iBAAW,UAAU;IACvB;EACF,GAAG,CAAC,SAAS,WAAW,CAAC;AAEzB,SAAOC,UAAS;IACd,GAAG;IACH,GAAG;IACH;IACA;IACA,WAAW,OAAO;GACnB;AACH;;;ACzDM,SAAU,kBAId,aAA2D,CAAA,GAAE;AAE7D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,8BAA8B,MAAM;AAC5D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,gBAAgB;IAChB,qBAAqB;;AAEzB;;;AC7BM,SAAU,cACd,aAA+C,CAAA,GAAE;AAEjD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,0BAA0B,MAAM;AACxD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,YAAY,eAAe,EAAE,OAAM,CAAE,EAAE,IACrC,CAAC,eAAe,WAAW,SAAS;IAEtC,YAAY;IACZ,iBAAiB;;AAErB;;;AC9BM,SAAU,cAId,aAA0D,CAAA,GAAE;AAE5D,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,0BAA0B,QAAQ;IAChD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AClBM,SAAU,aAId,aAAyD,CAAA,GAAE;AAE3D,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,yBAAyB,QAAQ;IAC/C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACtBM,SAAU,WAId,aAAuD,CAAA,GAAE;AAEzD,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,uBAAuB,QAAQ;IAC7C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACdM,SAAU,eAId,aAA2D,CAAA,GAAE;AAE7D,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,2BAA2B,QAAQ;IACjD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACtBM,SAAU,WAId,aAAuD,CAAA,GAAE;AAEzD,QAAM,EAAE,KAAK,MAAM,QAAQ,CAAA,EAAE,IAAK;AAElC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,uBAAuB,QAAQ;IAC7C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,OAAO,SAAS,MAAM,WAAW,KAAK;AAE9D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACVM,SAAU,sBAKd,aAAwE,CAAA,GAAE;AAE1E,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,+BAA+B,QAAQ;IACrD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACfM,SAAU,eACd,aAAuC,CAAA,GAAE;AAEzC,QAAM,EAAE,WAAW,QAAQ,CAAA,EAAE,IAAK;AAElC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,EAAE,MAAM,gBAAe,IAAK,mBAAmB;IACnD;IACA;IACA,OAAO,EAAE,SAAS,WAAW,YAAY,OAAS;GACnD;AACD,QAAM,UAAU,WAAW,WAAW,iBAAiB;AACvD,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,wBAAwB,QAAQ;IAC9C,GAAG;IACH;IACA,SAAS,WAAW,WAAW;IAC/B;GACD;AACD,QAAM,UAAU,SAAS,WAAW,eAAe,MAAM,WAAW,KAAK;AAEzE,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC7BM,SAAU,gCAId,aAGI,CAAA,GAAE;AAEN,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,yCAAyC,QAAQ;IAC/D,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACnBM,SAAU,cAMd,aAAmE,CAAA,GAAE;AAErE,QAAM,EAAE,YAAY,mBAAmB,QAAQ,CAAA,EAAE,IAAK;AAEtD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,0BAA0B,QAAQ;IAChD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,cAAc,sBAAsB,MAAM,WAAW,KAAK;AAG5D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACtBM,SAAU,YAMd,aAAiE,CAAA,GAAE;AAEnE,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAEtC,QAAM,UAAU,wBAAwB,QAAQ;IAC9C,GAAG;IACH;GACD;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACNM,SAAU,yBAOd,YAMC;AAED,QAAM,EAAE,YAAY,CAAA,GAAI,MAAK,IAAK;AAElC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,kCAAkC,QAAQ;IACxD,GAAG;IACH;IACA;IACA;GACD;AAED,SAAOC,kBAAiB;IACtB,GAAI;IACJ,GAAG;IACH,kBAAkB,QAAQ;IAC1B,mBAAmB,MAAM,qBAAqB;GAC/C;AACH;;;AChCM,SAAU,6BAYd,aAKI,CAAA,GAAS;AAOb,QAAM,EAAE,IAAI,QAAQ,CAAA,EAAE,IAAK;AAE3B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,sCAAsC,QAAQ;IAC5D,GAAG;IACH,SAAS,WAAW,WAAW;GAC8B;AAC/D,QAAM,UAAU,QAAQ,OAAO,MAAM,WAAW,KAAK;AAErD,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AAMH;;;AChEM,SAAU,SAId,aAAqD,CAAA,GAAE;AAEvD,QAAM,EAAE,SAAS,aAAa,QAAQ,CAAA,EAAE,IAAK;AAE7C,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,qBAAqB,QAAQ;IAC3C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,WAAW,gBAAgB,MAAM,WAAW,KAAK;AAEzE,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC5CA,IAAAC,wBAAiD;AAsB3C,SAAU,gBAMd,aAAyD,CAAA,GAAE;AAE3D,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,wDACL,CAAC,aAAa,kBAAkB,QAAQ,EAAE,SAAQ,CAAE,GACpD,MAAM,gBAAgB,QAAQ,UAAU,GACxC,MAAM,gBAAgB,QAAQ,UAAU,GACxC,CAAC,MAAM,GACP,CAAC,GAAG,MAAM,GAAG,QAAQ,GAAG,GAAG;AAE/B;;;ACYM,SAAU,gBAOd,aAMI,CAAA,GAAS;AAEb,QAAM,EAAE,KAAK,SAAS,cAAc,QAAQ,CAAA,EAAE,IAAK;AAEnD,QAAM,OAAO,WAAW;AAExB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,yBACd,QACA,EAAE,GAAI,YAAoB,SAAS,WAAW,WAAW,QAAO,CAAE;AAEpE,QAAM,UAAU,SACb,WAAW,SAAS,OAAO,iBAAiB,MAAM,WAAW,KAAK;AAGrE,SAAOC,UAAS;IACd,GAAG;IACH,GAAG;IACH;IACA,mBAAmB,MAAM,qBAAqB;GAC/C;AACH;;;AClFA,IAAAC,iBAAwB;AA+BlB,SAAU,iBAMd,aAKI,CAAA,GAAE;AAEN,QAAM,EAAE,YAAY,CAAA,GAAI,QAAQ,CAAA,EAAE,IAAK;AAEvC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,0BACd,QACA,EAAE,GAAG,YAAY,QAAO,CAAE;AAG5B,QAAM,cAAU,wBAAQ,MAAK;AAC3B,QAAI,mBAAmB;AACvB,eAAW,YAAY,WAAW;AAChC,YAAM,EAAE,KAAK,SAAS,aAAY,IAChC;AACF,UAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc;AACrC,2BAAmB;AACnB;MACF;AACA,yBAAmB;IACrB;AACA,WAAO,QAAQ,qBAAqB,MAAM,WAAW,KAAK;EAC5D,GAAG,CAAC,WAAW,MAAM,OAAO,CAAC;AAE7B,SAAOC,UAAS;IACd,GAAG;IACH,GAAG;IACH;IACA,mBAAmB,MAAM,qBAAqB;GAC/C;AACH;;;AC3CM,SAAU,aACd,aAA8C,CAAA,GAAE;AAEhD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,yBAAyB,MAAM;AACvD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,YAAY,OAAO;IACnB,WAAW;IACX,gBAAgB;;AAEpB;;;ACdM,SAAU,aAId,aAAsD,CAAA,GAAE;AAExD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,yBAAyB,MAAM;AACvD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,WAAW;IACX,gBAAgB;;AAEpB;;;AClBM,SAAU,mBAId,aAA4D,CAAA,GAAE;AAE9D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,+BAA+B,MAAM;AAC7D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,iBAAiB;IACjB,sBAAsB;;AAE1B;;;ACzBM,SAAU,mBAId,aAA4D,CAAA,GAAE;AAE9D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,+BAA+B,MAAM;AAC7D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,iBAAiB;IACjB,sBAAsB;;AAE1B;;;AC7BM,SAAU,eACd,aAAgD,CAAA,GAAE;AAElD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,2BAA2B,MAAM;AACzD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,aAAa;IACb,kBAAkB;;AAEtB;;;AClBM,SAAU,iBACd,aAAkD,CAAA,GAAE;AAEpD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,6BAA6B,MAAM;AAC3D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,eAAe;IACf,oBAAoB;;AAExB;;;ACHM,SAAU,oBAYd,aAOI,CAAA,GAAS;AASb,QAAM,EAAE,KAAK,SAAS,WAAW,cAAc,QAAQ,CAAA,EAAE,IAAK;AAE9D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,EAAE,MAAM,gBAAe,IAAK,mBAAmB;IACnD;IACA;IACA,OAAO,EAAE,SAAS,WAAW,YAAY,OAAS;GACnD;AACD,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,6BAMd,QAAQ;IACR,GAAG;IACH,SAAS,WAAW,WAAW,iBAAiB;IAChD,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,OAAO,WAAW,iBAAiB,MAAM,WAAW,KAAK;AAG3D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC9EM,SAAU,aAId,aAAyD,CAAA,GAAE;AAE3D,QAAM,EAAE,SAAS,MAAM,QAAQ,CAAA,EAAE,IAAK;AAEtC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,yBAAyB,QAAQ;IAC/C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,WAAW,SAAS,MAAM,WAAW,KAAK;AAElE,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACGM,SAAU,iBAId,aAA0D,CAAA,GAAE;AAE5D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,6BAA6B,MAAM;AAC3D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,YAAY,eAAe,EAAE,OAAM,CAAE,EAAE,IACrC,CAAC,eAAe,WAAW,SAAS;IAEtC,eAAe;IACf,oBAAoB;;AAExB;;;ACzBM,SAAU,eAId,aAAwD,CAAA,GAAE;AAE1D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,2BAA2B,MAAM;AACzD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,QAAQ,UAAU,EAAE,OAAM,CAAE;IAC5B,aAAa;IACb,kBAAkB;;AAEtB;;;ACxCM,SAAU,SAId,aAAqD,CAAA,GAAE;AAEvD,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,qBAAqB,QAAQ;IAC3C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACdM,SAAU,eAMd,aAAoE,CAAA,GAAE;AAEtE,QAAM,EAAE,WAAW,aAAa,UAAU,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE/D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,2BAA2B,QAAQ;IACjD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,EAAE,aAAa,eAAe,YAAY,UAAU,MAAM,WAAW,KAAK;AAG5E,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AACH;;;ACjCM,SAAU,4BAKd,aAII,CAAA,GAAS;AAEb,QAAM,EAAE,MAAM,oBAAoB,QAAQ,CAAA,EAAE,IAAK;AAEjD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,wCAAwC,QAAQ;IAC9D,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,EAAE,QAAQ,wBACP,QAAQ,wBACR,MAAM,WAAW,KAAK;AAG3B,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACzBM,SAAU,oBAId,aAAgE,CAAA,GAAE;AAElE,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,gCAAgC,QAAQ;IACtD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACdM,SAAU,sBAMd,aAA2E,CAAA,GAAE;AAE7E,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,kCAAkC,QAAQ;IACxD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AACH;;;AC9BM,SAAU,iBAId,aAA6D,CAAA,GAAE;AAE/D,QAAM,EAAE,SAAS,SAAS,WAAW,QAAQ,CAAA,EAAE,IAAK;AAEpD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,0BAA0B,QAAQ;IAChD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,WAAW,WAAW,cAAc,MAAM,WAAW,KAAK;AAG5D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACpBM,SAAU,mBAMd,aAKI,CAAA,GAAS;AAEb,QAAM,EACJ,SACA,SACA,aACA,WACA,OACA,QAAQ,CAAA,EAAE,IACR;AAEJ,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,4BACd,QACA;IACE,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AAEH,QAAM,UAAU,QACd,WACE,WACA,eACA,aACA,UACC,MAAM,WAAW,KAAK;AAG3B,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACzCM,SAAU,sBAId,YAA+D;AAE/D,QAAM,EAAE,IAAI,QAAQ,CAAA,EAAE,IAAK;AAE3B,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,UAAU,+BAA+B,QAAQ,UAAU;AACjE,QAAM,UAAU,QAAQ,OAAO,MAAM,WAAW,KAAK;AAErD,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACRM,SAAU,6BAMd,aAII,CAAA,GAAE;AAEN,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,sCAAsC,QAAQ;IAC5D,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AACH;;;ACtDA,IAAAC,iBAAkC;AA4C5B,SAAU,gBAMd,aAAqE,CAAA,GAAE;AAEvE,QAAM,EAAE,QAAQ,CAAA,GAAI,GAAG,KAAI,IAAK;AAEhC,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,cAAc,eAAc;AAClC,QAAM,EAAE,SAAS,WAAW,OAAM,IAAK,WAAW,EAAE,OAAM,CAAE;AAC5D,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AACrC,QAAM,kBAAkB,WAAW,aAAa;AAEhD,QAAM,EAAE,UAAU,GAAG,QAAO,IAAK,4BAC/B,QACA;IACE,GAAG;IACH,SAAS,WAAW,WAAW;IAC/B,WAAW,WAAW,aAAa;GACpC;AAEH,QAAM,UAAU,SACb,WAAW,eACT,WAAW,kBAAkB,iBAAiB,iBAC9C,MAAM,WAAW,KAAK;AAG3B,QAAM,iBAAa,uBAAO,OAAO;AAEjC,gCAAU,MAAK;AACb,UAAM,kBAAkB,WAAW;AACnC,QAAI,CAAC,WAAW,iBAAiB;AAE/B,kBAAY,cAAc,EAAE,SAAQ,CAAE;AACtC,iBAAW,UAAU;IACvB,WAAW,YAAY,iBAAiB;AAEtC,kBAAY,kBAAkB,EAAE,SAAQ,CAAE;AAC1C,iBAAW,UAAU;IACvB;EACF,GAAG,CAAC,SAAS,WAAW,CAAC;AAEzB,SAAOC,UAAS;IACd,GAAG;IACH,GAAG;IACH;IACA;IACA,WAAW,OAAO;GACZ;AACV;;;ACrEM,SAAU,cACd,aAA+C,CAAA,GAAE;AAEjD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,0BAA0B,MAAM;AACxD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,YAAY;IACZ,iBAAiB;;AAErB;;;ACvDA,IAAAC,iBAA0B;AAyBpB,SAAU,sBAQd,aAMI,CAAA,GAAS;AAEb,QAAM,EAAE,UAAU,MAAM,QAAQ,QAAQ,GAAG,GAAG,KAAI,IAAK;AAEvD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAItC,gCAAU,MAAK;AACb,QAAI,CAAC;AAAS;AACd,QAAI,CAAC;AAAQ;AACb,WAAO,mBAAmB,QAAQ;MAChC,GAAI;MACJ;MACA;KACD;EACH,GAAG;IACD;IACA;IACA;IACA;;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;GACN;AACH;;;AC3EA,IAAAC,iBAA0B;AAmBpB,SAAU,4BAKd,aAGI,CAAA,GAAS;AAEb,QAAM,EAAE,UAAU,MAAM,gBAAgB,QAAQ,GAAG,GAAG,KAAI,IAAK;AAE/D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAItC,gCAAU,MAAK;AACb,QAAI,CAAC;AAAS;AACd,QAAI,CAAC;AAAgB;AACrB,WAAO,yBAAyB,QAAQ;MACtC,GAAI;MACJ;MACA;KACD;EACH,GAAG;IACD;IACA;IACA;IACA;;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;GACN;AACH;;;ACFM,SAAU,iBAId,aAA0D,CAAA,GAAE;AAE5D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,6BAA6B,MAAM;AAC3D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,eAAe;IACf,oBAAoB;;AAExB;", "names": ["useEffect", "useSyncExternalStore", "useRef", "useEffect", "useMemo", "import_react", "BaseError", "BaseError", "import_react", "import_react", "import_react", "isPlainObject", "structuralSharing", "useQuery", "useInfiniteQuery", "import_react", "useQuery", "import_react", "useQuery", "import_react", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "import_react", "import_with_selector", "import_react", "import_react", "import_react", "import_react", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useInfiniteQuery", "useQuery", "useQuery", "import_with_selector", "useQuery", "import_react", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "import_react", "useQuery", "import_react", "import_react"]}