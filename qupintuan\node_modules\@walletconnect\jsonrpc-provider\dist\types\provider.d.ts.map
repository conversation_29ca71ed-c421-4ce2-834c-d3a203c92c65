{"version": 3, "file": "provider.d.ts", "sourceRoot": "", "sources": ["../../src/provider.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EACL,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,EACd,cAAc,EAMf,MAAM,8BAA8B,CAAC;AAEtC,qBAAa,eAAgB,SAAQ,gBAAgB;IAC5C,MAAM,eAAsB;IAE5B,UAAU,EAAE,kBAAkB,CAAC;IAEtC,OAAO,CAAC,2BAA2B,CAAS;gBAEhC,UAAU,EAAE,kBAAkB;IAQ7B,OAAO,CAAC,UAAU,GAAE,MAAM,GAAG,kBAAoC,GAAG,OAAO,CAAC,IAAI,CAAC;IAIjF,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAIjC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,IAAI;IAItC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,IAAI;IAIxC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,IAAI;IAIvC,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,IAAI;IAI5C,OAAO,CAAC,MAAM,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,EAC7C,OAAO,EAAE,gBAAgB,CAAC,MAAM,CAAC,EACjC,OAAO,CAAC,EAAE,GAAG,GACZ,OAAO,CAAC,MAAM,CAAC;cAaF,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,EACtD,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC,EAC/B,OAAO,CAAC,EAAE,GAAG,GACZ,OAAO,CAAC,MAAM,CAAC;IAwBlB,SAAS,CAAC,aAAa,CAAC,UAAU,GAAE,kBAAoC;IAIxE,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,cAAc,GAAG,IAAI;IAYlD,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,UAAU,GAAG,IAAI;cAe3B,IAAI,CAAC,UAAU,GAAE,MAAM,GAAG,kBAAoC;cAa9D,KAAK;IAMrB,OAAO,CAAC,sBAAsB;CAQ/B;AAED,eAAe,eAAe,CAAC"}