function y(e,t){try{const o=`lottery_${e}`,r={...t,savedAt:Date.now(),version:"1.0"};return localStorage.setItem(o,JSON.stringify(r)),u(e,r),!0}catch{return!1}}function s(e){try{const t=`lottery_${e}`,o=localStorage.getItem(t);return o?JSON.parse(o):f(e)}catch{return null}}function u(e,t){try{const o="all_lottery_info",r=JSON.parse(localStorage.getItem(o)||"{}");r[e]=t,localStorage.setItem(o,JSON.stringify(r))}catch{}}function f(e){try{const o=JSON.parse(localStorage.getItem("all_lottery_info")||"{}");return o[e]?o[e]:null}catch{return null}}function g(e){const t=s(e);return!!(t&&t.txHash&&t.winner)}function p(e){try{const t=`lottery_${e}`;localStorage.removeItem(t);const o="all_lottery_info",r=JSON.parse(localStorage.getItem(o)||"{}");return r[e]&&(delete r[e],localStorage.setItem(o,JSON.stringify(r))),!0}catch{return!1}}function c(e){if(!e||!e.id)return e;try{const t=s(e.id);if(t){let o=0;if(e.createTime)o=Date.now()-new Date(e.createTime).getTime();else if(e.createdAt){const a=typeof e.createdAt=="number"?e.createdAt*1e3:new Date(e.createdAt).getTime();o=Date.now()-a}if(o<300*1e3&&(!e.participants||e.participants.length===0)||!e.participants||e.participants.length<8||e.roomType==="qpt-buyback"&&!e.locked)return e;const n={...e};return!n.readyForWinner&&t.txHash&&(n.readyForWinner=!0),!n.lotteryTxHash&&t.txHash&&(n.lotteryTxHash=t.txHash),!n.lotteryTimestamp&&t.timestamp&&(n.lotteryTimestamp=t.timestamp),!n.calculatedWinner&&t.winner&&(n.calculatedWinner=t.winner),t.txHash&&t.winner&&(n.lotteryInfo={readyForWinner:!0,winner:t.winner,lotteryTxHash:t.txHash,lotteryTimestamp:t.timestamp||0,winnerIndex:t.winnerIndex||0,txHash:t.txHash}),n._lotteryInfoSource="local_storage",n._lotteryInfoEnhanced=!0,n}return e}catch{return e}}function d(e){return Array.isArray(e)?e.map(t=>c(t)):e}function h(e=720*60*60*1e3){try{const t="all_lottery_info",o=JSON.parse(localStorage.getItem(t)||"{}"),r=Date.now();let n=0;return Object.keys(o).forEach(a=>{const l=o[a];l.savedAt&&r-l.savedAt>e&&(delete o[a],localStorage.removeItem(`lottery_${a}`),n++)}),n>0&&(localStorage.setItem(t,JSON.stringify(o)),console.log(`🧹 已清理 ${n} 个过期的开奖信息`)),n}catch(t){return console.error("清理过期开奖信息失败:",t),0}}function i(){try{const t=JSON.parse(localStorage.getItem("all_lottery_info")||"{}"),o={totalRooms:Object.keys(t).length,roomIds:Object.keys(t).map(n=>parseInt(n)),oldestSaveTime:null,newestSaveTime:null},r=Object.values(t).map(n=>n.savedAt).filter(n=>n);return r.length>0&&(o.oldestSaveTime=Math.min(...r),o.newestSaveTime=Math.max(...r)),o}catch(e){return console.error("获取开奖信息统计失败:",e),{totalRooms:0,roomIds:[],oldestSaveTime:null,newestSaveTime:null}}}function m(){console.log("🔍 所有开奖信息调试:");const e=i();console.log("📊 统计信息:",e),e.totalRooms>0?(console.log("📋 详细信息:"),e.roomIds.forEach(t=>{const o=s(t);console.log(`   房间 #${t}:`,o)})):console.log("📭 没有找到任何开奖信息")}function S(e){console.log(`🔍 检查房间 #${e} 的开奖信息:`);try{const t=localStorage.getItem(`lottery_${e}`);console.log("📦 localStorage:",t?JSON.parse(t):"无数据")}catch(t){console.log("❌ localStorage 读取失败:",t)}try{const t=sessionStorage.getItem(`lottery_session_${e}`);console.log("📦 sessionStorage:",t?JSON.parse(t):"无数据")}catch(t){console.log("❌ sessionStorage 读取失败:",t)}try{const t=document.cookie.split(";"),o=`lottery_cookie_${e}=`;let r=null;for(let n of t)if(n=n.trim(),n.startsWith(o)){const a=n.substring(o.length);r=JSON.parse(atob(a));break}console.log("🍪 cookie:",r||"无数据")}catch(t){console.log("❌ cookie 读取失败:",t)}try{const t=window._lotteryHashData?.[e];console.log("🔗 URL hash:",t||"无数据")}catch(t){console.log("❌ URL hash 读取失败:",t)}}typeof window<"u"&&(window.lotteryInfoDebug={saveLotteryInfo:y,getLotteryInfo:s,hasLotteryInfo:g,enhanceRoomWithLotteryInfo:c,cleanupExpiredLotteryInfo:h,getLotteryInfoStats:i,debugShowAllLotteryInfo:m,debugCheckRoomLotteryInfo:S});export{h as cleanupExpiredLotteryInfo,p as clearLotteryInfo,S as debugCheckRoomLotteryInfo,m as debugShowAllLotteryInfo,c as enhanceRoomWithLotteryInfo,d as enhanceRoomsWithLotteryInfo,s as getLotteryInfo,i as getLotteryInfoStats,g as hasLotteryInfo,y as saveLotteryInfo};
