"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.hardforks = void 0;
exports.hardforks = {
    chainstart: require('./chainstart.json'),
    homestead: require('./homestead.json'),
    dao: require('./dao.json'),
    tangerineWhistle: require('./tangerineWhistle.json'),
    spuriousDragon: require('./spuriousDragon.json'),
    byzantium: require('./byzantium.json'),
    constantinople: require('./constantinople.json'),
    petersburg: require('./petersburg.json'),
    istanbul: require('./istanbul.json'),
    muirGlacier: require('./muirGlacier.json'),
    berlin: require('./berlin.json'),
    london: require('./london.json'),
    shanghai: require('./shanghai.json'),
    arrowGlacier: require('./arrowGlacier.json'),
    grayGlacier: require('./grayGlacier.json'),
    mergeForkIdTransition: require('./mergeForkIdTransition.json'),
    merge: require('./merge.json'),
    shardingFork: require('./sharding.json'),
};
//# sourceMappingURL=index.js.map