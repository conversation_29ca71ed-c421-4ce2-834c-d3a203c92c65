const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-C8294aMB-*************-wb8rvug16.js","assets/web3-uglCpOhK-*************-7ns772q62.js","assets/vendor-CgHzTxSQ-*************-7ns772q62.js","assets/ui-CbWGv3YI-*************-7ns772q62.js","assets/index-QZjJZq-p-*************-mniiclbh2.css"])))=>i.map(i=>d[i]);
import{_ as d,c as w,h as S,d as T,e as m}from"./web3-uglCpOhK-*************-7ns772q62.js";import{c as i}from"./ui-CbWGv3YI-*************-7ns772q62.js";import{b as u}from"./index-C8294aMB-*************-wb8rvug16.js";import"./vendor-CgHzTxSQ-*************-7ns772q62.js";const g=async t=>{try{if(!t||typeof t!="string")return console.warn("getUserAgentInfo: 无效的用户地址参数:",t),{level:0,rewardPercent:5};const{CONTRACT_ADDRESSES:a,ABIS:n}=await d(async()=>{const{CONTRACT_ADDRESSES:s,ABIS:l}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(p=>p.o);return{CONTRACT_ADDRESSES:s,ABIS:l}},__vite__mapDeps([0,1,2,3,4])),e=a[97].AgentSystem,c=await w({chain:u,transport:S()}).readContract({address:e,abi:n.AgentSystem,functionName:"getLevel",args:[t]}),o=5+Number(c)*5;return{level:Number(c),rewardPercent:o}}catch(a){return console.warn("获取代理信息失败:",a),{level:0,rewardPercent:5}}},h=async(t,a)=>{if(!a)return!1;try{const{CONTRACT_ADDRESSES:n,ABIS:e}=await d(async()=>{const{CONTRACT_ADDRESSES:l,ABIS:p}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(b=>b.o);return{CONTRACT_ADDRESSES:l,ABIS:p}},__vite__mapDeps([0,1,2,3,4])),r=T({chain:u,transport:m(window.ethereum)}),c=n[97].QPTBuyback,o=await r.writeContract({address:c,abi:e.QPTBuyback,functionName:"finalizeTimeout",args:[BigInt(t)],account:a});return i.success("处理过期房间交易已提交，等待确认..."),await w({chain:u,transport:S("https://bsc-testnet.public.blastapi.io")}).waitForTransactionReceipt({hash:o}),i.success(`房间 #${t} 过期处理成功！`),!0}catch(n){return console.error("处理过期房间失败:",n),i.error("处理过期房间失败: "+(n.message||"未知错误")),!1}},R=async(t,a,n)=>{try{const{CONTRACT_ADDRESSES:e,ABIS:r}=await d(async()=>{const{CONTRACT_ADDRESSES:b,ABIS:A}=await import("./index-C8294aMB-*************-wb8rvug16.js").then(f=>f.o);return{CONTRACT_ADDRESSES:b,ABIS:A}},__vite__mapDeps([0,1,2,3,4])),c=T({chain:u,transport:m(window.ethereum)}),o=e[97].QPTBuyback;let s;switch(n){case"failed":s="refundFailed";break;case"success":s="refundSuccess";break;case"expired":s="refundExpired";break;default:throw new Error("未知的退款类型")}const l=await c.writeContract({address:o,abi:r.QPTBuyback,functionName:s,args:[BigInt(t)],account:a});return i.success("退款交易已提交，等待确认..."),await w({chain:u,transport:S("https://bsc-testnet.public.blastapi.io")}).waitForTransactionReceipt({hash:l}),i.success(`房间 #${t} QPT退款成功！`),!0}catch(e){console.error("QPT退款失败:",e);let r="QPT退款失败";throw e.message.includes("User rejected")?r="用户取消了交易":e.message.includes("Already refunded")?r="已经退款过了":e.message.includes("Not a participant")&&(r="您不是此房间的参与者"),i.error(r),e}},P=h;export{P as finalizeTimeoutRoom,g as getUserAgentInfo,h as handleExpireRoom,R as refundQPTBuyback};
