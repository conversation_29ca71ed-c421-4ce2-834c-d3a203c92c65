"use client";

// src/wallets/walletConnectors/subWallet/subWallet.svg
var subWallet_default = "data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2228%22%20height%3D%2228%22%3E%3Cdefs%3E%3ClinearGradient%20id%3D%22a%22%20x1%3D%22352%22%20x2%3D%22352%22%20y1%3D%220%22%20y2%3D%22704%22%20gradientTransform%3D%22scale(.03977)%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%3Cstop%20offset%3D%220%22%20style%3D%22stop-color%3A%23004bff%3Bstop-opacity%3A1%22%2F%3E%3Cstop%20offset%3D%221%22%20style%3D%22stop-color%3A%234ceaac%3Bstop-opacity%3A1%22%2F%3E%3C%2FlinearGradient%3E%3CclipPath%20id%3D%22b%22%3E%3Cpath%20d%3D%22M7.637%204.453h12.726v19.094H7.637Zm0%200%22%2F%3E%3C%2FclipPath%3E%3C%2Fdefs%3E%3Cpath%20d%3D%22M0%200h28v28H0z%22%20style%3D%22fill%3Aurl(%23a)%3Bstroke%3Anone%22%2F%3E%3Cg%20clip-path%3D%22url(%23b)%22%3E%3Cpath%20d%3D%22M20.363%2011.36V8.71L9.753%204.454%207.638%205.531l.011%208.246%207.934%203.196-4.238%201.804v-1.394l-1.946-.793-1.75.828v5.05l2.11%201.079%2010.605-4.785v-3.395l-9.547-3.82v-2.32l7.575%203.027Zm0%200%22%20style%3D%22stroke%3Anone%3Bfill-rule%3Anonzero%3Bfill%3A%23fff%3Bfill-opacity%3A1%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%0A";
export {
  subWallet_default as default
};
