const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/web3-uglCpOhK-1754367538540-7ns772q62.js","assets/vendor-CgHzTxSQ-1754367538540-7ns772q62.js","assets/ui-CbWGv3YI-1754367538540-7ns772q62.js","assets/index-C8294aMB-1754367538540-wb8rvug16.js","assets/index-QZjJZq-p-1754367538540-mniiclbh2.css"])))=>i.map(i=>d[i]);
import{_ as b}from"./web3-uglCpOhK-1754367538540-7ns772q62.js";import"./vendor-CgHzTxSQ-1754367538540-7ns772q62.js";import"./ui-CbWGv3YI-1754367538540-7ns772q62.js";const h=["https://bsc-testnet.public.blastapi.io","https://data-seed-prebsc-1-s1.binance.org:8545","https://data-seed-prebsc-2-s1.binance.org:8545","https://bsc-testnet.blockpi.network/v1/rpc/public","https://data-seed-prebsc-1-s2.binance.org:8545","https://data-seed-prebsc-2-s2.binance.org:8545","https://data-seed-prebsc-1-s3.binance.org:8545","https://data-seed-prebsc-2-s3.binance.org:8545"],f=["https://bsc-dataseed1.binance.org","https://bsc-dataseed2.binance.org","https://bsc-dataseed3.binance.org","https://bsc-dataseed4.binance.org","https://bsc-dataseed1.defibit.io","https://bsc-dataseed2.defibit.io","https://bsc-dataseed3.defibit.io","https://bsc-dataseed4.defibit.io"];async function l(e,s=5e3){try{const r=new AbortController,n=setTimeout(()=>r.abort(),s),a=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({jsonrpc:"2.0",method:"eth_blockNumber",params:[],id:1}),signal:r.signal});if(clearTimeout(n),a.ok){const t=await a.json();return t.result&&t.result.startsWith("0x")}return!1}catch{return!1}}async function g(e){const s=e===97?h:f,r=s.map(async(n,a)=>await l(n,3e3)?{rpc:n,priority:a}:null);try{const a=(await Promise.allSettled(r)).map(t=>t.status==="fulfilled"?t.value:null).filter(Boolean).sort((t,c)=>t.priority-c.priority);if(a.length>0)return a[0].rpc}catch{}return s[0]}async function m(e){const s=e===97?"https://bsc-testnet.public.blastapi.io":"https://bsc-dataseed1.binance.org";return await l(s,3e3)?s:await g(e)}async function T(e=97,s={}){const{timeout:r=1e4,retryCount:n=3,retryDelay:a=1e3}=s,{createPublicClient:t,http:c}=await b(async()=>{const{createPublicClient:i,http:p}=await import("./web3-uglCpOhK-1754367538540-7ns772q62.js").then(u=>u.aL);return{createPublicClient:i,http:p}},__vite__mapDeps([0,1,2])),{bscTestnet:d}=await b(async()=>{const{bscTestnet:i}=await import("./index-C8294aMB-1754367538540-wb8rvug16.js").then(p=>p.m);return{bscTestnet:i}},__vite__mapDeps([3,0,1,2,4]));let o;try{o=await m(e)}catch{o=e===97?"https://data-seed-prebsc-1-s1.binance.org:8545":"https://bsc-dataseed1.binance.org"}return t({chain:d,transport:c(o,{timeout:r,retryCount:n,retryDelay:a})})}export{T as createRpcClient,g as getFastestRpc,m as getRpcUrl};
