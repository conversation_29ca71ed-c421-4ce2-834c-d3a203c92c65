const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-BDw84Puy.js","assets/web3-DnWbColA.js","assets/vendor-CgHzTxSQ.js","assets/ui-B9ZzSjJF.js","assets/index-QZjJZq-p.css"])))=>i.map(i=>d[i]);
import{_ as m}from"./web3-DnWbColA.js";import"./vendor-CgHzTxSQ.js";import"./ui-B9ZzSjJF.js";async function C(){try{const n=`groupBuyPoints_${new Date().toDateString()}`,i=localStorage.getItem(n);if(i){const s=JSON.parse(i);return{dailyGroupBuyPointsIssued:s.dailyGroupBuyPointsIssued||"0",source:"本地缓存",timestamp:s.timestamp,cacheKey:n}}const u=await D(),d={dailyGroupBuyPointsIssued:u.dailyGroupBuyPointsIssued,timestamp:Date.now(),calculatedAt:new Date().toISOString()};return localStorage.setItem(n,JSON.stringify(d)),{dailyGroupBuyPointsIssued:u.dailyGroupBuyPointsIssued,source:"实时计算",timestamp:d.timestamp,cacheKey:n}}catch(a){return console.error("❌ [AdminStatsSync] 获取今日拼团积分失败:",a),{dailyGroupBuyPointsIssued:"0",source:"错误回退",error:a.message,timestamp:Date.now()}}}async function D(){try{const{CONTRACT_ADDRESSES:a,ABIS:n}=await m(async()=>{const{CONTRACT_ADDRESSES:t,ABIS:o}=await import("./index-BDw84Puy.js").then(e=>e.o);return{CONTRACT_ADDRESSES:t,ABIS:o}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:i,http:u}=await m(async()=>{const{createPublicClient:t,http:o}=await import("./web3-DnWbColA.js").then(e=>e.aL);return{createPublicClient:t,http:o}},__vite__mapDeps([1,2,3])),{bscTestnet:d}=await m(async()=>{const{bscTestnet:t}=await import("./index-BDw84Puy.js").then(o=>o.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4])),s=i({chain:d,transport:u()}),l=a[97].PointsManagement,c=new Date,S=new Date(c.getFullYear(),c.getMonth(),c.getDate()),_=new Date(S.getTime()-1440*60*1e3);try{const t=await s.readContract({address:l,abi:n.PointsManagement,functionName:"pointsStats"}),o=`pointsStats_${_.toDateString()}`;let e=0;try{const y=localStorage.getItem(o);if(y){const g=JSON.parse(y);e=Number(g.totalGroupBuyPoints||0)}}catch(y){console.warn("无法读取昨日缓存:",y)}const r=Number(t.totalGroupBuyPoints);return{dailyGroupBuyPointsIssued:Math.max(0,r-e).toString(),currentTotal:r.toString(),yesterdayTotal:e.toString(),calculationMethod:"合约统计数据差值计算"}}catch(t){console.error("❌ [AdminStatsSync] 查询合约失败:",t);const o=await T();let e=0;for(const r of o)e+=r.tier||30;return{dailyGroupBuyPointsIssued:e.toString(),completedRooms:o.length,calculationMethod:"完成房间档位积分累计"}}}catch(a){return console.error("❌ [AdminStatsSync] 计算今日积分失败:",a),{dailyGroupBuyPointsIssued:"0",error:a.message}}}async function T(){try{const{CONTRACT_ADDRESSES:a,ABIS:n}=await m(async()=>{const{CONTRACT_ADDRESSES:t,ABIS:o}=await import("./index-BDw84Puy.js").then(e=>e.o);return{CONTRACT_ADDRESSES:t,ABIS:o}},__vite__mapDeps([0,1,2,3,4])),{createPublicClient:i,http:u}=await m(async()=>{const{createPublicClient:t,http:o}=await import("./web3-DnWbColA.js").then(e=>e.aL);return{createPublicClient:t,http:o}},__vite__mapDeps([1,2,3])),{bscTestnet:d}=await m(async()=>{const{bscTestnet:t}=await import("./index-BDw84Puy.js").then(o=>o.m);return{bscTestnet:t}},__vite__mapDeps([0,1,2,3,4])),s=i({chain:d,transport:u()}),l=a[97].GroupBuyRoom,c=new Date,S=new Date(c.getFullYear(),c.getMonth(),c.getDate()),_=Math.floor(S.getTime()/1e3);try{const t=await s.readContract({address:l,abi:n.GroupBuyRoom,functionName:"totalRooms"}),o=[],e=Math.min(Number(t),100);for(let r=Math.max(0,Number(t)-e);r<Number(t);r++)try{const p=await s.readContract({address:l,abi:n.GroupBuyRoom,functionName:"getRoom",args:[BigInt(r)]}),[y,g,P,I,w,h]=p;w&&Number(h)>=_&&o.push({id:r,tier:Number(g)/1e6,completedAt:Number(h)*1e3,winner:I,participantCount:P.length})}catch(p){console.warn(`查询房间 ${r} 失败:`,p.message)}return o}catch(t){return console.error("❌ [AdminStatsSync] 查询合约房间失败:",t),[]}}catch(a){return console.error("❌ [AdminStatsSync] 获取今日完成房间失败:",a),[]}}export{C as getTodayGroupBuyPointsIssued};
