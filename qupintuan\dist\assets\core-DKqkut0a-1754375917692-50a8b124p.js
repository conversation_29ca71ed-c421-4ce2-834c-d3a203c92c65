const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/roomManagement-BZPf_gXk-1754375917692-50a8b124p.js","assets/vendor-D7uqzx8C-1754375917692-50a8b124p.js"])))=>i.map(i=>d[i]);
import{_ as y}from"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";import{E as h,l as T,s as k,i as f}from"./index-CaV4ohF9-1754375917692-opz9ain10.js";import{fetchTotalRooms as v,fetchRoom as D}from"./basicOperations-BOq0NLuf-1754375917692-50a8b124p.js";import{createRoomWithQPTVerification as Q,approveQPTForCreate as C,lockQPTForCreate as E,joinRoomWithPaymentVerification as B}from"./roomManagement-BZPf_gXk-1754375917692-50a8b124p.js";import{e as M}from"./rewardOperations-I9R1_Rnl-1754375917692-50a8b124p.js";import{v as L,p as O,a as R,t as g,e as p}from"./transaction-DfFoEzbA-1754375917692-50a8b124p.js";import"./web3-NCUyEZtP-1754375917692-50a8b124p.js";class S{constructor(){this.metrics=new Map,this.networkRequests=[],this.isEnabled=!1}startTimer(t){this.isEnabled&&this.metrics.set(t,{startTime:performance.now(),endTime:null,duration:null})}endTimer(t){if(!this.isEnabled)return;const e=this.metrics.get(t);if(e){e.endTime=performance.now(),e.duration=e.endTime-e.startTime;const s=`${t}_${Math.floor(e.duration/1e3)}s`,o=this.lastLogTimes?.get(s)||0,a=Date.now();if(a-o<5e3)return;this.lastLogTimes||(this.lastLogTimes=new Map),this.lastLogTimes.set(s,a),e.duration>8e3||e.duration>5e3}}recordNetworkRequest(t,e="GET",s=0,o=!0){this.isEnabled&&(this.networkRequests.push({url:t,method:e,duration:s,success:o,timestamp:Date.now()}),this.networkRequests.length>100&&(this.networkRequests=this.networkRequests.slice(-100)),s>1e4&&console.warn(`🚨 [网络监控] 请求超时: ${t} (${s}ms)`))}getPerformanceReport(){if(!this.isEnabled)return null;const t=Array.from(this.metrics.entries()).filter(([a,i])=>i.duration!==null).map(([a,i])=>({name:a,duration:i.duration,status:i.duration>5e3?"慢":i.duration>2e3?"中等":"快"})).sort((a,i)=>i.duration-a.duration),e=this.networkRequests.slice(-20),s=this.networkRequests.filter(a=>!a.success),o=this.networkRequests.filter(a=>a.duration>5e3);return{timing:t,network:{total:this.networkRequests.length,recent:e,failed:s.length,slow:o.length,averageTime:this.networkRequests.length>0?this.networkRequests.reduce((a,i)=>a+i.duration,0)/this.networkRequests.length:0},recommendations:this.generateRecommendations(t,this.networkRequests)}}generateRecommendations(t,e){const s=[],o=t.filter(r=>r.duration>5e3);o.length>0&&s.push({type:"性能优化",message:`发现 ${o.length} 个慢操作，建议优化: ${o.map(r=>r.name).join(", ")}`});const a=e.filter(r=>!r.success).length/e.length;a>.1&&s.push({type:"网络优化",message:`网络请求失败率过高 (${(a*100).toFixed(1)}%)，建议检查RPC节点或网络连接`});const i=e.length>0?e.reduce((r,u)=>r+u.duration,0)/e.length:0;return i>3e3&&s.push({type:"网络优化",message:`平均请求时间过长 (${i.toFixed(0)}ms)，建议优化RPC配置或使用更快的节点`}),s}clear(){this.metrics.clear(),this.networkRequests=[]}}const P=new S,w=n=>P.startTimer(n),q=n=>P.endTimer(n),m=(n,t,e,s)=>P.recordNetworkRequest(n,t,e,s);class b{constructor(){this.pendingRequests=new Map,this.requestCounts=new Map,this.lastRequestTimes=new Map}async deduplicate(t,e,s=1e3){const o=Date.now();if(this.pendingRequests.has(t))return this.pendingRequests.get(t);const a=this.lastRequestTimes.get(t)||0;if(o-a<s)return null;const i=(this.requestCounts.get(t)||0)+1;if(this.requestCounts.set(t,i),i>10&&(console.warn(`⚠️ [请求去重] 请求过于频繁: ${t} (第${i}次)`),i>20))return this.lastRequestTimes.set(t,o+5e3),console.warn(`🚫 [请求去重] 请求被限制，强制延迟5秒: ${t}`),null;const r=(async()=>{try{return this.lastRequestTimes.set(t,o),await e()}catch(u){throw console.error(`❌ [请求去重] 请求失败: ${t}`,u),u}finally{this.pendingRequests.delete(t),setTimeout(()=>{this.requestCounts.delete(t)},300*1e3)}})();return this.pendingRequests.set(t,r),r}clear(t){this.pendingRequests.delete(t),this.requestCounts.delete(t),this.lastRequestTimes.delete(t)}clearAll(){this.pendingRequests.clear(),this.requestCounts.clear(),this.lastRequestTimes.clear()}getStats(){return{pendingCount:this.pendingRequests.size,totalRequests:Array.from(this.requestCounts.values()).reduce((t,e)=>t+e,0),uniqueRequests:this.requestCounts.size,mostFrequentRequest:this.getMostFrequentRequest()}}getMostFrequentRequest(){let t=0,e=null;for(const[s,o]of this.requestCounts.entries())o>t&&(t=o,e=s);return e?{key:e,count:t}:null}}const x=new b,_=(n,t,e)=>x.deduplicate(n,t,e);async function U(n,t){console.log("📞 [groupBuyService] 调用 createAndLock，参数:",{tierAmountStr:t});try{L(n);const e=O(t),s=await n.getChainId();R(s);const o=Number(e)/1e6,a=[30,50,100,200,500,1e3];a.includes(o)||g(h.INVALID_TIER_AMOUNT,`不支持的拼团金额: ${o}，支持的档位: ${a.join(", ")} (原始值: ${t})`),T({component:"groupBuyService",function:"createAndLock",message:`开始创建拼团房间，金额: ${o}`,tierNum:o,chainId:s}),console.log("🚀 [createAndLock] 开始发起人固定三步操作流程");const{toast:i}=await y(async()=>{const{toast:l}=await import("./vendor-D7uqzx8C-1754375917692-50a8b124p.js").then(c=>c.a9);return{toast:l}},[]);console.log("📞 [createAndLock] 步骤 1/3: 创建拼团房间"),i.loading("第1步：正在创建拼团房间...",{id:"step1"});const r=await p(()=>Q({chainId:97,tier:o,signer:n}),"createRoomWithQPTVerification");console.log("✅ [createAndLock] 步骤 1/3: 拼团房间创建成功:",`房间ID: ${r.roomId}`),i.success(`✅ 第1步完成：拼团房间 #${r.roomId} 创建成功！`,{id:"step1"}),console.log("📞 [createAndLock] 步骤 2/3: 授权QPT给QPTLocker合约"),i.loading("第2步：正在授权QPT给锁仓合约...",{id:"step2"});const u=await p(()=>C({chainId:97,tier:o,signer:n}),"approveQPTForCreate");console.log("✅ [createAndLock] 步骤 2/3: QPT授权成功:",u.message),i.success("✅ 第2步完成：QPT授权成功！",{id:"step2"}),console.log("📞 [createAndLock] 步骤 3/3: 锁仓QPT"),i.loading("第3步：正在锁仓QPT...",{id:"step3"});const A=await p(()=>E({chainId:97,tier:o,roomId:r.roomId,signer:n}),"lockQPTForCreate");console.log("✅ [createAndLock] 步骤 3/3: QPT锁仓成功:",A.message),i.success("✅ 第3步完成：QPT锁仓成功！",{id:"step3"}),console.log("🔍 [createAndLock] createResult 调试信息:",{createResult:r,hasRoomId:r?.roomId!==void 0,hasReceipt:r?.receipt!==void 0,roomIdValue:r?.roomId,receiptHash:r?.receipt?.hash}),(!r||r.roomId===void 0||r.roomId===null||!r.receipt)&&(console.error("❌ [createAndLock] 创建结果验证失败:",{createResult:r,hasCreateResult:!!r,hasRoomId:r?.roomId!==void 0&&r?.roomId!==null,hasReceipt:!!r?.receipt,roomId:r?.roomId,roomIdType:typeof r?.roomId,receipt:r?.receipt}),g(h.CREATE_AND_LOCK_FAILED,"创建房间失败，未获取到有效的 roomId 或 receipt"));const{roomId:d,receipt:$}=r;console.log("🔍 [createAndLock] 验证QPT锁仓状态...");try{const{validateCreatorQPTLock:l}=await y(async()=>{const{validateCreatorQPTLock:I}=await import("./roomManagement-BZPf_gXk-1754375917692-50a8b124p.js");return{validateCreatorQPTLock:I}},__vite__mapDeps([0,1])),c=await l({chainId:97,roomId:d});if(!c.isValid)throw console.error("❌ [createAndLock] QPT锁仓验证失败:",c),new Error(`QPT锁仓验证失败: ${c.message}`);console.log("✅ [createAndLock] QPT锁仓验证通过:",{roomId:d,lockedAmount:c.amount,creator:c.creator})}catch(l){console.error("❌ [createAndLock] QPT锁仓验证异常:",l),i.error(`⚠️ QPT锁仓验证失败: ${l.message}`,{duration:8e3,position:"top-center"})}console.log("✅ [createAndLock] 发起人三步操作完成");try{const c=`lottery_${r.roomId}`;localStorage.getItem(c)&&localStorage.removeItem(c)}catch{}return T({component:"groupBuyService",function:"createAndLock",message:`发起人三步操作完成，房间ID: ${d}，交易哈希: ${$.hash}`,roomId:d,receipt:$,approveResult:u.message,lockResult:A.message}),console.log("🎉 [createAndLock] 发起人固定三步操作完成，房间创建成功"),i.success(`🎉 拼团房间创建完成！房间ID: ${r.roomId}`,{duration:6e3,position:"top-center",style:{background:"linear-gradient(135deg, #10b981 0%, #059669 100%)",color:"#fff",fontWeight:"bold",fontSize:"16px",padding:"16px 24px",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0,0,0,0.3)"}}),k("groupBuy",5e3),{receipt:$,roomId:d,createResult:`房间ID: ${r.roomId}`,approveResult:u.message,lockResult:A.message,message:"发起人三步操作完成：创建房间 → 授权QPT → 锁仓QPT"}}catch(e){f({component:"groupBuyService",function:"createAndLock",message:`创建拼团房间失败: ${e.message}`,error:e,tierAmountStr:t});const{toast:s}=await y(async()=>{const{toast:a}=await import("./vendor-D7uqzx8C-1754375917692-50a8b124p.js").then(i=>i.a9);return{toast:a}},[]);s.dismiss("step1"),s.dismiss("step2"),s.dismiss("step3");let o="创建拼团房间失败";throw e.message&&(e.message.includes("用户拒绝")||e.message.includes("User rejected")?o="用户取消了交易":e.message.includes("余额不足")||e.message.includes("insufficient funds")?o="余额不足，请检查BNB和QPT余额":e.message.includes("网络")||e.message.includes("network")?o="网络连接失败，请检查网络设置":o=`创建失败: ${e.message}`),s.error(o,{duration:6e3,position:"top-center"}),e}}async function G(n){const t=w("fetchTotalRooms");try{R(n);const e=await _(`fetchTotalRooms_${n}`,()=>v({chainId:n}),5e3);return m("fetchTotalRooms",!0),e}catch(e){throw m("fetchTotalRooms",!1),f({component:"groupBuyService",function:"fetchTotalRooms",message:`获取房间总数失败: ${e.message}`,error:e,chainId:n}),e}finally{q(t)}}async function H(n,t){const e=w("fetchRoom");try{R(n),(!t||t<=0)&&g(h.INVALID_ROOM_DATA,"无效的房间ID");const s=await _(`fetchRoom_${n}_${t}`,()=>D({chainId:n,roomId:t}),3e3);return m("fetchRoom",!0),s}catch(s){throw m("fetchRoom",!1),f({component:"groupBuyService",function:"fetchRoom",message:`获取房间信息失败: ${s.message}`,error:s,chainId:n,roomId:t}),s}finally{q(e)}}async function J({signer:n,chainId:t,roomId:e}){const s=w("joinRoom");try{L(n),R(t),(!e||e<=0)&&g(h.INVALID_ROOM_DATA,"无效的房间ID"),T({component:"groupBuyService",function:"joinRoom",message:`开始加入房间: ${e}`,chainId:t,roomId:e});const o=await p(()=>B({signer:n,chainId:t,roomId:e}),"joinRoom");return m("joinRoom",!0),k("groupBuy",3e3),o}catch(o){throw m("joinRoom",!1),f({component:"groupBuyService",function:"joinRoom",message:`加入房间失败: ${o.message}`,error:o,chainId:t,roomId:e}),o}finally{q(s)}}async function X({signer:n,chainId:t,roomId:e}){const s=w("claimReward");try{L(n),R(t),(!e||e<=0)&&g(h.INVALID_ROOM_DATA,"无效的房间ID"),T({component:"groupBuyService",function:"claimReward",message:`开始领取奖励，房间: ${e}`,chainId:t,roomId:e});const o=await p(()=>M({signer:n,chainId:t,roomId:e}),"claimReward");return m("claimReward",!0),k("groupBuy",3e3),o}catch(o){throw m("claimReward",!1),f({component:"groupBuyService",function:"claimReward",message:`领取奖励失败: ${o.message}`,error:o,chainId:t,roomId:e}),o}finally{q(s)}}export{X as claimReward,U as createAndLock,H as fetchRoom,G as fetchTotalRooms,J as joinRoom};
