{"version": 3, "file": "injected-connector.esm.js", "sources": ["../../../node_modules/babel-plugin-transform-async-to-promises/helpers.js", "../src/index.ts"], "sourcesContent": ["// A type of promise-like that resolves synchronously and supports only one observer\nexport const _Pact = /*#__PURE__*/(function() {\n\tfunction _Pact() {}\n\t_Pact.prototype.then = function(onFulfilled, onRejected) {\n\t\tconst result = new _Pact();\n\t\tconst state = this.s;\n\t\tif (state) {\n\t\t\tconst callback = state & 1 ? onFulfilled : onRejected;\n\t\t\tif (callback) {\n\t\t\t\ttry {\n\t\t\t\t\t_settle(result, 1, callback(this.v));\n\t\t\t\t} catch (e) {\n\t\t\t\t\t_settle(result, 2, e);\n\t\t\t\t}\n\t\t\t\treturn result;\n\t\t\t} else {\n\t\t\t\treturn this;\n\t\t\t}\n\t\t}\n\t\tthis.o = function(_this) {\n\t\t\ttry {\n\t\t\t\tconst value = _this.v;\n\t\t\t\tif (_this.s & 1) {\n\t\t\t\t\t_settle(result, 1, onFulfilled ? onFulfilled(value) : value);\n\t\t\t\t} else if (onRejected) {\n\t\t\t\t\t_settle(result, 1, onRejected(value));\n\t\t\t\t} else {\n\t\t\t\t\t_settle(result, 2, value);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t_settle(result, 2, e);\n\t\t\t}\n\t\t};\n\t\treturn result;\n\t}\n\treturn _Pact;\n})();\n\n// Settles a pact synchronously\nexport function _settle(pact, state, value) {\n\tif (!pact.s) {\n\t\tif (value instanceof _Pact) {\n\t\t\tif (value.s) {\n\t\t\t\tif (state & 1) {\n\t\t\t\t\tstate = value.s;\n\t\t\t\t}\n\t\t\t\tvalue = value.v;\n\t\t\t} else {\n\t\t\t\tvalue.o = _settle.bind(null, pact, state);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\tif (value && value.then) {\n\t\t\tvalue.then(_settle.bind(null, pact, state), _settle.bind(null, pact, 2));\n\t\t\treturn;\n\t\t}\n\t\tpact.s = state;\n\t\tpact.v = value;\n\t\tconst observer = pact.o;\n\t\tif (observer) {\n\t\t\tobserver(pact);\n\t\t}\n\t}\n}\n\nexport function _isSettledPact(thenable) {\n\treturn thenable instanceof _Pact && thenable.s & 1;\n}\n\n// Converts argument to a function that always returns a Promise\nexport function _async(f) {\n\treturn function() {\n\t\tfor (var args = [], i = 0; i < arguments.length; i++) {\n\t\t\targs[i] = arguments[i];\n\t\t}\n\t\ttry {\n\t\t\treturn Promise.resolve(f.apply(this, args));\n\t\t} catch(e) {\n\t\t\treturn Promise.reject(e);\n\t\t}\n\t}\n}\n\n// Awaits on a value that may or may not be a Promise (equivalent to the await keyword in ES2015, with continuations passed explicitly)\nexport function _await(value, then, direct) {\n\tif (direct) {\n\t\treturn then ? then(value) : value;\n\t}\n\tif (!value || !value.then) {\n\t\tvalue = Promise.resolve(value);\n\t}\n\treturn then ? value.then(then) : value;\n}\n\n// Awaits on a value that may or may not be a Promise, then ignores it\nexport function _awaitIgnored(value, direct) {\n\tif (!direct) {\n\t\treturn value && value.then ? value.then(_empty) : Promise.resolve();\n\t}\n}\n\n// Proceeds after a value has resolved, or proceeds immediately if the value is not thenable\nexport function _continue(value, then) {\n\treturn value && value.then ? value.then(then) : then(value);\n}\n\n// Proceeds after a value has resolved, or proceeds immediately if the value is not thenable\nexport function _continueIgnored(value) {\n\tif (value && value.then) {\n\t\treturn value.then(_empty);\n\t}\n}\n\n// Asynchronously iterate through an object that has a length property, passing the index as the first argument to the callback (even as the length property changes)\nexport function _forTo(array, body, check) {\n\tvar i = -1, pact, reject;\n\tfunction _cycle(result) {\n\t\ttry {\n\t\t\twhile (++i < array.length && (!check || !check())) {\n\t\t\t\tresult = body(i);\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.then(_cycle, reject || (reject = _settle.bind(null, pact = new _Pact(), 2)));\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (pact) {\n\t\t\t\t_settle(pact, 1, result);\n\t\t\t} else {\n\t\t\t\tpact = result;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\t_settle(pact || (pact = new _Pact()), 2, e);\n\t\t}\n\t}\n\t_cycle();\n\treturn pact;\n}\n\n// Asynchronously iterate through an object's properties (including properties inherited from the prototype)\n// Uses a snapshot of the object's properties\nexport function _forIn(target, body, check) {\n\tvar keys = [];\n\tfor (var key in target) {\n\t\tkeys.push(key);\n\t}\n\treturn _forTo(keys, function(i) { return body(keys[i]); }, check);\n}\n\n// Asynchronously iterate through an object's own properties (excluding properties inherited from the prototype)\n// Uses a snapshot of the object's properties\nexport function _forOwn(target, body, check) {\n\tvar keys = [];\n\tfor (var key in target) {\n\t\tif (Object.prototype.hasOwnProperty.call(target, key)) {\n\t\t\tkeys.push(key);\n\t\t}\n\t}\n\treturn _forTo(keys, function(i) { return body(keys[i]); }, check);\n}\n\nexport const _iteratorSymbol = /*#__PURE__*/ typeof Symbol !== \"undefined\" ? (Symbol.iterator || (Symbol.iterator = Symbol(\"Symbol.iterator\"))) : \"@@iterator\";\n\n// Asynchronously iterate through an object's values\n// Uses for...of if the runtime supports it, otherwise iterates until length on a copy\nexport function _forOf(target, body, check) {\n\tif (typeof target[_iteratorSymbol] === \"function\") {\n\t\tvar iterator = target[_iteratorSymbol](), step, pact, reject;\n\t\tfunction _cycle(result) {\n\t\t\ttry {\n\t\t\t\twhile (!(step = iterator.next()).done && (!check || !check())) {\n\t\t\t\t\tresult = body(step.value);\n\t\t\t\t\tif (result && result.then) {\n\t\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresult.then(_cycle, reject || (reject = _settle.bind(null, pact = new _Pact(), 2)));\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (pact) {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t} else {\n\t\t\t\t\tpact = result;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\t_settle(pact || (pact = new _Pact()), 2, e);\n\t\t\t}\n\t\t}\n\t\t_cycle();\n\t\tif (iterator.return) {\n\t\t\tvar _fixup = function(value) {\n\t\t\t\ttry {\n\t\t\t\t\tif (!step.done) {\n\t\t\t\t\t\titerator.return();\n\t\t\t\t\t}\n\t\t\t\t} catch(e) {\n\t\t\t\t}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t\tif (pact && pact.then) {\n\t\t\t\treturn pact.then(_fixup, function(e) {\n\t\t\t\t\tthrow _fixup(e);\n\t\t\t\t});\n\t\t\t}\n\t\t\t_fixup();\n\t\t}\n\t\treturn pact;\n\t}\n\t// No support for Symbol.iterator\n\tif (!(\"length\" in target)) {\n\t\tthrow new TypeError(\"Object is not iterable\");\n\t}\n\t// Handle live collections properly\n\tvar values = [];\n\tfor (var i = 0; i < target.length; i++) {\n\t\tvalues.push(target[i]);\n\t}\n\treturn _forTo(values, function(i) { return body(values[i]); }, check);\n}\n\nexport const _asyncIteratorSymbol = /*#__PURE__*/ typeof Symbol !== \"undefined\" ? (Symbol.asyncIterator || (Symbol.asyncIterator = Symbol(\"Symbol.asyncIterator\"))) : \"@@asyncIterator\";\n\n// Asynchronously iterate on a value using it's async iterator if present, or its synchronous iterator if missing\nexport function _forAwaitOf(target, body, check) {\n\tif (typeof target[_asyncIteratorSymbol] === \"function\") {\n\t\tvar pact = new _Pact();\n\t\tvar iterator = target[_asyncIteratorSymbol]();\n\t\titerator.next().then(_resumeAfterNext).then(void 0, _reject);\n\t\treturn pact;\n\t\tfunction _resumeAfterBody(result) {\n\t\t\tif (check && check()) {\n\t\t\t\treturn _settle(pact, 1, iterator.return ? iterator.return().then(function() { return result; }) : result);\n\t\t\t}\n\t\t\titerator.next().then(_resumeAfterNext).then(void 0, _reject);\n\t\t}\n\t\tfunction _resumeAfterNext(step) {\n\t\t\tif (step.done) {\n\t\t\t\t_settle(pact, 1);\n\t\t\t} else {\n\t\t\t\tPromise.resolve(body(step.value)).then(_resumeAfterBody).then(void 0, _reject);\n\t\t\t}\n\t\t}\n\t\tfunction _reject(error) {\n\t\t\t_settle(pact, 2, iterator.return ? iterator.return().then(function() { return error; }) : error);\n\t\t}\n\t}\n\treturn Promise.resolve(_forOf(target, function(value) { return Promise.resolve(value).then(body); }, check));\n}\n\n// Asynchronously implement a generic for loop\nexport function _for(test, update, body) {\n\tvar stage;\n\tfor (;;) {\n\t\tvar shouldContinue = test();\n\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\tshouldContinue = shouldContinue.v;\n\t\t}\n\t\tif (!shouldContinue) {\n\t\t\treturn result;\n\t\t}\n\t\tif (shouldContinue.then) {\n\t\t\tstage = 0;\n\t\t\tbreak;\n\t\t}\n\t\tvar result = body();\n\t\tif (result && result.then) {\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.s;\n\t\t\t} else {\n\t\t\t\tstage = 1;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (update) {\n\t\t\tvar updateValue = update();\n\t\t\tif (updateValue && updateValue.then && !_isSettledPact(updateValue)) {\n\t\t\t\tstage = 2;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\tvar pact = new _Pact();\n\tvar reject = _settle.bind(null, pact, 2);\n\t(stage === 0 ? shouldContinue.then(_resumeAfterTest) : stage === 1 ? result.then(_resumeAfterBody) : updateValue.then(_resumeAfterUpdate)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterBody(value) {\n\t\tresult = value;\n\t\tdo {\n\t\t\tif (update) {\n\t\t\t\tupdateValue = update();\n\t\t\t\tif (updateValue && updateValue.then && !_isSettledPact(updateValue)) {\n\t\t\t\t\tupdateValue.then(_resumeAfterUpdate).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\tshouldContinue = test();\n\t\t\tif (!shouldContinue || (_isSettledPact(shouldContinue) && !shouldContinue.v)) {\n\t\t\t\t_settle(pact, 1, result);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.v;\n\t\t\t}\n\t\t} while (!result || !result.then);\n\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t}\n\tfunction _resumeAfterTest(shouldContinue) {\n\t\tif (shouldContinue) {\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t} else {\n\t\t\t\t_resumeAfterBody(result);\n\t\t\t}\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n\tfunction _resumeAfterUpdate() {\n\t\tif (shouldContinue = test()) {\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t} else {\n\t\t\t\t_resumeAfterTest(shouldContinue);\n\t\t\t}\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n}\n\n// Asynchronously implement a do ... while loop\nexport function _do(body, test) {\n\tvar awaitBody;\n\tdo {\n\t\tvar result = body();\n\t\tif (result && result.then) {\n\t\t\tif (_isSettledPact(result)) {\n\t\t\t\tresult = result.v;\n\t\t\t} else {\n\t\t\t\tawaitBody = true;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tvar shouldContinue = test();\n\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\tshouldContinue = shouldContinue.v;\n\t\t}\n\t\tif (!shouldContinue) {\n\t\t\treturn result;\n\t\t}\n\t} while (!shouldContinue.then);\n\tconst pact = new _Pact();\n\tconst reject = _settle.bind(null, pact, 2);\n\t(awaitBody ? result.then(_resumeAfterBody) : shouldContinue.then(_resumeAfterTest)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterBody(value) {\n\t\tresult = value;\n\t\tfor (;;) {\n\t\t\tshouldContinue = test();\n\t\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\t\tshouldContinue = shouldContinue.v;\n\t\t\t}\n\t\t\tif (!shouldContinue) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (shouldContinue.then) {\n\t\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\tresult = result.v;\n\t\t\t\t} else {\n\t\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t_settle(pact, 1, result);\n\t}\n\tfunction _resumeAfterTest(shouldContinue) {\n\t\tif (shouldContinue) {\n\t\t\tdo {\n\t\t\t\tresult = body();\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tif (_isSettledPact(result)) {\n\t\t\t\t\t\tresult = result.v;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tshouldContinue = test();\n\t\t\t\tif (_isSettledPact(shouldContinue)) {\n\t\t\t\t\tshouldContinue = shouldContinue.v;\n\t\t\t\t}\n\t\t\t\tif (!shouldContinue) {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} while (!shouldContinue.then);\n\t\t\tshouldContinue.then(_resumeAfterTest).then(void 0, reject);\n\t\t} else {\n\t\t\t_settle(pact, 1, result);\n\t\t}\n\t}\n}\n\n// Asynchronously implement a switch statement\nexport function _switch(discriminant, cases) {\n\tvar dispatchIndex = -1;\n\tvar awaitBody;\n\touter: {\n\t\tfor (var i = 0; i < cases.length; i++) {\n\t\t\tvar test = cases[i][0];\n\t\t\tif (test) {\n\t\t\t\tvar testValue = test();\n\t\t\t\tif (testValue && testValue.then) {\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t\tif (testValue === discriminant) {\n\t\t\t\t\tdispatchIndex = i;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Found the default case, set it as the pending dispatch case\n\t\t\t\tdispatchIndex = i;\n\t\t\t}\n\t\t}\n\t\tif (dispatchIndex !== -1) {\n\t\t\tdo {\n\t\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\t\twhile (!body) {\n\t\t\t\t\tdispatchIndex++;\n\t\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t\t}\n\t\t\t\tvar result = body();\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tawaitBody = true;\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\t\tdispatchIndex++;\n\t\t\t} while (fallthroughCheck && !fallthroughCheck());\n\t\t\treturn result;\n\t\t}\n\t}\n\tconst pact = new _Pact();\n\tconst reject = _settle.bind(null, pact, 2);\n\t(awaitBody ? result.then(_resumeAfterBody) : testValue.then(_resumeAfterTest)).then(void 0, reject);\n\treturn pact;\n\tfunction _resumeAfterTest(value) {\n\t\tfor (;;) {\n\t\t\tif (value === discriminant) {\n\t\t\t\tdispatchIndex = i;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (++i === cases.length) {\n\t\t\t\tif (dispatchIndex !== -1) {\n\t\t\t\t\tbreak;\n\t\t\t\t} else {\n\t\t\t\t\t_settle(pact, 1, result);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t\ttest = cases[i][0];\n\t\t\tif (test) {\n\t\t\t\tvalue = test();\n\t\t\t\tif (value && value.then) {\n\t\t\t\t\tvalue.then(_resumeAfterTest).then(void 0, reject);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tdispatchIndex = i;\n\t\t\t}\n\t\t}\n\t\tdo {\n\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\twhile (!body) {\n\t\t\t\tdispatchIndex++;\n\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t}\n\t\t\tvar result = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\tdispatchIndex++;\n\t\t} while (fallthroughCheck && !fallthroughCheck());\n\t\t_settle(pact, 1, result);\n\t}\n\tfunction _resumeAfterBody(result) {\n\t\tfor (;;) {\n\t\t\tvar fallthroughCheck = cases[dispatchIndex][2];\n\t\t\tif (!fallthroughCheck || fallthroughCheck()) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tdispatchIndex++;\n\t\t\tvar body = cases[dispatchIndex][1];\n\t\t\twhile (!body) {\n\t\t\t\tdispatchIndex++;\n\t\t\t\tbody = cases[dispatchIndex][1];\n\t\t\t}\n\t\t\tresult = body();\n\t\t\tif (result && result.then) {\n\t\t\t\tresult.then(_resumeAfterBody).then(void 0, reject);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}\n\t\t_settle(pact, 1, result);\n\t}\n}\n\n// Asynchronously call a function and pass the result to explicitly passed continuations\nexport function _call(body, then, direct) {\n\tif (direct) {\n\t\treturn then ? then(body()) : body();\n\t}\n\ttry {\n\t\tvar result = Promise.resolve(body());\n\t\treturn then ? result.then(then) : result;\n\t} catch (e) {\n\t\treturn Promise.reject(e);\n\t}\n}\n\n// Asynchronously call a function and swallow the result\nexport function _callIgnored(body, direct) {\n\treturn _call(body, _empty, direct);\n}\n\n// Asynchronously call a function and pass the result to explicitly passed continuations\nexport function _invoke(body, then) {\n\tvar result = body();\n\tif (result && result.then) {\n\t\treturn result.then(then);\n\t}\n\treturn then(result);\n}\n\n// Asynchronously call a function and swallow the result\nexport function _invokeIgnored(body) {\n\tvar result = body();\n\tif (result && result.then) {\n\t\treturn result.then(_empty);\n\t}\n}\n\n// Asynchronously call a function and send errors to recovery continuation\nexport function _catch(body, recover) {\n\ttry {\n\t\tvar result = body();\n\t} catch(e) {\n\t\treturn recover(e);\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(void 0, recover);\n\t}\n\treturn result;\n}\n\n// Asynchronously await a promise and pass the result to a finally continuation\nexport function _finallyRethrows(body, finalizer) {\n\ttry {\n\t\tvar result = body();\n\t} catch (e) {\n\t\treturn finalizer(true, e);\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(finalizer.bind(null, false), finalizer.bind(null, true));\n\t}\n\treturn finalizer(false, result);\n}\n\n// Asynchronously await a promise and invoke a finally continuation that always overrides the result\nexport function _finally(body, finalizer) {\n\ttry {\n\t\tvar result = body();\n\t} catch (e) {\n\t\treturn finalizer();\n\t}\n\tif (result && result.then) {\n\t\treturn result.then(finalizer, finalizer);\n\t}\n\treturn finalizer();\n}\n\n// Rethrow or return a value from a finally continuation\nexport function _rethrow(thrown, value) {\n\tif (thrown)\n\t\tthrow value;\n\treturn value;\n}\n\n// Empty function to implement break and other control flow that ignores asynchronous results\nexport function _empty() {\n}\n\n// Sentinel value for early returns in generators \nexport const _earlyReturn = /*#__PURE__*/ {};\n\n// Asynchronously call a function and send errors to recovery continuation, skipping early returns\nexport function _catchInGenerator(body, recover) {\n\treturn _catch(body, function(e) {\n\t\tif (e === _earlyReturn) {\n\t\t\tthrow e;\n\t\t}\n\t\treturn recover(e);\n\t});\n}\n\n// Asynchronous generator class; accepts the entrypoint of the generator, to which it passes itself when the generator should start\nexport const _AsyncGenerator = /*#__PURE__*/(function() {\n\tfunction _AsyncGenerator(entry) {\n\t\tthis._entry = entry;\n\t\tthis._pact = null;\n\t\tthis._resolve = null;\n\t\tthis._return = null;\n\t\tthis._promise = null;\n\t}\n\n\tfunction _wrapReturnedValue(value) {\n\t\treturn { value: value, done: true };\n\t}\n\tfunction _wrapYieldedValue(value) {\n\t\treturn { value: value, done: false };\n\t}\n\n\t_AsyncGenerator.prototype._yield = function(value) {\n\t\t// Yield the value to the pending next call\n\t\tthis._resolve(value && value.then ? value.then(_wrapYieldedValue) : _wrapYieldedValue(value));\n\t\t// Return a pact for an upcoming next/return/throw call\n\t\treturn this._pact = new _Pact();\n\t};\n\t_AsyncGenerator.prototype.next = function(value) {\n\t\t// Advance the generator, starting it if it has yet to be started\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tconst _entry = _this._entry;\n\t\t\t\tif (_entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the next call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Start the generator\n\t\t\t\t_this._entry = null;\n\t\t\t\t_this._resolve = resolve;\n\t\t\t\tfunction returnValue(value) {\n\t\t\t\t\t_this._resolve(value && value.then ? value.then(_wrapReturnedValue) : _wrapReturnedValue(value));\n\t\t\t\t\t_this._pact = null;\n\t\t\t\t\t_this._resolve = null;\n\t\t\t\t}\n\t\t\t\tvar result = _entry(_this);\n\t\t\t\tif (result && result.then) {\n\t\t\t\t\tresult.then(returnValue, function(error) {\n\t\t\t\t\t\tif (error === _earlyReturn) {\n\t\t\t\t\t\t\treturnValue(_this._return);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst pact = new _Pact();\n\t\t\t\t\t\t\t_this._resolve(pact);\n\t\t\t\t\t\t\t_this._pact = null;\n\t\t\t\t\t\t\t_this._resolve = null;\n\t\t\t\t\t\t\t_resolve(pact, 2, error);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\treturnValue(result);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Generator is started and a yield expression is pending, settle it\n\t\t\t\t_this._pact = null;\n\t\t\t\t_this._resolve = resolve;\n\t\t\t\t_settle(_pact, 1, value);\n\t\t\t}\n\t\t});\n\t};\n\t_AsyncGenerator.prototype.return = function(value) {\n\t\t// Early return from the generator if started, otherwise abandons the generator\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tif (_this._entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the return call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Generator is not started, abandon it and return the specified value\n\t\t\t\t_this._entry = null;\n\t\t\t\treturn resolve(value && value.then ? value.then(_wrapReturnedValue) : _wrapReturnedValue(value));\n\t\t\t}\n\t\t\t// Settle the yield expression with a rejected \"early return\" value\n\t\t\t_this._return = value;\n\t\t\t_this._resolve = resolve;\n\t\t\t_this._pact = null;\n\t\t\t_settle(_pact, 2, _earlyReturn);\n\t\t});\n\t};\n\t_AsyncGenerator.prototype.throw = function(error) {\n\t\t// Inject an exception into the pending yield expression\n\t\tconst _this = this;\n\t\treturn _this._promise = new Promise(function (resolve, reject) {\n\t\t\tconst _pact = _this._pact;\n\t\t\tif (_pact === null) {\n\t\t\t\tif (_this._entry === null) {\n\t\t\t\t\t// Generator is started, but not awaiting a yield expression\n\t\t\t\t\t// Abandon the throw call!\n\t\t\t\t\treturn resolve(_this._promise);\n\t\t\t\t}\n\t\t\t\t// Generator is not started, abandon it and return a rejected Promise containing the error\n\t\t\t\t_this._entry = null;\n\t\t\t\treturn reject(error);\n\t\t\t}\n\t\t\t// Settle the yield expression with the value as a rejection\n\t\t\t_this._resolve = resolve;\n\t\t\t_this._pact = null;\n\t\t\t_settle(_pact, 2, error);\n\t\t});\n\t};\n\n\t_AsyncGenerator.prototype[_asyncIteratorSymbol] = function() {\n\t\treturn this;\n\t};\n\t\n\treturn _AsyncGenerator;\n})();\n", "import { AbstractConnectorArguments, ConnectorUpdate } from '@web3-react/types'\nimport { AbstractConnector } from '@web3-react/abstract-connector'\nimport warning from 'tiny-warning'\n\nimport { SendReturnResult, SendReturn, Send, SendOld } from './types'\n\nfunction parseSendReturn(sendReturn: SendReturnResult | SendReturn): any {\n  return sendReturn.hasOwnProperty('result') ? sendReturn.result : sendReturn\n}\n\nexport class NoEthereumProviderError extends Error {\n  public constructor() {\n    super()\n    this.name = this.constructor.name\n    this.message = 'No Ethereum provider was found on window.ethereum.'\n  }\n}\n\nexport class UserRejectedRequestError extends Error {\n  public constructor() {\n    super()\n    this.name = this.constructor.name\n    this.message = 'The user rejected the request.'\n  }\n}\n\nexport class InjectedConnector extends AbstractConnector {\n  constructor(kwargs: AbstractConnectorArguments) {\n    super(kwargs)\n\n    this.handleNetworkChanged = this.handleNetworkChanged.bind(this)\n    this.handleChainChanged = this.handleChainChanged.bind(this)\n    this.handleAccountsChanged = this.handleAccountsChanged.bind(this)\n    this.handleClose = this.handleClose.bind(this)\n  }\n\n  private handleChainChanged(chainId: string | number): void {\n    if (__DEV__) {\n      console.log(\"Handling 'chainChanged' event with payload\", chainId)\n    }\n    this.emitUpdate({ chainId, provider: window.ethereum })\n  }\n\n  private handleAccountsChanged(accounts: string[]): void {\n    if (__DEV__) {\n      console.log(\"Handling 'accountsChanged' event with payload\", accounts)\n    }\n    if (accounts.length === 0) {\n      this.emitDeactivate()\n    } else {\n      this.emitUpdate({ account: accounts[0] })\n    }\n  }\n\n  private handleClose(code: number, reason: string): void {\n    if (__DEV__) {\n      console.log(\"Handling 'close' event with payload\", code, reason)\n    }\n    this.emitDeactivate()\n  }\n\n  private handleNetworkChanged(networkId: string | number): void {\n    if (__DEV__) {\n      console.log(\"Handling 'networkChanged' event with payload\", networkId)\n    }\n    this.emitUpdate({ chainId: networkId, provider: window.ethereum })\n  }\n\n  public async activate(): Promise<ConnectorUpdate> {\n    if (!window.ethereum) {\n      throw new NoEthereumProviderError()\n    }\n\n    if (window.ethereum.on) {\n      window.ethereum.on('chainChanged', this.handleChainChanged)\n      window.ethereum.on('accountsChanged', this.handleAccountsChanged)\n      window.ethereum.on('close', this.handleClose)\n      window.ethereum.on('networkChanged', this.handleNetworkChanged)\n    }\n\n    if ((window.ethereum as any).isMetaMask) {\n      ;(window.ethereum as any).autoRefreshOnNetworkChange = false\n    }\n\n    // try to activate + get account via eth_requestAccounts\n    let account\n    try {\n      account = await (window.ethereum.send as Send)('eth_requestAccounts').then(\n        sendReturn => parseSendReturn(sendReturn)[0]\n      )\n    } catch (error) {\n      if ((error as any).code === 4001) {\n        throw new UserRejectedRequestError()\n      }\n      warning(false, 'eth_requestAccounts was unsuccessful, falling back to enable')\n    }\n\n    // if unsuccessful, try enable\n    if (!account) {\n      // if enable is successful but doesn't return accounts, fall back to getAccount (not happy i have to do this...)\n      account = await window.ethereum.enable().then(sendReturn => sendReturn && parseSendReturn(sendReturn)[0])\n    }\n\n    return { provider: window.ethereum, ...(account ? { account } : {}) }\n  }\n\n  public async getProvider(): Promise<any> {\n    return window.ethereum\n  }\n\n  public async getChainId(): Promise<number | string> {\n    if (!window.ethereum) {\n      throw new NoEthereumProviderError()\n    }\n\n    let chainId\n    try {\n      chainId = await (window.ethereum.send as Send)('eth_chainId').then(parseSendReturn)\n    } catch {\n      warning(false, 'eth_chainId was unsuccessful, falling back to net_version')\n    }\n\n    if (!chainId) {\n      try {\n        chainId = await (window.ethereum.send as Send)('net_version').then(parseSendReturn)\n      } catch {\n        warning(false, 'net_version was unsuccessful, falling back to net version v2')\n      }\n    }\n\n    if (!chainId) {\n      try {\n        chainId = parseSendReturn((window.ethereum.send as SendOld)({ method: 'net_version' }))\n      } catch {\n        warning(false, 'net_version v2 was unsuccessful, falling back to manual matches and static properties')\n      }\n    }\n\n    if (!chainId) {\n      if ((window.ethereum as any).isDapper) {\n        chainId = parseSendReturn((window.ethereum as any).cachedResults.net_version)\n      } else {\n        chainId =\n          (window.ethereum as any).chainId ||\n          (window.ethereum as any).netVersion ||\n          (window.ethereum as any).networkVersion ||\n          (window.ethereum as any)._chainId\n      }\n    }\n\n    return chainId\n  }\n\n  public async getAccount(): Promise<null | string> {\n    if (!window.ethereum) {\n      throw new NoEthereumProviderError()\n    }\n\n    let account\n    try {\n      account = await (window.ethereum.send as Send)('eth_accounts').then(sendReturn => parseSendReturn(sendReturn)[0])\n    } catch {\n      warning(false, 'eth_accounts was unsuccessful, falling back to enable')\n    }\n\n    if (!account) {\n      try {\n        account = await window.ethereum.enable().then(sendReturn => parseSendReturn(sendReturn)[0])\n      } catch {\n        warning(false, 'enable was unsuccessful, falling back to eth_accounts v2')\n      }\n    }\n\n    if (!account) {\n      account = parseSendReturn((window.ethereum.send as SendOld)({ method: 'eth_accounts' }))[0]\n    }\n\n    return account\n  }\n\n  public deactivate() {\n    if (window.ethereum && window.ethereum.removeListener) {\n      window.ethereum.removeListener('chainChanged', this.handleChainChanged)\n      window.ethereum.removeListener('accountsChanged', this.handleAccountsChanged)\n      window.ethereum.removeListener('close', this.handleClose)\n      window.ethereum.removeListener('networkChanged', this.handleNetworkChanged)\n    }\n  }\n\n  public async isAuthorized(): Promise<boolean> {\n    if (!window.ethereum) {\n      return false\n    }\n\n    try {\n      return await (window.ethereum.send as Send)('eth_accounts').then(sendReturn => {\n        if (parseSendReturn(sendReturn).length > 0) {\n          return true\n        } else {\n          return false\n        }\n      })\n    } catch {\n      return false\n    }\n  }\n}\n"], "names": ["_iteratorSymbol", "Symbol", "iterator", "_asyncIteratorSymbol", "asyncIterator", "_catch", "body", "recover", "result", "e", "then", "parseSendReturn", "sendReturn", "hasOwnProperty", "NoEthereumProviderError", "name", "constructor", "message", "Error", "UserRejectedRequestError", "InjectedConnector", "kwargs", "handleNetworkChanged", "bind", "handleChainChanged", "handleAccountsChanged", "handleClose", "chainId", "console", "log", "emitUpdate", "provider", "window", "ethereum", "accounts", "length", "emitDeactivate", "account", "code", "reason", "networkId", "activate", "enable", "on", "isMetaMask", "autoRefreshOnNetworkChange", "send", "error", "warning", "get<PERSON><PERSON><PERSON>", "get<PERSON>hainId", "method", "isDapper", "cachedResults", "net_version", "netVersion", "networkVersion", "_chainId", "getAccount", "deactivate", "removeListener", "isAuthorized", "AbstractConnector"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA,AAmKO,IAAMA,eAAe;;AAAiB,OAAOC,MAAP,KAAkB,WAAlB,GAAiCA,MAAM,CAACC,QAAP,KAAoBD,MAAM,CAACC,QAAP;;AAAkBD,MAAM,CAAC,iBAAD,CAA5C,CAAjC,GAAqG,YAA3I;AAGP,AA0DO,IAAME,oBAAoB;;AAAiB,OAAOF,MAAP,KAAkB,WAAlB,GAAiCA,MAAM,CAACG,aAAP,KAAyBH,MAAM,CAACG,aAAP;;AAAuBH,MAAM,CAAC,sBAAD,CAAtD,CAAjC,GAAoH,iBAA/J;;AAiVP,AAAO,SAASI,MAAT,CAAgBC,IAAhB,EAAsBC,OAAtB,EAA+B;MACjC;QACCC,MAAM,GAAGF,IAAI,EAAjB;GADD,CAEE,OAAMG,CAAN,EAAS;WACHF,OAAO,CAACE,CAAD,CAAd;;;MAEGD,MAAM,IAAIA,MAAM,CAACE,IAArB,EAA2B;WACnBF,MAAM,CAACE,IAAP,CAAY,KAAK,CAAjB,EAAoBH,OAApB,CAAP;;;SAEMC,MAAP;;;ACrjBD,SAASG,eAAT,CAAyBC,UAAzB;SACSA,UAAU,CAACC,cAAX,CAA0B,QAA1B,IAAsCD,UAAU,CAACJ,MAAjD,GAA0DI,UAAjE;;;AAGF,IAAaE,uBAAb;;AAAA;;;;;;;UAGSC,IAAL,GAAY,MAAKC,WAAL,CAAiBD,IAA7B;UACKE,OAAL,GAAe,oDAAf;;;;;;;AAJJ,iBAA6CC,KAA7C;AAQA,IAAaC,wBAAb;;AAAA;;;;;;;WAGSJ,IAAL,GAAY,OAAKC,WAAL,CAAiBD,IAA7B;WACKE,OAAL,GAAe,gCAAf;;;;;;;AAJJ,iBAA8CC,KAA9C;AAQA,IAAaE,iBAAb;;AAAA;;;6BACcC,MAAZ;;;2CACQA,MAAN;WAEKC,oBAAL,GAA4B,OAAKA,oBAAL,CAA0BC,IAA1B,gCAA5B;WACKC,kBAAL,GAA0B,OAAKA,kBAAL,CAAwBD,IAAxB,gCAA1B;WACKE,qBAAL,GAA6B,OAAKA,qBAAL,CAA2BF,IAA3B,gCAA7B;WACKG,WAAL,GAAmB,OAAKA,WAAL,CAAiBH,IAAjB,gCAAnB;;;;;;SAGMC,kBAVV,GAUU,4BAAmBG,OAAnB;+CACO;MACXC,OAAO,CAACC,GAAR,CAAY,4CAAZ,EAA0DF,OAA1D;;;SAEGG,UAAL,CAAgB;MAAEH,OAAO,EAAPA,OAAF;MAAWI,QAAQ,EAAEC,MAAM,CAACC;KAA5C;GAdJ;;SAiBUR,qBAjBV,GAiBU,+BAAsBS,QAAtB;+CACO;MACXN,OAAO,CAACC,GAAR,CAAY,+CAAZ,EAA6DK,QAA7D;;;QAEEA,QAAQ,CAACC,MAAT,KAAoB,CAAxB,EAA2B;WACpBC,cAAL;KADF,MAEO;WACAN,UAAL,CAAgB;QAAEO,OAAO,EAAEH,QAAQ,CAAC,CAAD;OAAnC;;GAxBN;;SA4BUR,WA5BV,GA4BU,qBAAYY,IAAZ,EAA0BC,MAA1B;+CACO;MACXX,OAAO,CAACC,GAAR,CAAY,qCAAZ,EAAmDS,IAAnD,EAAyDC,MAAzD;;;SAEGH,cAAL;GAhCJ;;SAmCUd,oBAnCV,GAmCU,8BAAqBkB,SAArB;+CACO;MACXZ,OAAO,CAACC,GAAR,CAAY,8CAAZ,EAA4DW,SAA5D;;;SAEGV,UAAL,CAAgB;MAAEH,OAAO,EAAEa,SAAX;MAAsBT,QAAQ,EAAEC,MAAM,CAACC;KAAvD;GAvCJ;;SA0CeQ,QA1Cf;;;;;;;YA6EaV,QAAQ,EAAEC,MAAM,CAACC;aAAcI,OAAO,GAAG;YAAEA,OAAO,EAAPA;WAAL,GAAiB,EAAhE;;;;cALI,CAACA;;mCAEaL,MAAM,CAACC,QAAP,CAAgBS,MAAhB,GAAyBhC,IAAzB,CAA8B,UAAAE,UAAU;qBAAIA,UAAU,IAAID,eAAe,CAACC,UAAD,CAAf,CAA4B,CAA5B,CAAlB;aAAxC;cAAhByB,OAAO,wBAAP;;;;;;;;;;;mBA1BmC;;UALjC,CAACL,MAAM,CAACC,QAAZ,EAAsB;cACd,IAAInB,uBAAJ,EAAN;;;UAGEkB,MAAM,CAACC,QAAP,CAAgBU,EAApB,EAAwB;QACtBX,MAAM,CAACC,QAAP,CAAgBU,EAAhB,CAAmB,cAAnB,EAAmC,OAAKnB,kBAAxC;QACAQ,MAAM,CAACC,QAAP,CAAgBU,EAAhB,CAAmB,iBAAnB,EAAsC,OAAKlB,qBAA3C;QACAO,MAAM,CAACC,QAAP,CAAgBU,EAAhB,CAAmB,OAAnB,EAA4B,OAAKjB,WAAjC;QACAM,MAAM,CAACC,QAAP,CAAgBU,EAAhB,CAAmB,gBAAnB,EAAqC,OAAKrB,oBAA1C;;;UAGGU,MAAM,CAACC,QAAP,CAAwBW,UAA7B,EAAyC;;QACrCZ,MAAM,CAACC,QAAP,CAAwBY,0BAAxB,GAAqD,KAArD;;;;UAIAR,OAAJ;;sCACI;+BACeL,MAAM,CAACC,QAAP,CAAgBa,IAAhB,CAA8B,qBAA9B,EAAqDpC,IAArD,CACf,UAAAE,UAAU;iBAAID,eAAe,CAACC,UAAD,CAAf,CAA4B,CAA5B,CAAJ;SADK,CADf;UACFyB,OAAO,wBAAP;;mBAGOU,OAAO;YACTA,KAAa,CAACT,IAAd,KAAuB,IAA5B,EAAkC;gBAC1B,IAAInB,wBAAJ,EAAN;;;gDAEF6B,OAAO,CAAC,KAAD,EAAQ,8DAAR,CAAP;;;;KApEN;;;;;SAgFeC,WAhFf;;6BAiFWjB,MAAM,CAACC,QAAd;KAjFJ;;;;;SAoFeiB,UApFf;;;;cAwGQ,CAACvB,OAAL,EAAc;gBACR;cACFA,OAAO,GAAGhB,eAAe,CAAEqB,MAAM,CAACC,QAAP,CAAgBa,IAAhB,CAAiC;gBAAEK,MAAM,EAAE;eAA3C,CAAF,CAAzB;aADF,CAEE,gBAAM;sDACNH,OAAO,CAAC,KAAD,EAAQ,uFAAR,CAAP;;;;cAIA,CAACrB,OAAL,EAAc;gBACPK,MAAM,CAACC,QAAP,CAAwBmB,QAA7B,EAAuC;cACrCzB,OAAO,GAAGhB,eAAe,CAAEqB,MAAM,CAACC,QAAP,CAAwBoB,aAAxB,CAAsCC,WAAxC,CAAzB;aADF,MAEO;cACL3B,OAAO,GACJK,MAAM,CAACC,QAAP,CAAwBN,OAAxB,IACAK,MAAM,CAACC,QAAP,CAAwBsB,UADxB,IAEAvB,MAAM,CAACC,QAAP,CAAwBuB,cAFxB,IAGAxB,MAAM,CAACC,QAAP,CAAwBwB,QAJ3B;;;;iBAQG9B,OAAP;;;;cA5BI,CAACA;6CACC;qCACeK,MAAM,CAACC,QAAP,CAAgBa,IAAhB,CAA8B,aAA9B,EAA6CpC,IAA7C,CAAkDC,eAAlD,CADf;gBACFgB,OAAO,yBAAP;;2BACM;sDACNqB,OAAO,CAAC,KAAD,EAAQ,8DAAR,CAAP;;;;;;;;;;UAfA,CAAChB,MAAM,CAACC,QAAZ,EAAsB;cACd,IAAInB,uBAAJ,EAAN;;;UAGEa,OAAJ;;uCACI;+BACeK,MAAM,CAACC,QAAP,CAAgBa,IAAhB,CAA8B,aAA9B,EAA6CpC,IAA7C,CAAkDC,eAAlD,CADf;UACFgB,OAAO,yBAAP;;qBACM;gDACNqB,OAAO,CAAC,KAAD,EAAQ,2DAAR,CAAP;;;;KA7FN;;;;;SA+HeU,UA/Hf;;;;cAmJQ,CAACrB,OAAL,EAAc;YACZA,OAAO,GAAG1B,eAAe,CAAEqB,MAAM,CAACC,QAAP,CAAgBa,IAAhB,CAAiC;cAAEK,MAAM,EAAE;aAA3C,CAAF,CAAf,CAA+E,CAA/E,CAAV;;;iBAGKd,OAAP;;;;cAZI,CAACA;6CACC;qCACcL,MAAM,CAACC,QAAP,CAAgBS,MAAhB,GAAyBhC,IAAzB,CAA8B,UAAAE,UAAU;uBAAID,eAAe,CAACC,UAAD,CAAf,CAA4B,CAA5B,CAAJ;eAAxC,CADd;gBACFyB,OAAO,yBAAP;;2BACM;sDACNW,OAAO,CAAC,KAAD,EAAQ,0DAAR,CAAP;;;;;;;;;;UAfA,CAAChB,MAAM,CAACC,QAAZ,EAAsB;cACd,IAAInB,uBAAJ,EAAN;;;UAGEuB,OAAJ;;uCACI;+BACeL,MAAM,CAACC,QAAP,CAAgBa,IAAhB,CAA8B,cAA9B,EAA8CpC,IAA9C,CAAmD,UAAAE,UAAU;iBAAID,eAAe,CAACC,UAAD,CAAf,CAA4B,CAA5B,CAAJ;SAA7D,CADf;UACFyB,OAAO,yBAAP;;qBACM;gDACNW,OAAO,CAAC,KAAD,EAAQ,uDAAR,CAAP;;;;KAxIN;;;;;SA0JSW,UA1JT,GA0JS;QACD3B,MAAM,CAACC,QAAP,IAAmBD,MAAM,CAACC,QAAP,CAAgB2B,cAAvC,EAAuD;MACrD5B,MAAM,CAACC,QAAP,CAAgB2B,cAAhB,CAA+B,cAA/B,EAA+C,KAAKpC,kBAApD;MACAQ,MAAM,CAACC,QAAP,CAAgB2B,cAAhB,CAA+B,iBAA/B,EAAkD,KAAKnC,qBAAvD;MACAO,MAAM,CAACC,QAAP,CAAgB2B,cAAhB,CAA+B,OAA/B,EAAwC,KAAKlC,WAA7C;MACAM,MAAM,CAACC,QAAP,CAAgB2B,cAAhB,CAA+B,gBAA/B,EAAiD,KAAKtC,oBAAtD;;GA/JN;;SAmKeuC,YAnKf;;UAoKQ,CAAC7B,MAAM,CAACC,QAAZ,EAAsB;+BACb,KAAP;;;gDAGE;+BACYD,MAAM,CAACC,QAAP,CAAgBa,IAAhB,CAA8B,cAA9B,EAA8CpC,IAA9C,CAAmD,UAAAE,UAAU;cACrED,eAAe,CAACC,UAAD,CAAf,CAA4BuB,MAA5B,GAAqC,CAAzC,EAA4C;mBACnC,IAAP;WADF,MAEO;mBACE,KAAP;;SAJU,CADZ;qBAQI;eACC,KAAP;;KAjLN;;;;;;EAAuC2B,iBAAvC;;;;"}