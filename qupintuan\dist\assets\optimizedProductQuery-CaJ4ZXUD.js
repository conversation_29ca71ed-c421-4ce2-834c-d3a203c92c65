import{g as i,A as u,b as m}from"./index-BDw84Puy.js";import{c as d,h as p}from"./web3-DnWbColA.js";import"./ui-B9ZzSjJF.js";import"./vendor-CgHzTxSQ.js";const f=t=>!t||t===0?0:Number(t)||0,l=d({chain:m,transport:p("https://bsc-testnet.public.blastapi.io",{batch:!0,fetchOptions:{timeout:1e4},retryCount:2,retryDelay:1e3})}),n=new Map,s=3e4;function h(t,...e){return`${t}_${JSON.stringify(e)}`}function g(t){const e=n.get(t);return e&&Date.now()-e.timestamp<s?e.data:(n.delete(t),null)}function b(t,e){n.set(t,{data:e,timestamp:Date.now()})}function C(t){return f(t)}async function N(t){const e=h("productInfo",t),c=g(e);if(c)return c;try{const o=i(97,"ProductManagement"),r=await l.readContract({address:o,abi:u.ProductManagement,functionName:"products",args:[BigInt(t)]}),a={productId:Number(r[0]),merchant:r[1],name:r[2],description:r[3],price:C(r[4]),stock:Number(r[5]),isActive:r[6],sales:Number(r[7]),priceFixed:!0,originalPrice:r[4].toString()};return b(e,a),a}catch(o){throw console.error("🚨 [getOptimizedProductInfo] 获取商品信息失败:",o),new Error(`获取商品信息失败: ${o.message}`)}}function w(){const t=Date.now();for(const[e,c]of n.entries())t-c.timestamp>s&&n.delete(e)}setInterval(w,6e4);export{w as clearExpiredCache,N as getOptimizedProductInfo};
