"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var o=require("@react-native-async-storage/async-storage");function i(t){return t&&typeof t=="object"&&"default"in t?t:{default:t}}var u=i(o);const c=t=>JSON.stringify(t,(e,n)=>typeof n=="bigint"?n.toString()+"n":n),y=t=>{const e=/([\[:])?(\d{17,}|(?:[9](?:[1-9]07199254740991|0[1-9]7199254740991|00[8-9]199254740991|007[2-9]99254740991|007199[3-9]54740991|0071992[6-9]4740991|00719925[5-9]740991|007199254[8-9]40991|0071992547[5-9]0991|00719925474[1-9]991|00719925474099[2-9])))([,\}\]])/g,n=t.replace(e,'$1"$2n"$3');return JSON.parse(n,(l,r)=>typeof r=="string"&&r.match(/^\d+n$/)?BigInt(r.substring(0,r.length-1)):r)};function a(t){if(typeof t!="string")throw new Error(`Cannot safe json parse value of type ${typeof t}`);try{return y(t)}catch{return t}}function f(t){return typeof t=="string"?t:c(t)||""}function g(t){var e;return[t[0],a((e=t[1])!=null?e:"")]}class s{constructor(){this.asyncStorage=u.default}async getKeys(){return this.asyncStorage.getAllKeys()}async getEntries(){const e=await this.getKeys();return(await this.asyncStorage.multiGet(e)).map(g)}async getItem(e){const n=await this.asyncStorage.getItem(e);if(!(typeof n>"u"||n===null))return a(n)}async setItem(e,n){await this.asyncStorage.setItem(e,f(n))}async removeItem(e){await this.asyncStorage.removeItem(e)}}exports.KeyValueStorage=s,exports.default=s;
//# sourceMappingURL=index.js.map
