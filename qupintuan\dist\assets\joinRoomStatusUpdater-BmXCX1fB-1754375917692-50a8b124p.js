const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/groupBuyApi-Bn5o5iNb-1754375917692-50a8b124p.js","assets/basicOperations-BOq0NLuf-1754375917692-50a8b124p.js","assets/vendor-D7uqzx8C-1754375917692-50a8b124p.js","assets/roomManagement-BZPf_gXk-1754375917692-50a8b124p.js","assets/rewardOperations-I9R1_Rnl-1754375917692-50a8b124p.js"])))=>i.map(i=>d[i]);
import{_ as p}from"./vendor-D7uqzx8C-1754375917692-50a8b124p.js";class m{constructor(){this.updateQueue=new Map,this.isUpdating=!1}async handleJoinRoomStatusUpdate(t,e,o){try{this.updateLocalJoinStatus(t,e),this.triggerImmediateRefresh(o),this.scheduleStatusVerification(t,e,o)}catch(a){console.error("参与拼团状态更新失败:",a)}}updateLocalJoinStatus(t,e){try{const o=`room_participants_${t}`;let a=[];try{const r=localStorage.getItem(o);r&&(a=JSON.parse(r))}catch{a=[]}const i=e.toLowerCase();a.includes(i)||(a.push(i),localStorage.setItem(o,JSON.stringify(a)),localStorage.setItem(`${o}_timestamp`,Date.now().toString())),window.dispatchEvent(new CustomEvent("roomParticipantUpdated",{detail:{roomId:t,userAddress:e,participants:a,timestamp:Date.now()}}))}catch(o){console.warn("更新本地参与状态失败:",o)}}getLocalParticipants(t){try{const e=`room_participants_${t}`,o=`${e}_timestamp`,a=localStorage.getItem(e),i=localStorage.getItem(o);if(a&&i){if(Date.now()-parseInt(i)<120*1e3)return JSON.parse(a);localStorage.removeItem(e),localStorage.removeItem(o)}}catch(e){console.warn("获取本地参与者列表失败:",e)}return[]}triggerImmediateRefresh(t){typeof window<"u"&&(window.queryClient&&(window.queryClient.invalidateQueries(["groupBuyRooms"]),window.queryClient.refetchQueries(["groupBuyRooms"]),window.queryClient.invalidateQueries(["tokenBalance"])),window.refreshRoomList&&window.refreshRoomList(),window.refreshTokenBalances&&window.refreshTokenBalances()),t&&t()}scheduleStatusVerification(t,e,o){this.updateQueue.has(t)&&clearTimeout(this.updateQueue.get(t));const a=[500,1500,3e3,5e3];a.forEach((i,r)=>{const c=setTimeout(async()=>{try{await this.verifyJoinStatus(t,e,o)}catch(l){console.warn(`第${r+1}次参与状态验证失败:`,l)}},i);r===a.length-1&&this.updateQueue.set(t,c)})}async verifyJoinStatus(t,e,o){try{const{fetchRoom:a}=await p(async()=>{const{fetchRoom:s}=await import("./groupBuyApi-Bn5o5iNb-1754375917692-50a8b124p.js");return{fetchRoom:s}},__vite__mapDeps([0,1,2,3,4])),i=await a({chainId:97,roomId:BigInt(t)});if(!i){console.warn(`验证参与状态失败：无法获取房间 ${t} 数据`);return}const r=Array.isArray(i.participants)?i.participants:[],c=e.toLowerCase();if(r.some(s=>s.toLowerCase()===c)){const s=`room_participants_${t}`,u=`${s}_timestamp`;localStorage.removeItem(s),localStorage.removeItem(u),this.triggerImmediateRefresh(o),this.updateQueue.delete(t)}else console.warn(`房间 ${t} 参与状态验证失败，用户 ${e} 不在参与者列表中`)}catch(a){console.warn(`验证房间 ${t} 参与状态失败:`,a)}}cleanup(){this.updateQueue.forEach(t=>{clearTimeout(t)}),this.updateQueue.clear()}clearParticipantsCache(t=null){if(t){const e=`room_participants_${t}`,o=`${e}_timestamp`;localStorage.removeItem(e),localStorage.removeItem(o)}else Object.keys(localStorage).forEach(e=>{e.startsWith("room_participants_")&&localStorage.removeItem(e)})}}const h=new m;function f(n,t,e){h.handleJoinRoomStatusUpdate(n,t,e)}export{h as default,f as handleJoinRoomStatusUpdate};
