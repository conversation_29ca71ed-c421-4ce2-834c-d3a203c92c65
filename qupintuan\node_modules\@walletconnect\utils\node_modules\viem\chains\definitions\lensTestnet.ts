import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const lensTestnet = /*#__PURE__*/ defineChain({
  id: 37_111,
  name: 'Lens Testnet',
  nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'G<PERSON><PERSON>', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.testnet.lens.dev'],
      webSocket: ['wss://rpc.testnet.lens.dev/ws'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Lens Block Explorer',
      url: 'https://block-explorer.testnet.lens.dev',
      apiUrl: 'https://block-explorer-api.staging.lens.dev/api',
    },
  },
  testnet: true,
})
